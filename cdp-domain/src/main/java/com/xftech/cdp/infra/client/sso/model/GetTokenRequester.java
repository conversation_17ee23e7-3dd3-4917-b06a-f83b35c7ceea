package com.xftech.cdp.infra.client.sso.model;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> wanghu
 * @since : 2022/8/2 15:45:03
 */
@Setter
@Getter
public class GetTokenRequester {


    /**
     * 手机号	否	15899394006
     */
    private String mobile;

    /**
     * 短信验证码，当mobile有值，此项必填	否	1234
     */
    private String captcha;

    /**
     * 登陆方式，如 dingding	否	空字符
     */
    private String type;

    /**
     * 第三方登录code，当type有值，此项必填	否	daskdhshfudguifg34fdsfbk
     */
    private String code;

    /**
     * 如果传此参数，则代表使用token登录方式
     */
    private String token;

}
