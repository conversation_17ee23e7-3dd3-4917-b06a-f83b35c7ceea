package com.xftech.cdp.feign.service;


import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSON;
import com.xftech.cdp.feign.EnginePredictionClient;
import com.xftech.cdp.infra.client.ads.model.req.ModelPredictionReq;
import com.xftech.cdp.infra.exception.NoneException;
import com.xftech.cdp.infra.utils.JsonUtil;
import com.xftech.cdp.infra.utils.ThreadUtils;
import com.xinfei.enginepredictcenter.facade.ModelPredictionFacade;
import com.xinfei.enginepredictcenter.facade.rr.constant.PredictionFacadeConstants;
import com.xinfei.enginepredictcenter.facade.rr.request.PredictionRequest;
import com.xinfei.enginepredictcenter.facade.rr.response.BaseResponse;
import com.xinfei.enginepredictcenter.facade.rr.response.PredictionResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version $ EnginePredictionClientImpl, v 0.1 2025/3/7 14:28 mingwen.zang
 */
@Component
@Slf4j
public class EnginePredictionClientImpl implements EnginePredictionClient {
    @Resource
    private ModelPredictionFacade modelPredictionFacade;

    @Override
    public JSONObject modelPrediction(ModelPredictionReq param) {

        PredictionRequest request = new PredictionRequest();
        request.setBiz_data(JSON.parseObject(JSON.toJSONString(param.getBiz_data()), Map.class));
        request.setModel_name(param.getModel_name());
        request.setLocalId(ThreadUtils.getTraceId());
        BaseResponse<PredictionResponse> result = null;
        try {
            result = modelPredictionFacade.predict(request);
            log.info("接口请求日志:调用决策引擎新链路,request:{},resp:{}", JsonUtil.toJson(request), JsonUtil.toJson(result));
        } catch (Exception e) {
            log.error("接口报错:调用决策引擎新链路,request:{}", JsonUtil.toJson(request), e);
        }
        if (result == null || !Objects.equals(result.getCode(), PredictionFacadeConstants.SUCCESS_CODE)) {
            log.error("接口返回错误码:调用决策引擎新链路,request:{},resp:{}", JsonUtil.toJson(request), JsonUtil.toJson(result), NoneException.catError());
        }
        if (Objects.isNull(result.getData())) {
            log.error("接口数据为空:调用决策引擎新链路,request:{},resp:{}", JsonUtil.toJson(request), JsonUtil.toJson(result), NoneException.catError());
        }
        return (JSONObject)JSON.toJSON(result);
    }
}
