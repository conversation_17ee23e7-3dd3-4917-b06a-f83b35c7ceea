package com.xftech.cdp.infra.client.sms.model.resp;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> wanghu
 * @since : 2022/7/28 14:14:51
 */
@Getter
@Setter
public class SmsItem {

    /**
     * 588
     */
    @JsonProperty("id")
    @JSONField(name = "id")
    private Long id;

    /**
     * 模板类型
     */
    @JsonProperty("group")
    @JSONField(name = "group")
    private String group;

    /**
     * fxk
     */
    @JsonProperty("app")
    @JSONField(name = "app")
    private String app;

    /**
     * smstpl988051
     */
    @JsonProperty("template_id")
    @JSONField(name = "template_id")
    private String templateId;

    /**
     * userName您好，您的账单已逾期，请及时通过“飞行卡APP”消费账单进行还款。退订回T
     */
    @JsonProperty("template")
    @JSONField(name = "template")
    private String template;

    /**
     * 消金账单逾期通知2
     */
    @JsonProperty("description")
    @JSONField(name = "description")
    private String description;

}
