<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="crowdInfoMapper">
    <resultMap id="BaseResultMap" type="com.xftech.cdp.distribute.crowd.repository.model.CrowdInfoDo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="crowd_id" jdbcType="BIGINT" property="crowdId"/>
        <result column="crowd_name" jdbcType="VARCHAR" property="crowdName"/>
        <result column="crowd_desc" jdbcType="VARCHAR" property="crowdDesc"/>
        <result column="validity_begin" jdbcType="TIMESTAMP" property="validityBegin"/>
        <result column="validity_end" jdbcType="TIMESTAMP" property="validityEnd"/>
        <result column="is_permanent" jdbcType="SMALLINT" property="isPermanent"/>
        <result column="crowd_create_type" jdbcType="SMALLINT" property="crowdCreateType"/>
        <result column="crowd_category" jdbcType="VARCHAR" property="crowdCategory"/>
        <result column="crowd_status" jdbcType="SMALLINT" property="crowdStatus"/>
        <result column="d_flag" jdbcType="SMALLINT" property="dFlag"/>
        <result column="created_op" jdbcType="VARCHAR" property="createdOp"/>
        <result column="updated_op" jdbcType="VARCHAR" property="updatedOp"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , crowd_id, crowd_name, crowd_desc, validity_begin, validity_end, is_permanent,
    crowd_create_type, crowd_category, crowd_status, d_flag, created_op, updated_op,
    created_time, updated_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crowd_info
        where id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        update crowd_info set d_flag = 1
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.xftech.cdp.distribute.crowd.repository.model.CrowdInfoDo" useGeneratedKeys="true">
        insert into crowd_info (crowd_id, crowd_name, crowd_desc,
                                validity_begin, validity_end, is_permanent,
                                crowd_create_type, crowd_category, crowd_status,
                                d_flag, created_op, updated_op,
                                created_time, updated_time)
        values (#{crowdId,jdbcType=BIGINT}, #{crowdName,jdbcType=VARCHAR}, #{crowdDesc,jdbcType=VARCHAR},
                #{validityBegin,jdbcType=TIMESTAMP}, #{validityEnd,jdbcType=TIMESTAMP},
                #{isPermanent,jdbcType=SMALLINT},
                #{crowdCreateType,jdbcType=SMALLINT}, #{crowdCategory,jdbcType=VARCHAR},
                #{crowdStatus,jdbcType=SMALLINT},
                #{dFlag,jdbcType=SMALLINT}, #{createdOp,jdbcType=VARCHAR}, #{updatedOp,jdbcType=VARCHAR},
                #{createdTime,jdbcType=TIMESTAMP}, #{updatedTime,jdbcType=TIMESTAMP})
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.xftech.cdp.distribute.crowd.repository.model.CrowdInfoDo"
            useGeneratedKeys="true">
        insert into crowd_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="crowdId != null">
                crowd_id,
            </if>
            <if test="crowdName != null">
                crowd_name,
            </if>
            <if test="crowdDesc != null">
                crowd_desc,
            </if>
            <if test="validityBegin != null">
                validity_begin,
            </if>
            <if test="validityEnd != null">
                validity_end,
            </if>
            <if test="isPermanent != null">
                is_permanent,
            </if>
            <if test="crowdCreateType != null">
                crowd_create_type,
            </if>
            <if test="crowdCategory != null">
                crowd_category,
            </if>
            <if test="crowdStatus != null">
                crowd_status,
            </if>
            <if test="dFlag != null">
                d_flag,
            </if>
            <if test="createdOp != null">
                created_op,
            </if>
            <if test="updatedOp != null">
                updated_op,
            </if>
            <if test="createdTime != null">
                created_time,
            </if>
            <if test="updatedTime != null">
                updated_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="crowdId != null">
                #{crowdId,jdbcType=BIGINT},
            </if>
            <if test="crowdName != null">
                #{crowdName,jdbcType=VARCHAR},
            </if>
            <if test="crowdDesc != null">
                #{crowdDesc,jdbcType=VARCHAR},
            </if>
            <if test="validityBegin != null">
                #{validityBegin,jdbcType=TIMESTAMP},
            </if>
            <if test="validityEnd != null">
                #{validityEnd,jdbcType=TIMESTAMP},
            </if>
            <if test="isPermanent != null">
                #{isPermanent,jdbcType=SMALLINT},
            </if>
            <if test="crowdCreateType != null">
                #{crowdCreateType,jdbcType=SMALLINT},
            </if>
            <if test="crowdCategory != null">
                #{crowdCategory,jdbcType=VARCHAR},
            </if>
            <if test="crowdStatus != null">
                #{crowdStatus,jdbcType=SMALLINT},
            </if>
            <if test="dFlag != null">
                #{dFlag,jdbcType=SMALLINT},
            </if>
            <if test="createdOp != null">
                #{createdOp,jdbcType=VARCHAR},
            </if>
            <if test="updatedOp != null">
                #{updatedOp,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedTime != null">
                #{updatedTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.xftech.cdp.distribute.crowd.repository.model.CrowdInfoDo">
        update crowd_info
        <set>
            <if test="crowdId != null">
                crowd_id = #{crowdId,jdbcType=BIGINT},
            </if>
            <if test="crowdName != null">
                crowd_name = #{crowdName,jdbcType=VARCHAR},
            </if>
            <if test="crowdDesc != null">
                crowd_desc = #{crowdDesc,jdbcType=VARCHAR},
            </if>
            <if test="validityBegin != null">
                validity_begin = #{validityBegin,jdbcType=TIMESTAMP},
            </if>
            <if test="validityEnd != null">
                validity_end = #{validityEnd,jdbcType=TIMESTAMP},
            </if>
            <if test="isPermanent != null">
                is_permanent = #{isPermanent,jdbcType=SMALLINT},
            </if>
            <if test="crowdCreateType != null">
                crowd_create_type = #{crowdCreateType,jdbcType=SMALLINT},
            </if>
            <if test="crowdCategory != null">
                crowd_category = #{crowdCategory,jdbcType=VARCHAR},
            </if>
            <if test="crowdStatus != null">
                crowd_status = #{crowdStatus,jdbcType=SMALLINT},
            </if>
            <if test="dFlag != null">
                d_flag = #{dFlag,jdbcType=SMALLINT},
            </if>
            <if test="createdOp != null">
                created_op = #{createdOp,jdbcType=VARCHAR},
            </if>
            <if test="updatedOp != null">
                updated_op = #{updatedOp,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedTime != null">
                updated_time = #{updatedTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.xftech.cdp.distribute.crowd.repository.model.CrowdInfoDo">
        update crowd_info
        set crowd_id          = #{crowdId,jdbcType=BIGINT},
            crowd_name        = #{crowdName,jdbcType=VARCHAR},
            crowd_desc        = #{crowdDesc,jdbcType=VARCHAR},
            validity_begin    = #{validityBegin,jdbcType=TIMESTAMP},
            validity_end      = #{validityEnd,jdbcType=TIMESTAMP},
            is_permanent      = #{isPermanent,jdbcType=SMALLINT},
            crowd_create_type = #{crowdCreateType,jdbcType=SMALLINT},
            crowd_category    = #{crowdCategory,jdbcType=VARCHAR},
            crowd_status      = #{crowdStatus,jdbcType=SMALLINT},
            d_flag            = #{dFlag,jdbcType=SMALLINT},
            created_op        = #{createdOp,jdbcType=VARCHAR},
            updated_op        = #{updatedOp,jdbcType=VARCHAR},
            created_time      = #{createdTime,jdbcType=TIMESTAMP},
            updated_time      = #{updatedTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateCrowdStatusByCrowdId" parameterType="com.xftech.cdp.distribute.crowd.repository.model.CrowdInfoDo">
        update crowd_info
        <set>
            <if test="validityBegin != null">
                validity_begin = #{validityBegin,jdbcType=TIMESTAMP},
            </if>
            <if test="validityEnd != null">
                validity_end = #{validityEnd,jdbcType=TIMESTAMP},
            </if>
            <if test="isPermanent != null">
                is_permanent = #{isPermanent,jdbcType=SMALLINT},
            </if>
            <if test="crowdStatus != null">
                crowd_status = #{crowdStatus,jdbcType=SMALLINT},
            </if>
        </set>
        where crowd_id = #{crowdId,jdbcType=BIGINT} and d_flag = 0
    </update>

    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crowd_info
        where d_flag = 0
    </select>

    <select id="selectAllOnline" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crowd_info
        where d_flag = 0 and crowd_status in (4)
    </select>

    <select id="selectByCrowdId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crowd_info
        where crowd_id = #{crowdId,jdbcType=BIGINT} and d_flag = 0 order by id desc limit 1
    </select>

    <select id="selectValidCrowdByCrowdId"  resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crowd_info
        where crowd_id = #{crowdId,jdbcType=BIGINT}
          and validity_begin &lt; #{curDate,jdbcType=TIMESTAMP}
          and validity_end &gt; #{curDate,jdbcType=TIMESTAMP}
          and d_flag = 0
    </select>
</mapper>