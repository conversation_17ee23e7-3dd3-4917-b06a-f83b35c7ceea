package com.xftech.cdp.domain.strategy.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/2/20
 */
@Getter
@AllArgsConstructor
public enum StrategyGroupTypeEnum {

    LAST_UID_2(0, "uid倒数两位"),

    RANDOM(1, "随机数"),

    NEW_RANDOM(2, "新随机数");

    private final int code;

    private final String description;


    public static StrategyGroupTypeEnum getInstance(Integer code) {
        for (StrategyGroupTypeEnum strategyGroupTypeEnum : StrategyGroupTypeEnum.values()) {
            if (Objects.equals(strategyGroupTypeEnum.getCode(), code)) {
                return strategyGroupTypeEnum;
            }
        }
        return null;
    }
}
