/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.rocketmq.telemkt;

import com.alibaba.fastjson.JSONObject;
import com.xftech.cdp.domain.mq.MqConsumeService;
import com.xftech.cdp.infra.constant.Constants;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventMessageVO;
import com.xftech.cdp.infra.rocketmq.MessageHandler;
import com.xftech.cdp.infra.rocketmq.dto.TeleImportResultDTO;
import com.xftech.cdp.infra.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Slf4j
@Component
public class TeleInventoryHandler implements MessageHandler {

    @Autowired
    private MqConsumeService mqConsumeService;

    @Override
    public String getTopic() {
        return "tp_marketing_telname_enter_result_tele";
    }

    @Override
    public boolean execute(String messageId, Object message) {
        try {
            TeleImportResultDTO messageVO = JSONObject.parseObject(message.toString(), TeleImportResultDTO.class);
            log.info("TeleInventoryHandler messageVO: {}", JSONObject.toJSONString(messageVO));
            List<BizEventMessageVO> bizEventMessageVOList = transform(messageVO);
            int counter = 1; // 计数器初始化
            if (bizEventMessageVOList == null || bizEventMessageVOList.isEmpty()) {
                log.info("TeleInventoryHandler Invalid message content: {}", JSONObject.toJSONString(messageVO));
                return false;
            }
            log.info("TeleInventoryHandler bizEventMessageVOList: {}", JSONObject.toJSONString(bizEventMessageVOList));
            for (BizEventMessageVO bizEventMessageVO : bizEventMessageVOList) {
                bizEventMessageVO.setBizEventType("TeleInventory");
                mqConsumeService.bizEventProcess(messageId + counter, bizEventMessageVO);
                counter++; // 递增计数器
            }
            return true;
        } catch (Exception e) {
            log.info("TeleInventoryHandler exception", e);
            return false;
        }
    }

    private List<BizEventMessageVO> transform(TeleImportResultDTO messageVO) {
        List<BizEventMessageVO> bizEventMessageVOList = new ArrayList<>();

        if (messageVO == null || messageVO.getUserList() == null) {
            log.warn("TeleInventoryHandler Invalid message content: {}", JSONObject.toJSONString(messageVO));
            return bizEventMessageVOList;
        }
        if (messageVO.getUserList().isEmpty()) {
            log.info("TeleInventoryHandler userList is empty");
            return bizEventMessageVOList;
        }
        messageVO.getUserList().forEach(teleUser -> {
            BizEventMessageVO bizEventMessageVO = new BizEventMessageVO();
            BizEventMessageVO.ExtrData extrData = new BizEventMessageVO.ExtrData();
            bizEventMessageVO.setApp(Constants.XYF01);
            bizEventMessageVO.setAppUserId(teleUser.getCreditId());
            bizEventMessageVO.setTriggerDatetime(DateUtil.convertStr(new Date()));
            extrData.setInventoryStatus(teleUser.getStatus());
            bizEventMessageVO.setExtrData(extrData);
            bizEventMessageVOList.add(bizEventMessageVO);
        });
        return bizEventMessageVOList;

    }
}