/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.adapter.strategy;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.infra.constant.XxlJobConstants;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ TableCreator, v 0.1 2024/3/4 20:15 yye.xu Exp $
 */

@Service
@Slf4j
@AllArgsConstructor
public class TableCreatorHandler {

    // 每日创建 dispatch_crowd_20240304
    @XxlJob(XxlJobConstants.TABLE_CREATOR)
    public ReturnT<String> tableCreator(String param) {
        log.info("任务表生成器开始执行");
        // 最大步长, 创建未来10天的
        List<String> params = Arrays.asList(param.split(","));
        String table = params.get(0);
        int maxSpan = Integer.parseInt(params.get(1));
        LocalDate localDate = LocalDate.now();
        String nowValue = localDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String tableTemplate = "CREATE TABLE IF NOT EXISTS `%s` LIKE `%s`;";
        for (int i = 1; i < maxSpan; i++) {
            LocalDate dt = localDate.plusDays(i);
            String dayOffValue = dt.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String createSql = String.format(tableTemplate, table + dayOffValue, table + nowValue);

            log.info("创建表createSql:{}", createSql);
            DBUtil.getJdbcTemplate().execute(createSql);
        }
        log.info("Xxl-Job 任务表生成器");
        return ReturnT.SUCCESS;
    }
}