/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2025. All Rights Reserved.
 */

package com.xftech.cdp.infra.constant;

import java.text.MessageFormat;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version $ RedisKeyEnum, v 0.1 2025/1/6 11:40 xu.fan Exp $
 */
public enum RedisKeyEnum {

    // 优惠券膨胀锁
    ACT_COUPON_INFLATE_LOCK("activity_coupon_inflate_{0}","1", 5L),
    //
    ACT_COUPON_LOTTERY_LOCK("activity_coupon_lottery_{0}","1", 5L);

    private String key;
    private String defaultValue;
    private long expire;

    RedisKeyEnum(String key, String defaultValue, long expire) {
        this.key = key;
        this.defaultValue = defaultValue;
        this.expire = expire;

    }

    public String getKey(Object... args) {
        if(args == null || args.length == 0) {
            return key;
        } else {
            return MessageFormat.format(key, args);
        }
    }

    RedisKeyEnum(String key, long expire) {
        this.key = key;
        this.expire = expire;
    }
    public String getDefaultValue() {
        return defaultValue;
    }

    public long getExpire() {
        return expire;
    }
}
