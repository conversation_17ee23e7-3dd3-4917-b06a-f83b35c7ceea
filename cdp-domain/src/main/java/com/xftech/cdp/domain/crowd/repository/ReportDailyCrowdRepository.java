/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.domain.crowd.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.base.database.Page;
import com.xftech.cdp.api.dto.req.CrowdRefreshInfoReq;
import com.xftech.cdp.infra.repository.cdp.crowd.po.ReportDailyCrowdDo;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 */
@Component
public class ReportDailyCrowdRepository {

    public ReportDailyCrowdDo selectById(Long id) {
        return DBUtil.selectOne("reportDailyCrowdMapper.selectByPrimaryKey", id);
    }

    public boolean insert(ReportDailyCrowdDo reportDailyCrowdDo) {
        return DBUtil.insert("reportDailyCrowdMapper.insertSelective", reportDailyCrowdDo) > 0;
    }

    public boolean updateById(ReportDailyCrowdDo reportDailyCrowdDo) {
        reportDailyCrowdDo.setUpdatedTime(new Date());
        return DBUtil.update("reportDailyCrowdMapper.updateByPrimaryKeySelective", reportDailyCrowdDo) > 0;
    }

    public boolean existReportDailyCrowd(ReportDailyCrowdDo reportDailyCrowdDo) {
        Integer num = DBUtil.selectOne("reportDailyCrowdMapper.existReportDailyCrowd", reportDailyCrowdDo);
        return num > 0;
    }

    public void updateByDateAndCrowdId(ReportDailyCrowdDo reportDailyCrowdDo) {
        DBUtil.update("reportDailyCrowdMapper.updateByDateAndCrowdId", reportDailyCrowdDo);
    }

    public List<ReportDailyCrowdDo> selectTodayFail() {
        return DBUtil.selectList("reportDailyCrowdMapper.selectTodayFail", null);
    }

    public List<ReportDailyCrowdDo> selectToday() {
        return DBUtil.selectList("reportDailyCrowdMapper.selectToday", null);
    }

    public Page<ReportDailyCrowdDo> queryPageByCrowdId(CrowdRefreshInfoReq req) {
        return DBUtil.selectPage("reportDailyCrowdMapper.queryPageByCrowdId", req, req.getBeginNum(), req.getSize());
    }
}