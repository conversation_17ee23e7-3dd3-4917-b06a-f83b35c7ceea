/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.client.ai;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import javax.annotation.Resource;

import com.xftech.cdp.infra.client.telemarketing.model.resp.AiSendResp;
import com.xftech.cdp.infra.client.usercenter.CisService;
import com.xftech.cdp.infra.client.usercenter.model.BaseCisResp;
import com.xftech.cdp.infra.client.usercenter.model.RegisterInfoByUserNo;
import com.xftech.cdp.infra.config.LogUtil;
import com.xftech.cdp.infra.rocketmq.ai.AiProntoSendMqProducer;
import com.xftech.cdp.infra.rocketmq.dto.AiSendArgs;
import com.xftech.cdp.infra.rocketmq.dto.AiUserData;
import com.xftech.cdp.infra.utils.IDCardUtils;
import com.xftech.cdp.infra.utils.JsonUtil;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.SendResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version $ AiClient, v 0.1 2024/8/20 19:41 lingang.han Exp $
 */
@Slf4j
@Component
public class AiClient {

    @Autowired
    private AiProntoSendMqProducer aiProntoSendMqProducer;

    @Resource
    private CisService cisService;

    public AiSendResp doBatchSendAi(AiSendArgs aiSendArgs) {
        supplementParameters(aiSendArgs);
        AiSendResp aiSendResp = new AiSendResp();
        aiSendResp.setStatus(1);
        SendResult sendResult = aiProntoSendMqProducer.asyncSend(aiSendArgs);
        log.info("send ai, sendResult:{}", JsonUtil.toJson(sendResult));
        if (!Objects.isNull(sendResult) && !Objects.isNull(sendResult.getSendStatus())) {
            return aiSendResp;
        }
        aiSendResp.setStatus(2);
        return aiSendResp;
    }

    /**
     * AI触达 补充必要参数
     *
     * @param aiSendArgs
     */
    private void supplementParameters(AiSendArgs aiSendArgs) {
        try {
            if (aiSendArgs == null || CollectionUtils.isEmpty(aiSendArgs.getUserDataList())) {
                return;
            }
            for (AiUserData userData : aiSendArgs.getUserDataList()) {
                Map<String, Object> params = Optional.ofNullable(userData.getParams()).orElse(Maps.newHashMap());
                Long userNo = userData.getUserNo();
                BaseCisResp<RegisterInfoByUserNo.RespDto> resp = cisService.queryRegisterInfoByUserNo(userNo);
                if (resp != null && resp.isCodeSucceed() && Objects.nonNull(resp.getData())) {
                    RegisterInfoByUserNo.RespDto data = resp.getData();
                    // 产品
                    String app = Optional.ofNullable(data.getApp()).orElse(StringUtils.EMPTY);
                    // 姓名
                    String custName = data.getCustName();
                    // 身份证号
                    String idCardNumber = data.getIdCardNumber();

                    params.putIfAbsent("productName", getProductName(app));
                    params.putIfAbsent("productHotLine", getProductHotLine(app));
                    if (StringUtils.isNotBlank(custName)) {
                        params.putIfAbsent("username", custName);
                        params.putIfAbsent("lastname", custName.substring(0, 1));
                    }
                    if (StringUtils.isNotBlank(idCardNumber)) {
                        params.putIfAbsent("gender", IDCardUtils.getGenderByIdCard(idCardNumber));
                    }
                }
                LogUtil.logDebug("AiClient supplementParameters params={}", JSONObject.toJSONString(params));
                userData.setParams(params);
            }
        } catch (Exception e) {
            log.warn("AiClient supplementParameters error={}", e.getMessage(), e);
        }
    }

    /**
     * 根据app转换为产品名称
     *
     * @param app
     * @return
     */
    private String getProductName(String app) {
        String productName;
        switch (app) {
            case "cxh":
                productName = "畅行花";
                break;
            case "xyf":
                productName = "信用飞";
                break;
            case "fxk":
                productName = "飞行卡";
                break;
            case "lqqx":
                productName = "趣前行";
                break;
            case "fxk01":
                productName = "飞行卡";
                break;
            case "xyf01":
                productName = "信用飞";
                break;
            default:
                productName = StringUtils.EMPTY;
        }
        return productName;
    }

    /**
     * 根据app转换为产品热线
     *
     * @param app
     * @return
     */
    private String getProductHotLine(String app) {
        String productHotLine;
        switch (app) {
            case "cxh":
                productHotLine = "400-600-5222";
                break;
            case "xyf":
                productHotLine = "400-862-5862";
                break;
            case "fxk":
                productHotLine = "400-862-5862";
                break;
            case "lqqx":
                productHotLine = "400-862-5862";
                break;
            case "fxk01":
                productHotLine = "400-862-5862";
                break;
            case "xyf01":
                productHotLine = "400-862-5862";
                break;
            default:
                productHotLine = StringUtils.EMPTY;
        }
        return productHotLine;
    }

}