package com.xftech.cdp.domain.strategy.repository;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.xftech.cdp.BaseTest;
import com.xftech.cdp.infra.rabbitmq.enums.DecisionResultEnum;
import com.xftech.cdp.infra.utils.RedisKeyUtils;
import com.xftech.cdp.infra.utils.RedisUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class DecisionRecordRepositoryTest extends BaseTest {

    @Autowired
    private DecisionRecordRepository decisionRecordRepository;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private StrategyRepository strategyRepository;

    @Test
    public void insert() {
    }

    @Test
    public void insertBatch() {
    }

    @Test
    public void queryDecisionRecordStrategyIds() {
    }

    @Test
    public void countDecisionStrategy() {
//        String tableName = "decision_record_202306";
        String tableNameNo = "202306";
        Long strategyId = 39L;
        String startDate = "2023-05-01";
        String endDate = "2023-06-08";
        Integer countDecisionStrategy = decisionRecordRepository.countDecisionStrategy(tableNameNo, strategyId, startDate, endDate);
        logger.info("countDecisionStrategy={}", countDecisionStrategy);
    }

    @Test
    public void countDecisionUserFlowData() {
//        String tableName = "decision_record_202306";
        String tableNameNo = "202306";
        Long strategyId = 39L;
        String startDate = "2023-05-01";
        String endDate = "2023-06-08";
        Integer failCode = DecisionResultEnum.MARKET_SUB_FAIL.getFailCode();
        Integer num = decisionRecordRepository.countDecisionUserFlowData(tableNameNo, strategyId, startDate, endDate, failCode);
        logger.info("num={}", num);
    }

    @Test
    public void countDecisionResultUserNum() {
//        String tableName = "decision_record_202306";
        String tableNameNo = "202306";
        Long strategyId = 39L;
        String startDate = "2023-05-01";
        String endDate = "2023-06-08";
        Integer decisionResult = 1;
        Integer num = decisionRecordRepository.countDecisionResultUserNum(tableNameNo, strategyId, startDate, endDate, decisionResult);
        logger.info("num={}", num);
    }

    @Test
    public void test() {
        // 业务日期
        LocalDate startDate = LocalDate.now();
        LocalDate endDate = startDate.plusDays(1);
        String startDateStr = LocalDateTimeUtil.format(startDate, "yyyy-MM-dd");
        String endDateStr = LocalDateTimeUtil.format(endDate, "yyyy-MM-dd");
//        String tableName = "decision_record_" + LocalDateTimeUtil.format(startDate, "yyyyMM");
        String tableNameNo = LocalDateTimeUtil.format(startDate, "yyyyMM");

        // 按天缓存key
        String dayKey = RedisKeyUtils.genStatRealtimeStrategyDayKey(startDateStr);

//        redisUtils.set(dayKey, "");

        Set<Long> strategyIdSet = new HashSet<>();
        String strategyIds = redisUtils.get(dayKey);
        if (StringUtils.isNotBlank(strategyIds)) {
            String[] split = strategyIds.split(",");
            for (String item : split) {
                strategyIdSet.add(Long.parseLong(item));
            }
        }
        logger.info("dayKey={}, strategyIdSet={}, redis.value={}", dayKey, strategyIdSet, redisUtils.get(dayKey));
        // 查询执行中的策略ID列表
        List<Long> strategyIds1 = strategyRepository.getExecutingEventStrategyIds();

        // 查询策略结果表中所有的策略ID列表
        List<Long> strategyIds2 = decisionRecordRepository.queryDecisionRecordStrategyIds(tableNameNo, startDateStr, endDateStr);

        // 汇总所有的策略ID
        strategyIdSet.addAll(strategyIds1);
        strategyIdSet.addAll(strategyIds2);

        StringBuffer sb = new StringBuffer();
        for (Long item : strategyIdSet) {
            sb.append(item).append(",");
        }
        sb.deleteCharAt(sb.length() - 1);

        // 将策略ID放到Redis缓存上
        redisUtils.set(dayKey, sb.toString(), RedisUtils.DEFAULT_EXPIRE_DAYS);

        logger.info("dayKey={}, strategyIdSet={}, redis.value={}", dayKey, strategyIdSet, redisUtils.get(dayKey));
    }
}