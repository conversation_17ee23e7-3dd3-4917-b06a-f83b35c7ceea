<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="dispatchTaskMapper">
  <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.strategy.po.DispatchTaskDo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_id" jdbcType="VARCHAR" property="bizId" />
    <result column="association_id" jdbcType="VARCHAR" property="associationId" />
    <result column="dispatch_time" jdbcType="TIMESTAMP" property="dispatchTime" />
    <result column="date_value" jdbcType="INTEGER" property="dateValue" />
    <result column="retry_times" jdbcType="INTEGER" property="retryTimes" />
    <result column="next_dispatch_time" jdbcType="TIMESTAMP" property="nextDispatchTime" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="biz_type" jdbcType="TINYINT" property="bizType" />
    <result column="exec_ret_msg" jdbcType="VARCHAR" property="execRetMsg" />
    <result column="last_finish_time" jdbcType="TIMESTAMP" property="lastFinishTime" />
    <result column="serial_no" jdbcType="VARCHAR" property="serialNo" />
    <result column="d_flag" jdbcType="TINYINT" property="dFlag" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
    <result column="ext_detail" jdbcType="VARCHAR" property="extDetail" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, biz_id, association_id, dispatch_time, date_value, retry_times, next_dispatch_time,
    status, biz_type, exec_ret_msg, last_finish_time, serial_no, d_flag, created_time,
    updated_time,ext_detail
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from dispatch_task
    where id = #{id,jdbcType=BIGINT}
  </select>

  <insert id="insertSelective" keyProperty="id" useGeneratedKeys="true" parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.DispatchTaskDo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into dispatch_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="bizId != null">
        biz_id,
      </if>
      <if test="associationId != null">
        association_id,
      </if>
      <if test="dispatchTime != null">
        dispatch_time,
      </if>
      <if test="dateValue != null">
        date_value,
      </if>
      <if test="retryTimes != null">
        retry_times,
      </if>
      <if test="nextDispatchTime != null">
        next_dispatch_time,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="execRetMsg != null">
        exec_ret_msg,
      </if>
      <if test="lastFinishTime != null">
        last_finish_time,
      </if>
      <if test="serialNo != null">
        serial_no,
      </if>
      <if test="dFlag != null">
        d_flag,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="updatedTime != null">
        updated_time,
      </if>
      <if test="extDetail != null">
        ext_detail,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="bizId != null">
        #{bizId,jdbcType=VARCHAR},
      </if>
      <if test="associationId != null">
        #{associationId,jdbcType=VARCHAR},
      </if>
      <if test="dispatchTime != null">
        #{dispatchTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dateValue != null">
        #{dateValue,jdbcType=INTEGER},
      </if>
      <if test="retryTimes != null">
        #{retryTimes,jdbcType=INTEGER},
      </if>
      <if test="nextDispatchTime != null">
        #{nextDispatchTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=TINYINT},
      </if>
      <if test="execRetMsg != null">
        #{execRetMsg,jdbcType=VARCHAR},
      </if>
      <if test="lastFinishTime != null">
        #{lastFinishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="serialNo != null">
        #{serialNo,jdbcType=VARCHAR},
      </if>
      <if test="dFlag != null">
        #{dFlag,jdbcType=TINYINT},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="extDetail != null">
        #{extDetail},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.DispatchTaskDo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update dispatch_task
    <set>
      <if test="bizId != null">
        biz_id = #{bizId,jdbcType=VARCHAR},
      </if>
      <if test="associationId != null">
        association_id = #{associationId,jdbcType=VARCHAR},
      </if>
      <if test="dispatchTime != null">
        dispatch_time = #{dispatchTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dateValue != null">
        date_value = #{dateValue,jdbcType=INTEGER},
      </if>
      <if test="retryTimes != null">
        retry_times = #{retryTimes,jdbcType=INTEGER},
      </if>
      <if test="nextDispatchTime != null">
        next_dispatch_time = #{nextDispatchTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="bizType != null">
        biz_type = #{bizType,jdbcType=TINYINT},
      </if>
      <if test="execRetMsg != null">
        exec_ret_msg = #{execRetMsg,jdbcType=VARCHAR},
      </if>
      <if test="lastFinishTime != null">
        last_finish_time = #{lastFinishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="serialNo != null">
        serial_no = #{serialNo,jdbcType=VARCHAR},
      </if>
      <if test="dFlag != null">
        d_flag = #{dFlag,jdbcType=TINYINT},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateByPrimaryKey" parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.DispatchTaskDo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update dispatch_task
    set biz_id = #{bizId,jdbcType=VARCHAR},
    association_id = #{associationId,jdbcType=VARCHAR},
    dispatch_time = #{dispatchTime,jdbcType=TIMESTAMP},
    date_value = #{dateValue,jdbcType=INTEGER},
    retry_times = #{retryTimes,jdbcType=INTEGER},
    next_dispatch_time = #{nextDispatchTime,jdbcType=TIMESTAMP},
    status = #{status,jdbcType=TINYINT},
    biz_type = #{bizType,jdbcType=TINYINT},
    exec_ret_msg = #{execRetMsg,jdbcType=VARCHAR},
    last_finish_time = #{lastFinishTime,jdbcType=TIMESTAMP},
    serial_no = #{serialNo,jdbcType=VARCHAR},
    d_flag = #{dFlag,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from dispatch_task
    where  d_flag = 0
    <if test="bizId != null">
      and biz_id = #{bizId}
    </if>
    <if test="associationId != null">
      and association_id = #{associationId}
    </if>
    <if test="dateValue != null">
      and date_value = #{dateValue}
    </if>
    <if test="statusList !=null and statusList.size()>0">
      and status in
      <foreach collection="statusList" item="status" open="(" close=")" separator=",">
        #{status}
      </foreach>
    </if>
  </select>

  <select id="selectLastExecutingTask" resultMap="BaseResultMap">
    select
     <include refid="Base_Column_List" />
    from dispatch_task
    where  d_flag = 0
    <if test="bizId != null">
      and biz_id = #{bizId}
    </if>
    <if test="dateValue != null">
      and date_value = #{dateValue}
    </if>
    <if test="fromStatus != null">
    and status = #{fromStatus}
    </if>
    order by dispatch_time desc
  </select>

  <select id="selectTodoList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from dispatch_task
    where  d_flag = 0
    <if test="bizType != null">
      and biz_type = #{bizType}
    </if>
    <if test="dateValue != null">
      and date_value = #{dateValue}
    </if>
    <if test="dispatchTime != null">
      and dispatch_time &lt;= #{dispatchTime}
    </if>

    <if test="statusList !=null and statusList.size()>0">
      and status in
      <foreach collection="statusList" item="status" open="(" close=")" separator=",">
        #{status}
      </foreach>
    </if>
  </select>

  <update id="updateDispatchTime">
      update dispatch_task
       set dispatch_time = #{dispatchTime}
       <if test="nextDispatchTime != null">
        , next_dispatch_time = #{nextDispatchTime}
       </if>
      where id = #{id} and status = 0;
  </update>

  <update id="updateTaskStatus">
    update dispatch_task
    set status = #{toStatus}
    where id = #{id} and status = #{fromStatus} and d_flag = 0;
  </update>

  <update id="updateTaskFinish">
    update dispatch_task
    set status = #{toStatus},
        last_finish_time = now(),
        exec_ret_msg = LEFT(#{execRetMsg},1020)
    where id = #{id} and status = #{fromStatus} and d_flag = 0;
  </update>

  <update id="updateTaskRetry">
    update dispatch_task
    set status = #{toStatus},
        last_finish_time = now(),
        retry_times = #{retryTimes},
        next_dispatch_time = #{nextTime},
        exec_ret_msg = LEFT(#{execRetMsg},1020)
    where id = #{id} and status = #{fromStatus} and d_flag = 0;
  </update>

</mapper>