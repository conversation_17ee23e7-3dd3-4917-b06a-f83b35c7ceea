package com.xftech.cdp.domain.strategy.exception;

import com.xftech.cdp.infra.exception.BizException;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> wanghu
 * @since : 2022/7/26 14:38:55
 */
@Getter
@Setter
public class TemplateParamException extends BizException {

    private int code;

    public TemplateParamException(String message) {
        super(message);
    }

    public TemplateParamException(int code, String message) {
        super(message);
        this.code = code;
    }

    public TemplateParamException(String message, Throwable cause) {
        super(message, cause);
    }

    public TemplateParamException(int code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }

    public TemplateParamException(Throwable cause) {
        super(cause);
    }

}
