/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.feign.model.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 * @version $ VipCoreResponse, v 0.1 2024/6/13 17:21 lingang.han Exp $
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class VipCoreResponse<T> {

    private Boolean suc;

    private ErrorContext errorContext;

    private String code;

    private String message;

    private T data;


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ErrorContext {
        private String errCode;//错误码
        private String errDesc;//错误文案
    }

    public boolean isSuccess(){
        return suc;
    }
}