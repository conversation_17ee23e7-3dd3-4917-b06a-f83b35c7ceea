package com.xftech.cdp.infra.kafka.producer;

import java.util.Date;
import java.util.Objects;
import java.util.concurrent.Executor;

import javax.annotation.Resource;

import com.xinfei.enginebase.util.apollo.ApolloUtil;

import com.xftech.cdp.domain.strategy.repository.StrategyEngineRepository;
import com.xftech.cdp.infra.client.ads.model.req.ModelPredictionReq;
import com.xftech.cdp.infra.config.LogUtil;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyEngineRateLimitDo;
import com.xftech.cdp.infra.utils.DateUtil;
import com.xftech.cdp.infra.utils.SerialNumberUtil;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.stereotype.Component;

import static com.xftech.cdp.infra.kafka.topic.KafkaTopicEnum.TP_CDP_T0_EVENT_STRATEGY_COUNT;

/**
 * <AUTHOR>
 * @version v1.0 2025/3/31
 * @description T0EventCountProducer
 */
@Slf4j
@Component
public class T0EventCountProducer {

    @Resource(name = "kafkaT0EventCountExecutorWrapper")
    private Executor kafkaT0EventCountExecutorWrapper;

    @Resource
    private KafkaProducer<String, String> kafkaProducer;

    @Resource
    private StrategyEngineRepository strategyEngineRepository;

    public void asyncSend(ModelPredictionReq param) {
        try {
            if (param == null || !ApolloUtil.switchStatus("T0EventCountProducer")) {
                return;
            }
            ModelPredictionReq.BizData bizData = param.getBiz_data();
            if (bizData == null
                    || Objects.isNull(bizData.getUser_no())
                    || Objects.isNull(bizData.getStrategy_id())
                    || StringUtils.isBlank(bizData.getRequestType())) {
                return;
            }

            if (StringUtils.equalsAny(bizData.getRequestType(), "ONLINE", "API")) {
                kafkaT0EventCountExecutorWrapper.execute(() -> {
                    // 策略是否配置了：推入引擎次数限制
                    StrategyEngineRateLimitDo strategyEngineRateLimitDo = strategyEngineRepository.selectByStrategyIdCache(bizData.getStrategy_id());
                    if (strategyEngineRateLimitDo != null) {
                        T0EventCountModel t0EventCountModel = new T0EventCountModel(bizData.getUser_no(), bizData.getStrategy_id(), DateUtil.convertStr(new Date()), SerialNumberUtil.nextId());
                        LogUtil.logDebug("T0EventCountProducer asyncSend t0EventCountModel={}", JSONObject.toJSONString(t0EventCountModel));
                        ProducerRecord<String, String> record = new ProducerRecord<>(TP_CDP_T0_EVENT_STRATEGY_COUNT.getCode(), JSONObject.toJSONString(t0EventCountModel));
                        kafkaProducer.send(record);
                    }
                });
            }
        } catch (Exception e) {
            log.error("T0EventCountProducer asyncSend error", e);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    static class T0EventCountModel {
        private Long user_no;
        private Long strategy_id;
        /**
         * YYYY-MM-DD HH:MM:SS, 如"2025-03-01 08:00:00",
         */
        private String report_time;
        private String report_id;
    }

}