/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.resp;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version $ AppBannerResp, v 0.1 2024/4/18 17:44 benlin.wang Exp $
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AppBannerResp implements Serializable {
    private static final long serialVersionUID = -6579121183015731468L;

    @ApiModelProperty(value = "资源位id")
    private String bannerId;

    @ApiModelProperty(value = "是否命中")
    private Boolean result;

    @ApiModelProperty(value = "策略id列表")
    private List<String> strategyIdList;

    @ApiModelProperty(value = "关联的优惠券id")
    private Map<String, String> strategyId2CouponIdMap;

    @ApiModelProperty(value = "引擎决策-提额前金额，单位：元")
    private Long beforeIncreaseAmount;

    @ApiModelProperty(value = "引擎决策-提额后金额，单位：元")
    private Long afterIncreaseAmount;

    @ApiModelProperty(value = "引擎决策-提额信息")
    private EngineIncreaseInfo engineIncreaseInfo;

    @ApiModelProperty(value = "借款挽留弹窗参数")
    private LoanRetentionResponse loanRetentionResponse;

    @ApiModelProperty(value = "结清挽留弹窗决策结果")
    private SettlementResponse settlementResponse;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LoanRetentionResponse {
        @ApiModelProperty(value = "活动ID")
        private String activityId;

        @ApiModelProperty(value = "弹窗ID")
        private Long appBannerId;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EngineIncreaseInfo {
        /**
         * 提额类型：只涉及提临额（increase_tmp_amount_marketing）
         */
        private String increaseType;
        /**
         * 提额金额，单位：元
         */
        private Long increaseAmount;
        /**
         * 额度有限期，单位：天
         */
        private Long increaseDays;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SettlementResponse {
        @ApiModelProperty(value = "弹窗ID")
        private Long appBannerId;

        @ApiModelProperty(value = "活动ID")
        private String activityId;

        @ApiModelProperty(value = "自定义券活动名称")
        private String activityName;
    }

}