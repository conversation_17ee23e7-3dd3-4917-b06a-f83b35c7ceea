/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2025. All Rights Reserved.
 */

package com.xftech.cdp.infra.repository.cdp.slice.po;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @version $ StrategySliceExecLogMapper, v 0.1 2025/5/4 10:30 xu.fan Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class StrategySliceExecLogDo {

    private Long id;
    private Long strategyId;
    private Long crowdId;
    private Long crowdSliceId;
    private Long crowdVersion;
    private LocalDateTime dispatchTime;
    private Long totalUserCnt;
    private Long execUserCnt;
    private Long dispatchCnt;
    private Integer status;
    private Integer retryTimes;
    private String errorMsg;
    private LocalDateTime createdTime;
    private LocalDateTime updatedTime;
    private Integer dFlag;
    private String createdOp;
    private String updatedOp;
}
