package com.xftech.cdp.infra.config;

import com.ctrip.framework.apollo.ConfigService;
import com.xftech.cdp.domain.strategy.model.dto.AdbRealTimeVariableGrayConfig;
import com.xftech.cdp.domain.strategy.model.dto.EventFlcConfig;
import com.xftech.cdp.domain.strategy.model.dto.LoanFinalFailedReasonDto;
import com.xftech.cdp.domain.strategy.model.dto.StrategyCycleDayConfig;
import com.xftech.cdp.domain.strategy.model.enums.StrategyMarketChannelEnum;
import com.xftech.cdp.infra.utils.JsonUtil;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;

@Service
@AllArgsConstructor
public class AppConfigService {


    public String getXfModelPlatformPredictionUrl() {
        final String key = "xf.model.platform.prediction";
        return ConfigService.getAppConfig().getProperty(key,null);
    }

    public String getXfModelPlatformPredictionToken() {
        final String key = "xf.model.platform.prediction.authorization";
        return ConfigService.getAppConfig().getProperty(key,null);
    }

    public String getXfModelPlatformModelsUrl() {
        final String key = "xf.model.platform.modellist";
        return ConfigService.getAppConfig().getProperty(key,null);
    }

    public String getXfModelPlatformModelsToken() {
        final String key = "xf.model.platform.modellist.authorization";
        return ConfigService.getAppConfig().getProperty(key,null);
    }

    public boolean getSingleDispatchFlcSwitch(StrategyMarketChannelEnum marketChannelEnum) {
        if (marketChannelEnum == StrategyMarketChannelEnum.NONE ||
                marketChannelEnum == StrategyMarketChannelEnum.APP_BANNER) {
            return false;
        }
        String key = String.format("singleDispatchFlc.%s", marketChannelEnum.getCode());
        return ConfigService.getAppConfig().getBooleanProperty(key,false);
    }

    public AdbRealTimeVariableGrayConfig.StrategyGrayConfig getAdbStrategyGrayConfig() {
        final String key = "adb.realTime.variable.strategy.gray";
        String val = ConfigService.getAppConfig().getProperty(key,null);
        return JsonUtil.parse(val, AdbRealTimeVariableGrayConfig.StrategyGrayConfig.class);
    }

    public AdbRealTimeVariableGrayConfig.VariableGrayConfig getAdbVariableGrayConfig() {
        final String key = "adb.realTime.variable.gray";
        String val = ConfigService.getAppConfig().getProperty(key,null);
        return JsonUtil.parse(val, AdbRealTimeVariableGrayConfig.VariableGrayConfig.class);
    }

    public String getAdbRealTimeVariableUrl() {
        final String key = "adb.realTime.variable.url";
        return ConfigService.getAppConfig().getProperty(key,null);
    }

    public String getInclusionExpressionStr() {
        final String key = "adb.realTime.variable.strategy.contain";
        return ConfigService.getAppConfig().getProperty(key,null);
    }

    public String getExclusionExpressionStr() {
        final String key = "adb.realTime.variable.strategy.exclusion";
        return ConfigService.getAppConfig().getProperty(key,null);
    }

    public LoanFinalFailedReasonDto getLoanFinalFailedFailReason() {
        String key = "loanFinalFailed.failReason.list";
        String val = ConfigService.getAppConfig().getProperty(key,null);
        return JsonUtil.parse(val, LoanFinalFailedReasonDto.class);
    }

    public LoanFinalFailedReasonDto getLoanFinalFailedFundFailReasonDetail() {
        String key = "loanFinalFailed.fundFailReasonDetail.list";
        String val = ConfigService.getAppConfig().getProperty(key,null);
        return JsonUtil.parse(val, LoanFinalFailedReasonDto.class);
    }

    // 流控开关，分布式锁
    public boolean getSingleDispatchFlcLockSwitch(StrategyMarketChannelEnum marketChannelEnum) {
        if (marketChannelEnum == StrategyMarketChannelEnum.NONE ||
                marketChannelEnum == StrategyMarketChannelEnum.FILTER) {
            return false;
        }
        String key = String.format("singleDispatchFlc.lock.%s", marketChannelEnum.getCode());
        return ConfigService.getAppConfig().getBooleanProperty(key,true);
    }

    public boolean getSingleIgnoreDispatchFlcLockSwitch() {
        final String key = "singleDispatchFlc.lock.ignore";
        String val = ConfigService.getAppConfig().getProperty(key, "false");
        return Boolean.parseBoolean(val);
    }

    public long getDispatchFlcLockPerWaitMills() {
        final String key = "dispatchFlc.lock.perWaitMills";
        return ConfigService.getAppConfig().getLongProperty(key, 50L);
    }

    public long getDistributeSliceTaskRetryWaitMins() {
        final String key = "Distribute.SliceTask.Retry.WaitMins";
        return ConfigService.getAppConfig().getLongProperty(key, 20L);
    }

    public String getEventTrackingEventName(String eventId) {
        String key = String.format("pulsar.cdp.topic.tracking.event.%s", eventId);
        return ConfigService.getAppConfig().getProperty(key,null);
    }

    public EventFlcConfig getEventFlcConfig() {
        final String key = "eventFlcConfig";
        String val = ConfigService.getAppConfig().getProperty(key,null);
        Map config = JsonUtil.parse(val, Map.class);
        EventFlcConfig eventFlcConfig = new EventFlcConfig();
        eventFlcConfig.setEventFlcMap(config);
        return eventFlcConfig;
    }

    public List<String> getIgnoreDispatchReponseFailedCodesAlarm(StrategyMarketChannelEnum marketChannelEnum) {
        if (marketChannelEnum == null) {
            return new ArrayList<>(0);
        }
        final String key = String.format("dispatch.reponse.ignore.failedCodes.%s", marketChannelEnum.getCode());
        String val = ConfigService.getAppConfig().getProperty(key, "[]");
        return JsonUtil.toList(val, String.class);
    }

    public List<String> getIgnoreDispatchErrorMsgAlarm(StrategyMarketChannelEnum marketChannelEnum) {
        if (marketChannelEnum == null) {
            return new ArrayList<>(0);
        }
        final String key = String.format("dispatch.ignore.errmsg.%s", marketChannelEnum.getCode());
        String val = ConfigService.getAppConfig().getProperty(key, "[]");
        return JsonUtil.toList(val, String.class);
    }

    public boolean getBatchDispatchFlcLockSwitch(StrategyMarketChannelEnum marketChannelEnum) {
        if (marketChannelEnum == StrategyMarketChannelEnum.NONE ||
                marketChannelEnum == StrategyMarketChannelEnum.FILTER ) {
            return false;
        }
        String key = String.format("batchDispatchFlc.lock.%s", marketChannelEnum.getCode());
        return ConfigService.getAppConfig().getBooleanProperty(key, true);
    }

    public StrategyCycleDayConfig getStrategyCycleDayConfig() {
        final String key = "strategyCycleDayConfig";
        String val = ConfigService.getAppConfig().getProperty(key,null);
        return JsonUtil.parse(val, StrategyCycleDayConfig.class);
    }

    public int getDispatchTaskMaxRetryTimes() {
        final String key = "dispatchTaskMaxRetryTime";
        return ConfigService.getAppConfig().getIntProperty(key,7);
    }

    public String getCrowdRunFixedNumerPageExpressionStr() {
        final String key = "crowd.run.fixedNumerPage.contain";
        return ConfigService.getAppConfig().getProperty(key,null);
    }

    public String getPropertyValue(String k, String df) {
        return ConfigService.getAppConfig().getProperty(k, df);
    }

    public int timePassCrowdTask() {
        final String key = "cdp.crowd.task.timePass";
        return ConfigService.getAppConfig().getIntProperty(key, 5);
    }

    public Boolean whetherGrayscaleCrowd(Long crowdId) {
        final String key = "cdp.crowd.stop.white";
        try {
            String val = ConfigService.getAppConfig().getProperty(key, "[]");
            List<Long> res = JsonUtil.toList(val, Long.class);
            if (CollectionUtils.isNotEmpty(res)) {
                return res.contains(crowdId) || res.contains(0L);
            }
        }catch (Exception e){}
        return false;
    }

    public boolean getUserConvertSwitch() {
        final String key = "cdp.strategy.userConvert";
        String val = ConfigService.getAppConfig().getProperty(key, "true");
        return Boolean.parseBoolean(val);
    }

    /**是否关闭人群告警*/
    public boolean getAlarmSwitch() {
        final String key = "cdp.crowd.alarm";
        String val = ConfigService.getAppConfig().getProperty(key, "true");
        return Boolean.parseBoolean(val);
    }

    /**
     * 大数据实时查标签结果异常告警开关
     */
    public boolean getLabelDingAlarmSwitch() {
        final String key = "label.ding.alarm.switch";
        String val = ConfigService.getAppConfig().getProperty(key, "true");
        return Boolean.parseBoolean(val);
    }

    /**
     * 离线策略中实时标签全走特征平台开关
     */
    public String getOfflineStrategyLabel() {
        final String key = "offline.strategy.variable.label";
        return ConfigService.getAppConfig().getProperty(key, null);
    }

    public List<String> getIgnoreStrategyJobExcutingErrors(){
        final String key = "strategy.job.excutingErrorsIgnore";
        String val = ConfigService.getAppConfig().getProperty(key, "[]");
        return JsonUtil.toList(val, String.class);
    }

    public int getBatchQueryUserPageSize() {
        final String key = "batchQueryUserPageSize";
        return ConfigService.getAppConfig()
                .getIntProperty(key, 100);
    }

    public String getIncreaseAmtUrl() {
        final String key = "increaseAmtUrl";
        return ConfigService.getAppConfig()
                .getProperty(key, "http://rcs-provider.xinfei.io/psnlInfo/modify-amt-decision");
    }

    public String getAccessControlUrl() {
        final String key = "accessControlUrl";
        return ConfigService.getAppConfig()
                .getProperty(key, "http://rcs-provider.xinfei.io/common/access-control");
    }

    public String getIncreaseAmtCallbackUrl(){
        final String key = "increaseAmtCallbackUrl";
        return ConfigService.getAppConfig()
                .getProperty(key, "http://api-cdp-backend.xinfei.cn/api/strategy/increaseAmt-notify");
    }

    public String getOfflineInclusionExpressionStr() {
        final String key = "offline.adb.realTime.variable.strategy.contain";
        return ConfigService.getAppConfig().getProperty(key,null);
    }

    public String getOfflineExclusionExpressionStr() {
        final String key = "offline.adb.realTime.variable.strategy.exclusion";
        return ConfigService.getAppConfig().getProperty(key,null);
    }

    public BigDecimal getInstantLabelMinValue(String label){
        String key = "minValue." + label;
        String value = ConfigService.getAppConfig().getProperty(key,"0");
        return new BigDecimal(value);
    }
    public int get24HourDispatchTimesRedisExpire() {
        final String key = "dispatchTimesRedisExpire";
        return ConfigService.getAppConfig().getIntProperty(key,7200);
    }

    public List<String> getFlowSpecialStartTimeLabels(){
        final String key = "flowSpecialStartTimeLabels";
        String val = ConfigService.getAppConfig().getProperty(key, "[]");
        return JsonUtil.toList(val, String.class);
    }

    public List<String> getAmountZeroLimitTempParam() {
        final String key = "amountZeroLimitTempParam";
        String val = ConfigService.getAppConfig().getProperty(key, "[]");
        return JsonUtil.toList(val, String.class);
    }

    public String getAppBannerSourceURL() {
        final String key = "app-banner.source.url";
        return ConfigService.getAppConfig().getProperty(key,null);
    }

    /**
     * 新SSO 开关
     * @return
     */
    public boolean getNewSsoSwitch() {
        final String key = "cdp.new.sso";
        String val = ConfigService.getAppConfig().getProperty(key, "true");
        return Boolean.parseBoolean(val);
    }

    public String getApiHoldDingTalkAlarmURL() {
        final String key = "api-hold.ding.alarm.url";
        return ConfigService.getAppConfig().getProperty(key,
                "https://oapi.dingtalk.com/robot/send?access_token=a22999edb972e5482e903e066d34cee2f976fc5564acbd88bf0864446d5e7985");
    }

    public String getVipCoreTagMatchURL() {
        final String key = "vip-core.tag.match.url";
        return ConfigService.getAppConfig().getProperty(key,
                "http://dev-vipcore.testxinfei.cn/tag/match");
    }

    /**
     * 实时标签 最小值限制
     */
    public Map<String, Object> getLabelMinValue() {
        final String key = "minValue.label";
        String val = ConfigService.getAppConfig().getProperty(key, null);
        return JsonUtil.toMap(val);
    }

    public String getBankCoreMultiQueryBankCardURL() {
        final String key = "bank-core.multi.queryBankCard.url";
        return ConfigService.getAppConfig().getProperty(key,
                "http://dev-bank-core.devxinfei.cn/multi/queryBankCardListHistoryByUserNo");
    }
    public String getNpayApiAgreementQueryCardSignURL() {
        final String key = "npay-api.agreement.queryCardSign.url";
        return ConfigService.getAppConfig().getProperty(key,
                "https://dev-npay-api.devxinfei.cn/agreement/queryCardSignInfo");
    }
}
