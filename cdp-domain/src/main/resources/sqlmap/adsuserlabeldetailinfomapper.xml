<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="adsUserLabelDetailInfo">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xftech.cdp.domain.crowd.model.dispatch.CrowdWereHouse">
        <result column="crowd_id" property="crowdId" />
        <result column="app_user_id" property="appUserId" />
        <result column="app" property="app" />
        <result column="inner_app" property="innerApp" />
        <result column="ab_num" property="abNum" />
        <result column="app_user_id_last2" property= "appUserIdLast2" />
        <result column="mobile" property="mobile" />
        <result column="register_time" property="registerTime" />
        <result column="mobile_utm_source" property="mobileUtmSource" />
        <result column="utm_source_list" property="historyBorrowUtmSource" />
        <result column="last_loan_success_utm_source" property="lastLoanSuccessUtmSource" />
        <result column="c_no" property="cno" />
    </resultMap>

    <select id="queryWareHouseUserInfo" resultMap="BaseResultMap">
        ${sql}
    </select>

    <select id="getValidUserNum" resultType="java.lang.Long">
        ${sql}
    </select>

    <select id="selectMaxAppUserId" resultType="java.lang.Long">
        select max(app_user_id) from ads_user_label_detail_info_df
    </select>

    <select id="selectMinAppUserId" resultType="java.lang.Long">
        select min(app_user_id) from ads_user_label_detail_info_df
    </select>

    <select id="queryUtmByMobileAndApp" resultMap="BaseResultMap">
        select mobile_utm_source,utm_source_list,last_loan_success_utm_source
        from ads_user_label_detail_info_df
        where mobile = #{mobile}
        and app = #{app}
        limit 1
    </select>

    <select id="queryRegisterTimeByAppUserIdAndApp" resultMap="BaseResultMap">
        select register_time
        from ads_user_label_detail_info_df
        where app_user_id = #{appUserId}
        and app = #{app}
        limit 1
    </select>

    <select id="selectBatchStarrocks" resultMap="BaseResultMap">
        select
               c_no,
               app_user_id,
               mobile,
               app,
               inner_app,
               ab_num,
               app_user_id_last2,
               register_time,
               mobile_utm_source,
               utm_source_list,
               last_loan_success_utm_source
        from ${tableName}
        where crowd_id=#{crowdId}
              and pt=#{pt}
              and c_no  &gt; #{cnost}
              and c_no  &lt; #{cnoed}
            limit #{pageSize}
    </select>

    <select id="hasUserRecord" resultMap="BaseResultMap">
        select
            c_no,
            app_user_id,
            mobile,
            app,
            inner_app,
            ab_num,
            app_user_id_last2,
            register_time,
            mobile_utm_source,
            utm_source_list,
            last_loan_success_utm_source
        from ${tableName}
        where crowd_id=#{crowdId}
          and pt=#{pt}
          and app_user_id=#{userId}
    </select>

    <select id="hasUserRecordByIn" resultMap="BaseResultMap">
        select
            crowd_id,
            c_no,
            app_user_id,
            mobile,
            app,
            inner_app,
            ab_num,
            app_user_id_last2,
            register_time,
            mobile_utm_source,
            utm_source_list,
            last_loan_success_utm_source
        from ${tableName}
        where crowd_id in
        <foreach collection="crowdIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
          and pt=#{pt}
          and app_user_id=#{userId}
    </select>


</mapper>
