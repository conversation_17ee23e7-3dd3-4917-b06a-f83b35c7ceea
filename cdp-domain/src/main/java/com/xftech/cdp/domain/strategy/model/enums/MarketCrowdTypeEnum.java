package com.xftech.cdp.domain.strategy.model.enums;

import com.xftech.cdp.domain.strategy.exception.StrategyException;
import lombok.Getter;

import java.util.Objects;

/**
 * 策略营销人群类型枚举类，1:T0用户注册 2:离线人群包 3:不限人群
 * strategy表(market_crowd_type)
 * @<NAME_EMAIL>
 */
@Getter
public enum MarketCrowdTypeEnum {
    /**
     * T0用户注册
     */
    T0_REGISTER(1, "T0用户注册"),


    /**
     * 离线人群包
     */
    CROWD_PACK(2, "离线人群包"),


    /**
     * 不限人群
     */
    NO_LIMIT(3,"不限人群");


    private final Integer code;

    private final String description;

    MarketCrowdTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public static MarketCrowdTypeEnum getInstance(Integer code) {
        for (MarketCrowdTypeEnum marketCrowdTypeEnum : MarketCrowdTypeEnum.values()) {
            if (Objects.equals(marketCrowdTypeEnum.getCode(), code)) {
                return marketCrowdTypeEnum;
            }
        }
        throw new StrategyException(String.format("不存在该人群分组类型：%s", code));
    }
}
