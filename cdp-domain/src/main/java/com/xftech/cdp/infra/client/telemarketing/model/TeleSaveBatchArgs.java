package com.xftech.cdp.infra.client.telemarketing.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @date 2023/3/31 16:42
 */
@Data
public class TeleSaveBatchArgs {
    /**
     * 用户列表数组，字段取shoufuyou_v2库credit_user.id
     */
    @JsonProperty("credit_id_arr")
    @JSONField(name = "credit_id_arr")
    private List<Long> creditIdArr;
    /**
     * 名单节点
     */
    @JsonProperty("user_type")
    @JSONField(name = "user_type")
    private Integer userType;
    /**
     * 名单流水号，同一次名单分批传时必须相同，不超过32个字符
     */
    @JsonProperty("flow_no")
    @JSONField(name = "flow_no")
    private String flowNo;
    /**
     * 总批次数量
     */
    @JsonProperty("batch_count")
    @JSONField(name = "batch_count")
    private Integer batchCount;
    /**
     * 当前批次号，根据总批次数量，如batch_num=3，分批传 current_batch=1,2,3
     */
    @JsonProperty("current_batch")
    @JSONField(name = "current_batch")
    private Integer currentBatch;


}
