package com.xftech.cdp;

import com.alibaba.fastjson.JSON;
import com.xftech.cdp.domain.crowd.model.label.crowd.DataSegment;
import com.xftech.cdp.domain.crowd.model.label.crowd.NewRandom;

public class App {

    public static void main(String[] args) {
        String labelValue = "{\"digits\":\"3\",\"bizKey\":\"vip_price_test\",\"crowdLabelOption\":{\"segments\":[{\"min\":\"000\",\"max\":\"549\"},{\"min\":\"780\",\"max\":\"949\"}]}}";
        NewRandom newRandom = JSON.parseObject(labelValue, NewRandom.class);
        DataSegment dataSegment = JSON.parseObject(newRandom.getCrowdLabelOption(), DataSegment.class);
        String bizKey = newRandom.getBizKey();
    }

}
