package com.xftech.cdp.infra.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * 策略相关配置
 *
 * @<NAME_EMAIL>
 * @date 2023/3/24 15:16
 */
@Getter
@RefreshScope
@Component
public class StrategyConfig {
    /**
     * 分页大小配置
     */
    @Value("${strategy.dispatch.channel.sms.pagesize:5000}")
    private Integer smsBatchSize;

    /**
     * 分页大小配置
     */
    @Value("${strategy.dispatch.channel.push.pagesize:5000}")
    private Integer pushBatchSize;

    /**
     * 分页大小配置
     */
    @Value("${strategy.dispatch.channel.ai.pronto.pagesize:1000}")
    private Integer aiProntoBatchSize;

    /**
     * 分页大小配置
     */
    @Value("${strategy.dispatch.channel.tele.pagesize:2000}")
    private Integer teleBatchSize;
    /**
     * 分页大小配置
     */
    @Value("${strategy.dispatch.channel.coupon.pagesize:5000}")
    private Integer couponBatchSize;

    @Value("${strategy.dispatch.channel.offlineengine.pagesize:1000}")
    private Integer offlieEngineBatchSize;

    @Value("${strategy.dispatch.channel.increaseAmt.pagesize:500}")
    private Integer increaseAmtBatchSize;

    /**
     * 下发明细记录查询 in 大小
     */
    @Value("${strategy.dispatch.detail.query.pagesize:1000}")
    private Integer detailBatchSize;
    /**
     * 空白组批次大小
     */
    @Value("${strategy.dispatch.blank.query.pagesize:500}")
    private Integer blankBatchSize;
    /**
     * 触达指标查询方式 1-查询本地库 2-查询数仓
     */
    @Value("${strategy.dispatch.index.query.way:1}")
    private Integer dispatchIndexQueryWay;

    /**
     * 事件监控当日数据是否读取缓存
     */
    @Value("${strategy.monitor.today.updateFromCache:1}")
    private Integer monitorUpdateFromCache;

    /**
     * 标签查询开关，1-开 2-关
     */
    @Value("${strategy.label.query.switch:1}")
    private Integer labelQuerySwitch;

    /**
     * 标签查询批次大小
     */
    @Value("${strategy.label.query.BatchSize:500}")
    private Integer labelQueryBatchSize;

    @Value("${strategy.rule.max.limitDays:30}")
    private Integer maxLimitDays;

    @Value("${strategy.rule.max.limitTimes:10}")
    private Integer maxLimitTimes;

    /**
     * 实时策略超时重试次数，默认2次
     */
    @Value("${strategy.realtime.timeout.retryTimes:2}")
    private Integer retryTimes;

    /**
     * 实时策略超时重试间隔，单位：秒，默认：60秒
     */
    @Value("${strategy.realtime.timeout.RetryInterval:60}")
    private Integer labelTimeOutRetryInterval;

    /**
     * 离线策略要用的人群包未更新完报警，策略执行前N分钟，默认20分钟
     */
    @Value("${strategy.offline.crowdStatus.alarmInterval:20}")
    private Integer offlineStrategyCrowdStatusAlarmInterval;

    @Value("${strategy.dispatch.record.query.maxDays:90}")
    private Integer hasDispatchRecordDays;
}
