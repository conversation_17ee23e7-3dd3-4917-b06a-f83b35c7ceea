/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.rocketmq.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ AiCallBackMessageVO, v 0.1 2024/8/26 10:59 lingang.han Exp $
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AiCallBackMessageVO implements Serializable {
    private static final long serialVersionUID = -6933003588940926121L;

    private String batchNo;

    /**
     * FILTER:过滤
     * CALLED:拨打
     * FAILURE:失败
     */
    private String status;

    /**
     * 名单拨打详情
     */
    private List<AiCallBackCallDetail> callDetails;

    /**
     * 名单过滤详情
     */
    private List<AiCallBackFilterDetail> filterDetails;

    /**
     * 名单失败详情
     */
    private List<AiCallBackFailureDetail> failureDetails;
}