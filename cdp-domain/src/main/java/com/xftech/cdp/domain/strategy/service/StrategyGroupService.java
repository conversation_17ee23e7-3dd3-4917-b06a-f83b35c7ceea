package com.xftech.cdp.domain.strategy.service;

import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;

import java.util.List;
import java.util.function.BiPredicate;

/**
 * <AUTHOR> xugong<PERSON>@mzsk.com
 * @date 2023/4/23 10:37
 */
public interface StrategyGroupService {
    /**
     * 策略分组匹配
     *
     * @param bizKey        场景值
     * @param matchFunction 分组匹配函数
     * @param list          人群明细集合
     * @return 符合分组的配置的人群
     */
    List<CrowdDetailDo> matchGroupRule(String bizKey, BiPredicate<String, Integer> matchFunction, List<CrowdDetailDo> list);
}
