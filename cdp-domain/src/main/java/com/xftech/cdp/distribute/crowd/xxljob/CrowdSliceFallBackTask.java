/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2025. All Rights Reserved.
 */

package com.xftech.cdp.distribute.crowd.xxljob;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.xftech.cdp.distribute.crowd.enums.CrowdCreateTypeEnum;
import com.xftech.cdp.distribute.crowd.enums.CrowdSliceStatusEnum;
import com.xftech.cdp.distribute.crowd.feign.CrowdFeignClient;
import com.xftech.cdp.distribute.crowd.repository.CrowdInfoRepository;
import com.xftech.cdp.distribute.crowd.repository.CrowdSliceRepository;
import com.xftech.cdp.distribute.crowd.repository.model.CrowdInfoDo;
import com.xftech.cdp.distribute.crowd.repository.model.CrowdInfoVersionDo;
import com.xftech.cdp.distribute.crowd.repository.model.CrowdSliceDo;
import com.xftech.cdp.distribute.crowd.service.CrowdSliceService;
import com.xftech.cdp.infra.config.ApolloUtil;
import com.xftech.cdp.infra.utils.DateUtil;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.leo.datainsight.client.rr.api.request.ApiListCrowdInfoRequest;
import com.leo.datainsight.client.rr.api.response.ApiListCrowdInfoResponse;
import com.leo.datainsight.client.rr.response.RestResponse;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import static com.xftech.cdp.infra.config.ApolloKeyConstants.INSIGHT_CROWD_PACK_WHITELIST_FALLBACK_SLICE_CROWD_PACK;

/**
 * 人群分片兜底任务
 */
@Slf4j
@Service
public class CrowdSliceFallBackTask {

    @Value("${CrowdSliceFallBackTask.getOssInfoRetryCount:3}")
    private Integer getOssInfoRetryCount;

    @Resource
    private CrowdFeignClient crowdFeignClient;
    @Resource
    private CrowdInfoRepository crowdInfoRepository;
    @Resource
    private CrowdSliceRepository crowdSliceRepository;
    @Resource
    private CrowdSliceService crowdSliceService;

    @XxlJob("CrowdSliceFallBackTask")
    public ReturnT<String> crowdSyncFallBackTask(String param) {
        if (ApolloUtil.switchStatus("crowdSyncFallBackTaskClose")) {
            return ReturnT.SUCCESS;
        }

        Stopwatch stopwatch = Stopwatch.createStarted();
        log.info("CrowdSliceFallBackTask 人群分片兜底任务开始");
        try {
            List<CrowdInfoDo> crowdInfoDos = crowdInfoRepository.selectAllOnline();
            if (CollectionUtils.isEmpty(crowdInfoDos)) {
                return ReturnT.SUCCESS;
            }

            // 每日刷新的人群包
            List<Long> ruleBasedCrowdIds = crowdInfoDos.stream()
                    .filter(CrowdInfoDo::isValid)
                    .filter(x -> Objects.equals(x.getCrowdCreateType(), CrowdCreateTypeEnum.RULE_BASED.getStatus()))
                    .filter(x -> ApolloUtil.isInTheWhitelist(INSIGHT_CROWD_PACK_WHITELIST_FALLBACK_SLICE_CROWD_PACK, x.getCrowdId()))
                    .map(CrowdInfoDo::getCrowdId)
                    .collect(Collectors.toList());

            long todayVersion = Long.parseLong(DateUtil.dayOfInt(new Date()) + "01");
            for (Long crowdId : ruleBasedCrowdIds) {
                // 1.是否存在当日分片
                CrowdSliceDo crowdSliceToday = crowdSliceRepository.existTodaySliceCrowdVersion(crowdId, todayVersion);
                if (crowdSliceToday != null) {
                    log.info("CrowdSliceFallBackTask 当日已存在分片纪录, crowdId={}, crowdSliceToday={}", crowdId, JSONObject.toJSONString(crowdSliceToday));
                    continue;
                }
                log.warn("CrowdSliceFallBackTask 人群包未分片-兜底分片开始, crowdId={}, todayVersion={}", crowdId, todayVersion);
                // 2.查询oss信息
                List<ApiListCrowdInfoResponse> ossInfos = queryOssInfo(Lists.newArrayList(crowdId));
                if (CollectionUtils.isEmpty(ossInfos)) {
                    log.error("CrowdSliceFallBackTask 人群包未分片-兜底分片失败-未查询到oss信息, crowdId={}, todayVersion={}", crowdId, todayVersion);
                    continue;
                }
                // 3.分片入库
                CrowdInfoVersionDo crowdInfoVersionDo = convertToCrowdInfoVersionDo(crowdId, ossInfos.get(0));
                crowdSliceService.crowdSlice(crowdInfoVersionDo, "异常兜底分片");
            }
        } catch (DuplicateKeyException e) {
            log.warn("CrowdSliceFallBackTask 人群分片兜底任务异常-版本重复", e);
            return ReturnT.FAIL;
        } catch (Exception e) {
            log.error("CrowdSliceFallBackTask 人群分片兜底任务异常", e);
            return ReturnT.FAIL;
        }
        log.info("CrowdSliceFallBackTask 人群分片兜底任务结束, 耗时:{}", stopwatch.stop().elapsed(TimeUnit.MILLISECONDS));
        return ReturnT.SUCCESS;
    }

    /**
     * 查询oss信息
     *
     * @param crowdIds
     * @return
     */
    private List<ApiListCrowdInfoResponse> queryOssInfo(List<Long> crowdIds) {
        List<ApiListCrowdInfoResponse> ossInfos = Lists.newArrayList();
        try {
            ApiListCrowdInfoRequest ossQueryRequest = new ApiListCrowdInfoRequest();
            ossQueryRequest.setIds(crowdIds);
            ossQueryRequest.setNeedLatest(Boolean.TRUE);

            // 查询oss信息,异常重试3次
            int retryCount = 1;
            while (ossInfos.isEmpty() && retryCount <= getOssInfoRetryCount) {
                retryCount++;
                try {
                    RestResponse<List<ApiListCrowdInfoResponse>> ossQueryResponse = crowdFeignClient.queryListCrowdInfo(ossQueryRequest);
                    log.info("CrowdSliceFallBackTask queryOssInfo ossQueryRequest={}, ossQueryResponse={}, retryCount={}", JSONObject.toJSONString(ossQueryRequest), JSONObject.toJSONString(ossQueryResponse), retryCount);
                    if (ossQueryResponse != null && Objects.equals(ossQueryResponse.getCode(), 200) && ossQueryResponse.getData() != null) {
                        ossInfos.addAll(Optional.ofNullable(ossQueryResponse.getData()).orElse(Lists.newArrayList()));
                    }
                } catch (Exception e) {
                    log.warn("CrowdSliceFallBackTask queryOssInfo error, crowdIds={}", crowdIds, e);
                }
            }
        } catch (Exception e) {
            log.error("CrowdSliceFallBackTask queryOssInfo error", e);
        }
        return ossInfos;
    }

    /**
     * 转换为人群包版本信息
     *
     * @param crowdId
     * @param crowdInfoResponse
     * @return
     */
    private CrowdInfoVersionDo convertToCrowdInfoVersionDo(Long crowdId, ApiListCrowdInfoResponse crowdInfoResponse) {
        CrowdInfoVersionDo crowdInfoVersionDo = new CrowdInfoVersionDo();
        crowdInfoVersionDo.setCrowdId(crowdId);
        crowdInfoVersionDo.setCrowdVersion(Long.parseLong(DateUtil.dayOfInt(new Date()) + "01"));
        crowdInfoVersionDo.setCrowdOssFolder(crowdInfoResponse.getOssFolder());
        if (CollectionUtils.isNotEmpty(crowdInfoResponse.getOssFile())) {
            crowdInfoVersionDo.setCrowdOssFile(String.join(",", crowdInfoResponse.getOssFile()));
        }
        crowdInfoVersionDo.setCrowdSize(crowdInfoResponse.getEstimatedQuantity());
        crowdInfoVersionDo.setRawVersion(null);
        crowdInfoVersionDo.setSliceStatus(CrowdSliceStatusEnum.INIT.getStatus());
        return crowdInfoVersionDo;
    }

}