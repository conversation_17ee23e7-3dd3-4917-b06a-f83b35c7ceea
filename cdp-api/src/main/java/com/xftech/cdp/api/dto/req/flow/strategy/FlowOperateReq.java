/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.req.flow.strategy;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @version $ FlowStrategyOperateReq, v 0.1 2023/12/20 16:36 yye.xu Exp $
 */

@Data
public class FlowOperateReq {
    @ApiModelProperty(value = "flowNo", required = true)
    private String flowNo;

    @ApiModelProperty(value = "执行类型: 0-暂停,1-开启,3-发布")
    private Integer runType;
}