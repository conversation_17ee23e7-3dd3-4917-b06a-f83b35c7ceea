package com.xftech.cdp.infra.rabbitmq.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * RabbitMq 配置
 * <AUTHOR>
 * @since 2023-04-06
 */
@Configuration
@RefreshScope
@Getter
@Setter
public class SmsMqConfig {
    /**
     * mq批量消费大小
     */
    @Value("${spring.rabbitmq.batchSize:50}")
    private Integer batchSize;
    /**
     * mq消费超时时间
     */
    @Value("${spring.rabbitmq.timeout:10000}")
    private Integer timeout;

    @Value("${spring.rabbitmq.host}")
    private String host;
    @Value("${spring.rabbitmq.port}")
    private int port;
    @Value("${spring.rabbitmq.username}")
    private String username;
    @Value("${spring.rabbitmq.password}")
    private String password;
    @Value("${spring.rabbitmq.virtual-host}")
    private String virtualHost;

    @Value("${sms.report.exchange}")
    private String smsReportExchange;
    @Value("${sms.report.exchangeType}")
    private String smsExchangeType;
    @Value("sms_center_callback_app_xyf-cdp")
    private String smsReportRoutingKey;
    @Value("sms_supplier_report_callback")
    private String smsReportQueueName;
}
