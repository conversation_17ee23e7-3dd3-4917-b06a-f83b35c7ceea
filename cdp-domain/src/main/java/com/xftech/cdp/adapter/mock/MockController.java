/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.adapter.mock;

import javax.annotation.Resource;

import com.xftech.cdp.api.dto.base.Response;
import com.xftech.cdp.infra.pulsar.service.impl.TrackingLoanApplyViewServiceImpl;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class MockController {

    @Resource
    private TrackingLoanApplyViewServiceImpl trackingLoanApplyViewServiceImpl;

    @PostMapping("/Tracking_LoanApplyView/msg")
    public Response checkUserEligibility(@RequestBody MockMsgModel msg) {
        trackingLoanApplyViewServiceImpl.consumer(msg.getMessageId(), msg.getMessage());
        return Response.success();
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MockMsgModel {
        private String messageId;
        private String message;
    }

}