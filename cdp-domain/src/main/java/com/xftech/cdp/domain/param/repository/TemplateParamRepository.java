package com.xftech.cdp.domain.param.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.base.database.Page;
import com.xftech.cdp.api.dto.req.param.TemplateParamListReq;
import com.xftech.cdp.infra.repository.cdp.param.po.TemplateParamDo;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class TemplateParamRepository {
    /**
     * 根据主键id查询
     *
     * @param id id
     * @return id对应的记录
     */
    public TemplateParamDo selectById(Long id) {
        return DBUtil.selectOne("templateParamMapper.selectByPrimaryKey", id);
    }

    public TemplateParamDo selectByName(String name) {
        return DBUtil.selectOne("templateParamMapper.selectByName", name);
    }

    public TemplateParamDo selectByKey(String key) {
        return DBUtil.selectOne("templateParamMapper.selectByKey", key);
    }
    /**
     * 插入
     *
     * @param param 对象
     * @return 是否插入成功标识
     */
    public boolean insert(TemplateParamDo param) {
        return DBUtil.insert("templateParamMapper.insertSelective", param) > 0;
    }

    /**
     * 根据主键id更新
     *
     * @param param 对象
     * @return 是否更新成功标识
     */
    public boolean updateById(TemplateParamDo param) {
        return DBUtil.update("templateParamMapper.updateByPrimaryKeySelective", param) > 0;
    }

    /**
     * 查询所有模板参数
     *
     * @return 所有模板参数
     */
    public List<TemplateParamDo> getAll() {
        return DBUtil.selectList("templateParamMapper.getAll", null);
    }

    public Page<TemplateParamDo> selectByPage(TemplateParamListReq templateParamListReq) {
        return DBUtil.selectPage("templateParamMapper.selectByPage", templateParamListReq, templateParamListReq.getBeginNum(), templateParamListReq.getSize());
    }
}
