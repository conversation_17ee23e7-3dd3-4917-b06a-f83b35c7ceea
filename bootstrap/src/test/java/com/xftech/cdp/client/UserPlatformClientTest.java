package com.xftech.cdp.client;

import com.xftech.cdp.BaseTest;
import com.xftech.cdp.infra.client.appcore.UserCoreClient;
import com.xftech.cdp.infra.client.appcore.request.BalanceOptReq;
import com.xftech.cdp.infra.client.appcore.response.BalanceOptResp;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version $ UserPlatformClientTest, v 0.1 2025/1/8 13:33 mingwen.zang Exp $
 */
public class UserPlatformClientTest extends BaseTest {
    @Resource
    UserCoreClient userPlatformClient;

    @Value("${local.user.no}")
    Long userNo;

    @Test
    public void testAddBalance() {
        BalanceOptResp resp = userPlatformClient.increaseBalance(BalanceOptReq
                .builder()
                .userNo(userNo)
                .amount(new BigDecimal("1"))
                .description("活动返现测试").build());
        assert resp != null;
    }

}