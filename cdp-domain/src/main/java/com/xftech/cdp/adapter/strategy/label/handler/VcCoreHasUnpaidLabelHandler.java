/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.adapter.strategy.label.handler;

import com.xftech.cdp.adapter.strategy.label.LabelHandler;
import com.xftech.cdp.adapter.strategy.label.QueryLabelRequest;
import com.xftech.cdp.adapter.strategy.label.request.VipCoreTagMatchRequest;
import com.xftech.cdp.adapter.strategy.label.request.VipCoreTagMatchResponse;
import com.xftech.cdp.infra.client.ads.model.resp.AdsLabelResp;

import com.xftech.cdp.infra.config.ApolloUtil;
import com.xftech.cdp.infra.exception.NoneException;
import com.xftech.cdp.infra.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.*;

/**
 * <AUTHOR>
 * @version $ IsBankcardPaymentAgreementLabelHandler, v 0.1 2024/12/27 10:10 tianshuo.qiu Exp $
 */
@Slf4j
@Component
public class VcCoreHasUnpaidLabelHandler implements LabelHandler {
    @Autowired
    private RestTemplate restTemplate;

    @Override
    public String getLabel() {
        return "vc_core_has_unpaid";
    }

    @Override
    public AdsLabelResp execute(String label, QueryLabelRequest batchAdsLabelVO) {
        List<AdsLabelResp.Param> result = new ArrayList<>();

        for (Long userNo : batchAdsLabelVO.getUserNoList()) {
                VipCoreTagMatchRequest req = new VipCoreTagMatchRequest();
                req.setUserNo(userNo);
                req.setTags(Collections.singletonList(getLabel()));
                VipCoreTagMatchResponse response = request(req);

                if ( Objects.nonNull(response) && response.getCode().equals("1")
                        && Objects.nonNull(response.getData())) {
                    Map<String, Object> data = response.getData();
                    if (data.containsKey(getLabel())) {
                        Object value = data.get(getLabel());
                        boolean isVip = Boolean.parseBoolean(value.toString());
                        result.add(new AdsLabelResp.Param(userNo, null, String.valueOf(isVip)));
                    }
                }else {
                    result.add(new AdsLabelResp.Param(userNo, null, null));
                }

        }
        return new AdsLabelResp(label, result);
    }


    private VipCoreTagMatchResponse request(VipCoreTagMatchRequest req) {
        String uri = ApolloUtil.getAppProperty("vipcore.url","http://qa1-vipcore.testxinfei.cn/tag/match");
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> requestEntity = new HttpEntity<>(req, headers);
        try {
            ResponseEntity<String> responseEntity = restTemplate.exchange(uri, HttpMethod.POST, requestEntity, String.class);
            VipCoreTagMatchResponse resp = JsonUtil.parse(responseEntity.getBody(), VipCoreTagMatchResponse.class);
            log.info("接口请求日志:[{}],url:{}, header:{}, request:{},resp:{}",
                    getLabel(),
                    uri, JsonUtil.toJson(headers), JsonUtil.toJson(req), JsonUtil.toJson(responseEntity));
            if (resp == null || !Objects.equals(responseEntity.getStatusCode(),HttpStatus.OK)) {
                log.error("接口返回错误码:[{}],url:{},header:{}, request:{},resp:{}",
                        getLabel(),
                        uri, JsonUtil.toJson(headers), JsonUtil.toJson(req),
                        JsonUtil.toJson(responseEntity), NoneException.catError());
            }
            return resp;
        } catch (Exception ex) {
            log.error("接口异常:[{}],url:{},request:{}", getLabel(), uri, JsonUtil.toJson(requestEntity), ex);
        }
        return null;
    }
}