package com.xftech.cdp.infra.utils;

import com.xftech.cdp.infra.exception.BizException;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * @<NAME_EMAIL>
 */
public class HttpContextUtil {

    public static HttpServletRequest getHttpServletRequest() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes == null) {
            return null;
        }
        return ((ServletRequestAttributes) requestAttributes).getRequest();
    }

    public static String getDomain() {
        HttpServletRequest request = getHttpServletRequest();
        if (null == request) {
            throw new BizException("获取请求异常");
        }
        StringBuffer url = request.getRequestURL();
        return url.delete(url.length() - request.getRequestURI().length(), url.length()).toString();
    }

    public static String getOrigin() {
        HttpServletRequest request = getHttpServletRequest();
        if (null == request) {
            throw new BizException("获取请求异常");
        }
        return request.getHeader("Origin");
    }
}
