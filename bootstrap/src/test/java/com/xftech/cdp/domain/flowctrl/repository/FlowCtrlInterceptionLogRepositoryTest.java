package com.xftech.cdp.domain.flowctrl.repository;

import com.xftech.cdp.BaseTest;
import com.xftech.cdp.infra.repository.cdp.flowctrl.po.FlowCtrlGroupNum;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class FlowCtrlInterceptionLogRepositoryTest extends BaseTest {

    @Autowired
    private FlowCtrlInterceptionLogRepository flowCtrlInterceptionLogRepository;

    @Test
    public void selectById() {
    }

    @Test
    public void insert() {
    }

    @Test
    public void updateById() {
    }

    @Test
    public void saveBatch() {
    }

    @Test
    public void countStrategyGroupFlowCtrl() {
        Long strategyId = 1L;
        String startDate = "2023-06-01";
        String endDate = "2023-06-06";
        Integer num = flowCtrlInterceptionLogRepository.countStrategyGroupFlowCtrl(strategyId, null, startDate, endDate);
        logger.info("num={}", num);
    }
}