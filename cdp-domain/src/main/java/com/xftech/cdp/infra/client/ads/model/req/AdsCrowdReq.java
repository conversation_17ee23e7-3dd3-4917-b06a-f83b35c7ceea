/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.infra.client.ads.model.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ AdsCrowdReq, v 0.1 2023/11/21 13:59 wancheng.qu Exp $
 */

@Data
public class AdsCrowdReq implements Serializable {


    private List<DataItem> data;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DataItem {
        private Long crowdId;
        private String crowdSql;
        private Integer priority = 1;

        public DataItem(Long crowdId, String crowdSql) {
            this.crowdId = crowdId;
            this.crowdSql = crowdSql;
        }
    }
}
