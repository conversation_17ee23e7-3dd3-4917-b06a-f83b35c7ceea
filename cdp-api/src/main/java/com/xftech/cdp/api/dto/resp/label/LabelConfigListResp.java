/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.resp.label;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version $ LabelConfigListResp, v 0.1 2024/6/20 15:50 lingang.han Exp $
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LabelConfigListResp implements Serializable {
    private static final long serialVersionUID = 2650330812259477216L;

    /*metaLabel表数据 */
    @ApiModelProperty(value = "元数据标签表id")
    private Long mlId;

    private Long dataId;

    @ApiModelProperty(value = "标签名称")
    private String labelName;

    @ApiModelProperty(value = "标签code")
    private String labelCode;

    @ApiModelProperty(value = "数据类型")
    private String labelDataValueType;

    @ApiModelProperty(value = "聚合维度")
    private String aggDimension;

    private Integer available;

    @ApiModelProperty(value = "麻雀是否上线")
    private Integer online;

    @ApiModelProperty(value = "标签枚举值")
    private List<String> labelEnumValues;


    /* Label表数据 */
    @ApiModelProperty(value = "麻雀已上线标签id")
    private Long lId;

    @ApiModelProperty(value = "麻雀一级分类")
    private Integer primaryLabel;

    @ApiModelProperty(value = "麻雀二级分类")
    private Integer secondaryLabel;

    @ApiModelProperty(value = "麻雀展示名称")
    private String lLabelName;

    @ApiModelProperty(value = "注释")
    private String description;

    @ApiModelProperty(value = "业务线")
    private String businessType;

    @ApiModelProperty(value = "麻雀上线时间")
    private LocalDateTime lCreatedTime;

    @ApiModelProperty(value = "交互类型")
    private Integer exchangeType;

    @ApiModelProperty(value = "上线标签枚举")
    private Map<String, String> onLineLabelEnum;

}