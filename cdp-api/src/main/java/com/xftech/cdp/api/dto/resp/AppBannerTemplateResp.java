/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version $ AppBannerTemplateResp, v 0.1 2024/4/18 19:29 benlin.wang Exp $
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AppBannerTemplateResp implements Serializable {
    private static final long serialVersionUID = -6579121183015731468L;

    @ApiModelProperty(value = "业务归属")
    private String business;

    @ApiModelProperty(value = "弹窗id")
    private String id;

    @ApiModelProperty(value = "弹框状态")
    private String status;

    @ApiModelProperty(value = "展示app")
    private String app;

    @ApiModelProperty(value = "弹框名称")
    private String name;

    @ApiModelProperty(value = "样式类型")
    private String style;
}