package com.xftech.cdp.domain.flowctrl.model.dto;

import com.xftech.cdp.domain.strategy.model.enums.StrategyRulerEnum;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.flowctrl.po.FlowCtrlDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketChannelDo;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @<NAME_EMAIL>
 * @date 2023/6/8 13:54
 */
@Data
public class FlowCtrlDto {
    /**
     * 表序号
     */
    private String tableNo;
    /**
     * 渠道
     */
    private StrategyMarketChannelDo marketChannelDo;
    /**
     * 用户明细集合
     */
    private List<CrowdDetailDo> list;
    /**
     *
     */
    private List<FlowCtrlDo> flowCtrlRuleList;
    /**
     * 消息ID
     */
    private String messageId;
    /**
     * 上报时间
     */
    private LocalDateTime triggerDatetime;

    private StrategyRulerEnum strategyRulerEnum;

    private String bizEventType;

}
