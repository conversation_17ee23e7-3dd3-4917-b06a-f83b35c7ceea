package com.xftech.cdp.domain.strategy.repository;

import javax.annotation.Resource;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.infra.config.LogUtil;
import com.xftech.cdp.infra.constant.RedisKeyConstants;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyEngineRateLimitDo;
import com.xftech.cdp.infra.utils.RedisUtils;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

/**
 * T0引擎策略-推入引擎频次限制表
 *
 * <AUTHOR>
 * @since 2023/2/9 01:38
 */
@Slf4j
@Repository
public class StrategyEngineRepository {

    @Resource
    private RedisUtils redisUtils;

    public boolean insert(StrategyEngineRateLimitDo strategyEngineRateLimitDo) {
        return DBUtil.insert("strategyengine.insertSelective", strategyEngineRateLimitDo) > 0;
    }

    public StrategyEngineRateLimitDo selectByStrategyId(Long strategyId) {
        return DBUtil.selectOne("strategyengine.selectByStrategyId", strategyId);
    }

    public boolean updateByPrimaryKeySelective(StrategyEngineRateLimitDo strategyEngineRateLimitDo) {
        return DBUtil.update("strategyengine.updateByPrimaryKeySelective", strategyEngineRateLimitDo) > 0;
    }

    public int deleteById(Long id) {
        return DBUtil.update("strategyengine.deleteByPrimaryKey", id);
    }

    public StrategyEngineRateLimitDo selectByStrategyIdCache(Long strategyId) {
        try {
            // 从redis中获取
            String key = String.format(RedisKeyConstants.STRATEGY_ENGINE_RATE_LIMIT, strategyId);
            String strategyEngineRateLimitDoCache = redisUtils.get(key);
            LogUtil.logDebug("StrategyEngineRepository selectByStrategyIdCache strategyEngineRateLimitDoCache={}", strategyEngineRateLimitDoCache);
            if (StringUtils.isNotBlank(strategyEngineRateLimitDoCache)) {
                return JSON.parseObject(strategyEngineRateLimitDoCache, StrategyEngineRateLimitDo.class);
            }

            // 从数据库中获取
            StrategyEngineRateLimitDo strategyEngineRateLimitDo = selectByStrategyId(strategyId);
            LogUtil.logDebug("StrategyEngineRepository selectByStrategyIdCache strategyEngineRateLimitDo={}", JSON.toJSONString(strategyEngineRateLimitDo));
            if (strategyEngineRateLimitDo != null) {
                redisUtils.set(key, strategyEngineRateLimitDo, RedisUtils.DEFAULT_EXPIRE_SECONDS);
            }
            return strategyEngineRateLimitDo;
        } catch (Exception e) {
            log.error("StrategyEngineRepository selectByStrategyIdCache error", e);
            return null;
        }
    }

}
