package com.xftech.cdp.domain.event.model.enums;

import lombok.Getter;

/**
 * 数据处理器枚举信息
 * <AUTHOR>
 * @version $ DataProcessEnum, v 0.1 2024/11/12 17:42 snail Exp $
 */
@Getter
public enum DataProcessEnum {

    CONVERT("convert","数据类型转换"),
    CALCULATE("calculate","数值计算"),
    MAPPING("mapping","数据映射"),
    FORMAT("format","日期格式化"),
    AMOUNT("amount","金额转换"),
    DEFAULT("default","默认值"),
    CONSTANT("constant","常量"),
    ;
    DataProcessEnum(String name, String desc){
        this.name = name;
        this.desc = desc;
    }

    /** 处理器名称 */
    private final String name;
    /** 处理器描述 */
    private final String desc;
}
