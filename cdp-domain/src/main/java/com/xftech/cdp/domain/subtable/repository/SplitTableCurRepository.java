package com.xftech.cdp.domain.subtable.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.infra.repository.subtable.po.SplitTableCur;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.Map;

@Repository
public class SplitTableCurRepository {
    public SplitTableCur selectSplitTableCurById(Long id) {
        return DBUtil.selectOne("splitTableCur.selectSplitTableCurById", id);
    }

    public SplitTableCur selectTableCurNo(String tableName) {
        return DBUtil.selectOne("splitTableCur.selectTableCurNo", tableName);
    }

    public boolean insertSplitTableCur(SplitTableCur record) {
        return DBUtil.insert("splitTableCur.insertSplitTableCur", record) > 0;
    }

    public boolean updateSplitTableCurById(SplitTableCur record) {
        return DBUtil.update("splitTableCur.updateSplitTableCurById", record) > 0;
    }

    public boolean updateSplitTableCurNo(int curTableNo, String tableName) {
        Map<String, Object> param = new HashMap<>();
        param.put("curTableNo", curTableNo);
        param.put("tableName", tableName);
        return DBUtil.update("splitTableCur.updateSplitTableCurNo", param) > 0;
    }
}
