package com.xftech.cdp.infra.repository.cdp.crowd.po;

import com.xftech.cdp.infra.repository.Do;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CrowdLabelPrimaryDo extends Do {

    /**
     * 人群包id
     */
    private Long crowdId;

    /**
     * 人群圈选，0:圈选标签， 1: 排除标签
     */
    private Integer labelGroupType;

    /**
     * 一级标签，0:用户属性， 1：用户行为， 2：黑名单
     */
    private Integer primaryLabel;

    /**
     * 与上一个一级标签的关系，0:或，1:且
     */
    private Integer primaryLabelRelation;

    /**
     * 执行顺序号
     */
    private Integer execIndex;

}
