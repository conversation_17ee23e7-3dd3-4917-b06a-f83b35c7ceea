/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.domain.stat.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.xftech.cdp.api.dto.req.StrategyCreateReq;
import com.xftech.cdp.domain.stat.entity.StatOfflineStrategyGroupDataEntity;
import com.xftech.cdp.domain.stat.entity.StatStrategyGroupDataEntity;
import com.xftech.cdp.domain.stat.repository.StatOfflineStrategyGroupDataRepository;
import com.xftech.cdp.domain.stat.repository.StatStrategyGroupDataRepository;
import com.xftech.cdp.domain.strategy.model.dto.EngineStrategyGroupIdInfoDto;
import com.xftech.cdp.domain.strategy.model.enums.*;
import com.xftech.cdp.domain.strategy.repository.*;
import com.xftech.cdp.domain.strategy.service.StrategyService;
import com.xftech.cdp.infra.constant.RedisKeyConstants;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyExecLogDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyGroupDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketChannelDo;
import com.xftech.cdp.infra.utils.RedisKeyUtils;
import com.xftech.cdp.infra.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $ StatStrategyGroupDataServiceImpl, v 0.1 2023/12/22 16:04 lingang.han Exp $
 */
@Slf4j
@Service
public class StatStrategyGroupDataServiceImpl {

    @Autowired
    private StrategyService strategyService;
    @Autowired
    private StrategyGroupRepository strategyGroupRepository;
    @Autowired
    private EventPushBatchRepository eventPushBatchRepository;
    @Autowired
    private DecisionRecordRepository decisionRecordRepository;
    @Autowired
    private UserDispatchDetailRepository userDispatchDetailRepository;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private StatStrategyGroupDataRepository statStrategyGroupDataRepository;
    @Autowired
    private StatOfflineStrategyGroupDataRepository strategyGroupDataRepository;
    @Autowired
    private StrategyMarketChannelRepository strategyMarketChannelRepository;
    @Autowired
    private StrategyExecLogRepository strategyExecLogRepository;

    public void statStrategyGroupDataProcess() {
        //查出当天需要统计的策略
        List<Integer> statusList = StrategyStatusEnum.getReportCodes();
        List<StrategyDo> strategyDoList = strategyService.getAllStrategyList(null, statusList);
        strategyDoList = strategyDoList.stream().filter(item -> !item.getValidityEnd().isBefore(LocalDateTime.now())).collect(Collectors.toList());
        //引擎策略处理
        for (StrategyDo strategyDo : strategyDoList) {
            //T0-引擎版
            if (Objects.equals(StrategyRulerEnum.EVENT.getCode(), strategyDo.getSendRuler()) && Objects.equals(strategyDo.getType(), 1)) {
                try {
                    reportEngineEventGroupData(strategyDo);
                } catch (Exception e) {
                    log.error("策略分组数据失败,策略编号{}", strategyDo.getId(), e);
                }
            }
            //T0
            if (Objects.equals(StrategyRulerEnum.EVENT.getCode(), strategyDo.getSendRuler()) && Objects.equals(strategyDo.getType(), 0)) {
                try {
                    reportEventGroupData(strategyDo);
                } catch (Exception e) {
                    log.error("策略分组数据失败,策略编号{}", strategyDo.getId(), e);
                }
            }
            //离线-引擎版(单次,例行,周期)
            if ((Objects.equals(StrategyRulerEnum.ONCE.getCode(), strategyDo.getSendRuler())
                    || Objects.equals(StrategyRulerEnum.CYCLE.getCode(), strategyDo.getSendRuler())
                    || Objects.equals(StrategyRulerEnum.CYCLE_DAY.getCode(), strategyDo.getSendRuler()))
                    && Objects.equals(strategyDo.getType(), 1)) {
                try {
                    reportEngineGroupData(strategyDo);
                } catch (Exception e) {
                    log.error("策略分组数据失败,策略编号{}", strategyDo.getId(), e);
                }
            }
            //离线and画布中的离线(单次,例行,周期)
            if ((Objects.equals(StrategyRulerEnum.ONCE.getCode(), strategyDo.getSendRuler())
                    || Objects.equals(StrategyRulerEnum.CYCLE.getCode(), strategyDo.getSendRuler())
                    || Objects.equals(StrategyRulerEnum.CYCLE_DAY.getCode(), strategyDo.getSendRuler()))
                    && Objects.equals(strategyDo.getType(), 0)) {
                try {
                    reportGroupData(strategyDo);
                } catch (Exception e) {
                    log.error("策略分组数据失败,策略编号{}", strategyDo.getId(), e);
                }
            }
        }
    }

    /**
     * T0-引擎 分组数据
     *
     * @param strategyDo 策略do
     */
    private void reportEngineEventGroupData(StrategyDo strategyDo) {
        String tableNameNo = LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMM");
        Long strategyDoId = strategyDo.getId();
        //查询分组
        List<StrategyGroupDo> strategyGroupDos = strategyGroupRepository.selectListByStrategyId(strategyDo.getId());
        for (StrategyGroupDo strategyGroupDo : strategyGroupDos) {
            Long strategyGroupId = strategyGroupDo.getId();
            String strategyGroupName = strategyGroupDo.getName().replace("组", "");

            StrategyCreateReq.GroupConfig groupConfig = JSON.parseObject(strategyGroupDo.getGroupConfig(),
                    StrategyCreateReq.GroupConfig.class);

            //分组人数
//            Integer groupCount = decisionRecordRepository.countCurrentDecisionByGroupId(tableNameNo, strategyDoId, strategyGroupDo.getId(), LocalDateTimeUtil.beginOfDay(LocalDateTime.now()), LocalDateTimeUtil.endOfDay(LocalDateTime.now()));
            if (groupConfig == null || !Objects.equals(1, groupConfig.getSelected())) {
                //不进入引擎组
                StatStrategyGroupDataEntity strategyGroupData = new StatStrategyGroupDataEntity();
                strategyGroupData.setBizDate(LocalDateTime.now());
                strategyGroupData.setType(StrategyModelEnum.T0_ENGINE.getCode());
                strategyGroupData.setStrategyId(strategyDoId);
                strategyGroupData.setStrategyGroupId(strategyGroupId);
                strategyGroupData.setStrategyGroupName(strategyGroupName);
                strategyGroupData.setIfIntoEngine(0);
                strategyGroupData.setStatus(0);
//                strategyGroupData.setGroupCount(groupCount);

                if (statStrategyGroupDataRepository.exitsRecord(strategyGroupData)) {
                    statStrategyGroupDataRepository.updateByRecord(strategyGroupData);
                } else {
                    statStrategyGroupDataRepository.insert(strategyGroupData);
                }

            } else {
                //进入引擎组
                String curDate = LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMMdd");
                String redisKey = String.format(RedisKeyConstants.ENGINE_STRATEGY_GROUP_ID_DATA, curDate, strategyGroupId);

                Map<String, String> groupIdToInfoMap = redisUtils.hGetAll(redisKey);
                for (String groupIdInfo : groupIdToInfoMap.values()) {
                    if (StringUtils.isNotBlank(groupIdInfo)) {
                        EngineStrategyGroupIdInfoDto redisGroupIdInfo = JSON.parseObject(groupIdInfo, EngineStrategyGroupIdInfoDto.class);
                        String groupId = redisGroupIdInfo.getGroupId();
                        String groupSource = redisGroupIdInfo.getGroupSource();
                        List<EngineStrategyGroupIdInfoDto.ChannelInfo> channelInfoList = redisGroupIdInfo.getChannelInfoList();
                        for (EngineStrategyGroupIdInfoDto.ChannelInfo channelInfo : channelInfoList) {
                            Integer marketChannel = channelInfo.getMarketChannel();
                            String templateId = channelInfo.getTemplate();

                            StatStrategyGroupDataEntity strategyGroupData = new StatStrategyGroupDataEntity();
                            strategyGroupData.setBizDate(LocalDateTime.now());
                            strategyGroupData.setType(StrategyModelEnum.T0_ENGINE.getCode());
                            strategyGroupData.setStrategyId(strategyDoId);
                            strategyGroupData.setStrategyGroupId(strategyGroupId);
                            strategyGroupData.setStrategyGroupName(strategyGroupName);
                            strategyGroupData.setIfIntoEngine(1);
                            strategyGroupData.setEngineGroupId(groupId);
                            strategyGroupData.setGroupSource(groupSource);
                            strategyGroupData.setMarketChannel(marketChannel);
                            strategyGroupData.setTemplateId(templateId);
                            strategyGroupData.setStatus(0);

                            if (!StrategyMarketChannelEnum.NONE.equals(StrategyMarketChannelEnum.getInstance(marketChannel)) &&
                                    !StrategyMarketChannelEnum.APP_BANNER.equals(StrategyMarketChannelEnum.getInstance(marketChannel))) {
                                //应发用户数
//                                Integer execCount = eventPushBatchRepository.countByDetail(tableNameNo, strategyDoId, strategyGroupId, groupId, marketChannel, templateId, null, LocalDateTimeUtil.beginOfDay(LocalDateTime.now()), LocalDateTimeUtil.endOfDay(LocalDateTime.now()));
                                //发送用户数
//                                Integer sendCount = eventPushBatchRepository.countByDetail(tableNameNo, strategyDoId, strategyGroupId, groupId, marketChannel, templateId, Arrays.asList(1, 2), LocalDateTimeUtil.beginOfDay(LocalDateTime.now()), LocalDateTimeUtil.endOfDay(LocalDateTime.now()));
                                Integer execCount = userDispatchDetailRepository.countByDetail(tableNameNo, strategyDoId, strategyGroupId, groupId, marketChannel, templateId, null, LocalDateTimeUtil.beginOfDay(LocalDateTime.now()), LocalDateTime.now());
                                strategyGroupData.setExecCount(execCount);
                                strategyGroupData.setSendCount(execCount);

                                Integer receiveCount = userDispatchDetailRepository.countByDetail(tableNameNo, strategyDoId, strategyGroupId, groupId, marketChannel, templateId, Arrays.asList(-1,0, 1), LocalDateTimeUtil.beginOfDay(LocalDateTime.now()), LocalDateTime.now());
                                Integer succCount = userDispatchDetailRepository.countByDetail(tableNameNo, strategyDoId, strategyGroupId, groupId, marketChannel, templateId, Collections.singletonList(1), LocalDateTimeUtil.beginOfDay(LocalDateTime.now()), LocalDateTime.now());
                                //渠道接收数
                                strategyGroupData.setReceiveCount(receiveCount);
                                //回执成功数
                                strategyGroupData.setSuccCount(succCount);
                            }
//                            strategyGroupData.setGroupCount(groupCount);

                            if (statStrategyGroupDataRepository.exitsRecord(strategyGroupData)) {
                                statStrategyGroupDataRepository.updateByRecord(strategyGroupData);
                            } else {
                                statStrategyGroupDataRepository.insert(strategyGroupData);
                            }

                        }
                    }

                }

            }
        }

    }

    /**
     * 离线策略 分组数据
     */
    private void reportGroupData(StrategyDo strategyDo) {
        //离线策略执行过程中不做统计
        if (strategyDo.getStatus() != StrategyStatusEnum.SUCCESS.getCode() && strategyDo.getStatus() != StrategyStatusEnum.ENDED.getCode()) {
            return;
        }
        Long strategyDoId = strategyDo.getId();
        String tableNameNo = LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMM");
        //查询分组
        List<StrategyGroupDo> strategyGroupDos = strategyGroupRepository.selectListByStrategyId(strategyDo.getId());
        for (StrategyGroupDo strategyGroupDo : strategyGroupDos) {
            Long strategyGroupId = strategyGroupDo.getId();
            String strategyGroupName = strategyGroupDo.getName().replace("组", "");
            List<StrategyMarketChannelDo> marketChannelList = strategyMarketChannelRepository.selectByStrategyGroupId(strategyGroupDo.getId());
            if (CollectionUtils.isEmpty(marketChannelList)) {
                break;
            }
            boolean noMarketOrAppBanner = marketChannelList.stream()
                    .allMatch(item -> StrategyMarketChannelEnum.getInstance(item.getMarketChannel()) == StrategyMarketChannelEnum.NONE ||
                            StrategyMarketChannelEnum.getInstance(item.getMarketChannel()) == StrategyMarketChannelEnum.APP_BANNER);
            if (noMarketOrAppBanner) {
                //全部都是不营销或者app资源位
                StatStrategyGroupDataEntity strategyGroupData = new StatStrategyGroupDataEntity();
                strategyGroupData.setBizDate(LocalDateTime.now());
                strategyGroupData.setType(StrategyModelEnum.OFF.getCode());
                strategyGroupData.setStrategyId(strategyDoId);
                strategyGroupData.setStrategyGroupId(strategyGroupId);
                strategyGroupData.setStrategyGroupName(strategyGroupName);
                strategyGroupData.setIfIntoEngine(0);
                strategyGroupData.setStatus(0);

                StatOfflineStrategyGroupDataEntity lastByStrategyGroupIdAndBizDate = strategyGroupDataRepository.getLastByStrategyGroupIdAndBizDate(strategyGroupId, LocalDateTimeUtil.beginOfDay(LocalDateTime.now()));
                if (lastByStrategyGroupIdAndBizDate != null) {
                    strategyGroupData.setStatus(1);
                    strategyGroupData.setGroupCount(lastByStrategyGroupIdAndBizDate.getGroupUserNum());
                }

                if (statStrategyGroupDataRepository.exitsRecord(strategyGroupData)) {
                    statStrategyGroupDataRepository.updateByRecord(strategyGroupData);
                } else {
                    statStrategyGroupDataRepository.insert(strategyGroupData);
                }

            } else {
                List<StrategyExecLogDo> strategyExecLogDos = strategyExecLogRepository.selectByGroupIdAndExecTime(strategyGroupId, LocalDateTimeUtil.beginOfDay(LocalDateTime.now()), LocalDateTimeUtil.endOfDay(LocalDateTime.now()), StrategyExecStatusEnum.SUCCESS);
                Map<String, StrategyExecLogDo> templateToExecLog = strategyExecLogDos.stream().collect(Collectors.groupingBy(StrategyExecLogDo::getTemplateId, Collectors.collectingAndThen(
                        Collectors.maxBy(Comparator.comparingInt(item -> item.getId().intValue())),
                        Optional::get
                )));
                for (StrategyExecLogDo execLog : templateToExecLog.values()) {
                    StatStrategyGroupDataEntity strategyGroupData = new StatStrategyGroupDataEntity();
                    strategyGroupData.setBizDate(execLog.getExecTime());
                    strategyGroupData.setType(StrategyModelEnum.OFF.getCode());
                    strategyGroupData.setStrategyId(strategyDoId);
                    strategyGroupData.setStrategyGroupId(strategyGroupId);
                    strategyGroupData.setStrategyGroupName(strategyGroupName);
                    strategyGroupData.setIfIntoEngine(0);
                    strategyGroupData.setMarketChannel(execLog.getStrategyMarketChannel());
                    strategyGroupData.setTemplateId(execLog.getTemplateId());
                    strategyGroupData.setStatus(1);
                    strategyGroupData.setGroupCount(execLog.getGroupCount());
                    strategyGroupData.setExecCount(execLog.getExecCount());
                    strategyGroupData.setSendCount(execLog.getSendCount());

                    //渠道接收数
                    //回执成功数
                    Integer receiveCount = userDispatchDetailRepository.countByDetail(tableNameNo, strategyDoId, strategyGroupId, null, execLog.getStrategyMarketChannel(), execLog.getTemplateId(), Arrays.asList(-1,0, 1), LocalDateTimeUtil.beginOfDay(LocalDateTime.now()), LocalDateTime.now());
                    Integer succCount = userDispatchDetailRepository.countByDetail(tableNameNo, strategyDoId, strategyGroupId, null, execLog.getStrategyMarketChannel(), execLog.getTemplateId(), Collections.singletonList(1), LocalDateTimeUtil.beginOfDay(LocalDateTime.now()), LocalDateTime.now());
                    strategyGroupData.setReceiveCount(receiveCount == 0 ? execLog.getReceiveCount() : receiveCount);
                    strategyGroupData.setSuccCount(succCount == 0 ? execLog.getSuccCount() : succCount);

                    if (statStrategyGroupDataRepository.exitsRecord(strategyGroupData)) {
                        statStrategyGroupDataRepository.updateByRecord(strategyGroupData);
                    } else {
                        statStrategyGroupDataRepository.insert(strategyGroupData);
                    }
                }

            }
        }

    }

    /**
     * 离线-引擎策略 分组数据
     */
    private void reportEngineGroupData(StrategyDo strategyDo) {
        //离线策略执行过程中不做统计
        if (strategyDo.getStatus() != StrategyStatusEnum.SUCCESS.getCode()) {
            return;
        }
        String tableNameNo = LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMM");
        Long strategyId = strategyDo.getId();
        //查询分组
        List<StrategyGroupDo> strategyGroupDos = strategyGroupRepository.selectListByStrategyId(strategyDo.getId());
        for (StrategyGroupDo strategyGroupDo : strategyGroupDos) {
            Long strategyGroupId = strategyGroupDo.getId();
            String strategyGroupName = strategyGroupDo.getName().replace("组", "");

            StrategyCreateReq.GroupConfig groupConfig = JSON.parseObject(strategyGroupDo.getGroupConfig(),
                    StrategyCreateReq.GroupConfig.class);
            if (groupConfig == null || !Objects.equals(1, groupConfig.getSelected())) {
                //不进入引擎组
                StatStrategyGroupDataEntity strategyGroupData = new StatStrategyGroupDataEntity();
                strategyGroupData.setBizDate(LocalDateTime.now());
                strategyGroupData.setType(StrategyModelEnum.OFF_ENGINE.getCode());
                strategyGroupData.setStrategyId(strategyId);
                strategyGroupData.setStrategyGroupId(strategyGroupId);
                strategyGroupData.setStrategyGroupName(strategyGroupName);
                strategyGroupData.setIfIntoEngine(0);
                strategyGroupData.setStatus(1);
                String curDate = LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMMdd");
                Integer notIntoEngineNum = redisUtils.pfCount(RedisKeyUtils.genOffEngineNotIntoEngineNum(curDate, strategyDo.getId())).intValue();
                strategyGroupData.setGroupCount(notIntoEngineNum);

                if (statStrategyGroupDataRepository.exitsRecord(strategyGroupData)) {
                    statStrategyGroupDataRepository.updateByRecord(strategyGroupData);
                } else {
                    statStrategyGroupDataRepository.insert(strategyGroupData);
                }

            } else {
                //进入引擎
                String curDate = LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMMdd");
                String redisKey = String.format(RedisKeyConstants.ENGINE_STRATEGY_GROUP_ID_DATA, curDate, strategyGroupId);

                Map<String, String> groupIdToInfoMap = redisUtils.hGetAll(redisKey);
                for (String groupIdInfo : groupIdToInfoMap.values()) {
                    if (StringUtils.isNotBlank(groupIdInfo)) {
                        EngineStrategyGroupIdInfoDto redisGroupIdInfo = JSON.parseObject(groupIdInfo, EngineStrategyGroupIdInfoDto.class);
                        String groupId = redisGroupIdInfo.getGroupId();
                        String groupSource = redisGroupIdInfo.getGroupSource();
                        List<EngineStrategyGroupIdInfoDto.ChannelInfo> channelInfoList = redisGroupIdInfo.getChannelInfoList();
                        for (EngineStrategyGroupIdInfoDto.ChannelInfo channelInfo : channelInfoList) {
                            Integer marketChannel = channelInfo.getMarketChannel();
                            String templateId = channelInfo.getTemplate();

                            StatStrategyGroupDataEntity strategyGroupData = new StatStrategyGroupDataEntity();
                            strategyGroupData.setType(StrategyModelEnum.OFF_ENGINE.getCode());
                            strategyGroupData.setStrategyId(strategyId);
                            strategyGroupData.setStrategyGroupId(strategyGroupId);
                            strategyGroupData.setStrategyGroupName(strategyGroupName);
                            strategyGroupData.setIfIntoEngine(1);
                            strategyGroupData.setEngineGroupId(groupId);
                            strategyGroupData.setGroupSource(groupSource);
                            strategyGroupData.setMarketChannel(marketChannel);
                            strategyGroupData.setTemplateId(templateId);
                            strategyGroupData.setStatus(0);

                            List<StrategyExecLogDo> strategyExecLogDos = strategyExecLogRepository.selectByStrategyIdAndExecTimeUngrouped(strategyId, LocalDateTimeUtil.beginOfDay(LocalDateTime.now()), LocalDateTimeUtil.endOfDay(LocalDateTime.now()), StrategyExecStatusEnum.SUCCESS);
                            if (CollectionUtils.isEmpty(strategyExecLogDos)) {
                                return;
                            }
                            StrategyExecLogDo execLogDo = strategyExecLogDos.stream().sorted(Comparator.comparing(StrategyExecLogDo::getId).reversed()).collect(Collectors.toList()).get(0);
                            strategyGroupData.setGroupCount(execLogDo.getGroupCount());
                            strategyGroupData.setBizDate(execLogDo.getExecTime());

                            if (!StrategyMarketChannelEnum.NONE.equals(StrategyMarketChannelEnum.getInstance(marketChannel)) &&
                                    !StrategyMarketChannelEnum.APP_BANNER.equals(StrategyMarketChannelEnum.getInstance(marketChannel))) {
                                //应发用户数
                                //发送用户数
                                Integer execCount = userDispatchDetailRepository.countByDetail(tableNameNo, strategyId, strategyGroupId, groupId, marketChannel, templateId, null, LocalDateTimeUtil.beginOfDay(LocalDateTime.now()), LocalDateTime.now());
                                strategyGroupData.setExecCount(execCount);
                                strategyGroupData.setSendCount(execCount);

                                //渠道接收数
                                //回执成功数
                                Integer receiveCount = userDispatchDetailRepository.countByDetail(tableNameNo, strategyId, strategyGroupId, groupId, marketChannel, templateId, Arrays.asList(-1,0, 1), LocalDateTimeUtil.beginOfDay(LocalDateTime.now()), LocalDateTime.now());
                                Integer succCount = userDispatchDetailRepository.countByDetail(tableNameNo, strategyId, strategyGroupId, groupId, marketChannel, templateId, Collections.singletonList(1), LocalDateTimeUtil.beginOfDay(LocalDateTime.now()), LocalDateTime.now());
                                strategyGroupData.setReceiveCount(receiveCount);
                                strategyGroupData.setSuccCount(succCount);
                            }

                            if (statStrategyGroupDataRepository.exitsRecord(strategyGroupData)) {
                                statStrategyGroupDataRepository.updateByRecord(strategyGroupData);
                            } else {
                                statStrategyGroupDataRepository.insert(strategyGroupData);
                            }

                        }
                    }
                }

            }
        }

    }

    /**
     * T0策略 分组数据
     */
    private void reportEventGroupData(StrategyDo strategyDo) {
        String tableNameNo = LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMM");
        Long strategyId = strategyDo.getId();
        //查询分组
        List<StrategyGroupDo> strategyGroupDos = strategyGroupRepository.selectListByStrategyId(strategyId);
        for (StrategyGroupDo strategyGroupDo : strategyGroupDos) {
            Long strategyGroupId = strategyGroupDo.getId();
            String strategyGroupName = strategyGroupDo.getName().replace("组", "");
            List<StrategyMarketChannelDo> marketChannelList = strategyMarketChannelRepository.selectByStrategyGroupId(strategyGroupDo.getId());
            if (CollectionUtils.isEmpty(marketChannelList)) {
                break;
            }
            boolean noMarketOrAppBanner = marketChannelList.stream()
                    .allMatch(item -> {
                        return StrategyMarketChannelEnum.getInstance(item.getMarketChannel()) == StrategyMarketChannelEnum.NONE ||
                                StrategyMarketChannelEnum.getInstance(item.getMarketChannel()) == StrategyMarketChannelEnum.APP_BANNER;
                    });
            //分组人数
//            Integer groupCount = decisionRecordRepository.countCurrentDecisionByGroupId(tableNameNo, strategyId, strategyGroupDo.getId(), LocalDateTimeUtil.beginOfDay(LocalDateTime.now()), LocalDateTimeUtil.endOfDay(LocalDateTime.now()));
            if (noMarketOrAppBanner) {
                StatStrategyGroupDataEntity strategyGroupData = new StatStrategyGroupDataEntity();
                strategyGroupData.setBizDate(LocalDateTime.now());
                strategyGroupData.setType(StrategyModelEnum.T0.getCode());
                strategyGroupData.setStrategyId(strategyId);
                strategyGroupData.setStrategyGroupId(strategyGroupId);
                strategyGroupData.setStrategyGroupName(strategyGroupName);
                strategyGroupData.setIfIntoEngine(0);
                strategyGroupData.setStatus(0);
//                strategyGroupData.setGroupCount(groupCount);

                if (statStrategyGroupDataRepository.exitsRecord(strategyGroupData)) {
                    statStrategyGroupDataRepository.updateByRecord(strategyGroupData);
                } else {
                    statStrategyGroupDataRepository.insert(strategyGroupData);
                }
            } else {
                for (StrategyMarketChannelDo strategyMarketChannelDo : marketChannelList) {
                    Integer marketChannel = strategyMarketChannelDo.getMarketChannel();
                    if (!Objects.equals(marketChannel, StrategyMarketChannelEnum.NONE.getCode()) &&
                            !Objects.equals(marketChannel, StrategyMarketChannelEnum.APP_BANNER.getCode())) {
                        String templateId = strategyMarketChannelDo.getTemplateId();

                        StatStrategyGroupDataEntity strategyGroupData = new StatStrategyGroupDataEntity();
                        strategyGroupData.setBizDate(LocalDateTime.now());
                        strategyGroupData.setType(StrategyModelEnum.T0.getCode());
                        strategyGroupData.setStrategyId(strategyId);
                        strategyGroupData.setStrategyGroupId(strategyGroupId);
                        strategyGroupData.setStrategyGroupName(strategyGroupName);
                        strategyGroupData.setIfIntoEngine(0);
                        strategyGroupData.setMarketChannel(marketChannel);
                        strategyGroupData.setTemplateId(templateId);
                        strategyGroupData.setStatus(0);
                        Integer execCount = userDispatchDetailRepository.countByDetail(tableNameNo, strategyId, strategyGroupId, null, marketChannel, templateId, null, LocalDateTimeUtil.beginOfDay(LocalDateTime.now()), LocalDateTime.now());
                        Integer receiveCount = userDispatchDetailRepository.countByDetail(tableNameNo, strategyId, strategyGroupId, null, marketChannel, templateId, Arrays.asList(-1,0, 1), LocalDateTimeUtil.beginOfDay(LocalDateTime.now()), LocalDateTime.now());
                        Integer succCount = userDispatchDetailRepository.countByDetail(tableNameNo, strategyId, strategyGroupId, null, marketChannel, templateId, Collections.singletonList(1), LocalDateTimeUtil.beginOfDay(LocalDateTime.now()), LocalDateTime.now());
//                        strategyGroupData.setGroupCount(groupCount);
                        strategyGroupData.setExecCount(execCount);
                        strategyGroupData.setSendCount(execCount);
                        strategyGroupData.setReceiveCount(receiveCount);
                        strategyGroupData.setSuccCount(succCount);

                        if (statStrategyGroupDataRepository.exitsRecord(strategyGroupData)) {
                            statStrategyGroupDataRepository.updateByRecord(strategyGroupData);
                        } else {
                            statStrategyGroupDataRepository.insert(strategyGroupData);
                        }
                    }
                }

            }
        }

    }

    public void reportYesterdayData() {
        String tableNameNo = LocalDateTimeUtil.format(LocalDateTime.now().minusDays(1), "yyyyMM");
        List<StatStrategyGroupDataEntity> strategyGroupDataEntityList = statStrategyGroupDataRepository.selectByDateAndType(LocalDate.now().plusDays(-1), null);
        if (CollectionUtils.isEmpty(strategyGroupDataEntityList)) {
            return;
        }
        for (StatStrategyGroupDataEntity statStrategyGroupData : strategyGroupDataEntityList) {
            if (statStrategyGroupData.getIfIntoEngine() == 0 ||
                    StrategyMarketChannelEnum.NONE.equals(StrategyMarketChannelEnum.getInstance(statStrategyGroupData.getMarketChannel())) ||
                    StrategyMarketChannelEnum.APP_BANNER.equals(StrategyMarketChannelEnum.getInstance(statStrategyGroupData.getMarketChannel()))) {
                continue;
            }
            log.info("statStrategyGroupDateYesterdayJob execute strategyId:{}", statStrategyGroupData.getStrategyId());
            Long strategyId = statStrategyGroupData.getStrategyId();
            Long strategyGroupId = statStrategyGroupData.getStrategyGroupId();
            String groupId = statStrategyGroupData.getEngineGroupId();
            Integer marketChannel = statStrategyGroupData.getMarketChannel();
            String templateId = statStrategyGroupData.getTemplateId();
            if (Objects.equals(statStrategyGroupData.getType(), StrategyModelEnum.T0_ENGINE.getCode())) {
                //分组人数
//                Integer groupCount = decisionRecordRepository.countCurrentDecisionByGroupId(tableNameNo, strategyId, strategyGroupId,
//                        LocalDateTimeUtil.beginOfDay(LocalDateTime.now().minusDays(1)), LocalDateTimeUtil.endOfDay(LocalDateTime.now().minusDays(1)));
                //应发用户数
//                Integer execCount = eventPushBatchRepository.countByDetail(tableNameNo, strategyId, strategyGroupId, groupId, marketChannel, templateId, null,
//                        LocalDateTimeUtil.beginOfDay(LocalDateTime.now().minusDays(1)), LocalDateTimeUtil.endOfDay(LocalDateTime.now().minusDays(1)));
//                //发送用户数
//                Integer sendCount = eventPushBatchRepository.countByDetail(tableNameNo, strategyId, strategyGroupId, groupId, marketChannel, templateId, Arrays.asList(1, 2),
//                        LocalDateTimeUtil.beginOfDay(LocalDateTime.now().minusDays(1)), LocalDateTimeUtil.endOfDay(LocalDateTime.now().minusDays(1)));
                Integer execCount = userDispatchDetailRepository.countByDetail(tableNameNo, strategyId, strategyGroupId, groupId, marketChannel, templateId, null,
                        LocalDateTimeUtil.beginOfDay(LocalDateTime.now().minusDays(1)), LocalDateTimeUtil.endOfDay(LocalDateTime.now().minusDays(1)));
                //渠道接收数
                Integer receiveCount = userDispatchDetailRepository.countByDetail(tableNameNo, strategyId, strategyGroupId, groupId, marketChannel, templateId, Arrays.asList(1, 0),
                        LocalDateTimeUtil.beginOfDay(LocalDateTime.now().minusDays(1)), LocalDateTimeUtil.endOfDay(LocalDateTime.now().minusDays(1)));
                //回执成功数
                Integer succCount = userDispatchDetailRepository.countByDetail(tableNameNo, strategyId, strategyGroupId, groupId, marketChannel, templateId, Collections.singletonList(1),
                        LocalDateTimeUtil.beginOfDay(LocalDateTime.now().minusDays(1)), LocalDateTimeUtil.endOfDay(LocalDateTime.now().minusDays(1)));
                if (!Objects.equals(statStrategyGroupData.getReceiveCount(), receiveCount) || !Objects.equals(statStrategyGroupData.getSuccCount(), succCount)
                        || !Objects.equals(statStrategyGroupData.getExecCount(), execCount)) {
//                    statStrategyGroupData.setGroupCount(groupCount);
                    statStrategyGroupData.setReceiveCount(receiveCount);
                    statStrategyGroupData.setSuccCount(succCount);
                    statStrategyGroupData.setExecCount(execCount);
                    statStrategyGroupData.setSendCount(execCount);
                    statStrategyGroupData.setUpdatedTime(null);
                    statStrategyGroupDataRepository.updateById(statStrategyGroupData);
                }
            }
            if (Objects.equals(statStrategyGroupData.getType(), StrategyModelEnum.T0.getCode())) {
                //分组人数
//                Integer groupCount = decisionRecordRepository.countCurrentDecisionByGroupId(tableNameNo, strategyId, strategyGroupId,
//                        LocalDateTimeUtil.beginOfDay(LocalDateTime.now().minusDays(1)), LocalDateTimeUtil.endOfDay(LocalDateTime.now().minusDays(1)));

                Integer execCount = userDispatchDetailRepository.countByDetail(tableNameNo, strategyId, strategyGroupId, null, marketChannel, templateId, null, LocalDateTimeUtil.beginOfDay(LocalDateTime.now()), LocalDateTime.now());
                //渠道接收数
                Integer receiveCount = userDispatchDetailRepository.countByDetail(tableNameNo, strategyId, strategyGroupId, null, marketChannel, templateId, Arrays.asList(1, 0),
                        LocalDateTimeUtil.beginOfDay(LocalDateTime.now().minusDays(1)), LocalDateTimeUtil.endOfDay(LocalDateTime.now().minusDays(1)));
                //回执成功数
                Integer succCount = userDispatchDetailRepository.countByDetail(tableNameNo, strategyId, strategyGroupId, null, marketChannel, templateId, Collections.singletonList(1),
                        LocalDateTimeUtil.beginOfDay(LocalDateTime.now().minusDays(1)), LocalDateTimeUtil.endOfDay(LocalDateTime.now().minusDays(1)));
                if (!Objects.equals(statStrategyGroupData.getReceiveCount(), receiveCount) || !Objects.equals(statStrategyGroupData.getSuccCount(), succCount)
                        || !Objects.equals(statStrategyGroupData.getExecCount(), execCount)) {
//                    statStrategyGroupData.setGroupCount(groupCount);
                    statStrategyGroupData.setExecCount(execCount);
                    statStrategyGroupData.setSendCount(execCount);
                    statStrategyGroupData.setReceiveCount(receiveCount);
                    statStrategyGroupData.setSuccCount(succCount);
                    statStrategyGroupData.setUpdatedTime(null);
                    statStrategyGroupDataRepository.updateById(statStrategyGroupData);
                }
            }

            if (Objects.equals(statStrategyGroupData.getType(), StrategyModelEnum.OFF_ENGINE.getCode())) {
                //应发用户数
                //发送用户数
                Integer execCount = userDispatchDetailRepository.countByDetail(tableNameNo, strategyId, strategyGroupId, groupId, marketChannel, templateId, null, LocalDateTimeUtil.beginOfDay(LocalDateTime.now()), LocalDateTime.now());
                //渠道接收数
                Integer receiveCount = userDispatchDetailRepository.countByDetail(tableNameNo, strategyId, strategyGroupId, groupId, marketChannel, templateId, Arrays.asList(-1,0, 1), LocalDateTimeUtil.beginOfDay(LocalDateTime.now()), LocalDateTime.now());
                //回执成功数
                Integer succCount = userDispatchDetailRepository.countByDetail(tableNameNo, strategyId, strategyGroupId, groupId, marketChannel, templateId, Collections.singletonList(1), LocalDateTimeUtil.beginOfDay(LocalDateTime.now()), LocalDateTime.now());

                if (!Objects.equals(statStrategyGroupData.getReceiveCount(), receiveCount) || !Objects.equals(statStrategyGroupData.getSuccCount(), succCount)
                        || !Objects.equals(statStrategyGroupData.getExecCount(), execCount)) {
                    statStrategyGroupData.setExecCount(execCount);
                    statStrategyGroupData.setSendCount(execCount);
                    statStrategyGroupData.setReceiveCount(receiveCount);
                    statStrategyGroupData.setSuccCount(succCount);
                    statStrategyGroupData.setUpdatedTime(null);
                    statStrategyGroupDataRepository.updateById(statStrategyGroupData);
                }
            }

            if (Objects.equals(statStrategyGroupData.getType(), StrategyModelEnum.OFF.getCode())) {
                //渠道接收数
                Integer receiveCount = userDispatchDetailRepository.countByDetail(tableNameNo, strategyId, strategyGroupId, null, marketChannel, templateId, Arrays.asList(-1,0, 1), LocalDateTimeUtil.beginOfDay(LocalDateTime.now()), LocalDateTime.now());
                //回执成功数
                Integer succCount = userDispatchDetailRepository.countByDetail(tableNameNo, strategyId, strategyGroupId, null, marketChannel, templateId, Collections.singletonList(1), LocalDateTimeUtil.beginOfDay(LocalDateTime.now()), LocalDateTime.now());
                statStrategyGroupData.setReceiveCount(receiveCount);
                statStrategyGroupData.setSuccCount(succCount);
                if (!Objects.equals(statStrategyGroupData.getReceiveCount(), receiveCount) || !Objects.equals(statStrategyGroupData.getSuccCount(), succCount)) {
                    statStrategyGroupData.setReceiveCount(receiveCount);
                    statStrategyGroupData.setSuccCount(succCount);
                    statStrategyGroupData.setUpdatedTime(null);
                    statStrategyGroupDataRepository.updateById(statStrategyGroupData);
                }
            }

        }
    }

    public void statStrategyGroupDateEndCurrentStatus() {
        List<StatStrategyGroupDataEntity> strategyGroupDataEntityList = statStrategyGroupDataRepository.selectByDateAndType(LocalDate.now().plusDays(-1), Arrays.asList(2, 4));
        if (CollectionUtils.isEmpty(strategyGroupDataEntityList)) {
            return;
        }
        for (StatStrategyGroupDataEntity statStrategyGroupDataEntity : strategyGroupDataEntityList) {
            statStrategyGroupDataEntity.setStatus(1);
            statStrategyGroupDataEntity.setUpdatedTime(null);
        }
        statStrategyGroupDataRepository.batchUpdate(strategyGroupDataEntityList);
    }
}