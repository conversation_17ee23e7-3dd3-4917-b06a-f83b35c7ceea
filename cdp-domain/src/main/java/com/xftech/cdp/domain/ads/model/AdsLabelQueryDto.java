package com.xftech.cdp.domain.ads.model;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @<NAME_EMAIL>
 */
@Data
public class AdsLabelQueryDto {

    /**
     * 用户ID
     */
    private Long appUserId;

    /**
     * 标签集合
     */
    private List<Label> labelList;

    public AdsLabelQueryDto() {
    }

    public AdsLabelQueryDto(Long appUserId, List<Label> labelList) {
        this.appUserId = appUserId;
        this.labelList = labelList;
    }

    @Data
    public static class Label {
        /**
         * 标签值
         */
        private String labelKey;

        /**
         * 标签数据类型
         */
        private String labelValueType;

        /**
         * 是否需要自定义加工  0 不需要 1需要
         */
        private Integer customProcess;

        /**
         * 标签请求参数
         */
        private Map<String, Object> request;
    }
}
