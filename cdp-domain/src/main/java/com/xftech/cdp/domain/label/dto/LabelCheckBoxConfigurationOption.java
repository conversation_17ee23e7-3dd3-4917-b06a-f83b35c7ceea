/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.label.dto;

import com.sun.org.apache.xpath.internal.operations.Bool;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ LabelCheckBoxConfigurationOption, v 0.1 2024/6/19 19:09 lingang.han Exp $
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LabelCheckBoxConfigurationOption implements Serializable {
    private static final long serialVersionUID = 837870148889528268L;

    private List<String> items;

    private Boolean checkbox = false;

    public LabelCheckBoxConfigurationOption(List<String> items) {
        this.items = items;
    }
}