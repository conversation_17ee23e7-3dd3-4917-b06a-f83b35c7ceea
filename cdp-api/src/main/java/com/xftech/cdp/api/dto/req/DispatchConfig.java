/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.req;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xftech.cdp.api.dto.base.TimeFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 *
 * <AUTHOR>
 * @version $ DispatchConfig, v 0.1 2023/12/19 13:34 yye.xu Exp $
 */

@Data
public class DispatchConfig {

    // 普通策略- T0策略营销， 时间控制
    private DispatchTimeConfig dispatchTimeConfig;

    // 流程画布 策略配置营销对象和营销时间
    private FlowDispatchConfig flowDispatchConfig;

    @JsonIgnore
    @JSONField(serialize = false)
    public boolean isInTime(LocalDateTime local) {
        if (getDispatchTimeConfig() == null) {
            return true;
        }
        return dispatchTimeConfig.isIn(local);
    }

    @Data
    public static class DispatchTimeConfig {
        @JsonFormat(pattern = TimeFormat.DATE_TIME)
        private LocalDateTime begin;
        @JsonFormat(pattern = TimeFormat.DATE_TIME)
        private LocalDateTime end;

        @JsonIgnore
        @JSONField(serialize = false)
        public boolean isIn(LocalDateTime local) {
            return (!local.isBefore(begin)) && (!local.isAfter(end));
        }
    }

    @Data
    public static class FlowDispatchConfig {
        // 人群类型，FOLLOW_PRE-上游策略下发用户
        private String crowdType;
        private Delay delay;

        @Data
        public static class Delay {
            // 时间单位 0-秒 1-分钟 2-小时 3-天
            private Integer timeUnit;
            private Integer timeValue;
        }
    }
}