package com.xftech.cdp.domain.strategy.factory;

import com.alibaba.excel.annotation.ExcelProperty;
import com.xftech.cdp.domain.strategy.model.enums.StrategyExecStatusEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyMarketChannelEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyRulerEnum;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyExecLogDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/2/23
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MonitorListVoiceResp extends AbsMonitorListRespFinal {

    @ApiModelProperty(value = "节点编号")
    @ExcelProperty(index = 3, value = "节点编号")
    protected String templateId;


    @ApiModelProperty(value = "应发电销人数")
    @ExcelProperty(index = 5, value = "应发电销人数")
    protected String triggerCount;

    @ApiModelProperty(value = "麻雀推送进度")
    @ExcelProperty(index = 6, value = "麻雀推送进度")
    protected String selfProcess;

    @ApiModelProperty(value = "电销接收进度")
    @ExcelProperty(index = 7, value = "电销接收进度")
    protected String voiceProcess;

    @ApiModelProperty(value = "入库人数")
    @ExcelProperty(index = 8, value = "入库人数")
    protected String voiceCount;

    @ApiModelProperty(value = "入库率")
    @ExcelProperty(index = 9, value = "入库率")
    protected String voiceAcceptRate;

    @ApiModelProperty(value = "营销触达成功率")
    @ExcelProperty(index = 10, value = "营销触达成功率")
    protected String reachSuccRate;

    @Override
    public AbsMonitorListRespFinal convertRes(StrategyDo strategyDo, StrategyExecLogDo item) {
        boolean executingFlag = strategyDo.getSendRuler() != StrategyRulerEnum.EVENT.getCode() && StrategyExecStatusEnum.EXECUTING.getCode() == item.getExecStatus();
        boolean sendCountZero = item.getSendCount() == 0;
        boolean receiveCountZero = item.getReceiveCount() == 0;
        MonitorListVoiceResp monitorListVoiceResp = new MonitorListVoiceResp();
        StrategyExecStatusEnum strategyExecStatusEnum = StrategyExecStatusEnum.getInstance(item.getExecStatus());
        monitorListVoiceResp.setDateTime(item.getExecTime());
        monitorListVoiceResp.setGroupName(item.getStrategyGroupName());
        monitorListVoiceResp.setMarketChannel(StrategyMarketChannelEnum.VOICE.getDescription());
        monitorListVoiceResp.setExecStatus(Objects.nonNull(strategyExecStatusEnum) ? strategyExecStatusEnum.getDescription() : "");
        monitorListVoiceResp.setTemplateId(item.getTemplateId());
        monitorListVoiceResp.setTriggerCount(String.valueOf(item.getExecCount()));
        monitorListVoiceResp.setSelfProcess(item.getExecCount() + "/" + item.getSendCount());
        monitorListVoiceResp.setVoiceProcess(executingFlag || sendCountZero ? "-" : item.getSendCount() + "/" + item.getReceiveCount());
        monitorListVoiceResp.setVoiceCount(executingFlag || sendCountZero || receiveCountZero ? "-" : String.valueOf(item.getSuccCount()));
        monitorListVoiceResp.setVoiceAcceptRate(executingFlag || sendCountZero || receiveCountZero ? "-" : BigDecimal.valueOf(((float) item.getSuccCount() / item.getReceiveCount()) * 100).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
        monitorListVoiceResp.setReachSuccRate(sendCountZero || receiveCountZero ? "-" : BigDecimal.valueOf(((float) item.getSuccCount() / item.getExecCount()) * 100).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");

        return monitorListVoiceResp;
    }
}
