package com.xftech.cdp.api.dto.base;

import lombok.Data;

import java.util.List;

/**
 * <p>
 * 分页返回结果
 * <p/>
 *
 * <AUTHOR>
 * @since 2021/10/9 20:23
 */
@Data
public class PageResultResponse<T> {

    protected List<T> records;
    protected long current;
    protected long size;
    private long pages;
    private long total;

    public PageResultResponse() {
    }

    public PageResultResponse(List<T> records, long current, long size, long total) {
        this.records = records;
        this.current = current;
        this.size = size;
        this.total = total;
        if (total > 0 && size > 0) {
            this.pages = total % size == 0 ? total / size : total / size + 1;
        }
    }

}
