/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version $ IncreaseTypeEnum, v 0.1 2024/5/16 16:34 lingang.han Exp $
 */

@Getter
@AllArgsConstructor
public enum IncreaseTypeEnum {

    TEMP_CREDIT("TEMP_CREDIT", "increase_tmp_amount_marketing"),
    PERSONAL_MARKETING_RELOAN_TEMP("PERSONAL_MARKETING_RELOAN_TEMP", "personal_marketing_reloan_temp"),
    ;


    private final String code;
    private final String desc;


    public static IncreaseTypeEnum getEnum(String code) {
        for (IncreaseTypeEnum increaseTypeEnum : IncreaseTypeEnum.values()) {
            if (Objects.equals(increaseTypeEnum.getCode(), code)) {
                return increaseTypeEnum;
            }
        }
        return null;
    }
}