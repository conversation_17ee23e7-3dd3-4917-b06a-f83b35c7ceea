package com.xftech.cdp.infra.redis.sequence.impl.segment;

import com.xftech.cdp.infra.redis.sequence.SequenceService;
import com.xftech.cdp.infra.redis.sequence.config.SequenceConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service("redisSequenceImpl")
public class SegmentRedisSequenceServiceImpl implements SequenceService {


    private final static Logger logger = LoggerFactory.getLogger(SegmentRedisSequenceServiceImpl.class);
    /**
     * 步长1千
     */
    private final static Long customStep = 1000L;

    private static final String CUSTOM_KEY = "SEQUENCE:";


    private final Map<String, Sequence> sequenceMap = new ConcurrentHashMap<>();

    @Resource
    private SequenceConfig sequenceConfig;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public Long getSequenceID(String type) {
        if (sequenceMap.get(CUSTOM_KEY + type) == null) {
            synchronized (this) {
                if (sequenceMap.get(CUSTOM_KEY + type) == null) {
                    logger.info("type:{} init====================", type);
                    Sequence sequence = new Sequence(CUSTOM_KEY + type);
                    sequenceMap.put(CUSTOM_KEY + type, sequence);
                }
            }
        }
        Sequence sequence = sequenceMap.get(CUSTOM_KEY + type);
        return sequence.incrementAndGet();
    }


    class Sequence {

        private final String key;

        private Long maxValue;

        private Long currentValue;

        public Sequence(String key) {
            this.key = key;
            long nextMax = redisTemplate.opsForValue().increment(key, sequenceConfig.getSegmentMaxValue());
            currentValue = nextMax - customStep + 1;
            maxValue = nextMax;
        }

        public Long getMaxValue() {
            return maxValue;
        }

        public void setMaxValue(Long maxValue) {
            this.maxValue = maxValue;
        }

        public Long getCurrentValue() {
            return currentValue;
        }

        public void setCurrentValue(Long currentValue) {
            this.currentValue = currentValue;
        }

        public synchronized Long incrementAndGet() {
            if (currentValue >= maxValue) {
                long nextMax = redisTemplate.opsForValue().increment(key, customStep);
                currentValue = nextMax - customStep + 1;
                maxValue = nextMax;
            }
            return ++currentValue;
        }

    }


}
