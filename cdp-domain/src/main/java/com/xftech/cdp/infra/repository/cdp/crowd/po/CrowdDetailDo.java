package com.xftech.cdp.infra.repository.cdp.crowd.po;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xftech.cdp.domain.strategy.model.enums.MarketChannelTypeEnum;
import com.xftech.cdp.infra.repository.Do;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * 人群用户明细
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CrowdDetailDo extends Do {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * crowd_pack.id
     */
    private Long crowdId;

    /**
     * credit_user.id
     */
    private Long userId;

    /**
     * APP
     */
    private String app;

    /**
     * inner-app
     */
    private String innerApp;


    private String mobile;

    private String deviceId;

    /**
     * crowd_exec_log.id
     */
    private Long crowdExecLogId;


    private String abNum;

    private Integer appUserIdLast2;

    /**
     * 注册时间
     */
    private LocalDateTime registerTime;


    private String createdOp;

    private String updatedOp;

    private String tableName;

    // 画布, 扩展参数使用;
    private long preStrategyId;
    private String flowBatchNo;

    private long nextStrategyId;
    private String flowNo;
    private long strategyId;

    private String ossRunVersion;
    // 统一ID，离线策略通过crowdDetail传递
    private String unionId;

    @JsonIgnore
    @JSONField(serialize = false)
    public Map getExtDetailData(Integer marketChannel) {
        Map<String, Object> objectMap = null;
        if (StringUtils.isNotEmpty(flowNo)) {
            objectMap = new HashMap<>();
            Map<String, Object> data = new HashMap<>();
            data.put("flowNo", flowNo);
            data.put("preStrategyId", preStrategyId);
            data.put("flowBatchNo", flowBatchNo);
            data.put("nextStrategyId", nextStrategyId);
            objectMap.put("flow", data);
        }

        if (MarketChannelTypeEnum.LIFE_RIGHTS.getCode().equals(marketChannel)
                || MarketChannelTypeEnum.X_DAY_INTEREST_FREE.getCode().equals(marketChannel)) {
            if (Objects.isNull(objectMap)) {
                objectMap = new HashMap<>();
            }
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("app", app);
            userInfo.put("innerApp", innerApp);
            objectMap.put("userInfo", userInfo);
        }

        return objectMap;
    }

    @JsonIgnore
    @JSONField(serialize = false)
    public Map getUserDetailData() {
        Map<String, Object> objectMap = new HashMap<>();
        objectMap.put("mobile", mobile);
        objectMap.put("app", app);
        objectMap.put("innerApp", innerApp);
        objectMap.put("crowdId", crowdId);
        return objectMap;
    }

    @JsonIgnore
    @JSONField(serialize = false)
    public Map getUserInfoData() {
        Map<String, Object> objectMap = new HashMap<>();
        objectMap.put("app", app);
        objectMap.put("innerApp", innerApp);
        return objectMap;
    }
}
