package com.xftech.cdp.infra.repository.cdp.strategy.po;

import java.io.Serializable;
import java.util.Date;

public class StrategyExecCycleCounterDo implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_exec_cycle_counter.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_exec_cycle_counter.strategy_id
     *
     * @mbg.generated
     */
    private Long strategyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_exec_cycle_counter.sum_val
     *
     * @mbg.generated
     */
    private Integer sumVal;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_exec_cycle_counter.date_value
     *
     * @mbg.generated
     */
    private Integer dateValue;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_exec_cycle_counter.d_flag
     *
     * @mbg.generated
     */
    private Byte dFlag;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_exec_cycle_counter.created_time
     *
     * @mbg.generated
     */
    private Date createdTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_exec_cycle_counter.updated_time
     *
     * @mbg.generated
     */
    private Date updatedTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table strategy_exec_cycle_counter
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_exec_cycle_counter.id
     *
     * @return the value of strategy_exec_cycle_counter.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_exec_cycle_counter.id
     *
     * @param id the value for strategy_exec_cycle_counter.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_exec_cycle_counter.strategy_id
     *
     * @return the value of strategy_exec_cycle_counter.strategy_id
     *
     * @mbg.generated
     */
    public Long getStrategyId() {
        return strategyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_exec_cycle_counter.strategy_id
     *
     * @param strategyId the value for strategy_exec_cycle_counter.strategy_id
     *
     * @mbg.generated
     */
    public void setStrategyId(Long strategyId) {
        this.strategyId = strategyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_exec_cycle_counter.sum_val
     *
     * @return the value of strategy_exec_cycle_counter.sum_val
     *
     * @mbg.generated
     */
    public Integer getSumVal() {
        return sumVal;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_exec_cycle_counter.sum_val
     *
     * @param sumVal the value for strategy_exec_cycle_counter.sum_val
     *
     * @mbg.generated
     */
    public void setSumVal(Integer sumVal) {
        this.sumVal = sumVal;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_exec_cycle_counter.date_value
     *
     * @return the value of strategy_exec_cycle_counter.date_value
     *
     * @mbg.generated
     */
    public Integer getDateValue() {
        return dateValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_exec_cycle_counter.date_value
     *
     * @param dateValue the value for strategy_exec_cycle_counter.date_value
     *
     * @mbg.generated
     */
    public void setDateValue(Integer dateValue) {
        this.dateValue = dateValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_exec_cycle_counter.d_flag
     *
     * @return the value of strategy_exec_cycle_counter.d_flag
     *
     * @mbg.generated
     */
    public Byte getdFlag() {
        return dFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_exec_cycle_counter.d_flag
     *
     * @param dFlag the value for strategy_exec_cycle_counter.d_flag
     *
     * @mbg.generated
     */
    public void setdFlag(Byte dFlag) {
        this.dFlag = dFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_exec_cycle_counter.created_time
     *
     * @return the value of strategy_exec_cycle_counter.created_time
     *
     * @mbg.generated
     */
    public Date getCreatedTime() {
        return createdTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_exec_cycle_counter.created_time
     *
     * @param createdTime the value for strategy_exec_cycle_counter.created_time
     *
     * @mbg.generated
     */
    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_exec_cycle_counter.updated_time
     *
     * @return the value of strategy_exec_cycle_counter.updated_time
     *
     * @mbg.generated
     */
    public Date getUpdatedTime() {
        return updatedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_exec_cycle_counter.updated_time
     *
     * @param updatedTime the value for strategy_exec_cycle_counter.updated_time
     *
     * @mbg.generated
     */
    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table strategy_exec_cycle_counter
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", strategyId=").append(strategyId);
        sb.append(", sumVal=").append(sumVal);
        sb.append(", dateValue=").append(dateValue);
        sb.append(", dFlag=").append(dFlag);
        sb.append(", createdTime=").append(createdTime);
        sb.append(", updatedTime=").append(updatedTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}