package com.xftech.cdp.domain.strategy.service;

import com.xftech.cdp.domain.stat.entity.OfflineDecisionRecordEntity;
import com.xftech.cdp.infra.rabbitmq.vo.HitResult;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketChannelDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.UserDispatchDetailDo;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
public interface AppBannerLogService {
    void saveLogs(Long userId,
                  Map<String, List<StrategyDo>> appBannerId2StrategyListMap,
                  Map<String, Map<Long, StrategyMarketChannelDo>> appBannerId2StrategyId2StrategyChannelMap,
                  Map<Long, HitResult> strategyId2HitResultMap,
                  List<Long> crowdNotHitStrategyIdList,
                  List<Long> labelNotHitStrategyIdList,
                  List<Long> abGroupNotHitStrategyIdList,
                  List<OfflineDecisionRecordEntity> succeedDecisionList,
                  List<UserDispatchDetailDo> dispatchDetailDoList,
                  LocalDateTime triggerDatetime, String tableNumber);
}
