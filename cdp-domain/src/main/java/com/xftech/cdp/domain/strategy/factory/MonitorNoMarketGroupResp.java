package com.xftech.cdp.domain.strategy.factory;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xftech.cdp.api.dto.base.TimeFormat;
import com.xftech.cdp.api.dto.resp.AbsMonitorListResp;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyExecLogDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * @<NAME_EMAIL>
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class MonitorNoMarketGroupResp extends AbsMonitorListResp {
    @ApiModelProperty(value = "日期")
    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    @ExcelProperty(index = 0, value = "日期")
    protected LocalDateTime dateTime;

    @ApiModelProperty(value = "分组")
    @ExcelProperty(index = 1, value = "分组")
    protected String groupName;

    @ApiModelProperty(value = "应发用户数")
    @ExcelProperty(index = 2, value = "应发用户数")
    protected String execCount;

    public MonitorNoMarketGroupResp convertRes(StrategyExecLogDo item) {
        MonitorNoMarketGroupResp monitorNoMarketGroupResp = new MonitorNoMarketGroupResp();
        monitorNoMarketGroupResp.setDateTime(item.getExecTime());
        monitorNoMarketGroupResp.setGroupName(item.getStrategyGroupName());
        monitorNoMarketGroupResp.setExecCount(String.valueOf(item.getExecCount()));
        return monitorNoMarketGroupResp;
    }
}
