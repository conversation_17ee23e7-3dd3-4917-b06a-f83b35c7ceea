package com.xftech.cdp.domain.crowd.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.domain.crowd.model.dispatch.MinMaxId;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * crowd_detail 主表 dao
 *
 * <AUTHOR>
 * @since 2023/2/14
 */

@Component
public class CrowdDetailRepository {
    /**
     * 批量保存人群用户明细
     *
     * @param crowdDetailDoList 人群用户明细对象列表
     */
    public void saveBatch(List<CrowdDetailDo> crowdDetailDoList) {
        DBUtil.insertBatch("crowdDetail.insertSelective", crowdDetailDoList);
    }

    public void clearData(LocalDateTime sinceTime, List<Long> crowdIds) {
        Map<String, Object> param = new HashMap<>();
        param.put("sinceTime", sinceTime);
        param.put("crowdIds", crowdIds);
        DBUtil.delete("crowdDetail.clearData", param);
    }

    public void clearData(Long minId, Long maxId) {
        Map<String, Object> param = new HashMap<>();
        param.put("minId", minId);
        param.put("maxId", maxId);
        DBUtil.delete("crowdDetail.clearDataByExecLogId", param);
    }

    public void clearSubData(Long minId, Long maxId, String tableName) {
        Map<String, Object> param = new HashMap<>();
        param.put("minId", minId);
        param.put("maxId", maxId);
        param.put("tableName", tableName);
        DBUtil.delete("crowdDetail.clearSubData", param);
    }

    /**
     * 根据人群包id删除人群用户明细
     *
     * @param crowdId 人群包id
     */
    public void deleteByCrowdId(Long crowdId) {
        DBUtil.delete("crowdDetail.deleteByCrowdId", crowdId);
    }

    /**
     * 分页查询人群
     *
     * @param id
     * @param limit
     * @param crowdId
     * @param crowdExecLogId
     * @return
     */
    public List<CrowdDetailDo> selectByIdPage(Long id, Integer limit, Long crowdId, Long crowdExecLogId) {
        Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        param.put("crowdId", crowdId);
        param.put("crowdExecLogId", crowdExecLogId);
        param.put("pageSize", limit);
        return DBUtil.selectList("crowdDetail.selectByIdPage", param);
    }

    /**
     * 根据最新执行日志id，查询人群
     *
     * @param tableName
     * @param crowdMaxExecLogId
     * @param crowdDetailId
     * @param pageSize
     * @return
     */
    public List<CrowdDetailDo> selectBatchByCrowdMaxExecLogId(String tableName, Long crowdMaxExecLogId, Long crowdDetailId, Long pageSize) {
        Map<String, Object> param = new HashMap<>();
        param.put("tableName", tableName);
        param.put("crowdMaxExecLogId", crowdMaxExecLogId);
        param.put("crowdDetailId", crowdDetailId);
        param.put("pageSize", pageSize);
        return DBUtil.selectList("crowdDetail.selectBatchByCrowdMaxExecLogId", param);
    }

    public MinMaxId selectMinId() {
        return DBUtil.selectOne("crowdDetail.selectMinId", null);
    }

    public MinMaxId selectSubMinId(String tableName) {
        return DBUtil.selectOne("crowdDetail.selectSubMinId", tableName);
    }

    /**
     * 根据主键id删除记录
     *
     * @param id 主键id
     */
    public void deleteById(Long id) {
        DBUtil.delete("crowdDetail.deleteById", id);
    }

    /**
     * 根据主键id删除记录
     *
     * @param ids 主键ids
     */
    public void delDirtyData(List<Long> ids) {
        DBUtil.deleteBatchWithoutTx("crowdDetail.deleteById", ids);
    }


    /**
     * 批量更新人群用户明细
     *
     * @param crowdDetailDoList 人群用户明细对象列表
     * @return 是否更新成功标识
     */
    public boolean updateBatchById(List<CrowdDetailDo> crowdDetailDoList) {
        return DBUtil.updateBatchWithoutTx("crowdDetail.updateByPrimaryKeySelective", crowdDetailDoList) > 0;
    }


    /**
     * 根据crowdId删除记录
     *
     * @param crowdId 人群包id
     */
    public void updateStatusByCrowdIdAndExecLogId(Long crowdId, Integer dFlag, Integer oriDflag, Long crowdExecLogId) {
        Map<String, Object> param = new HashMap<>();
        param.put("crowdId", crowdId);
        param.put("dFlag", dFlag);
        param.put("oriDflag", oriDflag);
        param.put("crowdExecLogId", crowdExecLogId);
        DBUtil.update("crowdDetail.updateFlagByCrowdIdAndExecLogId", param);
    }

    public Optional<CrowdDetailDo> hasUserRecord(Long userId, Long crowdExecLogId, String tableName) {
        Map<String, Object> param = new HashMap<>();
        param.put("userId", userId);
        param.put("crowdExecLogId", crowdExecLogId);
        param.put("tableName", tableName);
        return Optional.ofNullable(DBUtil.selectOne("crowdDetail.hasUserRecord", param));
    }
}
