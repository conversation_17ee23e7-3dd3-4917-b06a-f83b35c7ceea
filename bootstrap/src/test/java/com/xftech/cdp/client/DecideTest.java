package com.xftech.cdp.client;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.xftech.cdp.BaseTest;
import com.xftech.cdp.adapter.strategy.label.QueryLabelRequest;
//import com.xftech.cdp.adapter.strategy.label.handler.VcCoreIsVipLabelHandler;
import com.xftech.cdp.api.ExternalApi;
import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.base.Response;
import com.xftech.cdp.api.dto.req.DecideReq;
import com.xftech.cdp.api.dto.req.external.CallingSourceEnum;
import com.xftech.cdp.api.dto.req.external.ExternalBaseRequest;
import com.xftech.cdp.api.dto.resp.DecideResp;
import com.xftech.cdp.api.dto.req.external.StrategyListReq;
import com.xftech.cdp.api.dto.resp.external.StrategyListResp;
import com.xftech.cdp.domain.cache.CacheStrategyMarketEventConditionService;
import com.xftech.cdp.domain.cache.CacheStrategyMarketSubEventService;
import com.xftech.cdp.domain.randomnum.RandomNumService;
import com.xftech.cdp.domain.strategy.model.enums.BusinessTypeEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyInstantLabelTypeEnum;
import com.xftech.cdp.domain.strategy.service.DecideService;
import com.xftech.cdp.domain.strategy.service.StrategyInstantLabelService;
import com.xftech.cdp.domain.strategy.vo.BatchAdsLabelVO;
import com.xftech.cdp.infra.client.ads.model.resp.AdsLabelResp;
import com.xftech.cdp.infra.client.dingtalk.DingTalkUtil;
import com.xftech.cdp.infra.client.dingtalk.config.DingTalkConfig;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyInstantLabelDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketEventConditionDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketSubEventDo;
import com.xftech.cdp.infra.utils.JsonUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2023/2/16
 */
public class DecideTest extends BaseTest {

    @Autowired
    private DecideService decideService;
    @Autowired
    private ExternalApi externalApi;

    @Test
    public void testGetStrategyListForOverloan() {
        ExternalBaseRequest<StrategyListReq> req = new ExternalBaseRequest<>();
        StrategyListReq slReq = new StrategyListReq();
        slReq.setBusinessType(BusinessTypeEnum.LOAN_OVERLOAD.getCode());
        slReq.setCallingSource(CallingSourceEnum.Overloan);
        req.setArgs(slReq);
        req.setUa("test");

        Response<PageResultResponse<StrategyListResp>> resp = externalApi.getStrategyList(req);
        System.out.println(JsonUtil.toJson(resp));
        for (StrategyListResp record : resp.getResult().getRecords()) {
            System.out.println(JsonUtil.toJson(record));
        }
    }

    @Test
    public void testDecide() {
        Long strategyId = 2512L;
        String app = "xyf01";
        LocalDateTime start = LocalDate.now().atStartOfDay();
        String mobile = "***********";
        Long appUserId = 1109792134L;
        appUserId = 1939303089098215234L;
        BatchAdsLabelVO adsLabelVO = new BatchAdsLabelVO();
        adsLabelVO.setStrategyId(strategyId);
        adsLabelVO.setApp(app);
//        String startTime = LocalDateTimeUtil.format(start, "yyyy-MM-dd HH:mm:ss");
//        adsLabelVO.setStartTime(startTime);
//        System.out.println(startTime);

        DecideReq req = new DecideReq();
        req.setUserId(appUserId);
        req.setCallingSource(CallingSourceEnum.Overloan);
        req.setBusinessType(BusinessTypeEnum.LOAN_OVERLOAD.getCode());
//        req.setStrategyIdList(Arrays.asList(2564L, 2565L, 2566L));
        req.setStrategyIdList(Arrays.asList(2591L));
        req.setApp("xyf01");
        req.setMobile(mobile);
        ExternalBaseRequest<DecideReq> decideReq = new ExternalBaseRequest<>();
        decideReq.setUa("test");
        decideReq.setArgs(req);

        Response<List<DecideResp>> response = externalApi.decide(decideReq);

        System.out.println(JsonUtil.toJson(decideReq));
        System.out.println(JsonUtil.toJson(response));
        List<DecideResp> respList = response.getResult();
        if (Objects.nonNull(respList)) {
            for (DecideResp resp : respList) {
                System.out.println(JsonUtil.toJson(resp));
            }
        }

        System.out.println("------------------------------------------");

        req.setUserId(100L);
        response = externalApi.decide(decideReq);

        System.out.println(JsonUtil.toJson(req));
        System.out.println(JsonUtil.toJson(response));
        respList = response.getResult();
        if (Objects.nonNull(respList)) {
            for (DecideResp resp : respList) {
                System.out.println(JsonUtil.toJson(resp));
            }
        }
    }


    @Test
    public void testDecideApiHold() {
        Long strategyId = 2512L;
        String app = "xyf01";
        LocalDateTime start = LocalDate.now().atStartOfDay();
        String mobile = "***********";
        Long appUserId = 1109792134L;
        BatchAdsLabelVO adsLabelVO = new BatchAdsLabelVO();
        adsLabelVO.setStrategyId(strategyId);
        adsLabelVO.setApp(app);
        String startTime = LocalDateTimeUtil.format(start, "yyyy-MM-dd HH:mm:ss");
        adsLabelVO.setStartTime(startTime);
        System.out.println(startTime);

        DecideReq req = new DecideReq();
        req.setUserId(appUserId);
        req.setCallingSource(CallingSourceEnum.ApiHold);
        req.setApp("xyf01");
        req.setInnerApp("xyf01_cxh");
        req.setMobile(mobile);
        req.setBusinessType(BusinessTypeEnum.OLD_CUST.getCode());

        List<DecideResp> respList = decideService.decide(req);

        System.out.println(JsonUtil.toJson(req));
        for (DecideResp resp : respList) {
            System.out.println(JsonUtil.toJson(resp));
        }

        System.out.println("------------------------------------------");
    }

    @Test
    public void testApiHold() {
        String s = "{\"app\":\"xyf01\",\"callingSource\":\"ApiHold\",\"innerApp\":\"xyf01_xykd\",\"ua\":\"test\",\"userId\":\"1939303089098211438\"}";
        ExternalBaseRequest<DecideReq> decideReq = new ExternalBaseRequest<>();
        decideReq.setUa("test");
        DecideReq req = new DecideReq();
        req.setApp("xyf01");
        req.setCallingSource(CallingSourceEnum.ApiHold);
        req.setInnerApp("xyf01_xykd");
        req.setMobile("12345678");
        req.setBusinessType(BusinessTypeEnum.OLD_CUST.getCode());
//        req.setUserId(1939303089098211438L);
        req.setUserId(1109792134L);
        decideReq.setArgs(req);
        s = JsonUtil.toJson(decideReq);
        System.out.println(s);
        System.out.println("--------------------------------------------------------------------");

        Response<List<DecideResp>> resp = externalApi.decide(decideReq);
        System.out.println(JsonUtil.toJson(resp));
        System.out.println("--------------------------------------------------------------------");
        System.out.println(resp.getCode());
        System.out.println(resp.getMessage());
        System.out.println(resp.getDateTime());
        if (resp.getCode() == HttpStatus.OK.value()) {
            for (DecideResp decideResp : resp.getResult()) {
                System.out.print("\t");
                System.out.println(JsonUtil.toJson(decideResp));
            }
        }
    }

    @Test
    public void testApiHoldFromParam() {
        String s = "{\n" +
                "    \"args\": {\n" +
                "        \"app\": \"xyf01\",\n" +
                "        \"businessType\": \"old-cust\",\n" +
                "        \"callingSource\": \"ApiHold\",\n" +
                "        \"innerApp\": \"xyf01_yixin\",\n" +
                "        \"mobile\": \"***********\",\n" +
                "        \"userId\": 1939303089098215296\n" +
                "    },\n" +
                "    \"sign\": null,\n" +
                "    \"timestamp\": 1720519449,\n" +
                "    \"ua\": \"test\"\n" +
                "}";
        ExternalBaseRequest<DecideReq> req = new ExternalBaseRequest<>();
        req = JsonUtil.parse(s, req.getClass());

        Response<List<DecideResp>> response = externalApi.decide(req);
        System.out.println(JsonUtil.toJson(response));
    }

    @Test
    public void testLoanOverloadfromParam() {
        String s = "{\n" +
                "    \"callingSource\": \"Overloan\",\n" +
                "    \"businessType\": 2,\n" +
                "    \"strategyId\": 2798,\n" +
                "    \"strategyStatusList\": [\n" +
                "        0,\n" +
                "        1,\n" +
                "        2,\n" +
                "        3,\n" +
                "        4\n" +
                "    ],\n" +
                "    \"pageNum\": 1,\n" +
                "    \"pageSize\": 10\n" +
                "}";

        ExternalBaseRequest<StrategyListReq> req = new ExternalBaseRequest<>();
        StrategyListReq listReq = new StrategyListReq();
        listReq.setPageNum(1);
        listReq.setPageSize(10);
        listReq.setStrategyStatusList(Arrays.asList(0, 1, 2, 3, 4));
        listReq.setStrategyId(2798L);
        listReq.setBusinessType(BusinessTypeEnum.LOAN_OVERLOAD.getCode());
        listReq.setCallingSource(CallingSourceEnum.Overloan);
        req.setUa("test");
        req.setArgs(listReq);
//        req = JsonUtil.parse(s, req.getClass());
        String reqJson = JsonUtil.toJson(req);
        System.out.println(reqJson);

        Response<PageResultResponse<StrategyListResp>> response = externalApi.getStrategyList(req);
        System.out.println(JsonUtil.toJson(response));
    }

    @Test
    public void testOverloanDecideFromParam() {
        String s = "{\n" +
                "    \"args\": {\n" +
                "        \"app\": \"xyf01\",\n" +
                "        \"businessType\": \"loan-overload-cust\",\n" +
                "        \"callingSource\": \"Overloan\",\n" +
                "        \"innerApp\": \"xyf01\",\n" +
                "        \"mobile\": \"***********\",\n" +
                "        \"strategyIdList\": [\n" +
                "            1\n" +
                "        ],\n" +
                "        \"userId\": 1109792134\n" +
                "    },\n" +
                "    \"sign\": null,\n" +
                "    \"timestamp\": 1720519449,\n" +
                "    \"ua\": \"test\"\n" +
                "}";
        ExternalBaseRequest<DecideReq> req = new ExternalBaseRequest<>();
        req = JsonUtil.parse(s, req.getClass());

        Response<List<DecideResp>> response = externalApi.decide(req);
        System.out.println(JsonUtil.toJson(response));

    }


    @Autowired
    private DingTalkConfig dingTalkConfig;

    @Test
    public void testDingAlarm() {
        List<String> atMobileList = new ArrayList<>(dingTalkConfig.atMobileList());
//        atMobileList.add("17718526781");
        String url = "https://oapi.dingtalk.com/robot/send?access_token=a22999edb972e5482e903e066d34cee2f976fc5564acbd88bf0864446d5e7985";
//        DingTalkUtil.sendTextToDingTalk(dingTalkConfig.getAlarmUrl(), "API卡单多策略共享 测试一下", atMobileList, false);
        DingTalkUtil.sendTextToDingTalk(url, "API卡单多策略共享 测试一下", atMobileList, false);
    }


    @Test
    public void testDecideOverLoan() {
        String mobile = "***********";
        Long appUserId = 1109792134L;
        DecideReq req = new DecideReq();
        req.setUserId(appUserId);
        req.setCallingSource(CallingSourceEnum.Overloan);
        req.setApp("xyf01");
        req.setMobile(mobile);
        req.setBusinessType(BusinessTypeEnum.LOAN_OVERLOAD.getCode());
        req.setStrategyIdList(Arrays.asList(2627L, 2610L, 2609L, 2605L, 2591L));

        ExternalBaseRequest<DecideReq> decideReq = new ExternalBaseRequest<>();
        decideReq.setArgs(req);
        decideReq.setUa("test");
        Response<List<DecideResp>> response = externalApi.decide(decideReq);
//        List<DecideResp> respList = decideService.decide(req);

        System.out.println(JsonUtil.toJson(req));
        System.out.println(JsonUtil.toJson(response));
        List<DecideResp> respList = response.getResult();
        if (Objects.nonNull(respList)) {
            for (DecideResp resp : respList) {
                System.out.println(JsonUtil.toJson(resp));
            }
        }

        System.out.println("------------------------------------------");

//        req.setUserId(100L);
//        respList = decideService.decide(req);
//
//        System.out.println(JsonUtil.toJson(req));
//
//        for (DecideResp resp : respList) {
//            System.out.println(JsonUtil.toJson(resp));
//        }
    }

    @Test
    public void testDecideOverLoanWithCrowdNewRandom() {

        Map<Long, String> userMobileMap = new HashMap<>();
        userMobileMap.put(111111114677875L, "15308683171");
        userMobileMap.put(111111113377673L, "18072316778");
        userMobileMap.put(111111114677871L, "***********");
        userMobileMap.put(111111113377680L, "***********");

        for (Map.Entry<Long, String> entry : userMobileMap.entrySet()) {
            String mobile = entry.getValue();
            Long appUserId = entry.getKey();
            DecideReq req = new DecideReq();
            req.setUserId(appUserId);
            req.setCallingSource(CallingSourceEnum.Overloan);
            req.setApp("xyf01");
            req.setMobile(mobile);
            req.setBusinessType(BusinessTypeEnum.LOAN_OVERLOAD.getCode());
            req.setStrategyIdList(Arrays.asList(2643L));

            ExternalBaseRequest<DecideReq> decideReq = new ExternalBaseRequest<>();
            decideReq.setArgs(req);
            decideReq.setUa("test");
            Response<List<DecideResp>> response = externalApi.decide(decideReq);
//        List<DecideResp> respList = decideService.decide(req);

            System.out.println(JsonUtil.toJson(req));
            System.out.println(JsonUtil.toJson(response));
            List<DecideResp> respList = response.getResult();
            if (Objects.nonNull(respList)) {
                for (DecideResp resp : respList) {
                    System.out.println(JsonUtil.toJson(resp));
                }
            }

            System.out.println("------------------------------------------");
        }

    }

    @Autowired
    private CacheStrategyMarketSubEventService cacheStrategyMarketSubEventService;

    @Test
    public void testGetByEventId() {
        for (int i = 0; i < 10; i++) {
            List<StrategyMarketSubEventDo> obj = cacheStrategyMarketSubEventService.getByEventId(111111L);
            System.out.println(JsonUtil.toJson(obj));
            obj = cacheStrategyMarketSubEventService.getByEventId(298L);
            System.out.println(JsonUtil.toJson(obj));
            System.out.println("----------------------");
        }
    }

    @Autowired
    private StrategyInstantLabelService strategyInstantLabelService;

    @Test
    public void testGetByLabelNameAndLabelType() {
        for (int i = 0; i < 1; i++) {
            StrategyInstantLabelDo vcCoreIsVip = strategyInstantLabelService.getByLabelNameAndLabelType("vc_core_is_vip", StrategyInstantLabelTypeEnum.LABEL, 2, null);
            System.out.println(JsonUtil.toJson(vcCoreIsVip));
            vcCoreIsVip = strategyInstantLabelService.getByLabelNameAndLabelType("vc_core_is_vip", StrategyInstantLabelTypeEnum.LABEL, 3, null);
            System.out.println(JsonUtil.toJson(vcCoreIsVip));
        }
    }

    @Autowired
    private CacheStrategyMarketEventConditionService cacheStrategyMarketEventConditionService;

    @Test
    public void testGetStrategyMarketEventConditionByStrategyId() {
        for (int i = 0; i < 10; i++) {
            List<StrategyMarketEventConditionDo> data = cacheStrategyMarketEventConditionService.getByStrategyId(11111111L);
            System.out.println(JsonUtil.toJson(data));
            data = cacheStrategyMarketEventConditionService.getByStrategyId(324L);
            System.out.println(JsonUtil.toJson(data));
            System.out.println("-------------");
        }
    }

    //@Autowired
    //private VcCoreIsVipLabelHandler vcCoreIsVipLabelHandler;

    //@Test
    //public void testVcCoreIsVipLabelHandler() {
    //    QueryLabelRequest batchAdsLabelVO = new QueryLabelRequest();
    //    batchAdsLabelVO.setUserNoList(Arrays.asList(
    //            1939303089098209236L,
    //            1639203089095980520L,
    //            1939303089101716278L,
    //            1939303089102117155L,
    //            1939303089102116297L,
    //            1939303089102116296L));
    //    AdsLabelResp resp = vcCoreIsVipLabelHandler.execute(null, batchAdsLabelVO);
    //    System.out.println(JsonUtil.toJson(resp));
    //    if (Objects.nonNull(resp) && Objects.nonNull(resp.getParams())) {
    //        for (AdsLabelResp.Param param : resp.getParams()) {
    //            System.out.print("\t");
    //            System.out.println(JsonUtil.toJson(param));
    //        }
    //    }
    //}


    @Autowired
    private RandomNumService randomNumService;

    @Test
    public void testGenerateRandomNumber() {
        String str = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-_";
        Random random = new Random();
        BufferedWriter bufferedWriter = null;
        try {
            bufferedWriter = new BufferedWriter(new FileWriter("new_random_number.out"));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        for (int i = 1; i < 100; i++) {
            for (int j = 0; j < str.length(); j++) {
                StringBuffer sb = new StringBuffer();
                for (int k = 0; k < i; k++) {
                    int randIndex = random.nextInt(str.length());
                    sb.append(str.charAt(randIndex));
                }
                String bizKey = sb.toString();
                Long userId = random.nextLong();
                Long randomNumber = randomNumService.generateRandomNumber(bizKey, userId, 2);
                String line = String.format("%s,%d,%d\n", bizKey, userId, randomNumber);
                System.out.print(line);
                try {
                    bufferedWriter.write(line);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        }

        try {
            bufferedWriter.close();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
