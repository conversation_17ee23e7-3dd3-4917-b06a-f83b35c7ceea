package com.xftech.cdp.infra.rabbitmq.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Map;
import java.util.Optional;

@Data
public class BizEventMessageVO {
    /**
     * 事件英文名
     */
    @JsonProperty("biz_event_type")
    @JSONField(name = "biz_event_type")
    private String bizEventType;

    /**
     * 用户 creditUserId
     */
    @JsonProperty("credit_user_id")
    @JSONField(name = "credit_user_id")
    private Long creditUserId;

    /**
     * 用户 app_user_id
     */
    @JsonProperty("app_user_id")
    @JSONField(name = "app_user_id")
    private Long appUserId;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 手机号密文
     */
    private String mobile_cipher;

    /**
     * 事件触发 app
     */
    private String app;

    /**
     * 事件触发 os
     */
    private String os;

    /**
     * 事件触发 innerApp
     */
    @JsonProperty("inner_app")
    @JSONField(name = "inner_app")
    private String innerApp;

    /**
     * 渠道
     */
    @JsonProperty("utm_source")
    @JSONField(name = "utm_source")
    private String utmSource;

    /**
     * 扩展字段
     */
    @JsonProperty("extra_data")
    @JSONField(name = "extra_data")
    private ExtrData extrData;

    /**
     * 事件时间
     */
    @JsonProperty("event_datetime")
    @JSONField(name = "event_datetime")
    private String eventDatetime;

    /**
     * 上报时间
     */
    @JsonProperty("trigger_datetime")
    @JSONField(name = "trigger_datetime")
    private String triggerDatetime;

    /**
     * 设备ID
     */
    @JsonProperty("device_id")
    @JSONField(name = "device_id")
    private String deviceId;

    /**
     * 渠道类型: client-客户端 wap-h5 wxmnp-微信小程序
     */
    @JsonProperty("source_type")
    @JSONField(name = "source_type")
    private String sourceType;

    /**
     * 扩展字段
     */
    private Map<String,Object> ext;

    /**
     * IP地址
     */
    private String ip;

    // 请不要在业务中使用该字段，目前只作为流控使用;
    private Long user_id;

    // 兜底小程序app和userNo未对应问题
    private String cnlPdCode;

    /**
     * 重写getTriggerDatetime
     * <p>
     * triggerDatetime、eventDatetime均表示触发时间，返回不是空，均为空返回null
     *
     * @return 触发时间
     */
    public String getTriggerDatetime() {
        return Optional.ofNullable(triggerDatetime).orElse(eventDatetime);
    }

    public Long getCreditUserId() {
        return Optional.ofNullable(creditUserId).orElse(appUserId);
    }

    @Data
    @NoArgsConstructor
    public static class ExtrData {
        /**
         * 提额后可用金额
         */
        private BigDecimal amount;

        /**
         * 提额金额
         */
        @JsonProperty("adjust_change")
        @JSONField(name = "adjust_change")
        private BigDecimal adjustChange;

        /**
         * 提额金额
         */
        @JsonProperty("adjust_amount")
        @JSONField(name = "adjust_amount")
        private BigDecimal adjustAmount;

        /**
         * 注册时间
         */
        @JsonProperty("created_time")
        @JSONField(name = "created_time")
        private String createdTime;

        /**
         * 当前渠道
         */
        private String currentUtmSource;

        /**
         * 调用风控的提额类型Code
         */
        private String increaseCode;

        /**
         * 来源类型
         */
        private String sourceType;

        /**
         * 订单号
         * <p>
         * biz_event_type：SettleSuccess
         */
        @JsonProperty("order_number")
        @JSONField(name = "order_number")
        private String orderNumber;

        /**
         * 是否结清,true 是结清，false 不是结清
         * <p>
         * biz_event_type：SettleSuccess
         */
        @JsonProperty("is_settle")
        @JSONField(name = "is_settle")
        private boolean isSettle;

        /**
         * 还款类型
         * <p>
         * 0自动扣款，1主动还款，2线下还款，3管理员扣款
         * biz_event_type：SettleSuccess
         */
        @JsonProperty("repay_type")
        @JSONField(name = "repay_type")
        private Integer repayType;

        /**
         * biz_event_type：RepaySuccess
         */
        @JsonProperty("fund_source")
        @JSONField(name = "fund_source")
        private String fundSource;

        /**
         * biz_event_type：RepaySuccess
         */
        @JsonProperty("pay_type")
        @JSONField(name = "pay_type")
        private String payType;

        /**
         * biz_event_type：RepaySuccess
         */
        @JsonProperty("pay_channel")
        @JSONField(name = "pay_channel")
        private String payChannel;

        /**
         * biz_event_type：RepaySuccess
         */
        @JsonProperty("biz_payment_number")
        @JSONField(name = "biz_payment_number")
        private String bizPaymentNumber;

        /**
         * biz_event_type：RepaySuccess
         */
        @JsonProperty("app")
        @JSONField(name = "app")
        private String app;

        /**
         * biz_event_type：RepaySuccess
         */
        @JsonProperty("inner_app")
        @JSONField(name = "inner_app")
        private String innerApp;

        /**
         * 扣款单状态.   03-成功,04-失败
         * biz_event_type：RepaySuccess
         */
        @JsonProperty("deduction_status")
        @JSONField(name = "deduction_status")
        private String deductionStatus;

        private String fail_reason;
        private String fail_reason_detail;

        // 对接的事件：RiskActivation
        //extra data里，activation_status='success'\“激活成功”;activation_status='failed'\“激活失败”
        private String activation_status;

        //对接的事件：RiskTempCredit
        //extra data里,start_time为临额生效时间,end_time为临额失效时间
        private String start_time;
        private String end_time;

        //对接的事件: LoanSuccess
        private Boolean is_first_loan;
        private String fund_source_type;
        private String cnlPdCode;
        private Integer term;
        private String riskPriceType;
        private Double riskPrice;
        //订单状态
        private String status;
        private String failedReason;
        private String failedCode;
        private String createTime;
        private String loanSuccessTime;

        // 对接提额通知事件
        private String increaseAmtStrategyId;
        // 提额状态, 1-成功 0-失败
        private Integer increaseAmtStatus;
        //对接mq事件：userLogOff
        private String source;
        //对接mq事件：ExtractQuotaCardGuide
        private String useManageType;
        private String priceGroup;
        private String expectedAdvanceAmount;
        private String adjustmentStatus;
        private String useTriggerType;
        //对接mq事件：orderPayMember
        private String orderPayStatus;
        private Integer currentDeductNumber;
        private Date completeTime;
        private Date firstDeductExecutionTime;
        private BigDecimal orderPayAmount;
        // LifeRights:1-成功 0-失败
        private Long lifeRightsCallBackStrategyId;
        private Integer lifeRightCallBackStatus;

        //rcsptIncreaseCredit
        @JSONField(name = "manage_type")
        @JsonProperty("manage_type")
        private String manageType;
        private String increaseCreditResult;
        private BigDecimal availableAmount;

        //AppLoginIncreaseCredit
        private BigDecimal appLoginIncreaseAvailableAmount;
        private BigDecimal appLoginIncreaseAdjustAmount;
        //CxhRepayXyf01IncreaseCredit
        private String cxhRepayXyf01IncreaseAdjustResult;
        private BigDecimal cxhRepayXyf01IncreaseAdjustAmount;
        //xDay
        private Long xDayInterestFreeCallBackStrategyId;
        private Integer xDayInterestFreeCallBackStatus;
        //vcSign
        private Integer vcSignType;
        private Integer vcSignCardType;
        private Integer vcSignIsRenew;
        private Date vcDeductTime;
        private String vcSignOrderPrice;
        // 是否参与续费活动
        private Integer isRenewActivity;
        // 是否领取vip资金权益
        private Integer isVipFund;
        //LendtradeDerate
        private BigDecimal oriLoanAmt;
        private BigDecimal loanAmt;
        private Integer expirationHour;
        private String freezeType;
        /* 撞库 */
        private Integer credentialStuffingResult;
        //api撞库
        private Integer apiCredentialStuffingResult;
        @JsonProperty("check_type")
        @JSONField(name = "check_type")
        private Integer checkType;
        @JsonProperty("check_time")
        @JSONField(name = "check_time")
        private Date checkTime;
        private String checkResult;
        /**
         * 是否有放款成功记录，0-无，1-有；
         */
        private Integer hasLoanSuccRecord;

        /**
         * 数据来源，1-API渠道实时请求，2-离线撞库
         */
        private Integer dataSource;
        /* 分发授信成功事件 */
        private String product;
        private String orderNo;
        //风控禁申事件 RcsptRiskAccessControl
        private String bizType;
        private String loanType;
        private Integer forbiddenApplyDay;
        private String diversionLabel;
        private String accessAction;//禁申动作 0禁申，1解除禁申
        //月月惊喜权益发放成功 事件 surpriseRight
        private String surpriseRightType;//权益类型 1-超级提现机会；2-送提额卡；3-VIP特批资金；4-金融券；5-消费券
        private BigDecimal surpriseRightLimitAmtCardAmount;//提额卡金额（单位分）
        private BigDecimal surpriseRightVipFund;// vip特批资金金额（单位分）
        private String surpriseRightCashCouponName;//金融券名称
        private String surpriseRightConsumeCouponName;//消费券名称
        /**
         * 调额结果 S:成功,F:失败
         */
        @JSONField(name = "adjust_result")
        @JsonProperty("adjust_result")
        String adjustResult;

        @JsonProperty("decision_result")
        @JSONField(name = "decision_result")
        private DecisionResult decisionResult;

        //对接事件 RiskTempCredit
        private String temporaryQuotaEffectiveTime;
        private String temporaryQuotaInvalidTime;


        @JSONField(name = "credit_message_type")
        @JsonProperty("credit_message_type")
        String creditMessageType;

        //(新)注册 用户注册来源-营销落地页注册xc_mk = 1 为是
        private Integer userRegisterSource;
        private Integer equityType;//权益类型

        /**
         * 预借款场景：PRE_LEND_NEW:预借款单-新客,PRE_LEND_OLD:预借款单-老客
         */
        private String advanceLoanScene;

        /**
         * AI通话事件-外呼状态
         */
        private String callStatus;

        //API进件事件 API进件结果
        private Integer apiEntryResult;

        /**
         * 用户收到优惠劵事件券类型 1:借款免息券 2:还款立减金 3:限时提额券
         */
        private Integer couponType;
        /**
         * 券归属方1：首贷运营2：复贷运营3：客服4：变现运营5：产品测试
         */
        private Integer couponBelongsTo;

        //电销入库事件 入库结果1：入库成功2：入库失败
        private Integer inventoryStatus;
        public ExtrData(String createdTime, String currentUtmSource, String sourceType) {
            this.createdTime = createdTime;
            this.currentUtmSource = currentUtmSource;
            this.sourceType = sourceType;
        }
    }

    @Data
    @NoArgsConstructor
    public static class DecisionResult {

        @JsonProperty("api_batch_adj_output")
        @JSONField(name = "api_batch_adj_output")
        private String apiBatchAdjOutput;

        @JsonProperty("pre_modify_amount_price_group_output")
        @JSONField(name = "pre_modify_amount_price_group_output")
        private String preModifyAmountPriceGroupOutput;
    }
}
