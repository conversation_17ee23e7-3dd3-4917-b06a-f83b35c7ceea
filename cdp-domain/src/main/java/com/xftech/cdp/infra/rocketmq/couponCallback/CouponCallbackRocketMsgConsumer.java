package com.xftech.cdp.infra.rocketmq.couponCallback;

import com.xftech.cdp.infra.utils.WhitelistSwitchUtil;
import com.xinfei.xfframework.common.starter.mq.MqConsumerListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @version $ CouponCallbackMsgConsumer, v 0.1 2025/3/17 17:34 tianshuo.qiu Exp $
 */
@Slf4j
@Component
@RocketMQMessageListener(consumerGroup = "cg_xyf_cdp_tp_userassetcore_cash_coupon_distribute_success", topic = "tp_userassetcore_cash_coupon_distribute_success", consumeMode = ConsumeMode.CONCURRENTLY)
public class CouponCallbackRocketMsgConsumer extends MqConsumerListener<String> {
    @Autowired
    private CouponCallbackHandler couponCallbackHandler;

    @Override
    public void doMessage(String topic, String bodyMessage, MessageExt messageExt) {
        if (WhitelistSwitchUtil.boolSwitchByApollo("CouponCallbackRocketMsgConsumerEnable")) {
            log.info("CouponCallbackRocketMsgConsumer doMessage topic={} messageId={} body={}", topic, messageExt.getMsgId(), bodyMessage);
            try {
                couponCallbackHandler.execute( messageExt.getMsgId(), bodyMessage);
            } catch (Exception e) {
                log.error("CouponCallbackRocketMsgConsumer doMessage error topic={} messageId={} body={}", topic, messageExt.getMsgId(), bodyMessage, e);
            }
        }
    }
}
