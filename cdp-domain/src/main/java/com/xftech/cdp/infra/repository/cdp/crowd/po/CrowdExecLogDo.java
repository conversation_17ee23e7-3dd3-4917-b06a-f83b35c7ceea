package com.xftech.cdp.infra.repository.cdp.crowd.po;

import com.alibaba.fastjson.JSON;
import com.xftech.cdp.api.dto.crowd.CrowdXxlJobParam;
import com.xftech.cdp.domain.crowd.model.dispatch.CrowdContext;
import com.xftech.cdp.domain.crowd.model.enums.CrowdExecResultEnum;
import com.xftech.cdp.domain.crowd.model.enums.CrowdStatusEnum;
import com.xftech.cdp.infra.config.ApolloKeyConstants;
import com.xftech.cdp.infra.constant.XxlJobConstants;
import com.xftech.cdp.infra.repository.Do;
import com.xftech.xxljob.model.XxlJobDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 人群包执行日志记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CrowdExecLogDo extends Do {

    /**
     * 人群包id
     */
    private Long crowdId;

    /**
     * 执行时间
     */
    private LocalDateTime execTime;

    /**
     * 执行结束时间
     */
    private LocalDateTime finishExecTime;

    /**
     * 执行结果，0:正在执行，1:成功，2:失败
     */
    private Integer execResult;

    /**
     * 执行类型，0:老方案：job执行，1:新方案：大数据执行
     */
    private Integer execType;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 执行人
     */
    private String execMan;


    private Integer retryJobId;

    private Integer crowdPersonNum;

    public static CrowdExecLogDo beginExecute(Long crowdId, String execMan) {
        CrowdExecLogDo execLog = new CrowdExecLogDo();
        execLog.setCrowdId(crowdId);
        execLog.setExecResult(CrowdExecResultEnum.EXECUTING.getCode());
        execLog.setExecTime(LocalDateTime.now());
        execLog.setExecMan(execMan);
        return execLog;
    }

    public void execFailed(String failReason) {
        execResult = CrowdExecResultEnum.FAIL.getCode();
        this.failReason = failReason;
        this.finishExecTime = LocalDateTime.now();
    }


    public void execSuccess(Integer personNum) {
        execResult = CrowdExecResultEnum.SUCCESS.getCode();
        crowdPersonNum = personNum;
        finishExecTime = LocalDateTime.now();
    }


    public XxlJobDto failToRetry(String cron, CrowdContext crowdContext, Integer retryCount) {
        CrowdXxlJobParam crowdXxlJobParam = new CrowdXxlJobParam(crowdContext.getCrowdExecLog().getCrowdId());
        return XxlJobDto.builder()
                .jobCron(cron)
                .jobDesc(String.format("人群包：%s 重试第%s次", crowdContext.getCrowdPack().getCrowdName(), retryCount))
                .author(ApolloKeyConstants.getXxlJobConfigUsername())
                .executorParam(JSON.toJSONString(crowdXxlJobParam))
                .addTime(new Date())
                .executorHandler(XxlJobConstants.CROWD_DISPATCH)
                .build();
    }

    public void execStatus(CrowdStatusEnum crowdStatusEnum) {
        if (crowdStatusEnum == null) {
            execResult = CrowdExecResultEnum.EXECUTING.getCode();
            return;
        }
        switch (crowdStatusEnum) {
            case SUCCESS:
                execResult = CrowdExecResultEnum.SUCCESS.getCode();
                break;
            case FAILED:
                execResult = CrowdExecResultEnum.FAIL.getCode();
                break;
            default:
                execResult = CrowdExecResultEnum.EXECUTING.getCode();
                break;
        }
    }
}
