/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.domain.stat.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.base.database.Page;
import com.xftech.cdp.domain.stat.entity.StatStrategyGroupDataEntity;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version $ StatStrategyGroupDataRepository, v 0.1 2023/12/22 15:20 lingang.han Exp $
 */

@Component
public class StatStrategyGroupDataRepository {
    public StatStrategyGroupDataEntity selectById(Long id) {
        return DBUtil.selectOne("statStrategyGroupData.selectByPrimaryKey", id);
    }

    public boolean insert(StatStrategyGroupDataEntity entity) {
        return DBUtil.insert("statStrategyGroupData.insertSelective", entity) > 0;
    }

    public boolean updateById(StatStrategyGroupDataEntity entity) {
        return DBUtil.update("statStrategyGroupData.updateByPrimaryKeySelective", entity) > 0;
    }

    public boolean exitsRecord(StatStrategyGroupDataEntity strategyGroupData) {
        Integer num = DBUtil.selectOne("statStrategyGroupData.exitsRecord", strategyGroupData);
        return num > 0;
    }

    public boolean updateByRecord(StatStrategyGroupDataEntity strategyGroupData) {
        return DBUtil.update("statStrategyGroupData.updateByRecord", strategyGroupData) > 0;
    }

    public List<StatStrategyGroupDataEntity> selectByDateAndType(LocalDate time, List<Integer> typeList) {
        Map<String, Object> param = new HashMap<>();
        param.put("time", time);
        if (!CollectionUtils.isEmpty(typeList)) {
            param.put("typeList", typeList);
        }
        return DBUtil.selectList("statStrategyGroupData.selectByDateAndType", param);
    }

    public void batchUpdate(List<StatStrategyGroupDataEntity> list){
        DBUtil.updateBatchWithoutTx("statStrategyGroupData.updateByPrimaryKeySelective", list);
    }

    public Page<StatStrategyGroupDataEntity> selectPageByStrategyGroupId(String strategyGroupId, Integer marketChannel, String templateId, Integer pageNum, Integer pageSize) {
        Map<String, Object> param = new HashMap<>();
        param.put("strategyGroupId", strategyGroupId);
        if (Objects.nonNull(marketChannel)) {
            param.put("marketChannel", marketChannel);
        }
        if (StringUtils.isNotBlank(templateId)) {
            param.put("templateId", templateId);
        }
        return DBUtil.selectPage("statStrategyGroupData.selectPageByStrategyGroupId", param, pageNum, pageSize);
    }
}