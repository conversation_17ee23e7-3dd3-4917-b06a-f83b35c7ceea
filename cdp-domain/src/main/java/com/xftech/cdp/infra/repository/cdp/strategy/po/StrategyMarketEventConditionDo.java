package com.xftech.cdp.infra.repository.cdp.strategy.po;

import com.xftech.cdp.infra.repository.Do;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 策略营销事件实时判断条件
 *
 * @TableName strategy_market_event_condition
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class StrategyMarketEventConditionDo extends Do {
    private static final long serialVersionUID = 1L;

    /**
     * 策略ID
     */
    private Long strategyId;

    /**
     * 时间类型，1-分钟，2-小时，3-当天
     */
    private Integer timeType;

    /**
     * 时间值，当天时为0，即不需要这个值；
     */
    private Integer timeValue;

    /**
     * 标签英文名称
     */
    private String labelName;

    /**
     * 条件操作符，eq、ge...
     */
    private String operateType;

    /**
     * 条件值，多个值用逗号分隔
     */
    private String conditionValue;

    /**
     * 表达式
     */
    private String expression;

    /**
     * 关系，1-且 2-或
     */
    private Integer relationship;

    /**
     * 层级，1，2，3，n
     */
    private Integer level;

    /**
     * 上一级ID，如果没有上一级为-1
     */
    private Integer parentId;

    /**
     * 是否可选 1-可选择标签  2 默认排除项标签(不可选) 3排除项标签（可选）
     */
    private Integer optional;
}