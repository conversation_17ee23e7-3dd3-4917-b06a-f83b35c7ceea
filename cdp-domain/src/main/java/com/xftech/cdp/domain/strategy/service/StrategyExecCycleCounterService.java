/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.service;

import cn.hutool.core.thread.ThreadUtil;
import com.xftech.cdp.domain.strategy.repository.StrategyExecCycleCounterRepository;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyExecCycleCounterDo;
import com.xftech.cdp.infra.utils.RedisUtils;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 * @version $ StrategyExecCycleCounterService, v 0.1 2023/11/3 13:53 yye.xu Exp $
 */

@Service
@AllArgsConstructor
public class StrategyExecCycleCounterService {
    private RedisUtils redisUtils;
    private StrategyExecCycleCounterRepository strategyExecCycleCounterRepository;

    public void insert(StrategyExecCycleCounterDo strategyExecCycleCounterDo) {
        String lockKey = String.format("lockStrategyExecCycleCounter:%s", strategyExecCycleCounterDo.getStrategyId());
        for (; ; ) {
            boolean ret = redisUtils.lock(lockKey, "1", 200);
            if (ret) {
                StrategyExecCycleCounterDo cycleCounterDo = strategyExecCycleCounterRepository.selectStrategyCycleCounter(strategyExecCycleCounterDo.getStrategyId());
                if (cycleCounterDo == null) {
                    strategyExecCycleCounterRepository.insert(strategyExecCycleCounterDo);
                }
                redisUtils.unlock(lockKey);
                break;
            }
            ThreadUtil.sleep(10);
        }
    }

    public StrategyExecCycleCounterDo selectStrategyCycleCounter(Long strategyId, int dateValue) {
        return strategyExecCycleCounterRepository.selectStrategyCycleCounter(strategyId, dateValue);
    }

    public StrategyExecCycleCounterDo selectStrategyCycleCounter(Long strategyId) {
        return strategyExecCycleCounterRepository.selectStrategyCycleCounter(strategyId);
    }

    public int updateStrategyCycleCounter(Long strategyId, int dateValue) {
        return strategyExecCycleCounterRepository.updateStrategyCycleCounter(strategyId, dateValue);
    }
}