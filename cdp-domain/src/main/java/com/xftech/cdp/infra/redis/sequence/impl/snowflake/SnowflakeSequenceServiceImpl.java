package com.xftech.cdp.infra.redis.sequence.impl.snowflake;

import com.google.common.base.Preconditions;
import com.xftech.cdp.infra.config.Config;
import com.xftech.cdp.infra.redis.sequence.SequenceService;
import com.xftech.cdp.infra.utils.Utils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Optional;

@Service("snowFlake")
public class SnowflakeSequenceServiceImpl implements SequenceService {

    protected static final Logger LOGGER = LoggerFactory.getLogger(SnowflakeSequenceServiceImpl.class);
    private static final long DATA_CENTER_ID = 2L;
    private static final long WORKER_ID_BITS = 12L;
    private static final long DATA_CENTER_ID_BITS = 2L;
    private static final long MAX_WORKER_ID = ~(-1L << WORKER_ID_BITS);
    private static final long SEQUENCE_BITS = 12L;
    private static final long WORKER_ID_SHIFT = SEQUENCE_BITS;
    private static final long DATA_CENTER_ID_SHIFT = SEQUENCE_BITS + WORKER_ID_BITS;
    private static final long TIMESTAMP_LEFT_SHIFT = SEQUENCE_BITS + WORKER_ID_BITS + DATA_CENTER_ID_BITS;
    private static final long SEQUENCE_MASK = ~(-1L << SEQUENCE_BITS);
    private final long twepoch = 159764278200L;
    private long workerId;
    private long sequence = 0L;
    private long lastTimestamp = -1L;


    @Autowired
    private Config config;
    @Autowired
    private WorkerIdRegister workerIdRegister;


    @PostConstruct
    public void init() {

        Preconditions.checkArgument(timeGen() > twepoch, "Snowflake not support twepoch gt currentTime");
        final String ip = Utils.getIp();

        LOGGER.info("twepoch:{} ,ip:{}, port:{}", twepoch, ip, config.getPort());
        Optional<Integer> workerIdOp = workerIdRegister.registerWorker(ip, config.getPort(), config.getApplicationName());

        Preconditions.checkArgument(workerIdOp.isPresent(), "Snowflake Id Gen is not init ok");

        this.workerId = workerIdOp.get();
        LOGGER.info("start to use redis WorkerId:{} successfully", workerId);

        Preconditions.checkArgument(workerId >= 0 && workerId <= MAX_WORKER_ID, "workerID must between 0 and 4096");
    }


    @Override
    public synchronized Long getSequenceID(String type) {
        long timestamp = timeGen();

        if (timestamp < lastTimestamp) {
            LOGGER.warn(String.format("clock is moving backwards.  Rejecting requests until %d.", lastTimestamp));
            throw new RuntimeException(String.format("Clock moved backwards.  Refusing to generate id for %d milliseconds", lastTimestamp - timestamp));
        }

        if (lastTimestamp == timestamp) {
            sequence = (sequence + 1) & SEQUENCE_MASK;
            if (sequence == 0) {
                timestamp = tilNextMillis(lastTimestamp);
            }
        } else {
            sequence = 0L;
        }

        lastTimestamp = timestamp;

        return (long) (((timestamp - twepoch) << TIMESTAMP_LEFT_SHIFT) | (DATA_CENTER_ID << DATA_CENTER_ID_SHIFT) | (workerId << WORKER_ID_SHIFT) | sequence);
    }

    protected long tilNextMillis(long lastTimestamp) {
        long timestamp = timeGen();
        while (timestamp <= lastTimestamp) {
            timestamp = timeGen();
        }
        return timestamp;
    }

    protected long timeGen() {
        return System.currentTimeMillis() / 10;
    }
}
