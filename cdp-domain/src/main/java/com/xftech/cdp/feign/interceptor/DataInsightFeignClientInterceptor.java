/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.feign.interceptor;

import java.time.Instant;
import java.util.UUID;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DataInsightFeignClientInterceptor implements RequestInterceptor {
    @Override
    public void apply(RequestTemplate requestTemplate) {
        //设置请求头
        requestTemplate.header("requestId", UUID.randomUUID().toString());
        requestTemplate.header("appId", "xyf-cdp");
        requestTemplate.header("requestTimestamp", String.valueOf(Instant.now().toEpochMilli()));
    }

}