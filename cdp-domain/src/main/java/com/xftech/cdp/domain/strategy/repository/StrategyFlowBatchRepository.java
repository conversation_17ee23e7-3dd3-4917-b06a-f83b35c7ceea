/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyFlowBatchDo;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @version $ StrategyFlowBatchRepository, v 0.1 2023/12/21 15:59 yye.xu Exp $
 */

@Service
@AllArgsConstructor
public class StrategyFlowBatchRepository {
    public void insert(StrategyFlowBatchDo strategyFlowBatchDo) {
        if (strategyFlowBatchDo == null) {
            return;
        }
        DBUtil.insert("strategyFlowBatchDoMapper.insertSelective", strategyFlowBatchDo);
    }

    public StrategyFlowBatchDo selectByDateValue(String flowNo, Integer dateValue) {
        if (StringUtils.isEmpty(flowNo)) {
            return null;
        }
        Map<String, Object> param = new HashMap<>();
        param.put("flowNo", flowNo);
        param.put("dateValue", dateValue);
        return DBUtil.selectOne("strategyFlowBatchDoMapper.selectByDateValue", param);
    }

    public StrategyFlowBatchDo selectByBatchNo(String batchNo) {
        if (StringUtils.isEmpty(batchNo)) {
            return null;
        }
        Map<String, Object> param = new HashMap<>();
        param.put("batchNo", batchNo);
        return DBUtil.selectOne("strategyFlowBatchDoMapper.selectByBatchNo", param);
    }

    public List<StrategyFlowBatchDo> selectByFlowNo(String flowNo) {
        if (StringUtils.isBlank(flowNo)){
            return new ArrayList<>();
        }
        Map<String, Object> param = new HashMap<>();
        param.put("flowNo", flowNo);
        return DBUtil.selectList("strategyFlowBatchDoMapper.selectByFlowNo", param);
    }
}