package com.xftech.cdp.adapter.flowctrl;

import com.xftech.cdp.api.FlowCtrlApi;
import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.base.Response;
import com.xftech.cdp.api.dto.req.flowctrl.FlowCtrlCreateReq;
import com.xftech.cdp.api.dto.req.flowctrl.FlowCtrlDetailReq;
import com.xftech.cdp.api.dto.req.flowctrl.FlowCtrlEffectiveContentReq;
import com.xftech.cdp.api.dto.req.flowctrl.FlowCtrlListReq;
import com.xftech.cdp.api.dto.req.flowctrl.FlowCtrlOperateReq;
import com.xftech.cdp.api.dto.req.flowctrl.FlowCtrlUpdateReq;
import com.xftech.cdp.api.dto.resp.flowctrl.EffectiveContentListResp;
import com.xftech.cdp.api.dto.resp.flowctrl.FlowCtrlDetailResp;
import com.xftech.cdp.api.dto.resp.flowctrl.FlowCtrlListResp;
import com.xftech.cdp.domain.flowctrl.service.FlowCtrlService;
import com.xftech.cdp.infra.annotation.OperateLogAnnotation;
import com.xftech.cdp.infra.annotation.SysLog;
import com.xftech.cdp.infra.aviator.enums.OperateModeEnum;
import com.xftech.cdp.infra.aviator.enums.OperateTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @<NAME_EMAIL>
 */

@RestController
public class FlowCtrlController implements FlowCtrlApi {

    @Autowired
    FlowCtrlService flowCtrlService;

    @Override
    @SysLog("流控配置-创建规则")
    @OperateLogAnnotation(description = "新增流控规则", type = OperateTypeEnum.ADD, mode = OperateModeEnum.FLC)
    public Response<Boolean> create(FlowCtrlCreateReq flowCtrlCreateReq) {
        // 拦截单策略流控规则
        if(flowCtrlCreateReq != null && flowCtrlCreateReq.getType() == 1) {
            return Response.fail("单策略流控规则不允许通过此接口新增");
        }
        return Response.success(flowCtrlService.insert(flowCtrlCreateReq));
    }

    @Override
    @SysLog("流控配置-更新规则")
    @OperateLogAnnotation(description = "更新流控规则", type = OperateTypeEnum.UPDATE, mode = OperateModeEnum.FLC)
    public Response<Boolean> update(FlowCtrlUpdateReq flowCtrlUpdateReq) {
        return Response.success(flowCtrlService.update(flowCtrlUpdateReq));
    }

    @Override
    @SysLog("流控配置-获取规则列表")
    public Response<PageResultResponse<FlowCtrlListResp>> list(FlowCtrlListReq flowCtrlListReq) {
        return Response.success(flowCtrlService.list(flowCtrlListReq));
    }

    @Override
    @SysLog("流控配置-获取规则详情")
    public Response<FlowCtrlDetailResp> detail(FlowCtrlDetailReq flowCtrlDetailReq) {
        return Response.success(flowCtrlService.getDetail(flowCtrlDetailReq));
    }

    @Override
    @SysLog("流控配置-规则操作")
    @OperateLogAnnotation(description = "操作流控规则", type = OperateTypeEnum.OPERATE, mode = OperateModeEnum.FLC)
    public Response<Boolean> operate(FlowCtrlOperateReq flowCtrlOperateReq) {
        return Response.success(flowCtrlService.operate(flowCtrlOperateReq));
    }

    @Override
    @SysLog("流控配置-获取生效内容列表")
    public Response<List<EffectiveContentListResp>> getEffectiveContent(FlowCtrlEffectiveContentReq flowCtrlEffectiveContentReq) {
        return Response.success(flowCtrlService.getEffectiveContentList(flowCtrlEffectiveContentReq));
    }
}
