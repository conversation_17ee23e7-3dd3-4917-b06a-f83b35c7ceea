package com.xftech.cdp.api.dto.marketing.request;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0 2024/10/16
 * @description CheckUserEligibilityRequest
 */
@Data
@ApiModel("查询用户活动资格 请求体")
public class CheckUserEligibilityRequest {

    @ApiModelProperty(value = "活动ID")
    @NotNull(message = "活动信息不能为空")
    private Long activityId;

    @ApiModelProperty(value = "活动ID")
    @NotNull(message = "活动信息不能为空")
    private Long userId;

}
