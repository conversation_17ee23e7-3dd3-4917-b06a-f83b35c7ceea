package com.xftech.cdp.domain.dict.fetch;

import com.xftech.cdp.api.dto.resp.dict.Dict;
import com.xftech.cdp.domain.crowd.model.enums.CrowdFilterMethodEnum;
import com.xftech.cdp.domain.dict.model.enums.DictCode;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/2/18
 */
@Component
public class CrowdFilterMethodDictFetcher implements DictFetcher {
    @Override
    public List<Dict> getDict() {
        List<Dict> result = new ArrayList<>();
        List<CrowdFilterMethodEnum> enums = Arrays.stream(CrowdFilterMethodEnum.values()).collect(Collectors.toList());
        for (CrowdFilterMethodEnum value : enums) {
            result.add(Dict.builder().dictCode(String.valueOf(value.getCode())).dictValue(value.getDescription()).build());
        }
        return result;
    }

    @Override
    public DictCode getDictCode() {
        return DictCode.CROWD_FILTER_METHOD;
    }

    @Override
    public String getDescription() {
        return "筛选类型";
    }
}
