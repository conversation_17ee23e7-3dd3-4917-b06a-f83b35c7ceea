package com.xftech.cdp.domain.strategy.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @<NAME_EMAIL>
 */

@AllArgsConstructor
@Getter
public enum StrategyOperateEnum {
    PAUSE(0, "暂停"),
    ENABLE(1, "开始"),
    RETRY(2, "刷新"),
    PUBLISH(3, "发布"),
    DELETE(4,"删除"),
    RECOVER(5,"恢复");

    private final int code;
    private final String description;

    public static StrategyOperateEnum getInstance(Integer code) {
        for (StrategyOperateEnum strategyOperateEnum : StrategyOperateEnum.values()) {
            if (Objects.equals(strategyOperateEnum.getCode(), code)) {
                return strategyOperateEnum;
            }
        }
        return null;
    }
}
