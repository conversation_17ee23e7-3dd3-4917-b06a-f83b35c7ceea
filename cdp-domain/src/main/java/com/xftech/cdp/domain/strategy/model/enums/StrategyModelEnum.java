/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version $ StrategySortEnum, v 0.1 2024/3/27 11:04 lingang.han Exp $
 */

@Getter
@AllArgsConstructor
public enum StrategyModelEnum {

    OFF_ENGINE(1, "离线-引擎策略"),
    T0_ENGINE(2, "T0-引擎策略"),
    OFF(3, "离线策略"),
    T0(4, "T0策略"),
    ;
    private final Integer code;
    private final String desc;
}