package com.xftech.cdp.infra.repository.cdp.strategy.po;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class DecisionRecordDo implements Serializable {
    /**
     * tableName
     */
    private String tableName;

    /**
     * 自增id
     */
    private Long id;

    /**
     * app
     */
    private String app;

    /**
     * innerApp
     */
    private String innerApp;

    /**
     * os
     */
    private String os;

    /**
     * 手机号码, 删除废弃
     */
    private String mobile;

    /**
     * app_user_id
     */
    private Long appUserId;

    /**
     * 渠道来源
     */
    private String utmSource;

    /**
     * 事件英文名
     */
    private String eventName;

    /**
     * 注册时间
     */
    private LocalDateTime registerTime;

    /**
     * 上报时间
     */
    private LocalDateTime triggerDatetime;

    /**
     * 提额后可用额度
     */
    private BigDecimal amount;

    /**
     * 提额金额
     */
    private BigDecimal adjustAmount;

    /**
     * messageId
     */
    private String messageId;

    /**
     * 记录类型 1-事件 2-重试
     */
    private Integer recordType;

    /**
     * traceId
     */
    private String traceId;

    /**
     * 策略id
     */
    private Long strategyId;

    /**
     * 决策结果
     */
    private Integer decisionResult;

    /**
     * 失败原因码
     */
    private Integer failCode;

    /**
     * 失败原因描述
     */
    private String failReason;

    /**
     * List<HitResult>对象json串
     */
    private String decisionDetail;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 决策时间
     */
    private LocalDateTime decisionTime;

    /**
     * 当前渠道
     */
    private String currentUtmSource;

    /**
     * 来源类型
     */
    private String sourceType;

    private String engineDetail;
    /**
     * BizEventMessageVO.ExtrData 对应的JSON串
     */
    private String extrData;

    /**
     * 麻雀分组id
     */
    private String groupName;

    /**
     * 麻雀分组名
     */
    private Long groupId;

    private String unionId;

    private static final long serialVersionUID = 1L;
}