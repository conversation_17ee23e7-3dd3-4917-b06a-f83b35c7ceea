package com.xftech.cdp.domain.strategy.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.xftech.cdp.domain.cache.CacheStrategyMarketEventConditionService;
import com.xftech.cdp.domain.cache.CacheStrategyService;
import com.xftech.cdp.domain.crowd.model.enums.UtmTypeEnum;
import com.xftech.cdp.domain.crowd.service.UtmSourceService;
import com.xftech.cdp.domain.stat.entity.UtmResult;
import com.xftech.cdp.domain.strategy.repository.StrategyMarketEventConditionRepository;
import com.xftech.cdp.domain.strategy.service.StrategyMarketEventConditionService;
import com.xftech.cdp.infra.client.dingtalk.DingTalkUtil;
import com.xftech.cdp.infra.client.dingtalk.config.DingTalkConfig;
import com.xftech.cdp.infra.client.loanmarket.LoanMarketClient;
import com.xftech.cdp.infra.client.loanmarket.enums.UtmSourceTypeEnum;
import com.xftech.cdp.infra.client.usercenter.UserCenterClient;
import com.xftech.cdp.infra.client.usercenter.model.UserAbNumResp;
import com.xftech.cdp.infra.client.usercenter.model.UserIdDetailResp;
import com.xftech.cdp.infra.client.usercenter.model.UserInfoResp;
import com.xftech.cdp.infra.constant.Constants;
import com.xftech.cdp.infra.exception.BizException;
import com.xftech.cdp.infra.exception.InterfaceException;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventMessageVO;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventVO;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketEventConditionDo;
import com.xftech.cdp.infra.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @<NAME_EMAIL>
 */
@Slf4j
@Service
public class StrategyMarketEventConditionServiceImpl implements StrategyMarketEventConditionService {
    private static final Logger logger = LoggerFactory.getLogger(StrategyMarketEventConditionServiceImpl.class);

    @Autowired
    private StrategyMarketEventConditionRepository strategyMarketEventConditionRepository;
    @Autowired
    private CacheStrategyMarketEventConditionService cacheStrategyMarketEventConditionService;
    @Autowired
    private LoanMarketClient loanMarketClient;
    @Autowired
    private UserCenterClient userCenterClient;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private CacheStrategyService cacheStrategyService;
    @Autowired
    private DingTalkConfig dingTalkConfig;
    @Autowired
    private UtmSourceService utmSourceService;

    @Override
    public List<StrategyMarketEventConditionDo> getByStrategyId(Long strategyId) {
        return cacheStrategyMarketEventConditionService.getByStrategyId(strategyId);
    }


    public Map<String, List<StrategyMarketEventConditionDo>> getStringToListByStrategyId(Long strategyId) {
        return getByStrategyId(strategyId).stream()
                .collect(Collectors.groupingBy(StrategyMarketEventConditionDo::getLabelName));
    }

    @Override
    public Integer isMarketable(Long strategyId, String utmSourceStr, String labelName, LocalDateTime registerTime) {

        UtmSourceTypeEnum utmSourceTypeEnum = null;

        if (UtmSourceTypeEnum.REGISTER.getType().equals(UtmSourceTypeEnum.getType(labelName))) {
            // 注册渠道
            utmSourceTypeEnum = UtmSourceTypeEnum.REGISTER;
        } else if (UtmSourceTypeEnum.ENTRY.getType().equals(UtmSourceTypeEnum.getType(labelName))) {
            // 进件渠道
            utmSourceTypeEnum = UtmSourceTypeEnum.ENTRY;
        }
        if (utmSourceTypeEnum == null) {
            return Constants.UTM_LABEL_PASS;
        }
        List<UtmResult> utmResultList = null;
        try {
            utmResultList = utmSourceService.filterBatchUtmSource(utmSourceStr, utmSourceTypeEnum, registerTime, UtmTypeEnum.NEW_CUST.getName());
        } catch (InterfaceException ie) {
            UtmResult utmResult = new UtmResult();
            utmResult.setHit(false);
            utmResult.setNewMarketingType(ie.getMessage());
            utmResultList.add(utmResult);
            this.alarm(strategyId, utmSourceStr, UtmSourceTypeEnum.getDesc(labelName));
        }
        if (utmResultList.size() > 0) {
            boolean utmResultFlag = utmResultList.stream().allMatch(a -> a.getHit());
            if (utmResultFlag) {
                return Constants.UTM_LABEL_PASS;
            } else {
                return Constants.UTM_LABEL_FAIL;
            }
        } else {
            return Constants.UTM_LABEL_PASS;
        }

    }


    @Override
    public Integer isMarketableBatch(Collection<Long> strategyIdList, String utmSourceStr, String labelName, LocalDateTime registerTime) {

        UtmSourceTypeEnum utmSourceTypeEnum = null;

        if (UtmSourceTypeEnum.REGISTER.getType().equals(UtmSourceTypeEnum.getType(labelName))) {
            // 注册渠道
            utmSourceTypeEnum = UtmSourceTypeEnum.REGISTER;
        } else if (UtmSourceTypeEnum.ENTRY.getType().equals(UtmSourceTypeEnum.getType(labelName))) {
            // 进件渠道
            utmSourceTypeEnum = UtmSourceTypeEnum.ENTRY;
        }
        if (utmSourceTypeEnum == null) {
            return Constants.UTM_LABEL_PASS;
        }
        List<UtmResult> utmResultList = null;
        try {
            utmResultList = utmSourceService.filterBatchUtmSource(utmSourceStr, utmSourceTypeEnum, registerTime, UtmTypeEnum.NEW_CUST.getName());
        } catch (InterfaceException ie) {
            UtmResult utmResult = new UtmResult();
            utmResult.setHit(false);
            utmResult.setNewMarketingType(ie.getMessage());
            utmResultList.add(utmResult);
            this.alarmBatch(strategyIdList, utmSourceStr, UtmSourceTypeEnum.getDesc(labelName));
        }
        if (utmResultList.size() > 0) {
            boolean utmResultFlag = utmResultList.stream().allMatch(a -> a.getHit());
            if (utmResultFlag) {
                return Constants.UTM_LABEL_PASS;
            } else {
                return Constants.UTM_LABEL_FAIL;
            }
        } else {
            return Constants.UTM_LABEL_PASS;
        }

    }

    @Override
    public void paramComplete(BizEventVO bizEventVO, BizEventMessageVO bizEventMessageVO) {
        if (bizEventVO.getAppUserId() == null || bizEventVO.getRegisterTime() == null) {
            UserInfoResp userInfoResp = userCenterClient.getUserByMobile(bizEventVO.getMobile(), bizEventVO.getApp());
            if (bizEventVO.getAppUserId() == null) {
                bizEventVO.setAppUserId(userInfoResp.getCreditUserId());
            }
            if (StringUtils.isNotBlank(userInfoResp.getCreatedTime())) {
                bizEventVO.setRegisterTime(LocalDateTime.parse(userInfoResp.getCreatedTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            }
            if (bizEventVO.getAppUserIdLast2() == null) {
                // UID后两位
                bizEventVO.setAppUserIdLast2(Integer.valueOf(CharSequenceUtil.subSufByLength(String.valueOf(bizEventVO.getAppUserId()), 2)));
            }
            if (bizEventMessageVO.getCreditUserId() == null) {
                bizEventMessageVO.setCreditUserId(bizEventVO.getAppUserId());
            }
            if (bizEventMessageVO.getExtrData() == null && StringUtils.isNotBlank(userInfoResp.getCreatedTime())) {
                BizEventMessageVO.ExtrData extrData = new BizEventMessageVO.ExtrData();
                extrData.setCreatedTime(LocalDateTimeUtil.format(bizEventVO.getRegisterTime(), DatePattern.NORM_DATETIME_PATTERN));
                bizEventMessageVO.setExtrData(extrData);
            } else if (StringUtils.isBlank(bizEventMessageVO.getExtrData().getCreatedTime()) && StringUtils.isNotBlank(userInfoResp.getCreatedTime())) {
                bizEventMessageVO.getExtrData().setCreatedTime(LocalDateTimeUtil.format(bizEventVO.getRegisterTime(), DatePattern.NORM_DATETIME_PATTERN));
            }
        }
    }

    @Override
    public void userInfoComplete(BizEventVO bizEventVO, BizEventMessageVO bizEventMessageVO) {
        if (bizEventVO.getAppUserId() == null && (StringUtils.isBlank(bizEventVO.getMobile()) || StringUtils.isBlank(bizEventVO.getApp()))) {
            throw new BizException("appUserId and (mobile or app) cannot be empty at the same time");
        }
        if (bizEventVO.getAppUserId() == null) {
            paramComplete(bizEventVO, bizEventMessageVO);
        }
        if (StringUtils.isBlank(bizEventVO.getMobile())) {
            userCenterClient.getUsersById(bizEventVO.getAppUserId()).getUserList().stream().findFirst().map(UserIdDetailResp::getMobile).ifPresent(bizEventVO::setMobile);
        }
    }

    @Override
    public void abNumComplete(BizEventVO bizEventVO) {
        if (StringUtils.isBlank(bizEventVO.getAbNum())) {
            List<String> mobiles = new ArrayList<>();
            mobiles.add(bizEventVO.getMobile());
            UserAbNumResp userAbNumResp = userCenterClient.getAbNum(mobiles);
            if (!CollectionUtils.isEmpty(userAbNumResp.getAbNums())) {
                bizEventVO.setAbNum(userAbNumResp.getAbNums().get(0).getAbNum());
            }
        }
    }

//    private Integer blankUtmSourceMarketable(String labelName) {
//        // 注册渠道为空，返回不可营销
//        if (UtmSourceTypeEnum.REGISTER.getType().equals(UtmSourceTypeEnum.getType(labelName))) {
//            logger.info("注册渠道规则结果：不可营销，渠道为空");
//            return Constants.FAIL_STATUS;
//        }
//        // 进件渠道为空，返回可营销
//        if (UtmSourceTypeEnum.ENTRY.getType().equals(UtmSourceTypeEnum.getType(labelName))) {
//            logger.info("进件渠道规则结果：可营销，渠道为空");
//            return Constants.SUCCESS_STATUS;
//        }
//        return Constants.SUCCESS_STATUS;
//    }

//    private Integer marketable(Long strategyId, String utmSource, String labelName, LocalDateTime registerTime) {
//
//
//
//        List<UtmResult> utmResultList = utmSourceService.filterBatchUtmSource(utmSource, utmSourceTypeEnum, registerTime);
//        boolean utmResultFlag = utmResultList.stream().allMatch(a -> a.getHit());
//        if (utmResultFlag) {
//            return Constants.SUCCESS_STATUS;
//        } else {
//            return Constants.FAIL_STATUS;
//        }
//
//
//        if (StringUtils.isBlank(utmSource)) {
//            return this.blankUtmSourceMarketable(labelName);
//        }
//
//
//
//        String redisKey = String.format(RedisKeyConstants.UTM_SOURCE_INFO, utmSource);
//        GetUtmSourceResp getUtmSourceResp = redisUtils.get(redisKey, GetUtmSourceResp.class);
//        if (getUtmSourceResp == null) {
//            // 接口返回异常码，不可营销
//            GetUtmSourceAllResp allResp = loanMarketClient.getUtmSourceInfo(utmSource);
//            if (!allResp.isSuccess()) {
//                logger.info("渠道规则结果：不可营销，渠道：{}，标签：{}，接口返回异常码", utmSource, labelName);
//                this.alarm(strategyId, utmSource, UtmSourceTypeEnum.getDesc(labelName));
//                return Constants.FAIL_STATUS;
//            }
//            getUtmSourceResp = allResp.getData();
//            redisUtils.set(redisKey, getUtmSourceResp, RedisUtils.DEFAULT_EXPIRE_SECONDS * 30);
//            // 接口数据为空，可营销
//            if (getUtmSourceResp == null) {
//                logger.info("渠道规则结果：可营销，渠道：{}，标签：{}，接口数据为空", utmSource, labelName);
//                return Constants.SUCCESS_STATUS;
//            }
//        }
//        // 注册渠道
//        if (UtmSourceTypeEnum.REGISTER.getType().equals(UtmSourceTypeEnum.getType(labelName))) {
//            // 规则配置字段为空，可营销
//            if(StringUtils.isBlank(getUtmSourceResp.getNewMarketingType())) {
//                logger.info("注册渠道规则结果：可营销，渠道：{}，newMarketingType为空", utmSource);
//                return Constants.SUCCESS_STATUS;
//            }
//            // 注册渠道不可营销，判断注册时间
//            if (UtmSourceTypeEnum.getType(labelName).equals(getUtmSourceResp.getNewMarketingType())) {
//                logger.info("注册渠道规则结果：不可营销，渠道：{}，判断注册天数：{}", utmSource, getUtmSourceResp.getNewNoMarketingDays());
//                if (getUtmSourceResp.getNewNoMarketingDays() == null) {
//                    logger.info("注册渠道规则结果：不可营销，渠道：{}，newNoMarketingDays为空", utmSource);
//                    this.alarm(strategyId, utmSource, UtmSourceTypeEnum.getDesc(labelName));
//                    return Constants.FAIL_STATUS;
//                }
//                if (getUtmSourceResp.getNewNoMarketingDays() < 0) {
//                    logger.info("注册渠道规则结果：不可营销，渠道：{}，注册天数N小于0", utmSource);
//                    return Constants.FAIL_STATUS;
//                }
//                if (getUtmSourceResp.getNewNoMarketingDays() >= 0) {
//                    if (registerTime == null) {
//                        logger.info("注册渠道规则结果：不可营销，渠道：{}，注册时间为空", utmSource);
//                        return Constants.FAIL_STATUS;
//                    }
//                    if (registerTime.toLocalDate().plusDays(getUtmSourceResp.getNewNoMarketingDays()).isBefore(LocalDate.now())) {
//                        logger.info("注册渠道规则结果：可营销，渠道：{}，注册N天后", utmSource);
//                        return Constants.SUCCESS_STATUS;
//                    }
//                    logger.info("注册渠道规则结果：不可营销，渠道：{}，注册N天内", utmSource);
//                    return Constants.FAIL_STATUS;
//                }
//            }
//            logger.info("注册渠道规则结果：可营销，渠道：{}，规则类型：{}", utmSource, getUtmSourceResp.getNewMarketingType());
//            return Constants.SUCCESS_STATUS;
//        }
//        // 进件渠道
//        if (UtmSourceTypeEnum.ENTRY.getType().equals(UtmSourceTypeEnum.getType(labelName))) {
//            // 规则配置字段为空，可营销
//            if(StringUtils.isBlank(getUtmSourceResp.getNewMarketingType())) {
//                logger.info("进件渠道规则结果：可营销，渠道：{}，newMarketingType为空", utmSource);
//                return Constants.SUCCESS_STATUS;
//            }
//            // 进件渠道不可营销
//            if (UtmSourceTypeEnum.getType(labelName).equals(getUtmSourceResp.getNewMarketingType())) {
//                logger.info("进件渠道规则结果：不可营销，渠道：{}", utmSource);
//                return Constants.FAIL_STATUS;
//            }
//            logger.info("进件渠道规则结果：可营销，渠道：{}，规则类型：{}", utmSource, getUtmSourceResp.getNewMarketingType());
//            return Constants.SUCCESS_STATUS;
//        }
//        return Constants.SUCCESS_STATUS;
//    }

    /**
     * 告警
     */
    private void alarm(Long strategyId, String utmSource, String sourceType) {
        StrategyDo strategyDo = cacheStrategyService.selectById(strategyId);

//        StringBuilder content = new StringBuilder();
//        content.append(String.format("策略编号：%s", strategyDo.getId())).append("\n");
//        content.append(String.format("策略名称：%s", strategyDo.getName())).append("\n");
//        content.append(String.format("报警渠道：%s", sourceType)).append("\n");
//        content.append(String.format("渠道：%s", utmSource)).append("\n");
//        content.append("查询商户后台接口异常请立即排查");
//        DingTalkUtil.sendTextToDingTalk(dingTalkConfig.getAlarmUrl(), content.toString(), null, false);
        DingTalkUtil.dingTalk(dingTalkConfig, strategyDo, sourceType, utmSource);
    }

    private void alarmBatch(Collection<Long> strategyIdList, String utmSource, String sourceType) {
        List<StrategyDo> strategyDoList = new ArrayList<>(strategyIdList.size());
        for (Long strategyId : strategyIdList) {
            StrategyDo strategyDo = cacheStrategyService.selectById(strategyId);
            if (Objects.nonNull(strategyDo)) {
                strategyDoList.add(strategyDo);
            }
        }
        DingTalkUtil.dingTalkBatch(dingTalkConfig, strategyDoList, sourceType, utmSource);
    }
}
