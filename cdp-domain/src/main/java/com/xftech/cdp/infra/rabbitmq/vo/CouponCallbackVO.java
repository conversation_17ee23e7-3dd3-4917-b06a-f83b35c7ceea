package com.xftech.cdp.infra.rabbitmq.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * 优惠券明细回传VO
 * <AUTHOR>
 * @since 2023-04-17
 */
@Data
public class CouponCallbackVO {
    /**
     * type类型 固定值text-sms
     */
    private String type;
    /**
     * 回执报告
     */
    private Report report;

    @Data
    public static class Report {
        /**
         * 回执唯一标识
         */
        @JSONField(name = "agent_id")
        private String agentId;
        /**
         * 优惠券Id
         */
        @JSONField(name = "coupon_id")
        private String couponId;
        /**
         * 手机号
         */
        private String mobile;
        /**
         * credit_user.id
         */
        @JSONField(name = "user_id")
        private String userId;
        /**
         * 用户姓名
         */
        @JSONField(name = "name")
        private String name;
        /**
         * 最终状态，failed 发送失败，successed 发送成功, used 使用成功
         */
        @JSONField(name = "final_status")
        private String finalStatus;
        /**
         * 活动id
         */
        @JSONField(name = "activity_id")
        private String activityId;
        /**
         * 批次号
         */
        @JSONField(name = "batch_num")
        private String batchNum;
        /**
         * 批次号
         */
        @JSONField(name = "publish_time")
        private String publishTime;

        /**
         * 类型1：金融券（默认）， 2:消费券（新增）
         */
        @JSONField(name = "type")
        private Integer type;

        /**
         * 券类型 1:借款免息券 2:还款立减金 3:限时提额券, 4.拉卡拉聚合支付 ,5:x天免息券
         */
        @JSONField(name = "coupon_type")
        private Integer couponType;
    }
}
