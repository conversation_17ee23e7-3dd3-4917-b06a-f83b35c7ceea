package com.xftech.cdp.domain.event.model.config;

import com.xftech.cdp.domain.event.model.annotation.ProcessConfig;
import com.xftech.cdp.domain.event.model.enums.DataProcessEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数值计算处理器
 * <AUTHOR>
 * @version $ CalculateConfig, v 0.1 2024/11/13 13:46 snail Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ProcessConfig(processor = DataProcessEnum.CALCULATE)
public class CalculateConfig extends FieldConfig {
    /** 操作方式：+,-,*,/ */
    private String operate;
    /** 参与计算的数值 */
    private String joinValue;
    /** 数据类型：int,float,long,short等 */
    private String dataType;
}
