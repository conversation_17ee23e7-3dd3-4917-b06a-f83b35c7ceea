package com.xftech.cdp.infra.rocketmq.risk;

import com.xftech.cdp.infra.rocketmq.EventMessageBaseProcessor;
import com.xftech.cdp.infra.rocketmq.MessageHandlerSelector;
import com.xftech.cdp.infra.utils.WhitelistSwitchUtil;

import com.xinfei.xfframework.common.starter.mq.MqConsumerListener;
import groovy.util.logging.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version $ RiskSingleAdjSesameMsgConsumer, v 0.1 2025/2/25 14:20 tianshuo.qiu Exp $
 */
@Slf4j
@Component
@RocketMQMessageListener(consumerGroup = "cg_xyf_cdp_tg_risk_temp_amt", topic = "tp_rcspt_risk_amount_change_message", selectorExpression = "tg_risk_temp_amt", consumeMode = ConsumeMode.CONCURRENTLY)

public class NewRiskTempCreditMsgConsumer extends MqConsumerListener<String> {
    private static final Logger log = LoggerFactory.getLogger(NewRiskTempCreditMsgConsumer.class);
    @Autowired
    private MessageHandlerSelector handlerSelector;

    @Override
    protected void doMessage(String topic, String bodyMessage, MessageExt messageExt) {
        if (WhitelistSwitchUtil.boolSwitchByApollo("NewRiskTempCreditMsgConsumerEnable")) {
            log.info("NewRiskTempCreditMsgConsumer receive message topic={},messageId={},body={}", topic, messageExt.getMsgId(), bodyMessage);
            try {
                handlerSelector.doMessage(topic, "tg_risk_temp_amt", messageExt.getMsgId(), bodyMessage);
            } catch (Exception e) {
                log.error("NewRiskTempCreditMsgConsumer consumer error,topic={} messageId={},body={}", topic, messageExt.getMsgId(), bodyMessage, e);
            }
        }
    }
}
