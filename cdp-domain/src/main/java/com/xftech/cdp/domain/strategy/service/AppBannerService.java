/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.service;

import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.req.AppBannerReq;
import com.xftech.cdp.api.dto.req.AppBannerTemplateReq;
import com.xftech.cdp.api.dto.resp.AppBannerResp;
import com.xftech.cdp.api.dto.resp.AppBannerTemplateResp;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ AppBannerService, v 0.1 2024/4/18 17:51 benlin.wang Exp $
 */

public interface AppBannerService {
    PageResultResponse<AppBannerTemplateResp> getAppBannerList(AppBannerTemplateReq appBannerTemplateReq);

    List<AppBannerResp> recommendAppBannerList(AppBannerReq appBannerReq);
}