package com.xftech.cdp.infra.repository.cdp.dictionary.po;

import com.xftech.cdp.infra.repository.Do;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 字典表
 *
 * @TableName dictionary
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DictionaryDo extends Do {

    /**
     * 字典类型
     */
    private String dictType;

    /**
     * 字典类型名称
     */
    private String dictTypeName;

    /**
     * 字典编码
     */
    private String dictCode;

    /**
     * 字典值
     */
    private String dictValue;

    /**
     * 父类型
     */
    private Long parentId;

    /**
     * 排序
     */
    private Integer orderSeq;

    private static final long serialVersionUID = 1L;
}