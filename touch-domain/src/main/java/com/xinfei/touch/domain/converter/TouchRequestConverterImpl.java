package com.xinfei.touch.domain.converter;

import com.xinfei.touch.domain.model.TouchChannel;
import com.xinfei.touch.domain.model.TouchType;
import com.xinfei.touch.domain.model.unified.*;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 触达请求转换器实现类
 * 负责将现有的各种参数结构转换为统一的TouchRequest模型
 * 
 * 这个实现类使用具体的类型，用于实际的转换工作
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-23
 */
@Component
public class TouchRequestConverterImpl {
    
    /**
     * 转换T0普通触达参数（execSend方法）
     * 
     * 参数映射说明：
     * - DispatchDto dispatchDto -> 策略信息、业务信息、渠道配置
     * - CrowdDetailDo crowdDetail -> 用户信息
     * - StrategyMarketChannelEnum channelEnum -> 渠道类型
     * - StrategyMarketChannelDo channelDo -> 渠道配置
     * - BizEventVO bizEvent -> 业务事件信息
     */
    public TouchRequest convertFromExecSend(Object dispatchDto, 
                                          Object crowdDetail, 
                                          Object channelEnum, 
                                          Object channelDo, 
                                          Object bizEvent) {
        TouchRequest request = new TouchRequest();
        
        // ===== 基础信息 =====
        request.setRequestId(UUID.randomUUID().toString());
        request.setTouchType(TouchType.REALTIME_NORMAL);
        request.setTouchMode(TouchMode.SINGLE);
        request.setTimestamp(System.currentTimeMillis());
        
        // ===== 策略信息 (源于DispatchDto) =====
        setStrategyInfoFromDispatchDto(request, dispatchDto);
        
        // ===== 渠道信息 (源于StrategyMarketChannelEnum + StrategyMarketChannelDo) =====
        setChannelInfo(request, channelEnum, channelDo);
        
        // ===== 用户信息 (源于CrowdDetailDo) =====
        request.setUserInfo(convertCrowdDetailToTouchUserInfo(crowdDetail));
        
        // ===== 业务信息 (源于BizEventVO) =====
        setBizEventInfo(request, bizEvent);
        
        // ===== 触达配置 =====
        request.setTouchConfig(createTouchConfig(dispatchDto, channelDo));
        
        // ===== 流控配置 =====
        request.setFlowControlConfig(FlowControlConfig.createT0Config());
        
        return request;
    }
    
    /**
     * 转换T0引擎触达参数（marketingSend方法 - T0场景）
     * 
     * 参数映射说明：
     * - DispatchDto dispatchDto -> 策略信息、业务信息
     * - CrowdDetailDo crowdDetailDo -> 用户信息
     * - StrategyMarketChannelEnum channelEnum -> 渠道类型
     * - String groupName -> 引擎分组名称
     * - Map detailInfo -> 引擎详细信息
     * - BizEventVO bizEventVO -> 业务事件信息（包含引擎代码）
     */
    public TouchRequest convertFromT0MarketingSend(Object dispatchDto,
                                                 Object crowdDetailDo,
                                                 Object channelEnum,
                                                 String groupName,
                                                 Map<String, Object> detailInfo,
                                                 Object bizEventVO) {
        TouchRequest request = new TouchRequest();
        
        // ===== 基础信息 =====
        request.setRequestId(UUID.randomUUID().toString());
        request.setTouchType(TouchType.REALTIME_ENGINE);
        request.setTouchMode(TouchMode.SINGLE);
        request.setTimestamp(System.currentTimeMillis());
        
        // ===== 策略信息 (源于DispatchDto) =====
        setStrategyInfoFromDispatchDto(request, dispatchDto);
        
        // ===== 渠道信息 (源于StrategyMarketChannelEnum) =====
        setChannelInfo(request, channelEnum, null);
        
        // ===== 用户信息 (源于CrowdDetailDo) =====
        request.setUserInfo(convertCrowdDetailToTouchUserInfo(crowdDetailDo));
        
        // ===== 引擎信息 (源于groupName + detailInfo + BizEventVO) =====
        request.setEngineGroupName(groupName);
        request.setEngineDetail(detailInfo);
        
        // ===== 业务信息 (源于BizEventVO) =====
        if (bizEventVO != null) {
            setBizEventInfo(request, bizEventVO);
            setEngineCodeFromBizEvent(request, bizEventVO);
        }
        
        // ===== 触达配置 =====
        request.setTouchConfig(createTouchConfig(dispatchDto, null));
        
        // ===== 流控配置 =====
        request.setFlowControlConfig(FlowControlConfig.createT0EngineConfig());
        
        return request;
    }
    
    /**
     * 转换离线引擎触达参数（marketingSend方法 - 离线场景）
     * 
     * 参数映射说明：
     * - DispatchDto dispatchDto -> 策略信息、业务信息
     * - CrowdDetailDo crowdDetailDo -> 用户信息
     * - StrategyMarketChannelEnum channelEnum -> 渠道类型
     * - String groupName -> 引擎分组名称
     * - Map detailInfo -> 引擎详细信息
     * - BizEventVO bizEventVO -> null（离线场景）
     */
    public TouchRequest convertFromOfflineMarketingSend(Object dispatchDto,
                                                      Object crowdDetailDo,
                                                      Object channelEnum,
                                                      String groupName,
                                                      Map<String, Object> detailInfo) {
        TouchRequest request = new TouchRequest();
        
        // ===== 基础信息 =====
        request.setRequestId(UUID.randomUUID().toString());
        request.setTouchType(TouchType.OFFLINE_ENGINE);
        request.setTouchMode(TouchMode.SINGLE);
        request.setTimestamp(System.currentTimeMillis());
        
        // ===== 策略信息 (源于DispatchDto) =====
        setStrategyInfoFromDispatchDto(request, dispatchDto);
        
        // ===== 渠道信息 (源于StrategyMarketChannelEnum) =====
        setChannelInfo(request, channelEnum, null);
        
        // ===== 用户信息 (源于CrowdDetailDo) =====
        request.setUserInfo(convertCrowdDetailToTouchUserInfo(crowdDetailDo));
        
        // ===== 引擎信息 (源于groupName + detailInfo) =====
        request.setEngineGroupName(groupName);
        request.setEngineDetail(detailInfo);
        
        // ===== 触达配置 =====
        request.setTouchConfig(createTouchConfig(dispatchDto, null));
        
        // ===== 流控配置 =====
        request.setFlowControlConfig(FlowControlConfig.createOfflineEngineConfig());
        
        return request;
    }
    
    /**
     * 转换离线普通触达参数（dispatchHandler方法）
     * 
     * 参数映射说明：
     * - StrategyContext strategyContext -> 策略信息、流控规则
     * - String app -> 应用标识（用于用户信息补充）
     * - String innerApp -> 内部应用标识（用于用户信息补充）
     * - List<CrowdDetailDo> batch -> 用户批次信息
     * - List<T> templateParam -> 模板参数
     */
    public TouchRequest convertFromDispatchHandler(Object strategyContext,
                                                 String app,
                                                 String innerApp,
                                                 List<Object> userBatch,
                                                 List<Object> templateParams) {
        TouchRequest request = new TouchRequest();
        
        // ===== 基础信息 =====
        request.setRequestId(UUID.randomUUID().toString());
        request.setTouchType(TouchType.OFFLINE_NORMAL);
        request.setTouchMode(TouchMode.BATCH);
        request.setTimestamp(System.currentTimeMillis());
        
        // ===== 策略信息 (源于StrategyContext) =====
        setStrategyInfoFromContext(request, strategyContext);
        
        // ===== 用户信息列表 (源于batch) =====
        List<TouchUserInfo> touchUserList = userBatch.stream()
                .map(this::convertCrowdDetailToTouchUserInfo)
                .collect(Collectors.toList());
        request.setUserList(touchUserList);
        
        // ===== 批量处理信息 =====
        String batchNo = generateBatchNo();
        BatchInfo batchInfo = BatchInfo.create(batchNo, userBatch.size(), userBatch.size());
        setDetailTableNoFromContext(batchInfo, strategyContext);
        request.setBatchInfo(batchInfo);
        
        // ===== 模板参数 (源于templateParam) =====
        request.setTemplateParams(convertTemplateParams(templateParams));
        
        // ===== 触达配置 =====
        request.setTouchConfig(createTouchConfigFromContext(strategyContext));
        
        // ===== 流控配置 (源于StrategyContext.flowCtrlList) =====
        request.setFlowControlConfig(createFlowControlConfigFromContext(strategyContext));
        
        return request;
    }
    
    // ========== 私有辅助方法 ==========
    
    /**
     * 从DispatchDto设置策略信息
     */
    private void setStrategyInfoFromDispatchDto(TouchRequest request, Object dispatchDto) {
        // TODO: 实际实现时需要根据具体的DispatchDto类型进行字段提取
        // 这里提供字段映射的模板，实际使用时需要取消注释并调整
        
        /*
        if (dispatchDto instanceof DispatchDto) {
            DispatchDto dto = (DispatchDto) dispatchDto;
            request.setStrategyId(dto.getStrategyId());
            request.setStrategyExecId(dto.getStrategyExecId());
            request.setStrategyGroupId(dto.getStrategyGroupId());
            request.setStrategyGroupName(dto.getStrategyGroupName());
            request.setStrategyChannelId(dto.getStrategyChannelId());
            request.setStrategyChannelXxlJobId(dto.getStrategyChannelXxlJobId());
            request.setStrategyChannel(dto.getStrategyChannel());
            request.setStrategyExecLogId(dto.getStrategyExecLogId());
            request.setStrategyExecLogRetryId(dto.getStrategyExecLogRetryId());
            request.setDetailTableNo(dto.getDetailTableNo());
            request.setMessageId(dto.getMessageId());
            request.setTemplateId(dto.getStrategyMarketChannelTemplateId());
            request.setBizEventType(dto.getBizEventType());
            request.setEventParamMap(dto.getEventParamMap());
            request.setSignatureKey(dto.getSignatureKey());
            request.setActivityId(dto.getActivityId());
            request.setNameTypeId(dto.getNameTypeId());
            request.setDispatchType(dto.getDispatchType());
            request.setBizType(dto.getBizType());
            request.setIncreaseAmtParamDto(dto.getIncreaseAmtParamDto());
            request.setAiProntoChannelDto(dto.getAiProntoChannelDto());
        }
        */
        
        // 示例实现（需要根据实际情况调整）
        request.setStrategyId(1L); // 默认值
        request.setBizEventType("DefaultEvent"); // 默认值
    }
    
    /**
     * 从StrategyContext设置策略信息
     */
    private void setStrategyInfoFromContext(TouchRequest request, Object strategyContext) {
        // TODO: 实际实现时需要根据具体的StrategyContext类型进行字段提取
        
        /*
        if (strategyContext instanceof StrategyContext) {
            StrategyContext context = (StrategyContext) strategyContext;
            request.setStrategyId(context.getStrategyDo().getId());
            request.setStrategyGroupId(context.getStrategyGroupDo().getId());
            request.setStrategyGroupName(context.getStrategyGroupDo().getName());
            request.setStrategyChannelId(context.getStrategyMarketChannelDo().getId());
            request.setDetailTableNo(context.getDetailTableNo());
            // 设置渠道信息
            setChannelFromContext(request, context);
        }
        */
        
        // 示例实现
        request.setStrategyId(1L);
        request.setChannel(TouchChannel.SMS);
    }
    
    /**
     * 设置渠道信息
     */
    private void setChannelInfo(TouchRequest request, Object channelEnum, Object channelDo) {
        // TODO: 转换StrategyMarketChannelEnum到TouchChannel
        
        /*
        if (channelEnum instanceof StrategyMarketChannelEnum) {
            StrategyMarketChannelEnum channel = (StrategyMarketChannelEnum) channelEnum;
            request.setChannel(convertStrategyChannelToTouchChannel(channel));
        }
        
        if (channelDo instanceof StrategyMarketChannelDo) {
            StrategyMarketChannelDo channelConfig = (StrategyMarketChannelDo) channelDo;
            request.setChannelExtInfo(channelConfig.getExtInfo());
        }
        */
        
        // 示例实现
        request.setChannel(TouchChannel.SMS);
    }
    
    /**
     * 转换CrowdDetailDo到TouchUserInfo
     */
    private TouchUserInfo convertCrowdDetailToTouchUserInfo(Object crowdDetail) {
        TouchUserInfo userInfo = new TouchUserInfo();
        
        // TODO: 实际实现时需要根据具体的CrowdDetailDo类型进行字段提取
        
        /*
        if (crowdDetail instanceof CrowdDetailDo) {
            CrowdDetailDo crowd = (CrowdDetailDo) crowdDetail;
            userInfo.setUserId(crowd.getUserId());
            userInfo.setMobile(crowd.getMobile());
            userInfo.setApp(crowd.getApp());
            userInfo.setInnerApp(crowd.getInnerApp());
            userInfo.setDeviceId(crowd.getDeviceId());
            userInfo.setAbNum(crowd.getAbNum());
            userInfo.setAppUserIdLast2(crowd.getAppUserIdLast2());
            userInfo.setCrowdId(crowd.getCrowdId());
            userInfo.setCrowdExecLogId(crowd.getCrowdExecLogId());
            userInfo.setRegisterTime(crowd.getRegisterTime());
            userInfo.setTableName(crowd.getTableName());
            userInfo.setFlowNo(crowd.getFlowNo());
            userInfo.setPreStrategyId(crowd.getPreStrategyId());
            userInfo.setFlowBatchNo(crowd.getFlowBatchNo());
            userInfo.setNextStrategyId(crowd.getNextStrategyId());
            userInfo.setStrategyId(crowd.getStrategyId());
            userInfo.setOssRunVersion(crowd.getOssRunVersion());
        }
        */
        
        // 示例实现
        userInfo.setUserId(1L);
        userInfo.setMobile("***********");
        userInfo.setApp("default");
        
        return userInfo;
    }
    
    /**
     * 设置业务事件信息
     */
    private void setBizEventInfo(TouchRequest request, Object bizEvent) {
        // TODO: 从BizEventVO中提取业务事件信息
        
        /*
        if (bizEvent instanceof BizEventVO) {
            BizEventVO event = (BizEventVO) bizEvent;
            request.setBizEventType(event.getBizEventType());
            request.setBizEventData(convertBizEventToMap(event));
        }
        */
        
        // 示例实现
        request.setBizEventType("DefaultEvent");
        request.setBizEventData(new HashMap<>());
    }
    
    /**
     * 从BizEventVO设置引擎代码
     */
    private void setEngineCodeFromBizEvent(TouchRequest request, Object bizEventVO) {
        // TODO: 从BizEventVO中提取引擎代码
        
        /*
        if (bizEventVO instanceof BizEventVO) {
            BizEventVO event = (BizEventVO) bizEventVO;
            request.setEngineCode(event.getEngineCode());
        }
        */
        
        // 示例实现
        request.setEngineCode("DefaultEngine");
    }
    
    /**
     * 创建触达配置
     */
    private TouchConfig createTouchConfig(Object dispatchDto, Object channelDo) {
        TouchConfig config = TouchConfig.createDefault();
        
        // TODO: 根据dispatchDto和channelDo设置特定配置
        
        return config;
    }
    
    /**
     * 从StrategyContext创建触达配置
     */
    private TouchConfig createTouchConfigFromContext(Object strategyContext) {
        TouchConfig config = TouchConfig.createDefault();
        
        // TODO: 根据strategyContext设置特定配置
        
        return config;
    }
    
    /**
     * 从StrategyContext创建流控配置
     */
    private FlowControlConfig createFlowControlConfigFromContext(Object strategyContext) {
        FlowControlConfig config = FlowControlConfig.createOfflineBatchConfig();
        
        // TODO: 转换流控规则
        /*
        if (strategyContext instanceof StrategyContext) {
            StrategyContext context = (StrategyContext) strategyContext;
            if (context.getFlowCtrlList() != null && !context.getFlowCtrlList().isEmpty()) {
                // 转换流控规则列表
                // config.setRules(convertFlowCtrlRules(context.getFlowCtrlList()));
            }
        }
        */
        
        return config;
    }
    
    /**
     * 从StrategyContext设置明细表序号
     */
    private void setDetailTableNoFromContext(BatchInfo batchInfo, Object strategyContext) {
        // TODO: 从StrategyContext中提取detailTableNo
        
        /*
        if (strategyContext instanceof StrategyContext) {
            StrategyContext context = (StrategyContext) strategyContext;
            batchInfo.setDetailTableNo(context.getDetailTableNo());
        }
        */
    }
    
    /**
     * 转换模板参数
     */
    private Map<String, Object> convertTemplateParams(List<Object> templateParams) {
        Map<String, Object> params = new HashMap<>();
        if (templateParams != null) {
            for (int i = 0; i < templateParams.size(); i++) {
                params.put("param" + i, templateParams.get(i));
            }
        }
        return params;
    }
    
    /**
     * 生成批次号
     */
    private String generateBatchNo() {
        return "BATCH_" + System.currentTimeMillis() + "_" +
               UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }

    /**
     * 转换StrategyMarketChannelEnum到TouchChannel
     */
    private TouchChannel convertStrategyChannelToTouchChannel(Object channelEnum) {
        if (channelEnum == null) {
            return TouchChannel.SMS;
        }

        String channelName = channelEnum.toString();

        // 根据StrategyMarketChannelEnum的定义进行映射
        switch (channelName) {
            case "SMS":
                return TouchChannel.SMS;
            case "VOICE":
                return TouchChannel.VOICE;
            case "VOICE_NEW":
                return TouchChannel.VOICE_NEW;
            case "SALE_TICKET":
                return TouchChannel.COUPON;
            case "PUSH":
                return TouchChannel.PUSH;
            case "AI_PRONTO":
                return TouchChannel.AI_PRONTO;
            case "INCREASE_AMOUNT":
                return TouchChannel.INCREASE_AMOUNT;
            case "APP_BANNER":
                return TouchChannel.APP_BANNER;
            case "X_DAY_INTEREST_FREE":
                return TouchChannel.X_DAY_INTEREST_FREE;
            case "LIFE_RIGHTS":
                return TouchChannel.LIFE_RIGHTS;
            case "NONE":
                return TouchChannel.NONE;
            default:
                return TouchChannel.SMS; // 默认值
        }
    }
}
