/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.rocketmq;

import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSONObject;
import com.xftech.cdp.domain.strategy.service.dispatch.event.StrategyEventDispatchService;
import com.xftech.cdp.infra.aviator.util.DateTimeUtil;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventMessageVO;
import com.xftech.cdp.infra.rocketmq.dto.BizEventRocketMessageVO;
import com.xftech.cdp.infra.rocketmq.dto.OrderPayResultMsgDTO;
import com.xftech.cdp.infra.utils.RedisUtils;
import com.xinfei.xfframework.common.starter.mq.MqConsumerListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 *
 * <AUTHOR>
 * @version $ LifeRightsRocketMqConsumer, v 0.1 2024/4/30 16:29 lingang.han Exp $
 */

@Component
@Slf4j
@RocketMQMessageListener(consumerGroup = "cg_xyf_cdp_notify_tg_liferights", topic = "tp_xyf_cdp_notify", selectorExpression = "tg_liferights", consumeMode = ConsumeMode.CONCURRENTLY)
public class LifeRightsRocketMqConsumer extends MqConsumerListener<String> {

    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private StrategyEventDispatchService strategyEventDispatchService;

    @Override
    protected void doMessage(String s, String s1, MessageExt messageExt) {
        log.info("lifeRightsRocketMqConsumer receive message topic={},messageId={},body={}", s, messageExt.getMsgId(), s1);
        String lockKey = null;
        if (StringUtils.isNotBlank(messageExt.getMsgId())) {
            lockKey = SecureUtil.md5("cg:liferights:" + messageExt.getMsgId());
        }
        try {
            try {
                if (StringUtils.isNotBlank(lockKey) && !redisUtils.lock(lockKey, "0", 5, TimeUnit.MINUTES)) {
                    log.warn("lifeRightsRocketMqConsumer 重复消息,messageId={},body={}", messageExt.getMsgId(), s1);
                    return;
                }
            } catch (Exception e) {
                log.error("lifeRightsRocketMqConsumer,处理异常", e);
            }
            BizEventRocketMessageVO messageVO = JSONObject.parseObject(s1, BizEventRocketMessageVO.class);
            BizEventMessageVO bizEventMessageVO = transform(messageVO);
            bizEventMessageVO.setBizEventType("Notify_LifeRights");
            strategyEventDispatchService.prescreen(messageExt.getMsgId(), bizEventMessageVO);

        } catch (Exception e) {
            if (StringUtils.isNotEmpty(lockKey)) {
                redisUtils.unlock(lockKey);
            }
            log.error("lifeRightsRocketMqConsumer consumer error, messageid={},body={}", messageExt.getMsgId(), s1, e);
        }
    }

    private BizEventMessageVO transform(BizEventRocketMessageVO bizEventRocketMessageVO) {
        BizEventMessageVO bizEventMessageVO = new BizEventMessageVO();
        bizEventMessageVO.setAppUserId(bizEventRocketMessageVO.getAppUserId());
        bizEventMessageVO.setApp(bizEventRocketMessageVO.getApp());
        bizEventMessageVO.setInnerApp(bizEventRocketMessageVO.getInnerApp());
        bizEventMessageVO.setTriggerDatetime(DateTimeUtil.formatDateToStr(new Date(bizEventRocketMessageVO.getEventTime()), null));
        BizEventMessageVO.ExtrData extrData = new BizEventMessageVO.ExtrData();
        extrData.setLifeRightsCallBackStrategyId(bizEventRocketMessageVO.getExtraData().getLifeRightsCallBackStrategyId());
        extrData.setLifeRightCallBackStatus(bizEventRocketMessageVO.getExtraData().getLifeRightCallBackStatus());
        bizEventMessageVO.setExtrData(extrData);
        return bizEventMessageVO;
    }
}