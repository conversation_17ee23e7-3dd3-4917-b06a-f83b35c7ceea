package com.xftech.cdp.domain.strategy.model.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 */
public enum CouponStatusEnum {
    /**
     * 发送成功
     */
    SUCCESSED("successed", "发送成功", 1, 0),
    /**
     * 发送失败
     */
    FAILED("failed", "发送失败", 0, -1),
    /**
     * 使用成功
     */
    USED("used", "使用成功", 1, 1);

    private final String finalStatus;
    private final String description;
    private final Integer status;
    private final Integer usedStatus;

    CouponStatusEnum(String finalStatus, String description, Integer status, Integer usedStatus){
        this.finalStatus = finalStatus;
        this.description = description;
        this.status = status;
        this.usedStatus = usedStatus;
    }

    public Integer getStatus() {
        return status;
    }

    public Integer getUsedStatus() {
        return usedStatus;
    }

    public String getFinalStatus() {
        return finalStatus;
    }

    public String getDescription() {
        return description;
    }

    public static CouponStatusEnum getEnum(String finalStatus) {
        for (CouponStatusEnum couponStatusEnum : CouponStatusEnum.values()) {
            if (Objects.equals(couponStatusEnum.getFinalStatus(), finalStatus)) {
                return couponStatusEnum;
            }
        }
        return null;
    }
}