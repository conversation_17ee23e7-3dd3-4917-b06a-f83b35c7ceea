package com.xftech.cdp.api.dto.req.external;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */

@Getter
@AllArgsConstructor
public enum CallingSourceEnum {
    None(0, "null"),
    Overloan(1, "loanOverload"),
    ApiHold(2, "apiHold");

    public static CallingSourceEnum getByCode(Integer code) {
        for (CallingSourceEnum value : CallingSourceEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    private final Integer code;
    private final String label;
}
