package com.xftech.cdp.domain.strategy.model.enums;

import com.xftech.cdp.domain.strategy.exception.StrategyException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/2/10
 */

@Getter
@AllArgsConstructor
public enum StrategyStatusEnum {

    DRAFT(-1, "待发布"),

    INIT(0, "已发布"),

    EXECUTING(1, "执行中"),

    SUCCESS(2, "执行成功"),

    FAIL(3, "执行失败"),

    PAUSING(4, "暂停中"),

    ENDED(5, "已结束"),

    DEPRECATED(6, "已删除");

    private final int code;

    private final String description;

    public static StrategyStatusEnum getInstance(Integer code) {
        for (StrategyStatusEnum strategyStatusEnum : StrategyStatusEnum.values()) {
            if (Objects.equals(strategyStatusEnum.getCode(), code)) {
                return strategyStatusEnum;
            }
        }
        throw new StrategyException(String.format("策略状态异常，状态：%s", code));
    }

    public static List<Integer> getPausedCodes() {
        return Arrays.asList(DRAFT.getCode(), PAUSING.getCode(), ENDED.getCode(), DEPRECATED.getCode());
    }

    public static List<Integer> getActiveCodes() {
        return Arrays.asList(INIT.getCode(), EXECUTING.getCode(), SUCCESS.getCode(), FAIL.getCode());
    }

    public static List<Integer> getDistinctInnerAppCodes() {
        return Arrays.asList(DRAFT.getCode(), INIT.getCode(), EXECUTING.getCode());
    }

    public static List<Integer> getReportCodes() {
        return Arrays.asList(INIT.getCode(), EXECUTING.getCode(), SUCCESS.getCode(), FAIL.getCode(), ENDED.getCode());
    }
}