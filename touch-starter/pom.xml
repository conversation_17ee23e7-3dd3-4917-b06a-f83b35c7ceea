<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.xinfei.touch</groupId>
        <artifactId>xyf-touch-service</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>touch-starter</artifactId>
    <name>Touch Starter</name>
    <description>触达服务启动层 - 应用启动</description>

    <dependencies>
        <!-- API层依赖 -->
        <dependency>
            <groupId>com.xinfei.touch</groupId>
            <artifactId>touch-api</artifactId>
        </dependency>
        
        <!-- 基础设施层依赖 -->
        <dependency>
            <groupId>com.xinfei.touch</groupId>
            <artifactId>touch-infrastructure</artifactId>
        </dependency>

        <!-- Spring Boot Starter -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <!-- 测试依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <configuration>
                    <mainClass>com.xinfei.touch.TouchServiceApplication</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
