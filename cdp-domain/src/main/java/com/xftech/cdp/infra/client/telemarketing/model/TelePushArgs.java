/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.infra.client.telemarketing.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 *
 * <AUTHOR>
 * @version $ TelePushArgs, v 0.1 2023/10/17 20:17 wancheng.qu Exp $
 */
@Data
@Builder
@AllArgsConstructor
public class TelePushArgs {
    private String batchNumber;
    private List<UserData> data;
    private Integer policyId;
    private String policyType;
    private String strategyType; //策略实时类型，只能为offline/realtime
    private String traceId;
    private Long ts;
    private String ua;
    private String uuid;
    private String nameTypeId;
    private Long cdpStrategyId;


    public TelePushArgs() {
        ua = "xyf-cdp";
        ts = System.currentTimeMillis() / 1000;
        traceId = UUID.randomUUID().toString();
    }

    @Data
    @Builder
    public static class UserData {
        private String app;
        private Integer isWhite; //是否是留白用户，0:否，1：是
        private String mobile;
        private Long userId;
        private ExtData extData;
        public void addExtData(ExtData extData) {
            this.extData = extData;
        }
    }

    @Data
    @Builder
    public static class ExtData {
        private Integer decision_power;
    }

}