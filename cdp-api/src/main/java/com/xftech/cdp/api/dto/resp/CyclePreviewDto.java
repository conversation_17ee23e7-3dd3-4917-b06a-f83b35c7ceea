/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 * @version $ CyclePreviewDto, v 0.1 2023/10/31 15:10 yye.xu Exp $
 */

@Data
@Builder
@AllArgsConstructor
public class CyclePreviewDto {
    @ApiModelProperty(value = "序号", required = true)
    private Integer order;
    @ApiModelProperty(value = "有效区间", required = true)
    private Range range;

    @Data
    @Builder
    @AllArgsConstructor
    public static class Range {
        private String begin;
        private String end;
    }
}