package com.xftech.cdp.adapter.strategy;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.xftech.base.common.util.LogUtil;
import com.xftech.base.log.IUdpLogger;
import com.xftech.cdp.distribute.crowd.repository.CrowdInfoRepository;
import com.xftech.cdp.distribute.crowd.repository.CrowdInfoVersionRepository;
import com.xftech.cdp.distribute.crowd.repository.model.CrowdInfoDo;
import com.xftech.cdp.distribute.crowd.repository.model.CrowdInfoVersionDo;
import com.xftech.cdp.distribute.crowd.repository.model.CrowdSliceDo;
import com.xftech.cdp.domain.crowd.repository.CrowdExecLogRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdPackRepository;
import com.xftech.cdp.domain.crowd.service.CrowdPackService;
import com.xftech.cdp.domain.flowctrl.repository.FlowCtrlInterceptionLogRepository;
import com.xftech.cdp.domain.stat.entity.StatRealtimeStrategyFlowDataEntity;
import com.xftech.cdp.domain.stat.entity.StatRealtimeStrategyGroupDataEntity;
import com.xftech.cdp.domain.stat.entity.StatStrategyEngineFlowDataEntity;
import com.xftech.cdp.domain.stat.repository.StatRealtimeStrategyFlowDataRepository;
import com.xftech.cdp.domain.stat.repository.StatRealtimeStrategyGroupDataRepository;
import com.xftech.cdp.domain.stat.repository.StatStrategyEngineFlowDataRepository;
import com.xftech.cdp.domain.stat.service.StatOfflineStrategyFlowDataService;
import com.xftech.cdp.domain.stat.service.impl.StatStrategyFlowDataServiceImpl;
import com.xftech.cdp.domain.stat.service.impl.StatStrategyGroupDataServiceImpl;
import com.xftech.cdp.domain.strategy.exception.StrategyException;
import com.xftech.cdp.domain.strategy.model.enums.StrategyRulerEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyStatusEnum;
import com.xftech.cdp.domain.strategy.repository.*;
import com.xftech.cdp.infra.constant.XxlJobConstants;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPackDo;
import com.xftech.cdp.infra.repository.cdp.flowctrl.po.FlowCtrlGroupNum;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyGroupDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketChannelDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.UserDispatchGroupNum;
import com.xftech.cdp.infra.utils.RedisKeyUtils;
import com.xftech.cdp.infra.utils.RedisUtils;
import com.xftech.cdp.infra.utils.WhitelistSwitchUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 策略统计类任务
 */
@Component
public class StrategyStatJob {

    private static final IUdpLogger logger = LogUtil.getLogger(StrategyStatJob.class);

    private static final String YYYY_MM_DD = "yyyy-MM-dd";
    private static final String YYYYMMDD = "yyyyMMdd";

    @Autowired
    private StrategyRepository strategyRepository;
    @Autowired
    private DecisionRecordRepository decisionRecordRepository;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private FlowCtrlInterceptionLogRepository flowCtrlInterceptionLogRepository;
    @Autowired
    private UserDispatchDetailRepository userDispatchDetailRepository;
    @Autowired
    private StrategyMarketChannelRepository strategyMarketChannelRepository;
    @Autowired
    private StrategyGroupRepository strategyGroupRepository;
    @Autowired
    private StatRealtimeStrategyFlowDataRepository flowDataRepository;
    @Autowired
    private StatRealtimeStrategyGroupDataRepository groupDataRepository;
    @Autowired
    private UserBlankGroupDetailRepository userBlankGroupDetailRepository;
    @Autowired
    private StatOfflineStrategyFlowDataService statOfflineStrategyFlowDataService;
    @Autowired
    private StatStrategyEngineFlowDataRepository statStrategyEngineFlowDataRepository;
    @Autowired
    private StatStrategyGroupDataServiceImpl statStrategyGroupDataService;
    @Autowired
    private CrowdPackRepository crowdPackRepository;
    @Autowired
    private CrowdExecLogRepository crowdExecLogRepository;
    @Autowired
    private StrategyCrowdPackRepository strategyCrowdPackRepository;
    @Autowired
    private StatStrategyFlowDataServiceImpl statStrategyFlowDataService;
    @Autowired
    private CrowdInfoRepository crowdInfoRepository;
    @Autowired
    private CrowdInfoVersionRepository crowdInfoVersionRepository;


    //离线策略类型：1:离线，2:T0
    private final static Integer STRATEGY_TYPE_OFFLINE = 1;
    private final static Integer STRATEGY_TYPE_T0 = 2;

    /**
     * 同步离线策略的数据流
     *
     * @param param 执行参数
     * @return 执行结果
     */
    @XxlJob(XxlJobConstants.STAT_OFFLINE_STRATEGY_FLOW_DATA)
    public ReturnT<String> offlineStrategyFlowDataJob(String param) {
        try {
            LocalDate date = LocalDate.now();
            if (StringUtils.isNotBlank(param)) {
                date = LocalDateTimeUtil.parseDate(param, YYYY_MM_DD);
            }
            statOfflineStrategyFlowDataService.statOfflineStrategyFlowData(date);
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            logger.warn("offlineStrategyFlowDataJob execute error, e: ", e);
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }
    }

    /**
     * T+0实时策略流程数据统计
     *
     * @param param 执行参数
     * @return 执行结果
     */
    @XxlJob(XxlJobConstants.STAT_REALTIME_STRATEGY_FLOW_DATA)
    public ReturnT<String> realtimeStrategyFlowDataJob(String param) {
        XxlJobLogger.log("realtimeStrategyFlowDataJob start");
        // 业务日期
        LocalDateTime curDateTime = LocalDateTime.now();

        strategyFlowDataProcess(curDateTime.toLocalDate(), false);

        XxlJobLogger.log("realtimeStrategyFlowDataJob end");

        return ReturnT.SUCCESS;
    }

    /**
     * T+0实时策略流程数据统计(统计昨天的)
     *
     * @param param 执行参数
     * @return 执行结果
     */
    @XxlJob(XxlJobConstants.STAT_REALTIME_STRATEGY_FLOW_DATA_YESTERDAY)
    public ReturnT<String> realtimeStrategyYesterdayFlowDataJob(String param) {
        XxlJobLogger.log("realtimeStrategyYesterdayFlowDataJob start");
        // 业务日期
        LocalDateTime curDateTime = LocalDateTime.now();

        // 统计前一天的
        LocalDateTime preDateTime = curDateTime.minusDays(1);
        strategyFlowDataProcess(preDateTime.toLocalDate(), true);

        XxlJobLogger.log("realtimeStrategyYesterdayFlowDataJob end");

        return ReturnT.SUCCESS;
    }

    @Deprecated
    @XxlJob(XxlJobConstants.MANUAL_REALTIME_STRATEGY_FLOW_DATA)
    public ReturnT<String> manualRealtimeStrategyFlowDataJob(String param) {
        XxlJobLogger.log("manualRealtimeStrategyFlowDataJob start");

        if (StringUtils.isBlank(param)) {
            XxlJobLogger.log("manualRealtimeStrategyFlowDataJob param is null");
            return ReturnT.FAIL;
        }

        // 参数校验
        LocalDate startDate = null;
        boolean mysql = false;
        Long strategyId = null;
        try {
            String[] split = param.split(",");
            String startDateStr = split[0];
            startDate = LocalDateTimeUtil.parseDate(startDateStr, YYYY_MM_DD);
            if (split.length >= 2) {
                mysql = Boolean.parseBoolean(split[1]);
            }
            if (split.length >= 3) {
                strategyId = Long.parseLong(split[2]);
            }
        } catch (Exception e) {
            logger.warn("参数校验异常", e);
            XxlJobLogger.log("manualRealtimeStrategyFlowDataJob param valid error");
        }

        if (startDate != null && strategyId == null) {
            strategyFlowDataProcess(startDate, mysql);
        } else if (startDate != null && strategyId != null) {
            strategyFlowDataProcess(startDate, strategyId, mysql);
        }

        XxlJobLogger.log("manualRealtimeStrategyFlowDataJob end");

        return ReturnT.SUCCESS;
    }

    /**
     * 分组监控数据统计
     * @param param
     * @return
     */
    @XxlJob(XxlJobConstants.STAT_STRATEGY_GROUP_DATA_JOB)
    public ReturnT<String> statStrategyGroupDateJob(String param) {
        try {
            statStrategyGroupDataService.statStrategyGroupDataProcess();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            logger.warn("statStrategyGroupDateJob execute error, e: ", e);
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }
    }

    /**
     * 分组监控数据统计
     * 当天结束后把状态修改为已结束
     * @param param 执行参数
     * @return 执行结果
     */
    @XxlJob(XxlJobConstants.STAT_STRATEGY_GROUP_DATA_END_CURRENT_STATUS_JOB)
    public ReturnT<String> statStrategyGroupDateEndCurrentStatusJob(String param) {
        try {
            statStrategyGroupDataService.statStrategyGroupDateEndCurrentStatus();
        } catch (Exception e) {
            logger.warn("strategyEventRefreshExecLogStatus execute error, e: ", e);
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }

        return ReturnT.SUCCESS;
    }

    @XxlJob(XxlJobConstants.STAT_STRATEGY_GROUP_DATA_YESTERDAY_JOB)
    public ReturnT<String> statStrategyGroupDateYesterdayJob(String param) {
        try {
            statStrategyGroupDataService.reportYesterdayData();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            logger.warn("statStrategyGroupDateYesterdayJob execute error, e: ", e);
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }
    }

    /**
     * 画布报表统计任务
     * @param param
     * @return
     */
    @XxlJob(XxlJobConstants.STAT_STRATEGY_FLOW_DATA_JOB)
    public ReturnT<String> statStrategyFlowDataJob(String param) {
        try {
            statStrategyFlowDataService.statStrategyGroupDataProcess();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            logger.warn("statStrategyFlowDataJob execute error, e: ", e);
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }
    }

    private void strategyFlowDataProcess(LocalDate startDate, Long strategyId, boolean mysql) {
        XxlJobLogger.log("realtimeStrategyFlowDataJob begin startDate:{}, strategyId={}", startDate, strategyId);
        LocalDate endDate = startDate.plusDays(1);
        String startDateStr = LocalDateTimeUtil.format(startDate, YYYY_MM_DD);
        String endDateStr = LocalDateTimeUtil.format(endDate, YYYY_MM_DD);
        String tableNameNo = LocalDateTimeUtil.format(startDate, "yyyyMM");

        StrategyDo strategyDo = strategyRepository.selectById(strategyId);
        if (strategyDo != null && StrategyRulerEnum.EVENT.getCode() == strategyDo.getSendRuler() && strategyDo.getType() == 1){
            engineStrategyFlowStat(tableNameNo, strategyId, startDateStr, endDateStr, mysql);
        }
        if (strategyDo != null && StrategyRulerEnum.EVENT.getCode() == strategyDo.getSendRuler() && strategyDo.getType() == 0){
            strategyFlowStat(tableNameNo, strategyId, startDateStr, endDateStr, mysql);
        }

        XxlJobLogger.log("realtimeStrategyFlowDataJob end startDate:{}, strategyId={}", startDate, strategyId);
    }

    private void strategyFlowDataProcess(LocalDate startDate, boolean mysql) {
        XxlJobLogger.log("realtimeStrategyFlowDataJob begin startDate:{}", startDate);
        LocalDate endDate = startDate.plusDays(1);
        String startDateStr = LocalDateTimeUtil.format(startDate, YYYY_MM_DD);
        String endDateStr = LocalDateTimeUtil.format(endDate, YYYY_MM_DD);
        String tableNameNo = LocalDateTimeUtil.format(startDate, "yyyyMM");

        // 查询执行中的T0策略(T0+引擎版)
//        List<StrategyDo> strategyDoList = strategyRepository.getExecutingEventStrategy();
        List<Integer> statusList = Arrays.asList(StrategyStatusEnum.EXECUTING.getCode(),StrategyStatusEnum.SUCCESS.getCode());
        List<Integer> sendRulerList = Arrays.asList(StrategyRulerEnum.EVENT.getCode(), StrategyRulerEnum.CYCLE.getCode(),
                StrategyRulerEnum.ONCE.getCode());
        List<StrategyDo> strategyDoList = strategyRepository.getStrategyList(sendRulerList, statusList);
        // 循环策略ID， 统计每个策略的流程数据
        for (StrategyDo strategyDo : strategyDoList) {
            if (Objects.equals(strategyDo.getSendRuler(), StrategyRulerEnum.EVENT.getCode()) && Objects.equals(strategyDo.getStatus(), StrategyStatusEnum.EXECUTING.getCode())) {
                if (strategyDo.getType() == 0) { // T0
                    XxlJobLogger.log("realtimeStrategyFlowData start strategyId:{}", strategyDo.getId());
                    strategyFlowStat(tableNameNo, strategyDo.getId(), startDateStr, endDateStr, mysql);
                }
                if (strategyDo.getType() == 1) { // T0+引擎版
                    XxlJobLogger.log("realtimeEngineStrategyFlowData start strategyId:{}", strategyDo.getId());
                    engineStrategyFlowStat(tableNameNo, strategyDo.getId(), startDateStr, endDateStr, mysql);
                }
            }
            if ((Objects.equals(strategyDo.getSendRuler(), StrategyRulerEnum.CYCLE.getCode()) || Objects.equals(strategyDo.getSendRuler(), StrategyRulerEnum.ONCE.getCode()))
                    && Objects.equals(strategyDo.getStatus(), StrategyStatusEnum.SUCCESS.getCode())) {
                if (strategyDo.getType() == 1) {
                    XxlJobLogger.log("offEngineStrategyFlowData start strategyId:{}", strategyDo.getId());
                    try { // 离线策略
                        offEngineStrategyFlowStat(strategyDo.getId(), startDateStr);
                    } catch (Exception e) {
                        logger.error("offEngineStrategyFlowData fail strategyId:{}", strategyDo.getId(), e);
                    }
                }
            }
        }
        XxlJobLogger.log("realtimeStrategyFlowDataJob end startDate:{}", startDate);
    }


    private void engineStrategyFlowStat(String tableNameNo, Long strategyId, String startDate, String endDate, Boolean mysql) {
        startDate = startDate.replace("-", "");
        // 触发事件总人次(总次数)
        Integer eventSum = redisUtils.getToInteger(RedisKeyUtils.genStatDecnSum(startDate, strategyId));
        // 事件条件过滤总人次
        Integer filterEventSum = redisUtils.getToInteger(RedisKeyUtils.genStatDecnEventSum(startDate, strategyId));
        //分组后进入引擎人次
        Integer intoEngineSum = redisUtils.getToInteger(RedisKeyUtils.genIntoEngineSum(startDate, strategyId));
        //分组后进入引擎人数
        Integer intoEngineNum = redisUtils.pfCount(RedisKeyUtils.genIntoEngineNum(startDate, strategyId)).intValue();
        //决策结果为营销人次
        Integer marketSum = redisUtils.getToInteger(RedisKeyUtils.genMarketSum(startDate, strategyId));
        //决策结果为营销人数
        Integer marketNum = redisUtils.pfCount(RedisKeyUtils.genMarketNum(startDate, strategyId)).intValue();
        //排除标签过滤人次(决策结果为营销)
        Integer excludeMarketSum = redisUtils.getToInteger(RedisKeyUtils.genExcludeUserMarketSum(startDate, strategyId));
        //决策结果为不营销人次
        Integer noMarketSum = redisUtils.getToInteger(RedisKeyUtils.genNotMarketSum(startDate, strategyId));
        //决策结果为不营销人数
        Integer noMarketNum = redisUtils.pfCount(RedisKeyUtils.genNotMarketNum(startDate, strategyId)).intValue();
        //排除标签过滤人次(决策结果为不营销)
        Integer excludeNotMarketSum = redisUtils.getToInteger(RedisKeyUtils.genExcludeUserNotMarketSum(startDate, strategyId));
        //决策失败人次
        Integer decisionFailSum = redisUtils.getToInteger(RedisKeyUtils.genExecuteEngineFailSum(startDate, strategyId));
        //决策失败人数
        Integer decisionFailNum = redisUtils.pfCount(RedisKeyUtils.genExecuteEngineFailNum(startDate, strategyId)).intValue();

        //流控过滤人次
        Integer flowControlSum = redisUtils.getToInteger(RedisKeyUtils.genStatDecnFlowSum(startDate, strategyId));

        //应发用户数
        // 分组用户触达数据统计
        List<UserDispatchGroupNum> userDispatchGroupNumList = redisStatStrategyDispatchData(strategyId, startDate);
        Integer dispatchNum = userDispatchGroupNumList.stream().map(UserDispatchGroupNum::getUserNum).filter(userNum -> userNum != 0).reduce(0, Integer::sum);

        StatStrategyEngineFlowDataEntity engineFlowDataEntity = new StatStrategyEngineFlowDataEntity();
        engineFlowDataEntity.setBizDate(LocalDateTimeUtil.parseDate(startDate, YYYYMMDD));
        engineFlowDataEntity.setStrategyId(strategyId);
        engineFlowDataEntity.setType(STRATEGY_TYPE_T0);
        engineFlowDataEntity.setEventSum(eventSum);
        engineFlowDataEntity.setFilterEventSum(filterEventSum);
        engineFlowDataEntity.setEngineSum(intoEngineSum);
        engineFlowDataEntity.setEngineNum(intoEngineNum);
        engineFlowDataEntity.setMarketSum(marketSum);
        engineFlowDataEntity.setMarketNum(marketNum);
        engineFlowDataEntity.setExcludeMarketSum(excludeMarketSum);
        engineFlowDataEntity.setFlowControlSum(flowControlSum);
        engineFlowDataEntity.setDispatchNum(dispatchNum);
        engineFlowDataEntity.setNotMarketSum(noMarketSum);
        engineFlowDataEntity.setNotMarketNum(noMarketNum);
        engineFlowDataEntity.setExcludeNotMarketSum(excludeNotMarketSum);
        engineFlowDataEntity.setDecisionFailSum(decisionFailSum);
        engineFlowDataEntity.setDecisionFailNum(decisionFailNum);

        //按照策略id + 日期 维度，保存或更改
        if (statStrategyEngineFlowDataRepository.existStrategyFlowData(engineFlowDataEntity)) {
            statStrategyEngineFlowDataRepository.updateByDateAndStrategyId(engineFlowDataEntity);
        } else {
            statStrategyEngineFlowDataRepository.insert(engineFlowDataEntity);
        }
    }

    private void offEngineStrategyFlowStat(Long strategyId, String startDate) {
        startDate = startDate.replace("-", "");
        String todayDateStr = LocalDateTimeUtil.format(LocalDateTime.now(), YYYYMMDD);
        //人群包总数
        List<Long> crowdPackIds = strategyCrowdPackRepository.selectCrowdPackIdsByStrategyId(strategyId);
        LocalDateTime refreshTime = null;
        int crowdUserNumSum = 0;
        if(WhitelistSwitchUtil.commonGraySwitchByApollo("" + strategyId, "offEngineStrategyDispatchSwitch")) {
            // 改为判断洞察平台表
            for (Long crowdId : crowdPackIds) {
                // 判断导入类型人群包，不需要当日刷新
                CrowdInfoDo crowdInfoDo = crowdInfoRepository.selectByCrowdId(crowdId);
                if(crowdInfoDo == null) {
                    throw new StrategyException("策略人群包没有准备好" + crowdId);
                }

                CrowdInfoVersionDo crowdInfoVersionDo = null;
                if(crowdInfoDo.getCrowdCreateType() == 1) { // 规则创建
                    // 当天最新版本，且已分片，
                    // 如果人群包没有改动，重跑可复用已经生成的exelog，如果已经有新版，则生成新版任务，幂等会保证不会重复执行
                    crowdInfoVersionDo = crowdInfoVersionRepository.selectMaxCrowdVersion(crowdInfoDo.getCrowdId());

                } else if(crowdInfoDo.getCrowdCreateType() == 2) { // 文件上传
                    crowdInfoVersionDo = crowdInfoVersionRepository.selectMaxSliceVersion(crowdInfoDo.getCrowdId());
                }

                if(crowdInfoVersionDo != null) {
                    refreshTime = LocalDateTimeUtil.of(crowdInfoVersionDo.getUpdatedTime());
                    if(crowdInfoVersionDo.getCrowdSize() != null) {
                        crowdUserNumSum += crowdInfoVersionDo.getCrowdSize().intValue();
                    }
                }
            }
        } else {
            List<CrowdPackDo> crowdPackDos = crowdPackRepository.selectByIds(crowdPackIds.stream().map(x -> x + "").collect(Collectors.toList()).toArray(new String[crowdPackIds.size()]));

            if (crowdPackDos != null && crowdPackDos.size() > 0) {
                refreshTime = crowdPackDos.stream().max(Comparator.comparing(CrowdPackDo::getUpdatedTime)).get().getUpdatedTime();
                crowdUserNumSum = crowdPackDos.stream().mapToInt(CrowdPackDo::getCrowdPersonNum).sum();
            }
        }
        //分组后进入引擎人数
        Integer intoEngineSum = redisUtils.getToInteger(RedisKeyUtils.genIntoEngineSum(startDate, strategyId));
        //决策结果为营销人数
        Integer marketSum = redisUtils.pfCount(RedisKeyUtils.genMarketNum(startDate, strategyId)).intValue();
        //标签排除过滤人数
        Integer excludeLabelSum = redisUtils.getToInteger(RedisKeyUtils.genOffEngineLabelExcludeSum(startDate, strategyId));
        //流控过滤人数
        Integer flowControlSum = redisUtils.getToInteger(RedisKeyUtils.genStatDecnFlowSum(startDate, strategyId));
        //应发用户数
        Integer dispatchSum = redisUtils.pfCount(RedisKeyUtils.genOffEngineSendNum(startDate, strategyId)).intValue();
        //决策结果为不营销人数
        Integer noMarketSum = redisUtils.pfCount(RedisKeyUtils.genNotMarketNum(startDate, strategyId)).intValue();
        //决策失败人数
        Integer decisionFailSum = redisUtils.getToInteger(RedisKeyUtils.genExecuteEngineFailSum(startDate, strategyId));


        StatStrategyEngineFlowDataEntity engineFlowDataEntity = new StatStrategyEngineFlowDataEntity();
        engineFlowDataEntity.setBizDate(LocalDateTimeUtil.parseDate(startDate, YYYYMMDD));
        if (startDate.equals(todayDateStr)) { // 更新昨天的数据是
            engineFlowDataEntity.setCrowdRefreshTime(refreshTime);
            engineFlowDataEntity.setCrowdUserNum(crowdUserNumSum);
        }
        engineFlowDataEntity.setStrategyId(strategyId);
        engineFlowDataEntity.setType(STRATEGY_TYPE_OFFLINE);
        engineFlowDataEntity.setEngineSum(intoEngineSum);
        engineFlowDataEntity.setMarketSum(marketSum);
        engineFlowDataEntity.setExcludeMarketSum(excludeLabelSum);
        engineFlowDataEntity.setFlowControlSum(flowControlSum);
        engineFlowDataEntity.setDispatchNum(dispatchSum);
        engineFlowDataEntity.setNotMarketSum(noMarketSum);
        engineFlowDataEntity.setDecisionFailSum(decisionFailSum);

        //按照策略id + 日期 维度，保存或更改
        if (statStrategyEngineFlowDataRepository.existStrategyFlowData(engineFlowDataEntity)) {
            statStrategyEngineFlowDataRepository.updateByDateAndStrategyId(engineFlowDataEntity);
        } else {
            statStrategyEngineFlowDataRepository.insert(engineFlowDataEntity);
        }
    }


    private void strategyFlowStat(String tableNameNo, Long strategyId, String startDate, String endDate, Boolean mysql) {
        // 策略决策结果流程数据
        StatRealtimeStrategyFlowDataEntity entity = redisStatStrategyFlowData(strategyId, startDate);
        // if (mysql) {
        //     StatRealtimeStrategyFlowDataEntity mysqlEntity = statStrategyFlowData(tableNameNo, strategyId, startDate, endDate);
        //     if (mysqlEntity != null) {
        //         entity = mysqlEntity;
        //     }
        // }
        // 分组流控数据统计
        List<FlowCtrlGroupNum> flowCtrlGroupNumList = redisStatStrategyFlowControlData(strategyId, startDate);
        if (mysql) {
            List<FlowCtrlGroupNum> mysqlList = statStrategyFlowControlData(strategyId, startDate, endDate);
            if (CollectionUtils.isNotEmpty(mysqlList)) {
                flowCtrlGroupNumList = mysqlList;
            }
        }

        // 分组用户触达数据统计
        List<UserDispatchGroupNum> userDispatchGroupNumList = redisStatStrategyDispatchData(strategyId, startDate);
        if (mysql) {
            List<UserDispatchGroupNum> mysqlList = statStrategyDispatchData(tableNameNo, strategyId, startDate, endDate);
            if (CollectionUtils.isNotEmpty(mysqlList)) {
                userDispatchGroupNumList = mysqlList;
            }
        }
        // 分组留白数据统计
        List<UserDispatchGroupNum> userBlankGroupList = redisStatUserBlankGroupData(strategyId, startDate);
        if (mysql) {
            List<UserDispatchGroupNum> mysqlList = statUserBlankGroupData(tableNameNo, strategyId, startDate, endDate);
            if (CollectionUtils.isNotEmpty(mysqlList)) {
                userBlankGroupList = mysqlList;
            }
        }
        for (UserDispatchGroupNum userBlankGroupNum : userBlankGroupList) {
            UserDispatchGroupNum userDispatchGroup = getUserDispatchGroup(userDispatchGroupNumList, userBlankGroupNum.getStrategyGroupId());
            if (userDispatchGroup == null) {
                userDispatchGroupNumList.add(userBlankGroupNum);
            } else {
                userDispatchGroup.setUserNum(userDispatchGroup.getUserNum() + userBlankGroupNum.getUserNum());
            }
        }

        LocalDate bizDate = LocalDateTimeUtil.parseDate(startDate, YYYY_MM_DD);

        List<StatRealtimeStrategyGroupDataEntity> list = new ArrayList<>();
        if (flowCtrlGroupNumList != null && flowCtrlGroupNumList.size() > 0) {
            for (FlowCtrlGroupNum item : flowCtrlGroupNumList) {
                StatRealtimeStrategyGroupDataEntity groupDataEntity = new StatRealtimeStrategyGroupDataEntity();
                groupDataEntity.setStrategyId(strategyId);
                groupDataEntity.setBizDate(bizDate);
                groupDataEntity.setStrategyGroupId(item.getStrategyGroupId());
                groupDataEntity.setFlowControlNum(item.getUserNum());
                groupDataEntity.setGroupName(item.getGroupName());
                groupDataEntity.setDispatchNum(0);
                if (userDispatchGroupNumList != null && userDispatchGroupNumList.size() > 0) {
                    for (UserDispatchGroupNum dispatch : userDispatchGroupNumList) {
                        if (Objects.equals(dispatch.getStrategyGroupId(), item.getStrategyGroupId())) {
                            groupDataEntity.setDispatchNum(dispatch.getUserNum());
                        }
                    }
                }
                list.add(groupDataEntity);
            }
        }
        if (userDispatchGroupNumList != null && userDispatchGroupNumList.size() > 0) {
            for (UserDispatchGroupNum dispatch : userDispatchGroupNumList) {
                if (!contain(list, dispatch.getStrategyGroupId())) {
                    StatRealtimeStrategyGroupDataEntity groupDataEntity = new StatRealtimeStrategyGroupDataEntity();
                    groupDataEntity.setStrategyId(strategyId);
                    groupDataEntity.setBizDate(bizDate);
                    groupDataEntity.setStrategyGroupId(dispatch.getStrategyGroupId());
                    groupDataEntity.setFlowControlNum(0);
                    groupDataEntity.setDispatchNum(dispatch.getUserNum());
                    groupDataEntity.setGroupName(dispatch.getGroupName());
                    list.add(groupDataEntity);
                }
            }
        }

        Integer flowControlNum = 0;
        Integer dispatchNum = 0;
        for (StatRealtimeStrategyGroupDataEntity item : list) {
            flowControlNum += item.getFlowControlNum();
            dispatchNum += item.getDispatchNum();
        }

        entity.setFlowControlNum(flowControlNum);
        entity.setDispatchNum(dispatchNum);

        if (flowDataRepository.existRealtimeStrategyFlowData(entity)) {
            flowDataRepository.updateStatRealtimeStrategyFlowData(entity);
        } else {
            flowDataRepository.saveStatRealtimeStrategyFlowData(entity);
        }

        for (StatRealtimeStrategyGroupDataEntity item : list) {
            if (groupDataRepository.existRealtimeStrategyGroupData(item)) {
                groupDataRepository.updateStatRealtimeStrategyGroupData(item);
            } else {
                groupDataRepository.saveStatRealtimeStrategyGroupData(item);
            }
        }

    }

    private UserDispatchGroupNum getUserDispatchGroup(List<UserDispatchGroupNum> list, Long strategyGroupId) {
        for (UserDispatchGroupNum item : list) {
            if (item.getStrategyGroupId().equals(strategyGroupId)) {
                return item;
            }
        }
        return null;
    }

    private Boolean contain(List<StatRealtimeStrategyGroupDataEntity> list, Long strategyGroupId) {
        if (list == null || list.size() == 0) {
            return false;
        }
        for (StatRealtimeStrategyGroupDataEntity item : list) {
            if (item.getStrategyGroupId().equals(strategyGroupId)) {
                return true;
            }
        }
        return false;
    }

    private List<FlowCtrlGroupNum> statStrategyFlowControlData(Long strategyId, String startDate, String endDate) {
        try {
            List<StrategyGroupDo> groupDoList = strategyGroupRepository.selectListByStrategyId(strategyId);
            List<FlowCtrlGroupNum> groupNumList = new ArrayList<>();
            if (groupDoList != null) {
                for (StrategyGroupDo groupDo : groupDoList) {
                    FlowCtrlGroupNum flowCtrlGroupNum = new FlowCtrlGroupNum();
                    flowCtrlGroupNum.setStrategyGroupId(groupDo.getId());
                    flowCtrlGroupNum.setGroupName(groupDo.getName());
                    flowCtrlGroupNum.setUserNum(0);
                    List<StrategyMarketChannelDo> channelDoList = strategyMarketChannelRepository.selectByStrategyGroupId(groupDo.getId());
                    if (CollectionUtils.isNotEmpty(channelDoList)) {
                        List<Long> strategyChannelIds = channelDoList.stream().map(StrategyMarketChannelDo::getId).collect(Collectors.toList());
                        Integer dispatchUserNum = flowCtrlInterceptionLogRepository.countStrategyGroupFlowCtrl(strategyId, strategyChannelIds, startDate, endDate);
                        flowCtrlGroupNum.setUserNum(dispatchUserNum);
                    }
                    groupNumList.add(flowCtrlGroupNum);
                }
            }
            logger.info("flowCtrlGroupNumList={}", groupNumList);
            return groupNumList;
        } catch (Exception e) {
            logger.warn("statStrategyFlowControlData", e);
        }
        return null;
    }

    private List<FlowCtrlGroupNum> redisStatStrategyFlowControlData(Long strategyId, String bizDate) {
        String startDate = bizDate.replace("-", "");
        List<StrategyGroupDo> groupDoList = strategyGroupRepository.selectListByStrategyId(strategyId);
        List<FlowCtrlGroupNum> groupNumList = new ArrayList<>();
        if (groupDoList != null) {
            for (StrategyGroupDo groupDo : groupDoList) {
                String key = RedisKeyUtils.genStatDecnUserFlow(startDate, strategyId, groupDo.getId());
                Integer num = redisUtils.pfCount(key).intValue();
                FlowCtrlGroupNum flowCtrlGroupNum = new FlowCtrlGroupNum();
                flowCtrlGroupNum.setStrategyGroupId(groupDo.getId());
                flowCtrlGroupNum.setGroupName(groupDo.getName());
                flowCtrlGroupNum.setUserNum(num);
                groupNumList.add(flowCtrlGroupNum);
            }
        }
        logger.info("redisFlowCtrlGroupNumList={}", groupNumList);
        return groupNumList;
    }

    private List<UserDispatchGroupNum> statStrategyDispatchData(String tableNameNo, Long strategyId, String startDate, String endDate) {
        try {
            List<StrategyGroupDo> groupDoList = strategyGroupRepository.selectListByStrategyId(strategyId);
            List<UserDispatchGroupNum> groupNumList = new ArrayList<>();
            if (groupDoList != null) {
                for (StrategyGroupDo groupDo : groupDoList) {
                    UserDispatchGroupNum userDispatchGroupNum = new UserDispatchGroupNum();
                    userDispatchGroupNum.setStrategyGroupId(groupDo.getId());
                    userDispatchGroupNum.setGroupName(groupDo.getName());
                    userDispatchGroupNum.setUserNum(0);
                    List<StrategyMarketChannelDo> channelDoList = strategyMarketChannelRepository.selectByStrategyGroupId(groupDo.getId());
                    if (CollectionUtils.isNotEmpty(channelDoList)) {
                        List<Long> strategyChannelIds = channelDoList.stream().map(StrategyMarketChannelDo::getId).collect(Collectors.toList());
                        Integer dispatchUserNum = userDispatchDetailRepository.countStrategyGroupDispatchUserNum(tableNameNo, strategyId, strategyChannelIds, startDate, endDate);
                        userDispatchGroupNum.setUserNum(dispatchUserNum);
                    }
                    groupNumList.add(userDispatchGroupNum);
                }
            }
            logger.info("userDispatchGroupNumList={}", groupNumList);
            return groupNumList;
        } catch (Exception e) {
            logger.warn("statStrategyDispatchData", e);
        }
        return null;
    }

    private List<UserDispatchGroupNum> redisStatStrategyDispatchData(Long strategyId, String bizDate) {
        String startDate = bizDate.replace("-", "");
        List<StrategyGroupDo> groupDoList = strategyGroupRepository.selectListByStrategyId(strategyId);
        List<UserDispatchGroupNum> groupNumList = new ArrayList<>();
        if (groupDoList != null) {
            for (StrategyGroupDo groupDo : groupDoList) {
                String key = RedisKeyUtils.genStatDecnUserDisp(startDate, strategyId, groupDo.getId());
                Integer num = redisUtils.pfCount(key).intValue();
                UserDispatchGroupNum userDispatchGroupNum = new UserDispatchGroupNum();
                userDispatchGroupNum.setStrategyGroupId(groupDo.getId());
                userDispatchGroupNum.setGroupName(groupDo.getName());
                userDispatchGroupNum.setUserNum(num);
                groupNumList.add(userDispatchGroupNum);
            }
        }
        logger.info("redisUserDispatchGroupNumList={}", groupNumList);
        return groupNumList;
    }

    private List<UserDispatchGroupNum> statUserBlankGroupData(String tableNameNo, Long strategyId, String startDate, String endDate) {
        try {
            List<StrategyGroupDo> groupDoList = strategyGroupRepository.selectListByStrategyId(strategyId);
            List<UserDispatchGroupNum> groupNumList = new ArrayList<>();
            if (groupDoList != null) {
                for (StrategyGroupDo groupDo : groupDoList) {
                    UserDispatchGroupNum dispatchGroupNum = new UserDispatchGroupNum();
                    dispatchGroupNum.setStrategyGroupId(groupDo.getId());
                    dispatchGroupNum.setGroupName(groupDo.getName());
                    dispatchGroupNum.setUserNum(0);
                    List<StrategyMarketChannelDo> channelDoList = strategyMarketChannelRepository.selectByStrategyGroupId(groupDo.getId());
                    if (CollectionUtils.isNotEmpty(channelDoList)) {
                        List<Long> strategyChannelIds = channelDoList.stream().map(StrategyMarketChannelDo::getId).collect(Collectors.toList());
                        Integer dispatchUserNum = userBlankGroupDetailRepository.countUserBlankGroupData(tableNameNo, strategyId, strategyChannelIds, startDate, endDate);
                        dispatchGroupNum.setUserNum(dispatchUserNum);
                    }
                    groupNumList.add(dispatchGroupNum);
                }
            }
            logger.info("blankGroupNumList={}", groupNumList);
            return groupNumList;
        } catch (Exception e) {
            logger.warn("statUserBlankGroupData", e);
        }
        return null;
    }

    private List<UserDispatchGroupNum> redisStatUserBlankGroupData(Long strategyId, String bizDate) {
        String startDate = bizDate.replace("-", "");
        List<StrategyGroupDo> groupDoList = strategyGroupRepository.selectListByStrategyId(strategyId);
        List<UserDispatchGroupNum> groupNumList = new ArrayList<>();
        if (groupDoList != null) {
            for (StrategyGroupDo groupDo : groupDoList) {
                String key = RedisKeyUtils.genStatDecnUserBlank(startDate, strategyId, groupDo.getId());
                Integer num = Optional.ofNullable(redisUtils.pfCount(key)).orElse(0L).intValue();
                UserDispatchGroupNum dispatchGroupNum = new UserDispatchGroupNum();
                dispatchGroupNum.setStrategyGroupId(groupDo.getId());
                dispatchGroupNum.setGroupName(groupDo.getName());
                dispatchGroupNum.setUserNum(num);
                groupNumList.add(dispatchGroupNum);
            }
        }
        logger.info("redisBlankGroupNumList={}", groupNumList);
        return groupNumList;
    }


    /**
     * 根据strategyChannelId获取组名
     *
     * @param strategyChannelId
     * @return
     */
    private String queryGroupNameByStrategyChannelId(Long strategyChannelId) {
        String groupName = "";
        String key = RedisKeyUtils.genStrategyChannelIdGroupNameKey(strategyChannelId);
        groupName = redisUtils.get(key);
        if (StringUtils.isBlank(groupName)) {
            StrategyMarketChannelDo strategyMarketChannelDo = strategyMarketChannelRepository.selectById(strategyChannelId);
            if (strategyMarketChannelDo != null) {
                StrategyGroupDo strategyGroupDo = strategyGroupRepository.selectById(strategyMarketChannelDo.getStrategyGroupId());
                if (strategyGroupDo != null) {
                    groupName = strategyGroupDo.getName();
                }
            }
            if (StringUtils.isNotBlank(groupName)) {
                redisUtils.set(key, groupName, RedisUtils.DEFAULT_EXPIRE_HOUR);
            }
        }
        return groupName;
    }

    /**
     * 策略决策流程数据统计
     *
     * @param strategyId
     * @param bizDate
     */
    private StatRealtimeStrategyFlowDataEntity redisStatStrategyFlowData(Long strategyId, String bizDate) {
        String startDate = bizDate.replace("-", "");
        // 触发事件总次数
        String eventSumStr = redisUtils.get(RedisKeyUtils.genStatDecnSum(startDate, strategyId));
        int eventSum = 0;
        if (StringUtils.isNotBlank(eventSumStr)) {
            eventSum = Integer.parseInt(eventSumStr);
        }
        // 触发事件总用户数
        Integer userSum = redisUtils.pfCount(RedisKeyUtils.genStatDecnUser(startDate, strategyId)).intValue();
        // 事件条件过滤用户数
        Integer filterEventNum = redisUtils.pfCount(RedisKeyUtils.genStatDecnUserEvent(startDate, strategyId)).intValue();
        // 注册时间过滤用户数
        Integer filterRegTimNum = redisUtils.pfCount(RedisKeyUtils.genStatDecnUserRegtime(startDate, strategyId)).intValue();
        // 离线人群过滤用户数
        Integer filterCrowdNum = redisUtils.pfCount(RedisKeyUtils.genStatDecnUserCrow(startDate, strategyId)).intValue();
        // 实时标签过滤用户数
        Integer filterLabelNum = redisUtils.pfCount(RedisKeyUtils.genStatDecnUserLabel(startDate, strategyId)).intValue();
        // 实时排除项过滤用户数
        Integer filterExcludeNum = redisUtils.pfCount(RedisKeyUtils.genStatDecnUserExclude(startDate, strategyId)).intValue();
        // 复筛通过用户数
        Integer passNum = redisUtils.pfCount(RedisKeyUtils.genStatDecnUserPass(startDate, strategyId)).intValue();
        logger.info("strategyId={}, date={}, eventSum={}, userSum={}, filterEventNum={}, filterRegTimNum={}, filterCrowdNum={}, filterLabelNum={}, filterExcludeNum={}, passNum={}",
                eventSum, userSum, filterEventNum, filterRegTimNum, filterCrowdNum, filterLabelNum, filterExcludeNum, passNum);
        StatRealtimeStrategyFlowDataEntity entity = new StatRealtimeStrategyFlowDataEntity();
        entity.setStrategyId(strategyId);
        entity.setBizDate(LocalDateTimeUtil.parseDate(startDate, "yyyyMMdd"));
        entity.setEventSum(eventSum);
        entity.setUserSum(userSum);
        entity.setFilterEventNum(filterEventNum);
        entity.setFilterRegTimNum(filterRegTimNum);
        entity.setFilterCrowdNum(filterCrowdNum);
        entity.setFilterLabelNum(filterLabelNum);
        entity.setFilterExcludeNum(filterExcludeNum);
        entity.setPassNum(passNum);
        return entity;
    }

}
