package com.xftech.cdp.domain.cache.impl;


import com.github.benmanes.caffeine.cache.Cache;
import com.xftech.base.common.util.LogUtil;
import com.xftech.base.log.IUdpLogger;
import com.xftech.cdp.domain.cache.StrategyLocalCacheService;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * 策略表操作
 */
@Service
@Slf4j
public class StrategyCacheServiceImpl implements StrategyLocalCacheService {

    @Qualifier("strategyDoLocalCache")
    @Autowired
    private Cache<Long, StrategyDo> strategyDoLocalCache;

    /**
     * 根据策略ID获取策略信息
     * @param strategyId
     * @return
     */
    @Override
    public StrategyDo getStrategyDo(Long strategyId) {
        return strategyDoLocalCache.getIfPresent(strategyId);
    }

    @Override
    public void refreshStrategy(Long strategyId, StrategyDo strategyDo) {
        if(strategyDo == null) return;
        strategyDoLocalCache.put(strategyId, strategyDo);
    }
}
