/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.service.flow;

import cn.hutool.core.thread.ThreadUtil;
import com.dianping.cat.proxy.Tracer;
import com.xftech.cdp.domain.strategy.model.strategy.CrowdUserInfoBO;
import com.xftech.cdp.domain.strategy.repository.DispatchCrowdRepository;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.DispatchCrowdDo;
import com.xftech.cdp.infra.utils.JsonUtil;
import com.xftech.cdp.infra.utils.RedisUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * @version $ DispatchCrowdService, v 0.1 2023/12/29 14:03 yye.xu Exp $
 */

@Slf4j
@Service
@AllArgsConstructor
public class DispatchCrowdService {
    private RedisUtils redisUtils;
    private DispatchCrowdRepository dispatchCrowdRepository;

    public Integer selectCount(long strategyId, String flowNo,
                               int dateValue) {
        try {
            return dispatchCrowdRepository.selectCount(strategyId, flowNo, dateValue);
        } catch (Exception ex) {
            log.warn("DispatchCrowdService, flowNo={}, dateValue={} strategyId={}, ex={}", flowNo, dateValue, strategyId, ex, ex);
        }
        return 0;
    }

    public Integer selectDispatchCount(long strategyId, String batchNo,
                               int dateValue) {
        try {
            return dispatchCrowdRepository.selectDispatchCount(strategyId, batchNo, dateValue);
        } catch (Exception ex) {
            log.warn("DispatchCrowdService.selectDispatchCount, batchNo={}, dateValue={} strategyId={}, ex={}", batchNo, dateValue, strategyId, ex, ex);
        }
        return 0;
    }

    public List<DispatchCrowdDo> pullCrowds(long startIndex, long size, long strategyId, String flowNo, int dateValue) {
        return dispatchCrowdRepository.selectList(startIndex, size, strategyId, flowNo, dateValue);
    }

    @Async("dispatchCrowdExecutorWrapper")
    public void saveCrowdDetail(List<CrowdDetailDo> crowdDetailDos, int dateValue) {
        if (CollectionUtils.isEmpty(crowdDetailDos)) {
            return;
        }
        boolean lockRet = false;
        String flowNo = crowdDetailDos.get(0).getFlowNo();
        Long strategyId = crowdDetailDos.get(0).getStrategyId();
        try {
            lockRet = batchLock(flowNo, strategyId, dateValue);
            List<Long> userIdLList = crowdDetailDos.stream().map(CrowdDetailDo::getUserId)
                    .distinct().collect(Collectors.toList());
            Set<Long> userIds = dispatchCrowdRepository.selectUserList(flowNo, dateValue,
                            userIdLList, strategyId).stream().map(DispatchCrowdDo::getUserId)
                    .collect(Collectors.toSet());
            List<DispatchCrowdDo> dispatchCrowdList = new ArrayList<>();
            for (CrowdDetailDo crowdDetailDo : crowdDetailDos) {
                if (userIds.contains(crowdDetailDo.getUserId())) {
                    continue;
                }
                DispatchCrowdDo dispatchCrowdDo = new DispatchCrowdDo();
                dispatchCrowdDo.setUserId(crowdDetailDo.getUserId());
                dispatchCrowdDo.setFlowNo(crowdDetailDo.getFlowNo());
                dispatchCrowdDo.setBatchNo(crowdDetailDo.getFlowBatchNo());
                dispatchCrowdDo.setStrategyId(crowdDetailDo.getStrategyId());
                dispatchCrowdDo.setPreStrategyId(crowdDetailDo.getPreStrategyId());
                dispatchCrowdDo.setNextStrategyId(crowdDetailDo.getNextStrategyId());

                CrowdUserInfoBO userInfoBO = CrowdUserInfoBO.convert(crowdDetailDo);
                dispatchCrowdDo.setUserInfo(JsonUtil.toJson(userInfoBO));
                dispatchCrowdList.add(dispatchCrowdDo);
            }
            batchInsert(dispatchCrowdList, dateValue);
            log.info("saveCrowdDetail 保存数据,flowNo:{}, strategyId:{}, 本次数量:{}", flowNo, strategyId, dispatchCrowdList.size());
        } catch (Exception ex) {
            log.error("saveCrowdDetail failed, strategyId = {}", strategyId, ex);
        } finally {
            if (lockRet) {
                batchUnLock(flowNo, strategyId, dateValue);
            }
        }
    }

    public void batchInsert(List<DispatchCrowdDo> list, int dateValue) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        dispatchCrowdRepository.bacthInsert(list, dateValue);
    }

    // 批量锁
    private boolean batchLock(String flowNo, Long strategyId, int dateValue) {
        int expireSecs = 300;
        String lockKey = String.format("batchLock:dispatchCrowd:%s:%s:%s",
                flowNo, strategyId, dateValue);
        try {
            while (!redisUtils.lock(lockKey, "1", expireSecs)) {
                ThreadUtil.sleep(30);
                Tracer.logEvent("DispatchCrowdServiceLock", String.valueOf(strategyId));
            }
        }catch (Exception ex){
            log.error("saveCrowdDetail redis lock failed, strategyId={}", strategyId, ex);
        }
        return true;
    }

    private void batchUnLock(String flowNo, Long strategyId, int dateValue) {
        String lockKey = String.format("batchLock:dispatchCrowd:%s:%s:%s",
                flowNo, strategyId, dateValue);
        redisUtils.unlock(lockKey);
    }
}