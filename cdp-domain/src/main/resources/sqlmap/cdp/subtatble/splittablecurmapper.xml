<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="splitTableCur">
  <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.subtable.po.SplitTableCur">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="table_name" jdbcType="VARCHAR" property="tableName" />
    <result column="cur_table_no" jdbcType="INTEGER" property="curTableNo" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, `table_name`, cur_table_no, created_time, updated_time
  </sql>

  <select id="selectSplitTableCurById" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from split_table_cur
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectTableCurNo" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from split_table_cur
    where `table_name` = #{tableName}
    order by id desc
    limit 1
  </select>

  <update id="updateSplitTableCurNo">
    update split_table_cur set cur_table_no = #{curTableNo} where `table_name` = #{tableName}
  </update>

  <insert id="insertSplitTableCur" keyColumn="id" keyProperty="id" parameterType="com.xftech.cdp.infra.repository.subtable.po.SplitTableCur" useGeneratedKeys="true">
    insert into split_table_cur
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tableName != null">
        `table_name`,
      </if>
      <if test="curTableNo != null">
        cur_table_no,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="updatedTime != null">
        updated_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tableName != null">
        #{tableName,jdbcType=VARCHAR},
      </if>
      <if test="curTableNo != null">
        #{curTableNo,jdbcType=INTEGER},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        #{updatedTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <update id="updateSplitTableCurById" parameterType="com.xftech.cdp.infra.repository.subtable.po.SplitTableCur">
    update split_table_cur
    <set>
      <if test="tableName != null">
        `table_name` = #{tableName,jdbcType=VARCHAR},
      </if>
      <if test="curTableNo != null">
        cur_table_no = #{curTableNo,jdbcType=INTEGER},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>