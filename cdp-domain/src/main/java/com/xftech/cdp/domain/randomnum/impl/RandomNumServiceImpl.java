package com.xftech.cdp.domain.randomnum.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSON;
import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.req.random.NewRandomListReq;
import com.xftech.cdp.api.dto.resp.random.RandomListResp;
import com.xftech.cdp.distribute.offline.dto.StrategyExecuteContext;
import com.xftech.cdp.domain.crowd.exception.CrowdException;
import com.xftech.cdp.domain.crowd.model.dispatch.CrowdContext;
import com.xftech.cdp.domain.crowd.model.dispatch.CrowdWereHouse;
import com.xftech.cdp.domain.crowd.model.enums.CrowdLabelEnum;
import com.xftech.cdp.domain.crowd.model.label.crowd.DataSegment;
import com.xftech.cdp.domain.crowd.model.label.crowd.NewRandom;
import com.xftech.cdp.domain.randomnum.RandomNumService;
import com.xftech.cdp.domain.randomnum.model.RandomListReq;
import com.xftech.cdp.domain.strategy.exception.StrategyException;
import com.xftech.cdp.domain.strategy.model.dispatch.StrategyContext;
import com.xftech.cdp.domain.strategy.model.dto.UserDispatchFailDetailDto;
import com.xftech.cdp.domain.strategy.model.enums.BlankMethod;
import com.xftech.cdp.domain.strategy.model.enums.StrategyGroupTypeEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyRulerEnum;
import com.xftech.cdp.domain.strategy.service.UserDispatchFailDetailService;
import com.xftech.cdp.infra.client.dingtalk.config.DingTalkConfig;
import com.xftech.cdp.infra.client.randomnum.RandomNumClient;
import com.xftech.cdp.infra.client.randomnum.model.RandomList;
import com.xftech.cdp.infra.client.randomnum.model.RandomResp;
import com.xftech.cdp.infra.client.telemarketing.model.resp.TeleBizConfigResp;
import com.xftech.cdp.infra.client.telemarketing.model.resp.TeleNameConfigDetailResp;
import com.xftech.cdp.infra.exception.NoneException;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;
import com.xftech.cdp.infra.utils.JsonUtil;
import com.xftech.cdp.infra.utils.RedisKeyUtils;
import com.xftech.cdp.infra.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @<NAME_EMAIL>
 */
@Slf4j
@Service
public class RandomNumServiceImpl implements RandomNumService {

    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private DingTalkConfig dingTalkConfig;
    @Autowired
    private RandomNumClient randomNumClient;
    @Autowired
    private UserDispatchFailDetailService userDispatchFailDetailService;

    @Override
    public PageResultResponse<RandomListResp> getRandomList(NewRandomListReq newRandomListReq, Integer type) {
        RandomListReq randomListReq = new RandomListReq();
        randomListReq.setBizKey(newRandomListReq.getBizKey());
        randomListReq.setBizKeyAccurate(newRandomListReq.getBizKeyAccurate());
        randomListReq.setTestName(newRandomListReq.getTestName());
        randomListReq.setStatus(Objects.nonNull(type) ? null : 1);
        randomListReq.setBizNames(StringUtils.isBlank(newRandomListReq.getBizNames()) ? new ArrayList<>() : Collections.singletonList(newRandomListReq.getBizNames()));
        RandomResp randomResp = randomNumClient.getRandomList(randomListReq);

        if (!randomResp.isSuccess()) {
            throw new StrategyException("查询随机数列表异常");
        }
        RandomList randomList = randomResp.getData();
        List<RandomListResp> randomListRespList = randomResp.getData().getAbTests().stream().map(item -> {
            RandomListResp randomListResp = new RandomListResp();
            randomListResp.setTestName(item.getTestName());
            randomListResp.setBizKey(item.getBizKey());
            randomListResp.setBizName(item.getBizName());
            randomListResp.setNumLength(item.getNumLength());
            randomListResp.setRemark(item.getRemark());
            return randomListResp;
        }).collect(Collectors.toList());
        return new PageResultResponse<>(randomListRespList, newRandomListReq.getCurrent(), newRandomListReq.getSize(), randomList.getTotal());
    }


    /**
     * 获取随机数，失败记录入库
     *
     * @param context 初始化数据
     * @param list    当前批人群明细
     * @return 当前批人群明细
     */
    @Override
    public List<CrowdDetailDo> randomNum(StrategyContext context, List<CrowdDetailDo> list) {
        return this.randomNum(
                context.getStrategyDo(),
                context.getStrategyMarketChannelDo().getMarketChannel(),
                context.getStrategyMarketChannelDo().getTemplateId(),
                list
        );
    }

    /**
     * 获取用户随机数
     *
     * @param strategy   策略
     * @param channel    渠道类型
     * @param templateId 模板ID
     * @param list       当前批人群明细
     * @return 当前批人群明细
     */
    @Override
    public List<CrowdDetailDo> randomNum(StrategyDo strategy, Integer channel, String templateId, List<CrowdDetailDo> list) {
        if (StrategyGroupTypeEnum.getInstance(strategy.getAbType()) != StrategyGroupTypeEnum.NEW_RANDOM) {
            log.warn("非新随机数分组配置...");
            return list;
        }

        if (StringUtils.isBlank(strategy.getBizKey())) {
            throw new StrategyException("新随机数场景Key值为空");
        }

        List<CrowdDetailDo> failDetailList = new ArrayList<>();
        Set<Long> userIdSet = list.stream().map(CrowdDetailDo::getUserId).collect(Collectors.toSet());
        Map<Long, String> randomNumMap = randomNumClient.randomNumber(strategy.getBizKey(), new ArrayList<>(userIdSet), alarmFun(strategy, list));

        list.forEach(crowdDetailDo -> {
            String abNum = randomNumMap.get(crowdDetailDo.getUserId());
            if (StringUtils.isBlank(abNum)) {
                log.error("查询随机数是空，策略ID:{}, 用户ID:{}", strategy.getId(), crowdDetailDo.getUserId(), NoneException.catError());
                failDetailList.add(crowdDetailDo);
            }
            crowdDetailDo.setAbNum(abNum);
        });

        saveFailDetail(strategy.getId(), channel, templateId, failDetailList);
        return list;
    }

    /**
     * 策略随机数异常告警
     *
     * @param strategy 策略
     * @param list     人群集合
     * @return 告警函数
     */
    private Runnable alarmFun(StrategyDo strategy, List<CrowdDetailDo> list) {
        return () -> {
            List<String> atMobileList = new ArrayList<>(dingTalkConfig.atMobileList());
            atMobileList.add(strategy.getUpdatedOpMobile());
            switch (StrategyRulerEnum.getInstance(strategy.getSendRuler())) {
                case EVENT:
                    String msg = CharSequenceUtil.format("userid：{}，随机数调用失败", list.get(0).getUserId());
                    strategy.alarmDingTalk(dingTalkConfig.getAlarmUrl(), atMobileList, msg, null);
                    throw new StrategyException("随机数调用失败");
                case CYCLE_DAY:
                case ONCE:
                case CYCLE:
                    if (redisUtils.lock(RedisKeyUtils.genRandomNumAlarmKey(strategy.getId()), strategy.getId(), RedisUtils.DEFAULT_EXPIRE_HOUR)) {
                        strategy.alarmDingTalk(dingTalkConfig.getAlarmUrl(), atMobileList, "随机数调用失败", null);
                    }
                    break;
                default:
            }
        };
    }

    /**
     * 保存未获取到随机数的记录
     *
     * @param strategyId 策略ID
     * @param channel    渠道类型
     * @param templateId 模板ID
     * @param failList   失败集合
     */
    private void saveFailDetail(Long strategyId, Integer channel, String templateId, List<CrowdDetailDo> failList) {
        if (!CollectionUtils.isEmpty(failList)) {
            UserDispatchFailDetailDto dispatchFailDetail = new UserDispatchFailDetailDto();
            dispatchFailDetail.setStrategyId(strategyId);
            dispatchFailDetail.setMarketChannel(channel);
            dispatchFailDetail.setTemplateNo(templateId);
            dispatchFailDetail.setFailReason("随机数获取失败");
            dispatchFailDetail.setList(failList.stream().map(crowdDetailDo -> {
                UserDispatchFailDetailDto.UserInfo userInfo = new UserDispatchFailDetailDto.UserInfo();
                userInfo.setUserId(crowdDetailDo.getUserId());
                userInfo.setApp(crowdDetailDo.getApp());
                userInfo.setMobile(crowdDetailDo.getMobile());
                return userInfo;
            }).collect(Collectors.toList()));
            userDispatchFailDetailService.batchSave(dispatchFailDetail);
        }
    }

    @Override
    public void crowdFilterNewRandom(CrowdContext crowdContext, String labelValue, List<CrowdWereHouse> crowdWereHouseList, Integer labelGroupType) {
        log.info("filterNewRandom start");
        Iterator<CrowdWereHouse> iterator = crowdWereHouseList.iterator();
        NewRandom newRandom = JSON.parseObject(labelValue, NewRandom.class);
        DataSegment dataSegment = JSON.parseObject(newRandom.getCrowdLabelOption(), DataSegment.class);
        String bizKey = newRandom.getBizKey();

        Set<Long> userIdSet = crowdWereHouseList.stream().map(CrowdWereHouse::getAppUserId).collect(Collectors.toSet());
        Map<Long, String> randomNumMap = randomNumClient.randomNumber(bizKey, new ArrayList<>(userIdSet), () -> {
            crowdContext.setNewRandomfailed(true);
            throw new CrowdException("获取新随机数失败");
        });


        while (iterator.hasNext()) {
            CrowdWereHouse crowdWereHouse = iterator.next();
            String abNum = randomNumMap.get(crowdWereHouse.getAppUserId());
            boolean includeFlag = (labelGroupType == CrowdLabelEnum.INCLUDE_LABEL.getCode()) != range(abNum, dataSegment.getSegments());
            if (StringUtils.isBlank(abNum) || includeFlag) {
                iterator.remove();
            }
        }
    }

    @Override
    public void ossCrowdFilterNewRandom(CrowdContext crowdContext, String labelValue, List<CrowdDetailDo> crowdDetailList, Integer labelGroupType) {
        log.info("filterNewRandom start");
        Iterator<CrowdDetailDo> iterator = crowdDetailList.iterator();
        NewRandom newRandom = JSON.parseObject(labelValue, NewRandom.class);
        DataSegment dataSegment = JSON.parseObject(newRandom.getCrowdLabelOption(), DataSegment.class);
        String bizKey = newRandom.getBizKey();

        Set<Long> userIdSet = crowdDetailList.stream().map(CrowdDetailDo::getUserId).collect(Collectors.toSet());
        Map<Long, String> randomNumMap = randomNumClient.randomNumber(bizKey, new ArrayList<>(userIdSet), () -> {
            crowdContext.setNewRandomfailed(true);
            throw new CrowdException("获取新随机数失败");
        });


        while (iterator.hasNext()) {
            CrowdDetailDo crowdDetail = iterator.next();
            String abNum = randomNumMap.get(crowdDetail.getUserId());
            boolean includeFlag = (labelGroupType == CrowdLabelEnum.INCLUDE_LABEL.getCode()) != range(abNum, dataSegment.getSegments());
            if (StringUtils.isBlank(abNum) || includeFlag) {
                iterator.remove();
            }
        }
    }

    @Override
    public String getRandomItem(String bizKey) {
        NewRandomListReq randomListReq = new NewRandomListReq();
        randomListReq.setBizKeyAccurate(bizKey);
        List<RandomListResp> respList = getRandomList(randomListReq, 1).getRecords();
        return !CollectionUtils.isEmpty(respList) ? JSON.toJSONString(respList.get(0)) : null;

    }

    @Override
    public Integer getRandomIsWhite(TeleNameConfigDetailResp detail, Long userId, Set<Long> randomUser) {
        if (detail == null || detail.getIsBlank() == null || detail.getIsBlank() == 0) {
            return 0;
        }

        String blankMethod = detail.getBlankMethod();
        if (BlankMethod.RAND.getMethod().equals(blankMethod)) {
            return getRandomIsWhiteForRand(detail, userId);
        } else if (BlankMethod.RATE.getMethod().equals(blankMethod) && randomUser.contains(userId)) {
            return 1;
        }

        return 0;
    }

    public Long generateRandomNumber(String bizKey, Long userId, int numLength) {
        long digitCount = (long)(Math.pow(10, numLength));
        String newBizId = bizKey + String.format("%0" + (digitCount - bizKey.length()) + "d", 0) + userId.toString();
        MessageDigest digest = null;
        try {
            digest = MessageDigest.getInstance("SHA-256");
        } catch (NoSuchAlgorithmException e) {
            log.error("计算随机数出错, bizKey={}, userId={}, numLength={}, ", bizKey, userId, numLength);
            return null;
        }

        byte[] encodedHash = digest.digest(newBizId.getBytes(StandardCharsets.UTF_8));
        String encodedBizId = bytesToHex(encodedHash);
        encodedHash = digest.digest(encodedBizId.getBytes(StandardCharsets.UTF_8));
        encodedBizId = bytesToHex(encodedHash);

        BigInteger bigIntegerValue = new BigInteger(encodedBizId, 16);
        BigInteger base = new BigInteger(String.format("1%0" + numLength + "d", 0));
        BigInteger mod = bigIntegerValue.mod(base);
        return mod.longValue();
    }

    private static String bytesToHex(byte[] hash) {
        StringBuilder hexString = new StringBuilder(2 * hash.length);
        for (byte b : hash) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }

    private Integer getRandomIsWhiteForRand(TeleNameConfigDetailResp detail, Long userId) {
        String blankBizKey = detail.getBlankBizKey();
        if (StringUtils.isBlank(blankBizKey)) {
            throw new StrategyException("新随机数场景配置为空");
        }

        TeleBizConfigResp config = JsonUtil.parse(blankBizKey, TeleBizConfigResp.class);
        if (config == null) {
            throw new StrategyException("随机数配置解析失败");
        }

        Map<Long, String> longStringMap = randomNumClient.randomNumber(config.getTypeKey(), userId);
        String valueStr = longStringMap.get(userId);

        if (StringUtils.isNotBlank(valueStr)) {
            Integer value = Integer.valueOf(valueStr);
            if (value >= Integer.valueOf(config.getName()) && value <= Integer.valueOf(config.getValue())) {
                return 1;
            }
        }

        return 0;
    }

    private static boolean range(String value, List<DataSegment.Segment> segmentList) {
        if (StringUtils.isBlank(value)) {
            return false;
        }
        for (DataSegment.Segment segment : segmentList) {
            Long valueInt = Long.valueOf(value);
            if (segment.getMin() <= valueInt && valueInt <= segment.getMax()) {
                return true;
            }
        }
        return false;
    }

}
