/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.service;

import com.xftech.cdp.domain.strategy.repository.DispatchUserDelayRepository;
import com.xftech.cdp.infra.repository.cdp.strategy.po.DispatchUserDelayDo;
import io.swagger.models.auth.In;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ DispatchUserDelayService, v 0.1 2024/1/3 17:28 yye.xu Exp $
 */

@Service
@AllArgsConstructor
public class DispatchUserDelayService {
    private DispatchUserDelayRepository dispatchUserDelayRepository;

    public void batchInsert(List<DispatchUserDelayDo> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        dispatchUserDelayRepository.batchInsert(records);
    }

    public List<DispatchUserDelayDo> selectList(int startIndex, int pageSize, int status, int dateValue, Date at) {
        return dispatchUserDelayRepository.selectTodoList(startIndex, pageSize, status, dateValue, at);
    }

    public List<DispatchUserDelayDo> selectNotTelList(int startIndex, int pageSize, int status, int dateValue, Date at, List<Integer> telTypes) {
        return dispatchUserDelayRepository.selectNotTelTodoList(startIndex, pageSize, status, dateValue, at, telTypes);
    }

    public List<DispatchUserDelayDo> selectListByType(int startIndex, int pageSize, int status, int dateValue, Date at, Integer type) {
        return dispatchUserDelayRepository.selectTodoListByType(startIndex, pageSize, status, dateValue, at, type);
    }

    public List<DispatchUserDelayDo> selectTodoListByTypeSorted(int startIndex, int pageSize, int status, int dateValue, Date at, Integer type) {
        return dispatchUserDelayRepository.selectTodoListByTypeSorted(startIndex, pageSize, status, dateValue, at, type);
    }

    /**
     * 查询今日重新决策次数
     *
     * @param dateValue 日期
     * @param type 类型
     * @param strategyId 策略id
     * @param userNo 用户id
     * @return
     */
    public Integer selectTodayReDecisionCount(Integer dateValue, Integer type, Long strategyId, Long userNo) {
        return dispatchUserDelayRepository.selectTodayReDecisionCount(dateValue, type, strategyId, userNo);
    }

    public List<DispatchUserDelayDo> selectTelList(int startIndex, int pageSize, int status, int dateValue, Date at, List<Integer> telTypes) {
        return dispatchUserDelayRepository.selectTelTodoList(startIndex, pageSize, status, dateValue, at, telTypes);
    }

    public void updateStatusFinished(Long id, int status) {
        dispatchUserDelayRepository.updateFinishedStatus(id, status);
    }
}