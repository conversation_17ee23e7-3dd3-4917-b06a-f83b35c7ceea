package com.xftech.cdp.distribute.crowd.feign;

import java.util.List;

import com.xftech.cdp.distribute.crowd.feign.interceptor.CrowdFeignClientInterceptor;

import com.leo.datainsight.client.rr.api.request.ApiCrowdQueryListDetailRequest;
import com.leo.datainsight.client.rr.api.request.ApiCrowdQueryListRequest;
import com.leo.datainsight.client.rr.api.request.ApiListCrowdInfoRequest;
import com.leo.datainsight.client.rr.api.response.ApiCrowdQueryListResponse;
import com.leo.datainsight.client.rr.api.response.ApiListCrowdInfoResponse;
import com.leo.datainsight.client.rr.response.PageResponse;
import com.leo.datainsight.client.rr.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/*
 * 注意FeignClient注解需要配置三个参数
 * name 为应用名
 * path 为父路径
 * url  为sevice对应的域名 域名可以配置在配置中心中，格式"xf." + ${name} + ".url"
 *
 * 使用了信飞的xfframework，框架会自动生成Feign的配置信息。详情参考源码。
 * com.xinfei.xfframework.common.starter.feign.XfServiceInstance.createUri()
 *
 * 注意contextId表示此feignClient对应的配置id，推荐的格式是contextId=name+"-"+类名
 * feign.client.config.${name}.connectTimeout= *** 默认是10s
 * feign.client.config.${name}.readTimeout= **** 默认是60s
 * 默认连接池默认存活时间是900秒
 * 理论上对外暴露的所有接口都是post类型的，需要指定使用PostMapping注解
 */

/**
 * <AUTHOR>
 * @version v1.0 2025/4/28
 * @description CrowdFeignClient
 * @detail https://alidocs.dingtalk.com/i/nodes/QOG9lyrgJP3ADkjXc2eBDAoxVzN67Mw4?doc_type=wiki_doc
 */
@FeignClient(
        name = "datainsight",
        contextId = "datainsight.CrowdFeignClient",
        path = "/api/crowd",
        configuration = CrowdFeignClientInterceptor.class
)
public interface CrowdFeignClient {

    @PostMapping({"/list"})
    RestResponse<PageResponse<ApiCrowdQueryListResponse>> queryList(@RequestBody ApiCrowdQueryListRequest request);

    @PostMapping({"/list/detail"})
    RestResponse<List<ApiCrowdQueryListResponse>> queryListDetail(@RequestBody ApiCrowdQueryListDetailRequest request);

    @PostMapping({"/list/ossinfo"})
    RestResponse<List<ApiListCrowdInfoResponse>> queryListCrowdInfo(@RequestBody ApiListCrowdInfoRequest request);

}