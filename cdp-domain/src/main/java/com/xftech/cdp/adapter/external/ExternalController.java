package com.xftech.cdp.adapter.external;

import com.xftech.cdp.api.ExternalApi;
import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.base.Response;
import com.xftech.cdp.api.dto.req.AppBannerReq;
import com.xftech.cdp.api.dto.req.DecideReq;
import com.xftech.cdp.api.dto.req.external.*;
import com.xftech.cdp.api.dto.resp.AppBannerResp;
import com.xftech.cdp.api.dto.resp.DecideResp;
import com.xftech.cdp.api.dto.resp.external.DispatchRecordQueryResp;
import com.xftech.cdp.api.dto.resp.external.ExistDispatchRecordResp;
import com.xftech.cdp.api.dto.resp.external.StrategyListResp;
import com.xftech.cdp.domain.strategy.service.AppBannerService;
import com.xftech.cdp.domain.strategy.service.DecideService;
import com.xftech.cdp.domain.strategy.service.StrategyService;
import com.xftech.cdp.domain.strategy.service.UserDispatchDetailService;
import com.xftech.cdp.infra.annotation.ApiNoSigner;
import com.xftech.cdp.infra.annotation.ApiSigner;
import com.xftech.cdp.infra.annotation.SysLog;
import com.xftech.cdp.infra.client.dingtalk.DingTalkUtil;
import com.xftech.cdp.infra.client.dingtalk.config.DingTalkConfig;
import com.xftech.cdp.infra.config.AppConfigService;
import com.xftech.cdp.infra.constant.SysConstants;
import com.xftech.cdp.infra.exception.BizException;
import com.xftech.cdp.infra.utils.JsonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @<NAME_EMAIL>
 * @date 2023/4/4 9:50
 *
 * 对外接口一定要加@ApiSigner注解进行加签，token校验排除也是根据这个注解来判断的
 */
@Slf4j
@RestController
@AllArgsConstructor
public class ExternalController implements ExternalApi {

    @Autowired
    private DingTalkConfig dingTalkConfig;
    @Autowired
    private AppConfigService appConfigService;

    private final StrategyService strategyService;
    private final UserDispatchDetailService userDispatchDetailService;
    private final AppBannerService appBannerService;
    private final DecideService decideService;

//    public ExternalController(StrategyService strategyService, UserDispatchDetailService userDispatchDetailService, AppBannerService appBannerService) {
//        this.strategyService = strategyService;
//        this.userDispatchDetailService = userDispatchDetailService;
//        this.appBannerService = appBannerService;
//    }

    @Override
    @ApiSigner(ua = {SysConstants.SignerEnum.TELE_MARKETING, SysConstants.SignerEnum.TEST})
    @SysLog(value = "电销导入结果通知接口", extApi = true)
    public Response<Boolean> teleImportResult(ExternalBaseRequest<TeleImportResultReq> request) {
        return Response.success(userDispatchDetailService.teleImportResult(request.getArgs()));
    }

    @ApiSigner(ua = {SysConstants.SignerEnum.TEST, SysConstants.SignerEnum.PRDOCCORE})
    @SysLog(value = "用户是否存在下发记录接口", extApi = true)
    @Override
    public Response<DispatchRecordQueryResp> hasDispatchRecord(ExternalBaseRequest<DispatchRecordQueryReq> request) {
        DispatchRecordQueryResp resp = userDispatchDetailService.hasDispatchRecord(request.getArgs());
        return Response.success(resp);
    }

    @ApiSigner(ua = {SysConstants.SignerEnum.TEST, SysConstants.SignerEnum.XYF_APP_MANAGE, SysConstants.SignerEnum.PRDOCCORE})
    @SysLog(value = "策略列表接口", extApi = true)
    @Override
    public Response<PageResultResponse<StrategyListResp>> getStrategyList(ExternalBaseRequest<StrategyListReq> request) {
        PageResultResponse<StrategyListResp> resp = strategyService.getStrategyList(request.getArgs());
        return Response.success(resp);
    }

    @ApiSigner(ua = {SysConstants.SignerEnum.NONE})
    @SysLog(value = "提额风险回调通知", extApi = true)
    @Override
    public Response increaseAmtNotify(IncreaseAmtCallbakRequest request) {
        boolean ret = false;
        try {
            ret = userDispatchDetailService.increaseAmtNotify(request);
        }catch (Exception ex){
            log.error("提额风险回调通知处理失败, request:{}, ex:{}", JsonUtil.toJson(request), ex, ex);
        }
        log.info("提额风险回调通知响应, request:{}, ret:{}", JsonUtil.toJson(request), ret);
        return Response.success(ret);
    }

    @Override
    @ApiSigner(ua = {SysConstants.SignerEnum.TEST, SysConstants.SignerEnum.XYF_APP_API, SysConstants.SignerEnum.PSENGINECORE,
            SysConstants.SignerEnum.PRDOCCORE})
    @SysLog(value = "用户是否存在下发记录接口", extApi = true)
    public Response<ExistDispatchRecordResp> existDispatchRecord(ExternalBaseRequest<ExistDispatchRecordReq> request) {
        try {
            ExistDispatchRecordResp resp = userDispatchDetailService.existDispatchRecord(request.getArgs());
            return Response.success(resp);
        } catch (BizException e) {
            log.error("提额风险回调通知处理失败, request:{}, ex:{}", JsonUtil.toJson(request), e, e);
            return Response.fail(e.getMessage());
        } catch (Exception e) {
            log.error("提额风险回调通知处理失败, request:{}, ex:{}", JsonUtil.toJson(request), e, e);
            return Response.fail("查询处理失败");
        }
    }

    @Override
    //@ApiSigner(ua = {SysConstants.SignerEnum.TEST, SysConstants.SignerEnum.XYF_APP_API, SysConstants.SignerEnum.PSENGINECORE,
    //        SysConstants.SignerEnum.PRDOCCORE})
    @ApiNoSigner
    @SysLog(value = "app弹窗资源位", extApi = true)
    public Response<List<AppBannerResp>> recommendAppBannerList(@RequestBody ExternalBaseRequest<AppBannerReq> appBannerReq) {
        AppBannerReq args = appBannerReq.getArgs();
        if (args == null) {
            return Response.fail("args can't be null");
        }
        if (args.getUserId() == null) {
            return Response.fail("userId can't be null");
        }
        if (StringUtils.isBlank(args.getApp())) {
            return Response.fail("app can't be blank");
        }
        if (CollectionUtils.isEmpty(args.getAppBannerIdList())) {
            return Response.fail("appBannerIdList can't be empty");
        }

        Response<List<AppBannerResp>> response;
        try {
            Integer queryType = args.getQueryType();
            AppBannerReq.LoanRetentionRequest loanRetentionRequest = args.getLoanRetentionRequest();
            if (Objects.equals(queryType, 3) && (loanRetentionRequest == null || StringUtils.isBlank(loanRetentionRequest.getChoiceTitle()) || loanRetentionRequest.getInstallments() == null || loanRetentionRequest.getAmount() == null)) {
                response = Response.fail("借款挽留弹窗-参数错误");
                log.info("app弹窗资源位, request:{}, resp:{}", JsonUtil.toJson(appBannerReq), JsonUtil.toJson(response));
                return response;
            }
            AppBannerReq.SettlementRequest settlementRequest = args.getSettlementRequest();
            if (Objects.equals(queryType, 4) && (settlementRequest == null || StringUtils.isAnyBlank(settlementRequest.getCustNo(), settlementRequest.getOrderNo()))) {
                response = Response.fail("结清挽留弹窗-参数错误");
                log.info("app弹窗资源位, request:{}, resp:{}", JsonUtil.toJson(appBannerReq), JsonUtil.toJson(response));
                return response;
            }
            List<AppBannerResp> resp = appBannerService.recommendAppBannerList(args);
            response = Response.success(resp);
        } catch (Exception ex) {
            log.error("app弹窗资源位, 调用异常, request:{}", JsonUtil.toJson(appBannerReq), ex);
            response = Response.fail("服务错误");
        }
        log.info("app弹窗资源位, request:{}, resp:{}", JsonUtil.toJson(appBannerReq), JsonUtil.toJson(response));
        return response;
    }


    private final String ApiHoldMultiStrategyAlarmUrl = "https://oapi.dingtalk.com/robot/send?access_token=a22999edb972e5482e903e066d34cee2f976fc5564acbd88bf0864446d5e7985";

    @Override
    //@ApiSigner(ua = {SysConstants.SignerEnum.TEST, SysConstants.SignerEnum.XYF_APP_API, SysConstants.SignerEnum.PSENGINECORE,
    //        SysConstants.SignerEnum.PRDOCCORE})
    @SysLog(value = "decide决策", extApi = true)
    @ApiNoSigner
    public Response<List<DecideResp>> decide(@RequestBody ExternalBaseRequest<DecideReq> req) {
        Response<List<DecideResp>> response;

        Set<String> callingSourceSet = Arrays.stream(CallingSourceEnum.values())
                .map(CallingSourceEnum::getLabel)
                .collect(Collectors.toSet());
        DecideReq decideReq = req.getArgs();

        /*
        必填字段：
            callingSource
            userId
            app
            mobile
            businessType
         */

        if (Objects.isNull(decideReq.getCallingSource()) ||
                Objects.isNull(decideReq.getUserId()) || Objects.isNull(decideReq.getApp()) ||
                Objects.isNull(decideReq.getMobile()) || Objects.isNull(decideReq.getBusinessType())) {
            return Response.fail(DecideResp.CodeEnum.InvalidParam.getCode(), DecideResp.CodeEnum.InvalidParam.getMessage());
        }

        //callingSource必须是有效的枚举
        if (!callingSourceSet.contains(decideReq.getCallingSource().getLabel())) {
            return Response.fail(DecideResp.CodeEnum.InvalidParam.getCode(), DecideResp.CodeEnum.InvalidParam.getMessage());
        }

        //正常来说，callingSource不能为None
        if ( decideReq.getCallingSource().equals(CallingSourceEnum.None)) {
            return Response.fail(DecideResp.CodeEnum.InvalidParam.getCode(), "callingSource参数不正确");
        }

        if (decideReq.getCallingSource().equals(CallingSourceEnum.ApiHold)) {
            //ApiHold的请求，innerApp不能为空
            if (Objects.isNull(decideReq.getInnerApp())) {
                return Response.fail(DecideResp.CodeEnum.InvalidParam.getCode(), "innerApp为空");
            }
        } else if (decideReq.getCallingSource().equals(CallingSourceEnum.Overloan)) {
            //贷超策略，候选策略id集合不能为空
            if (CollectionUtils.isEmpty(decideReq.getStrategyIdList())) {
                return Response.fail(DecideResp.CodeEnum.InvalidParam.getCode(), "strategyIdList为空");
            }
        }

        try {

            List<DecideResp> decideRespList = decideService.decide(decideReq);
            if (decideReq.getCallingSource().equals(CallingSourceEnum.ApiHold)) {
                List<DecideResp> hitList = decideRespList.stream().filter(DecideResp::getIsHit).collect(Collectors.toList());
                long count = hitList.size();
                if (count > 1) {
                    String strategyIdStr = hitList.stream()
                            .map(decideResp -> decideResp.getStrategyId().toString())
                            .collect(Collectors.joining(","));
                    String content = "api进件hold单" + "策略配置异常告警:" + "innerApp:" + decideReq.getInnerApp() + ";" + "关联多个策略, 策略id:" + strategyIdStr;
                    DingTalkUtil.sendTextToDingTalk(appConfigService.getApiHoldDingTalkAlarmURL(), content,
                            dingTalkConfig.atMobileAdsList(), false);
                    return Response.fail(DecideResp.CodeEnum.HitMultiStrategy.getCode(), DecideResp.CodeEnum.HitMultiStrategy.getMessage());
                } else if (count == 0) {
                    return Response.fail(DecideResp.CodeEnum.NotHitStrategy.getCode(), DecideResp.CodeEnum.NotHitStrategy.getMessage());
                } else {
                    DecideResp decideResp = hitList.get(0);
                    if ( !decideResp.getIsHit()) {
                        return Response.fail(DecideResp.CodeEnum.HitStrategyNotExecute.getCode(),
                                DecideResp.CodeEnum.HitStrategyNotExecute.getMessage());
                    } else {
                        return Response.success(DecideResp.CodeEnum.Success.getMessage(), hitList);
                    }
                }
            }
            response = Response.success(decideRespList);
        } catch (Exception e) {
            log.error("[决策服务] 调用异常, request:{}", JsonUtil.toJson(req), e);
            response = Response.fail(DecideResp.CodeEnum.CdpInnerError.getCode(), e.getLocalizedMessage());
        }

        log.info("[决策服务] request:{}, resp:{}", JsonUtil.toJson(req), JsonUtil.toJson(response));

        return response;
    }
}
