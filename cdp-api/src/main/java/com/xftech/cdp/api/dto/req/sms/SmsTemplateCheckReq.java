package com.xftech.cdp.api.dto.req.sms;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;
import javax.validation.constraints.NotBlank;

/**
 * @<NAME_EMAIL>
 */

@Data
public class SmsTemplateCheckReq {
    @ApiModelProperty(value = "模板列表")
    private List<SingleTemplate> templateList;

    @Data
    public static class SingleTemplate {
        @NotBlank(message = "app不能为空")
        private String app;

        @NotBlank(message = "模板内容不能为空")
        private String template;
    }
}
