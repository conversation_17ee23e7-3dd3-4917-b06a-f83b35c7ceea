package com.xftech.cdp.domain.strategy.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.xftech.cdp.domain.dispatch.dto.DispatchDto;
import com.xftech.cdp.domain.strategy.model.enums.UserDispatchDetailStatusEnum;
import com.xftech.cdp.domain.strategy.repository.EventPushBatchRepository;
import com.xftech.cdp.domain.strategy.repository.UserDispatchDetailRepository;
import com.xftech.cdp.domain.strategy.service.EventPushBatchService;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.EventPushBatchDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.UserDispatchDetailDo;
import com.xftech.cdp.infra.utils.TransactionUtil;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;

/**
 * @<NAME_EMAIL>
 * @date 2023/5/23 13:56
 */
@Service
public class EventPushBatchServiceImpl implements EventPushBatchService {

    @Autowired
    private EventPushBatchRepository eventPushBatchRepository;
    @Autowired
    private UserDispatchDetailRepository userDispatchDetailRepository;

}
