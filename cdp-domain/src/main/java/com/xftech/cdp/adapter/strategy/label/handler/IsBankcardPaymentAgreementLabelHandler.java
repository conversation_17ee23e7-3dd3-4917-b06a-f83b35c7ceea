package com.xftech.cdp.adapter.strategy.label.handler;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xftech.cdp.adapter.strategy.label.LabelHandler;
import com.xftech.cdp.adapter.strategy.label.QueryLabelRequest;
import com.xftech.cdp.adapter.strategy.label.request.*;
import com.xftech.cdp.infra.client.ads.model.resp.AdsLabelResp;
import com.xftech.cdp.infra.client.usercenter.CisService;
import com.xftech.cdp.infra.client.usercenter.model.BaseCisResp;
import com.xftech.cdp.infra.client.usercenter.model.RegisterInfoByUserNo;
import com.xftech.cdp.infra.config.ApolloUtil;
import com.xftech.cdp.infra.config.AppConfigService;
import com.xftech.cdp.infra.constant.Constants;
import com.xftech.cdp.infra.exception.NoneException;
import com.xftech.cdp.infra.utils.DateUtil;
import com.xftech.cdp.infra.utils.JsonUtil;
import com.xyf.cis.SecureClient;
import com.xyf.cis.dto.SecureEncryptDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.*;

/**
 * <AUTHOR>
 * @version $ IsBankcardPaymentAgreementLabelHandler, v 0.1 2024/11/22 13:10 tianshuo.qiu Exp $
 */
@Slf4j
@Component
public class IsBankcardPaymentAgreementLabelHandler implements LabelHandler {
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private CisService cisService;
    @Autowired
    private AppConfigService appConfigService;
    @Autowired
    SecureClient secureClient;

    @Override
    public String getLabel() {
        return "default_card_is_bankcard_payment_agreement";
    }


    public static Map<String, Object> getHeader() {
        Map<String, Object> header = new HashMap<>();
        header.put("TRACE_ID", IdUtil.fastSimpleUUID());
        header.put("upstreamService", Constants.APP_NAME);
        header.put("requestTime", DateFormatUtils.format(new Date(),
                DateUtil.NOMAL_DATE_FORMAT));
        return header;
    }
    @Override
    public AdsLabelResp execute(String label, QueryLabelRequest batchAdsLabelVO) {
        List<AdsLabelResp.Param> result = new ArrayList<>();

        for (Long userNo : batchAdsLabelVO.getUserNoList()) {
                // Step 1: 获取所有银行卡记录
                BankCardListHistoryResponse bankCardListResp = getBankCardList(userNo, "CREDIT_LOAN");
                log.info("[default_card_is_bankcard_payment_agreement] 获取所有银行卡记录接口响应: {}", JsonUtil.toJson(bankCardListResp));

                // Step 2: 根据银行卡信息查询签约信息
                boolean hasAgreement = checkPaymentAgreement(bankCardListResp.getData());
                result.add(new AdsLabelResp.Param(userNo, null, String.valueOf(hasAgreement)));

        }

        return new AdsLabelResp(label, result);
    }

    private BankCardListHistoryResponse getBankCardList(Long userNo, String bizType) {
        HttpEntity httpEntity = new HttpEntity(getHeader());

        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(ApolloUtil.getAppProperty("bank-core.url", "http://qa1-bank-core.testxinfei.cn/multi/queryBankCardListHistoryByUserNo"));
        builder.queryParam("userNo", userNo);
        builder.queryParam("bizType", bizType);
        String uri = builder.toUriString();

        try {
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    uri,
                    HttpMethod.GET,
                    httpEntity,
                    String.class);
            BankCardListHistoryResponse resp = JsonUtil.parse(responseEntity.getBody(), BankCardListHistoryResponse.class);
            log.info("[queryRegisterInfoByUserNo], url:{}, userNo:{}, final-url:{}, request:{}, resp:{}", ApolloUtil.getAppProperty("bank-core.url", "http://qa1-bank-core.testxinfei.cn/multi/queryBankCardListHistoryByUserNo"), userNo, uri, JsonUtil.toJson(httpEntity),JsonUtil.toJson(resp));
            if (resp == null || !StringUtils.equalsIgnoreCase(resp.getCode(), "200")) {
                log.error("[queryRegisterInfoByUserNo] error, url:{}, userNo:{}, final-url:{}, resp:{}", ApolloUtil.getAppProperty("bank-core.url", "http://qa1-bank-core.testxinfei.cn/multi/queryBankCardListHistoryByUserNo"), userNo, uri, responseEntity.getBody());
            }
            return resp;
        } catch (Exception ex) {
            log.error("接口异常:[{}],url:{},userNo:{}", getLabel(), uri, userNo, ex);
        }
        return null;
    }

    private boolean checkPaymentAgreement(List<BankCardListHistoryResponse.BankCardInfo> bankCards) {
        if (bankCards == null || bankCards.isEmpty()) {
            return false; // 如果银行卡列表为空，直接返回false
        }

        for (BankCardListHistoryResponse.BankCardInfo cardInfo : bankCards) {
            // 只处理状态为1的银行卡号
            if (1 == cardInfo.getStatus()) { // 使用整数值进行比较
                // 加密银行卡号
                List<SecureEncryptDTO> secureEncryptDTOS = secureClient.batchEncrypt(Collections.singletonList(cardInfo.getCardNo()));
                if (secureEncryptDTOS == null || secureEncryptDTOS.isEmpty()) {
                    log.warn("银行卡号加密失败，卡号：{}", cardInfo.getCardNo());
                    continue; // 如果加密失败，记录警告并继续下一个循环
                }
                String bankCardCipher = secureEncryptDTOS.get(0).getCipherText();

                String uri = ApolloUtil.getAppProperty("npay-api.url", "https://qa1-npay-api.testxinfei.cn/agreement/queryCardSignInfo");
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);
                QueryCardSignInfoRequest req = new QueryCardSignInfoRequest();
                req.setBankCardCipher(bankCardCipher);
                req.setUa("xyf-cdp");
                HttpEntity<QueryCardSignInfoRequest> requestEntity = new HttpEntity<>(req, headers);
                try {
                    ResponseEntity<String> responseEntity = restTemplate.exchange(uri, HttpMethod.POST, requestEntity, String.class);
                    log.info("[checkPaymentAgreement] 查询卡有效的签约信息接口请求: {}", JsonUtil.toJson(req));
                    QueryCardSignInfoResponse resp = JsonUtil.parse(responseEntity.getBody(), QueryCardSignInfoResponse.class);
                    log.info("[checkPaymentAgreement] 查询卡有效的签约信息接口响应: {}", JsonUtil.toJson(resp));
                    if (resp != null && "000000".equals(resp.getCode()) && resp.getData() != null && !resp.getData().isEmpty()) {
                        return true; // 如果查询到有效的签约信息，返回true
                    }
                } catch (Exception ex) {
                    log.error("查询卡有效的签约信息接口异常，卡号：{}，异常：{}", cardInfo.getCardNo(), ex.getMessage());
                }
            }
        }
        return false; // 如果所有银行卡都未查询到有效的签约信息，返回false
    }
}
