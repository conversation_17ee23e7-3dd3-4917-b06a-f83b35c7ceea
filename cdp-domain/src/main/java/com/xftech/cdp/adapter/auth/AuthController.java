package com.xftech.cdp.adapter.auth;

import com.xftech.cdp.api.AuthApi;
import com.xftech.cdp.api.dto.base.Response;
import com.xftech.cdp.api.dto.req.auth.GetTokenReq;
import com.xftech.cdp.api.dto.resp.auth.GetTokenResp;
import com.xftech.cdp.domain.auth.annotation.Anonymous;
import com.xftech.cdp.domain.auth.service.AuthService;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2023/2/9
 */
@RestController
public class AuthController implements AuthApi {

    private final AuthService authService;

    public AuthController(AuthService authService) {
        this.authService = authService;
    }

    @Override
    @Anonymous
    public Response<GetTokenResp> getToken(GetTokenReq req) {
        return Response.success(authService.getToken(req));
    }
}
