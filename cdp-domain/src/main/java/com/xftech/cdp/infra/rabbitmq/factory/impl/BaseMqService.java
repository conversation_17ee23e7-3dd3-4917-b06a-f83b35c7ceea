package com.xftech.cdp.infra.rabbitmq.factory.impl;

import com.xftech.cdp.infra.rabbitmq.factory.AbstractMqFactory;
import com.xftech.rabbitmq.build.BindParamBuilder;
import com.xftech.rabbitmq.build.ConnectionBuilder;
import com.xftech.rabbitmq.core.UdpMqConsumer;
import com.xftech.rabbitmq.core.UdpMqProducer;

import java.io.IOException;
import java.util.concurrent.TimeoutException;

public class BaseMqService extends AbstractMqFactory {
    @Override
    public void init() throws IOException, TimeoutException {

    }

    @Override
    public UdpMqProducer initProducerConn(String connectionId) {
        return null;
    }

    @Override
    public UdpMqConsumer initConsumerConn(String connectionId) {
        return null;
    }

    @Override
    public void createProducer(ConnectionBuilder builder) {

    }

    @Override
    public void createConsumer(BindParamBuilder builder) {

    }

    @Override
    public void createDeadLetterConsumer() {

    }

    @Override
    public void createDelayProducer() {

    }

    @Override
    public void createDelayConsumer() {

    }
}
