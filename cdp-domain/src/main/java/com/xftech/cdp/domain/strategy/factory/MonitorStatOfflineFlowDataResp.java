package com.xftech.cdp.domain.strategy.factory;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xftech.cdp.api.dto.base.TimeFormat;
import com.xftech.cdp.api.dto.resp.AbsMonitorListResp;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;


/**
 * @<NAME_EMAIL>
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class MonitorStatOfflineFlowDataResp extends AbsMonitorListResp {
    @ApiModelProperty(value = "日期")
    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    @ExcelProperty(index = 0, value = "日期")
    private LocalDateTime bizDate;

    @ApiModelProperty(value = "人群包更新完成时间")
    @ExcelProperty(index = 1, value = "人群包更新完成时间")
    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    private LocalDateTime crowdRefreshTime;

    @ApiModelProperty(value = "人群包用户数")
    @ExcelProperty(index = 2, value = "人群包用户数")
    private Integer crowdUserNum;

    @ApiModelProperty(value = "分组")
    @ExcelProperty(index = 3, value = "分组")
    private String groupName;

    @ApiModelProperty(value = "分组用户数")
    @ExcelProperty(index = 4, value = "分组用户数")
    private Integer groupUserNum;

    @ApiModelProperty(value = "流控过滤用户数")
    @ExcelProperty(index = 5, value = "流控过滤用户数")
    private Integer flowControlNum;

    @ApiModelProperty(value = "实时标签过滤用户数")
    @ExcelProperty(index = 6, value = "实时标签过滤用户数")
    private Integer filterLabelNum;

    @ApiModelProperty(value = "实时排除项过滤用户数")
    @ExcelProperty(index = 7, value = "实时排除项过滤用户数")
    private Integer filterExcludeNum;

    @ApiModelProperty(value = "应发用户数")
    @ExcelProperty(index = 8, value = "应发用户数")
    private Integer dispatchNum;
}
