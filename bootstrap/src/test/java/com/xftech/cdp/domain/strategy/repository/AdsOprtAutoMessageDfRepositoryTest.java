/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.repository;

import com.xftech.cdp.BaseTest;
import com.xftech.cdp.domain.ads.repository.AdsOprtAutoMessageDfRepository;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;

/**
 *
 * <AUTHOR>
 * @version $ AdsOprtAutoMessageDfRepositoryTest, v 0.1 2023/11/16 15:57 yye.xu Exp $
 */

public class AdsOprtAutoMessageDfRepositoryTest extends BaseTest {

    @Autowired
    private AdsOprtAutoMessageDfRepository adsOprtAutoMessageDfRepository;

    @Test
    public void test_selectMind(){
       Long i = adsOprtAutoMessageDfRepository.selectAutoMessageMinAppUserId(LocalDateTime.now());
       System.out.println(i);
    }
}