package com.xftech.cdp.domain.crowd.factory.impl;

import com.dianping.cat.proxy.Tracer;
import com.dianping.cat.proxy.spi.Transaction;
import com.xftech.cdp.domain.crowd.exception.CrowdException;
import com.xftech.cdp.domain.crowd.factory.AbsCrowdOptService;
import com.xftech.cdp.domain.crowd.model.dispatch.CrowdContext;
import com.xftech.cdp.domain.crowd.model.dispatch.CrowdWereHouse;
import com.xftech.cdp.domain.crowd.model.dispatch.InvokePage;
import com.xftech.cdp.domain.crowd.model.enums.CrowdLabelEnum;
import com.xftech.cdp.domain.crowd.model.enums.CrowdLabelRelationEnum;
import com.xftech.cdp.domain.crowd.repository.AdsUserLabelDetailInfoDfRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdLabelPrimaryRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdLabelRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdLabelSubRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdWereHouseSnapshotRepository;
import com.xftech.cdp.domain.crowd.repository.LabelRepository;
import com.xftech.cdp.infra.repository.cdp.crowd.po.*;
import com.xftech.cdp.infra.utils.ListUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.MutablePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Slf4j
@Service
public class UserLableOptService extends AbsCrowdOptService {
    private static final String ADS_USER_LABEL_SQL = "select app_user_id,app,inner_app,ab_num,app_user_id_last2,mobile,register_time,mobile_utm_source,utm_source_list,last_loan_success_utm_source from ads_user_label_detail_info_df where (";

    @Autowired
    private CrowdWereHouseSnapshotRepository crowdWereHouseSnapshotRepository;
    @Autowired
    private AdsUserLabelDetailInfoDfRepository adsUserLabelDetailInfoDfRepository;
    @Autowired
    private CrowdLabelRepository crowdLabelRepository;
    @Autowired
    private CrowdLabelPrimaryRepository crowdLabelPrimaryRepository;
    @Autowired
    private LabelRepository labelRepository;
    @Autowired
    private CrowdLabelSubRepository crowdLabelSubRepository;

    @Override
    public void preHandler(CrowdContext crowdContext) {
        CrowdWereHouseSnapshotDo todayCrowd = crowdWereHouseSnapshotRepository.getTodayCrowd();
        if (todayCrowd == null) {
            crowdContext.setFailReason("数仓数据尚未初始化");
            throw new CrowdException("人群刷行失败，数仓数据尚未初始化");
        }
        int rockPageSize = crowdContext.isRunAsFixedNumberPage() ? crowdConfig.getRockPageSizeNew() : crowdConfig.getRockPageSize();
        if (crowdContext.isRunAsFixedNumberPage()) {
            Tracer.logEvent("RunCrowdPackAsFixedNumberPage", String.valueOf(crowdContext.getCrowdPack().getId()));
        }
        log.info("跑人群包数据, 是否使用新的分页方式:{}, 分页大小:{}", crowdContext.isRunAsFixedNumberPage(), rockPageSize);
        crowdContext.subLevel(todayCrowd.getMinId(), todayCrowd.getMaxId(),
                crowdConfig.getTaskBatchCount(), rockPageSize);
    }

    @Override
    public CrowdContext initContext(boolean isRunAsFixedNumberPage, CrowdPackDo crowdPack, AbsCrowdOptService crowdOpt) {
        List<CrowdLabelDo> crowdLabelList = crowdLabelRepository.selectListByCrowdId(crowdPack.getId());
        List<CrowdLabelSubDo> crowdLabeSublDoList = crowdLabelSubRepository.selectSubListByCrowdId(crowdPack.getId());
        List<Long> labelIds = ListUtils.distinctMap(crowdLabelList, CrowdLabelDo::getLabelId);
        labelIds.addAll(ListUtils.distinctMap(crowdLabeSublDoList, CrowdLabelSubDo::getLabelId));
        List<CrowdLabelPrimaryDo> crowdLabelPrimaryList = crowdLabelPrimaryRepository.selectListByCrowdId(crowdPack.getId());

        List<LabelDo> labelDos = labelRepository.selectBatchIds(labelIds);
        CrowdContext crowdContext = CrowdContext.init(isRunAsFixedNumberPage, crowdPack, crowdLabelPrimaryList, crowdLabelList, labelDos, crowdLabeSublDoList);
        crowdContext.setCrowdOpt(crowdOpt);
        return crowdContext;
    }

    @Override
    public void organizeSql(CrowdContext crowdContext) {
        List<CrowdContext.Delineate> delineates = crowdContext.getDelineates();
        MutablePair<StringBuilder, StringBuilder> pair = new MutablePair<>();
        for (CrowdContext.Delineate delineate : delineates) {
            if (CollectionUtils.isEmpty(delineate.getPrimaryLabels())) {
                continue;
            }
            StringBuilder sql = new StringBuilder(ADS_USER_LABEL_SQL);
            // 一层逻辑拼接
            for (int i = 0; i < delineate.getPrimaryLabels().size(); i++) {
                CrowdContext.PrimaryLabel primaryLabel = delineate.getPrimaryLabels().get(i);
                if (i == 0) {
                    primaryLabel.setLabelRelation(CrowdLabelRelationEnum.NONE);
                }
                StringBuilder labelSql = new StringBuilder();
                labelSql.append(primaryLabel.getLabelRelation().getValue());
                labelSql.append("(");
                // 二层逻辑拼接
                for (int labelIndex = 0; labelIndex < primaryLabel.getLabels().size(); labelIndex++) {
                    CrowdContext.Label label = primaryLabel.getLabels().get(labelIndex);
                    if (labelIndex == 0) {
                        label.setLabelRelation(CrowdLabelRelationEnum.NONE);
                    }
                    if (CollectionUtils.isEmpty(label.getSubLabels())) {
                        // 自定义标签跳过，不参与拼接
                        if (label.getLabelType() == 1) {
                            continue;
                        }
                        labelSql.append(label.getLabelRelation().getValue()).append(label.condition());
                        continue;
                    }
                    if (label.getLabelType() == 1) {
                        labelSql.append(label.getLabelRelation().getValue()).append("(");
                    } else {
                        labelSql.append(label.getLabelRelation().getValue()).append("(").append(label.condition());
                    }
                    // 三层逻辑拼接
                    List<CrowdContext.SubLabel> subLabels = label.getSubLabels();
                    for (int subIndex = 0; subIndex < subLabels.size(); subIndex++) {
                        CrowdContext.SubLabel subLabel = subLabels.get(subIndex);
                        // 三层父级标签为自定义标签
                        if (label.getLabelType() == 1 && subIndex == 0) {
                            subLabel.setLabelRelation(CrowdLabelRelationEnum.NONE);
                        }
                        // 自定义标签跳过，不参与拼接
                        if (subLabel.getLabelType() == 1) {
                            continue;
                        }
                        labelSql.append(subLabel.getLabelRelation().getValue()).append(subLabel.condition());
                    }
                    labelSql.append(")");
                }
                labelSql.append(")");
                sql.append(labelSql);
            }
            if (CrowdLabelEnum.INCLUDE_LABEL == delineate.getCrowdLabel()) {
                pair.setLeft(sql);
            } else {
                pair.setRight(sql);
            }
        }
        crowdContext.setLabelSqlPair(pair);
    }

    @Override
    public void andPage(StringBuilder sql, InvokePage invokePage) {
        sql.append(" ) and app_user_id > ").append(invokePage.getAppUserId()).append(" and app_user_id <= ").append(invokePage.getMaxAppUserId()).append(" order by app_user_id limit ").append(invokePage.getPageSize());
    }

    public void andPageNew(StringBuilder sql, InvokePage invokePage) {
        sql.append(" ) and app_user_id > ").append(invokePage.getAppUserId()).append(" and app_user_id <= ").append(invokePage.getMaxAppUserId());
    }

    @Override
    public List<CrowdWereHouse> queryAdsDataInfo(String sql, boolean isRunAsFixedNumberPage) {
        String catTransName = isRunAsFixedNumberPage ? "queryAdsDataInfoNew" : "queryAdsDataInfo";
        Transaction transaction = Tracer.newTransaction("UserLableOptService", catTransName);
        try {
            List<CrowdWereHouse> crowdWereHouses = adsUserLabelDetailInfoDfRepository.queryWareHouseUserInfo(sql);
            transaction.setStatus(Transaction.SUCCESS);
            return crowdWereHouses;
        } catch (Exception ex) {
            transaction.setStatus(ex);
            throw ex;
        } finally {
            transaction.complete();
        }
    }
}
