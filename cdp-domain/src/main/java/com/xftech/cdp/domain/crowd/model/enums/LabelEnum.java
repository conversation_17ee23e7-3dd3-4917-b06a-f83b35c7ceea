package com.xftech.cdp.domain.crowd.model.enums;

import com.xftech.cdp.domain.crowd.exception.CrowdException;
import com.xftech.cdp.domain.crowd.model.label.crowd.CrowdLabelOption;
import com.xftech.cdp.domain.crowd.model.label.crowd.DataSegment;
import com.xftech.cdp.domain.crowd.model.label.crowd.Entry;
import com.xftech.cdp.domain.crowd.model.label.crowd.FixedCheckOption;
import com.xftech.cdp.domain.crowd.model.label.crowd.FixedRadioOption;
import com.xftech.cdp.domain.crowd.model.label.crowd.NewRandom;
import com.xftech.cdp.domain.crowd.model.label.crowd.Random;
import com.xftech.cdp.domain.crowd.model.label.crowd.TimeLimit;
import com.xftech.cdp.domain.crowd.model.label.crowd.ValueRange;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023/2/13
 */
public enum LabelEnum {

    ;


    @AllArgsConstructor
    @Getter
    public enum LabelOptionTypeEnum {

        FIXED_RADIO(0, "固定单选", FixedRadioOption.class),

        FIXED_CHECK(1, "固定多选", FixedCheckOption.class),

        ENTRY(2, "输入项", Entry.class),

        TIME_LIMIT(3, "时间范围", TimeLimit.class),

        VALUE_RANGE(4, "数值范围", ValueRange.class),

        DATA_SEGMENT(5, "数值段", DataSegment.class),

        RANDOM(6, "随机值", Random.class),
        NEW_RANDOM(7, "新随机数", NewRandom.class);


        private final int code;

        private final String description;

        private final Class<? extends CrowdLabelOption> clazz;


        public static LabelOptionTypeEnum ofCode(int code) {
            for (LabelOptionTypeEnum labelOptionTypeEnum : values()) {
                if (labelOptionTypeEnum.code == code) {
                    return labelOptionTypeEnum;
                }
            }
            throw new CrowdException(String.format("不存在该可选配置类型: %s", code));
        }

    }


    @AllArgsConstructor
    @Getter
    public enum PrimaryLabelEnum {

        USER_ATTRIBUTE(0, "属性标签"),

        USER_BEHAVIOR(1, "行为标签"),

        BLACK_LIST(2, "黑名单"),

        RISK_MODEL(3, "风险模型");

        private final int code;

        private final String description;


        public static PrimaryLabelEnum ofCode(int code) {
            for (PrimaryLabelEnum primaryLabelEnum : values()) {
                if (primaryLabelEnum.code == code) {
                    return primaryLabelEnum;
                }
            }
            throw new CrowdException(String.format("不存在该一级属性: %s", code));
        }
    }


    @AllArgsConstructor
    @Getter
    public enum SecondaryLabelEnum {

        APP(PrimaryLabelEnum.USER_ATTRIBUTE, 0, "app信息"),

        GROUP_IDENTIFICATION(PrimaryLabelEnum.USER_ATTRIBUTE, 1, "分组标识"),

        CHANNEL_MANAGER(PrimaryLabelEnum.USER_ATTRIBUTE, 2, "渠道管理"),

        DROP_THE_NODE(PrimaryLabelEnum.USER_ATTRIBUTE, 3, "脱落节点"),

        DROP_THE_NODE_DATE(PrimaryLabelEnum.USER_ATTRIBUTE, 3, "脱落节点天数"),

        REGISTER(PrimaryLabelEnum.USER_BEHAVIOR, 4, "注册行为"),

        LOGIN(PrimaryLabelEnum.USER_BEHAVIOR, 5, "登录行为"),

        INPUT(PrimaryLabelEnum.USER_BEHAVIOR, 6, "进件行为"),

        CREDIT(PrimaryLabelEnum.USER_BEHAVIOR, 7, "授信行为"),

        WITHDRAWAL(PrimaryLabelEnum.USER_BEHAVIOR, 8, "提现行为"),

        ADJUST_AMOUNT(PrimaryLabelEnum.USER_BEHAVIOR, 9, "调额行为"),

        REPAY(PrimaryLabelEnum.USER_BEHAVIOR, 10, "还款行为"),

        MARKET_CONDUCT(PrimaryLabelEnum.USER_BEHAVIOR, 11, "营销行为"),

        MARKET_RELATE(PrimaryLabelEnum.BLACK_LIST, 12, "营销相关"),

        RISK_RELATE(PrimaryLabelEnum.BLACK_LIST, 13, "风险相关"),

        CHANNEL_RELATE(PrimaryLabelEnum.BLACK_LIST, 14, "渠道相关"),

        MODEL_SCORE_RELATE(PrimaryLabelEnum.RISK_MODEL, 15, "模型打分"),

        TELE_MARKET(PrimaryLabelEnum.USER_ATTRIBUTE, 16, "电销行为"),

        API_COLLIDING(PrimaryLabelEnum.USER_BEHAVIOR, 17, "API撞库行为"),

        ADJUST_PRICE(PrimaryLabelEnum.USER_BEHAVIOR, 18, "调价行为"),

        HALF_A_YEAR(PrimaryLabelEnum.USER_BEHAVIOR, 19, "近半年行为"),

        RISK_RATING(PrimaryLabelEnum.RISK_MODEL, 20, "风险评级"),

        SMS_COLLIDING(PrimaryLabelEnum.USER_ATTRIBUTE, 21, "短信撞库行为"),

        FXK_BEHAVIOR(PrimaryLabelEnum.USER_BEHAVIOR, 22, "fxk行为"),

        DEVICE_INFO(PrimaryLabelEnum.USER_ATTRIBUTE,23,"设备信息"),

        LOAN_OVER_LOAD_DISTRIBUTE(PrimaryLabelEnum.USER_BEHAVIOR,24,"贷超分发"),

        ;

        private final PrimaryLabelEnum primaryLabelEnum;

        private final int code;

        private final String description;
    }


    @AllArgsConstructor
    @Getter
    public enum LabelOptionDigitsEnum {

        FIXED_CHECK(1, "", LabelOptionTypeEnum.FIXED_CHECK),

        DATA_SEGMENT(2, "", LabelOptionTypeEnum.DATA_SEGMENT);

        private final int code;

        private final String description;


        private final LabelOptionTypeEnum labelOptionTypeEnum;

        public static LabelOptionDigitsEnum ofCode(int code) {
            for (LabelOptionDigitsEnum labelOptionDigitsEnum : values()) {
                if (labelOptionDigitsEnum.code == code) {
                    return labelOptionDigitsEnum;
                }
            }
            throw new CrowdException(String.format("不存在该可选配置类型: %s", code));
        }

    }


}
