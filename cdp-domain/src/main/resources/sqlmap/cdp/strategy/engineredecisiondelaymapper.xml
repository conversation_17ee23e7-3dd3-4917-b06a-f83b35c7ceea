<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="engineredecisiondelay">
    <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.strategy.po.EngineReDecisionDelayDo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="strategy_id" jdbcType="BIGINT" property="strategyId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="group_name" jdbcType="VARCHAR" property="groupName"/>
        <result column="reinput_count" jdbcType="SMALLINT" property="reInputCount"/>
        <result column="reinput_delay_second" jdbcType="INTEGER" property="reInputDelaySecond"/>
        <result column="reinput_time" jdbcType="TIMESTAMP" property="reInputTime"/>
        <result column="event_first_time" jdbcType="TIMESTAMP" property="eventFirstTime"/>
        <result column="status" jdbcType="SMALLINT" property="status"/>
        <result column="reinput_result" jdbcType="SMALLINT" property="reInputResult"/>
        <result column="message_id" jdbcType="VARCHAR" property="messageId"/>
        <result column="last_redecision_id" jdbcType="BIGINT" property="lastReDecisionId"/>
        <result column="ext_info" property="extInfo"/>
        <result column="d_flag" jdbcType="SMALLINT" property="dFlag"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime"/>
        <result column="date_value" jdbcType="INTEGER" property="dateValue"/>
    </resultMap>

    <resultMap id="ReportResultMap" type="com.xftech.cdp.infra.repository.cdp.strategy.po.EngineReDecisionDelayReportDo">
        <id column="group_name" jdbcType="VARCHAR" property="groupName"/>
        <result column="reinput_count" jdbcType="SMALLINT" property="reInputCount"/>
        <result column="total_records" jdbcType="INTEGER" property="totalRecords"/>
        <result column="distinct_user_count" jdbcType="INTEGER" property="distinctUserCount"/>
        <result column="total_status_1_records" jdbcType="INTEGER" property="totalStatus1Records"/>
        <result column="distinct_user_count_status_1" jdbcType="INTEGER" property="distinctUserCountStatus1"/>
        <result column="total_reinput_result_3_records" jdbcType="INTEGER" property="totalReInputResult3Records"/>
        <result column="distinct_user_reinput_result_3" jdbcType="INTEGER" property="distinctUserReInputResult3"/>
        <result column="distinct_user_reinput_result_1" jdbcType="INTEGER" property="distinctUserReInputResult1"/>
        <result column="total_status_2_records" jdbcType="INTEGER" property="totalStatus2Records"/>
        <result column="distinct_user_count_status_2" jdbcType="INTEGER" property="distinctUserCountStatus2"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , strategy_id, user_id, group_name, reinput_count, reinput_delay_second, reinput_time,
    event_first_time, `status`, reinput_result, message_id, last_redecision_id, ext_info, 
    d_flag, create_time, updated_time, date_value
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from engine_redecision_delay
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectLastRecordByStrategyMsgId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from engine_redecision_delay
        where strategy_id = #{strategyId,jdbcType=BIGINT} and message_id = #{messageId,jdbcType=VARCHAR} and d_flag = 0 order by id desc limit 1
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        update engine_redecision_delay set d_flag = 1
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.EngineReDecisionDelayDo"
            useGeneratedKeys="true">
        insert into engine_redecision_delay (strategy_id, user_id, group_name,
                                             reinput_count, reinput_delay_second, reinput_time,
                                             event_first_time, `status`, reinput_result,
                                             message_id, last_redecision_id, ext_info,
                                             d_flag, create_time, updated_time,
                                             date_value)
        values (#{strategyId,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{groupName,jdbcType=VARCHAR},
                #{reInputCount,jdbcType=SMALLINT}, #{reInputDelaySecond,jdbcType=INTEGER},
                #{reInputTime,jdbcType=TIMESTAMP},
                #{eventFirstTime,jdbcType=TIMESTAMP}, #{status,jdbcType=SMALLINT}, #{reInputResult,jdbcType=SMALLINT},
                #{messageId,jdbcType=VARCHAR}, #{lastReDecisionId,jdbcType=BIGINT}, #{extInfo,jdbcType=OTHER},
                #{dFlag,jdbcType=SMALLINT}, #{createTime,jdbcType=TIMESTAMP}, #{updatedTime,jdbcType=TIMESTAMP},
                #{dateValue,jdbcType=INTEGER})
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.EngineReDecisionDelayDo"
            useGeneratedKeys="true">
        insert into engine_redecision_delay
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="strategyId != null">
                strategy_id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="groupName != null">
                group_name,
            </if>
            <if test="reInputCount != null">
                reinput_count,
            </if>
            <if test="reInputDelaySecond != null">
                reinput_delay_second,
            </if>
            <if test="reInputTime != null">
                reinput_time,
            </if>
            <if test="eventFirstTime != null">
                event_first_time,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="reInputResult != null">
                reinput_result,
            </if>
            <if test="messageId != null">
                message_id,
            </if>
            <if test="lastReDecisionId != null">
                last_redecision_id,
            </if>
            <if test="extInfo != null">
                ext_info,
            </if>
            <if test="dFlag != null">
                d_flag,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updatedTime != null">
                updated_time,
            </if>
            <if test="dateValue != null">
                date_value,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="strategyId != null">
                #{strategyId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="groupName != null">
                #{groupName,jdbcType=VARCHAR},
            </if>
            <if test="reInputCount != null">
                #{reInputCount,jdbcType=SMALLINT},
            </if>
            <if test="reInputDelaySecond != null">
                #{reInputDelaySecond,jdbcType=INTEGER},
            </if>
            <if test="reInputTime != null">
                #{reInputTime,jdbcType=TIMESTAMP},
            </if>
            <if test="eventFirstTime != null">
                #{eventFirstTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                #{status,jdbcType=SMALLINT},
            </if>
            <if test="reInputResult != null">
                #{reInputResult,jdbcType=SMALLINT},
            </if>
            <if test="messageId != null">
                #{messageId,jdbcType=VARCHAR},
            </if>
            <if test="lastReDecisionId != null">
                #{lastReDecisionId,jdbcType=BIGINT},
            </if>
            <if test="extInfo != null">
                #{extInfo,jdbcType=OTHER},
            </if>
            <if test="dFlag != null">
                #{dFlag,jdbcType=SMALLINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedTime != null">
                #{updatedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="dateValue != null">
                #{dateValue,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.EngineReDecisionDelayDo">
        update engine_redecision_delay
        <set>
            <if test="strategyId != null">
                strategy_id = #{strategyId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="groupName != null">
                group_name = #{groupName,jdbcType=VARCHAR},
            </if>
            <if test="reInputCount != null">
                reinput_count = #{reInputCount,jdbcType=SMALLINT},
            </if>
            <if test="reInputDelaySecond != null">
                reinput_delay_second = #{reInputDelaySecond,jdbcType=INTEGER},
            </if>
            <if test="reInputTime != null">
                reinput_time = #{reInputTime,jdbcType=TIMESTAMP},
            </if>
            <if test="eventFirstTime != null">
                event_first_time = #{eventFirstTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=SMALLINT},
            </if>
            <if test="reInputResult != null">
                reinput_result = #{reInputResult,jdbcType=SMALLINT},
            </if>
            <if test="messageId != null">
                message_id = #{messageId,jdbcType=VARCHAR},
            </if>
            <if test="lastReDecisionId != null">
                last_redecision_id = #{lastReDecisionId,jdbcType=BIGINT},
            </if>
            <if test="extInfo != null">
                ext_info = #{extInfo,jdbcType=OTHER},
            </if>
            <if test="dFlag != null">
                d_flag = #{dFlag,jdbcType=SMALLINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedTime != null">
                updated_time = #{updatedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="dateValue != null">
                date_value = #{dateValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByPrimaryKey"
            parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.EngineReDecisionDelayDo">
        update engine_redecision_delay
        set strategy_id          = #{strategyId,jdbcType=BIGINT},
            user_id              = #{userId,jdbcType=BIGINT},
            group_name           = #{groupName,jdbcType=VARCHAR},
            reinput_count        = #{reInputCount,jdbcType=SMALLINT},
            reinput_delay_second = #{reInputDelaySecond,jdbcType=INTEGER},
            reinput_time         = #{reInputTime,jdbcType=TIMESTAMP},
            event_first_time     = #{eventFirstTime,jdbcType=TIMESTAMP},
            `status`             = #{status,jdbcType=SMALLINT},
            reinput_result       = #{reInputResult,jdbcType=SMALLINT},
            message_id           = #{messageId,jdbcType=VARCHAR},
            last_redecision_id   = #{lastReDecisionId,jdbcType=BIGINT},
            ext_info             = #{extInfo,jdbcType=OTHER},
            d_flag               = #{dFlag,jdbcType=SMALLINT},
            create_time          = #{createTime,jdbcType=TIMESTAMP},
            updated_time         = #{updatedTime,jdbcType=TIMESTAMP},
            date_value           = #{dateValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectReportByStrategyIdAndDate" resultMap="ReportResultMap">
        SELECT
            group_name,
            reinput_count,
            COUNT(*) AS total_records,
            COUNT(DISTINCT user_id) AS distinct_user_count,
            COUNT(CASE WHEN status = 1 THEN 1 END) AS total_status_1_records,
            COUNT(DISTINCT CASE WHEN status = 1 THEN user_id END) AS distinct_user_count_status_1,
            COUNT(CASE WHEN reinput_result = 3 THEN 1 END) AS total_reinput_result_3_records,
            COUNT(DISTINCT CASE WHEN reinput_result = 3 THEN user_id END) AS distinct_user_reinput_result_3,
            COUNT(DISTINCT CASE WHEN reinput_result = 1 THEN user_id END) AS distinct_user_reinput_result_1,
            COUNT(CASE WHEN status = 2 THEN 1 END) AS total_status_2_records,
            COUNT(DISTINCT CASE WHEN status = 2 THEN user_id END) AS distinct_user_count_status_2
        FROM
            engine_redecision_delay
        WHERE strategy_id = #{strategyId,jdbcType=BIGINT} and date_value = #{dateValue,jdbcType=INTEGER}
        GROUP BY
            group_name,
            reinput_count
        ORDER BY
            group_name,
            reinput_count
    </select>
</mapper>