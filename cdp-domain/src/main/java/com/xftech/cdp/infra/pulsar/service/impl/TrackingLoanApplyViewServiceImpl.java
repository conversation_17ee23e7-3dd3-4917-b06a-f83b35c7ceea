/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.infra.pulsar.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xftech.cdp.domain.strategy.service.dispatch.event.StrategyEventDispatchService;
import com.xftech.cdp.infra.client.usercenter.UserCenterClient;
import com.xftech.cdp.infra.client.usercenter.model.UserInfoResp;
import com.xftech.cdp.infra.config.AppConfigService;
import com.xftech.cdp.infra.config.LogUtil;
import com.xftech.cdp.infra.constant.Constants;
import com.xftech.cdp.infra.pulsar.service.CdpPulsarService;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventMessageVO;

import com.xyf.cis.SecureClient;
import com.xyf.cis.dto.SecureEncryptDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version $ TrackingLoanApplyViewServiceImpl, v 0.1 2023/10/7 16:57 lingang.han Exp $
 */

@Slf4j
@Service("trackingLoanApplyView")
@AllArgsConstructor
public class TrackingLoanApplyViewServiceImpl implements CdpPulsarService {

    private StrategyEventDispatchService strategyEventDispatchService;
    private AppConfigService appConfigService;

    @Autowired
    private UserCenterClient userCenterClient;
    @Resource
    private SecureClient secureClient;

    @Override
    public Optional<BizEventMessageVO> toBizEventMessageVO(String eventMessage) {
        JSONObject jsonObject = JSON.parseObject(eventMessage);
        String eventId = (String) jsonObject.get("event_id");
        String creditUserId = (String) jsonObject.get("credit_user_id");
        //如果没有event_id,则是一条错误的消息，不做处理
        //如果没有credit_user_id,则是未登录状态的上报信息，不做处理
        if (StringUtils.isBlank(eventId) || StringUtils.isBlank(creditUserId)) {
            log.info("Tracking_LoanApplyView eventMessage is have event_id or credit_user_id, eventMessage:{}", eventMessage);
            return Optional.empty();
        }
        BizEventMessageVO bizEventMessageVO = JSON.parseObject(eventMessage, BizEventMessageVO.class);
        //从配置中取出event_id对应的event_name
        String eventTrackingEventName = appConfigService.getEventTrackingEventName(eventId);
        if (StringUtils.isBlank(eventTrackingEventName)) {
            log.info("eventId can not convert event name,eventId:{}", eventId);
            return Optional.empty();
        }
        if (StringUtils.equals(bizEventMessageVO.getCnlPdCode(), "MPP001000068")) { //小程序上报的app为xyf01,但上报的userno是xyf的,麻雀临时转换
            try {
                String mobile = bizEventMessageVO.getMobile();
                String mobileCipher = bizEventMessageVO.getMobile_cipher();
                if (StringUtils.isBlank(mobile) && StringUtils.isNotBlank(mobileCipher)) {
                    List<SecureEncryptDTO> secureEncryptDTOS = secureClient.batchDecrypt(Arrays.asList(mobileCipher));
                    if (CollectionUtils.isNotEmpty(secureEncryptDTOS)) {
                        mobile = secureEncryptDTOS.get(0).getPlainText();
                    }
                }
                UserInfoResp userInfoResp = userCenterClient.getUserByMobile(mobile, Constants.XYF01);
                bizEventMessageVO.setApp(Constants.XYF01);
                bizEventMessageVO.setCreditUserId(userInfoResp.getCreditUserId());
                bizEventMessageVO.setAppUserId(userInfoResp.getCreditUserId());
                bizEventMessageVO.setInnerApp(userInfoResp.getInnerApp());
                log.info("Tracking_LoanApplyView convertToUserNoXcx mobileCipher={}, mobile={}, userInfoResp={}", mobileCipher, mobile, userInfoResp);
            } catch (Exception e) {
                LogUtil.logDebug("Tracking_LoanApplyView convertToUserNoXcx error", e);
                return Optional.empty();
            }
        } else if (Constants.XYF.equals(bizEventMessageVO.getApp())){ //xyf转xyf01
            try {
                String mobile = bizEventMessageVO.getMobile();
                String mobileCipher = bizEventMessageVO.getMobile_cipher();
                if (StringUtils.isBlank(mobile) && StringUtils.isNotBlank(mobileCipher)) {
                    List<SecureEncryptDTO> secureEncryptDTOS = secureClient.batchDecrypt(Arrays.asList(mobileCipher));
                    if (CollectionUtils.isNotEmpty(secureEncryptDTOS)) {
                        mobile = secureEncryptDTOS.get(0).getPlainText();
                    }
                }
                UserInfoResp userInfoResp = userCenterClient.getUserByMobile(mobile, Constants.XYF01);
                bizEventMessageVO.setApp(Constants.XYF01);
                bizEventMessageVO.setCreditUserId(userInfoResp.getCreditUserId());
                bizEventMessageVO.setAppUserId(userInfoResp.getCreditUserId());
                bizEventMessageVO.setInnerApp(userInfoResp.getInnerApp());
                log.info("Tracking_LoanApplyView convertToUserNo mobileCipher={}, mobile={}, userInfoResp={}", mobileCipher, mobile, userInfoResp);
            } catch (Exception e) {
                LogUtil.logDebug("Tracking_LoanApplyView convertToUserNo error", e);
                return Optional.empty();
            }
        }

        bizEventMessageVO.setBizEventType(eventTrackingEventName);
        return Optional.of(bizEventMessageVO);
    }

    @Override
    public void consumer(String messageId, String eventMessage) {
        toBizEventMessageVO(eventMessage).ifPresent(eventMsg -> {
            log.info("Tracking_LoanApplyView eventMessage toBizEventMessageVO={}", eventMsg);
            strategyEventDispatchService.prescreen(messageId, eventMsg);
        });
    }
}