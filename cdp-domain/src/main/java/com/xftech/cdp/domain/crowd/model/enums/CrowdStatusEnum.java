package com.xftech.cdp.domain.crowd.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/2/10
 */
@AllArgsConstructor
@Getter
public enum CrowdStatusEnum {

    INIT(0, "初始化"),

    EXECUTING(1, "刷新中"),

    SUCCESS(2, "刷新成功"),

    FAILED(3, "刷新失败"),

    PAUSING(4, "暂停中"),

    ENDED(5, "已完成"),

    DEPRECATED(6,"已删除");

    private final int code;

    private final String description;

    public static List<Integer> getPausedCodes() {
        return Arrays.asList(EXECUTING.getCode(), PAUSING.getCode(), ENDED.getCode(), DEPRECATED.getCode());
    }

    public static CrowdStatusEnum getInstance(Integer code) {
        for (CrowdStatusEnum crowdStatusEnum : CrowdStatusEnum.values()) {
            if (Objects.equals(crowdStatusEnum.getCode(), code)) {
                return crowdStatusEnum;
            }
        }
        return null;
    }
}
