package com.xftech.cdp.domain.dict.fetch;

import com.xftech.cdp.api.dto.resp.dict.Dict;
import com.xftech.cdp.domain.crowd.model.enums.CrowdRefreshTypeEnum;
import com.xftech.cdp.domain.dict.model.enums.DictCode;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/2/18
 */
@Component
public class CrowdRefreshTypeDictFetcher implements DictFetcher {
    @Override
    public List<Dict> getDict() {
        List<Dict> result = new ArrayList<>();
        List<CrowdRefreshTypeEnum> enums = Arrays.stream(CrowdRefreshTypeEnum.values()).collect(Collectors.toList());
        for (CrowdRefreshTypeEnum value : enums) {
            result.add(Dict.builder().dictCode(String.valueOf(value.getCode())).dictValue(value.getDescription()).build());
        }
        return result;
    }

    @Override
    public DictCode getDictCode() {
        return DictCode.CROWD_REFRESH_TYPE;
    }

    @Override
    public String getDescription() {
        return "刷新类型";
    }
}
