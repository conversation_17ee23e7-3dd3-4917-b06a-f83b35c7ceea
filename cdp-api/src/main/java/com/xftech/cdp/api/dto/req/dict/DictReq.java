package com.xftech.cdp.api.dto.req.dict;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-07-06-5:34 PM
 */
@Data
@ApiModel(value = "字典对象")
public class DictReq {

    @ApiModelProperty(value = "字典code集合")
    private List<String> dictCodeList;

    @ApiModelProperty(value = "业务线")
    private String businessType;

}