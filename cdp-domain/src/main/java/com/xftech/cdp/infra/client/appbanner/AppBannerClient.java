/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.client.appbanner;

import cn.hutool.core.util.IdUtil;
import com.xftech.cdp.api.dto.req.AppBannerTemplateReq;
import com.xftech.cdp.api.dto.resp.AppBannerTemplateResp;
import com.xftech.cdp.feign.model.AppBannerTemplateDetail;
import com.xftech.cdp.feign.model.AppBannerTemplateList;
import com.xftech.cdp.feign.model.PageInfo;
import com.xftech.cdp.feign.model.response.AppBannerResponse;
import com.xftech.cdp.infra.config.AppConfigService;
import com.xftech.cdp.infra.utils.HttpClientUtil;
import com.xftech.cdp.infra.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.xftech.cdp.infra.config.LogUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @version $ AppBannerService, v 0.1 2024/4/30 14:11 benlin.wang Exp $
 */

@Slf4j
@Service
public class AppBannerClient {
    @Autowired
    private AppConfigService appConfigService;
    @Autowired
    private HttpClientUtil httpClientUtil;

    public AppBannerResponse<AppBannerTemplateList> requestAppBannerList(AppBannerTemplateReq appBannerListRequest) {
        appBannerListRequest.setUtm_source("cdp");

        Map<String, String> headers = new HashMap<>();
        headers.put("trace-id", IdUtil.fastSimpleUUID());

        List<AppBannerTemplateDetail> respList = new ArrayList<>();
        String url = appConfigService.getAppBannerSourceURL() + "/rpc/popup/list";
        String param = JsonUtil.toJson(appBannerListRequest);
        try {
            String resp = httpClientUtil.postForJson(url, param, headers);
            Map<String, Object> respObj = JsonUtil.parse(resp, Map.class);
            LogUtil.logDebug("AppBannerClient url={} param={} headers={} resp={},respObj={}", url, param, headers, resp,respObj);
            AppBannerResponse<AppBannerTemplateList> appBannerResponse = new AppBannerResponse<>();
            AppBannerTemplateList appBannerList = new AppBannerTemplateList();
            PageInfo pageInfo = new PageInfo();
            if (respObj != null && respObj.containsKey("suc") && (boolean) (respObj.get("suc"))) {
                appBannerResponse.setSuc(true);
                if (respObj.containsKey("data")) {
                    Map<String, Object> dataMap = (Map) (respObj.get("data"));
                    Object total = dataMap.get("total");
                    if (total != null) {
                        pageInfo.setCount((Integer) total);
                    }
                    if (dataMap.containsKey("list")) {
                        pageInfo.setPage(appBannerListRequest.getPage());
                        pageInfo.setPageSize(appBannerListRequest.getPage_size());
                        List<Map<String, Object>> listObject = (List<Map<String, Object>>) (dataMap.get("list"));
                        for (Map<String, Object> respMap : listObject) {
                            AppBannerTemplateDetail appDetail = new AppBannerTemplateDetail();
                            appDetail.setId(respMap.get("id").toString());
                            appDetail.setBusiness(respMap.get("biz_type").toString());
                            appDetail.setStatus(respMap.get("status").toString());
                            appDetail.setApp(respMap.get("app").toString());
                            appDetail.setName(respMap.get("resource_name").toString());
                            appDetail.setStyle(respMap.get("bind_logic").toString());
                            respList.add(appDetail);
                        }
                        appBannerList.setDataList(respList);
                        appBannerList.setPageInfo(pageInfo);
                        appBannerResponse.setData(appBannerList);
                    }
                }
            } else {
                appBannerResponse.setSuc(false);
            }
            return appBannerResponse;
        } catch (Exception e) {
            LogUtil.logDebug("Error occurred in AppBannerClient, url={}, param={}, headers={}", url, param, headers, e);
            AppBannerResponse<AppBannerTemplateList> appBannerResponse = new AppBannerResponse<>();
            appBannerResponse.setSuc(false);
            return appBannerResponse;
        }
    }
}