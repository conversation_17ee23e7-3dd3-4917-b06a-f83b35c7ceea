/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.adapter.strategy;

import com.xftech.cdp.api.FlowApi;
import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.base.Response;
import com.xftech.cdp.api.dto.req.flow.strategy.*;
import com.xftech.cdp.api.dto.resp.flow.strategy.Flow;
import com.xftech.cdp.api.dto.resp.flow.strategy.FlowDetailResp;
import com.xftech.cdp.api.dto.resp.flow.strategy.FlowMonitorResp;
import com.xftech.cdp.domain.strategy.service.flow.FlowService;
import com.xftech.cdp.infra.annotation.OperateLogAnnotation;
import com.xftech.cdp.infra.annotation.SysLog;
import com.xftech.cdp.infra.aviator.enums.OperateModeEnum;
import com.xftech.cdp.infra.aviator.enums.OperateTypeEnum;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * <AUTHOR>
 * @version $ StrategyFlowController, v 0.1 2023/12/15 13:30 yye.xu Exp $
 */

@RestController
@AllArgsConstructor
public class FlowController implements FlowApi {
    private final FlowService flowService;

    @Override
    @SysLog("画布-创建")
    @OperateLogAnnotation(description = "新增画布", type = OperateTypeEnum.ADD, mode = OperateModeEnum.FLOW)
    public Response<Boolean> create(FlowCreateReq request) {
        flowService.create(request);
        return Response.success(true);
    }

    @Override
    @SysLog("画布-详情")
    public Response<FlowDetailResp> detail(FlowDetailReq request) {
        return Response.success(flowService.detail(request.getFlowNo()));
    }

    @Override
    @SysLog("画布-更新")
    @OperateLogAnnotation(description = "修改画布", type = OperateTypeEnum.UPDATE, mode = OperateModeEnum.FLOW)
    public Response<Boolean> update(FlowUpdateReq request) {
        flowService.update(request);
        return Response.success(true);
    }

    @Override
    @SysLog("画布-操作")
    @OperateLogAnnotation(description = "操作画布", type = OperateTypeEnum.OPERATE, mode = OperateModeEnum.FLOW)
    public Response<Boolean> operate(FlowOperateReq request) {
        flowService.operate(request);
        return Response.success(true);
    }

    @Override
    @SysLog("画布-列表")
    public Response<PageResultResponse<Flow>> list(FlowListReq request) {
        return Response.success(flowService.list(request));
    }

    @Override
    public Response<PageResultResponse<FlowMonitorResp>> monitor(FlowMonitorReq request) {
        return Response.success(flowService.monitor(request));
    }
}