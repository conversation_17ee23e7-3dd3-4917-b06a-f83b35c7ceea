package com.xftech.cdp.infra.client.appcore.response;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version $ BalanceOptResp, v 0.1 2025/1/3 21:38 mingwen.zang Exp $
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BalanceOptResp {
    final static int SUCCESS = 0;
    // 用户余额操作结果
    private Integer status = SUCCESS;

    public static int getFromBooleanStatus(Boolean b, int i) {
        return b ? SUCCESS : i;
    }
}
