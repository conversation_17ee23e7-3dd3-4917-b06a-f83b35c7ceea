package com.xftech.cdp.distribute.crowd.service;

import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;

import com.xftech.cdp.distribute.crowd.feign.CrowdFeignClient;
import com.xftech.cdp.distribute.crowd.feign.CrowdJudgementFeignClient;
import com.xftech.cdp.infra.config.ApolloUtil;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.leo.datainsight.client.rr.api.request.ApiCrowdQueryListRequest;
import com.leo.datainsight.client.rr.api.response.ApiCrowdQueryListResponse;
import com.leo.datainsight.client.rr.response.PageResponse;
import com.leo.datainsight.client.rr.response.RestResponse;
import com.leo.datainsightcore.client.rr.request.ApiBatchJudgeSinglePersonRequest;
import com.leo.datainsightcore.client.rr.response.ApiBatchJudgeSinglePersonResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import static com.xftech.cdp.infra.config.ApolloKeyConstants.INSIGHT_CROWD_PACK_WHITELIST_CROWD_PACK;

/**
 * <AUTHOR>
 * @version v1.0 2025/5/6
 * @description CrowdService
 */
@Slf4j
@Service
public class CrowdInfoService {

    @Value("${CrowdInfoService.queryCrowdInfoRetryCount:3}")
    private Integer queryCrowdInfoRetryCount;

    @Resource
    private CrowdFeignClient crowdFeignClient;
    @Resource
    private CrowdJudgementFeignClient crowdJudgementFeignClient;

    /**
     * 查询人群元数据信息
     *
     * @param request
     * @return
     */
    public RestResponse<PageResponse<ApiCrowdQueryListResponse>> queryCrowdInfo(ApiCrowdQueryListRequest request) {
        if (request == null) {
            return null;
        }

        RestResponse<PageResponse<ApiCrowdQueryListResponse>> response = null;
        int retryCount = 1;
        while (response == null && retryCount <= queryCrowdInfoRetryCount) {
            retryCount++;
            try {
                response = crowdFeignClient.queryList(request);
                log.info("CrowdInfoService queryCrowdInfo request={} response={}", JSONObject.toJSONString(request), JSONObject.toJSONString(response));
            } catch (Exception e) {
                log.warn("CrowdInfoService queryCrowdInfo error", e);
            }
        }
        return response;
    }

    /**
     * 是否通过洞察平台判断
     *
     * @param crowdPackIds 人群包列表
     * @return
     */
    public boolean isCheckByInsightPlatform(List<Long> crowdPackIds) {
        try {
            if (CollectionUtils.isEmpty(crowdPackIds)) {
                return false;
            }
            int hitWhiteListCount = 0;
            for (Long crowdId : crowdPackIds) {
                if (ApolloUtil.isInTheWhitelist(INSIGHT_CROWD_PACK_WHITELIST_CROWD_PACK, crowdId)) {
                    hitWhiteListCount++;
                }
            }
            if (hitWhiteListCount == crowdPackIds.size()) {
                log.info("CrowdInfoService isCheckByInsightPlatform true, crowdPackIds={}", crowdPackIds);
                return true;
            }
        } catch (Exception e) {
            log.error("CrowdInfoService isCheckByInsightPlatform error", e);
        }

        return false;
    }

    /**
     * 通过洞察平台判断是否命中人群包
     *
     * @param crowdPackIds
     * @param userNo
     * @return
     */
    public Pair<Boolean, CrowdDetailDo> checkByInsightPlatform(List<Long> crowdPackIds, Long userNo) {
        if (CollectionUtils.isEmpty(crowdPackIds) || userNo == null) {
            return Pair.of(Boolean.FALSE, null);
        }

        try {
            ApiBatchJudgeSinglePersonRequest request = new ApiBatchJudgeSinglePersonRequest();
            request.setCrowdIds(crowdPackIds);
            request.setUniqueKey(String.valueOf(userNo));
            request.setHitAll(Boolean.FALSE);
            RestResponse<ApiBatchJudgeSinglePersonResponse> response = crowdJudgementFeignClient.batchJudgeSinglePeople(request);
            log.info("CrowdInfoService checkByInsightPlatform request={} response={}", JSON.toJSONString(request), JSON.toJSONString(response));

            if (response != null && Objects.equals(response.getCode(), 200) && response.getData() != null) {
                ApiBatchJudgeSinglePersonResponse data = response.getData();
                if (data.isHitResult()) { // 命中
                    CrowdDetailDo crowdDetailDo = new CrowdDetailDo();
                    crowdDetailDo.setCrowdId(crowdPackIds.get(0));
                    crowdDetailDo.setAppUserIdLast2(null);
                    crowdDetailDo.setAbNum(null);
                    log.info("CrowdInfoService checkByInsightPlatform true, crowdPackIds={} userNo={}", crowdPackIds, userNo);
                    return Pair.of(Boolean.TRUE, crowdDetailDo);
                } else {
                    log.info("CrowdInfoService checkByInsightPlatform false, crowdPackIds={} userNo={}", crowdPackIds, userNo);
                }
            }
        } catch (Exception e) {
            log.error("CrowdInfoService checkByInsightPlatform error", e);
        }

        return Pair.of(Boolean.FALSE, null);
    }

}