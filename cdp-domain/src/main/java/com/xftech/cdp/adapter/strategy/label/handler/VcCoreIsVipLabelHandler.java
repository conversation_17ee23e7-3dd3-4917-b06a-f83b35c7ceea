///**
// * Xinfei.com Inc.
// * Copyright (c) 2004-2024 All Rights Reserved.
// */
//package com.xftech.cdp.adapter.strategy.label.handler;
//
//import com.xftech.cdp.adapter.strategy.label.LabelHandler;
//import com.xftech.cdp.adapter.strategy.label.QueryLabelRequest;
//import com.xftech.cdp.adapter.strategy.label.request.VipCoreTagMatchRequest;
//import com.xftech.cdp.adapter.strategy.label.request.VipCoreTagMatchResponse;
//import com.xftech.cdp.infra.aviator.util.DateTimeUtil;
//import com.xftech.cdp.infra.client.ads.model.resp.AdsLabelResp;
//import com.xftech.cdp.infra.client.increaseamt.RcsProviderClient;
//import com.xftech.cdp.infra.client.increaseamt.model.req.AccessControlQueryReq;
//import com.xftech.cdp.infra.client.increaseamt.model.req.RequestHeader;
//import com.xftech.cdp.infra.client.increaseamt.model.resp.AccessControlResp;
//import com.xftech.cdp.infra.client.increaseamt.model.resp.Response;
//import com.xftech.cdp.infra.client.usercenter.CisService;
//import com.xftech.cdp.infra.client.usercenter.model.BaseCisResp;
//import com.xftech.cdp.infra.client.usercenter.model.RegisterInfoByUserNo;
//import com.xftech.cdp.infra.config.AppConfigService;
//import com.xftech.cdp.infra.exception.NoneException;
//import com.xftech.cdp.infra.utils.JsonUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.http.*;
//import org.springframework.stereotype.Component;
//import org.springframework.web.client.RestTemplate;
//
//import java.util.*;
//
///**
// * <AUTHOR>
// * @version $ BanDistributionTruncationStrategyLabelHandler, v 0.1 2024/7/3 16:56 lingang.han Exp $
// */
//@Slf4j
//@Component
//public class VcCoreIsVipLabelHandler implements LabelHandler {
//    @Autowired
//    private RestTemplate restTemplate;
//    @Autowired
//    private CisService cisService;
//    @Autowired
//    private AppConfigService appConfigService;
//
//    @Override
//    public String getLabel() {
//        return "vc_core_is_vip";
//    }
//
//    @Override
//    public AdsLabelResp execute(String label, QueryLabelRequest batchAdsLabelVO) {
//        List<AdsLabelResp.Param> result = new ArrayList<>();
//
//        for (Long userNo : batchAdsLabelVO.getUserNoList()) {
//            BaseCisResp<RegisterInfoByUserNo.RespDto> respDtoBaseCisResp = cisService.queryRegisterInfoByUserNo(userNo);
//            log.info("[vc_core_is_vip] 查询cis接口req:{},resp:{}", userNo, JsonUtil.toJson(respDtoBaseCisResp));
//            if (respDtoBaseCisResp != null && respDtoBaseCisResp.getData() != null
//                    && StringUtils.isNotBlank(respDtoBaseCisResp.getData().getCustNo()) &&
//                    StringUtils.isNotBlank(respDtoBaseCisResp.getData().getCustNo()) && StringUtils.isNotBlank(respDtoBaseCisResp.getData().getIdCardNumber())) {
//                RegisterInfoByUserNo.RespDto userInfo = respDtoBaseCisResp.getData();
//                VipCoreTagMatchRequest req = new VipCoreTagMatchRequest();
//                req.setUserNo(userNo);
//                req.setTags(Collections.singletonList(getLabel()));
//                VipCoreTagMatchResponse response = request(req);
//
//                if ( Objects.nonNull(response) && response.getCode().equals("1")
//                        && Objects.nonNull(response.getData())) {
//                    Map<String, Object> data = response.getData();
//                    if (data.containsKey(getLabel())) {
//                        Object value = data.get(getLabel());
//                        boolean isVip = Boolean.parseBoolean(value.toString());
//                        result.add(new AdsLabelResp.Param(userNo, null, String.valueOf(isVip)));
//                    }
//                }
//            } else {
//                result.add(new AdsLabelResp.Param(userNo, null, null));
//            }
//
//        }
//        return new AdsLabelResp(label, result);
//    }
//
//
//    private VipCoreTagMatchResponse request(VipCoreTagMatchRequest req) {
//        String uri = appConfigService.getVipCoreTagMatchURL(); //"http://dev-vipcore.testxinfei.cn/tag/match";
//        HttpHeaders headers = new HttpHeaders();
//        headers.setContentType(MediaType.APPLICATION_JSON);
//        HttpEntity<Object> requestEntity = new HttpEntity<>(req, headers);
//        try {
//            ResponseEntity<String> responseEntity = restTemplate.exchange(uri, HttpMethod.POST, requestEntity, String.class);
//            VipCoreTagMatchResponse resp = JsonUtil.parse(responseEntity.getBody(), VipCoreTagMatchResponse.class);
//            log.info("接口请求日志:[{}],url:{}, header:{}, request:{},resp:{}",
//                    getLabel(),
//                    uri, JsonUtil.toJson(headers), JsonUtil.toJson(req), JsonUtil.toJson(responseEntity));
//            if (resp == null || !Objects.equals(responseEntity.getStatusCode(),HttpStatus.OK)) {
//                log.error("接口返回错误码:[{}],url:{},header:{}, request:{},resp:{}",
//                        getLabel(),
//                        uri, JsonUtil.toJson(headers), JsonUtil.toJson(req),
//                        JsonUtil.toJson(responseEntity), NoneException.catError());
//            }
//            return resp;
//        } catch (Exception ex) {
//            log.error("接口异常:[{}],url:{},request:{}", getLabel(), uri, JsonUtil.toJson(requestEntity), ex);
//        }
//        return null;
//    }
//}