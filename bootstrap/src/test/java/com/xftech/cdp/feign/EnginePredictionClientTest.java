package com.xftech.cdp.feign;


import javax.annotation.Resource;

import com.xftech.cdp.BaseTest;
import com.xftech.cdp.distribute.crowd.feign.CrowdFeignClient;
import com.xftech.cdp.distribute.crowd.feign.CrowdJudgementFeignClient;
import com.xftech.cdp.infra.client.ads.model.req.ModelPredictionReq;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.leo.datainsight.client.rr.api.request.ApiCrowdQueryListRequest;
import com.leo.datainsight.client.rr.api.response.ApiCrowdQueryListResponse;
import com.leo.datainsight.client.rr.response.PageResponse;
import com.leo.datainsight.client.rr.response.RestResponse;
import com.leo.datainsightcore.client.rr.request.ApiBatchJudgeSinglePersonRequest;
import com.leo.datainsightcore.client.rr.response.ApiBatchJudgeSinglePersonResponse;
import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * @version $ EnginePredictionClientTest, v 0.1 2025/3/7 15:13 mingwen.zang
 */

public class EnginePredictionClientTest extends BaseTest {
    @Resource
    private EnginePredictionClient enginePredictionClient;
    @Resource
    private CrowdFeignClient crowdFeignClient;
    @Resource
    private CrowdJudgementFeignClient crowdJudgementFeignClient;

    @Test
    public void test() {
        ModelPredictionReq param = ModelPredictionReq.builder()
                .model_name("wangxue_sendAI").biz_data(
                        ModelPredictionReq.BizData.builder()
                                .user_no(3645740L)
                                .build()).build();
        JSONObject jsonObject = enginePredictionClient.modelPrediction(param);
        System.out.println(jsonObject);
        Assert.assertNotNull(jsonObject);
    }

    @Test
    public void testCrowdFeignClient() {
        ApiCrowdQueryListRequest request = new ApiCrowdQueryListRequest();
        request.setPageNo(1);
        request.setPageSize(100);
        RestResponse<PageResponse<ApiCrowdQueryListResponse>> response = crowdFeignClient.queryList(request);
        logger.info("EnginePredictionClientTest request={}, response={}", JSONObject.toJSONString(request), JSONObject.toJSON(response));
    }

    @Test
    public void testCrowdJudgementFeignClient() {
        ApiBatchJudgeSinglePersonRequest request = new ApiBatchJudgeSinglePersonRequest();
        request.setCrowdIds(Lists.newArrayList(1L, 2L, 3L));
        request.setUniqueKey("1014522939");
        request.setHitAll(Boolean.FALSE);
        RestResponse<ApiBatchJudgeSinglePersonResponse> response = crowdJudgementFeignClient.batchJudgeSinglePeople(request);
        logger.info("EnginePredictionClientTest request={}, response={}", JSONObject.toJSONString(request), JSONObject.toJSON(response));
    }
}
