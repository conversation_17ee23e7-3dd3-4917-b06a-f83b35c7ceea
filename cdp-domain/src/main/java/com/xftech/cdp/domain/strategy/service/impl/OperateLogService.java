/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.service.impl;

import com.xftech.base.database.Page;
import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.req.log.QueryOperateLogReq;
import com.xftech.cdp.api.dto.resp.log.OperateLogResp;
import com.xftech.cdp.domain.strategy.repository.OperateLogRepository;
import com.xftech.cdp.infra.aviator.enums.OperateModeEnum;
import com.xftech.cdp.infra.aviator.enums.OperateTypeEnum;
import com.xftech.cdp.infra.repository.cdp.strategy.po.OperateLogDo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $ OperateLogService, v 0.1 2024/4/11 16:06 lingang.han Exp $
 */

@Service
@Slf4j
public class OperateLogService {
    @Autowired
    private OperateLogRepository operateLogRepository;

    public Boolean addOperateLog(OperateLogDo op) {
        try {
            operateLogRepository.insert(op);
        } catch (Exception e) {
            log.error("addOperateLog error", e);
            return false;
        }
        return true;
    }

    public Boolean saveBatchOperateLog(List<OperateLogDo>  operateLogDoList){
        try {
            operateLogRepository.saveBatch(operateLogDoList);
        } catch (Exception e) {
            log.error("addOperateLog error", e);
            return false;
        }
        return true;
    }

    public PageResultResponse<OperateLogResp> queryPage(QueryOperateLogReq queryOperateLogReq) {
        if (Objects.isNull(queryOperateLogReq.getId()) || Objects.isNull(queryOperateLogReq.getModel())) {
            return null;
        }
        Page<OperateLogDo> records = operateLogRepository.selectPage(queryOperateLogReq);
        List<OperateLogDo> list = records.getList();
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        List<OperateLogResp> operateLogRespList = list.stream().map(item -> {
            OperateLogResp operateLogResp = new OperateLogResp();
            BeanUtils.copyProperties(item, operateLogResp);
            OperateTypeEnum operateTypeEnum = OperateTypeEnum.getInstance(item.getType());
            operateLogResp.setType(operateTypeEnum == null ? null : operateTypeEnum.getDesc());
            OperateModeEnum operateModeEnum = OperateModeEnum.getInstance(item.getModel());
            operateLogResp.setModel(operateModeEnum == null ? null : operateModeEnum.getDesc());
            return operateLogResp;
        }).collect(Collectors.toList());
        return new PageResultResponse<>(operateLogRespList, records.getBeginNum(), records.getFetchNum(), records.getTotalNum());
    }
}