/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.infra.utils;

import java.nio.charset.StandardCharsets;
import java.util.zip.CRC32;

/**
 *
 * <AUTHOR>
 * @version $ HashUtils, v 0.1 2023/11/3 19:33 yye.xu Exp $
 */

public class HashUtils {

    public static long crc32Hash(String k) {
        CRC32 crc32 = new CRC32();
        crc32.update(k.getBytes(StandardCharsets.UTF_8));
        return crc32.getValue();
    }
}