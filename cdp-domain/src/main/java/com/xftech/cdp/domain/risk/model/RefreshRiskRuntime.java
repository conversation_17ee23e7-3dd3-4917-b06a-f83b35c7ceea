package com.xftech.cdp.domain.risk.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xftech.cdp.infra.client.ads.model.req.DTRiskUserListReq;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/6/28
 */
@Data
public class RefreshRiskRuntime {
    @JsonProperty("number_range")
    @JSONField(name = "number_range")
    private NumberRange numberRange;

    @JsonProperty("dt_risk_user_list_req")
    @JSONField(name = "dt_risk_user_list_req")
    private DTRiskUserListReq dtRiskUserListReq;
}
