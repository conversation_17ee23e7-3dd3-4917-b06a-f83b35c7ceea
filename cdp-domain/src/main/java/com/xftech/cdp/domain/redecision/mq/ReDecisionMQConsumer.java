package com.xftech.cdp.domain.redecision.mq;

import java.util.Date;

import javax.annotation.Resource;

import com.xinfei.xfframework.common.starter.mq.MqConsumerListener;
import com.xinfei.xfframework.common.starter.mq.multiple.MultipleRocketMQMessageListener;

import com.xftech.cdp.domain.redecision.enums.ReDecisionStatus;
import com.xftech.cdp.domain.strategy.repository.EngineReDecisionDelayRepository;
import com.xftech.cdp.domain.strategy.service.dispatch.event.impl.StrategyEventDispatchServiceImpl;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventVO;
import com.xftech.cdp.infra.repository.cdp.strategy.po.EngineReDecisionDelayDo;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version v1.0 2025/4/9
 * @description ReDecisionMQConsumer
 */
@Component
@Slf4j
@MultipleRocketMQMessageListener(source = "marketpt", topic = "tp_cdp_engine_redecision_delay", consumerGroup = "cg_tp_cdp_engine_redecision_delay", consumeMode = ConsumeMode.CONCURRENTLY)
public class ReDecisionMQConsumer extends MqConsumerListener<String> {

    @Resource
    private StrategyEventDispatchServiceImpl strategyEventDispatchService;

    @Resource
    private EngineReDecisionDelayRepository engineReDecisionDelayRepository;

    @Override
    protected void doMessage(String topic, String message, MessageExt messageExt) {
        log.info("ReDecisionMQConsumer doMessage message={}", message);
        EngineReDecisionDelayDo engineReDecisionDelayDo = null;
        try {
            BizEventVO bizEventVO = JSON.parseObject(message, BizEventVO.class);
            try {
                strategyEventDispatchService.rescreenWithEngine(bizEventVO);
            } catch (Exception ignored) {

            }

            engineReDecisionDelayDo = engineReDecisionDelayRepository.selectByPrimaryKey(bizEventVO.getEngineReDecisionId());
            if (engineReDecisionDelayDo != null) {
                engineReDecisionDelayDo.setStatus(ReDecisionStatus.SUCCESS.getStatus());
                engineReDecisionDelayDo.setUpdatedTime(new Date());
                engineReDecisionDelayRepository.updateByPrimaryKeySelective(engineReDecisionDelayDo);
            }
        } catch (Exception e) {
            if (engineReDecisionDelayDo != null) {
                engineReDecisionDelayDo.setStatus(ReDecisionStatus.FAIL.getStatus());
                engineReDecisionDelayDo.setUpdatedTime(new Date());
                engineReDecisionDelayRepository.updateByPrimaryKeySelective(engineReDecisionDelayDo);
            }
            log.error("ReDecisionMQConsumer doMessage error topic={} messageId={} message={}", topic, messageExt.getMsgId(), message, e);
        }
    }

}