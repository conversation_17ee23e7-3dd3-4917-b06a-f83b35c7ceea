package com.xftech.cdp.api.dto.resp;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xftech.cdp.api.dto.base.TimeFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/2/15
 */
@Getter
@Setter
public class CrowdOneResp extends BaseResp {

    @ApiModelProperty(value = "id")
    public Long id;

    @ApiModelProperty(value = "人群包名称", required = true)
    public String crowdName;

    @ApiModelProperty(value = "excel文件名，仅文件上传可用")
    public String fileName;

    @ApiModelProperty(value = "筛选方式,0:文件上传,1:标签圈选")
    public Integer filterMethod;

    @ApiModelProperty(value = "刷新类型，0:手动刷新，1:例行刷新")
    public Integer refreshType;

    @ApiModelProperty(value = "状态，0: 初始化、1: 刷新中、2: 刷新成功、3: 刷新失败、4: 暂停中、5: 已完成")
    public Integer status;

    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    @ApiModelProperty(value = "生效周期，开始时间，yyyy-MM-dd HH:mm:ss", required = true)
    public LocalDateTime validityBegin;

    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    @ApiModelProperty(value = "生效周期，结束时间，yyyy-MM-dd HH:mm:ss", required = true)
    public LocalDateTime validityEnd;

    @JsonFormat(pattern = TimeFormat.TIME)
    @ApiModelProperty(value = "刷新时间，HH:mm:ss", required = true)
    public LocalTime refreshTime;
    /***** 标签配置 *****/

    @ApiModelProperty(value = "人群圈选列表")
    public List<CrowdOneResp.CrowdLabelPrimary> crowdLabelPrimaries;
    @ApiModelProperty(value = "h5自定义字段")
    private String h5Option;

    @ApiModelProperty(value = "文件记录id")
    private Long uploadLogId;

    @ApiModelProperty(value = "人群分组")
    private Integer groupType;

    @Data
    public static class CrowdLabelPrimary {

        @Min(0)
        @Max(1)
        @ApiModelProperty(value = "人群圈选，0:圈选标签， 1: 排除标签", required = true)
        public Integer labelGroupType;

        @ApiModelProperty(value = "一级标签", required = true)
        public Integer primaryLabel;

        @ApiModelProperty(value = "与上一个一级标签的关系，0:或，1:且", required = true)
        public Integer primaryLabelRelation;

        @ApiModelProperty(value = "执行顺序号", required = true)
        public Integer execIndex;

        @ApiModelProperty(value = "标签选择")
        public List<CrowdOneResp.CrowdLabel> crowdLabels;
    }

    @Data
    public static class CrowdLabel {
        @ApiModelProperty(value = "标签id", required = true)
        public Integer labelId;

        @ApiModelProperty(value = "标签名")
        public String labelName;

        @ApiModelProperty(value = "固定多选标签可选配置")
        public JSONObject configurationOption;

        @ApiModelProperty(value = "标签值", required = true)
        public String labelValue;
        @ApiModelProperty(value = "执行顺序号", required = true)
        public Integer execIndex;
        @ApiModelProperty(value = "与上一个一级标签的关系，0:或，1:且", required = true)
        public Integer labelRelation;
        @ApiModelProperty(value = "可选配置类型, 0:固定单选,1固定多选:,2:输入项,3:时间范围,4:数值范围")
        private Integer labelOptionType;

        @ApiModelProperty(value = "第三层标签")
        private List<SubCrowdLabel> subCrowdLabels;

        private String randomItem;
    }

    @Data
    public static class SubCrowdLabel {
        @ApiModelProperty(value = "标签id", required = true)
        public Integer labelId;

        @ApiModelProperty(value = "标签名")
        public String labelName;

        @ApiModelProperty(value = "固定多选标签可选配置")
        public JSONObject configurationOption;

        @ApiModelProperty(value = "标签值", required = true)
        public String labelValue;
        @ApiModelProperty(value = "执行顺序号", required = true)
        public Integer execIndex;
        @ApiModelProperty(value = "与上一个一级标签的关系，0:或，1:且", required = true)
        public Integer labelRelation;
        @ApiModelProperty(value = "可选配置类型, 0:固定单选,1固定多选:,2:输入项,3:时间范围,4:数值范围")
        private Integer labelOptionType;
        private String randomItem;

    }
}
