<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="strategy">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="detail_description" property="detailDescription"/>
        <result column="ab_test" property="abTest"/>
        <result column="ab_type" property="abType"/>
        <result column="biz_key" property="bizKey"/>
        <result column="send_ruler" property="sendRuler"/>
        <result column="validity_begin" property="validityBegin"/>
        <result column="validity_end" property="validityEnd"/>
        <result column="send_frequency" property="sendFrequency"/>
        <result column="crowd_pack_id" property="crowdPackId"/>
        <result column="market_channel" property="marketChannel"/>
        <result column="status" property="status"/>
        <result column="group_type" property="groupType"/>
        <result column="market_crowd_type" property="marketCrowdType"/>
        <result column="business_type" property="businessType"/>
        <result column="flow_ctrl_id" property="flowCtrlId"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="d_flag" property="dFlag"/>
        <result column="created_op" property="createdOp"/>
        <result column="updated_op" property="updatedOp"/>
        <result column="updated_op_mobile" property="updatedOpMobile"/>
        <result column="type" property="type" />
        <result column="engine_code" property="engineCode" />
        <result column="dispatch_config" property="dispatchConfig" />
        <result column="user_convert" property="userConvert" />
        <result column="flow_no" property="flowNo" />
        <result column="dispatch_type" property="dispatchType" />
        <result column="market_type" property="marketType" />
        <result column="calling_source" property="callingSource" jdbcType="SMALLINT" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name,user_convert, detail_description, ab_test, ab_type, biz_key, send_ruler, validity_begin, validity_end, send_frequency, crowd_pack_id,market_channel,status,group_type,market_crowd_type,business_type,flow_ctrl_id, created_time, updated_time, d_flag, created_op, updated_op,updated_op_mobile,type,engine_code,dispatch_config,flow_no,dispatch_type,
        market_type, calling_source
    </sql>
    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy
    </select>

    <select id="listT0ExecutingStrategy" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy
        where send_ruler = 2
        and status = 1 and type = 0
        and flow_no is null
    </select>

    <select id="getAllStrategyStatusAndCrowdPack" resultMap="BaseResultMap">
        select id, crowd_pack_id, status
        from strategy
        where d_flag = 0
    </select>

    <select id="selectByMarketChannel" resultMap="BaseResultMap">
        select t1.* from strategy t1
        <if test="marketChannel != null and marketChannel.size() > 0">
            inner join strategy_market_channel t2
            on t2.d_flag = 0 and t1.id = t2.strategy_id AND t2.market_channel IN
            <foreach collection="marketChannel" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        where t1.d_flag = 0 and t1.flow_no is null
        <if test="name != null and name != ''">
            <bind name="nameTmp" value='name.replaceAll("%","/%")'/>
            AND t1.name like concat('%',#{nameTmp},'%') escape '/'
        </if>
        <if test="strategyId != null and strategyId != ''">
            AND t1.id = #{strategyId}
        </if>
        <if test="updatedOp != null and updatedOp != ''">
            <bind name="updatedOpTmp" value='updatedOp.replaceAll("%","/%")'/>
            AND t1.updated_op like concat('%',#{updatedOpTmp},'%') escape '/'
        </if>
        <if test="status == null">
            AND t1.status != 6
        </if>
        <if test="status != null">
            AND t1.status = #{status}
        </if>
        <if test="sendRuler != null">
            AND t1.send_ruler = #{sendRuler}
        </if>
        <if test="sendRulerList !=null and sendRulerList.size()>0">
            and t1.send_ruler in
            <foreach collection="sendRulerList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="startTime != null">
            AND t1.updated_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND t1.updated_time &lt;= #{endTime}
        </if>
        <if test="strategyGroupType != null">
            AND t1.group_type = #{strategyGroupType}
        </if>
        <if test="type != null">
            AND t1.type = #{type}
        </if>
        and t1.business_type = #{businessType}
        group by t1.id
        order by t1.updated_time desc
    </select>

    <select id="selectPageByStatusAndCallingSource" resultMap="BaseResultMap">
        select * from strategy
        <where>
            and d_flag = 0 and flow_no is null
            <if test="strategyId != null">
                and id = #{strategyId}
            </if>
            <if test="businessType != null and businessType != ''">
                and business_type = #{businessType}
            </if>
            <if test="callingSource != null and callingSource != ''">
                and calling_source = #{callingSource}
            </if>
            <if test="statusList != null and statusList.size()>0">
                and `status` in
                <foreach collection="statusList" index="index" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy
        where id = #{id,jdbcType=BIGINT}
        and d_flag = 0
    </select>

    <select id="selectByCallingSourceAndBusinessType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy
        where calling_source = #{callingSource,jdbcType=SMALLINT}
        and business_type = #{businessType,jdbcType=VARCHAR}
        and d_flag = 0
    </select>

    <select id="getByNameAndBusinessType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy
        where `name` = #{strategyName} and business_type = #{businessType}
        and d_flag = 0 and flow_no is null
        limit 1
    </select>

    <select id="getEventStrategy" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy
        where d_flag = 0 and send_ruler = 2 and status != 5
         and flow_no is null
    </select>

    <select id="getAllEventStrategy" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy
        where d_flag = 0 and send_ruler = 2
          and flow_no is null
    </select>

    <select id="getEventStrategyByValidity" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy
        where d_flag = 0
        and send_ruler = 2
        and status != -1
        and validity_end &gt;= #{startTime} and validity_begin &lt;= #{endTime}
        and flow_no is null
    </select>

    <select id="getExecutingEventStrategy" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy
        where d_flag = 0 and send_ruler = 2 and status = 1
        and flow_no is null
    </select>

    <select id="getBatchStrategy" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy
        where d_flag = 0 and status != 5 and ( send_ruler = 0 or send_ruler = 1 or send_ruler = 3 )
        and flow_no is null
    </select>

    <select id="offlineStrategyCrowdStatusAlarm" resultMap="BaseResultMap">
        select distinct s.*
        from strategy s
                 inner join strategy_crowd_pack scp on s.id = scp.strategy_id
                 inner join crowd_pack cp on scp.crowd_pack_id = cp.id
                 inner join strategy_market_channel smc on s.id = smc.strategy_id
        where s.validity_end > now()
          and s.status in (0, 2, 3)
          and s.send_ruler != 2
          and s.d_flag = 0
          and cp.status not in (2, 5)
          and smc.send_time &lt;= #{localTime}
          and s.flow_no is null
    </select>

    <select id="getAllNotExpired" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy
        where d_flag = 0
        and validity_begin &lt;= now()
        and validity_end &gt; now()
        and status in (-1, 0, 1, 2, 3)
    </select>

    <update id="deleteByPrimaryKey" parameterType="java.lang.Long">
        update strategy
        set d_flag = 1
        where id = #{id,jdbcType=BIGINT}
    </update>

    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo"
            useGeneratedKeys="true">
        insert into strategy
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="detailDescription != null">detail_description,</if>
            <if test="abTest != null">ab_test,</if>
            <if test="abType != null">ab_type,</if>
            <if test="bizKey != null">biz_key,</if>
            <if test="sendRuler != null">send_ruler,</if>
            <if test="validityBegin != null">validity_begin,</if>
            <if test="validityEnd != null">validity_end,</if>
            <if test="sendFrequency != null">send_frequency,</if>
            <if test="crowdPackId != null">crowd_pack_id,</if>
            <if test="marketChannel != null">market_channel,</if>
            <if test="status != null">status,</if>
            <if test="groupType != null">group_type,</if>
            <if test="marketCrowdType != null">market_crowd_type,</if>
            <if test="flowCtrlId != null">flow_ctrl_id,</if>
            <if test="businessType != null">business_type,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updatedTime != null">updated_time,</if>
            <if test="dFlag != null">d_flag,</if>
            <if test="createdOp != null">created_op,</if>
            <if test="updatedOp != null">updated_op,</if>
            <if test="updatedOpMobile != null">updated_op_mobile,</if>
            <if test="type != null">type,</if>
            <if test="engineCode != null">engine_code,</if>
            <if test="dispatchConfig != null">dispatch_config,</if>
            <if test="userConvert != null">user_convert,</if>
            <if test="flowNo != null">flow_no,</if>
            <if test="dispatchType != null">dispatch_type,</if>
            <if test="marketType != null">market_type,</if>
            <if test="callingSource != null">calling_source,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="name != null">#{name,jdbcType=VARCHAR},</if>
            <if test="detailDescription != null">#{detailDescription,jdbcType=VARCHAR},</if>
            <if test="abTest != null">#{abTest,jdbcType=SMALLINT},</if>
            <if test="abType != null">#{abType,jdbcType=SMALLINT},</if>
            <if test="bizKey != null">#{bizKey,jdbcType=VARCHAR},</if>
            <if test="sendRuler != null">#{sendRuler,jdbcType=SMALLINT},</if>
            <if test="validityBegin != null">#{validityBegin,jdbcType=TIMESTAMP},</if>
            <if test="validityEnd != null">#{validityEnd,jdbcType=TIMESTAMP},</if>
            <if test="sendFrequency != null">#{sendFrequency,jdbcType=OTHER},</if>
            <if test="crowdPackId != null">#{crowdPackId,jdbcType=VARCHAR},</if>
            <if test="marketChannel != null">#{marketChannel,jdbcType=VARCHAR},</if>
            <if test="status != null">#{status,jdbcType=SMALLINT},</if>
            <if test="groupType != null">#{groupType,jdbcType=SMALLINT},</if>
            <if test="marketCrowdType != null">#{marketCrowdType,jdbcType=VARCHAR},</if>
            <if test="flowCtrlId != null">#{flowCtrlId,jdbcType=BIGINT},</if>
            <if test="businessType != null">#{businessType,jdbcType=VARCHAR},</if>
            <if test="createdTime != null">#{createdTime,jdbcType=TIMESTAMP},</if>
            <if test="updatedTime != null">#{updatedTime,jdbcType=TIMESTAMP},</if>
            <if test="dFlag != null">#{dFlag,jdbcType=SMALLINT},</if>
            <if test="createdOp != null">#{createdOp,jdbcType=VARCHAR},</if>
            <if test="updatedOp != null">#{updatedOp,jdbcType=VARCHAR},</if>
            <if test="updatedOpMobile != null">#{updatedOpMobile,jdbcType=VARCHAR},</if>
            <if test="type != null">#{type,jdbcType=SMALLINT},</if>
            <if test="engineCode != null">#{engineCode,jdbcType=VARCHAR},</if>
            <if test="dispatchConfig != null">#{dispatchConfig,jdbcType=VARCHAR},</if>
            <if test="userConvert != null">#{userConvert,jdbcType=SMALLINT},</if>
            <if test="flowNo != null">#{flowNo,jdbcType=VARCHAR},</if>
            <if test="dispatchType != null">#{dispatchType,jdbcType=VARCHAR},</if>
            <if test="marketType != null">#{marketType,jdbcType=SMALLINT},</if>
            <if test="callingSource != null">#{callingSource,jdbcType=SMALLINT},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo">
        update strategy
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="detailDescription != null">
                detail_description = #{detailDescription,jdbcType=VARCHAR},
            </if>
            <if test="abTest != null">
                ab_test = #{abTest,jdbcType=SMALLINT},
            </if>
            <if test="abType != null">
                ab_type = #{abType,jdbcType=SMALLINT},
            </if>
            <if test="userConvert != null">
                user_convert = #{userConvert,jdbcType=SMALLINT},
            </if>
            <if test="bizKey != null">
                biz_key = #{bizKey,jdbcType=VARCHAR},
            </if>
            <if test="sendRuler != null">
                send_ruler = #{sendRuler,jdbcType=SMALLINT},
            </if>
            <if test="validityBegin != null">
                validity_begin = #{validityBegin,jdbcType=TIMESTAMP},
            </if>
            <if test="validityEnd != null">
                validity_end = #{validityEnd,jdbcType=TIMESTAMP},
            </if>
            <if test="sendFrequency != null">
                send_frequency = #{sendFrequency,jdbcType=OTHER},
            </if>
            <if test="crowdPackId != null">
                crowd_pack_id = #{crowdPackId,jdbcType=VARCHAR},
            </if>
            <if test="marketChannel != null">
                market_channel = #{marketChannel,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=SMALLINT},
            </if>
            <if test="groupType != null">
                group_type = #{groupType,jdbcType=SMALLINT},
            </if>
            <if test="marketCrowdType != null">
                market_crowd_type = #{marketCrowdType,jdbcType=VARCHAR},
            </if>
            <if test="flowCtrlId != null">
                flow_ctrl_id = #{flowCtrlId,jdbcType=BIGINT},
            </if>
            <if test="businessType != null">
                business_type = #{businessType,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedTime != null">
                updated_time = #{updatedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="dFlag != null">
                d_flag = #{dFlag,jdbcType=SMALLINT},
            </if>
            <if test="createdOp != null">
                created_op = #{createdOp,jdbcType=VARCHAR},
            </if>
            <if test="updatedOp != null">
                updated_op = #{updatedOp,jdbcType=VARCHAR},
            </if>
            <if test="updatedOpMobile != null">
                updated_op_mobile = #{updatedOpMobile,jdbcType=VARCHAR},
            </if>
            <if test="engineCode != null">
                engine_code = #{engineCode,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=SMALLINT},
            </if>
            <if test="marketType != null">
                market_type = #{marketType,jdbcType=SMALLINT},
            </if>
            <if test="callingSource != null">
                calling_source = #{callingSource,jdbcType=SMALLINT},
            </if>
            dispatch_config = #{dispatchConfig,jdbcType=VARCHAR}
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="getByNameLike" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy
        where name like concat('%',#{strategyName},'%')
        and d_flag = 0
    </select>


    <select id="getByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy
        where id in
        <foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and d_flag = 0
    </select>

    <!-- 查询执行中的实时策略ID列表 -->
    <select id="getExecutingEventStrategyIds" resultType="Long">
        select id
        from strategy
        where d_flag = 0
          and send_ruler = 2
          and status = 1
          and flow_no is null
    </select>

    <select id="selectExistOfflineStrategy" resultMap="BaseResultMap">
        select *
        from strategy
        where d_flag = 0
          and send_ruler = 1
          and flow_ctrl_id is null
          and flow_no is null
    </select>

    <select id="getStrategyByExecTime" resultMap="BaseResultMap">
        select distinct s.*
        from strategy s
                 inner join strategy_exec_log sel on s.id = sel.strategy_id
        where s.d_flag = 0
          and s.send_ruler != 2
          and date(sel.exec_time) = #{date}
          and sel.exec_status != 0
    </select>

    <select id="getStrategyList" resultMap="BaseResultMap">
        select *
        from strategy
        where d_flag = 0 and flow_no is null
        <if test="sendRulerList !=null and sendRulerList.size()>0">
            and send_ruler in
            <foreach collection="sendRulerList" item="sendRuler" open="(" close=")" separator=",">
                #{sendRuler}
            </foreach>
        </if>
        <if test="statusList !=null and statusList.size()>0">
            and status in
            <foreach collection="statusList" item="status" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
    </select>

    <select id="getAllStrategyList" resultMap="BaseResultMap">
        select *
        from strategy
        where d_flag = 0
        <if test="sendRulerList !=null and sendRulerList.size()>0">
            and send_ruler in
            <foreach collection="sendRulerList" item="sendRuler" open="(" close=")" separator=",">
                #{sendRuler}
            </foreach>
        </if>
        <if test="statusList !=null and statusList.size()>0">
            and status in
            <foreach collection="statusList" item="status" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
    </select>

</mapper>
