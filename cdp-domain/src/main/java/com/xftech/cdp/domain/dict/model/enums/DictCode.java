package com.xftech.cdp.domain.dict.model.enums;

import lombok.Getter;

/**
 * <p>
 * 业务异常枚举类定义
 * <p/>
 *
 * <AUTHOR>
 * @since 2021/7/11 17:45
 */
@Getter
public enum DictCode {

    STRATEGY_STATUS,
    STRATEGY_GROUP_TYPE,
    STRATEGY_FREQUENCY,
    STRATEGY_EXEC_STATUS,
    CROWD_STATUS,
    CROWD_LABEL_GROUP_TYPE,
    CROWD_FILTER_METHOD,
    CROWD_EXEC_RESULT,
    CROWD_REFRESH_TYPE,
    CROWD_LABEL_RELATION,
    LABEL_OPTION_TYPE,
    LABEL_PRIMARY_LABEL,
    LABEL_SECONDARY_LABEL,
    FLOW_CTRL_CHANNEL_TYPE,
    FLOW_CTRL_RULE_STATUS,
    STRATEGY_RULE_TYPE,
    CROWD_OPERATE_TYPE
    ;


}
