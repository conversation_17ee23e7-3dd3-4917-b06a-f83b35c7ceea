/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.domain.service;

import com.xftech.cdp.BaseTest;
import com.xftech.cdp.domain.strategy.model.dto.EventFlcConfig;
import com.xftech.cdp.domain.strategy.model.dto.LoanFinalFailedReasonDto;
import com.xftech.cdp.domain.strategy.model.enums.StrategyMarketChannelEnum;
import com.xftech.cdp.infra.config.AppConfigService;
import com.xftech.cdp.infra.utils.JsonUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ AppConfigServiceTest, v 0.1 2023/9/18 16:34 yye.xu Exp $
 */

public class AppConfigServiceTest extends BaseTest {

    @Autowired
    private AppConfigService configService;
    @Test
    public void test() {
        LoanFinalFailedReasonDto loanFinalFailedReasonDto = configService
                .getLoanFinalFailedFailReason();
        System.out.println(JsonUtil.toJson(loanFinalFailedReasonDto.getReasonList()));
        System.out.println(JsonUtil.toJson(configService.getLoanFinalFailedFundFailReasonDetail()));

        List<String> ss = configService.getIgnoreDispatchErrorMsgAlarm(StrategyMarketChannelEnum.SMS);
        System.out.println(JsonUtil.toJson(ss));
        if (ss.contains("模板不能为空")){
            System.out.println("nu");
        }
        List<String> ss2 = configService.getIgnoreDispatchReponseFailedCodesAlarm(StrategyMarketChannelEnum.SMS);
        System.out.println(JsonUtil.toJson(ss2));

        System.out.println(configService.getSingleDispatchFlcLockSwitch(StrategyMarketChannelEnum.SMS));
        System.out.println(configService.getDispatchFlcLockPerWaitMills());

        EventFlcConfig eventFlcConfig  = configService.getEventFlcConfig();
        System.out.println("1=" + eventFlcConfig.getLimitSeconds("Start"));
        System.out.println("2=" + eventFlcConfig.getLimitSeconds("1"));
    }

}
