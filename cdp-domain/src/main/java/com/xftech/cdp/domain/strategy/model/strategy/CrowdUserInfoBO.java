/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.model.strategy;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xftech.cdp.api.dto.base.TimeFormat;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import lombok.Data;

import java.time.LocalDateTime;

/**
 *
 * <AUTHOR>
 * @version $ CrowdUserInfoBO, v 0.1 2024/1/2 8:27 yye.xu Exp $
 */

@Data
public class CrowdUserInfoBO {
    /**
     * APP
     */
    private String app;

    private String innerApp;

    private String mobile;

    private String abNum;

    private Integer appUserIdLast2;

    private Long crowdId;

    /**
     * 注册时间
     */
    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    private LocalDateTime registerTime;

    public static CrowdUserInfoBO convert(CrowdDetailDo crowdDetailDo) {
        CrowdUserInfoBO crowdUserInfoBO = new CrowdUserInfoBO();
        crowdUserInfoBO.setApp(crowdDetailDo.getApp());
        crowdUserInfoBO.setInnerApp(crowdDetailDo.getInnerApp());
        crowdUserInfoBO.setMobile(crowdDetailDo.getMobile());
        crowdUserInfoBO.setAbNum(crowdDetailDo.getAbNum());
        crowdUserInfoBO.setAppUserIdLast2(crowdDetailDo.getAppUserIdLast2());
        crowdUserInfoBO.setRegisterTime(crowdDetailDo.getRegisterTime());
        crowdUserInfoBO.setCrowdId(crowdDetailDo.getCrowdId());
        return crowdUserInfoBO;
    }
}