/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.service.impl;

import com.xftech.cdp.domain.strategy.exception.StrategyException;
import com.xftech.cdp.feign.GoodsFeignClient;
import com.xftech.cdp.feign.model.GoodsDetail;
import com.xftech.cdp.feign.model.GoodsList;
import com.xftech.cdp.feign.model.requset.GoodsListRequest;
import com.xftech.cdp.feign.model.response.GoodsResponse;
import com.xftech.cdp.infra.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version $ GoodsServiceImol, v 0.1 2024/5/7 13:56 lingang.han Exp $
 */

@Service
@Slf4j
public class GoodsServiceImpl {

    @Autowired
    private GoodsFeignClient goodsFeignClient;

    private static final String ua = "xyf-cdp";

    public GoodsDetail getGoodsDetailByGoodsId(Long goodsId) {
        if (goodsId == null) {
            throw new StrategyException("goodsId do not null");
        }
        GoodsListRequest goodsListRequest = new GoodsListRequest(goodsId, 1, 1);
        GoodsResponse<GoodsList> goodsListResponse = goodsFeignClient.queryGoodsList(goodsListRequest, ua);
        log.info("goods url:/goods/queryGoodsList req:{},resp:{}", goodsListRequest, goodsListResponse);

        if (goodsListResponse == null || !goodsListResponse.isSuccess()) {
            String errorMessage = goodsListResponse != null ? "goods url:/goods/queryGoodsList return error=" + goodsListResponse.getMessage() : "goods url:/goods/queryGoodsList return null";
            throw new StrategyException(errorMessage);
        }

        if (goodsListResponse.getData() != null && goodsListResponse.getData().getList() != null && goodsListResponse.getData().getList().size() == 1) {
            return goodsListResponse.getData().getList().get(0);
        } else {
            String message = "goods url:/goods/queryGoodsList return error req:" + JsonUtil.toJson(goodsListRequest) + ",resp:{}" + JsonUtil.toJson(goodsListResponse);
            throw new StrategyException(message);
        }
    }
}