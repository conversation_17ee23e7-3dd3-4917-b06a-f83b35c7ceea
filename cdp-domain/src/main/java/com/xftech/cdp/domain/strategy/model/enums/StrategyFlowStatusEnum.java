package com.xftech.cdp.domain.strategy.model.enums;

import com.xftech.cdp.domain.strategy.exception.StrategyException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
@Getter
@AllArgsConstructor
public enum StrategyFlowStatusEnum {
    DRAFT(-1, "待发布"),
    INIT(0, "已发布"),
    EXECUTING(1, "执行中"),
    PAUSING(4, "暂停中"),
    ENDED(5, "已结束"),

    ;

    private final int code;
    private final String description;

    public static StrategyFlowStatusEnum getInstance(Integer code) {
        for (StrategyFlowStatusEnum flowStatusEnum : StrategyFlowStatusEnum.values()) {
            if (Objects.equals(flowStatusEnum.getCode(), code)) {
                return flowStatusEnum;
            }
        }
        throw new StrategyException(String.format("画布状态异常，状态：%s", code));
    }

    public static List<Integer> getDisabledStatus() {
        return Arrays.asList(ENDED.getCode());
    }

    public static List<Integer> getCanChangedStatus() {
        return Arrays.asList(INIT.getCode(), EXECUTING.getCode(), PAUSING.getCode());
    }

    public static List<Integer> getExecutingStatus() {
        return Arrays.asList(INIT.getCode(), EXECUTING.getCode());
    }

    public static boolean isRunning(Short code) {
        return getExecutingStatus().contains(code
                .intValue());
    }
}