/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.req.label;

import com.xftech.cdp.api.dto.base.PageRequestDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version $ LabelConfigListReq, v 0.1 2024/6/20 15:16 lingang.han Exp $
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LabelConfigListReq extends PageRequestDto {

    @ApiModelProperty(value = "元数据标签code")
    private String metaLabelCode;

    @ApiModelProperty(value = "元数据标签名称")
    private String metaLabelName;

    @ApiModelProperty(value = "麻雀上线状态")
    private Integer online;

    @ApiModelProperty(value = "麻雀一级标签code")
    private Integer firstLevelLabel;

    @ApiModelProperty(value = "麻雀二级标签code")
    private Integer secondLevelLabel;

}