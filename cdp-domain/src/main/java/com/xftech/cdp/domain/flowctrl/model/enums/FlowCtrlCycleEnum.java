package com.xftech.cdp.domain.flowctrl.model.enums;

import com.xftech.cdp.infra.exception.BizException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @<NAME_EMAIL>
 * @date 2023/3/29 9:17
 */
@Deprecated
@Getter
@AllArgsConstructor
public enum FlowCtrlCycleEnum {
    /**
     * 日触达次数类型
     */
    DAY_COUNT(1),
    /**
     * 周触达次数类型
     */
    WEEK_COUNT(2),
    /**
     * 月触达次数类型
     */
    MONTH_COUNT(3);

    private final Integer type;

    public static FlowCtrlCycleEnum getInstance(Integer type) {
        for (FlowCtrlCycleEnum cycleEnum : FlowCtrlCycleEnum.values()) {
            if (Objects.equals(type, cycleEnum.getType())) {
                return cycleEnum;
            }
        }
        throw new BizException(String.format("流控触达次数类型异常：%s", type));
    }
}
