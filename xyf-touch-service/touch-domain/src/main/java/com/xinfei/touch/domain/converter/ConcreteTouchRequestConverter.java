package com.xinfei.touch.domain.converter;

import com.xinfei.touch.domain.model.TouchChannel;
import com.xinfei.touch.domain.model.TouchType;
import com.xinfei.touch.domain.model.unified.*;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 具体的触达请求转换器
 * 使用实际的类型进行转换，需要引入外部依赖的具体类型
 * 
 * 注意：这个类需要在实际项目中引入cdp-domain模块的依赖才能编译通过
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-23
 */
@Component
public class ConcreteTouchRequestConverter {
    
    /**
     * 转换T0普通触达参数（execSend方法）
     * 
     * 完整的参数映射：
     * DispatchDto dispatchDto -> 策略信息、业务信息、渠道配置
     * CrowdDetailDo crowdDetail -> 用户信息
     * StrategyMarketChannelEnum channelEnum -> 渠道类型
     * StrategyMarketChannelDo channelDo -> 渠道配置
     * BizEventVO bizEvent -> 业务事件信息
     */
    /*
    public TouchRequest convertFromExecSend(DispatchDto dispatchDto, 
                                          CrowdDetailDo crowdDetail, 
                                          StrategyMarketChannelEnum channelEnum, 
                                          StrategyMarketChannelDo channelDo, 
                                          BizEventVO bizEvent) {
        TouchRequest request = new TouchRequest();
        
        // ===== 基础信息 =====
        request.setRequestId(UUID.randomUUID().toString());
        request.setTouchType(TouchType.REALTIME_NORMAL);
        request.setTouchMode(TouchMode.SINGLE);
        request.setTimestamp(System.currentTimeMillis());
        
        // ===== 策略信息 (源于DispatchDto) =====
        request.setStrategyId(dispatchDto.getStrategyId());
        request.setStrategyExecId(dispatchDto.getStrategyExecId());
        request.setStrategyGroupId(dispatchDto.getStrategyGroupId());
        request.setStrategyGroupName(dispatchDto.getStrategyGroupName());
        request.setStrategyChannelId(dispatchDto.getStrategyChannelId());
        request.setStrategyChannelXxlJobId(dispatchDto.getStrategyChannelXxlJobId());
        request.setStrategyChannel(dispatchDto.getStrategyChannel());
        request.setStrategyExecLogId(dispatchDto.getStrategyExecLogId());
        request.setStrategyExecLogRetryId(dispatchDto.getStrategyExecLogRetryId());
        request.setDetailTableNo(dispatchDto.getDetailTableNo());
        request.setMessageId(dispatchDto.getMessageId());
        request.setTemplateId(dispatchDto.getStrategyMarketChannelTemplateId());
        
        // ===== 渠道信息 (源于StrategyMarketChannelEnum + StrategyMarketChannelDo) =====
        request.setChannel(convertStrategyChannelToTouchChannel(channelEnum));
        if (channelDo != null) {
            request.setChannelExtInfo(channelDo.getExtInfo());
        }
        
        // ===== 用户信息 (源于CrowdDetailDo) =====
        request.setUserInfo(convertCrowdDetailToTouchUserInfo(crowdDetail));
        
        // ===== 业务信息 (源于DispatchDto + BizEventVO) =====
        request.setBizEventType(dispatchDto.getBizEventType());
        request.setEventParamMap(dispatchDto.getEventParamMap());
        request.setSignatureKey(dispatchDto.getSignatureKey());
        request.setActivityId(dispatchDto.getActivityId());
        request.setNameTypeId(dispatchDto.getNameTypeId());
        request.setDispatchType(dispatchDto.getDispatchType());
        request.setBizType(dispatchDto.getBizType());
        
        // 特殊参数对象
        request.setIncreaseAmtParamDto(dispatchDto.getIncreaseAmtParamDto());
        request.setAiProntoChannelDto(dispatchDto.getAiProntoChannelDto());
        
        if (bizEvent != null) {
            request.setBizEventData(convertBizEventToMap(bizEvent));
            if (bizEvent.getTraceId() != null) {
                request.setTraceId(bizEvent.getTraceId());
            }
        }
        
        // ===== 触达配置 =====
        request.setTouchConfig(createTouchConfig(dispatchDto, channelDo));
        
        // ===== 流控配置 =====
        request.setFlowControlConfig(FlowControlConfig.createT0Config());
        
        return request;
    }
    */
    
    /**
     * 转换T0引擎触达参数（marketingSend方法 - T0场景）
     */
    /*
    public TouchRequest convertFromT0MarketingSend(DispatchDto dispatchDto,
                                                 CrowdDetailDo crowdDetailDo,
                                                 StrategyMarketChannelEnum channelEnum,
                                                 String groupName,
                                                 Map<String, Object> detailInfo,
                                                 BizEventVO bizEventVO) {
        TouchRequest request = new TouchRequest();
        
        // ===== 基础信息 =====
        request.setRequestId(UUID.randomUUID().toString());
        request.setTouchType(TouchType.REALTIME_ENGINE);
        request.setTouchMode(TouchMode.SINGLE);
        request.setTimestamp(System.currentTimeMillis());
        
        // ===== 策略信息 (源于DispatchDto) =====
        setStrategyInfoFromDispatchDto(request, dispatchDto);
        
        // ===== 渠道信息 (源于StrategyMarketChannelEnum) =====
        request.setChannel(convertStrategyChannelToTouchChannel(channelEnum));
        
        // ===== 用户信息 (源于CrowdDetailDo) =====
        request.setUserInfo(convertCrowdDetailToTouchUserInfo(crowdDetailDo));
        
        // ===== 引擎信息 (源于groupName + detailInfo + BizEventVO) =====
        request.setEngineGroupName(groupName);
        request.setEngineDetail(detailInfo);
        
        if (bizEventVO != null) {
            request.setBizEventData(convertBizEventToMap(bizEventVO));
            request.setEngineCode(bizEventVO.getEngineCode());
            if (bizEventVO.getTraceId() != null) {
                request.setTraceId(bizEventVO.getTraceId());
            }
        }
        
        // ===== 触达配置 =====
        request.setTouchConfig(createTouchConfig(dispatchDto, null));
        
        // ===== 流控配置 =====
        request.setFlowControlConfig(FlowControlConfig.createT0EngineConfig());
        
        return request;
    }
    */
    
    /**
     * 转换离线引擎触达参数（marketingSend方法 - 离线场景）
     */
    /*
    public TouchRequest convertFromOfflineMarketingSend(DispatchDto dispatchDto,
                                                      CrowdDetailDo crowdDetailDo,
                                                      StrategyMarketChannelEnum channelEnum,
                                                      String groupName,
                                                      Map<String, Object> detailInfo) {
        TouchRequest request = new TouchRequest();
        
        // ===== 基础信息 =====
        request.setRequestId(UUID.randomUUID().toString());
        request.setTouchType(TouchType.OFFLINE_ENGINE);
        request.setTouchMode(TouchMode.SINGLE);
        request.setTimestamp(System.currentTimeMillis());
        
        // ===== 策略信息 (源于DispatchDto) =====
        setStrategyInfoFromDispatchDto(request, dispatchDto);
        
        // ===== 渠道信息 (源于StrategyMarketChannelEnum) =====
        request.setChannel(convertStrategyChannelToTouchChannel(channelEnum));
        
        // ===== 用户信息 (源于CrowdDetailDo) =====
        request.setUserInfo(convertCrowdDetailToTouchUserInfo(crowdDetailDo));
        
        // ===== 引擎信息 (源于groupName + detailInfo) =====
        request.setEngineGroupName(groupName);
        request.setEngineDetail(detailInfo);
        
        // ===== 触达配置 =====
        request.setTouchConfig(createTouchConfig(dispatchDto, null));
        
        // ===== 流控配置 =====
        request.setFlowControlConfig(FlowControlConfig.createOfflineEngineConfig());
        
        return request;
    }
    */
    
    /**
     * 转换离线普通触达参数（dispatchHandler方法）
     */
    /*
    public TouchRequest convertFromDispatchHandler(StrategyContext strategyContext,
                                                 String app,
                                                 String innerApp,
                                                 List<CrowdDetailDo> userBatch,
                                                 List<Object> templateParams) {
        TouchRequest request = new TouchRequest();
        
        // ===== 基础信息 =====
        request.setRequestId(UUID.randomUUID().toString());
        request.setTouchType(TouchType.OFFLINE_NORMAL);
        request.setTouchMode(TouchMode.BATCH);
        request.setTimestamp(System.currentTimeMillis());
        
        // ===== 策略信息 (源于StrategyContext) =====
        request.setStrategyId(strategyContext.getStrategyDo().getId());
        request.setStrategyGroupId(strategyContext.getStrategyGroupDo().getId());
        request.setStrategyGroupName(strategyContext.getStrategyGroupDo().getName());
        request.setStrategyChannelId(strategyContext.getStrategyMarketChannelDo().getId());
        request.setDetailTableNo(strategyContext.getDetailTableNo());
        
        // 渠道信息
        request.setChannel(convertStrategyChannelToTouchChannel(
            strategyContext.getStrategyMarketChannelDo().getMarketChannel()));
        request.setTemplateId(strategyContext.getStrategyMarketChannelDo().getTemplateId());
        
        // ===== 用户信息列表 (源于userBatch) =====
        List<TouchUserInfo> touchUserList = userBatch.stream()
                .map(this::convertCrowdDetailToTouchUserInfo)
                .collect(Collectors.toList());
        request.setUserList(touchUserList);
        
        // ===== 批量处理信息 =====
        String batchNo = generateBatchNo();
        BatchInfo batchInfo = BatchInfo.create(batchNo, userBatch.size(), userBatch.size());
        batchInfo.setDetailTableNo(strategyContext.getDetailTableNo());
        request.setBatchInfo(batchInfo);
        
        // ===== 模板参数 (源于templateParams) =====
        request.setTemplateParams(convertTemplateParams(templateParams));
        
        // ===== 触达配置 =====
        request.setTouchConfig(createTouchConfigFromContext(strategyContext));
        
        // ===== 流控配置 (源于StrategyContext.flowCtrlList) =====
        request.setFlowControlConfig(createFlowControlConfigFromContext(strategyContext));
        
        return request;
    }
    */
    
    // ========== 私有辅助方法 ==========
    
    /**
     * 从DispatchDto设置策略信息
     */
    /*
    private void setStrategyInfoFromDispatchDto(TouchRequest request, DispatchDto dispatchDto) {
        request.setStrategyId(dispatchDto.getStrategyId());
        request.setStrategyExecId(dispatchDto.getStrategyExecId());
        request.setStrategyGroupId(dispatchDto.getStrategyGroupId());
        request.setStrategyGroupName(dispatchDto.getStrategyGroupName());
        request.setStrategyChannelId(dispatchDto.getStrategyChannelId());
        request.setStrategyChannelXxlJobId(dispatchDto.getStrategyChannelXxlJobId());
        request.setStrategyChannel(dispatchDto.getStrategyChannel());
        request.setStrategyExecLogId(dispatchDto.getStrategyExecLogId());
        request.setStrategyExecLogRetryId(dispatchDto.getStrategyExecLogRetryId());
        request.setDetailTableNo(dispatchDto.getDetailTableNo());
        request.setMessageId(dispatchDto.getMessageId());
        request.setTemplateId(dispatchDto.getStrategyMarketChannelTemplateId());
        request.setBizEventType(dispatchDto.getBizEventType());
        request.setEventParamMap(dispatchDto.getEventParamMap());
        request.setSignatureKey(dispatchDto.getSignatureKey());
        request.setActivityId(dispatchDto.getActivityId());
        request.setNameTypeId(dispatchDto.getNameTypeId());
        request.setDispatchType(dispatchDto.getDispatchType());
        request.setBizType(dispatchDto.getBizType());
        request.setIncreaseAmtParamDto(dispatchDto.getIncreaseAmtParamDto());
        request.setAiProntoChannelDto(dispatchDto.getAiProntoChannelDto());
    }
    */
    
    /**
     * 转换CrowdDetailDo到TouchUserInfo
     */
    /*
    private TouchUserInfo convertCrowdDetailToTouchUserInfo(CrowdDetailDo crowdDetail) {
        TouchUserInfo userInfo = new TouchUserInfo();
        
        userInfo.setUserId(crowdDetail.getUserId());
        userInfo.setMobile(crowdDetail.getMobile());
        userInfo.setApp(crowdDetail.getApp());
        userInfo.setInnerApp(crowdDetail.getInnerApp());
        userInfo.setDeviceId(crowdDetail.getDeviceId());
        userInfo.setAbNum(crowdDetail.getAbNum());
        userInfo.setAppUserIdLast2(crowdDetail.getAppUserIdLast2());
        userInfo.setCrowdId(crowdDetail.getCrowdId());
        userInfo.setCrowdExecLogId(crowdDetail.getCrowdExecLogId());
        userInfo.setRegisterTime(crowdDetail.getRegisterTime());
        userInfo.setTableName(crowdDetail.getTableName());
        userInfo.setFlowNo(crowdDetail.getFlowNo());
        userInfo.setPreStrategyId(crowdDetail.getPreStrategyId());
        userInfo.setFlowBatchNo(crowdDetail.getFlowBatchNo());
        userInfo.setNextStrategyId(crowdDetail.getNextStrategyId());
        userInfo.setStrategyId(crowdDetail.getStrategyId());
        userInfo.setOssRunVersion(crowdDetail.getOssRunVersion());
        
        return userInfo;
    }
    */
    
    /**
     * 转换StrategyMarketChannelEnum到TouchChannel
     */
    /*
    private TouchChannel convertStrategyChannelToTouchChannel(StrategyMarketChannelEnum channelEnum) {
        if (channelEnum == null) {
            return TouchChannel.SMS;
        }
        
        switch (channelEnum) {
            case SMS:
                return TouchChannel.SMS;
            case VOICE:
                return TouchChannel.VOICE;
            case VOICE_NEW:
                return TouchChannel.VOICE_NEW;
            case SALE_TICKET:
                return TouchChannel.COUPON;
            case PUSH:
                return TouchChannel.PUSH;
            case AI_PRONTO:
                return TouchChannel.AI_PRONTO;
            case INCREASE_AMOUNT:
                return TouchChannel.INCREASE_AMOUNT;
            case APP_BANNER:
                return TouchChannel.APP_BANNER;
            case X_DAY_INTEREST_FREE:
                return TouchChannel.X_DAY_INTEREST_FREE;
            case LIFE_RIGHTS:
                return TouchChannel.LIFE_RIGHTS;
            case NONE:
                return TouchChannel.NONE;
            default:
                return TouchChannel.SMS;
        }
    }
    */
    
    /**
     * 转换BizEventVO到Map
     */
    /*
    private Map<String, Object> convertBizEventToMap(BizEventVO bizEvent) {
        Map<String, Object> eventData = new HashMap<>();
        eventData.put("appUserId", bizEvent.getAppUserId());
        eventData.put("mobile", bizEvent.getMobile());
        eventData.put("app", bizEvent.getApp());
        eventData.put("innerApp", bizEvent.getInnerApp());
        eventData.put("bizEventType", bizEvent.getBizEventType());
        eventData.put("engineCode", bizEvent.getEngineCode());
        eventData.put("strategyId", bizEvent.getStrategyId());
        eventData.put("traceId", bizEvent.getTraceId());
        // 添加其他需要的字段
        return eventData;
    }
    */
    
    /**
     * 创建触达配置
     */
    /*
    private TouchConfig createTouchConfig(DispatchDto dispatchDto, StrategyMarketChannelDo channelDo) {
        TouchConfig config = TouchConfig.createDefault();
        
        if (dispatchDto.getDispatchType() != null) {
            config.setDispatchType(dispatchDto.getDispatchType());
        }
        
        if (channelDo != null && channelDo.getExtInfo() != null) {
            Map<String, Object> channelConfig = new HashMap<>();
            channelConfig.put("extInfo", channelDo.getExtInfo());
            config.setChannelConfig(channelConfig);
        }
        
        return config;
    }
    */
    
    /**
     * 从StrategyContext创建触达配置
     */
    /*
    private TouchConfig createTouchConfigFromContext(StrategyContext strategyContext) {
        TouchConfig config = TouchConfig.createDefault();
        
        // 根据策略上下文设置特定配置
        if (strategyContext.getStrategyMarketChannelDo() != null) {
            Map<String, Object> channelConfig = new HashMap<>();
            channelConfig.put("extInfo", strategyContext.getStrategyMarketChannelDo().getExtInfo());
            config.setChannelConfig(channelConfig);
        }
        
        return config;
    }
    */
    
    /**
     * 从StrategyContext创建流控配置
     */
    /*
    private FlowControlConfig createFlowControlConfigFromContext(StrategyContext strategyContext) {
        FlowControlConfig config = FlowControlConfig.createOfflineBatchConfig();
        
        if (strategyContext.getFlowCtrlList() != null && !strategyContext.getFlowCtrlList().isEmpty()) {
            // 转换流控规则列表
            List<FlowControlRule> rules = strategyContext.getFlowCtrlList().stream()
                    .map(this::convertFlowCtrlDoToRule)
                    .collect(Collectors.toList());
            config.setRules(rules);
        }
        
        return config;
    }
    */
    
    /**
     * 转换FlowCtrlDo到FlowControlRule
     */
    /*
    private FlowControlRule convertFlowCtrlDoToRule(FlowCtrlDo flowCtrlDo) {
        FlowControlRule rule = new FlowControlRule();
        rule.setRuleId(flowCtrlDo.getId());
        rule.setLimitTimes(flowCtrlDo.getLimitTimes());
        rule.setLimitSeconds(flowCtrlDo.getLimitSeconds());
        // 设置其他字段
        return rule;
    }
    */
    
    /**
     * 转换模板参数
     */
    private Map<String, Object> convertTemplateParams(List<Object> templateParams) {
        Map<String, Object> params = new HashMap<>();
        if (templateParams != null) {
            for (int i = 0; i < templateParams.size(); i++) {
                params.put("param" + i, templateParams.get(i));
            }
        }
        return params;
    }
    
    /**
     * 生成批次号
     */
    private String generateBatchNo() {
        return "BATCH_" + System.currentTimeMillis() + "_" + 
               UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }
}
