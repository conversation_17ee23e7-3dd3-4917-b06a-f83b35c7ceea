package com.xftech.cdp.infra.rocketmq.judge.model;

import java.util.Date;

import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0 2024/9/23
 * @description CredentialStuffingVO
 */
@Data
public class CredentialStuffingVO {

    /**
     * 渠道撞库入参，可能是：
     * 1. 手机号md5
     * 2. 身份证md5
     * 3. 手机号+身份证号md5
     * eg:727d010f1327078fe1b213ef6cee3b08
     */
    private String mask_md5;
    /**
     * 撞库类型
     * 1-手机号md5
     * 2-身份证md5
     * 3-手机号+身份证md5
     * 4-手机号或身份证号md5
     */
    private Integer md5_type;
    /**
     * 用户类型 1：新用户； 2、无预授信记录；3、有预授信记录，无激活成功记录 ；4、有预授信记录，有激活成功记录
     */
    private Integer user_type;
    /**
     * 是否存在确认借款记录（0不存在、1存在）
     */
    private Integer is_loan;
    /**
     * 0：无已结清记录
     * 2：有已结清记录无在贷，最后一笔结清时间到当前时间小于n天，结清渠道不符
     * 3：有已结清记录,最后一笔结清时间到当前时间小于90天，渠道符合
     * 4：有已结清记录,最后一笔结清时间到当前时间大于90天，渠道符合
     * 5：有已结清记录,最后一笔结清时间到当前时间大于90天,渠道不符
     */
    private Integer loan_type;
    /**
     * 是否有放款中或还款中订单（0：没有在贷订单、1：有在贷订单）
     */
    private Integer is_loaning;
    /**
     * 撞库结果,0：撞库拒绝，1：撞库通过
     */
    private Integer result;
    /**
     * 进件次数是否达到上限 1-已达到上限；0-未达到上限
     */
    private Integer approval_num_limit;
    /**
     * 用户utm source
     */
    private String utm_source;
    /**
     * 撞库渠道utm source
     */
    private String check_utm_source;
    /**
     * 撞库渠道app
     */
    private String register_app;
    /**
     * 渠道配置的撞库规则
     */
    private String check_rule;
    /**
     * 是否加贷订单（0：不是、1：加贷订单）
     */
    private Integer is_again_loan;
    ///**
    // * 用户号，老客才会有值
    // */
    //private Long user_id;
    /**
     * 还款类型 0：没有还款中订单  1:有还款中订单且有本渠道还款中订单 2：有还款中订单且没有本渠道还款中订单
     * 默认是0
     */
    private Integer unpaid_type;
    /**
     * 0：本渠道不可以在贷复贷；1：本渠道可以在贷复贷
     */
    private String inner_app_type;
    /**
     * 是否有放款中订单，0：没有放款中订单  1：有放款中订单
     */
    private Integer remit_status;
    /**
     * 请求内部服务接口结果枚举，多个用逗号分隔：checkUserInfo_succ:请求check-user-info接口正常;checkUserInfo_timeout:请求check-user-info超时
     */
    private String inner_api_req_status;
    /**
     * 内部服务API熔断状态枚举，多个用逗号分隔 checkUserInfo_succ:正常;checkUserInfo_fusing:熔断
     */
    private String inner_api_fusing_status;
    /**
     * 撞库请求发起时间
     */
    private Date request_time;
    /**
     * 渠道撞库inner-app
     */
    private String inner_app;
    /**
     * userNo credit.id
     */
    private Long user_no;
}
