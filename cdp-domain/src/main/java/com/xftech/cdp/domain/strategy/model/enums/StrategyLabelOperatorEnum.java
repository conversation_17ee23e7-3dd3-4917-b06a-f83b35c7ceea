package com.xftech.cdp.domain.strategy.model.enums;

import com.google.common.collect.Maps;

import java.util.Map;

/**
 * 策略标签操作符
 *
 * @<NAME_EMAIL>
 */
public enum StrategyLabelOperatorEnum {
    /**
     *
     */
    GE(">=", "大于等于", ">="),
    LE("<=", "小于等于", "<="),
    EQ("==", "等于", "="),
    GT(">", "大于", ">"),
    LT("<", "小于", "<"),
    NEQ("!=", "不等于", "!="),
    IN("in", "属于", "in"),
    NOTIN("notin", "不属于", "not in"),
    HASVAL("has", "有值", "not null"),
    NOHASVAL("no has", "没值", "is null"),
    NULL("null", "空", "is null"),
    NONULL("no null", "不为空", "not null"),
    BETWEEN("between", "之间", "between"),
    LIKE("like", "类似", "like"),
    NOTLIKE("notlike", "不类似", "not like"),
    ALL("all", "全部", "===");

    private final String description;
    private final String code;
    private final String dbCode;

    private static final Map<String, StrategyLabelOperatorEnum> DB_CODE_MAP;

    StrategyLabelOperatorEnum(String code, String description, String dbCode) {
        this.code = code;
        this.description = description;
        this.dbCode = dbCode;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public String getDbCode() {
        return dbCode;
    }

    public static Map<String, StrategyLabelOperatorEnum> getDbCodeMap() {
        return DB_CODE_MAP;
    }

    static {
        DB_CODE_MAP = Maps.newHashMap();
        for (StrategyLabelOperatorEnum value : StrategyLabelOperatorEnum.values()) {
            DB_CODE_MAP.put(value.getCode().trim(), value);
        }
    }
}
