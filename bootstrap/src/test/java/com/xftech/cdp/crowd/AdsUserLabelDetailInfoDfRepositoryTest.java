package com.xftech.cdp.crowd;

import com.alibaba.fastjson.JSON;
import com.xftech.cdp.BaseTest;
import com.xftech.cdp.domain.crowd.model.dispatch.CrowdWereHouse;
import com.xftech.cdp.domain.crowd.repository.AdsUserLabelDetailInfoDfRepository;
import com.xftech.cdp.infra.constant.Constants;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @<NAME_EMAIL>
 */
public class AdsUserLabelDetailInfoDfRepositoryTest extends BaseTest {
    @Autowired
    AdsUserLabelDetailInfoDfRepository adsUserLabelDetailInfoDfRepository;

    @Test
    public void query() {
        String sql = "select * from ads_user_label_detail_info_df limit 1 ";
        List<CrowdWereHouse> result = adsUserLabelDetailInfoDfRepository.queryWareHouseUserInfo(sql);
        System.out.println(result);
    }

    @Test
    public void selectMaxAppUserId() {
        //Long result = adsUserLabelDetailInfoDfRepository.selectMaxAppUserId();
        //System.out.println(result);
    }

    @Test
    public void queryUtmByMobileAndApp() {
        CrowdWereHouse info = adsUserLabelDetailInfoDfRepository.queryUtmByMobileAndApp("15802603421", Constants.XYF01);
        System.out.println(info);
    }

    @Test
    public void queryRegisterTimeByAppUserIdAndApp() {
        //LocalDateTime registerTime = adsUserLabelDetailInfoDfRepository.queryRegisterTimeByAppUserIdAndApp(211111115064L, "fxk");
        //System.out.println(registerTime);
    }

    @Test
    public void selectBatchStarrocks(){
        List<CrowdWereHouse> crowdWereHouses = adsUserLabelDetailInfoDfRepository.selectBatchStarrocks("ads_user_crowd_detail", LocalDateTime.now().minusDays(1), 0l, 5l, 12345l);
        System.out.println("res===="+ JSON.toJSONString(crowdWereHouses));
        adsUserLabelDetailInfoDfRepository.hasUserRecord("ads_user_crowd_detail", 123L, 0L, LocalDateTime.now());
    }

}