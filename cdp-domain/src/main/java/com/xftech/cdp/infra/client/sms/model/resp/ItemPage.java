package com.xftech.cdp.infra.client.sms.model.resp;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR> wanghu
 * @since : 2022/7/28 14:14:51
 */
@Getter
@Setter
public class ItemPage {

    @JsonProperty("list")
    @JSONField(name = "list")
    private List<SmsItem> list;

    @JsonProperty("total")
    @JSONField(name = "total")
    private Long total;

    @JsonProperty("page")
    @JSONField(name = "page")
    private String page;

    @JsonProperty("page_size")
    @JSONField(name = "page_size")
    private String pageSize;

}
