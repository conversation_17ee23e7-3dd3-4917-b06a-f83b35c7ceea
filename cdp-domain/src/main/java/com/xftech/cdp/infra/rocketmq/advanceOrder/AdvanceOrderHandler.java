/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.rocketmq.advanceOrder;

import com.alibaba.fastjson.JSONObject;
import com.xftech.cdp.domain.mq.MqConsumeService;
import com.xftech.cdp.domain.strategy.service.dispatch.event.StrategyEventDispatchService;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventMessageVO;
import com.xftech.cdp.infra.rocketmq.EventEnum;
import com.xftech.cdp.infra.rocketmq.MessageHandler;
import com.xftech.cdp.infra.rocketmq.RocketMQEnum;
import com.xftech.cdp.infra.rocketmq.dto.AdvanceOrderMessageDTO;
import com.xftech.cdp.infra.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

@Slf4j
@Component
public class AdvanceOrderHandler implements MessageHandler {

    @Autowired
    private MqConsumeService mqConsumeService;

    @Override
    public String getTopic() {
        return RocketMQEnum.TP_PSENGINECORE_ADVANCE_ORDER_CREATE.getTopic();
    }

    @Override
    public boolean execute(String messageId, Object message) {
        try {
            AdvanceOrderMessageDTO messageVO = JSONObject.parseObject(message.toString(), AdvanceOrderMessageDTO.class);
            if (StringUtils.isAnyBlank(messageVO.getApp(), messageVO.getUserNo(), messageVO.getAdvanceLoanScene(), messageVO.getOrderNo())) {
                log.info("AdvanceOrderHandler 不符合[预借款提交成功]事件条件 message={}", JSONObject.toJSONString(messageVO));
                return false;
            }
            BizEventMessageVO bizEventMessageVO = transform(messageVO);
            bizEventMessageVO.setBizEventType(EventEnum.PRE_LOAN_SUBMISSION_SUCCESSFUL.getEventType());
            mqConsumeService.bizEventProcess(messageId, bizEventMessageVO);
            return true;
        } catch (Exception e) {
            log.info("AdvanceOrderHandler execute error", e);
        }
        return false;
    }

    private BizEventMessageVO transform(AdvanceOrderMessageDTO advanceOrderMessageDTO) {
        BizEventMessageVO bizEventMessageVO = new BizEventMessageVO();
        bizEventMessageVO.setApp(advanceOrderMessageDTO.getApp());
        bizEventMessageVO.setInnerApp(advanceOrderMessageDTO.getInnerApp());
        bizEventMessageVO.setAppUserId(Long.parseLong(advanceOrderMessageDTO.getUserNo()));
        bizEventMessageVO.setDeviceId(advanceOrderMessageDTO.getDeviceId());
        bizEventMessageVO.setSourceType(advanceOrderMessageDTO.getSourceType());
        BizEventMessageVO.ExtrData extrData = new BizEventMessageVO.ExtrData();
        extrData.setOrderNo(advanceOrderMessageDTO.getOrderNo());
        extrData.setSourceType(advanceOrderMessageDTO.getSourceType());
        extrData.setAdvanceLoanScene(advanceOrderMessageDTO.getAdvanceLoanScene());
        if (advanceOrderMessageDTO.getRequestDateTime() != null) {
            extrData.setCreatedTime(DateUtil.convertStr(advanceOrderMessageDTO.getRequestDateTime()));
        } else {
            extrData.setCreatedTime(DateUtil.convertStr(new Date()));
        }

        if (advanceOrderMessageDTO.getTriggerDatetime() != null) {
            bizEventMessageVO.setTriggerDatetime(DateUtil.convertStr(advanceOrderMessageDTO.getTriggerDatetime()));
        } else {
            bizEventMessageVO.setTriggerDatetime(DateUtil.convertStr(new Date()));
        }
        bizEventMessageVO.setExtrData(extrData);
        return bizEventMessageVO;
    }

}