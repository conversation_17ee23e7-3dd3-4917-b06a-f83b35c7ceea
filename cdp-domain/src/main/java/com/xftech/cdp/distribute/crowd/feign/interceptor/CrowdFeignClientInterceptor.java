package com.xftech.cdp.distribute.crowd.feign.interceptor;

import java.time.Instant;
import java.util.UUID;

import com.xftech.cdp.infra.config.ApolloUtil;

import com.leo.datainsight.client.constants.CrowdApiConstants;
import feign.RequestInterceptor;
import feign.RequestTemplate;

/**
 * <AUTHOR>
 * @version v1.0 2025/4/28
 * @description Interceptor
 */
public class CrowdFeignClientInterceptor implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate requestTemplate) {
        requestTemplate.header(CrowdApiConstants.CLIENT_APP_NAME, "xyf-cdp");
        requestTemplate.header(CrowdApiConstants.APP_ACCESS_KEY, ApolloUtil.getAppProperty("datainsightAccessKey", "test"));
        requestTemplate.header("requestId", UUID.randomUUID().toString());
        requestTemplate.header("requestTime", String.valueOf(Instant.now().toEpochMilli()));
    }

}
