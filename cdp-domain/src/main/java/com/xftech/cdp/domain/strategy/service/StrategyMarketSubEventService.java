package com.xftech.cdp.domain.strategy.service;

import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketSubEventDo;

import java.util.List;
import java.util.Map;

/**
 * @<NAME_EMAIL>
 */
public interface StrategyMarketSubEventService {

    Map<Integer, List<StrategyMarketSubEventDo>> getByEventId(Long eventId);

    Map<Integer, List<StrategyMarketSubEventDo>> getByStrategyId(Long strategyId);
}
