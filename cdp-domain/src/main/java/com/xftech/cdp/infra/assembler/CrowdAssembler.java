package com.xftech.cdp.infra.assembler;

import com.xftech.cdp.api.dto.req.CrowdCreateReq;
import com.xftech.cdp.domain.crowd.model.enums.LabelEnum;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdLabelDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdLabelPrimaryDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdLabelSubDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPackDo;

/**
 * <AUTHOR>
 * @since 2023/2/13
 */
public class CrowdAssembler {

    public static CrowdLabelDo assemblerLabelToDo(CrowdCreateReq.CrowdLabel crowdLabel) {
        CrowdLabelDo crowdLabelDo = new CrowdLabelDo();
        crowdLabelDo.setLabelId(crowdLabel.getLabelId());
        crowdLabelDo.setLabelValue(crowdLabel.getLabelValue());
        crowdLabelDo.setLabelOptionType(labelOptionType(crowdLabel.getLabelOptionType()));
        crowdLabelDo.setExecIndex(crowdLabel.getExecIndex());
        crowdLabelDo.setLabelRelation(crowdLabel.getLabelRelation());
        return crowdLabelDo;
    }

    public static CrowdLabelSubDo assemblerLabelSubToDo(CrowdCreateReq.SubCrowdLabel subCrowdLabel) {
        CrowdLabelSubDo crowdLabelSubDo = new CrowdLabelSubDo();
        crowdLabelSubDo.setLabelId(subCrowdLabel.getLabelId());
        crowdLabelSubDo.setLabelValue(subCrowdLabel.getLabelValue());
        crowdLabelSubDo.setLabelOptionType(labelOptionType(subCrowdLabel.getLabelOptionType()));
        crowdLabelSubDo.setExecIndex(subCrowdLabel.getExecIndex());
        crowdLabelSubDo.setLabelRelation(subCrowdLabel.getLabelRelation());
        return crowdLabelSubDo;
    }

    public static CrowdLabelPrimaryDo assemblerLabelPrimaryToDo(CrowdCreateReq.CrowdLabelPrimary crowdConfig) {
        CrowdLabelPrimaryDo crowdLabelPrimaryDo = new CrowdLabelPrimaryDo();
        crowdLabelPrimaryDo.setLabelGroupType(crowdConfig.getLabelGroupType());
        crowdLabelPrimaryDo.setPrimaryLabel(crowdConfig.getPrimaryLabel());
        crowdLabelPrimaryDo.setPrimaryLabelRelation(crowdConfig.getPrimaryLabelRelation());
        crowdLabelPrimaryDo.setExecIndex(crowdConfig.getExecIndex());
        return crowdLabelPrimaryDo;
    }

    public static CrowdPackDo assemblerPackToDo(CrowdCreateReq crowdCreateReq) {
        CrowdPackDo crowdPackDo = new CrowdPackDo();
        crowdPackDo.setCrowdName(crowdCreateReq.getCrowdName());
        crowdPackDo.setFilterMethod(crowdCreateReq.getFilterMethod());
        crowdPackDo.setRefreshType(crowdCreateReq.getRefreshType());
        crowdPackDo.setRefreshTime(crowdCreateReq.getRefreshTime());
        crowdPackDo.setValidityBegin(crowdCreateReq.getValidityBegin());
        crowdPackDo.setValidityEnd(crowdCreateReq.getValidityEnd());
        crowdPackDo.setH5Option(crowdCreateReq.getH5Option());
        crowdPackDo.setBusinessType(crowdCreateReq.getBusinessType());
        crowdPackDo.setGroupType(crowdCreateReq.getGroupType());
        return crowdPackDo;
    }

    private static LabelEnum.LabelOptionTypeEnum labelOptionType(Integer labelOptionType) {
        return labelOptionType == null ? null : LabelEnum.LabelOptionTypeEnum.ofCode(labelOptionType);
    }
}
