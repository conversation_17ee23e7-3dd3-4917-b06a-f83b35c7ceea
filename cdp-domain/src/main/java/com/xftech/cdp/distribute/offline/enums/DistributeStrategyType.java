/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2025. All Rights Reserved.
 */

package com.xftech.cdp.distribute.offline.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version $ DistributeStrategyType, v 0.1 2025/5/12 19:25 xu.fan Exp $
 */
@Getter
@AllArgsConstructor
public enum DistributeStrategyType {

    OFFLINE_ENGINE("offline_engine"),
    OFFLINE("offline");

    private final String type;
}
