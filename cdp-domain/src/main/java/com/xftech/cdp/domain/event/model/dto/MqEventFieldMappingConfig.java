package com.xftech.cdp.domain.event.model.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 消息事件字段映射信息
 * <AUTHOR>
 * @version $ MqEventFieldMappingConfig, v 0.1 2024/11/12 17:15 snail Exp $
 */
@Data
public class MqEventFieldMappingConfig {
    /** MQ生产者 */
    private String topic;
    /** MQ消费者 */
    private String consumer;
    /** MQ过滤Tag信息 */
    private String tag;
    /** 消息事件名称 */
    private String eventType;
    /** 事件映射配置信息 */
    private List<FieldDetail> fieldList;
    /** 参数解析处理器 */
    private String parse;
    /** 创建时间 */
    private Date createdTime;
    /** 修改时间 */
    private Date updatedTime;
}
