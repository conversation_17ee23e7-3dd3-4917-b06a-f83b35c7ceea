package com.xftech.cdp.domain.ads.repository;


import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.domain.crowd.model.dispatch.CrowdWereHouse;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class AdsOprtAutoMessageDfRepository {

    /**
     * 根据组装sql查询加贷自动短信表信息
     * @param sql
     * @return
     */
    public List<CrowdWereHouse> queryOprtAutoMessageDf(String sql) {
        return DBUtil.selectList("ads", "adsOprtAutoMessageDf.queryOprtAutoMessageDf", sql);
    }

    /**
     * 查询加贷自动短信表当天执行最大的appUserId
     * @return
     */
    public Long selectAutoMessageMaxAppUserId(LocalDateTime dateTime){
        return DBUtil.selectOne("ads","adsOprtAutoMessageDf.selectAutoMessageMaxAppUserId", dateTime);
    }

    public Long selectAutoMessageMinAppUserId(LocalDateTime dateTime){
        return DBUtil.selectOne("ads","adsOprtAutoMessageDf.selectAutoMessageMinAppUserId", dateTime);
    }
}
