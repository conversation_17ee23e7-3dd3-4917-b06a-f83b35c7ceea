package com.xftech.cdp.domain.strategy.service.dispatch.event;

import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;

/**
 * 策略事件缓存
 *
 * @<NAME_EMAIL>
 */
public interface StrategyEventCatchService {
    /**
     * 项目启动时，设置事件触发时间
     */
    void initEventTriggerTime();
    /**
     * 重置事件触发事件
     *
     * @param strategy 策略
     */
    void resetEventTriggerTime(String eventName, StrategyDo strategy);

    /**
     * 策略30分钟无事件告警
     *
     * @param strategy 策略
     */
    void noEventAlarm(String eventName, StrategyDo strategy);

    /**
     * 设置有事件但没有符合条的用户的标识
     *
     * @param eventName  事件名称
     * @param strategyId 策略ID
     */
    void hasEventButNoMatchFlag(String eventName, Long strategyId);

    /**
     * 删除有事件但没有符合条的用户的标识
     *
     * @param eventName  事件名称
     * @param strategyId 策略ID
     */
    void removeHasEventButNoMatchFlag(String eventName, Long strategyId);

    /**
     * 有事件但没有符合条的用户告警
     *
     * @param eventName 事件名称
     * @param strategy  策略
     */
    void hasEventButNoMatchAlarm(String eventName, StrategyDo strategy);

    /**
     * 缓存元数据
     */
    void catchMetaData();

}
