<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="crowdLabel">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdLabelDo">
        <id column="id" property="id" />
        <result column="crowd_id" property="crowdId" />
        <result column="crowd_label_primary_id" property="crowdLabelPrimaryId" />
        <result column="label_id" property="labelId" />
        <result column="label_value" property="labelValue" />
        <result column="exec_index" property="execIndex" />
        <result column="label_relation" property="labelRelation" />
        <result column="created_time" property="createdTime" />
        <result column="updated_time" property="updatedTime" />
        <result column="d_flag" property="dFlag" />
        <result column="created_op" property="createdOp" />
        <result column="updated_op" property="updatedOp" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, crowd_id, crowd_label_primary_id, label_id, label_value, exec_index, label_relation, created_time, updated_time, d_flag, created_op, updated_op
    </sql>

    <select id="selectListByCrowdId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crowd_label where crowd_id = #{crowdId} and d_flag = 0 order by exec_index
    </select>

    <select id="selectListByLabelPrimaryId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crowd_label where crowd_label_primary_id = #{labelPrimaryId} and d_flag = 0 order by exec_index
    </select>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdLabelDo" useGeneratedKeys="true">
        insert into crowd_label
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="crowdId != null">
                crowd_id,
            </if>
            <if test="crowdLabelPrimaryId != null">
                crowd_label_primary_id,
            </if>
            <if test="labelId != null">
                label_id,
            </if>
            <if test="labelValue != null">
                label_value,
            </if>
            <if test="execIndex != null">
                exec_index,
            </if>
            <if test="labelRelation != null">
                label_relation,
            </if>
            <if test="createdOp != null">
                created_op,
            </if>
            <if test="updatedOp != null">
                updated_op,
            </if>
            created_time,
            updated_time,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="crowdId != null">
                #{crowdId,jdbcType=BIGINT},
            </if>
            <if test="crowdLabelPrimaryId != null">
                #{crowdLabelPrimaryId,jdbcType=BIGINT},
            </if>
            <if test="labelId != null">
                #{labelId,jdbcType=BIGINT},
            </if>
            <if test="labelValue != null">
                #{labelValue,jdbcType=VARCHAR},
            </if>
            <if test="execIndex != null">
                #{execIndex,jdbcType=SMALLINT},
            </if>
            <if test="labelRelation != null">
                #{labelRelation,jdbcType=SMALLINT},
            </if>
            <if test="createdOp != null">
                #{createdOp,jdbcType=VARCHAR},
            </if>
            <if test="updatedOp != null">
                #{updatedOp,jdbcType=VARCHAR},
            </if>
            now(),
            now(),
        </trim>
    </insert>

    <delete id="deleteByCrowdId">
        update crowd_label
        set d_flag = 1
        where crowd_id = #{crowdId}
    </delete>
</mapper>
