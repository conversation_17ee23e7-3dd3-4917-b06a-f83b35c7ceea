<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="reportDailyCrowdMapper">
  <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.crowd.po.ReportDailyCrowdDo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="crowd_id" jdbcType="BIGINT" property="crowdId" />
    <result column="crowd_name" jdbcType="VARCHAR" property="crowdName" />
    <result column="exec_start_time" jdbcType="TIMESTAMP" property="execStartTime" />
    <result column="exec_end_time" jdbcType="TIMESTAMP" property="execEndTime" />
    <result column="exec_status" jdbcType="INTEGER" property="execStatus" />
    <result column="fail_reason" jdbcType="VARCHAR" property="failReason" />
    <result column="crowd_today_sum" jdbcType="INTEGER" property="crowdTodaySum" />
    <result column="crowd_yesterday_sum" jdbcType="INTEGER" property="crowdYesterdaySum" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
    <result column="d_flag" jdbcType="INTEGER" property="dFlag" />
    <result column="created_op" jdbcType="VARCHAR" property="createdOp" />
    <result column="updated_op" jdbcType="VARCHAR" property="updatedOp" />
  </resultMap>
  <sql id="Base_Column_List">
    `id`, `date`, `crowd_id`, `crowd_name`, `exec_start_time`, `exec_end_time`, `exec_status`,
    `fail_reason`, `crowd_today_sum`, `crowd_yesterday_sum`, `created_time`, `updated_time`, 
    `d_flag`, `created_op`, `updated_op`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from report_daily_crowd
    where `id` = #{id,jdbcType=BIGINT}
  </select>

  <select id="queryPageByCrowdId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from report_daily_crowd
    where `crowd_id` = #{id,jdbcType=BIGINT}
    order by `date` desc
  </select>

  <insert id="insertSelective" parameterType="com.xftech.cdp.infra.repository.cdp.crowd.po.ReportDailyCrowdDo">
    insert into report_daily_crowd
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="date != null">
        `date`,
      </if>
      <if test="crowdId != null">
        `crowd_id`,
      </if>
      <if test="crowdName != null">
        `crowd_name`,
      </if>
      <if test="execStartTime != null">
        `exec_start_time`,
      </if>
      <if test="execEndTime != null">
        `exec_end_time`,
      </if>
      <if test="execStatus != null">
        `exec_status`,
      </if>
      <if test="failReason != null">
        `fail_reason`,
      </if>
      <if test="crowdTodaySum != null">
        `crowd_today_sum`,
      </if>
      <if test="crowdYesterdaySum != null">
        `crowd_yesterday_sum`,
      </if>
      <if test="createdTime != null">
        `created_time`,
      </if>
      <if test="updatedTime != null">
        `updated_time`,
      </if>
      <if test="dFlag != null">
        `d_flag`,
      </if>
      <if test="createdOp != null">
        `created_op`,
      </if>
      <if test="updatedOp != null">
        `updated_op`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="date != null">
        #{date,jdbcType=DATE},
      </if>
      <if test="crowdId != null">
        #{crowdId,jdbcType=BIGINT},
      </if>
      <if test="crowdName != null">
        #{crowdName,jdbcType=VARCHAR},
      </if>
      <if test="execStartTime != null">
        #{execStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="execEndTime != null">
        #{execEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="execStatus != null">
        #{execStatus,jdbcType=INTEGER},
      </if>
      <if test="failReason != null">
        #{failReason,jdbcType=VARCHAR},
      </if>
      <if test="crowdTodaySum != null">
        #{crowdTodaySum,jdbcType=INTEGER},
      </if>
      <if test="crowdYesterdaySum != null">
        #{crowdYesterdaySum,jdbcType=INTEGER},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dFlag != null">
        #{dFlag,jdbcType=INTEGER},
      </if>
      <if test="createdOp != null">
        #{createdOp,jdbcType=VARCHAR},
      </if>
      <if test="updatedOp != null">
        #{updatedOp,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xftech.cdp.infra.repository.cdp.crowd.po.ReportDailyCrowdDo">
    update report_daily_crowd
    <set>
      <if test="date != null">
        `date` = #{date,jdbcType=DATE},
      </if>
      <if test="crowdId != null">
        `crowd_id` = #{crowdId,jdbcType=BIGINT},
      </if>
      <if test="crowdName != null">
        `crowd_name` = #{crowdName,jdbcType=VARCHAR},
      </if>
      <if test="execStartTime != null">
        `exec_start_time` = #{execStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="execEndTime != null">
        `exec_end_time` = #{execEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="execStatus != null">
        `exec_status` = #{execStatus,jdbcType=INTEGER},
      </if>
      <if test="failReason != null">
        `fail_reason` = #{failReason,jdbcType=VARCHAR},
      </if>
      <if test="crowdTodaySum != null">
        `crowd_today_sum` = #{crowdTodaySum,jdbcType=INTEGER},
      </if>
      <if test="crowdYesterdaySum != null">
        `crowd_yesterday_sum` = #{crowdYesterdaySum,jdbcType=INTEGER},
      </if>
      <if test="createdTime != null">
        `created_time` = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        `updated_time` = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dFlag != null">
        `d_flag` = #{dFlag,jdbcType=INTEGER},
      </if>
      <if test="createdOp != null">
        `created_op` = #{createdOp,jdbcType=VARCHAR},
      </if>
      <if test="updatedOp != null">
        `updated_op` = #{updatedOp,jdbcType=VARCHAR},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>

  <select id="existReportDailyCrowd" resultType="integer">
    select count(*)
    from report_daily_crowd
    where crowd_id = #{crowdId}
      and date(date) = current_date()
  </select>

  <select id="selectTodayFail" resultMap="BaseResultMap">
    select *
    from report_daily_crowd
    where status = 2
      and date (date) = current_date ();
  </select>

  <select id="selectToday" resultMap="BaseResultMap">
    select *
    from report_daily_crowd
    where date (date) = current_date ();
  </select>

  <update id="updateByDateAndCrowdId" parameterType="com.xftech.cdp.infra.repository.cdp.crowd.po.ReportDailyCrowdDo">
    update report_daily_crowd
    <set>
      <if test="date != null">
        `date` = #{date,jdbcType=DATE},
      </if>
      <if test="crowdId != null">
        `crowd_id` = #{crowdId,jdbcType=BIGINT},
      </if>
      <if test="crowdName != null">
        `crowd_name` = #{crowdName,jdbcType=VARCHAR},
      </if>
      <if test="execStartTime != null">
        `exec_start_time` = #{execStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="execEndTime != null">
        `exec_end_time` = #{execEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="execStatus != null">
        `exec_status` = #{execStatus,jdbcType=INTEGER},
      </if>
      <if test="failReason != null">
        `fail_reason` = #{failReason,jdbcType=VARCHAR},
      </if>
      <if test="crowdTodaySum != null">
        `crowd_today_sum` = #{crowdTodaySum,jdbcType=INTEGER},
      </if>
      <if test="crowdYesterdaySum != null">
        `crowd_yesterday_sum` = #{crowdYesterdaySum,jdbcType=INTEGER},
      </if>
      <if test="createdTime != null">
        `created_time` = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        `updated_time` = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dFlag != null">
        `d_flag` = #{dFlag,jdbcType=INTEGER},
      </if>
      <if test="createdOp != null">
        `created_op` = #{createdOp,jdbcType=VARCHAR},
      </if>
      <if test="updatedOp != null">
        `updated_op` = #{updatedOp,jdbcType=VARCHAR},
      </if>
    </set>
    where crowd_id = #{crowdId}
    and date(date) = current_date()
  </update>
</mapper>