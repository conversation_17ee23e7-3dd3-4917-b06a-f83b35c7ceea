/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.infra.repository.cdp.strategy.po.DispatchTaskDo;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @version $ DispatchTaskRepository, v 0.1 2023/11/2 21:09 yye.xu Exp $
 */

@Component
public class DispatchTaskRepository {

    public List<DispatchTaskDo> selectList(String bizId, String associationId, Byte bizType, Integer dateValue, List<Integer> statusList) {
        Map<String, Object> param = new HashMap<>();
        param.put("bizId", bizId);
        param.put("associationId", associationId);
        param.put("bizType", bizType);
        param.put("statusList", statusList);
        param.put("dateValue", dateValue);
        return DBUtil.selectList("dispatchTaskMapper.selectList", param);
    }

    public List<DispatchTaskDo> selectLastExecutingTask(String bizId, int fromStatus, int dateValue) {
        Map<String, Object> param = new HashMap<>();
        param.put("bizId", bizId);
        param.put("fromStatus", fromStatus);
        param.put("dateValue", dateValue);
        return DBUtil.selectList("dispatchTaskMapper.selectLastExecutingTask", param);
    }

    public  List<DispatchTaskDo> selectTodoList(Byte bizType, int dateValue, Date dispatchTime, List<Integer> statusList) {
        Map<String, Object> param = new HashMap<>();
        param.put("dispatchTime", dispatchTime);
        param.put("dateValue", dateValue);
        param.put("bizType", bizType);
        param.put("statusList", statusList);
        return DBUtil.selectList("dispatchTaskMapper.selectTodoList", param);
    }

    public void insertSelective(DispatchTaskDo dispatchTaskDo) {
        DBUtil.insert("dispatchTaskMapper.insertSelective", dispatchTaskDo);
    }

    public void updateDispatchTime(Long id, Date dispatchTime, Date nextDispatchTime) {
        Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        param.put("dispatchTime", dispatchTime);
        param.put("nextDispatchTime", nextDispatchTime);
        DBUtil.update("dispatchTaskMapper.updateDispatchTime", param);
    }

    public int updateTaskStatus(long id, int fromStatus, int toStatus) {
        Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        param.put("fromStatus", fromStatus);
        param.put("toStatus", toStatus);
        return DBUtil.update("dispatchTaskMapper.updateTaskStatus", param);
    }

    public int updateTaskFinish(long id, int fromStatus, int toStatus, String execRetMsg) {
        Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        param.put("fromStatus", fromStatus);
        param.put("toStatus", toStatus);
        param.put("execRetMsg", execRetMsg);
        return DBUtil.update("dispatchTaskMapper.updateTaskFinish", param);
    }

    public int updateTaskStatus(long id, int fromStatus, int toStatus, int retryTimes, Date nextTime, String execRetMsg){
        Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        param.put("fromStatus", fromStatus);
        param.put("toStatus", toStatus);
        param.put("retryTimes", retryTimes);
        param.put("nextTime", nextTime);
        param.put("execRetMsg", execRetMsg);
        return DBUtil.update("dispatchTaskMapper.updateTaskRetry", param);
    }
}