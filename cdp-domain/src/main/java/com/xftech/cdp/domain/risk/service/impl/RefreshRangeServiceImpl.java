package com.xftech.cdp.domain.risk.service.impl;

import com.xftech.cdp.domain.risk.service.RefreshRangeService;
import com.xftech.cdp.infra.client.ads.AdsClient;
import com.xftech.cdp.infra.client.ads.model.req.BaseAdsRequest;
import com.xftech.cdp.infra.client.ads.model.req.DTRiskUserListReq;
import com.xftech.cdp.infra.client.ads.model.resp.BaseAdsResponse;
import com.xftech.cdp.infra.client.ads.model.resp.DTRiskUserListResp;
import com.xftech.cdp.infra.client.bi.BiClient;
import com.xftech.cdp.infra.client.bi.model.req.ModelMarketingReq;
import com.xftech.cdp.infra.client.bi.model.resp.BaseBiResponse;
import com.xftech.cdp.infra.config.Config;
import com.xftech.cdp.infra.utils.AesUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2023/6/28
 */
@Service
@Slf4j
public class RefreshRangeServiceImpl implements RefreshRangeService {

    @Resource
    private AdsClient adsClient;

    @Resource
    private BiClient biClient;

    @Resource
    private Config config;

    /**
     * 异步调用模型分
     */
    @Override
    @Async("refreshRiskScoreTaskExecutor")
    public void refreshRange(DTRiskUserListReq req, List<Integer> pageNumbers) {

        DTRiskUserListReq dtRiskUserListReq = req.clone();

        for (Integer pageNum : pageNumbers) {

            dtRiskUserListReq.setPageNum(pageNum);

            try {

                BaseAdsResponse<DTRiskUserListResp> response = adsClient.dtRiskUserList(new BaseAdsRequest<>(dtRiskUserListReq));
                DTRiskUserListResp.Rows[] rows = response.getPayload().getRows();

                if (rows.length == 0) {
                    break;
                }

                // 遍历计算模型分 https://www.tapd.cn/60211538/prong/stories/view/1160211538001053117
                for (DTRiskUserListResp.Rows row : rows) {
                    ModelMarketingReq modelMarketingReq = new ModelMarketingReq();
                    ArrayList<String> elementIdList = new ArrayList<>(Collections.singletonList("personalloan_yunying_fd_fundcash_f90_lgb_v1_prob"));
                    modelMarketingReq.setContext(new ModelMarketingReq.Context());
                    modelMarketingReq.getContext().setApp(row.getApp());
                    modelMarketingReq.getContext().setDeviceId(row.getDeviceId());
                    modelMarketingReq.getContext().setCurrentLoanType(row.getCurrentLoanType());
                    modelMarketingReq.getContext().setName(row.getName());
                    modelMarketingReq.getContext().setMobile(AesUtil.decrypt(row.getMobile(), config.getAdsMobileAesKey()));
                    modelMarketingReq.getContext().setIdCardNumber(AesUtil.decrypt(row.getIdCardNumber(), config.getAdsMobileAesKey()));
                    modelMarketingReq.setElementIdList(elementIdList);
                    BaseBiResponse baseBiResponse = biClient.modelMarketing(modelMarketingReq);
                }

            } catch (Exception e) {
                log.error("reqDate {} 执行异常， error = {}", dtRiskUserListReq.getReqDate(), e, e);
                break;
            }
        }
    }
}
