package com.xftech.cdp.domain.redecision.mq;

import javax.annotation.Resource;

import com.xftech.cdp.infra.config.LogUtil;

import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version v1.0 2025/4/9
 * @description ReDecisionMQProducer
 */
@Slf4j
@Component
public class ReDecisionMQProducer {

    @Resource(name = "marketptRocketMQTemplate")
    private RocketMQTemplate rocketMQTemplate;

    public void send(String topic, String msg, int delaySecond) {
        try {
            LogUtil.logDebug("ReDecisionMQProducer send topic={} msg={} delaySecond={}", topic, msg, delaySecond);
            rocketMQTemplate.syncSendDelayTimeSeconds(topic, msg, delaySecond);
        } catch (Exception e) {
            log.error("ReDecisionMQProducer convertAndSend error", e);
        }
    }

}
