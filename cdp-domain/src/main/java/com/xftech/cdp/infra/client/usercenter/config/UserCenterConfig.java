package com.xftech.cdp.infra.client.usercenter.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/2/16
 */
@RefreshScope
@Component
@Setter
@Getter
public class UserCenterConfig {

    @Value("${php.user.host:https://api.devxinfei.cn/user}")
    private String phpUserHost;

    @Value("${php.user.getByMobile:/user/get-by-mobile}")
    private String userByMobile;

    @Value("${php.user.getAbNum:/ab/get-ab-num}")
    private String abNum;

    @Value("${php.user.batch:/user-list/get-by-credit-user-ids}")
    private String batchUserId;

    @Value("${php.user.uniqueKey:111111}")
    private String key;
}
