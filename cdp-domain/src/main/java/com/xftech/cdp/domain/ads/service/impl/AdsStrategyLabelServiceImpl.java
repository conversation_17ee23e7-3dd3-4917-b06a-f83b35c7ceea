package com.xftech.cdp.domain.ads.service.impl;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.xftech.cdp.adapter.strategy.label.LabelHandlerSelector;
import com.xftech.cdp.adapter.strategy.label.QueryLabelRequest;
import com.xftech.cdp.domain.ads.service.AdsStrategyLabelService;
import com.xftech.cdp.domain.cache.CacheStrategyService;
import com.xftech.cdp.domain.flowctrl.model.enums.StrategyCustomProcessEnum;
import com.xftech.cdp.domain.flowctrl.model.enums.StrategyTypeEnum;
import com.xftech.cdp.domain.strategy.exception.StrategyException;
import com.xftech.cdp.domain.strategy.model.enums.InterfaceTypeEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyInstantLabelTypeEnum;
import com.xftech.cdp.domain.strategy.model.enums.label.ValueTypeEnum;
import com.xftech.cdp.domain.strategy.repository.StrategyFlowBatchRepository;
import com.xftech.cdp.domain.strategy.service.StrategyInstantLabelService;
import com.xftech.cdp.domain.strategy.service.StrategyMarketEventConditionService;
import com.xftech.cdp.domain.strategy.vo.BatchAdsLabelParamVO;
import com.xftech.cdp.domain.strategy.vo.BatchAdsLabelVO;
import com.xftech.cdp.domain.strategy.vo.VariableLabelParamVO;
import com.xftech.cdp.infra.aviator.util.DateTimeUtil;
import com.xftech.cdp.infra.client.ads.model.req.label.AdsLabelReq;
import com.xftech.cdp.infra.client.ads.model.resp.AdsLabelResp;
import com.xftech.cdp.infra.client.ads.model.resp.BaseAdsResponse;
import com.xftech.cdp.infra.client.dingtalk.DingTalkUtil;
import com.xftech.cdp.infra.client.dingtalk.config.DingTalkConfig;
import com.xftech.cdp.infra.client.usercenter.CisService;
import com.xftech.cdp.infra.client.usercenter.model.BaseCisResp;
import com.xftech.cdp.infra.client.usercenter.model.RegisterInfoByUserNo;
import com.xftech.cdp.infra.config.AppConfigService;
import com.xftech.cdp.infra.config.StrategyConfig;
import com.xftech.cdp.infra.constant.Constants;
import com.xftech.cdp.infra.exception.BizException;
import com.xftech.cdp.infra.exception.TimeoutException;
import com.xftech.cdp.infra.rabbitmq.factory.impl.BizEventMqService;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventVO;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyFlowBatchDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyInstantLabelDo;
import com.xftech.cdp.infra.utils.DateUtil;
import com.xftech.cdp.infra.utils.JsonUtil;
import com.xftech.cdp.infra.utils.ReflectGetFieldUtil;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.TimeInterval;
import com.github.benmanes.caffeine.cache.Cache;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 策略数仓标签查询
 *
 * @<NAME_EMAIL>
 */
@Slf4j
@Service
public class AdsStrategyLabelServiceImpl extends AbstractAdsStrategyLabelService implements AdsStrategyLabelService {

    @Autowired
    private StrategyInstantLabelService strategyInstantLabelService;
    @Autowired
    private StrategyMarketEventConditionService strategyMarketEventConditionService;
    @Autowired
    private CacheStrategyService cacheStrategyService;
    @Autowired
    private DingTalkConfig dingTalkConfig;
    @Autowired
    private BizEventMqService bizEventMqService;
    @Autowired
    private StrategyConfig strategyConfig;
    @Autowired
    private AppConfigService appConfigService;
    @Qualifier("flowBatchNoToStartTimeCache")
    @Autowired
    private Cache<String, String> flowBatchNoToStartTimeCache;
    @Autowired
    private StrategyFlowBatchRepository strategyFlowBatchRepository;
    @Autowired
    private CisService cisService;
    @Autowired
    private LabelHandlerSelector labelHandlerSelector;

    @Override
    public Map<Long, Map<String, Object>> query(BizEventVO bizEventVO, Collection<String> labelNameList, StrategyInstantLabelTypeEnum queryType) {
        final TimeInterval timer = new TimeInterval();
        Map<Long, Map<String, Object>> resultMap = new HashMap<>(16);
        try {
            List<AdsLabelReq> reqs = new ArrayList<>();
            Map<String, StrategyInstantLabelDo> labelMetaMap = new HashMap<>();
            List<String> queryInterfaceLabels = new ArrayList<>();
            for (String labelName : labelNameList) {
                AdsLabelReq adsLabelReq = new AdsLabelReq();
                adsLabelReq.setLabel(labelName);

                StrategyInstantLabelDo labelInfo = strategyInstantLabelService.getByLabelNameAndLabelType(labelName, queryType, StrategyTypeEnum.REALTIME_STRATEGY.getCode(), null);
                if (labelInfo == null) {
                    throw new BizException("T0策略标签查询 需要查询的标签不在标签元数据表中");
                }
                labelMetaMap.put(labelName, labelInfo);

                //查询接口类的实时标签
                if (Objects.equals(StrategyCustomProcessEnum.IN_INTERFACE.getCode(), labelInfo.getCustomProcess())) {
                    queryInterfaceLabels.add(labelName);
                    log.info("T0策略标签查询 接口类标签 查询类型={}, 策略id={}, labelName={}", queryType.getType(), bizEventVO.getStrategyId(), labelName);
                    continue;
                }

                List<Map<String, Object>> params = new ArrayList<>();
                Map<String, Object> map = new HashMap<>();
                Arrays.stream(labelInfo.getLabelReqParam().split(",")).forEach(param -> map.put(param, ReflectGetFieldUtil.getFieldValue(bizEventVO, param)));
                params.add(map);

                adsLabelReq.setParams(params);

                reqs.add(adsLabelReq);
            }

            // 发送请求
            BaseAdsResponse<List<AdsLabelResp>> response = this.queryAdsWrapper(queryType, bizEventVO.getStrategyId(), bizEventVO.getAppUserId(), bizEventVO.getMobile(), reqs);

            Map<String, Object> subResultMap = new HashMap<>(16);
            List<AdsLabelResp> payload = response.getPayload(); // TODO 空指针异常解决

            //执行查询接口的实时标签
            if (!CollectionUtils.isEmpty(queryInterfaceLabels)) {
                //后续可以考虑用线程池执行
                for (String queryInterfaceLabel : queryInterfaceLabels) {
                    QueryLabelRequest queryLabelRequest = new QueryLabelRequest();
                    queryLabelRequest.setUserNoList(Collections.singletonList(bizEventVO.getAppUserId()));
                    queryLabelRequest.setOrderNo(bizEventVO.getOrderNo());
                    if (CollectionUtils.isEmpty(payload)) {
                        payload = new ArrayList<>();
                    }
                    payload.add(labelHandlerSelector.doQueryLabel(queryInterfaceLabel, queryLabelRequest));
                }
            }

            for (AdsLabelResp adsLabelResp : payload) {
                String labelName = adsLabelResp.getLabel();
                List<AdsLabelResp.Param> params = adsLabelResp.getParams();
                for (AdsLabelResp.Param subItem : params) {
                    String result = subItem.getResult();

                    StrategyInstantLabelDo strategyInstantLabelDo = labelMetaMap.get(labelName);
                    if (queryType == StrategyInstantLabelTypeEnum.SMS_PARAM && StringUtils.isBlank(result)) {
                        throw new StrategyException(-1, String.format("T0策略标签查询 标签值为空, 用户ID=%s, 标签=%s", bizEventVO.getAppUserId(), labelName));
                    }

                    // 标签值加工
                    result = labelUtmSourceProcess(bizEventVO.getStrategyId(), labelName, subItem.getResult(), strategyInstantLabelDo.getCustomProcess(), bizEventVO.getRegisterTime());

                    ValueTypeEnum valueType = ValueTypeEnum.getInstance(strategyInstantLabelDo.getLabelValueType());
                    if (queryType == StrategyInstantLabelTypeEnum.SMS_PARAM) {
                        subResultMap.put(labelName, valueType.smsFormatValue(result, Constants.SMS_CURRENCY_FORMAT));
                    } else {
                        subResultMap.put(labelName, valueType.normalizeValueNullNoValue(labelName, result));
                    }
                }
            }
            if (queryType == StrategyInstantLabelTypeEnum.SMS_PARAM) {
                filterLabelMinValue(subResultMap, bizEventVO.getAppUserId(), bizEventVO.getStrategyId());
            }
            resultMap.put(bizEventVO.getAppUserId(), subResultMap);
            log.info("T0策略标签查询 单个用户实时标签查询接口耗时={}ms, 结果={}", timer.interval(), JsonUtil.toJson(resultMap));
            return resultMap;
        } catch (TimeoutException te) {
            // 实时标签查询超时异常
            if (StrategyInstantLabelTypeEnum.LABEL == queryType) {
                //判断重发次数，超过重发次数预警
                if (bizEventVO.getRetryNum() == null) {
                    bizEventVO.setRetryNum(0);
                }
                if (bizEventVO.getRetryNum() < strategyConfig.getRetryTimes()) {
                    try {
                        String newMessageId = getOriginalMessageId(bizEventVO.getMessageId()) + "_" + (bizEventVO.getRetryNum() + 1);
                        bizEventMqService.sendMiddleLevelDelayMessage(bizEventVO, newMessageId, strategyConfig.getLabelTimeOutRetryInterval());
                        log.info("T0策略标签查询 实时策略超时重试, 策略ID={}, 用户ID={}, 消息ID={}, 重试次数={}", bizEventVO.getStrategyId(), bizEventVO.getAppUserId(), bizEventVO.getMessageId(), bizEventVO.getRetryNum());
                    } catch (IOException e) {
                        log.warn("T0策略标签查询 实时策略超时重试异常, 策略ID={}, 用户ID={}", bizEventVO.getStrategyId(), bizEventVO.getAppUserId(), e);
                    }
                } else {
                    alarmTimeOut(InterfaceTypeEnum.REALTIME, queryType, bizEventVO.getStrategyId(), bizEventVO.getAppUserId(), null);
                }
            }
            throw new StrategyException("T0策略标签查询 查询异常", te);
        } catch (BizException e) {
            this.alarmProgramEx(bizEventVO.getStrategyId());
            throw new StrategyException("T0策略标签查询 查询异常", e);
        }
    }


    @Override
    public Map<Long, Map<Long, Map<String, Object>>> queryLabelBatch(BizEventVO bizEventVO,
                                                     StrategyInstantLabelTypeEnum queryType,
                                                     Map<Long, Collection<String>> strategyId2LabelListMap) {
        final TimeInterval timer = new TimeInterval();
        Map<Long, Map<Long, Map<String, Object>>> resultMap = new HashMap<>(16);
        Map<Long, List<AdsLabelReq>> strategyReqMap = new HashMap<>(strategyId2LabelListMap.size());
        try {

            Map<String, StrategyInstantLabelDo> labelMetaMap = new HashMap<>();
            List<String> queryInterfaceLabels = new ArrayList<>();

            //获取所有待查的标签名的集合，通过set去重
            int maxLabelCount = strategyId2LabelListMap.values().stream().mapToInt(Collection::size).reduce(0, Integer::sum);
            Set<String> labelNameSet = new HashSet<>(maxLabelCount);
            strategyId2LabelListMap.values().forEach(labelNameSet::addAll);

            //遍历标签名，生成对应的AdsLabelReq
            List<AdsLabelReq> reqs = new ArrayList<>(labelNameSet.size());
//            Set<String> handledSet = new HashSet<>(maxLabelCount);
            Map<String, AdsLabelReq> label2ReqMap = new HashMap<>(maxLabelCount);

            for (Map.Entry<Long, Collection<String>> entry : strategyId2LabelListMap.entrySet()) {
                Collection<String> labelNames = entry.getValue();
                List<AdsLabelReq> reqListPerStrategy = new ArrayList<>(labelNames.size());

                for (String labelName : labelNames) {
                    AdsLabelReq adsLabelReq;
                    if (label2ReqMap.containsKey(labelName)) {  //去重
                        adsLabelReq = label2ReqMap.get(labelName);
                    } else {
                        adsLabelReq = new AdsLabelReq();
                        adsLabelReq.setLabel(labelName);
                        StrategyInstantLabelDo labelInfo = strategyInstantLabelService.getByLabelNameAndLabelType(labelName, queryType, StrategyTypeEnum.REALTIME_STRATEGY.getCode(), null);
                        if (labelInfo == null) {
                            throw new BizException(String.format("(批量查询)T0策略标签查询 需要查询的标签[%s]不在标签元数据表中", labelName));
                        }
                        labelMetaMap.put(labelName, labelInfo);

                        //查询接口类的实时标签
                        if (Objects.equals(StrategyCustomProcessEnum.IN_INTERFACE.getCode(), labelInfo.getCustomProcess())) {
                            queryInterfaceLabels.add(labelName);
                            log.info("(批量查询)T0策略标签查询 查询接口类实时标签:查询类型:{}, 策略id:{}, labelName:{}", queryType.getType(), bizEventVO.getStrategyId(), labelName);
                            continue;
                        }

                        List<Map<String, Object>> params = new ArrayList<>();
                        Map<String, Object> map = new HashMap<>();
                        Arrays.stream(labelInfo.getLabelReqParam().split(","))
                                .forEach(param -> map.put(param, ReflectGetFieldUtil.getFieldValue(bizEventVO, param)));
                        params.add(map);

                        adsLabelReq.setParams(params);
                    }
                    reqListPerStrategy.add(adsLabelReq);
                    reqs.add(adsLabelReq);
                    label2ReqMap.put(labelName, adsLabelReq);
                }
                Long strategyId = entry.getKey();
                strategyReqMap.put(strategyId, reqListPerStrategy);
            }

            // 发送请求
            BaseAdsResponse<List<AdsLabelResp>> response = this.queryAdsWrapperBatch(queryType,
                    bizEventVO.getAppUserId(), bizEventVO.getMobile(),
                    strategyId2LabelListMap.keySet(),
                    label2ReqMap.values());

            Map<String, Object> subResultMap = new HashMap<>(16);
            List<AdsLabelResp> payload = response.getPayload();

            //执行查询接口的实时标签
            if (!CollectionUtils.isEmpty(queryInterfaceLabels)) {
                //后续可以考虑用线程池执行
                for (String queryInterfaceLabel : queryInterfaceLabels) {
                    QueryLabelRequest queryLabelRequest = new QueryLabelRequest();
                    queryLabelRequest.setUserNoList(Collections.singletonList(bizEventVO.getAppUserId()));
                    if (CollectionUtils.isEmpty(payload)) {
                        payload = new ArrayList<>();
                    }
                    payload.add(labelHandlerSelector.doQueryLabel(queryInterfaceLabel, queryLabelRequest));
                }
            }

            for (AdsLabelResp adsLabelResp : payload) {
                String labelName = adsLabelResp.getLabel();
                Object value;
                List<AdsLabelResp.Param> params = adsLabelResp.getParams();
                for (AdsLabelResp.Param subItem : params) {
                    String result = subItem.getResult();

                    StrategyInstantLabelDo strategyInstantLabelDo = labelMetaMap.get(labelName);
                    if (queryType == StrategyInstantLabelTypeEnum.SMS_PARAM && StringUtils.isBlank(result)) {
                        throw new StrategyException(-1, String.format("(批量查询)T0策略标签查询 标签值为空，用户ID：%s，标签：%s", bizEventVO.getAppUserId(), labelName));
                    }

                    // 标签值加工
                    result = labelUtmSourceProcessBatch(strategyId2LabelListMap.keySet(), labelName, subItem.getResult(), strategyInstantLabelDo.getCustomProcess(), bizEventVO.getRegisterTime());

                    ValueTypeEnum valueType = ValueTypeEnum.getInstance(strategyInstantLabelDo.getLabelValueType());
                    if (queryType == StrategyInstantLabelTypeEnum.SMS_PARAM) {
                        value = valueType.smsFormatValue(result, Constants.SMS_CURRENCY_FORMAT);
                    } else {
                        value = valueType.normalizeValueNullNoValue(labelName, result);
                    }
                    subResultMap.put(labelName, value);
                }
            }
            if (queryType == StrategyInstantLabelTypeEnum.SMS_PARAM) {
                filterLabelMinValue(subResultMap, bizEventVO.getAppUserId(), bizEventVO.getStrategyId());
            }

            Map<Long, Map<String, Object>> strategy2ResultMap = new HashMap<>(strategyId2LabelListMap.size());
            for (Map.Entry<Long, Collection<String>> entry : strategyId2LabelListMap.entrySet()) {
                Collection<String> labelNameList = entry.getValue();
                Map<String, Object> labelValuePerStrategy = new HashMap<>(labelNameList.size());
                for (String labelName : labelNameList) {
                    if (subResultMap.containsKey(labelName)) {
                        labelValuePerStrategy.put(labelName, subResultMap.get(labelName));
                    }
                }
                Long strategyId = entry.getKey();
                strategy2ResultMap.put(strategyId, labelValuePerStrategy);
            }

            resultMap.put(bizEventVO.getAppUserId(), strategy2ResultMap);
            log.info("(批量查询)T0策略标签查询 单个用户实时标签查询接口耗时：{}ms", timer.interval());
            return resultMap;
        } catch (TimeoutException te) {
            // 实时标签查询超时异常
            if (StrategyInstantLabelTypeEnum.LABEL == queryType) {
                //判断重发次数，超过重发次数预警
                if (bizEventVO.getRetryNum() == null) {
                    bizEventVO.setRetryNum(0);
                }
                if (bizEventVO.getRetryNum() < strategyConfig.getRetryTimes()) {
                    try {
                        String newMessageId = getOriginalMessageId(bizEventVO.getMessageId()) + "_" + (bizEventVO.getRetryNum() + 1);
                        bizEventMqService.sendMiddleLevelDelayMessage(bizEventVO, newMessageId, strategyConfig.getLabelTimeOutRetryInterval());
                        log.info("(批量查询)T0策略标签查询 策略ID={}, 消息ID={}, 用户ID={}，重试次数={}", strategyId2LabelListMap.keySet(), bizEventVO.getMessageId(), bizEventVO.getAppUserId(), bizEventVO.getRetryNum());
                    } catch (IOException e) {
                        log.warn("(批量查询)T0策略标签查询 策略ID={}, 用户ID={}", bizEventVO.getStrategyId(), bizEventVO.getAppUserId(), e);
                    }
                } else {
                    alarmTimeOut(InterfaceTypeEnum.REALTIME, queryType, bizEventVO.getStrategyId(), bizEventVO.getAppUserId(), null);
                }
            }
            throw new StrategyException("(批量查询)T0策略标签查询 查询异常", te);
        } catch (BizException e) {
            this.alarmProgramEx(bizEventVO.getStrategyId());
            throw new StrategyException("(批量查询)T0策略标签查询 查询异常", e);
        }
    }

    @Override
    public boolean filterLabelMinValue(Map<String, Object> param, Long userId, Long strategyId) {
        AtomicBoolean removeFlag = new AtomicBoolean(false);
        if (!CollectionUtils.isEmpty(param)) {
            Map<String, Object> labelToMinValue = appConfigService.getLabelMinValue();
            labelToMinValue.forEach((labelCode, minValue) -> {
                if (param.containsKey(labelCode) && Objects.nonNull(minValue) && new BigDecimal(param.get(labelCode).toString()).compareTo(new BigDecimal(minValue.toString())) <= 0) {
                    log.info("{}低于最小阀值限制, 阀值:{}, 策略id:{}, 用户:{}, availableBalance:{}", labelCode, minValue, strategyId, userId, param.get(labelCode));
                    param.remove(labelCode);
                    removeFlag.set(true);
                }
            });
        }
        return removeFlag.get();
    }

    private static String getOriginalMessageId(String messageId) {
        int endIndex = messageId.indexOf("_", messageId.indexOf("_") + 1);
        return endIndex < 0 ? messageId : messageId.substring(0, endIndex);
    }

    @Override
    public Map<Long, Map<String, Object>> queryBatch(BatchAdsLabelVO batchAdsLabelVO, Collection<String> labelNameList, StrategyInstantLabelTypeEnum queryType, StrategyTypeEnum strategyType) {
        final TimeInterval timer = new TimeInterval();
        BatchAdsLabelParamVO batchAdsLabelParamVO = this.toBatchAdsLabelReqVO(batchAdsLabelVO);
        Map<Long, Map<String, Object>> resultMap = new HashMap<>(16);
        try {
            List<String> grayLabels = new ArrayList<>();
            List<String> queryInterfaceLabels = new ArrayList<>();
            Map<String, StrategyInstantLabelDo> labelMetaMap = new HashMap<>();
            for (String labelName : labelNameList) {
                StrategyInstantLabelDo labelInfo = strategyInstantLabelService.getByLabelNameAndLabelType(labelName, queryType, strategyType.getCode(), null);
                if (labelInfo == null) {
                    throw new BizException("离线策略标签查询 需要查询的标签不在标签元数据表中");
                }
                labelMetaMap.put(labelName, labelInfo);

                //查询接口类的实时标签
                if (Objects.equals(StrategyCustomProcessEnum.IN_INTERFACE.getCode(), labelInfo.getCustomProcess())) {
                    queryInterfaceLabels.add(labelName);
                    continue;
                }
                //查询变量中心的实时标签
                grayLabels.add(labelName);
            }
            log.info("离线策略标签查询 策略id={}, labelNameList={}, 接口标签={}, 变量中心标签={}", batchAdsLabelVO.getStrategyId(), JsonUtil.toJson(labelNameList), JsonUtil.toJson(queryInterfaceLabels), JsonUtil.toJson(grayLabels));

            // 查询结果
            List<AdsLabelResp> payload = new ArrayList<>();
            batchAdsLabelParamVO.getAppUserId().stream().forEach(appUserId -> {
                resultMap.put(appUserId, new HashMap<>(16));
            });

            StrategyDo strategyDo = cacheStrategyService.selectById(batchAdsLabelVO.getStrategyId());
            Map<Long, BatchAdsLabelVO.UserInfo> map1 = null;
            Map<String, BatchAdsLabelVO.UserInfo> map2 = null;
            Map<String, Long> mobileMap = null;
            List<String> existSpecialLabel = null;
            boolean specialLabelFLag = false;
            try {
                map1 = batchAdsLabelVO.getUserInfoList().stream().collect(Collectors.toMap(BatchAdsLabelVO.UserInfo::getAppUserId, Function.identity()));
                map2 = batchAdsLabelVO.getUserInfoList().stream().collect(Collectors.toMap(BatchAdsLabelVO.UserInfo::getMobile, Function.identity()));
                mobileMap = batchAdsLabelVO.getUserInfoList().stream().collect(Collectors.toMap(BatchAdsLabelVO.UserInfo::getMobile, BatchAdsLabelVO.UserInfo::getAppUserId));
                // 独立标签获取值
                List<String> flowSpecialStartTimeLabels = appConfigService.getFlowSpecialStartTimeLabels();
                existSpecialLabel = new ArrayList<>(CollectionUtil.intersection(grayLabels, flowSpecialStartTimeLabels));
                specialLabelFLag = !CollectionUtils.isEmpty(existSpecialLabel);
            } catch (Exception e) {
                log.error("离线策略标签查询异常 策略id={}", strategyDo.getId(), e);
            }

            if (!CollectionUtils.isEmpty(grayLabels)) {
                List<VariableLabelParamVO> variableLabelParamVOList = new ArrayList<>();
                boolean flowFlag = false;
                for (BatchAdsLabelVO.UserInfo item : batchAdsLabelVO.getUserInfoList()) {
                    VariableLabelParamVO variableLabelParamVO = new VariableLabelParamVO();
                    variableLabelParamVO.setAppUserId(item.getAppUserId());
                    variableLabelParamVO.setMobile(item.getMobile());
                    if (specialLabelFLag && StringUtils.isNotEmpty(item.getFlowBatchNo())) {
                        flowFlag = true;
                        variableLabelParamVO.setStartTime(flowBatchNoToStartTimeCache.get(item.getFlowBatchNo(), k -> {
                            log.info(String.format("离线策略标签查询 特殊标签查startTime-查询数据库strategy_flow_batch表，入参%s", k));
                            StrategyFlowBatchDo flowBatchDo = strategyFlowBatchRepository.selectByBatchNo(k);
                            if (flowBatchDo != null) {
                                return DateTimeUtil.formatDateToStr(flowBatchDo.getCreatedTime(), null);
                            }
                            return null;
                        }));
                    }
                    variableLabelParamVOList.add(variableLabelParamVO);
                }

                if (specialLabelFLag && flowFlag) {
                    List<String> finalExistSpecialLabel = existSpecialLabel;
                    variableLabelParamVOList.forEach(x -> {
                        x.setLabelName(finalExistSpecialLabel);
                    });
                    List<AdsLabelResp> newLabelResps = this.queryBatchAdsWrapper(variableLabelParamVOList);
                    payload.addAll(newLabelResps);
                    log.info("离线策略标签查询 变量中心查询-画布特殊标签, 策略id={}, 查询类型={}, 标签名称={}, 标签结果={}", batchAdsLabelVO.getStrategyId(), queryType.getType(), JsonUtil.toJson(existSpecialLabel), JsonUtil.toJson(newLabelResps));
                    grayLabels.removeAll(existSpecialLabel);
                }

                if (!CollectionUtils.isEmpty(grayLabels)) {
                    variableLabelParamVOList.forEach(x -> {
                        x.setLabelName(grayLabels);
                        x.setStartTime(batchAdsLabelVO.getStartTime());
                    });

                    List<AdsLabelResp> newLabelResps = this.queryBatchAdsWrapper(variableLabelParamVOList);
                    payload.addAll(newLabelResps);
                    log.info("离线策略标签查询 变量中心查询, 策略id={}, 查询类型={}, 标签名称={}, 标签结果={}", batchAdsLabelVO.getStrategyId(), queryType.getType(), JsonUtil.toJson(grayLabels), JsonUtil.toJson(newLabelResps));
                }
            }
            //执行查询接口的实时标签
            if (!CollectionUtils.isEmpty(queryInterfaceLabels)) {
                //后续可以考虑用线程池执行
                for (String queryInterfaceLabel : queryInterfaceLabels) {
                    QueryLabelRequest queryLabelRequest = new QueryLabelRequest();
                    queryLabelRequest.setUserNoList(batchAdsLabelVO.getUserInfoList().stream().map(BatchAdsLabelVO.UserInfo::getAppUserId).collect(Collectors.toList()));
                    if (CollectionUtils.isEmpty(payload)) {
                        payload = new ArrayList<>();
                    }
                    AdsLabelResp adsLabelResp = labelHandlerSelector.doQueryLabel(queryInterfaceLabel, queryLabelRequest);
                    payload.add(adsLabelResp);
                    log.info("离线策略标签查询 接口查询, 策略id={}, 查询类型={}, 标签名称={}, 标签结果={}", batchAdsLabelVO.getStrategyId(), queryType.getType(), JsonUtil.toJson(queryInterfaceLabel), JsonUtil.toJson(adsLabelResp));
                }
            }

            for (AdsLabelResp adsLabelResp : payload) {
                String labelName = adsLabelResp.getLabel();
                StrategyInstantLabelDo strategyInstantLabelDo = labelMetaMap.get(labelName);
                ValueTypeEnum valueType = ValueTypeEnum.getInstance(strategyInstantLabelDo.getLabelValueType());
                List<AdsLabelResp.Param> paramList = adsLabelResp.getParams();
                // 标签查询不到一个用户
                if (CollectionUtils.isEmpty(paramList)) {
                    // 短信类型，遍历下个标签
                    if (queryType == StrategyInstantLabelTypeEnum.SMS_PARAM) {
                        continue;
                    }
                    // 非短信，补充接口查询不到的用户
                    batchAdsLabelParamVO.getAppUserId().forEach(appUserId -> {
                        resultMap.get(appUserId).put(labelName, valueType.normalizeValueNullNoValue(labelName, null));
                    });
                    continue;
                }
                // 接口查询到的用户
                for (AdsLabelResp.Param subItem : paramList) {
                    LocalDateTime registerTime = getRegisterTime(map1, map2, subItem, batchAdsLabelVO.getApp());
                    // 标签值加工
                    String result = labelUtmSourceProcess(batchAdsLabelVO.getStrategyId(), labelName, subItem.getResult(), strategyInstantLabelDo.getCustomProcess(), registerTime);
                    if (subItem.getAppUserId() != null) {
                        if (queryType == StrategyInstantLabelTypeEnum.SMS_PARAM) {
                            resultMap.get(subItem.getAppUserId()).put(labelName, valueType.smsFormatValue(result, Constants.SMS_CURRENCY_FORMAT));
                        } else {
                            resultMap.get(subItem.getAppUserId()).put(labelName, valueType.normalizeValueNullNoValue(labelName, result));
                        }
                    } else if (mobileMap.get(subItem.getMobile()) != null) {
                        if (queryType == StrategyInstantLabelTypeEnum.SMS_PARAM) {
                            resultMap.get(Long.parseLong(mobileMap.get(subItem.getMobile()).toString())).put(labelName, valueType.smsFormatValue(result, Constants.SMS_CURRENCY_FORMAT));
                        } else {
                            resultMap.get(Long.parseLong(mobileMap.get(subItem.getMobile()).toString())).put(labelName, valueType.normalizeValueNullNoValue(labelName, result));
                        }
                    }
                }
                // 短信类型，遍历下个标签
                if (queryType == StrategyInstantLabelTypeEnum.SMS_PARAM) {
                    continue;
                }
                // 非短信，补充接口查询不到的用户
                if (paramList.get(0).getAppUserId() != null) {
                    List<Long> outAppUserId = batchAdsLabelParamVO.getAppUserId().stream().filter(appUserId -> paramList.stream().allMatch(param -> !appUserId.equals(param.getAppUserId()))).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(outAppUserId)) {
                        outAppUserId.forEach(appUserId -> {
                            resultMap.get(appUserId).put(labelName, valueType.normalizeValueNullNoValue(labelName, null));
                        });
                    }
                } else if (StringUtils.isNotBlank(paramList.get(0).getMobile())) {
                    List<String> outMobile = batchAdsLabelParamVO.getMobile().stream().filter(mobile -> paramList.stream().allMatch(param -> !mobile.equals(param.getMobile()))).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(outMobile)) {
                        Map<String, Long> finalMobileMap = mobileMap;
                        outMobile.forEach(mobile -> {
                            resultMap.get(Long.parseLong(finalMobileMap.get(mobile).toString())).put(labelName, valueType.normalizeValueNullNoValue(labelName, null));
                        });
                    }
                }
            }
            // 查询短信参数时，用户存在一个参数值为空，告警
            if (queryType == StrategyInstantLabelTypeEnum.SMS_PARAM) {
                batchAdsLabelParamVO.getAppUserId().forEach(appUserId -> {
                    if (resultMap.get(appUserId).size() < labelNameList.size()) {
                        this.alarm(dingTalkConfig, strategyDo, appUserId);
                    }
                });
            }
            log.info("离线策略标签查询 策略id={}, 查询类型={} 变量中心标签={}, 接口标签={}, 查询结果={} 查询耗时={}ms", batchAdsLabelVO.getStrategyId(), queryType.getType(), JsonUtil.toJson(grayLabels), JsonUtil.toJson(queryInterfaceLabels), JsonUtil.toJson(resultMap), timer.interval());

            // 可用余额标签过滤，临时紧急
            if (queryType == StrategyInstantLabelTypeEnum.SMS_PARAM) {
                resultMap.forEach((key, value) -> {
                    filterLabelMinValue(value, key, batchAdsLabelVO.getStrategyId());
                });
            }
            return resultMap;
        } catch (BizException e) {
            if (e.getCode() != -1) {
                this.alarmProgramEx(batchAdsLabelVO.getStrategyId());
            }
            throw new StrategyException("离线策略标签查询 查询异常", e);
        }
    }

    private LocalDateTime getRegisterTime(Map<Long, BatchAdsLabelVO.UserInfo> map1, Map<String, BatchAdsLabelVO.UserInfo> map2, AdsLabelResp.Param subItem, String app) {
        LocalDateTime registerTime = null;
        if (Objects.nonNull(subItem.getAppUserId())) {
            registerTime = Optional.ofNullable(map1.get(subItem.getAppUserId())).map(BatchAdsLabelVO.UserInfo::getRegisterTime).orElse(null);
        }

        if (StringUtils.isNotBlank(subItem.getMobile())) {
            registerTime = Optional.ofNullable(map2.get(subItem.getMobile())).map(BatchAdsLabelVO.UserInfo::getRegisterTime).orElse(null);
        }

        if (registerTime == null) {
            BaseCisResp<RegisterInfoByUserNo.RespDto> resp = cisService.queryRegisterInfoByUserNo(subItem.getAppUserId());
            if (resp != null && resp.getData() != null) {
                String registerTimeStr = resp.getData().getRegisterTime();
                if (StringUtils.isNotEmpty(registerTimeStr)) {
                    Date dt = DateUtil.convert(registerTimeStr);
                    registerTime = DateUtil.convert(dt);
                }
            }
        }
        return registerTime;
    }

    /**
     * 渠道标签加工
     *
     * @param labelName     标签名
     * @param labelValue    标签值
     * @param customProcess 是否自定义加工
     * @param registerTime  注册时间
     * @return 标签值
     */
    private String labelUtmSourceProcess(Long strategyId, String labelName, String labelValue, Integer customProcess, LocalDateTime registerTime) {
        if (Objects.equals(StrategyCustomProcessEnum.NEED_PROCESS.getCode(), customProcess)) {
            return String.valueOf(strategyMarketEventConditionService.isMarketable(strategyId, labelValue, labelName, registerTime));
        }
        return labelValue;
    }

    private String labelUtmSourceProcessBatch(Collection<Long> strategyIdList, String labelName, String labelValue, Integer customProcess, LocalDateTime registerTime) {
        if (Objects.equals(StrategyCustomProcessEnum.NEED_PROCESS.getCode(), customProcess)) {
            return String.valueOf(strategyMarketEventConditionService.isMarketableBatch(strategyIdList, labelValue, labelName, registerTime));
        }
        return labelValue;
    }

    private BatchAdsLabelParamVO toBatchAdsLabelReqVO(BatchAdsLabelVO batchAdsLabelVO) {
        BatchAdsLabelParamVO batchAdsLabelParamVO = new BatchAdsLabelParamVO();
        List<Long> appUserId = batchAdsLabelVO.getUserInfoList().stream().map(BatchAdsLabelVO.UserInfo::getAppUserId).collect(Collectors.toList());
        List<String> mobile = batchAdsLabelVO.getUserInfoList().stream().map(BatchAdsLabelVO.UserInfo::getMobile).collect(Collectors.toList());
        batchAdsLabelParamVO.setApp(batchAdsLabelVO.getApp());
        batchAdsLabelParamVO.setStartTime(batchAdsLabelVO.getStartTime());
        batchAdsLabelParamVO.setAppUserId(appUserId);
        batchAdsLabelParamVO.setMobile(mobile);
        return batchAdsLabelParamVO;
    }

    private void alarm(DingTalkConfig dingTalkConfig, StrategyDo strategyDo, Long appUserId) {
        List<String> atMobileList = new ArrayList<>();
        atMobileList.add(strategyDo.getUpdatedOpMobile());
        DingTalkUtil.dingTalk(dingTalkConfig, strategyDo, appUserId, atMobileList);
    }
}
