package com.xftech.cdp.infra.repository.cdp.crowd.po;

import com.xftech.cdp.domain.crowd.model.enums.CrowdPushBatchStatusEnum;
import com.xftech.cdp.domain.crowd.model.enums.CrowdPushQueryStatusEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyMarketChannelEnum;
import com.xftech.cdp.infra.repository.Do;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.Arrays;

/**
 * <p>
 * 人群推送批次
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CrowdPushBatchDo extends Do {
    private static final String[] SEND_SUCCESS_CODE = {"1", "000000", "success"};

    /**
     * 主键id
     */
    private Long id;

    /**
     * 执行记录id
     */
    private Integer xxlJobId;

    /**
     * 策略id
     */
    private Long strategyId;

    /**
     * 分组id
     */
    private Long strategyGroupId;

    /**
     * 策略渠道id
     */
    private Long strategyMarketChannelId;

    /**
     * 策略执行日志id
     */
    private Long strategyExecLogId;

    /**
     * 触达渠道：0: 不营销，1:短信，2:电销，3:优惠券
     */
    private Integer marketChannel;

    /**
     * 模板id
     */
    private String templateId;

    /**
     * 批次号
     */
    private String batchNum;

    /**
     * 批次数量
     */
    private Integer batchTotal;

    /**
     * 成功数量
     */
    private Integer smsSendCount;

    /**
     * 成功数量
     */
    private Integer succCount;

    /**
     * 失败数量
     */
    private Integer failCount;

    /**
     * 使用数量
     */
    private Integer usedCount;

    /**
     * 发送状态
     */
    private String sendCode;

    /**
     * 发送描述
     */
    private String sendMsg;

    /**
     * 结果查询状态：-1:无需查询 0:未查询 1:轮询中 2:已完成
     */
    private Integer queryStatus;

    /**
     * 执行类型：0：正常 1：重试
     */
    private Integer execType;

    /**
     * 批次状态：0：已完成 1：可重试
     */
    private String batchStatus;

    /**
     * 手机号批次
     */
    private String mobileBatch;

    /**
     * 明细表序号
     */
    private String detailTableNo;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;


    private Integer dFlag;

    private String createdOp;

    private String updatedOp;

    public void initQueryStatus(String sendCode) {
        StrategyMarketChannelEnum channelEnum = StrategyMarketChannelEnum.getInstance(marketChannel);
        if (Arrays.asList(SEND_SUCCESS_CODE).contains(sendCode)) {
            this.setBatchStatus(CrowdPushBatchStatusEnum.FINISH.getCode());
            if (channelEnum == StrategyMarketChannelEnum.VOICE) {
                this.setQueryStatus(CrowdPushQueryStatusEnum.COMPLETED.getCode());
                return;
            }
            this.setQueryStatus(CrowdPushQueryStatusEnum.WAIT_QUERIED.getCode());
        } else {
            this.setQueryStatus(CrowdPushQueryStatusEnum.DIS_QUERY.getCode());
            this.setBatchStatus(CrowdPushBatchStatusEnum.CAN_RETRY.getCode());
        }
    }

}
