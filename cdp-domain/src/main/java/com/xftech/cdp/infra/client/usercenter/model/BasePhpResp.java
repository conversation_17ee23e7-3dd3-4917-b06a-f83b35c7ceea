package com.xftech.cdp.infra.client.usercenter.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * <AUTHOR> Aven
 */
@Data
public class BasePhpResp<Response> {

    private static final long serialVersionUID = 1L;

    @JsonProperty("status")
    @SerializedName("status")
    private Integer status;

    @JsonProperty("message")
    @SerializedName("message")
    private String message;

    @JsonProperty("time")
    @SerializedName("time")
    private Long time;

    @JsonProperty("response")
    @SerializedName("response")
    private Response response;

    public boolean isSuccess() {
        return status != null && status.equals(1);
    }


}
