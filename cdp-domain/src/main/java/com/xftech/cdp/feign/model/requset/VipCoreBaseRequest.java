/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.feign.model.requset;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

/**
 * <AUTHOR>
 * @version $ VipCoreBaseRequest, v 0.1 2024/6/13 17:34 lingang.han Exp $
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class VipCoreBaseRequest {

    /**
     * 上游调用的ua，例如 xyf-cdp
     */
    private String ua = "xyf-cdp";

    /**
     * 使用场景
     */
    private String scene = "query";

    /**
     * 以秒为单位的时间戳
     */
    private Long requestTime = Instant.now().getEpochSecond();
}