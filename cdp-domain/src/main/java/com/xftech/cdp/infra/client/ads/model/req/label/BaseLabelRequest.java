package com.xftech.cdp.infra.client.ads.model.req.label;

import lombok.Data;

/**
 * @<NAME_EMAIL>
 */
@Data
public class BaseLabelRequest<T> {
    /**
     * 必须
     * 请求流水号
     */
    private String requestId;
    /**
     * 必须
     * 调用方系统简称，麻雀： xyf-cdp
     */
    private String ua;
    /**
     * 必须
     * 接口名称
     */
    private String apiName;
    /**
     * 必须
     * 时间戳
     */
    private Long timestamp = System.currentTimeMillis() / 1000L;
    /**
     * 请求参数
     */
    private T data;

    public BaseLabelRequest() {
    }

    public BaseLabelRequest(T data) {
        this.data = data;
    }
}
