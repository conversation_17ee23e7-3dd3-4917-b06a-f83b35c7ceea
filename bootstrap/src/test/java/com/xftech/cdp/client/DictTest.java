package com.xftech.cdp.client;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.xftech.cdp.BaseTest;
import com.xftech.cdp.api.DictApi;
import com.xftech.cdp.api.dto.base.Response;
import com.xftech.cdp.api.dto.req.dict.EventMetaDataReq;
import com.xftech.cdp.api.dto.req.external.CallingSourceEnum;
import com.xftech.cdp.api.dto.resp.dict.CallingSourceResp;
import com.xftech.cdp.api.dto.resp.dict.EventMetaDataResp;
import com.xftech.cdp.api.dto.resp.dict.SubEventData;
import com.xftech.cdp.domain.ads.service.AdsStrategyLabelService;
import com.xftech.cdp.domain.dict.service.DictService;
import com.xftech.cdp.domain.strategy.model.enums.StrategyInstantLabelTypeEnum;
import com.xftech.cdp.domain.strategy.vo.BatchAdsLabelVO;
import com.xftech.cdp.infra.utils.JsonUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/2/16
 */
public class DictTest extends BaseTest {

    @Autowired
    private DictApi dictApi;

    @Test
    public void queryUserLabels() {
        Response<List<CallingSourceResp>> listResponse = dictApi.queryT0CallingSource();
        for (CallingSourceResp resp : listResponse.getResult()) {
            System.out.println(resp);
        }
    }

    @Test
    public void testQueryEventMetaData() {
        EventMetaDataReq req = new EventMetaDataReq();
        req.setBusinessType("old-cust");
        req.setStrategyType(2);
        Response<List<EventMetaDataResp>> resp = dictApi.queryEventMetaData(req);
        for (EventMetaDataResp eventMetaDataResp : resp.getResult()) {
            System.out.print("DictType: ");
            System.out.println(eventMetaDataResp.getDictType());
            System.out.print("DictTypeName: ");
            System.out.println(eventMetaDataResp.getDictTypeName());
            for (EventMetaDataResp.EventDict eventDict : eventMetaDataResp.getEventDictList()) {
                System.out.print("\tEventDesc: ");
                System.out.println(eventDict.getEventDesc());
                System.out.print("\tEventName: ");
                System.out.println(eventDict.getEventName());
                System.out.print("\tEventType: ");
                System.out.println(eventDict.getEventType());
                for (SubEventData subEventData : eventDict.getSubEventDataList()) {
                    System.out.print("\t\tSubEventData");
                    System.out.println(JsonUtil.toJson(subEventData));
                }
            }
            System.out.println("------------------------------------------");
        }
    }
}
