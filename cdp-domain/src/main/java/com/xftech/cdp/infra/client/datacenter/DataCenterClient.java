package com.xftech.cdp.infra.client.datacenter;

import com.alibaba.fastjson.JSON;
import com.xftech.cdp.infra.client.ads.model.req.AesDecodeReq;
import com.xftech.cdp.infra.client.datacenter.config.DataCenterConfig;
import com.xftech.cdp.infra.client.datacenter.model.DecryptIdCardResp;
import com.xftech.cdp.infra.client.datacenter.model.DecryptMobileResp;
import com.xftech.cdp.infra.client.datacenter.model.IdCardNumber;
import com.xftech.cdp.infra.client.datacenter.model.MobileNumber;
import com.xftech.cdp.infra.redis.sequence.Sequence;
import com.xftech.cdp.infra.utils.HttpClientUtil;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import sun.misc.BASE64Decoder;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> wanghu
 * @since : 2022/8/11 10:14:54
 */
@Component
public class DataCenterClient {

    private static final Logger LOGGER = LoggerFactory.getLogger(DataCenterClient.class);

    private static final String KEY_NAME = "key";

    private static final String MOBILE_PROTYLE_NAME = "mobile_protyle";
    private static final String ID_CARD_PROTYLE_NAME = "id_card_protyle";

    private static final String Q_NO_NAME = "q_no=";

    private static final Map<String, Object> PARAMS = new HashMap<>();

    private static final Map<String, String> HEADERS = new HashMap<>();

    private static final List<NameValuePair>  FORM_PARAMS = new ArrayList<NameValuePair>() ;

    @Resource
    private DataCenterConfig dataCenterConfig;
    @Resource
    private HttpClientUtil httpClientUtil;

    @PostConstruct
    public void init() {
        PARAMS.put(KEY_NAME, dataCenterConfig.getMobileDecryptKey());
        FORM_PARAMS.add(new BasicNameValuePair(KEY_NAME, dataCenterConfig.getMobileDecryptKey()));
        HEADERS.put(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED.toString());
    }

    public MobileNumber decryptMobile(String encryptedMobile) {

        String url = dataCenterConfig.getDataCenterHost() + "/" + dataCenterConfig.getMobileDecryptRoute() + "?" + Q_NO_NAME + Sequence.nextId();
        try {
            String encryptedMobileDecoded = new String(new BASE64Decoder().decodeBuffer(encryptedMobile), StandardCharsets.UTF_8);
            FORM_PARAMS.add(new BasicNameValuePair(MOBILE_PROTYLE_NAME, encryptedMobileDecoded));
            LOGGER.info("decrypt encrypted mobile from data center,url:{}.", url);
            System.out.println(FORM_PARAMS);
            String resultStr = httpClientUtil.postForm(url, FORM_PARAMS, HEADERS);
            DecryptMobileResp resp = JSON.parseObject(resultStr, DecryptMobileResp.class);
            LOGGER.info("decrypt encrypted mobile from data center,resp:{}", resultStr);
            return resp.getData();
        }catch (IOException e) {
            LOGGER.error("decrypt encrypted mobile base64decode origin:{}", encryptedMobile, e);
            return null;
        } catch (Exception e) {
            LOGGER.error("decrypt encrypted mobile from data center,url:{},params:{},headers:{}", url, PARAMS, HEADERS, e);
            throw e;
        } finally {
            PARAMS.remove(MOBILE_PROTYLE_NAME);
        }
    }

    public IdCardNumber decryptIdCard(String encryptedIdCard) {
        FORM_PARAMS.add(new BasicNameValuePair(ID_CARD_PROTYLE_NAME, encryptedIdCard));
        String url = dataCenterConfig.getDataCenterHost() + "/" + dataCenterConfig.getIdCardDecryptRoute() + "?" + Q_NO_NAME + Sequence.nextId();

        try {
            LOGGER.info("decrypt encrypted id card from data center,url:{}.", url);
            String resultStr = httpClientUtil.postForm(url, FORM_PARAMS, HEADERS);
            DecryptIdCardResp resp = JSON.parseObject(resultStr, DecryptIdCardResp.class);
            LOGGER.info("decrypt encrypted id card from data center,resp:{}", resultStr);
            return resp.getData();
        } catch (Exception e) {
            LOGGER.error("decrypt encrypted id card from data center,url:{},params:{},headers:{}", url, PARAMS, HEADERS, e);
            throw e;
        } finally {
            PARAMS.remove(MOBILE_PROTYLE_NAME);
        }
    }

    public DecryptMobileResp decryptMobileThreadSafe(String encryptedMobile) {
        MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
        params.add(KEY_NAME, dataCenterConfig.getMobileDecryptKey());
        params.add(MOBILE_PROTYLE_NAME, encryptedMobile);
        String url = dataCenterConfig.getDataCenterHost() + "/" + dataCenterConfig.getMobileDecryptRoute() + "?" + Q_NO_NAME + Sequence.nextId();

        try {
            LOGGER.info("decrypt encrypted mobile from data center,url:{},params:{},headers:{}", url, params, HEADERS);
            String resultStr = httpClientUtil.postJson(url, params, HEADERS);
            LOGGER.info("decrypt encrypted mobile from data center,resp:{}", resultStr);
            return JSON.parseObject(resultStr, DecryptMobileResp.class);
        } catch (Exception e) {
            LOGGER.error("decrypt encrypted mobile from data center,url:{},params:{},headers:{}", url, params, HEADERS, e);
            throw e;
        }
    }

}
