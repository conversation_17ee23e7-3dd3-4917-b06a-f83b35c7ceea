package com.xftech.cdp.domain.dispatch;

import com.xftech.cdp.domain.dispatch.dto.DispatchDto;
import com.xftech.cdp.feign.model.PushUserData;
import com.xftech.cdp.infra.client.sms.model.SmsBatchWithParamArgs;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPushBatchDo;
import com.xftech.cdp.infra.rocketmq.dto.AiUserData;
import org.apache.commons.lang3.tuple.ImmutablePair;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @date 2023/5/23 14:41
 */
public interface BatchDispatchService {

    /**
     * 调用电销保存接口
     *
     * @param reach    策略执行初始化内容
     * @param app      app
     * @param innerApp innerApp
     * @param batch    需要下发的用户ID集合
     * @return ImmutablePair Left-下发成功数 Right-下发批次记录
     */
    ImmutablePair<Integer, CrowdPushBatchDo> sendTele(DispatchDto reach, String app, String innerApp, List<CrowdDetailDo> batch);


    /**
     * 调用优惠券批量下发接口
     *
     * @param reach    策略执行初始化内容
     * @param app      app
     * @param innerApp innerApp
     * @param batch    需要下发的用户集合
     * @return ImmutablePair Left-下发成功数 Right-下发批次记录
     */
    ImmutablePair<Integer, CrowdPushBatchDo> sendCoupon(DispatchDto reach, String app, String innerApp, List<CrowdDetailDo> batch);

    /**
     * 下发短信
     *
     * @param reach    策略上下文
     * @param app      app
     * @param innerApp innerApp
     * @param batch    手机号
     * @return 下发数量
     */
    ImmutablePair<Integer, CrowdPushBatchDo> sendSms(DispatchDto reach, String app, String innerApp, List<CrowdDetailDo> batch);

    /**
     * 下发短信
     *
     * @param reach    策略上下文
     * @param app      app
     * @param innerApp innerApp
     * @param batch    手机号
     * @return 下发数量
     */
    ImmutablePair<Integer, CrowdPushBatchDo> sendSmsWithParam(DispatchDto reach, String app, String innerApp, List<CrowdDetailDo> batch, List<SmsBatchWithParamArgs.Sms> params);

    ImmutablePair<Integer, CrowdPushBatchDo> sendNewTele(DispatchDto dispatchDto, String app, String innerApp, List<CrowdDetailDo> batch);

    ImmutablePair<Integer, CrowdPushBatchDo> sendPush(DispatchDto dispatchDto, String app, String innerApp, List<CrowdDetailDo> batch);

    ImmutablePair<Integer, CrowdPushBatchDo> sendPushWithParam(DispatchDto dispatchDto, String app, String innerApp, List<CrowdDetailDo> batch, List<PushUserData> params);

    ImmutablePair<Integer, CrowdPushBatchDo> sendAiProto(DispatchDto dispatchDto, String app, String innerApp, List<CrowdDetailDo> batch);
    ImmutablePair<Integer, CrowdPushBatchDo> sendAiProtoWithParam(DispatchDto dispatchDto, String app, String innerApp, List<CrowdDetailDo> batch, List<AiUserData> params);

    ImmutablePair<Integer, CrowdPushBatchDo> sendBatchIncreaseAmt(DispatchDto dispatchDto, String app, String innerApp, List<CrowdDetailDo> batch);

    ImmutablePair<Integer, CrowdPushBatchDo> sendXDayInterestFree(DispatchDto reach, String app, String innerApp, List<CrowdDetailDo> batch);

    ImmutablePair<Integer, CrowdPushBatchDo> sendLifeRights(DispatchDto reach, String app, String innerApp, List<CrowdDetailDo> batch);

    ImmutablePair<Integer, CrowdPushBatchDo> sendAPIOpenAmount(DispatchDto reach, String app, String innerApp, List<CrowdDetailDo> batch);
}
