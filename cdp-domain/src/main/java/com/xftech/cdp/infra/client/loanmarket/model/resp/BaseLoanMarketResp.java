package com.xftech.cdp.infra.client.loanmarket.model.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@ToString
public class BaseLoanMarketResp<Data> {

    private static final long serialVersionUID = 1L;

    @JsonProperty("code")
    @SerializedName("code")
    private String code;

    @JsonProperty("message")
    @SerializedName("message")
    private String message;

    @JsonProperty("data")
    @SerializedName("data")
    private Data data;

    public boolean isSuccess() {
        return code != null && code.equals("000000");
    }


}
