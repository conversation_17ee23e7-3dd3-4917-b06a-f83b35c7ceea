package com.xftech.cdp.domain.strategy.repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import javax.annotation.Resource;

import com.xftech.cdp.domain.ads.model.PredictDecisionDto;
import com.xftech.cdp.domain.ads.service.ModelPlatformService;
import com.xftech.cdp.domain.redecision.service.ReDecisionService;
import com.xftech.cdp.domain.strategy.model.enums.StrategyMarketChannelEnum;
import com.xftech.cdp.infra.client.ads.model.req.ModelPredictionReq;
import com.xftech.cdp.infra.client.usercenter.CisService;
import com.xftech.cdp.infra.client.usercenter.model.AcctInfoModel;
import com.xftech.cdp.infra.config.ApolloUtil;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventVO;
import com.xftech.cdp.infra.utils.JsonUtil;
import com.xftech.cdp.infra.utils.SerialNumberUtil;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @version v1.0 2024/11/10
 * @description AppBannerEngineRepository
 */
@Slf4j
@Repository
public class AppBannerEngineRepository {

    /* 弹窗-请求引擎 */
    public static final String REQUEST_PARAM_MOBILE = "mobile";
    public static final String REQUEST_PARAM_APP = "app";
    public static final String REQUEST_PARAM_CHOICE_TITLE = "choiceTitle";
    public static final String REQUEST_PARAM_INSTALLMENTS = "installments";
    public static final String REQUEST_PARAM_AMOUNT = "amount";
    public static final String REQUEST_PARAM_APP_BANNER_ID = "appBannerId";
    public static final String REQUEST_PARAM_CUST_NO = "custNo";
    public static final String REQUEST_PARAM_ORDER_NO = "orderNo";

    /* 弹窗提额-引擎返回 */
    private static final String TYPE_POPUP = "popup";
    private static final String PARAM_IS_POPUP = "isPopup";
    private static final String PARAM_BANNER_ID = "bannerId";
    private static final String PARAM_BEFORE_INCREASE_AMOUNT = "beforeIncreaseAmount";
    private static final String PARAM_AFTER_INCREASE_AMOUNT = "afterIncreaseAmount";
    private static final String PARAM_IS_INCREASE = "isIncrease";
    private static final String PARAM_INCREASE_INFO = "increaseInfo";
    private static final String PARAM_INCREASE_TYPE = "increaseType";
    private static final String PARAM_INCREASE_AMOUNT = "increaseAmount";
    private static final String PARAM_INCREASE_DAYS = "increaseDays";
    /* 挽留弹窗-引擎返回 */
    private static final String PARAM_IS_ACTIVITY = "isActivity";
    private static final String PARAM_ACTIVITY_ID = "activityId";
    private static final String PARAM_ACTIVITY_NAME = "activityName";

    @Resource
    private CisService cisService;
    @Resource
    private ModelPlatformService modelPlatformService;
    @Resource
    private ReDecisionService reDecisionService;

    @Deprecated
    public AppBannerEngineResult appBannerEnginePredict(String bannerId, String engineCode, Long userId, String mobile) {
        if (StringUtils.isAnyBlank(bannerId, engineCode, mobile) || userId == null) {
            return null;
        }
        AcctInfoModel acctInfo = cisService.getAcctNoByUserNo(userId);
        if (acctInfo == null) {
            log.warn("AppBannerEngineRepository appBannerEnginePredict noAppBanner by acctInfo. userId={}", userId);
            return null;
        }

        try {
            ModelPredictionReq modelPredictionReq = ModelPredictionReq.builder()
                    .model_name(engineCode)
                    .biz_data(ModelPredictionReq.BizData.builder()
                            .requestId(SerialNumberUtil.nextId())
                            .biz_type("AppBannerRecommend")
                            .app_user_id(userId)
                            .mobile(mobile)
                            .app(acctInfo.getApp())
                            .acct_no(acctInfo.getAccountNo())
                            .user_no(userId)
                            .trigger_datetime(System.currentTimeMillis())
                            .timestamp(System.currentTimeMillis())
                            .callerCount(1L)
                            .requestType("API")
                            .build())
                    .build();
            JSONObject resp = modelPlatformService.prediction(modelPredictionReq);
            log.info("AppBannerEngineRepository appBannerEnginePredict request={} response={}", JSON.toJSONString(modelPredictionReq), JSON.toJSONString(resp));

            /* 下发校验 */
            if (resp == null || !Objects.equals(resp.get("code"), 2000) || resp.getJSONObject("data").isEmpty()) {
                log.warn("AppBannerEngineRepository appBannerEnginePredict noAppBanner by engineFail. userId={}", userId);
                return null;
            }
            Object decisionVal = resp.getJSONObject("data").getJSONObject("output_data").get(engineCode);
            PredictDecisionDto predictDecisionDto = JsonUtil.parse(JsonUtil.toJson(decisionVal), PredictDecisionDto.class);
            if (predictDecisionDto == null || !predictDecisionDto.isSucced()) {
                return null;
            }
            List<PredictDecisionDto.DecisionData.Action> actions = predictDecisionDto.getActions();
            if (CollectionUtils.isEmpty(actions)) {
                return null;
            }
            PredictDecisionDto.DecisionData.Action action = actions.get(0);
            List<PredictDecisionDto.DecisionData.Action.Dispatch> dispatch = action.getDispatch();
            if (CollectionUtils.isEmpty(dispatch)) {
                return null;
            }
            PredictDecisionDto.DecisionData.Action.Dispatch popupDispatch = dispatch.get(0);
            if (popupDispatch == null || !StringUtils.equalsIgnoreCase(TYPE_POPUP, popupDispatch.getType())) {
                log.info("AppBannerEngineRepository appBannerEnginePredict noAppBanner by dispatchType. userId={}", userId);
                return null;
            }

            /* 弹窗校验 */
            Map<String, Object> detailInfo = Optional.ofNullable(popupDispatch.getDetail_info()).orElse(Maps.newHashMap());
            boolean isPopup = MapUtils.getBooleanValue(detailInfo, PARAM_IS_POPUP, false);
            if (!isPopup) {
                log.info("AppBannerEngineRepository appBannerEnginePredict noAppBanner by isPopup. userId={}", userId);
                return null;
            }
            String dispatchBannerId = MapUtils.getString(detailInfo, PARAM_BANNER_ID);
            if (!StringUtils.equals(bannerId, dispatchBannerId)) {
                log.info("AppBannerEngineRepository appBannerEnginePredict noAppBanner by dispatchBannerId. userId={}", userId);
                return null;
            }
            Long beforeIncreaseAmount = MapUtils.getLong(detailInfo, PARAM_BEFORE_INCREASE_AMOUNT);
            Long afterIncreaseAmount = MapUtils.getLong(detailInfo, PARAM_AFTER_INCREASE_AMOUNT);
            if (beforeIncreaseAmount == null || afterIncreaseAmount == null) {
                log.info("AppBannerEngineRepository appBannerEnginePredict noAppBanner by before|after IncreaseAmount. userId={}", userId);
                return null;
            }

            AppBannerEngineResult appBannerEngineResult = new AppBannerEngineResult();
            appBannerEngineResult.setPopup(true);
            appBannerEngineResult.setBeforeIncreaseAmount(beforeIncreaseAmount);
            appBannerEngineResult.setAfterIncreaseAmount(afterIncreaseAmount);
            appBannerEngineResult.setGroup_source(action.getGroup_source());
            boolean isIncrease = MapUtils.getBooleanValue(detailInfo, PARAM_IS_INCREASE, false);
            if (isIncrease) {
                String increaseInfo = MapUtils.getString(detailInfo, PARAM_INCREASE_INFO, "{}");
                Map<String, Object> increaseInfoMap = JSONObject.parseObject(increaseInfo, Map.class);
                String increaseType = MapUtils.getString(increaseInfoMap, PARAM_INCREASE_TYPE);
                Long increaseAmount = MapUtils.getLong(increaseInfoMap, PARAM_INCREASE_AMOUNT);
                Long increaseDays = MapUtils.getLong(increaseInfoMap, PARAM_INCREASE_DAYS);

                appBannerEngineResult.setIncrease(true);
                appBannerEngineResult.setIncreaseType(increaseType);
                appBannerEngineResult.setIncreaseAmount(increaseAmount);
                appBannerEngineResult.setIncreaseDays(increaseDays);
            }
            log.info("AppBannerEngineRepository appBannerEnginePredict appBannerEngineResult={}. userId={} ", JSON.toJSONString(appBannerEngineResult), userId);
            return appBannerEngineResult;
        } catch (Exception e) {
            log.error("AppBannerEngineRepository appBannerEnginePredict error={}", e.getMessage(), e);
        }
        return null;
    }

    /**
     * 获取弹窗引擎预测结果
     *
     * @param bannerId   弹窗ID
     * @param engineCode 引擎Code
     * @param userId     用户ID
     * @param params     预测参数
     * @return
     */
    public Map appBannerEnginePredictResult(String bannerId, String engineCode, Long userId, Map<String, Object> params,
                                            Long strategyId) {
        if (StringUtils.isAnyBlank(bannerId, engineCode) || userId == null) {
            return null;
        }
        params = Optional.ofNullable(params).orElse(Maps.newHashMap());
        try {
            String requestId = SerialNumberUtil.nextId();
            ModelPredictionReq modelPredictionReq = ModelPredictionReq.builder()
                    .model_name(engineCode)
                    .biz_data(ModelPredictionReq.BizData.builder()
                            .requestId(requestId)
                            .mobile(MapUtils.getString(params, REQUEST_PARAM_MOBILE))
                            .app(MapUtils.getString(params, REQUEST_PARAM_APP))
                            .app_user_id(userId)
                            .trigger_datetime(System.currentTimeMillis())
                            .biz_type("AppBannerRecommend")
                            .timestamp(System.currentTimeMillis())
                            .callerCount(1L)
                            .user_no(userId)
                            .choiceTitle(MapUtils.getString(params, REQUEST_PARAM_CHOICE_TITLE))
                            .installments(MapUtils.getInteger(params, REQUEST_PARAM_INSTALLMENTS))
                            .amount(MapUtils.getDouble(params, REQUEST_PARAM_AMOUNT))
                            .extMap(params) // 扩展字段,避免频繁调整,引擎侧会打平
                            .requestType("API")
                            .build())
                    .build();
            JSONObject resp = modelPlatformService.prediction(modelPredictionReq);
            log.info("AppBannerEngineRepository appBannerEnginePredictResult request={} response={}", JSON.toJSONString(modelPredictionReq), JSON.toJSONString(resp));

            /* 下发校验 */
            if (resp == null || !Objects.equals(resp.get("code"), 2000) || resp.getJSONObject("data").isEmpty()) {
                log.warn("AppBannerEngineRepository appBannerEnginePredictResult noAppBanner by engineFail. userId={}", userId);
                return null;
            }
            Object decisionVal = resp.getJSONObject("data").getJSONObject("output_data").get(engineCode);
            PredictDecisionDto predictDecisionDto = JsonUtil.parse(JsonUtil.toJson(decisionVal), PredictDecisionDto.class);
            if (predictDecisionDto == null || !predictDecisionDto.isSucced()) {
                return null;
            }
            List<PredictDecisionDto.DecisionData.Action> actions = predictDecisionDto.getActions();
            if (CollectionUtils.isEmpty(actions)) {
                return null;
            }

            // 引擎重推决策
            appBannerReDecision(actions, strategyId, userId, requestId, params, engineCode);

            PredictDecisionDto.DecisionData.Action action = actions.get(0);
            List<PredictDecisionDto.DecisionData.Action.Dispatch> dispatch = action.getDispatch();
            if (CollectionUtils.isEmpty(dispatch)) {
                return null;
            }
            PredictDecisionDto.DecisionData.Action.Dispatch popupDispatch = dispatch.get(0);
            if (popupDispatch == null || !StringUtils.equalsIgnoreCase(TYPE_POPUP, popupDispatch.getType())) {
                log.info("AppBannerEngineRepository appBannerEnginePredictResult noAppBanner by dispatchType. userId={}", userId);
                return null;
            }
            Map detailInfo = popupDispatch.getDetail_info();
            if (MapUtils.isEmpty(detailInfo)) {
                log.info("AppBannerEngineRepository appBannerEnginePredictResult noAppBanner by detailInfo. userId={}", userId);
                return null;
            }
            log.info("AppBannerEngineRepository appBannerEnginePredictResult detailInfo={}. userId={} ", JSON.toJSONString(detailInfo), userId);
            Optional.ofNullable(action.getGroup_source()).ifPresent(e -> detailInfo.put("group_source", e));
            return detailInfo;
        } catch (Exception e) {
            log.error("AppBannerEngineRepository appBannerEnginePredictResult error={}", e.getMessage(), e);
        }
        return null;
    }

    public static AppBannerEngineResult convertToEngineResult(Map detailInfo, Integer queryType) {
        if (MapUtils.isEmpty(detailInfo)) {
            return null;
        }
        boolean isPopup = MapUtils.getBooleanValue(detailInfo, PARAM_IS_POPUP, false);
        String bannerId = MapUtils.getString(detailInfo, PARAM_BANNER_ID, "");
        boolean isActivity = MapUtils.getBooleanValue(detailInfo, PARAM_IS_ACTIVITY, false);
        String activityId = MapUtils.getString(detailInfo, PARAM_ACTIVITY_ID, "");
        String activityName = MapUtils.getString(detailInfo, PARAM_ACTIVITY_NAME, "");

        AppBannerEngineResult appBannerEngineResult = new AppBannerEngineResult();
        appBannerEngineResult.setPopup(isPopup);
        if (isPopup && StringUtils.isNotBlank(bannerId)) {
            appBannerEngineResult.setBannerId(Long.parseLong(bannerId));
        }
        appBannerEngineResult.setActivity(isActivity);
        if (isActivity && StringUtils.isNotBlank(activityId)) {
            appBannerEngineResult.setActivityId(activityId);
        }
        if (Objects.equals(queryType, 4) && StringUtils.isNotBlank(activityId)) { // 结清弹窗，引擎下发活动ID
            appBannerEngineResult.setActivityId(activityId);
        }
        appBannerEngineResult.setActivityName(activityName); // // 结清弹窗，引擎下发自定义活动名称
        return appBannerEngineResult;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AppBannerEngineResult {
        /**
         * 分组标识
         */
        private String group_source;
        /**
         * 是否弹窗
         */
        private boolean isPopup;
        /**
         * 提额前金额
         */
        private Long beforeIncreaseAmount;
        /**
         * 提额后金额
         */
        private Long afterIncreaseAmount;
        /**
         * 是否提额
         */
        private boolean isIncrease;
        /**
         * 提额类型: 临额
         */
        private String increaseType;
        /**
         * 提额金额: 单位元
         */
        private Long increaseAmount;
        /**
         * 提额有效期: 单位天
         */
        private Long increaseDays;
        /**
         * 借款挽留：弹窗ID
         */
        private Long bannerId;
        /**
         * 借款挽留：是否发券
         */
        private boolean isActivity;
        /**
         * 借款挽留：发券ID
         */
        private String activityId;
        /**
         * 结清挽留：自定义券活动名称
         */
        private String activityName;
    }

    /**
     * 引擎重推决策
     *
     * @param actions    引擎决策结果
     * @param strategyId 策略ID
     * @param userId     用户ID
     * @param messageId  请求ID
     * @param params     请求引擎扩展参数
     */
    private void appBannerReDecision(List<PredictDecisionDto.DecisionData.Action> actions,
                                     Long strategyId, Long userId, String messageId, Map<String, Object> params,
                                     String engineCode) {
        try {
            if (CollectionUtils.isEmpty(actions) || strategyId == null || userId == null || StringUtils.isBlank(messageId)) {
                return;
            }

            for (PredictDecisionDto.DecisionData.Action action : actions) {
                String groupId = action.getGroup_id();
                List<PredictDecisionDto.DecisionData.Action.Dispatch> dispatchList = action.getDispatch();
                if (StringUtils.isBlank(groupId) || CollectionUtils.isEmpty(dispatchList)) {
                    continue;
                }

                for (PredictDecisionDto.DecisionData.Action.Dispatch dispatch : dispatchList) {
                    if (dispatch == null || MapUtils.isEmpty(dispatch.getDetail_info())) {
                        continue;
                    }

                    Map detailInfo = dispatch.getDetail_info();
                    StrategyMarketChannelEnum strategyMarketChannelEnum = StrategyMarketChannelEnum.getInstance(dispatch.getType());
                    if (strategyMarketChannelEnum == StrategyMarketChannelEnum.RE_DECISION) {
                        if (Objects.isNull(detailInfo.get("reinput_delay_second"))) {
                            log.warn("AppBannerEngineRepository 业务引擎-延迟决策,下发异常. 策略id={},用户id={}", strategyId, userId);
                            return;
                        }
                        int reInputDelaySecond = Integer.parseInt(detailInfo.get("reinput_delay_second").toString());
                        if (reInputDelaySecond <= 0 || reInputDelaySecond > 60 * 60 * 24) {
                            log.warn("AppBannerEngineRepository 业务引擎-延迟决策,下发异常. 策略id={},用户id={},延迟时间={}", strategyId, userId, reInputDelaySecond);
                            return;
                        }
                        BizEventVO bizEventVO = buildBizEventVO(strategyId, userId, messageId, params, engineCode);
                        reDecisionService.reDecision(groupId, reInputDelaySecond, bizEventVO);
                        return;
                    }
                }
            }
        } catch (Exception e) {
            log.error("AppBannerEngineRepository appBannerReDecision error={}", e.getMessage(), e);
        }
    }

    private BizEventVO buildBizEventVO(Long strategyId, Long userId, String messageId, Map<String, Object> params,
                                       String engineCode) {
        BizEventVO bizEventVO = new BizEventVO();
        bizEventVO.setStrategyId(strategyId);
        bizEventVO.setAppUserId(userId);
        bizEventVO.setMessageId(messageId);
        bizEventVO.setExt(Optional.ofNullable(params).orElse(Maps.newHashMap()));
        bizEventVO.setEngineCode(engineCode);

        bizEventVO.setMobile(MapUtils.getString(params, REQUEST_PARAM_MOBILE, ""));
        bizEventVO.setApp(MapUtils.getString(params, REQUEST_PARAM_APP, ""));
        bizEventVO.setInnerApp(MapUtils.getString(params, REQUEST_PARAM_APP, ""));

        bizEventVO.setTriggerDatetime(LocalDateTime.now());
        return bizEventVO;
    }

}
