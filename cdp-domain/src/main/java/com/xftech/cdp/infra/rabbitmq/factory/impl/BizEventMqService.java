package com.xftech.cdp.infra.rabbitmq.factory.impl;

import brave.Tracing;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.proxy.Tracer;
import com.rabbitmq.client.Channel;
import com.xftech.cdp.domain.mq.MqConsumeService;
import com.xftech.cdp.domain.strategy.model.enums.StrategyMarketChannelEnum;
import com.xftech.cdp.infra.constant.RedisKeyConstants;
import com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig;
import com.xftech.cdp.infra.rabbitmq.enums.DecisionResultEnum;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventMessageVO;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventVO;
import com.xftech.cdp.infra.utils.RedisKeyUtils;
import com.xftech.cdp.infra.utils.RedisUtils;
import com.xftech.rabbitmq.UdpMqConsumerManager;
import com.xftech.rabbitmq.UdpMqProducerManager;
import com.xftech.rabbitmq.build.BindParamBuilder;
import com.xftech.rabbitmq.build.ConnectionBuilder;
import com.xftech.rabbitmq.consumer.TraceBatchConsumer;
import com.xftech.rabbitmq.consumer.TraceSingleConsumer;
import com.xftech.rabbitmq.core.UdpMqConsumer;
import com.xftech.rabbitmq.core.UdpMqProducer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

@Service
public class BizEventMqService extends BaseMqService {
    private static final Logger logger = LoggerFactory.getLogger(BizEventMqConfig.class);
    // 事件节点队列-消费者
    public static final String BZ_CONSUMER_CONN_01 = "BZ_CONSUMER_CONN_01";
    public static final String BZ_HL_CONSUMER_01 = "BZ_HL_CONSUMER_01";
    public static final String BZ_HL_CONSUMER_02 = "BZ_HL_CONSUMER_02";
    public static final String BZ_ML_CONSUMER_01 = "BZ_ML_CONSUMER_01";

    public static final String BZ_ML_CONSUMER_02 = "BZ_ML_CONSUMER_02";
    // 事件延迟队列-生产者
    public static final String BZ_DELAY_PRODUCER_CONN_01 = "BZ_DELAY_PRODUCER_CONN_01";
    public static final String BZ_DELAY_HL_PRODUCER_01 = "BZ_DELAY_HL_PRODUCER_01";
    public static final String BZ_DELAY_ML_PRODUCER_01 = "BZ_DELAY_ML_PRODUCER_01";
    public static final String BZ_DELAY_LL_PRODUCER_01 = "BZ_DELAY_LL_PRODUCER_01";
    // 事件延迟队列-消费者
    public static final String BZ_DELAY_CONSUMER_CONN_01 = "BZ_DELAY_CONSUMER_CONN_01";
    public static final String BZ_DELAY_HL_CONSUMER_01 = "BZ_DELAY_HL_CONSUMER_01";
    public static final String BZ_DELAY_HL_CONSUMER_02 = "BZ_DELAY_HL_CONSUMER_02";
    public static final String BZ_DELAY_HL_CONSUMER_03 = "BZ_DELAY_HL_CONSUMER_03";
    public static final String BZ_DELAY_HL_CONSUMER_04 = "BZ_DELAY_HL_CONSUMER_04";
    public static final String BZ_DELAY_HL_CONSUMER_05 = "BZ_DELAY_HL_CONSUMER_05";
    public static final String BZ_DELAY_HL_CONSUMER_06 = "BZ_DELAY_HL_CONSUMER_06";
    public static final String BZ_DELAY_HL_CONSUMER_07 = "BZ_DELAY_HL_CONSUMER_07";
    public static final String BZ_DELAY_HL_CONSUMER_08 = "BZ_DELAY_HL_CONSUMER_08";
    public static final String BZ_DELAY_ML_CONSUMER_01 = "BZ_DELAY_ML_CONSUMER_01";
    public static final String BZ_DELAY_ML_CONSUMER_02 = "BZ_DELAY_ML_CONSUMER_02";
    public static final String BZ_DELAY_ML_CONSUMER_03 = "BZ_DELAY_ML_CONSUMER_03";
    public static final String BZ_DELAY_ML_CONSUMER_04 = "BZ_DELAY_ML_CONSUMER_04";
    public static final String BZ_DELAY_ML_CONSUMER_05 = "BZ_DELAY_ML_CONSUMER_05";
    public static final String BZ_DELAY_ML_CONSUMER_06 = "BZ_DELAY_ML_CONSUMER_06";
    public static final String BZ_DELAY_ML_CONSUMER_07 = "BZ_DELAY_ML_CONSUMER_07";
    public static final String BZ_DELAY_ML_CONSUMER_08 = "BZ_DELAY_ML_CONSUMER_08";
    public static final String BZ_DELAY_LL_CONSUMER_01 = "BZ_DELAY_LL_CONSUMER_01";
    public static final String BZ_DELAY_LL_CONSUMER_02 = "BZ_DELAY_LL_CONSUMER_02";
    public static final String BZ_DELAY_LL_CONSUMER_03 = "BZ_DELAY_LL_CONSUMER_03";
    public static final String BZ_DELAY_LL_CONSUMER_04 = "BZ_DELAY_LL_CONSUMER_04";
    public static final String BZ_DELAY_LL_CONSUMER_05 = "BZ_DELAY_LL_CONSUMER_05";
    public static final String BZ_DELAY_LL_CONSUMER_06 = "BZ_DELAY_LL_CONSUMER_06";
    // 事件触达队列-生产者
    public static final String BZ_DISPATCH_PRODUCER_CONN_01 = "BZ_DISPATCH_PRODUCER_CONN_01";
    public static final String BZ_SMS_DISPATCH_PRODUCER_01 = "BZ_SMS_DISPATCH_PRODUCER_01";
    public static final String BZ_TELE_DISPATCH_PRODUCER_01 = "BZ_TELE_DISPATCH_PRODUCER_01";
    public static final String BZ_COUPON_DISPATCH_PRODUCER_01 = "BZ_COUPON_DISPATCH_PRODUCER_01";
    public static final String BZ_PUSH_DISPATCH_PRODUCER_01 = "BZ_PUSH_DISPATCH_PRODUCER_01";
    public static final String BZ_INCREASEAMT_DISPATCH_PRODUCER_01 = "BZ_INCREASEAMT_DISPATCH_PRODUCER_01";
    public static final String BZ_LIFERIGHTS_DISPATCH_PRODUCER_01 = "BZ_LIFERIGHTS_DISPATCH_PRODUCER_01";
    public static final String BZ_XDAYINTERESTFREE_DISPATCH_PRODUCER_01 = "BZ_XDAYINTERESTFREE_DISPATCH_PRODUCER_01";
    public static final String BZ_AI_DISPATCH_PRODUCER_01 = "BZ_AI_DISPATCH_PRODUCER_01";
    public static final String BZ_NOMARKET_DISPATCH_PRODUCER_01 = "BZ_NOMARKET_DISPATCH_PRODUCER_01";
    // 事件触达队列-消费者
    public static final String BZ_DISPATCH_CONSUMER_CONN_01 = "BZ_DISPATCH_CONSUMER_CONN_01";
    public static final String BZ_SMS_DISPATCH_CONSUMER_01 = "BZ_SMS_DISPATCH_CONSUMER_01";

    //    public static final String BZ_SMS_DISPATCH_CONSUMER_02 = "BZ_SMS_DISPATCH_CONSUMER_02";
    public static final String BZ_PUSH_DISPATCH_CONSUMER_01 = "BZ_PUSH_DISPATCH_CONSUMER_01";
    public static final String BZ_INCREASEAMT_DISPATCH_CONSUMER_01 = "BZ_INCREASEAMT_DISPATCH_CONSUMER_01";
    public static final String BZ_LIFERIGHTS_DISPATCH_CONSUMER_01 = "BZ_LIFERIGHTS_DISPATCH_CONSUMER_01";
    public static final String BZ_XDAYINTERESTFREE_DISPATCH_CONSUMER_01 = "BZ_XDAYINTERESTFREE_DISPATCH_CONSUMER_01";
    public static final String BZ_AI_DISPATCH_CONSUMER_01 = "BZ_AI_DISPATCH_CONSUMER_01";
    public static final String BZ_TELE_DISPATCH_CONSUMER_01 = "BZ_TELE_DISPATCH_CONSUMER_01";
    public static final String BZ_COUPON_DISPATCH_CONSUMER_01 = "BZ_COUPON_DISPATCH_CONSUMER_01";
    public static final String BZ_NOMARKET_DISPATCH_CONSUMER_01 = "BZ_NOMARKET_DISPATCH_CONSUMER_01";
    // 事件决策结果队列-生产者
    public static final String BZ_DECISION_PRODUCER_CONN_01 = "BZ_DECISION_PRODUCER_CONN_01";
    public static final String BZ_DECISION_PRODUCER_01 = "BZ_DECISION_PRODUCER_01";
    // 事件决策结果队列-消费者
    public static final String BZ_DECISION_CONSUMER_CONN_01 = "BZ_DECISION_CONSUMER_CONN_01";
    public static final String BZ_DECISION_CONSUMER_01 = "BZ_DECISION_CONSUMER_01";

    private Channel delayHlProducerChannel;
    private Channel delayMlProducerChannel;
    private Channel delayLlProducerChannel;
    private Channel smsDispatchProducerChannel;
    private Channel pushDispatchProducerChannel;
    private Channel increaseAmtDispatchProducerChannel;
    private Channel lifeRightsDispatchProducerChannel;
    private Channel xDayInterestFreeDispatchProducerChannel;
    private Channel aiDispatchProducerChannel;

    private Channel teleDispatchProducerChannel;
    private Channel couponDispatchProducerChannel;
    private Channel noMarketProducerChannel;
    private Channel decisionResultChannel;

    @Autowired
    private BizEventMqConfig bizEventMqConfig;
    @Autowired
    private MqConsumeService mqConsumeService;
    @Autowired
    private RedisUtils redisUtils;

    public void init() throws IOException, TimeoutException {
        // 事件节点消费者
        this.initBizConsumer();

        // 事件延迟生产者
        this.initDelayProducer();
        // 事件延迟消费者
        this.initDelayConsumer();

        // 事件触达生产者
        this.initDispatchProducer();
        // 事件触达消费者
        this.initDispatchConsumer();

        // 事件决策结果生产者
        this.initDecisionProducer();
        // 事件决策结果消费者
        this.initDecisionConsumer();
    }

    public void initBizConsumer() throws IOException, TimeoutException {
        UdpMqConsumer bizEventUdpMqConsumer = this.initConnection(BZ_CONSUMER_CONN_01);
        this.createMqHighLevelConsumer(bizEventUdpMqConsumer, BZ_HL_CONSUMER_01);
        this.createMqHighLevelConsumer(bizEventUdpMqConsumer, BZ_HL_CONSUMER_02);
        this.createMqMiddleLevelConsumer(bizEventUdpMqConsumer, BZ_ML_CONSUMER_01);
        this.createMqMiddleLevelConsumer(bizEventUdpMqConsumer, BZ_ML_CONSUMER_02);
        logger.info("事件节点队列-消费者：创建成功......");
    }

    public void initDelayProducer() throws IOException, TimeoutException {
        UdpMqProducer delayUdpMqProducer = this.initProducerConnection(BZ_DELAY_PRODUCER_CONN_01);
        this.createHighLevelDelayProducer(delayUdpMqProducer, BZ_DELAY_HL_PRODUCER_01);
        this.createMiddleLevelDelayProducer(delayUdpMqProducer, BZ_DELAY_ML_PRODUCER_01);
        this.createLowLevelDelayProducer(delayUdpMqProducer, BZ_DELAY_LL_PRODUCER_01);
        logger.info("事件延迟队列-生产者：创建成功......");
    }

    public void initDelayConsumer() throws IOException, TimeoutException {
        UdpMqConsumer delayUdpMqConsumer = this.initConnection(BZ_DELAY_CONSUMER_CONN_01);
        this.createHighLevelDelayConsumer(delayUdpMqConsumer, BZ_DELAY_HL_CONSUMER_01);
        this.createHighLevelDelayConsumer(delayUdpMqConsumer, BZ_DELAY_HL_CONSUMER_02);
        this.createHighLevelDelayConsumer(delayUdpMqConsumer, BZ_DELAY_HL_CONSUMER_03);
        this.createHighLevelDelayConsumer(delayUdpMqConsumer, BZ_DELAY_HL_CONSUMER_04);
        this.createHighLevelDelayConsumer(delayUdpMqConsumer, BZ_DELAY_HL_CONSUMER_05);
        this.createHighLevelDelayConsumer(delayUdpMqConsumer, BZ_DELAY_HL_CONSUMER_06);
        this.createHighLevelDelayConsumer(delayUdpMqConsumer, BZ_DELAY_HL_CONSUMER_07);
        this.createHighLevelDelayConsumer(delayUdpMqConsumer, BZ_DELAY_HL_CONSUMER_08);
        this.createMiddleLevelDelayConsumer(delayUdpMqConsumer, BZ_DELAY_ML_CONSUMER_01);
        this.createMiddleLevelDelayConsumer(delayUdpMqConsumer, BZ_DELAY_ML_CONSUMER_02);
        this.createMiddleLevelDelayConsumer(delayUdpMqConsumer, BZ_DELAY_ML_CONSUMER_03);
        this.createMiddleLevelDelayConsumer(delayUdpMqConsumer, BZ_DELAY_ML_CONSUMER_04);
        this.createMiddleLevelDelayConsumer(delayUdpMqConsumer, BZ_DELAY_ML_CONSUMER_05);
        this.createMiddleLevelDelayConsumer(delayUdpMqConsumer, BZ_DELAY_ML_CONSUMER_06);
        this.createMiddleLevelDelayConsumer(delayUdpMqConsumer, BZ_DELAY_ML_CONSUMER_07);
        this.createMiddleLevelDelayConsumer(delayUdpMqConsumer, BZ_DELAY_ML_CONSUMER_08);
        this.createLowLevelDelayConsumer(delayUdpMqConsumer, BZ_DELAY_LL_CONSUMER_01);
        this.createLowLevelDelayConsumer(delayUdpMqConsumer, BZ_DELAY_LL_CONSUMER_02);
        this.createLowLevelDelayConsumer(delayUdpMqConsumer, BZ_DELAY_LL_CONSUMER_03);
        this.createLowLevelDelayConsumer(delayUdpMqConsumer, BZ_DELAY_LL_CONSUMER_04);
        this.createLowLevelDelayConsumer(delayUdpMqConsumer, BZ_DELAY_LL_CONSUMER_05);
        this.createLowLevelDelayConsumer(delayUdpMqConsumer, BZ_DELAY_LL_CONSUMER_06);
        logger.info("事件延迟队列-消费者：创建成功......");
    }

    public void initDispatchProducer() throws IOException, TimeoutException {
        UdpMqProducer dispatchUdpMqProducer = this.initProducerConnection(BZ_DISPATCH_PRODUCER_CONN_01);
        this.createSmsDispatchProducer(dispatchUdpMqProducer, BZ_SMS_DISPATCH_PRODUCER_01);
        this.createTeleDispatchProducer(dispatchUdpMqProducer, BZ_TELE_DISPATCH_PRODUCER_01);
        this.createCouponDispatchProducer(dispatchUdpMqProducer, BZ_COUPON_DISPATCH_PRODUCER_01);
        this.createNoMarketProducer(dispatchUdpMqProducer, BZ_NOMARKET_DISPATCH_PRODUCER_01);
        this.createPushDispatchProducer(dispatchUdpMqProducer, BZ_PUSH_DISPATCH_PRODUCER_01);
        this.createIncreaseDispatchProducer(dispatchUdpMqProducer, BZ_INCREASEAMT_DISPATCH_PRODUCER_01);
        this.createLifeRightsDispatchProducer(dispatchUdpMqProducer, BZ_LIFERIGHTS_DISPATCH_PRODUCER_01);
        this.createXDayInterestFreeDispatchProducer(dispatchUdpMqProducer, BZ_XDAYINTERESTFREE_DISPATCH_PRODUCER_01);
        this.createAiDispatchProducer(dispatchUdpMqProducer, BZ_AI_DISPATCH_PRODUCER_01);
        logger.info("事件触达-触达队列生产者，创建成功......");
    }

    public void initDispatchConsumer() throws IOException, TimeoutException {
        UdpMqConsumer dispatchUdpMqConsumer = this.initConnection(BZ_DISPATCH_CONSUMER_CONN_01);
        this.createSmsDispatchConsumer(dispatchUdpMqConsumer, BZ_SMS_DISPATCH_CONSUMER_01);
        this.createPushDispatchConsumer(dispatchUdpMqConsumer, BZ_PUSH_DISPATCH_CONSUMER_01);
        this.createTeleDispatchConsumer(dispatchUdpMqConsumer, BZ_TELE_DISPATCH_CONSUMER_01);
        this.createCouponDispatchConsumer(dispatchUdpMqConsumer, BZ_COUPON_DISPATCH_CONSUMER_01);
        this.createNoMarketConsumer(dispatchUdpMqConsumer, BZ_NOMARKET_DISPATCH_CONSUMER_01);
        this.createIncreaseAmtDispatchConsumer(dispatchUdpMqConsumer, BZ_INCREASEAMT_DISPATCH_CONSUMER_01);
        this.createLifeRightsDispatchConsumer(dispatchUdpMqConsumer, BZ_LIFERIGHTS_DISPATCH_CONSUMER_01);
        this.createXDayInterestFreeDispatchConsumer(dispatchUdpMqConsumer, BZ_XDAYINTERESTFREE_DISPATCH_CONSUMER_01);
        this.createAiDispatchConsumer(dispatchUdpMqConsumer, BZ_AI_DISPATCH_CONSUMER_01);
        logger.info("事件触达-触达队列消费者，创建成功......");
    }

    public void initDecisionProducer() throws IOException, TimeoutException {
        UdpMqProducer decisionUdpMqProducer = this.initProducerConnection(BZ_DECISION_PRODUCER_CONN_01);
        this.createDecisionProducer(decisionUdpMqProducer, BZ_DECISION_PRODUCER_01);
        logger.info("事件决策-决策结果队列-生产者：创建成功......");
    }

    public void initDecisionConsumer() throws IOException, TimeoutException {
        UdpMqConsumer decisionUdpMqConsumer = this.initConnection(BZ_DECISION_CONSUMER_CONN_01);
        this.createDecisionResultConsumer(decisionUdpMqConsumer, BZ_DECISION_CONSUMER_01);
        logger.info("事件决策-决策结果队列-消费者：创建成功......");
    }

    public UdpMqProducer initProducerConnection(String connectionId) throws IOException, TimeoutException {
        logger.info("queueConfig={}", this);
        ConnectionBuilder builder = new ConnectionBuilder().host(bizEventMqConfig.getHost3())
                .port(bizEventMqConfig.getPort3())
                .username(bizEventMqConfig.getUsername3())
                .password(bizEventMqConfig.getPassword3())
                .vHostName(bizEventMqConfig.getVirtualHost3());
        return UdpMqProducerManager.getInstance().createUdpMqProducer(builder, connectionId);
    }

    public UdpMqConsumer initConnection(String connectionId) throws IOException, TimeoutException {
        logger.info("queueConfig={}", this);
        ConnectionBuilder builder = new ConnectionBuilder().host(bizEventMqConfig.getHost3())
                .port(bizEventMqConfig.getPort3())
                .username(bizEventMqConfig.getUsername3())
                .password(bizEventMqConfig.getPassword3())
                .vHostName(bizEventMqConfig.getVirtualHost3());
        return UdpMqConsumerManager.getInstance().createUdpMqConsumer(builder, connectionId);
    }

    public void createMqHighLevelConsumer(UdpMqConsumer udpMqConsumer, String channelId) throws IOException {
        logger.info("queueConfig={}", this);
        BindParamBuilder builder = new BindParamBuilder()
                .exchangeName(bizEventMqConfig.getBizEventHighLevelExchange())
                .exchangeType(bizEventMqConfig.getBizEventHighLevelExchangeType())
                .routingKey(bizEventMqConfig.getBizEventHighLevelRoutingKey())
                .queueName(bizEventMqConfig.getBizEventHighLevelQueueName());
        Channel channel = udpMqConsumer.bindMqQueue(builder, channelId);
        String consumerTag="comsumer-"+ UUID.randomUUID();
        udpMqConsumer.basicConsume(bizEventMqConfig.getPrefetchSize(), new TraceSingleConsumer(channel) {
            @Override
            public void singleHandleDelivery(Message message) {
                try {
                    logger.info("事件节点消息消费-高优先级, messageId：{}", message.getMessageProperties().getMessageId());
                    String body = new String(message.getBody(), "UTF-8");
                    BizEventMessageVO bizEventMessageVO = JSONObject.parseObject(body, BizEventMessageVO.class);
                    Tracer.logEvent("T0Events-Initial", bizEventMessageVO.getBizEventType());

                    boolean isNotConsumed = verifyMessageId(RedisKeyConstants.BV_HL_MESSAGE_ID, message.getMessageProperties().getMessageId());
                    if (isNotConsumed) {
                        // 业务处理批量消息
                        logger.info("事件节点消息消费, messageId:{}, 消息内容:{}", message.getMessageProperties().getMessageId(), body);
                        String messageId = message.getMessageProperties().getMessageId();
                        // 消费，调用策略预筛方法
                        mqConsumeService.bizEventProcess(messageId, bizEventMessageVO);
                    }

                    // 消息确认
                    getChannel().basicAck(message.getMessageProperties().getDeliveryTag(), false);
                } catch (Exception e) {
                    logger.info("事件节点消息消费异常", e);
                    Tracer.logEvent("T0Events-Initial-Fail", "T0Events-Initial-Fail");
                }
            }
        },consumerTag);
    }

    public void createMqMiddleLevelConsumer(UdpMqConsumer udpMqConsumer, String channelId) throws IOException {
        logger.info("queueConfig={}", this);
        BindParamBuilder builder = new BindParamBuilder()
                .exchangeName(bizEventMqConfig.getBizEventMiddleLevelExchange())
                .exchangeType(bizEventMqConfig.getBizEventMiddleLevelExchangeType())
                .routingKey(bizEventMqConfig.getBizEventMiddleLevelRoutingKey())
                .queueName(bizEventMqConfig.getBizEventMiddleLevelQueueName());
        Channel channel = udpMqConsumer.bindMqQueue(builder, channelId);
        String consumerTag="comsumer-"+ UUID.randomUUID();
        udpMqConsumer.basicConsume(bizEventMqConfig.getPrefetchSize(), new TraceSingleConsumer(channel) {
            @Override
            public void singleHandleDelivery(Message message) {
                try {
                    logger.info("事件节点消息消费-中优先级, messageId:{}", message.getMessageProperties().getMessageId());
                    boolean isNotConsumed = verifyMessageId(RedisKeyConstants.BV_ML_MESSAGE_ID, message.getMessageProperties().getMessageId());
                    if (isNotConsumed) {
                        String body = new String(message.getBody(), "UTF-8");
                        // 业务处理批量消息
                        logger.info("事件节点消息消费, messageId:{}, 消息内容:{}", message.getMessageProperties().getMessageId(), body);
                        BizEventMessageVO bizEventMessageVO = JSONObject.parseObject(body, BizEventMessageVO.class);
                        String messageId = message.getMessageProperties().getMessageId();
                        // 消费，调用策略预筛方法
                        mqConsumeService.bizEventProcess(messageId, bizEventMessageVO);
                    }
                    // 消息确认
                    getChannel().basicAck(message.getMessageProperties().getDeliveryTag(), false);
                } catch (Exception e) {
                    logger.info("事件节点消息消费异常", e);
                }
            }
        },consumerTag);
    }

    public void createHighLevelDelayProducer(UdpMqProducer udpMqProducer, String channelId) throws IOException {
        logger.info("queueConfig={}", this);
        BindParamBuilder builder = new BindParamBuilder()
                .exchangeName(bizEventMqConfig.getBizEventDelayExchange())
                .exchangeType(bizEventMqConfig.getBizEventDelayXDelayedType())
                .bindingKey(bizEventMqConfig.getBizEventHighLevelDelayRoutingKey())
                .queueName(bizEventMqConfig.getBizEventHighLevelDelayQueueName());
        this.delayHlProducerChannel = udpMqProducer.bindDelayMqQueue(builder, channelId);
    }

    public void createMiddleLevelDelayProducer(UdpMqProducer udpMqProducer, String channelId) throws IOException {
        logger.info("queueConfig={}", this);
        BindParamBuilder builder = new BindParamBuilder()
                .exchangeName(bizEventMqConfig.getBizEventDelayExchange())
                .exchangeType(bizEventMqConfig.getBizEventDelayXDelayedType())
                .bindingKey(bizEventMqConfig.getBizEventMiddleLevelDelayRoutingKey())
                .queueName(bizEventMqConfig.getBizEventMiddleLevelDelayQueueName());
        this.delayMlProducerChannel = udpMqProducer.bindDelayMqQueue(builder, channelId);
    }

    public void createLowLevelDelayProducer(UdpMqProducer udpMqProducer, String channelId) throws IOException {
        logger.info("queueConfig={}", this);
        BindParamBuilder builder = new BindParamBuilder()
                .exchangeName(bizEventMqConfig.getBizEventDelayExchange())
                .exchangeType(bizEventMqConfig.getBizEventDelayXDelayedType())
                .bindingKey(bizEventMqConfig.getBizEventLowLevelDelayRoutingKey())
                .queueName(bizEventMqConfig.getBizEventLowLevelDelayQueueName());
        this.delayLlProducerChannel = udpMqProducer.bindDelayMqQueue(builder, channelId);
    }

    public void createHighLevelDelayConsumer(UdpMqConsumer udpMqConsumer, String channelId) throws IOException {
        logger.info("queueConfig={}", this);
        BindParamBuilder builder = new BindParamBuilder()
                .exchangeName(bizEventMqConfig.getBizEventDelayExchange())
                .exchangeType(bizEventMqConfig.getBizEventDelayExchangeType())
                .routingKey(bizEventMqConfig.getBizEventHighLevelDelayRoutingKey())
                .queueName(bizEventMqConfig.getBizEventHighLevelDelayQueueName());

        Map<String, Object> exchangeArguments =
                builder.getExchangeArguments()!=null? builder.getExchangeArguments(): new HashMap<>();
        exchangeArguments.put("x-delayed-type","direct");
        builder.exchangeArguments(exchangeArguments);

        Channel channel = udpMqConsumer.bindMqQueue(builder, channelId);
        udpMqConsumer.basicConsume(bizEventMqConfig.getPrefetchSize(), new TraceSingleConsumer(channel) {
            @Override
            public void singleHandleDelivery(Message message) {
                try {
                    logger.info("事件节点延迟消息消费-高优先级, messageId:{}", message.getMessageProperties().getMessageId());
                    boolean isNotConsumed = verifyMessageId(RedisKeyConstants.BV_HL_DL_MESSAGE_ID, message.getMessageProperties().getMessageId());
                    if (isNotConsumed) {
                        String body = new String(message.getBody(), "UTF-8");
                        // 业务处理批量消息
                        logger.info("事件节点延迟消息消费, messageId:{}，消息内容:{}", message.getMessageProperties().getMessageId(), body);
                        BizEventVO bizEventVO = JSONObject.parseObject(body, BizEventVO.class);
                        // 消费，调用策略复筛方法
                        mqConsumeService.bizEventDelayProcess(bizEventVO);
                    }
                    // 消息确认
                    getChannel().basicAck(message.getMessageProperties().getDeliveryTag(), false);
                } catch (Exception e) {
                    logger.info("事件节点延迟消息消费异常", e);
                }
            }
        });
    }

    public void createMiddleLevelDelayConsumer(UdpMqConsumer udpMqConsumer, String channelId) throws IOException {
        logger.info("queueConfig={}", this);
        BindParamBuilder builder = new BindParamBuilder()
                .exchangeName(bizEventMqConfig.getBizEventDelayExchange())
                .exchangeType(bizEventMqConfig.getBizEventDelayExchangeType())
                .routingKey(bizEventMqConfig.getBizEventMiddleLevelRoutingKey())
                .queueName(bizEventMqConfig.getBizEventMiddleLevelDelayQueueName());

        Map<String, Object> exchangeArguments =
                builder.getExchangeArguments()!=null? builder.getExchangeArguments(): new HashMap<>();
        exchangeArguments.put("x-delayed-type","direct");
        builder.exchangeArguments(exchangeArguments);

        Channel channel = udpMqConsumer.bindMqQueue(builder, channelId);
        udpMqConsumer.basicConsume(bizEventMqConfig.getPrefetchSize(), new TraceSingleConsumer(channel) {
            @Override
            public void singleHandleDelivery(Message message) {
                try {
                    logger.info("事件节点延迟消息消费-中优先级, messageId:{}", message.getMessageProperties().getMessageId());
                    boolean isNotConsumed = verifyMessageId(RedisKeyConstants.BV_ML_DL_MESSAGE_ID, message.getMessageProperties().getMessageId());
                    if (isNotConsumed) {
                        String body = new String(message.getBody(), "UTF-8");
                        // 业务处理批量消息
                        logger.info("事件节点延迟消息消费, messageId:{}，消息内容:{}", message.getMessageProperties().getMessageId(), body);
                        BizEventVO bizEventVO = JSONObject.parseObject(body, BizEventVO.class);
                        bizEventVO.setMessageId(message.getMessageProperties().getMessageId());
                        // 消费，调用策略复筛方法
                        mqConsumeService.bizEventDelayProcess(bizEventVO);
                    }
                    // 消息确认
                    getChannel().basicAck(message.getMessageProperties().getDeliveryTag(), false);
                } catch (Exception e) {
                    logger.info("事件节点延迟消息消费异常", e);
                }
            }
        });
    }

    public void createLowLevelDelayConsumer(UdpMqConsumer udpMqConsumer, String channelId) throws IOException {
        logger.info("queueConfig={}", this);
        BindParamBuilder builder = new BindParamBuilder()
                .exchangeName(bizEventMqConfig.getBizEventDelayExchange())
                .exchangeType(bizEventMqConfig.getBizEventDelayExchangeType())
                .routingKey(bizEventMqConfig.getBizEventLowLevelDelayRoutingKey())
                .queueName(bizEventMqConfig.getBizEventLowLevelDelayQueueName());

        Map<String, Object> exchangeArguments =
                builder.getExchangeArguments()!=null? builder.getExchangeArguments(): new HashMap<>();
        exchangeArguments.put("x-delayed-type","direct");
        builder.exchangeArguments(exchangeArguments);

        Channel channel = udpMqConsumer.bindMqQueue(builder, channelId);
        udpMqConsumer.basicConsume(bizEventMqConfig.getPrefetchSize(), new TraceSingleConsumer(channel) {
            @Override
            public void singleHandleDelivery(Message message) {
                try {
                    logger.info("事件节点延迟消息消费-低优先级, messageId:{}", message.getMessageProperties().getMessageId());
                    boolean isNotConsumed = verifyMessageId(RedisKeyConstants.BV_LL_DL_MESSAGE_ID, message.getMessageProperties().getMessageId());
                    if (isNotConsumed) {
                        String body = new String(message.getBody(), "UTF-8");
                        // 业务处理批量消息
                        logger.info("事件节点延迟消息消费, messageId:{}，消息内容：{}", message.getMessageProperties().getMessageId(), body);
                        BizEventVO bizEventVO = JSONObject.parseObject(body, BizEventVO.class);
                        // 消费，调用策略复筛方法
                        mqConsumeService.bizEventDelayProcess(bizEventVO);
                    }
                    // 消息确认
                    getChannel().basicAck(message.getMessageProperties().getDeliveryTag(), false);
                } catch (Exception e) {
                    logger.info("事件节点延迟消息消费异常", e);
                }
            }
        });
    }

    public void createSmsDispatchProducer(UdpMqProducer udpMqProducer, String channelId) throws IOException {
        logger.info("queueConfig={}", this);
        BindParamBuilder builder = new BindParamBuilder()
                .exchangeName(bizEventMqConfig.getBizEventDispatchExchange())
                .exchangeType(bizEventMqConfig.getBizEventDispatchExchangeType())
                .bindingKey(bizEventMqConfig.getBizEventSmsDispatchRoutingKey())
                .queueName(bizEventMqConfig.getBizEventSmsDispatchQueueName());
        this.smsDispatchProducerChannel = udpMqProducer.bindMqQueue(builder, channelId);
    }

    public void createPushDispatchProducer(UdpMqProducer udpMqProducer, String channelId) throws IOException {
        logger.info("queueConfig={}", this);
        BindParamBuilder builder = new BindParamBuilder()
                .exchangeName(bizEventMqConfig.getBizEventDispatchExchange())
                .exchangeType(bizEventMqConfig.getBizEventDispatchExchangeType())
                .bindingKey(bizEventMqConfig.getBizEventPushDispatchRoutingKey())
                .queueName(bizEventMqConfig.getBizEventPushDispatchQueueName());
        this.pushDispatchProducerChannel = udpMqProducer.bindMqQueue(builder, channelId);
    }

    public void createIncreaseDispatchProducer(UdpMqProducer udpMqProducer, String channelId) throws IOException {
        logger.info("queueConfig={}", this);
        BindParamBuilder builder = new BindParamBuilder()
                .exchangeName(bizEventMqConfig.getBizEventDispatchExchange())
                .exchangeType(bizEventMqConfig.getBizEventDispatchExchangeType())
                .bindingKey(bizEventMqConfig.getBizEventIncreaseAmtDispatchRoutingKey())
                .queueName(bizEventMqConfig.getBizEventIncreaseAmtDispatchQueueName());
        this.increaseAmtDispatchProducerChannel = udpMqProducer.bindMqQueue(builder, channelId);
    }

    public void createLifeRightsDispatchProducer(UdpMqProducer udpMqProducer, String channelId) throws IOException {
        logger.info("queueConfig={}", this);
        BindParamBuilder builder = new BindParamBuilder()
                .exchangeName(bizEventMqConfig.getBizEventDispatchExchange())
                .exchangeType(bizEventMqConfig.getBizEventDispatchExchangeType())
                .bindingKey(bizEventMqConfig.getBizEventLifeRightsDispatchRoutingKey())
                .queueName(bizEventMqConfig.getBizEventLifeRightsDispatchQueueName());
        this.lifeRightsDispatchProducerChannel = udpMqProducer.bindMqQueue(builder, channelId);
    }

    public void createXDayInterestFreeDispatchProducer(UdpMqProducer udpMqProducer, String channelId) throws IOException {
        logger.info("queueConfig={}", this);
        BindParamBuilder builder = new BindParamBuilder()
                .exchangeName(bizEventMqConfig.getBizEventDispatchExchange())
                .exchangeType(bizEventMqConfig.getBizEventDispatchExchangeType())
                .bindingKey(bizEventMqConfig.getBizEventXDayInterestFreeDispatchRoutingKey())
                .queueName(bizEventMqConfig.getBizEventXDayInterestFreeDispatchQueueName());
        this.xDayInterestFreeDispatchProducerChannel = udpMqProducer.bindMqQueue(builder, channelId);
    }

    public void createAiDispatchProducer(UdpMqProducer udpMqProducer, String channelId) throws IOException {
        logger.info("queueConfig={}", this);
        BindParamBuilder builder = new BindParamBuilder()
                .exchangeName(bizEventMqConfig.getBizEventDispatchExchange())
                .exchangeType(bizEventMqConfig.getBizEventDispatchExchangeType())
                .bindingKey(bizEventMqConfig.getBizEventAiDispatchRoutingKey())
                .queueName(bizEventMqConfig.getBizEventAiDispatchQueueName());
        this.aiDispatchProducerChannel = udpMqProducer.bindMqQueue(builder, channelId);
    }

    public void createTeleDispatchProducer(UdpMqProducer udpMqProducer, String channelId) throws IOException {
        logger.info("queueConfig={}", this);
        BindParamBuilder builder = new BindParamBuilder()
                .exchangeName(bizEventMqConfig.getBizEventDispatchExchange())
                .exchangeType(bizEventMqConfig.getBizEventDispatchExchangeType())
                .bindingKey(bizEventMqConfig.getBizEventTeleDispatchRoutingKey())
                .queueName(bizEventMqConfig.getBizEventTeleDispatchQueueName());
        this.teleDispatchProducerChannel = udpMqProducer.bindMqQueue(builder, channelId);
    }

    public void createCouponDispatchProducer(UdpMqProducer udpMqProducer, String channelId) throws IOException {
        logger.info("queueConfig={}", this);
        BindParamBuilder builder = new BindParamBuilder()
                .exchangeName(bizEventMqConfig.getBizEventDispatchExchange())
                .exchangeType(bizEventMqConfig.getBizEventDispatchExchangeType())
                .bindingKey(bizEventMqConfig.getBizEventCouponDispatchRoutingKey())
                .queueName(bizEventMqConfig.getBizEventCouponDispatchQueueName());
        this.couponDispatchProducerChannel = udpMqProducer.bindMqQueue(builder, channelId);
    }

    public void createNoMarketProducer(UdpMqProducer udpMqProducer, String channelId) throws IOException {
        logger.info("queueConfig={}", this);
        BindParamBuilder builder = new BindParamBuilder()
                .exchangeName(bizEventMqConfig.getBizEventDispatchExchange())
                .exchangeType(bizEventMqConfig.getBizEventDispatchExchangeType())
                .bindingKey(bizEventMqConfig.getBizEventNoMarketRoutingKey())
                .queueName(bizEventMqConfig.getBizEventNoMarketQueueName());
        this.noMarketProducerChannel = udpMqProducer.bindMqQueue(builder, channelId);
    }

    public void createDecisionProducer(UdpMqProducer udpMqProducer, String channelId) throws IOException {
        logger.info("queueConfig={}", this);
        BindParamBuilder builder = new BindParamBuilder()
                .exchangeName(bizEventMqConfig.getBizEventDecisionExchange())
                .exchangeType(bizEventMqConfig.getBizEventDecisionExchangeType())
                .bindingKey(bizEventMqConfig.getBizEventDecisionRoutingKey())
                .queueName(bizEventMqConfig.getBizEventDecisionQueueName());
        this.decisionResultChannel = udpMqProducer.bindMqQueue(builder, channelId);
    }

    public void createSmsDispatchConsumer(UdpMqConsumer udpMqConsumer, String channelId) throws IOException {
        logger.info("queueConfig={}", this);
        BindParamBuilder builder = new BindParamBuilder()
                .exchangeName(bizEventMqConfig.getBizEventDispatchExchange())
                .exchangeType(bizEventMqConfig.getBizEventDispatchExchangeType())
                .routingKey(bizEventMqConfig.getBizEventSmsDispatchRoutingKey())
                .queueName(bizEventMqConfig.getBizEventSmsDispatchQueueName());
        Channel channel = udpMqConsumer.bindMqQueue(builder, channelId);
        String consumerTag="comsumer-"+ UUID.randomUUID();
        udpMqConsumer.basicConsume(bizEventMqConfig.getPrefetchSize(), new TraceSingleConsumer(channel) {
            @Override
            public void singleHandleDelivery(Message message) {
                try {
                    logger.info("事件节点短信触达消息消费, messageId:{}", message.getMessageProperties().getMessageId());
                    boolean isNotConsumed = verifyMessageId(RedisKeyConstants.BV_SMS_MESSAGE_ID, message.getMessageProperties().getMessageId());
                    if (isNotConsumed) {
                        // 业务处理批量消息
                        logger.info("事件节点短信触达消息消费, 事件消息:{}", message);
                        // 消费，调用策略触达方法
                        mqConsumeService.bizEventDispatchProcess(message);
                    }
                    // 消息确认
                    getChannel().basicAck(message.getMessageProperties().getDeliveryTag(), false);
                } catch (Exception e) {
                    logger.info("事件节点短信触达消息消费异常", e);
                }
            }
        },consumerTag);
    }

    public void createPushDispatchConsumer(UdpMqConsumer udpMqConsumer, String channelId) throws IOException {
        logger.info("queueConfig={}", this);
        BindParamBuilder builder = new BindParamBuilder()
                .exchangeName(bizEventMqConfig.getBizEventDispatchExchange())
                .exchangeType(bizEventMqConfig.getBizEventDispatchExchangeType())
                .routingKey(bizEventMqConfig.getBizEventPushDispatchRoutingKey())
                .queueName(bizEventMqConfig.getBizEventPushDispatchQueueName());
        Channel channel = udpMqConsumer.bindMqQueue(builder, channelId);
        String consumerTag="comsumer-"+ UUID.randomUUID();
        udpMqConsumer.basicConsume(bizEventMqConfig.getPrefetchSize(), new TraceSingleConsumer(channel) {
            @Override
            public void singleHandleDelivery(Message message) {
                try {
                    logger.info("事件节点push触达消息消费, messageId:{}", message.getMessageProperties().getMessageId());
                    boolean isNotConsumed = verifyMessageId(RedisKeyConstants.BV_PUSH_MESSAGE_ID, message.getMessageProperties().getMessageId());
                    if (isNotConsumed) {
                        // 业务处理批量消息
                        logger.info("事件节点push触达消息消费, 事件消息:{}", message);
                        // 消费，调用策略触达方法
                        mqConsumeService.bizEventDispatchProcess(message);
                    }
                    // 消息确认
                    getChannel().basicAck(message.getMessageProperties().getDeliveryTag(), false);
                } catch (Exception e) {
                    logger.info("事件节点push触达消息消费异常", e);
                }
            }
        },consumerTag);
    }

    public void createIncreaseAmtDispatchConsumer(UdpMqConsumer udpMqConsumer, String channelId) throws IOException {
        logger.info("queueConfig={}", this);
        BindParamBuilder builder = new BindParamBuilder()
                .exchangeName(bizEventMqConfig.getBizEventDispatchExchange())
                .exchangeType(bizEventMqConfig.getBizEventDispatchExchangeType())
                .routingKey(bizEventMqConfig.getBizEventIncreaseAmtDispatchRoutingKey())
                .queueName(bizEventMqConfig.getBizEventIncreaseAmtDispatchQueueName());
        Channel channel = udpMqConsumer.bindMqQueue(builder, channelId);
        udpMqConsumer.basicConsume(bizEventMqConfig.getPrefetchSize(), new TraceSingleConsumer(channel) {
            @Override
            public void singleHandleDelivery(Message message) {
                try {
                    logger.info("事件节点提额通知消息消费, messageId:{}", message.getMessageProperties().getMessageId());
                    boolean isNotConsumed = verifyMessageId(RedisKeyConstants.BV_PUSH_MESSAGE_ID, message.getMessageProperties().getMessageId());
                    if (isNotConsumed) {
                        // 业务处理批量消息
                        logger.info("事件节点提额通知消息消费, 事件消息:{}", message);
                        // 消费，调用策略触达方法
                        mqConsumeService.bizEventDispatchProcess(message);
                    }
                    // 消息确认
                    getChannel().basicAck(message.getMessageProperties().getDeliveryTag(), false);
                } catch (Exception e) {
                    logger.info("事件节点提额通知消息消费", e);
                }
            }
        });
    }

    public void createLifeRightsDispatchConsumer(UdpMqConsumer udpMqConsumer, String channelId) throws IOException {
        logger.info("queueConfig={}", this);
        BindParamBuilder builder = new BindParamBuilder()
                .exchangeName(bizEventMqConfig.getBizEventDispatchExchange())
                .exchangeType(bizEventMqConfig.getBizEventDispatchExchangeType())
                .routingKey(bizEventMqConfig.getBizEventLifeRightsDispatchRoutingKey())
                .queueName(bizEventMqConfig.getBizEventLifeRightsDispatchQueueName());
        Channel channel = udpMqConsumer.bindMqQueue(builder, channelId);
        udpMqConsumer.basicConsume(bizEventMqConfig.getPrefetchSize(), new TraceSingleConsumer(channel) {
            @Override
            public void singleHandleDelivery(Message message) {
                try {
                    logger.info("事件节点生活权益通知消息消费, messageId：{}", message.getMessageProperties().getMessageId());
                    boolean isNotConsumed = verifyMessageId(RedisKeyConstants.BV_LIFERIGHTS_MESSAGE_ID, message.getMessageProperties().getMessageId());
                    if (isNotConsumed) {
                        // 业务处理批量消息
                        logger.info("事件节点生活权益通知消息消费, 事件消息：{}", message);
                        // 消费，调用策略触达方法
                        mqConsumeService.bizEventDispatchProcess(message);
                    }
                    // 消息确认
                    getChannel().basicAck(message.getMessageProperties().getDeliveryTag(), false);
                } catch (Exception e) {
                    logger.info("事件节点生活权益通知消息消费", e);
                }
            }
        });
    }

    public void createXDayInterestFreeDispatchConsumer(UdpMqConsumer udpMqConsumer, String channelId) throws IOException {
        logger.info("queueConfig={}", this);
        BindParamBuilder builder = new BindParamBuilder()
                .exchangeName(bizEventMqConfig.getBizEventDispatchExchange())
                .exchangeType(bizEventMqConfig.getBizEventDispatchExchangeType())
                .routingKey(bizEventMqConfig.getBizEventXDayInterestFreeDispatchRoutingKey())
                .queueName(bizEventMqConfig.getBizEventXDayInterestFreeDispatchQueueName());
        Channel channel = udpMqConsumer.bindMqQueue(builder, channelId);
        String consumerTag="comsumer-"+ UUID.randomUUID();
        udpMqConsumer.basicConsume(bizEventMqConfig.getPrefetchSize(), new TraceSingleConsumer(channel) {
            @Override
            public void singleHandleDelivery(Message message) {
                try {
                    logger.info("事件节点x天免息通知消息消费, messageId:{}", message.getMessageProperties().getMessageId());
                    boolean isNotConsumed = verifyMessageId(RedisKeyConstants.BV_XDAYINTERESTFREE_MESSAGE_ID, message.getMessageProperties().getMessageId());
                    if (isNotConsumed) {
                        // 业务处理批量消息
                        logger.info("事件节点x天免息通知消息消费, 事件消息:{}", message);
                        // 消费，调用策略触达方法
                        mqConsumeService.bizEventDispatchProcess(message);
                    }
                    // 消息确认
                    getChannel().basicAck(message.getMessageProperties().getDeliveryTag(), false);
                } catch (Exception e) {
                    logger.info("事件节点x天免息通知消息消费", e);
                }
            }
        },consumerTag);
    }

    public void createAiDispatchConsumer(UdpMqConsumer udpMqConsumer, String channelId) throws IOException {
        logger.info("queueConfig={}", this);
        BindParamBuilder builder = new BindParamBuilder()
                .exchangeName(bizEventMqConfig.getBizEventDispatchExchange())
                .exchangeType(bizEventMqConfig.getBizEventDispatchExchangeType())
                .routingKey(bizEventMqConfig.getBizEventAiDispatchRoutingKey())
                .queueName(bizEventMqConfig.getBizEventAiDispatchQueueName());
        Channel channel = udpMqConsumer.bindMqQueue(builder, channelId);
        String consumerTag="comsumer-"+ UUID.randomUUID();
        udpMqConsumer.basicConsume(bizEventMqConfig.getPrefetchSize(), new TraceSingleConsumer(channel) {
            @Override
            public void singleHandleDelivery(Message message) {
                try {
                    logger.info("事件节点ai通知消息消费, messageId:{}", message.getMessageProperties().getMessageId());
                    boolean isNotConsumed = verifyMessageId(RedisKeyConstants.BV_AI_MESSAGE_ID, message.getMessageProperties().getMessageId());
                    if (isNotConsumed) {
                        // 业务处理批量消息
                        logger.info("事件节点ai通知消息消费, 事件消息:{}", message);
                        // 消费，调用策略触达方法
                        mqConsumeService.bizEventDispatchProcess(message);
                    }
                    // 消息确认
                    getChannel().basicAck(message.getMessageProperties().getDeliveryTag(), false);
                } catch (Exception e) {
                    logger.info("事件节点ai通知消息消费", e);
                }
            }
        },consumerTag);
    }

    public void createTeleDispatchConsumer(UdpMqConsumer udpMqConsumer, String channelId) throws IOException {
        logger.info("queueConfig={}", this);
        BindParamBuilder builder = new BindParamBuilder()
                .exchangeName(bizEventMqConfig.getBizEventDispatchExchange())
                .exchangeType(bizEventMqConfig.getBizEventDispatchExchangeType())
                .routingKey(bizEventMqConfig.getBizEventTeleDispatchRoutingKey())
                .queueName(bizEventMqConfig.getBizEventTeleDispatchQueueName());
        Channel channel = udpMqConsumer.bindMqQueue(builder, channelId);
        String consumerTag="comsumer-"+ UUID.randomUUID();
        udpMqConsumer.basicConsume(bizEventMqConfig.getPrefetchSize(), new TraceSingleConsumer(channel) {
            @Override
            public void singleHandleDelivery(Message message) {
                try {
                    logger.info("事件节点电销触达消息消费, messageId:{}", message.getMessageProperties().getMessageId());
                    boolean isNotConsumed = verifyMessageId(RedisKeyConstants.BV_TELE_MESSAGE_ID, message.getMessageProperties().getMessageId());
                    if (isNotConsumed) {
                        // 业务处理批量消息
                        logger.info("事件节点电销触达消息消费, 事件消息:{}", message);
                        // 消费，调用策略触达方法
                        mqConsumeService.bizEventDispatchProcess(message);
                    }
                    // 消息确认
                    getChannel().basicAck(message.getMessageProperties().getDeliveryTag(), false);
                } catch (Exception e) {
                    logger.info("事件节点电销触达消息消费", e);
                }
            }
        },consumerTag);
    }

    public void createCouponDispatchConsumer(UdpMqConsumer udpMqConsumer, String channelId) throws IOException {
        logger.info("queueConfig={}", this);
        BindParamBuilder builder = new BindParamBuilder()
                .exchangeName(bizEventMqConfig.getBizEventDispatchExchange())
                .exchangeType(bizEventMqConfig.getBizEventDispatchExchangeType())
                .routingKey(bizEventMqConfig.getBizEventCouponDispatchRoutingKey())
                .queueName(bizEventMqConfig.getBizEventCouponDispatchQueueName());
        Channel channel = udpMqConsumer.bindMqQueue(builder, channelId);
        String consumerTag="comsumer-"+ UUID.randomUUID();
        udpMqConsumer.basicConsume(bizEventMqConfig.getPrefetchSize(), new TraceSingleConsumer(channel) {
            @Override
            public void singleHandleDelivery(Message message) {
                try {
                    logger.info("事件节点优惠券触达消息消费, messageId:{}", message.getMessageProperties().getMessageId());
                    boolean isNotConsumed = verifyMessageId(RedisKeyConstants.BV_COUPON_MESSAGE_ID, message.getMessageProperties().getMessageId());
                    if (isNotConsumed) {
                        // 业务处理批量消息
                        logger.info("事件节点优惠券触达消息消费, 事件消息:{}", message);
                        // 消费，调用策略触达方法
                        mqConsumeService.bizEventDispatchProcess(message);
                    }
                    // 消息确认
                    getChannel().basicAck(message.getMessageProperties().getDeliveryTag(), false);
                } catch (Exception e) {
                    logger.info("事件节点优惠券触达消息消费异常", e);
                }
            }
        },consumerTag);
    }

    public void createNoMarketConsumer(UdpMqConsumer udpMqConsumer, String channelId) throws IOException {
        logger.info("queueConfig={}", this);
        BindParamBuilder builder = new BindParamBuilder()
                .exchangeName(bizEventMqConfig.getBizEventDispatchExchange())
                .exchangeType(bizEventMqConfig.getBizEventDispatchExchangeType())
                .routingKey(bizEventMqConfig.getBizEventNoMarketRoutingKey())
                .queueName(bizEventMqConfig.getBizEventNoMarketQueueName());
        Channel channel = udpMqConsumer.bindMqQueue(builder, channelId);
        String consumerTag="comsumer-"+ UUID.randomUUID();
        udpMqConsumer.basicConsume(bizEventMqConfig.getPrefetchSize(), new TraceSingleConsumer(channel) {
            @Override
            public void singleHandleDelivery(Message message) {
                try {
                    logger.info("事件节点不营销组触达消息消费, messageId:{}", message.getMessageProperties().getMessageId());
                    boolean isNotConsumed = verifyMessageId(RedisKeyConstants.BV_NOMARKET_MESSAGE_ID, message.getMessageProperties().getMessageId());
                    if (isNotConsumed) {
                        // 业务处理批量消息
                        logger.info("事件节点不营销组触达消息消费, 事件消息:{}", message);
                        // 消费，调用策略触达方法
                        mqConsumeService.bizEventDispatchProcess(message);
                    }
                    // 消息确认
                    getChannel().basicAck(message.getMessageProperties().getDeliveryTag(), false);
                } catch (Exception e) {
                    logger.info("事件节点不营销组触达消息消费异常", e);
                }
            }
        },consumerTag);
    }

    public void createDecisionResultConsumer(UdpMqConsumer udpMqConsumer, String channelId) throws IOException {
        logger.info("queueConfig={}", this);
        BindParamBuilder builder = new BindParamBuilder()
                .exchangeName(bizEventMqConfig.getBizEventDecisionExchange())
                .exchangeType(bizEventMqConfig.getBizEventDecisionExchangeType())
                .routingKey(bizEventMqConfig.getBizEventDecisionRoutingKey())
                .queueName(bizEventMqConfig.getBizEventDecisionQueueName());
        Channel channel = udpMqConsumer.bindMqQueue(builder, channelId);
        udpMqConsumer.basicConsume(new TraceBatchConsumer(channel, bizEventMqConfig.getBatchSize(), bizEventMqConfig.getTimeout()) {
            @Override
            public void batchHandleDelivery(List<Message> messageList) {
                try {
                    // 业务处理批量消息
                    logger.info("事件节点决策结果消息消费, size={}", messageList.size());
                    List<BizEventVO> bizEventVOList = messageList.stream().map(message -> {
                        boolean isNotConsumed = verifyMessageId(RedisKeyConstants.BV_DECISION_MESSAGE_ID, message.getMessageProperties().getMessageId());
                        if (!isNotConsumed) {
                            logger.warn("事件节点决策结果消息已消费, 消息id:{}", message.getMessageProperties().getMessageId());
                            return null;
                        }
                        BizEventVO bizEventVO = null;
                        String body = null;
                        try {
                            body = new String(message.getBody(), "UTF-8");
                            bizEventVO = JSONObject.parseObject(body, BizEventVO.class);
                        } catch (Exception e) {
                            logger.warn("事件节点决策结果消息消费异常，消息内容：{}", body, e);
                        }
                        return bizEventVO;
                    }).collect(Collectors.toList());
                    // 消费，调用策略触达方法
                    mqConsumeService.bizEventDecisionResultProcess(bizEventVOList);
                    // 消息确认
                    long deliveryTag = messageList.get(messageList.size() - 1).getMessageProperties().getDeliveryTag();
                    logger.info("deliveryTag={}, batchSize={}, total={}", deliveryTag, messageList.size());
                    getChannel().basicAck(deliveryTag, true);
                } catch (Exception e) {
                    logger.info("事件节点决策结果消息消费异常", e);
                }
            }
        });
    }

    public void sendHighLevelDelayMessage(BizEventVO bizEventVO, long seconds) throws IOException {
        UdpMqProducer udpMqProducer = UdpMqProducerManager.getInstance().getUdpMqProducer(BZ_DELAY_PRODUCER_CONN_01);
        udpMqProducer.sendDelayMessage(this.delayHlProducerChannel, bizEventVO.getMessageId(), JSONObject.toJSONString(bizEventVO), String.valueOf(seconds * 1000));
        logger.info("发送延迟消息高优先级, 消息id:{}, 延迟时间:{}, 消息内容:{}", bizEventVO.getMessageId(), seconds, JSONObject.toJSONString(bizEventVO));
        Tracer.logEvent("mq_delayMessage", "highLevel");
        Tracer.logEvent("mq_delayMessageHighLevel", bizEventVO.getBizEventType() == null ? "NULL" : bizEventVO.getBizEventType());
    }

    public void sendMiddleLevelDelayMessage(BizEventVO bizEventVO, long seconds) throws IOException {
        UdpMqProducer udpMqProducer = UdpMqProducerManager.getInstance().getUdpMqProducer(BZ_DELAY_PRODUCER_CONN_01);
        udpMqProducer.sendDelayMessage(this.delayMlProducerChannel, bizEventVO.getMessageId(), JSONObject.toJSONString(bizEventVO), String.valueOf(seconds * 1000));
        logger.info("发送延迟消息中优先级, 消息id:{}, 延迟时间:{}, 消息内容:{}", bizEventVO.getMessageId(), seconds, JSONObject.toJSONString(bizEventVO));
        Tracer.logEvent("mq_delayMessage", "middleLevel");
        Tracer.logEvent("mq_delayMessageMiddleLevel", bizEventVO.getBizEventType() == null ? "NULL" : bizEventVO.getBizEventType());
    }

    public void sendMiddleLevelDelayMessage(BizEventVO bizEventVO, String messageId, long seconds) throws IOException {
        Map<String, Object> eventMap = BeanUtil.beanToMap(bizEventVO);
        eventMap.computeIfPresent("retryNum", (k, v) -> Convert.toInt(v, 0) + 1);
        UdpMqProducer udpMqProducer = UdpMqProducerManager.getInstance().getUdpMqProducer(BZ_DELAY_PRODUCER_CONN_01);
        udpMqProducer.sendDelayMessage(this.delayMlProducerChannel, messageId, JSONObject.toJSONString(eventMap), String.valueOf(seconds * 1000));
        logger.info("发送延迟消息中优先级retry, 消息id:{}, 延迟时间:{}, 消息内容:{}", messageId, seconds, JSONObject.toJSONString(eventMap));
        Tracer.logEvent("mq_delayMessage", "middleLevel_retry");
        Tracer.logEvent("mq_delayMessageMiddleLevel", bizEventVO.getBizEventType() == null ? "NULL" : bizEventVO.getBizEventType());
    }

    public void sendLowLevelDelayMessage(BizEventVO bizEventVO, long seconds) throws IOException {
        UdpMqProducer udpMqProducer = UdpMqProducerManager.getInstance().getUdpMqProducer(BZ_DELAY_PRODUCER_CONN_01);
        udpMqProducer.sendDelayMessage(this.delayLlProducerChannel, bizEventVO.getMessageId(), JSONObject.toJSONString(bizEventVO), String.valueOf(seconds * 1000));
        logger.info("发送延迟消息低优先级, 消息id:{}, 延迟时间:{}, 消息内容:{}", bizEventVO.getMessageId(), seconds, JSONObject.toJSONString(bizEventVO));
        Tracer.logEvent("mq_delayMessage", "lowLevel");
        Tracer.logEvent("mq_delayMessagelowLevel", bizEventVO.getBizEventType() == null ? "NULL" : bizEventVO.getBizEventType());
    }

    public void sendSmsDispatchMessage(BizEventVO bizEventVO) throws IOException {
        UdpMqProducer udpMqProducer = UdpMqProducerManager.getInstance().getUdpMqProducer(BZ_DISPATCH_PRODUCER_CONN_01);
        udpMqProducer.sendMessage(this.smsDispatchProducerChannel, bizEventVO.getMessageId(), JSONObject.toJSONString(bizEventVO));
        logger.info("发送短信触达消息成功, 消息id:{}, 消息内容:{}", bizEventVO.getMessageId(), JSONObject.toJSONString(bizEventVO));
        Tracer.logEvent("mq_dispatch", "sms");
        Tracer.logEvent("mq_dispatchSms", bizEventVO.getBizEventType() == null ? "NULL" : bizEventVO.getBizEventType());
    }

    public void sendPushDispatchMessage(BizEventVO bizEventVO) throws IOException {
        UdpMqProducer udpMqProducer = UdpMqProducerManager.getInstance().getUdpMqProducer(BZ_DISPATCH_PRODUCER_CONN_01);
        udpMqProducer.sendMessage(this.pushDispatchProducerChannel, bizEventVO.getMessageId(), JSONObject.toJSONString(bizEventVO));
        logger.info("发送push触达消息成功, 消息id:{}, 消息内容:{}", bizEventVO.getMessageId(), JSONObject.toJSONString(bizEventVO));
        Tracer.logEvent("mq_dispatch", "push");
        Tracer.logEvent("mq_dispatchPush", bizEventVO.getBizEventType() == null ? "NULL" : bizEventVO.getBizEventType());
    }

    public void sendIncreaseAmtDispatchMessage(BizEventVO bizEventVO) throws IOException {
        UdpMqProducer udpMqProducer = UdpMqProducerManager.getInstance().getUdpMqProducer(BZ_DISPATCH_PRODUCER_CONN_01);
        udpMqProducer.sendMessage(this.increaseAmtDispatchProducerChannel, bizEventVO.getMessageId(), JSONObject.toJSONString(bizEventVO));
        logger.info("发送提额触达消息成功, 消息id:{}, 消息内容:{}", bizEventVO.getMessageId(), JSONObject.toJSONString(bizEventVO));
        Tracer.logEvent("mq_dispatch", "increaseAmt");
        Tracer.logEvent("mq_dispatchIncreaseAmt", bizEventVO.getBizEventType() == null ? "NULL" : bizEventVO.getBizEventType());
    }

    public void sendLifeRightsDispatchMessage(BizEventVO bizEventVO) throws IOException {
        UdpMqProducer udpMqProducer = UdpMqProducerManager.getInstance().getUdpMqProducer(BZ_DISPATCH_PRODUCER_CONN_01);
        udpMqProducer.sendMessage(this.lifeRightsDispatchProducerChannel, bizEventVO.getMessageId(), JSONObject.toJSONString(bizEventVO));
        logger.info("发送生活权益触达消息成功, 消息id:{}, 消息内容:{}", bizEventVO.getMessageId(), JSONObject.toJSONString(bizEventVO));
        Tracer.logEvent("mq_dispatch", "lifeRights");
        Tracer.logEvent("mq_dispatchLifeRights", bizEventVO.getBizEventType() == null ? "NULL" : bizEventVO.getBizEventType());
    }

    public void sendXDayInterestFreeDispatchMessage(BizEventVO bizEventVO) throws IOException {
        UdpMqProducer udpMqProducer = UdpMqProducerManager.getInstance().getUdpMqProducer(BZ_DISPATCH_PRODUCER_CONN_01);
        udpMqProducer.sendMessage(this.xDayInterestFreeDispatchProducerChannel, bizEventVO.getMessageId(), JSONObject.toJSONString(bizEventVO));
        logger.info("发送X天免息触达消息成功, 消息id:{}, 消息内容:{}", bizEventVO.getMessageId(), JSONObject.toJSONString(bizEventVO));
        Tracer.logEvent("mq_dispatch", "xDayInterestFree");
        Tracer.logEvent("mq_dispatchXDayInterestFree", bizEventVO.getBizEventType() == null ? "NULL" : bizEventVO.getBizEventType());
    }

    public void sendAiDispatchMessage(BizEventVO bizEventVO) throws IOException {
        UdpMqProducer udpMqProducer = UdpMqProducerManager.getInstance().getUdpMqProducer(BZ_DISPATCH_PRODUCER_CONN_01);
        udpMqProducer.sendMessage(this.aiDispatchProducerChannel, bizEventVO.getMessageId(), JSONObject.toJSONString(bizEventVO));
        logger.info("发送ai触达消息成功, 消息id:{}, 消息内容:{}", bizEventVO.getMessageId(), JSONObject.toJSONString(bizEventVO));
        Tracer.logEvent("mq_dispatch", "ai");
        Tracer.logEvent("mq_dispatch_ai", bizEventVO.getBizEventType() == null ? "NULL" : bizEventVO.getBizEventType());
    }

    public void sendTeleDispatchMessage(BizEventVO bizEventVO) throws IOException {
        UdpMqProducer udpMqProducer = UdpMqProducerManager.getInstance().getUdpMqProducer(BZ_DISPATCH_PRODUCER_CONN_01);
        udpMqProducer.sendMessage(this.teleDispatchProducerChannel, bizEventVO.getMessageId(), JSONObject.toJSONString(bizEventVO));
        logger.info("发送电销触达消息成功, 消息id:{}, 消息内容:{}", bizEventVO.getMessageId(), JSONObject.toJSONString(bizEventVO));
        Tracer.logEvent("mq_dispatch", StrategyMarketChannelEnum.getInstance(bizEventVO.getMarketChannel()).getDescription());
        Tracer.logEvent("mq_dispatchTele", bizEventVO.getBizEventType() == null ? "NULL" : bizEventVO.getBizEventType());
    }

    public void sendCouponDispatchMessage(BizEventVO bizEventVO) throws IOException {
        UdpMqProducer udpMqProducer = UdpMqProducerManager.getInstance().getUdpMqProducer(BZ_DISPATCH_PRODUCER_CONN_01);
        udpMqProducer.sendMessage(this.couponDispatchProducerChannel, bizEventVO.getMessageId(), JSONObject.toJSONString(bizEventVO));
        logger.info("发送优惠券触达消息成功, 消息id:{}, 消息内容:{}", bizEventVO.getMessageId(), JSONObject.toJSONString(bizEventVO));
        Tracer.logEvent("mq_dispatch", "coupon");
        Tracer.logEvent("mq_dispatchCoupon", bizEventVO.getBizEventType() == null ? "NULL" : bizEventVO.getBizEventType());
    }

    public void sendNoMarketMessage(BizEventVO bizEventVO) throws IOException {
        UdpMqProducer udpMqProducer = UdpMqProducerManager.getInstance().getUdpMqProducer(BZ_DISPATCH_PRODUCER_CONN_01);
        udpMqProducer.sendMessage(this.noMarketProducerChannel, bizEventVO.getMessageId(), JSONObject.toJSONString(bizEventVO));
        logger.info("发送不营销组触达消息成功, 消息id:{}, 消息内容:{}", bizEventVO.getMessageId(), JSONObject.toJSONString(bizEventVO));
        Tracer.logEvent("mq_dispatch", "no");
        Tracer.logEvent("mq_dispatchNo", bizEventVO.getBizEventType() == null ? "NULL" : bizEventVO.getBizEventType());
    }

    public void sendDecisionResultMessage(BizEventVO bizEventVO) {
        try {
            bizEventVO.setTraceId(getTraceId());
            bizEventVO.setDecisionTime(LocalDateTime.now());
            UdpMqProducer udpMqProducer = UdpMqProducerManager.getInstance().getUdpMqProducer(BZ_DECISION_PRODUCER_CONN_01);
            udpMqProducer.sendMessage(this.decisionResultChannel, bizEventVO.getMessageId(), JSONObject.toJSONString(bizEventVO));
            statDecisionRecord(bizEventVO);
            logger.info("发送决策结果消息成功 消息id:{}，消息内容:{}", bizEventVO.getMessageId(), JSONObject.toJSONString(bizEventVO));
        } catch (Exception e) {
            logger.warn("发送决策结果消息异常 消息id:{}，消息内容:{}", bizEventVO.getMessageId(), JSONObject.toJSONString(bizEventVO), e);
        }
    }

    private String getTraceId() {
        try {
            return Tracing.current().currentTraceContext().get().traceIdString();
        } catch (Exception e) {
            logger.warn("getTraceId error...", e);
            return "";
        }
    }

    public boolean verifyMessageId(String redisKey, String messageId) {
        return redisUtils.lock(String.format(redisKey, messageId), 1, RedisUtils.DEFAULT_EXPIRE_SECONDS * 10, TimeUnit.SECONDS);
    }


    private void statDecisionRecord(BizEventVO entity) {

        String curDate = LocalDateTimeUtil.format(entity.getTriggerDatetime(), "yyyyMMdd");
        if (Objects.isNull(entity.getRetryNum()) || entity.getRetryNum() <= 0) {
            // 触发事件总次数
            redisUtils.increment(RedisKeyUtils.genStatDecnSum(curDate, entity.getStrategyId()), 1L, RedisUtils.DEFAULT_EXPIRE_TWO_DAYS);
        }
        // 触发事件总用户数（failCode=null）
        redisUtils.pfAddTwoDay(RedisKeyUtils.genStatDecnUser(curDate, entity.getStrategyId()), entity.getAppUserId());

        if (entity.getFailCode() != null) {
            switch (entity.getFailCode()) {
                case 502:
                    redisUtils.increment(RedisKeyUtils.genStatDecnEventSum(curDate, entity.getStrategyId()), 1L, RedisUtils.DEFAULT_EXPIRE_TWO_DAYS);
                    // 事件条件过滤用户数（failCode=502）
                    redisUtils.pfAddTwoDay(RedisKeyUtils.genStatDecnUserEvent(curDate, entity.getStrategyId()), entity.getAppUserId());
                    break;
                case 506:
                    // 注册时间过滤用户数（failCode=506）
                    redisUtils.pfAddTwoDay(RedisKeyUtils.genStatDecnUserRegtime(curDate, entity.getStrategyId()), entity.getAppUserId());
                    break;
                case 503:
                    // 离线人群过滤用户数（failCode=503）
                    redisUtils.pfAddTwoDay(RedisKeyUtils.genStatDecnUserCrow(curDate, entity.getStrategyId()), entity.getAppUserId());
                    break;
                case 521:
                    // 实时标签过滤用户数（failCode=521）
                    redisUtils.pfAddTwoDay(RedisKeyUtils.genStatDecnUserLabel(curDate, entity.getStrategyId()), entity.getAppUserId());
                    break;
                case 522:
                    // 实时排除项过滤用户数（failCode=522）
                    redisUtils.pfAddTwoDay(RedisKeyUtils.genStatDecnUserExclude(curDate, entity.getStrategyId()), entity.getAppUserId());
                    break;
                case 1001:
                    // 决策引擎调用失败人数，人次（failCode=1001）
                    redisUtils.increment(RedisKeyUtils.genExecuteEngineFailSum(curDate, entity.getStrategyId()), 1L, RedisUtils.DEFAULT_EXPIRE_TWO_DAYS);
                    redisUtils.pfAddTwoDay(RedisKeyUtils.genExecuteEngineFailNum(curDate, entity.getStrategyId()), entity.getAppUserId());
                    break;
                default:
            }
        }

        if (entity.getDecisionResult()) {
            // 复筛通过用户数（decisionResult=1）
            redisUtils.pfAddTwoDay(RedisKeyUtils.genStatDecnUserPass(curDate, entity.getStrategyId()), entity.getAppUserId());
        }

        if (!Objects.isNull(entity.getIfIntoEngine()) && entity.getIfIntoEngine()) {
            //进入引擎人次、人数
            redisUtils.increment(RedisKeyUtils.genIntoEngineSum(curDate, entity.getStrategyId()), 1L, RedisUtils.DEFAULT_EXPIRE_TWO_DAYS);
            redisUtils.pfAddTwoDay(RedisKeyUtils.genIntoEngineNum(curDate, entity.getStrategyId()), entity.getAppUserId());
        }

        if (!Objects.isNull(entity.getIfMarket()) && entity.getIfMarket()) {
            //决策结果为营销人次、人数
            redisUtils.increment(RedisKeyUtils.genMarketSum(curDate, entity.getStrategyId()), 1L, RedisUtils.DEFAULT_EXPIRE_TWO_DAYS);
            redisUtils.pfAddTwoDay(RedisKeyUtils.genMarketNum(curDate, entity.getStrategyId()), entity.getAppUserId());
            if (Objects.equals(DecisionResultEnum.EXCLUDE_LABEL_FILTER_FAIL.getFailCode(), entity.getFailCode())) {
                redisUtils.increment(RedisKeyUtils.genExcludeUserMarketSum(curDate, entity.getStrategyId()), 1L, RedisUtils.DEFAULT_EXPIRE_TWO_DAYS);
            }
        }
        if (!Objects.isNull(entity.getIfMarket()) && !entity.getIfMarket()) {
            //决策结果为不营销人次、人数
            redisUtils.increment(RedisKeyUtils.genNotMarketSum(curDate, entity.getStrategyId()), 1L, RedisUtils.DEFAULT_EXPIRE_TWO_DAYS);
            redisUtils.pfAddTwoDay(RedisKeyUtils.genNotMarketNum(curDate, entity.getStrategyId()), entity.getAppUserId());
            if (Objects.equals(DecisionResultEnum.EXCLUDE_LABEL_FILTER_FAIL.getFailCode(), entity.getFailCode())) {
                redisUtils.increment(RedisKeyUtils.genExcludeUserNotMarketSum(curDate, entity.getStrategyId()), 1L, RedisUtils.DEFAULT_EXPIRE_TWO_DAYS);
            }
        }

    }

}
