package com.xinfei.touch.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xinfei.touch.domain.model.*;
import com.xinfei.touch.domain.repository.FlowControlRuleRepository;
import com.xinfei.touch.infrastructure.repository.entity.FlowControlRuleEntity;
import com.xinfei.touch.infrastructure.repository.mapper.FlowControlRuleMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 频控规则仓储实现
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class FlowControlRuleRepositoryImpl implements FlowControlRuleRepository {
    
    private final FlowControlRuleMapper flowControlRuleMapper;
    
    @Override
    public FlowControlRule findById(Long ruleId) {
        FlowControlRuleEntity entity = flowControlRuleMapper.selectById(ruleId);
        return entity != null ? convertToDomain(entity) : null;
    }
    
    @Override
    public List<FlowControlRule> findEventFlowControlRules(String eventType) {
        LambdaQueryWrapper<FlowControlRuleEntity> wrapper = Wrappers.<FlowControlRuleEntity>lambdaQuery();
        wrapper.eq(FlowControlRuleEntity::getRuleType, FlowControlType.EVENT.getCode())
                .eq(FlowControlRuleEntity::getStatus, RuleStatus.ENABLED.getCode())
                .and(w -> w.eq(FlowControlRuleEntity::getBizEventType, eventType)
                        .or().isNull(FlowControlRuleEntity::getBizEventType))
                .orderByAsc(FlowControlRuleEntity::getPriority);
        
        List<FlowControlRuleEntity> entities = flowControlRuleMapper.selectList(wrapper);
        return entities.stream().map(this::convertToDomain).collect(Collectors.toList());
    }
    
    @Override
    public List<FlowControlRule> findTouchFlowControlRules(TouchChannel channel, Long strategyId, String bizEventType) {
        LambdaQueryWrapper<FlowControlRuleEntity> wrapper = Wrappers.<FlowControlRuleEntity>lambdaQuery();
        wrapper.eq(FlowControlRuleEntity::getRuleType, FlowControlType.TOUCH.getCode())
                .eq(FlowControlRuleEntity::getStatus, RuleStatus.ENABLED.getCode())
                .and(w -> {
                    // 渠道匹配
                    if (channel != null) {
                        w.eq(FlowControlRuleEntity::getChannel, channel.getCode())
                                .or().isNull(FlowControlRuleEntity::getChannel);
                    } else {
                        w.isNull(FlowControlRuleEntity::getChannel);
                    }
                })
                .and(w -> {
                    // 策略匹配
                    if (strategyId != null) {
                        w.eq(FlowControlRuleEntity::getStrategyId, strategyId)
                                .or().isNull(FlowControlRuleEntity::getStrategyId);
                    } else {
                        w.isNull(FlowControlRuleEntity::getStrategyId);
                    }
                })
                .and(w -> {
                    // 事件类型匹配
                    if (bizEventType != null) {
                        w.eq(FlowControlRuleEntity::getBizEventType, bizEventType)
                                .or().isNull(FlowControlRuleEntity::getBizEventType);
                    } else {
                        w.isNull(FlowControlRuleEntity::getBizEventType);
                    }
                })
                .orderByAsc(FlowControlRuleEntity::getPriority);
        
        List<FlowControlRuleEntity> entities = flowControlRuleMapper.selectList(wrapper);
        return entities.stream().map(this::convertToDomain).collect(Collectors.toList());
    }
    
    @Override
    public List<FlowControlRule> findDistributedFlowControlRules(TouchChannel channel, Long strategyId) {
        LambdaQueryWrapper<FlowControlRuleEntity> wrapper = Wrappers.<FlowControlRuleEntity>lambdaQuery();
        wrapper.eq(FlowControlRuleEntity::getRuleType, FlowControlType.DISTRIBUTED.getCode())
                .eq(FlowControlRuleEntity::getStatus, RuleStatus.ENABLED.getCode())
                .and(w -> {
                    if (channel != null) {
                        w.eq(FlowControlRuleEntity::getChannel, channel.getCode())
                                .or().isNull(FlowControlRuleEntity::getChannel);
                    }
                })
                .and(w -> {
                    if (strategyId != null) {
                        w.eq(FlowControlRuleEntity::getStrategyId, strategyId)
                                .or().isNull(FlowControlRuleEntity::getStrategyId);
                    }
                })
                .orderByAsc(FlowControlRuleEntity::getPriority);
        
        List<FlowControlRuleEntity> entities = flowControlRuleMapper.selectList(wrapper);
        return entities.stream().map(this::convertToDomain).collect(Collectors.toList());
    }
    
    @Override
    public List<FlowControlRule> findBatchFlowControlRules(TouchChannel channel, Long strategyId) {
        LambdaQueryWrapper<FlowControlRuleEntity> wrapper = Wrappers.<FlowControlRuleEntity>lambdaQuery();
        wrapper.eq(FlowControlRuleEntity::getRuleType, FlowControlType.BATCH.getCode())
                .eq(FlowControlRuleEntity::getStatus, RuleStatus.ENABLED.getCode())
                .and(w -> {
                    if (channel != null) {
                        w.eq(FlowControlRuleEntity::getChannel, channel.getCode())
                                .or().isNull(FlowControlRuleEntity::getChannel);
                    }
                })
                .and(w -> {
                    if (strategyId != null) {
                        w.eq(FlowControlRuleEntity::getStrategyId, strategyId)
                                .or().isNull(FlowControlRuleEntity::getStrategyId);
                    }
                })
                .orderByAsc(FlowControlRuleEntity::getPriority);
        
        List<FlowControlRuleEntity> entities = flowControlRuleMapper.selectList(wrapper);
        return entities.stream().map(this::convertToDomain).collect(Collectors.toList());
    }
    
    @Override
    public List<FlowControlRule> findAllEnabledRules() {
        LambdaQueryWrapper<FlowControlRuleEntity> wrapper = Wrappers.<FlowControlRuleEntity>lambdaQuery();
        wrapper.eq(FlowControlRuleEntity::getStatus, RuleStatus.ENABLED.getCode())
                .orderByAsc(FlowControlRuleEntity::getPriority);
        
        List<FlowControlRuleEntity> entities = flowControlRuleMapper.selectList(wrapper);
        return entities.stream().map(this::convertToDomain).collect(Collectors.toList());
    }
    
    @Override
    public FlowControlRule save(FlowControlRule rule) {
        FlowControlRuleEntity entity = convertToEntity(rule);
        
        if (entity.getId() == null) {
            flowControlRuleMapper.insert(entity);
        } else {
            flowControlRuleMapper.updateById(entity);
        }
        
        rule.setRuleId(entity.getId());
        return rule;
    }
    
    @Override
    public void deleteById(Long ruleId) {
        flowControlRuleMapper.deleteById(ruleId);
    }
    
    @Override
    public List<FlowControlRule> saveAll(List<FlowControlRule> rules) {
        return rules.stream().map(this::save).collect(Collectors.toList());
    }
    
    /**
     * 转换为领域对象
     */
    private FlowControlRule convertToDomain(FlowControlRuleEntity entity) {
        FlowControlRule rule = new FlowControlRule();
        rule.setRuleId(entity.getId());
        rule.setRuleName(entity.getRuleName());
        rule.setType(FlowControlType.getByCode(entity.getRuleType()));
        rule.setScope(FlowControlScope.getByCode(entity.getScopeType()));
        
        if (entity.getChannel() != null) {
            rule.setChannel(TouchChannel.getByCode(entity.getChannel()));
        }
        
        rule.setStrategyId(entity.getStrategyId());
        rule.setBizEventType(entity.getBizEventType());
        rule.setLimitTimes(entity.getLimitTimes());
        rule.setLimitSeconds(entity.getLimitSeconds());
        rule.setPriority(entity.getPriority());
        rule.setStatus(RuleStatus.getByCode(entity.getStatus()));
        rule.setCreatedTime(entity.getCreatedTime());
        rule.setUpdatedTime(entity.getUpdatedTime());
        
        return rule;
    }
    
    /**
     * 转换为实体对象
     */
    private FlowControlRuleEntity convertToEntity(FlowControlRule rule) {
        FlowControlRuleEntity entity = new FlowControlRuleEntity();
        entity.setId(rule.getRuleId());
        entity.setRuleName(rule.getRuleName());
        entity.setRuleType(rule.getType().getCode());
        entity.setScopeType(rule.getScope().getCode());
        
        if (rule.getChannel() != null) {
            entity.setChannel(rule.getChannel().getCode());
        }
        
        entity.setStrategyId(rule.getStrategyId());
        entity.setBizEventType(rule.getBizEventType());
        entity.setLimitTimes(rule.getLimitTimes());
        entity.setLimitSeconds(rule.getLimitSeconds());
        entity.setPriority(rule.getPriority());
        entity.setStatus(rule.getStatus().getCode());
        entity.setCreatedTime(rule.getCreatedTime());
        entity.setUpdatedTime(rule.getUpdatedTime());
        
        return entity;
    }
}
