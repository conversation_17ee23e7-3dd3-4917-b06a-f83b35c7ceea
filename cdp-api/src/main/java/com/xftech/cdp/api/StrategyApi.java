package com.xftech.cdp.api;

import com.xftech.cdp.api.dto.aitel.AITelBizSourceRequest;
import com.xftech.cdp.api.dto.aitel.AiParam;
import com.xftech.cdp.api.dto.aitel.BizSourceInfo;
import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.base.Response;
import com.xftech.cdp.api.dto.req.*;
import com.xftech.cdp.api.dto.req.random.NewRandomListReq;
import com.xftech.cdp.api.dto.req.sms.SmsGroupReq;
import com.xftech.cdp.api.dto.req.sms.SmsTemplateCheckReq;
import com.xftech.cdp.api.dto.req.sms.SmsTemplateListReq;
import com.xftech.cdp.api.dto.resp.*;
import com.xftech.cdp.api.dto.req.StrategyCreateReq;
import com.xftech.cdp.api.dto.req.StrategyDetailReq;
import com.xftech.cdp.api.dto.req.StrategyListReq;
import com.xftech.cdp.api.dto.req.StrategyOperateReq;
import com.xftech.cdp.api.dto.req.StrategyUpdateReq;
import com.xftech.cdp.api.dto.resp.*;
import com.xftech.cdp.api.dto.resp.*;
import com.xftech.cdp.api.dto.resp.AbsMonitorListResp;
import com.xftech.cdp.api.dto.resp.CouponListResp;
import com.xftech.cdp.api.dto.resp.CrowdPackListResp;
import com.xftech.cdp.api.dto.resp.InstantStrategyDetailResp;
import com.xftech.cdp.api.dto.resp.SmsTemplateListResp;
import com.xftech.cdp.api.dto.resp.StrategyDetailResp;
import com.xftech.cdp.api.dto.resp.StrategyListResp;
import com.xftech.cdp.api.dto.resp.TeleNodeListReq;
import com.xftech.cdp.api.dto.resp.TeleNodeListResp;
import com.xftech.cdp.api.dto.resp.dict.Dict;
import com.xftech.cdp.api.dto.resp.random.RandomListResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/2/9 01:42
 */
@Api(tags = "策略配置")
@RequestMapping("/strategy")
public interface StrategyApi {

    @ApiOperation("创建策略")
    @PostMapping("/create")
    Response<Boolean> create(@Validated @RequestBody StrategyCreateReq strategyCreateReq);

    @ApiOperation("更新策略")
    @PostMapping("/update")
    Response<Boolean> update(@Validated @RequestBody StrategyUpdateReq strategyUpdateReq);

//    @ApiOperation("删除策略")
//    @PostMapping("/delete")
//    Response<Boolean> delete(@Validated @RequestBody StrategyDeleteReq strategySaveReq);

    @ApiOperation("策略列表")
    @PostMapping("/list")
    Response<PageResultResponse<StrategyListResp>> list(@Validated @RequestBody StrategyListReq strategyListReq);

    @ApiOperation("策略详情接口")
    @PostMapping("/detail")
    Response<StrategyDetailResp> getDetail(@Validated @RequestBody StrategyDetailReq strategyDetailReq);

    @ApiOperation("执行策略")
    @PostMapping("/operate")
    Response<Boolean> operate(@Validated @RequestBody StrategyOperateReq strategyOperateReq);

    @ApiOperation("批量删除策略")
    @PostMapping("/batch-delete")
    Response<String> batchDelete(@Validated @RequestBody StrategyBatchDeleteReq strategyBatchDeleteReq);

    /**
     * https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001012238
     * 调用已实现 SmsClient::queryItem
     */
    @ApiOperation("短信模板列表")
    @PostMapping("/sms-template-list")
    Response<PageResultResponse<SmsTemplateListResp>> smsTemplateList(@Validated @RequestBody SmsTemplateListReq smsTemplateListReq);

    @ApiOperation("电销节点列表接口")
    @PostMapping("/tele-node-list")
    Response<TeleNodeListResp> teleNodeList(@Validated @RequestBody TeleNodeListReq teleNodeListReq);

    @ApiOperation("优惠券列表接口")
    @PostMapping("/coupon-list")
    Response<CouponListResp> couponList(@Validated @RequestBody CouponListReq couponListReq);

    @ApiOperation("监控列表")
    @PostMapping("/monitor-list")
    Response<PageResultResponse<AbsMonitorListResp>> monitorList(@Validated @RequestBody MonitorListReq monitorListReq);

    @ApiOperation("T0-引擎版监控列表")
    @PostMapping("/monitor-engine")
    Response<PageResultResponse<AbsMonitorListResp>> monitorEngineList(@Validated @RequestBody MonitorEngineListReq monitorListReq);

    @ApiOperation("下载监控Excel文件")
    @PostMapping("/export-monitor")
    void exportMonitor(@Validated @RequestBody ExportMonitorReq exportMonitorReq, HttpServletResponse response) throws IOException;

    @ApiOperation("查询人群包人数接口")
    @PostMapping("/crowdpack-user-num")
    Response<Integer> getCrowdPackUserNum(@Validated @RequestBody CrowdPackUserNumReq crowdPackUserNumReq);

    @ApiOperation("查询人群包列表")
    @PostMapping("/crowdpack-list")
    Response<PageResultResponse<CrowdPackListResp>> getCrowdPackList(@Validated @RequestBody CrowdPackListReq crowdPackListReq);


    @ApiOperation("创建T0策略")
    @PostMapping("/instant/create")
    Response<Boolean> createInstantStrategy(@Validated @RequestBody InstantStrategyCreateReq strategyCreateReq);

    @ApiOperation("创建T0策略")
    @PostMapping("/instant/update")
    Response<Boolean> updateInstantStrategy(@Validated @RequestBody InstantStrategyUpdateReq strategyUpdateReq);

    @ApiOperation("T0策略详情")
    @PostMapping("/instant/detail")
    Response<InstantStrategyDetailResp> getInstantStrategyDetail(@Validated @RequestBody StrategyDetailReq strategyDetailReq);

    @ApiOperation("获取短信营销类型")
    @PostMapping("/query-sms-group")
    Response<List<Dict>> getSmsTemplateGroup(@Validated @RequestBody SmsGroupReq smsGroupReq);

    @ApiOperation("检查短信模板参数")
    @PostMapping("/check-sms-template")
    Response<Boolean> checkSmsTemplate(@RequestBody SmsTemplateCheckReq smsTemplateCheckReq);

    @ApiOperation("检查短信模板参数")
    @PostMapping("/random-list")
    Response<PageResultResponse<RandomListResp>> randomList(@RequestBody NewRandomListReq newRandomListReq);

    @ApiOperation("引擎列表")
    @GetMapping("/engineCodeList")
    Response<List<String>> engineCodeList();

    // 策略周期展示
    @ApiOperation("预览策略周期区间")
    @GetMapping("/cycle-preview")
    Response<List<CyclePreviewDto>> cyclePreview(@RequestParam("cycleNum") Integer cycleNum);

    @ApiOperation("查询名单类型分页列表")
    @PostMapping("/name-type-list")
    Response<PageResultResponse<NameTypeResp>> getNameTypeList(@RequestBody NameTypeReq nameTypeReq);

    @ApiOperation("电销配置项列表")
    @PostMapping("/name-type-config")
    Response<NameTypeConfigResp> getNameTypeConfigList();

    @ApiOperation("当日策略执行情况")
    @PostMapping("/report-daily")
    Response<StrategyReportDailyResp> reportDailyStrategyList();

    @ApiOperation("查询push分页列表")
    @PostMapping("/push-template-list")
    Response<PageResultResponse<PushTemplateResp>> getPushTemplateList(@RequestBody PushTemplateReq pushTemplateReq);

    @ApiOperation("查询app资源位分页列表")
    @PostMapping("/app-banner-template-list")
    Response<PageResultResponse<AppBannerTemplateResp>> getAppBannerTemplateList(@Validated @RequestBody AppBannerTemplateReq appBannerTemplateReq);


    @ApiOperation("电销策略配置列表")
    @PostMapping("/policy-list")
    Response<PageResultResponse<PolicyListResp>> getPolicyList(@RequestBody PolicyListReq policyListReq);

    @ApiOperation("操作电销策略")
    @PostMapping("/operate-policy")
    Response<Boolean> operatePolicy(@Validated @RequestBody OperatePolicyReq operatePolicyReq);

    @ApiOperation("新电销,更新标签策略的优先级")
    @PostMapping("/update-policy-priority")
    Response<Boolean> updatePolicyPriority(@Validated @RequestBody UpdatePolicyPriorityReq req);

    @ApiOperation("查询电销策略详情")
    @GetMapping("/policy-detail")
    Response<String> policyDetail(@RequestParam("policyId") Integer policyId);

    @ApiOperation("查询商品信息")
    @PostMapping("/goods-list")
    Response<PageResultResponse<GoodsListResp>> getGoodsList(@RequestBody GoodsListReq goodsListReq);

    @ApiOperation("AI电销-查询业务线下所有业务来源")
    @PostMapping("/biz-source-list")
    Response<List<BizSourceInfo>> bizSourceList(@Validated @RequestBody AITelBizSourceRequest aiTelBizSourceRequest);


    @ApiOperation("AI电销-查询ai配置所需变量")
    @GetMapping("/ai_params")
    Response<List<AiParam>> aiParams();

    @ApiOperation("T0-引擎版监控列表")
    @PostMapping("/monitor-engine-reinput")
    Response<PageResultResponse<EngineReInputReportResp>> monitorEngineReInput(@Validated @RequestBody MonitorEngineReInputListReq monitorListReq);

}
