package com.xftech.cdp.crowd;

import com.xftech.cdp.BaseTest;
import com.xftech.cdp.domain.crowd.repository.LabelRepository;
import com.xftech.cdp.infra.repository.cdp.crowd.po.LabelDo;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * @<NAME_EMAIL>
 */
public class LabelRepositoryTest extends BaseTest {
    @Autowired
    LabelRepository labelRepository;

    @Test
    public void getAll() {
    }

    @Test
    public void selectBatchIds() {
        List<Long> distinctMap = new ArrayList<>();
        distinctMap.add(1L);
        distinctMap.add(2L);
        List<LabelDo> labelDos = labelRepository.selectBatchIds(distinctMap);
        System.out.println(labelDos);
    }
}