package com.xftech.cdp.domain.crowd.model.enums;

import com.xftech.cdp.domain.crowd.exception.CrowdException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 人群圈选类型
 *
 * <AUTHOR>
 * @since 2023/2/10
 */
@AllArgsConstructor
@Getter
public enum CrowdFilterMethodEnum {

    /**
     * 文件上传
     */
    UPLOAD(0, "文件上传"),

    /**
     * 标签圈选
     */
    LABEL(1, "标签圈选");

    private final int code;

    private final String description;

    public static CrowdFilterMethodEnum getInstance(Integer code) {

        for (CrowdFilterMethodEnum filterMethodEnum : CrowdFilterMethodEnum.values()) {
            if (Objects.equals(filterMethodEnum.getCode(), code)) {
                return filterMethodEnum;
            }
        }
        throw new CrowdException(String.format("不存在该筛选类型：%s", code));
    }
}
