/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.resp.flow.strategy;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ FlowListResp, v 0.1 2023/12/20 17:11 yye.xu Exp $
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Flow {
    @ApiModelProperty(value = "画布id")
    private Long id;
    @ApiModelProperty(value = "画布编号")
    private String flowNo;
    @ApiModelProperty(value = "画布名称")
    private String name;
    @ApiModelProperty(value = "画布类型：1-离线, 2-T0")
    private Integer flowType;
    @ApiModelProperty(value = "业务线： new-cust-新客, old-cust-老客, test-cust-测试")
    private String businessType;
    @ApiModelProperty(value = "有效开始, yyyy-MM-dd HH:mm:ss")
    private String validityBegin;
    @ApiModelProperty(value = "有效结束, yyyy-MM-dd HH:mm:ss")
    private String validityEnd;
    @ApiModelProperty(value = "发送类型：0-单次 1-例行 2-事件，3-周期；9-引擎")
    private Integer sendRuler;
    @ApiModelProperty(value = "触达渠道, json字符串列表[1,2,3,4]")
    private List<Integer> marketChannels;
    @ApiModelProperty(value = "状态：-1-未发布 0-已发布 1-执行中 4-暂停 5-已结束")
    private Integer status;
    @ApiModelProperty(value = "创建人")
    private String createdOp;
    @ApiModelProperty(value = "最近更新人")
    private String updatedOp;
    @ApiModelProperty(value = "创建时间,  yyyy-MM-dd HH:mm:ss")
    private String createdTime;
    @ApiModelProperty(value = "更新时间,  yyyy-MM-dd HH:mm:ss")
    private String updatedTime;
    @ApiModelProperty(value = "策略id列表")
    private List<Long> strategyIds;
}