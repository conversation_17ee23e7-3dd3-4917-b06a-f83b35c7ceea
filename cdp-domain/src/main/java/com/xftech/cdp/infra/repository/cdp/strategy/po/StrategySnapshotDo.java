package com.xftech.cdp.infra.repository.cdp.strategy.po;

import com.alibaba.fastjson.JSON;
import com.xftech.cdp.domain.strategy.model.dispatch.StrategyContext;
import com.xftech.cdp.infra.repository.Do;
import com.xftech.cdp.infra.utils.JsonUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 策略执行快照表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class StrategySnapshotDo extends Do {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 策略id
     */
    private Long strategyId;

    /**
     * 策略执行日志id
     */
    private Long strategyExecLogId;

    /**
     * 策略配置快照
     */
    private String snapshotDetail;

    public static StrategySnapshotDo beginExecute(StrategyContext strategyContext) {
        StrategySnapshotDo bo = new StrategySnapshotDo();
        bo.setStrategyId(strategyContext.getStrategyDo().getId());
        bo.setStrategyExecLogId(strategyContext.getStrategyExecLogDo().getId());
        //sql太长不存记录日志
        StrategyContext snapshot = JsonUtil.parse(JsonUtil.toJson(strategyContext),StrategyContext.class);
        snapshot.setCrowdContent(null);
        bo.setSnapshotDetail(JSON.toJSONString(snapshot));
        return bo;
    }

}
