package com.xftech.cdp.domain.crowd.service.impl;

import com.xftech.cdp.domain.crowd.repository.CrowdDetailSubRepository;
import com.xftech.cdp.domain.crowd.service.CrowdDetailSubService;
import com.xftech.cdp.domain.subtable.repository.SplitTableRouteRepository;
import com.xftech.cdp.domain.subtable.service.SplitTableService;
import com.xftech.cdp.infra.constant.Constants;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.subtable.po.SplitTableRoute;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

/**
 * 分表 Service 层
 * <AUTHOR>
 * @since 2023/2/9 01:32
 */
@Service
public class CrowdDetailSubServiceImpl implements CrowdDetailSubService {
    @Autowired
    public CrowdDetailSubRepository crowdDetailSubRepository;
    @Autowired
    private SplitTableService splitTableService;
    @Autowired
    private SplitTableRouteRepository splitTableRouteRepository;

    /**
     * 分表批量写入
     * @param crowdDetailDoList
     */
    @Override
    public void splitTableBatchSave(List<CrowdDetailDo> crowdDetailDoList) {
        // 获取分表序号
        int tableNo = splitTableService.getBatchSaveTableNo(Constants.CROWD_DETAIL_STR, crowdDetailDoList.size());
        // 生成表名，批量写入对应分表
        crowdDetailSubRepository.batchSaveCrowdDetailSub(tableNo, crowdDetailDoList);
        Long crowdId = crowdDetailDoList.get(0).getCrowdId();
        Long crowdExeclogId = crowdDetailDoList.get(0).getCrowdExecLogId();
        if (!splitTableRouteRepository.existSplitTableRoute(crowdExeclogId, tableNo)) {
            // 写入分表路由表
            SplitTableRoute insertSplitTableRoute = new SplitTableRoute();
            insertSplitTableRoute.setCrowdId(crowdId);
            insertSplitTableRoute.setCrowdExecLogId(crowdExeclogId);
            insertSplitTableRoute.setExecDate(LocalDate.now());
            insertSplitTableRoute.setTableName(Constants.CROWD_DETAIL_STR);
            insertSplitTableRoute.setTableNo(tableNo);
            splitTableRouteRepository.insertSplitTableRoute(insertSplitTableRoute);
        }
    }
}
