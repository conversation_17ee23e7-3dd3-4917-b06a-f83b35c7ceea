package com.xftech.cdp.feign.model.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 * @version $ AppBannerResponse, v 0.1 2025/5/27 16:19  Exp $
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AppBannerResponse<T> {

    private Boolean suc;

    private String code;

    private String message;

    private T data;

    public boolean isSuccess(){
        return suc;
    }
}