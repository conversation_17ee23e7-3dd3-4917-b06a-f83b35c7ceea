package com.xftech.cdp.domain.strategy.service;

public interface UserSendCounterService {
    long countSum(long userId, long strategyId, int marketChannel, int startDay, int endDay);

    void counterIncrementSum(long userId, String mobile, long strategyId, int marketChannel, int dateValue, int count);

    void counterIncrementFailed(long userId, long strategyId, int marketChannel, int dateValue);
}
