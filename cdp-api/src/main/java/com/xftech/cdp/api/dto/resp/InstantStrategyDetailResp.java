package com.xftech.cdp.api.dto.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xftech.cdp.api.dto.base.TimeFormat;
import com.xftech.cdp.api.dto.req.BaseReq;
import com.xftech.cdp.api.dto.req.DispatchConfig;
import com.xftech.cdp.api.dto.req.InstantStrategyCreateReq;
import com.xftech.cdp.api.dto.req.StrategyCreateReq;
import com.xftech.cdp.api.dto.req.external.CallingSourceEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class InstantStrategyDetailResp extends BaseReq {

    @ApiModelProperty(value = "id", required = true)
    private Long id;

    @ApiModelProperty(value = "策略类型 0-默认其它，1-引擎规则")
    private Integer type = 0;

    @Size(max = 64, message = "引擎code不能超过64个字符")
    @ApiModelProperty(value = "引擎code")
    private String engineCode;

    @Size(max = 20, message = "策略名称不能超过20个字符")
    @NotEmpty(message = "策略名称不能为空")
    @ApiModelProperty(value = "策略名称", required = true)
    private String name;

    @Size(max = 100, message = "策略描述不能超过100个字符")
    @ApiModelProperty(value = "策略描述")
    private String detailDescription;

    @ApiModelProperty(value = "策略分组")
    private Integer strategyGroupType;

    @ApiModelProperty(value = "人群包id,多个人群\";\"分隔", required = true)
    private String crowdPackIds;

    @ApiModelProperty(value = "用户是否转换 0-否 1-是")
    private Integer userConvert;

    @ApiModelProperty(value = "发布类型 -1 草稿  0 发布")
    private Integer publishType;

    @Min(0)
    @Max(1)
    @ApiModelProperty(value = "abTest 0-否 1-是", required = true)
    private Integer abTest;

    @Min(0)
    @ApiModelProperty(value = "分组方式，0：uid倒数两位 1：随机数")
    private Integer abType;

    @ApiModelProperty(value = "随机数")
    private String randomItem;

    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    @ApiModelProperty(value = "策略开始时间")
    @NotNull(message = "策略开始时间不能为空")
    private LocalDateTime validityBegin;

    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    @ApiModelProperty(value = "策略结束时间")
    private LocalDateTime validityEnd;

    @ApiModelProperty(value = "策略触发事件集合")
    private List<InstantStrategyCreateReq.EventSet> eventSet;

    @ApiModelProperty(value = "营销条件配置")
    List<MarketCondition> marketCondition;

    @ApiModelProperty(value = "每日应触达用户数")
    private Integer dispatchMinUserNum;

    @ApiModelProperty(value = "每日应触达用户数")
    private Integer dispatchMaxUserNum;

    @ApiModelProperty(value = "人群分组")
    private List<StrategyCreateReq.StrategyGroup> strategyGroups;

    @ApiModelProperty(value = "触达天数")
    private Integer limitDays;

    @ApiModelProperty(value = "触达次数")
    private Integer limitTimes;

    @ApiModelProperty(value = "业务线 new-cust-新客  old-cust-老客")
    @NotBlank(message = "业务线不能为空")
    private String businessType;

    @ApiModelProperty(value = "业务线名称")
    private String businessTypeName;

    @ApiModelProperty(value = "营销人群类型 1-实时人群  2-离线人群包")
    private Integer marketCrowdType;

    @ApiModelProperty(value = "排除项类型")
    private List<Integer> excludeType;

    @ApiModelProperty(value = "触达时间配置")
    private DispatchConfig dispatchConfig;

    // 触达类型: MKT-营销,  NOTIFY-通知
    @ApiModelProperty(value = "触达类型: MKT-营销,  NOTIFY-通知")
    private String dispatchType;


    @ApiModelProperty(value = "营销节点类型: 1——消息上报, 2——接口调用")
    private Integer marketType;

    @ApiModelProperty(value = "调用来源")
    private Integer callingSource;

    @ApiModelProperty(value = "T0策略 推入引擎次数限制")
    private InstantStrategyCreateReq.EngineRateLimit engineRateLimit;

    @Data
    public static class MarketCondition {
        @ApiModelProperty(value = "id")
        private Long conditionId;

        @ApiModelProperty(value = "时间类型，1-分钟，2-小时，3-当天")
        private Integer timeType;

        @ApiModelProperty(value = "标签英文名称")
        private String labelName;

        @ApiModelProperty(value = "条件操作符")
        private String operateType;

        @ApiModelProperty(value = "时间值")
        private Integer timeValue;

        @ApiModelProperty(value = "条件值")
        private String conditionValue;

        @ApiModelProperty(value = "关系，1-且 2-或")
        private Integer relationship;

        /**
         * 是否可选 1-可选择标签  2 默认排除项标签(不可选) 3排除项标签（可选）
         */
        private Integer optional;
    }

}
