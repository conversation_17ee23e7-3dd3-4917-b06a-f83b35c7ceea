package com.xftech.cdp.domain.strategy.service.dispatch.event;

import com.xftech.cdp.infra.rabbitmq.vo.BizEventMessageVO;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventVO;

/**
 * <AUTHOR> xugong<PERSON>@mzsk.com
 */
public interface StrategyEventDispatchService {

    /**
     * 1.预筛
     * @param messageId
     * @param bizEventMessageVO
     */
    void prescreen(String messageId, BizEventMessageVO bizEventMessageVO);

    /**
     * 2.复筛
     *
     * @param bizEventVO 事件通知
     */
    void rescreen(BizEventVO bizEventVO);

    /**
     * 3.触达
     *
     * @param bizEventVO 事件通知
     */
    void dispatch(BizEventVO bizEventVO);
}
