package com.xftech.cdp.domain.event.processor;

import com.xftech.cdp.domain.event.model.enums.DataParserEnum;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 数据处理器策略类
 * <AUTHOR>
 * @version $ DataProcessorStrategy, v 0.1 2024/11/18 11:30 snail Exp $
 */
@Component
public class DataProcessorStrategy {
    //数据处理器的容器
    private final Map<String,DataProcessor> PROCESSOR_MAP = new HashMap<>();

    /**
     * 添加一个数据处理器到容器中
     * @param processType 数据处理器的类型
     * @param processor 数据处理器处理类
     */
    public void register(String processType, DataProcessor processor){
        if(!PROCESSOR_MAP.containsKey(processType)){
            PROCESSOR_MAP.put(processType,processor);
        }
    }

    /**
     * 根据数据处理器类型获取对应的处理类
     * @param processType 数据处理器类型
     * @return 数据处理器处理类
     */
    public DataProcessor getProcessor(String processType){
        DataProcessor processor = PROCESSOR_MAP.get(processType);
        if(Objects.nonNull(processor)){
            return processor;
        }
        return PROCESSOR_MAP.get(DataParserEnum.SIMPLE.getType());
    }
}
