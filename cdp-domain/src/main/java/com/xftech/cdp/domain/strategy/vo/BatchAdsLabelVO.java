package com.xftech.cdp.domain.strategy.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class BatchAdsLabelVO {
    /**
     * 策略ID
     */
    private Long strategyId;

    private String app;

    private String startTime;

    private List<UserInfo> userInfoList;

    @Data
    public static class UserInfo {

        private String mobile;

        private Long appUserId;

        private LocalDateTime registerTime;

        private String flowBatchNo;
    }
}
