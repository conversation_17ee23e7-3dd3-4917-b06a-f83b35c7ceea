package com.xftech.cdp.api.dto.req.external;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @date 2023/4/3 15:39
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TeleImportResultReq {

    @JsonProperty("flow_no")
    @JSONField(name = "flow_no")
    @ApiModelProperty(value = "批次号", required = true)
    private String flowNo;

    @JsonProperty("user_type")
    @JSONField(name = "user_type")
    @ApiModelProperty(value = "名单节点", required = true)
    private Long userType;

    @JsonProperty("user_list")
    @JSONField(name = "user_list")
    @ApiModelProperty(value = "用户列表数组", required = true)
    private List<TeleUser> userList;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TeleUser {
        /**
         * 用户ID
         */
        @JsonProperty("credit_id")
        @JSONField(name = "credit_id")
        @ApiModelProperty(value = "用户列表数组", required = true)
        private Long creditId;
        /**
         * 1-入库用户，2-排除用户
         */
        @JSONField(name = "status")
        @ApiModelProperty(value = "1-入库用户，2-排除用户", required = true)
        private Integer status;
        /**
         * 入库时间
         */
        @JSONField(name = "execTime")
        @ApiModelProperty(value = "入库时间", required = true)
        private String execTime;
    }
}
