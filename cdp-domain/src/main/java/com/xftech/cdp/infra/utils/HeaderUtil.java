package com.xftech.cdp.infra.utils;

import com.xftech.cdp.infra.client.sso.model.constants.SsoConstants;
import com.xftech.cdp.infra.exception.InfraException;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR> wanghu
 * @since : 2022-08-02 17:12:54
 */
public class HeaderUtil {

    public static String getToken() {
        return getRequest().getHeader(SsoConstants.TOKEN_HEADER_NAME);
    }


    private static HttpServletRequest getRequest() {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();

        if (requestAttributes == null) {
            throw new InfraException("获取不到 HttpRequest 对象");
        }

        return requestAttributes.getRequest();
    }


    public interface Constants {
        String APP_NAME = "App-Name";

        String TOKEN = "token";

        String UNIQUE_APP = "ua";

        String REQUEST_FLOAT_NUMBER = "Request-Float-Number";
    }


}
