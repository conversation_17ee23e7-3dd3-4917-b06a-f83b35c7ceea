package com.xftech.cdp.domain.crowd.repository;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.domain.crowd.model.enums.CrowdExecResultEnum;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdExecLogDo;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 人群包执行日志记录表操作
 *
 * <AUTHOR>
 * @since 2023/2/14
 */

@Component
public class CrowdExecLogRepository {

    public List<CrowdExecLogDo> findTodayExecLogsByCrowdIdAndStatus(Long crowdId, CrowdExecResultEnum crowdExecResultEnum) {
        Map<String, Object> param = new HashMap<>();
        param.put("crowdId", crowdId);
        param.put("execResult", crowdExecResultEnum.getCode());
        param.put("createdTime", LocalDateTimeUtil.beginOfDay(LocalDateTime.now()));
        return DBUtil.selectList("crowdExecLog.findTodayExecLogsByCrowdIdAndStatus", param);
    }

    /**
     * 根据人群包id列表查询每个人群包最大执行记录id
     *
     * @param crowdIds 人群包id列表
     * @return
     */
    public List<Map<String, Long>> selectCrowdMaxExecLogIds(@NonNull List<Long> crowdIds) {
        Map<String, Object> param = new HashMap<>();
        param.put("crowdIds", crowdIds);
        return DBUtil.selectList("crowdExecLog.selectCrowdMaxExecLogIds", param);
    }

    public Map<String, Object> selectCrowdMaxExecLogIdMap(Long crowdId) {
        return DBUtil.selectOne("crowdExecLog.selectCrowdMaxExecLogIdMap", crowdId);
    }

    public Map<String, Object> selectCrowdMaxExecLogTimeMap(Long crowdId) {
        return DBUtil.selectOne("crowdExecLog.selectCrowdMaxExecLogTimeMap", crowdId);
    }

    /**
     * 根据人群包id查询人群包最大执行记录id
     *
     * @param crowdId 人群包id
     * @return 人群包最大执行记录id
     */
    public Long selectCrowdMaxExecLogId(Long crowdId) {
        return DBUtil.selectOne("crowdExecLog.selectCrowdMaxExecLogId", crowdId);
    }

    public List<Long> selectExecLogIdsByCrowdIds(List<Long> crowdIds, LocalDateTime localDateTime) {
        Map<String, Object> param = new HashMap<>();
        param.put("crowdIds", crowdIds);
        param.put("localDateTime", localDateTime);
        return DBUtil.selectList("crowdExecLog.selectExecLogIdsByCrowdIds", param);

    }

    /**
     * 插入一条人群包执行日志记录
     *
     * @param execLog 人群包执行日志对象
     */
    public void insert(CrowdExecLogDo execLog) {
        DBUtil.insert("crowdExecLog.insertSelective", execLog);
    }

    /**
     * 根据主键更新人群包执行日志记录
     *
     * @param crowdExecLog 人群包执行日志对象
     */
    public boolean updateById(CrowdExecLogDo crowdExecLog) {
        return DBUtil.update("crowdExecLog.updateByPrimaryKeySelective", crowdExecLog) > 0;
    }

    public LocalDateTime selectMaxRefreshSuccessTimeByCrowdIds(LocalDate date, String[] crowdIds) {
        Map<String, Object> param = new HashMap<>();
        param.put("date", date);
        param.put("crowdIds", crowdIds);
        return DBUtil.selectOne("crowdExecLog.selectMaxRefreshSuccessTimeByCrowdIds", param);
    }

    public Integer countCrowdPackUserNum(LocalDate date, String[] crowdIds) {
        Map<String, Object> param = new HashMap<>();
        param.put("date", date);
        param.put("crowdIds", crowdIds);
        return DBUtil.selectOne("crowdExecLog.countCrowdPackUserNum", param);
    }

    public List<CrowdExecLogDo> selectExecLogByCrowdIdsAndTime(List<Long> crowdIds, LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> param = new HashMap<>();
        param.put("crowdIds", crowdIds);
        param.put("startTime", startTime);
        param.put("endTime", endTime);
        return DBUtil.selectList("crowdExecLog.selectExecLogByCrowdIdsAndTime", param);
    }

    public boolean existLogByCrowdAndExecTime(CrowdExecLogDo crowdExecLogDo) {
        return (int) DBUtil.selectOne("crowdExecLog.existLogByCrowdAndExecTime", crowdExecLogDo) > 0;
    }


    public void updateByCrowdAndExecTime(CrowdExecLogDo crowdExecLogDo) {
        DBUtil.update("crowdExecLog.updateByCrowdAndExecTime", crowdExecLogDo);
    }
}
