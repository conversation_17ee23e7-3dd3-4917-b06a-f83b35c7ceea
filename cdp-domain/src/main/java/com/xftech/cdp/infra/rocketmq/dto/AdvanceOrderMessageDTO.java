package com.xftech.cdp.infra.rocketmq.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025年4月8日13:51:11
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AdvanceOrderMessageDTO {
    /**
     * userNo
     */
    private String userNo;

    /**
     * app
     */
    private String app;

    /**
     * innerApp
     */
    private String innerApp;

    /**
     * 设备标识符
     */
    private String deviceId;

    /**
     * 渠道类型：client-客户端, wap-h5-移动网页, wxmnp-微信小程序。
     */
    private String sourceType;

    /**
     * 事件触发时间,指预借款单的创建时间
     */
    private Date triggerDatetime;

    /**
     * 上报时间
     */
    private Date requestDateTime;

    /**
     * 预借款单单号
     */
    private String orderNo;

    /**
     * 预借款场景：PRE_LEND_NEW:预借款单-新客,PRE_LEND_OLD:预借款单-老客
     */
    private String advanceLoanScene;
}
