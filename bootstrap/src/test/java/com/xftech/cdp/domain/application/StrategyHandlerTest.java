/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.domain.application;

import com.xftech.cdp.BaseTest;
import com.xftech.cdp.application.StrategyHandler;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 *
 * <AUTHOR>
 * @version $ StrategyHandlerTest, v 0.1 2023/11/9 14:45 yye.xu Exp $
 */

public class StrategyHandlerTest extends BaseTest {

    @Autowired
    private StrategyHandler strategyHandler;

    @Test
    public void testAlarm() {
        strategyHandler.strategyDispatchTaskExectuteAlarm(30);
    }
}