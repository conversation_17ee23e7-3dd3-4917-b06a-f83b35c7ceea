package com.xftech.cdp.domain.touch.service;

import com.xftech.cdp.domain.touch.model.TouchRequest;
import com.xftech.cdp.domain.touch.model.TouchResponse;

/**
 * 统一触达服务接口
 * 提供统一的触达处理入口，支持所有类型的触达方式
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-23
 */
public interface UnifiedTouchService {
    
    /**
     * 统一触达处理入口
     * 支持T0实时触达、T0引擎触达、离线普通触达、离线引擎触达
     * 
     * @param request 统一触达请求
     * @return 触达响应
     */
    TouchResponse processTouch(TouchRequest request);
    
    /**
     * 异步触达处理
     * 适用于批量触达等耗时较长的场景
     * 
     * @param request 统一触达请求
     * @return 触达响应（包含批次号，可用于查询进度）
     */
    TouchResponse processTouchAsync(TouchRequest request);
    
    /**
     * 查询触达进度
     * 用于查询异步触达的处理进度
     * 
     * @param requestId 请求ID
     * @return 触达响应（包含当前进度信息）
     */
    TouchResponse queryTouchProgress(String requestId);
    
    /**
     * 取消触达处理
     * 用于取消正在处理中的异步触达
     * 
     * @param requestId 请求ID
     * @return 是否取消成功
     */
    boolean cancelTouch(String requestId);
}
