package com.xftech.cdp.adapter.param;

import com.xftech.cdp.api.TemplateParamApi;
import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.base.Response;
import com.xftech.cdp.api.dto.req.StrategyDetailReq;
import com.xftech.cdp.api.dto.req.param.TemplateParamCreateReq;
import com.xftech.cdp.api.dto.req.param.TemplateParamDetailReq;
import com.xftech.cdp.api.dto.req.param.TemplateParamListReq;
import com.xftech.cdp.api.dto.resp.StrategyDetailResp;
import com.xftech.cdp.api.dto.resp.param.TemplateParamListResp;
import com.xftech.cdp.domain.param.service.TemplateParamService;
import com.xftech.cdp.infra.annotation.OperateLogAnnotation;
import com.xftech.cdp.infra.aviator.enums.OperateModeEnum;
import com.xftech.cdp.infra.aviator.enums.OperateTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * @<NAME_EMAIL>
 */

@RestController
public class TemplateParamController implements TemplateParamApi {
    @Autowired
    TemplateParamService templateParamService;

    @Override
    @OperateLogAnnotation(description = "新增模板参数", type = OperateTypeEnum.ADD, mode = OperateModeEnum.TEMPLATE)
    public Response<Boolean> create(TemplateParamCreateReq templateParamCreateReq) {
        return Response.success(templateParamService.create(templateParamCreateReq));
    }

    @Override
    @OperateLogAnnotation(description = "修改模板参数", type = OperateTypeEnum.UPDATE, mode = OperateModeEnum.TEMPLATE)
    public Response<Boolean> update(TemplateParamCreateReq templateParamCreateReq) {
        return Response.success(templateParamService.update(templateParamCreateReq));
    }

    @Override
    public Response<PageResultResponse<TemplateParamListResp>> list(TemplateParamListReq templateParamListReq) {
        return Response.success(templateParamService.list(templateParamListReq));
    }

    @Override
    public Response<TemplateParamListResp> getDetail(TemplateParamDetailReq templateParamDetailReq) {
        return Response.success(templateParamService.getDetail(templateParamDetailReq));
    }
}
