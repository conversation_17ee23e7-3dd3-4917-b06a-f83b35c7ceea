/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.infra.aspect;

import com.xftech.cdp.infra.config.CrowdConfig;
import com.xftech.cdp.infra.utils.JsonUtil;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

import java.util.UUID;


/**
 *
 * <AUTHOR>
 * @version $ TimingInterceptor, v 0.1 2023/11/27 13:51 wancheng.qu Exp $
 */

@Aspect
@Component
@Slf4j
public class TimingInterceptor {

    private final CrowdConfig crowdConfig;

    @Autowired
    public TimingInterceptor(CrowdConfig crowdConfig) {
        this.crowdConfig = crowdConfig;
    }

    @Around("execution(* com.xftech.cdp.domain.crowd.repository.*.*(..))")
    public Object logExecutionTime(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        Object result = joinPoint.proceed();
        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;
        String uid = UUID.randomUUID().toString();
        try {
            MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
            String methodName = methodSignature.getName();
            if (executionTime > crowdConfig.getExecutionTime()  && log.isInfoEnabled()) {
                log.info("Method {} executed in {}ms uid {} req {} res {}", methodName, executionTime,uid,joinPoint.getArgs(), JsonUtil.toJson(result));
            }
        }catch (Exception e){}
        return result;
    }
}


