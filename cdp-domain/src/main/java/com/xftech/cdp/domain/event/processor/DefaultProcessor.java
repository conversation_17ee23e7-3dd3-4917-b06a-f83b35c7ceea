package com.xftech.cdp.domain.event.processor;

import cn.hutool.core.convert.Convert;
import com.xftech.cdp.domain.event.model.annotation.ProcessorRule;
import com.xftech.cdp.domain.event.model.config.DefaultConfig;
import com.xftech.cdp.domain.event.model.config.FieldConfig;
import com.xftech.cdp.domain.event.model.dto.Field;
import com.xftech.cdp.domain.event.model.dto.FieldDetail;
import com.xftech.cdp.domain.event.model.enums.DataProcessEnum;
import com.xftech.cdp.domain.event.model.enums.DataTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * 默认值数据处理器处理类
 * <AUTHOR>
 * @version $ DefaultProcessor, v 0.1 2024/11/19 15:04 snail Exp $
 */
@Slf4j
@Component
@ProcessorRule(processor = DataProcessEnum.DEFAULT)
public class DefaultProcessor extends AbstractDataProcessor{
    @Override
    public Field process(FieldDetail detail, FieldConfig fieldConfig, Map<String, Object> values) {
        DefaultConfig defaultConfig = (DefaultConfig) fieldConfig;
        Object originVal = values.get(detail.getTargetField());
        if(Objects.nonNull(originVal)){
            return null;
        }

        DataTypeEnum dataType = DataTypeEnum.getDataType(defaultConfig.getDefaultType());
        if(Objects.isNull(dataType)){
            return null;
        }

        Object result = Convert.convert(dataType.getClazz(),defaultConfig.getDefaultValue());
        return new Field(detail.getTargetField(),result);
    }
}
