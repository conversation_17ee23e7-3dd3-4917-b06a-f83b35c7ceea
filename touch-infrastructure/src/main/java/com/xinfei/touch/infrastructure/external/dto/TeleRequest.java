package com.xinfei.touch.infrastructure.external.dto;

import lombok.Data;

import java.util.Map;

/**
 * 电销请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class TeleRequest {
    
    /**
     * 请求ID
     */
    private String requestId;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 策略ID
     */
    private Long strategyId;
    
    /**
     * 手机号
     */
    private String mobile;
    
    /**
     * 话术脚本ID
     */
    private String scriptId;
    
    /**
     * 话术参数
     */
    private Map<String, Object> scriptParams;
    
    /**
     * 回调地址
     */
    private String callbackUrl;
    
    /**
     * 扩展参数
     */
    private Map<String, Object> extParams;
}
