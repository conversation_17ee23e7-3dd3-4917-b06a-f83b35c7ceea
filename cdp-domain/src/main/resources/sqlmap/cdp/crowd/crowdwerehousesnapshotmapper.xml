<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="crowdWereHouseSnapshot">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdWereHouseSnapshotDo">
        <id column="id" property="id"/>
        <result column="biz_date" property="bizDate"/>
        <result column="min_id" property="minId"/>
        <result column="max_id" property="maxId"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="d_flag" property="dFlag"/>
        <result column="created_op" property="createdOp"/>
        <result column="updated_op" property="updatedOp"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, biz_date, min_id, max_id, created_time, updated_time, d_flag, created_op, updated_op
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crowd_were_house_snapshot
        where id = #{id,jdbcType=BIGINT}
        and d_flag = 0
    </select>

    <select id="getTodayCrowd" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crowd_were_house_snapshot
        where biz_date >= #{date}
        and d_flag = 0
        order by id
        limit 1
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        update crowd_were_house_snapshot
        set d_flag = 1
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdWereHouseSnapshotDo"
            useGeneratedKeys="true">
        insert into crowd_were_house_snapshot
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="bizDate != null">biz_date,</if>
            <if test="minId != null">min_id,</if>
            <if test="maxId != null">max_id,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updatedTime != null">updated_time,</if>
            <if test="dFlag != null">d_flag,</if>
            <if test="createdOp != null">created_op,</if>
            <if test="updatedOp != null">updated_op,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="bizDate != null">#{bizDate,jdbcType=TIMESTAMP},</if>
            <if test="minId != null">#{minId,jdbcType=BIGINT},</if>
            <if test="maxId != null">#{maxId,jdbcType=BIGINT},</if>
            <if test="createdTime != null">#{createdTime,jdbcType=TIMESTAMP},</if>
            <if test="updatedTime != null">#{updatedTime,jdbcType=TIMESTAMP},</if>
            <if test="dFlag != null">#{dFlag,jdbcType=SMALLINT},</if>
            <if test="createdOp != null">#{createdOp,jdbcType=VARCHAR},</if>
            <if test="updatedOp != null">#{updatedOp,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdWereHouseSnapshotDo">
        update crowd_were_house_snapshot
        <set>
            <if test="bizDate != null">
                biz_date = #{bizDate,jdbcType=TIMESTAMP},
            </if>
            <if test="minId != null">
                min_id = #{minId,jdbcType=BIGINT},
            </if>
            <if test="maxId != null">
                max_id = #{maxId,jdbcType=BIGINT},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedTime != null">
                updated_time = #{updatedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="dFlag != null">
                d_flag = #{dFlag,jdbcType=SMALLINT},
            </if>
            <if test="createdOp != null">
                created_op = #{createdOp,jdbcType=VARCHAR},
            </if>
            <if test="updatedOp != null">
                updated_op = #{updatedOp,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

</mapper>
