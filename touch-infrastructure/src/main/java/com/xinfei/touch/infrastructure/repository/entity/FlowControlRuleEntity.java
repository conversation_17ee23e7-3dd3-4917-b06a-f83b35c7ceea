package com.xinfei.touch.infrastructure.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 频控规则实体
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@TableName("flow_control_rule")
public class FlowControlRuleEntity {
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 规则名称
     */
    @TableField("rule_name")
    private String ruleName;
    
    /**
     * 规则类型：1-事件级，2-触达级，3-分布式，4-批量
     */
    @TableField("rule_type")
    private Integer ruleType;
    
    /**
     * 范围类型：1-用户级，2-策略级，3-渠道级，4-全局级
     */
    @TableField("scope_type")
    private Integer scopeType;
    
    /**
     * 渠道：1-短信，2-电销，3-Push，4-优惠券
     */
    @TableField("channel")
    private Integer channel;
    
    /**
     * 策略ID
     */
    @TableField("strategy_id")
    private Long strategyId;
    
    /**
     * 业务事件类型
     */
    @TableField("biz_event_type")
    private String bizEventType;
    
    /**
     * 限制次数
     */
    @TableField("limit_times")
    private Integer limitTimes;
    
    /**
     * 限制时间（秒）
     */
    @TableField("limit_seconds")
    private Integer limitSeconds;
    
    /**
     * 优先级，数字越小优先级越高
     */
    @TableField("priority")
    private Integer priority;
    
    /**
     * 状态：0-禁用，1-启用
     */
    @TableField("status")
    private Integer status;
    
    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;
}
