# Touch Service API 文档

## 概述

Touch Service 提供统一的触达服务API，支持短信、电销、Push、优惠券等多种渠道的触达功能。

## 基础信息

- **Base URL**: `http://localhost:8080/touch-service`
- **Content-Type**: `application/json`
- **字符编码**: `UTF-8`

## 通用响应格式

```json
{
  "code": 0,
  "message": "success",
  "data": {},
  "timestamp": 1640995200000
}
```

- `code`: 响应码，0表示成功，其他表示失败
- `message`: 响应消息
- `data`: 响应数据
- `timestamp`: 响应时间戳

## API接口

### 1. 单个触达

**接口地址**: `POST /api/touch/send`

**请求参数**:
```json
{
  "requestId": "req_123456789",
  "touchType": "REALTIME_NORMAL",
  "channel": "SMS",
  "strategyId": 1001,
  "userId": 123456,
  "bizEventType": "Login",
  "templateParams": {
    "content": "您好，欢迎登录！"
  },
  "touchConfig": {
    "priority": 1,
    "timeout": 30000,
    "retryTimes": 3,
    "async": false
  },
  // 原DispatchDto中的重要字段
  "strategyExecId": "exec_001",
  "detailTableNo": "detail_001",
  "strategyGroupId": 2001,
  "strategyGroupName": "登录触达组",
  "strategyChannelId": 3001,
  "templateId": "SMS_001",
  "messageId": "msg_001",
  "dispatchType": "NORMAL",
  "signatureKey": "XYF",
  "batchNo": "batch_001",
  "app": "xyf-app",
  "innerApp": "xyf-cdp",
  "mobile": "13800138000",
  "bizType": "credit"
}
```

**参数说明**:
- `requestId`: 请求唯一标识，必填
- `touchType`: 触达类型，枚举值：REALTIME_NORMAL, REALTIME_ENGINE, OFFLINE_NORMAL, OFFLINE_ENGINE
- `channel`: 触达渠道，枚举值：SMS, VOICE, PUSH, COUPON
- `strategyId`: 策略ID，必填
- `userId`: 用户ID，必填
- `bizEventType`: 业务事件类型，可选
- `templateParams`: 模板参数，可选
- `touchConfig`: 触达配置，可选

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "requestId": "req_123456789",
    "status": "SUCCESS",
    "batchNo": "BATCH_1640995200000",
    "timestamp": 1640995200000
  }
}
```

### 2. 批量触达

**接口地址**: `POST /api/touch/batch-send`

**请求参数**: 触达请求数组
```json
[
  {
    "requestId": "req_123456789",
    "touchType": "OFFLINE_NORMAL",
    "channel": "SMS",
    "strategyId": 1001,
    "userId": 123456
  },
  {
    "requestId": "req_123456790",
    "touchType": "OFFLINE_NORMAL",
    "channel": "SMS",
    "strategyId": 1001,
    "userId": 123457
  }
]
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "requestId": "req_123456789",
      "status": "SUCCESS",
      "batchNo": "BATCH_1640995200001"
    },
    {
      "requestId": "req_123456790",
      "status": "FLOW_CONTROLLED",
      "errorCode": "FLOW_CONTROL",
      "errorMessage": "触达被流控拦截"
    }
  ]
}
```

### 3. 查询触达状态

**接口地址**: `GET /api/touch/status/{requestId}`

**路径参数**:
- `requestId`: 请求ID

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": "SUCCESS"
}
```

**状态枚举**:
- `PENDING`: 待处理
- `SUCCESS`: 成功
- `FAILED`: 失败
- `FLOW_CONTROLLED`: 流控拦截
- `PROCESSING`: 处理中
- `CANCELLED`: 已取消

### 4. 查询触达记录

**接口地址**: `GET /api/touch/records`

**查询参数**:
- `userId`: 用户ID，可选
- `strategyId`: 策略ID，可选
- `channel`: 触达渠道，可选
- `pageNum`: 页码，默认1
- `pageSize`: 页大小，默认20

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "pageNum": 1,
    "pageSize": 20,
    "total": 100,
    "pages": 5,
    "hasNext": true,
    "hasPrevious": false,
    "list": [
      {
        "id": 1,
        "requestId": "req_123456789",
        "batchNo": "BATCH_1640995200000",
        "touchType": "REALTIME_NORMAL",
        "channel": "SMS",
        "strategyId": 1001,
        "userId": 123456,
        "status": "SUCCESS",
        "sendTime": "2021-12-31T12:00:00",
        "createdTime": "2021-12-31T12:00:00"
      }
    ]
  }
}
```

### 5. 健康检查

**接口地址**: `GET /api/touch/health`

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": "OK"
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| -1 | 系统错误 |
| 1001 | 参数错误 |
| 1002 | 请求ID重复 |
| 2001 | 渠道不可用 |
| 2002 | 渠道调用失败 |
| 3001 | 频控拦截 |
| 3002 | 频控规则错误 |
| 4001 | 数据库错误 |
| 4002 | 缓存错误 |

## 渠道特定参数

### 短信渠道 (SMS)
```json
{
  "templateParams": {
    "mobile": "13800138000",
    "content": "短信内容",
    "templateId": "SMS_001"
  }
}
```

### 电销渠道 (VOICE)
```json
{
  "templateParams": {
    "mobile": "13800138000",
    "scriptId": "SCRIPT_001",
    "callbackUrl": "http://callback.example.com"
  }
}
```

### Push渠道 (PUSH)
```json
{
  "templateParams": {
    "title": "推送标题",
    "content": "推送内容",
    "extras": {
      "key1": "value1"
    }
  }
}
```

### 优惠券渠道 (COUPON)
```json
{
  "templateParams": {
    "couponType": "DISCOUNT",
    "amount": 10.00,
    "validDays": 30
  }
}
```

## 使用示例

### cURL示例

```bash
# 发送短信触达
curl -X POST http://localhost:8080/touch-service/api/touch/send \
  -H "Content-Type: application/json" \
  -d '{
    "requestId": "req_123456789",
    "touchType": "REALTIME_NORMAL",
    "channel": "SMS",
    "strategyId": 1001,
    "userId": 123456,
    "templateParams": {
      "mobile": "13800138000",
      "content": "您好，欢迎登录！"
    }
  }'

# 查询触达状态
curl -X GET http://localhost:8080/touch-service/api/touch/status/req_123456789
```

### Java示例

```java
// 使用RestTemplate
RestTemplate restTemplate = new RestTemplate();
TouchRequest request = new TouchRequest();
request.setRequestId("req_123456789");
request.setTouchType(TouchType.REALTIME_NORMAL);
request.setChannel(TouchChannel.SMS);
request.setStrategyId(1001L);
request.setUserId(123456L);

ApiResponse<TouchResponse> response = restTemplate.postForObject(
    "http://localhost:8080/touch-service/api/touch/send",
    request,
    ApiResponse.class
);
```

## 注意事项

1. **请求ID唯一性**: 每个请求必须有唯一的requestId，重复的requestId会被拒绝
2. **频控限制**: 系统会根据配置的频控规则对请求进行限制
3. **超时设置**: 建议设置合理的超时时间，避免长时间等待
4. **重试机制**: 对于失败的请求，建议实现指数退避的重试机制
5. **监控告警**: 建议监控触达成功率、延迟等关键指标
