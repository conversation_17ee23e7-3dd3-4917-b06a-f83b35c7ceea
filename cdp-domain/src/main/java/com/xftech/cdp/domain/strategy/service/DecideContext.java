/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.service;

import com.xftech.cdp.api.dto.req.DecideReq;
import com.xftech.cdp.api.dto.req.external.CallingSourceEnum;
import com.xftech.cdp.api.dto.resp.DecideResp;
import com.xftech.cdp.domain.strategy.model.enums.StrategyMarketChannelEnum;
import com.xftech.cdp.infra.rabbitmq.enums.DecisionResultEnum;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPackDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyGroupDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketEventConditionDo;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 *
 * <AUTHOR>
 * @version $ DecideContext, v 0.1 2024/7/27 11:10 benlin.wang Exp $
 */

@Data
public class DecideContext {
    private Long userId;
    private String app;
    private String innerApp;
    private StrategyDo strategyDo;
    private StrategyMarketChannelEnum channelEnum;
    private CallingSourceEnum callingSourceEnum;
    private StrategyGroupDo strategyGroup;
    private DecisionResultEnum decisionResult;
//    private List<Long> crowdIds;
    private String userParam;
    private String strategyLabelExpression;
    private CrowdPackDo hitCrowdPack;
    private CISUserInfo cisUserInfo;
    private Map<String, Object> labelValueMap;
    private Set<StrategyMarketEventConditionDo> marketEventConditionDoSet;
    private DecideReq decideReq;
    private DecideResp resp;
}