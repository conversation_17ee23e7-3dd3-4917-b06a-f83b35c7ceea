/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.param.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $ TempParamToBizEventFieldEnum, v 0.1 2024/4/24 16:35 lingang.han Exp $
 */

@Getter
@AllArgsConstructor
public enum TempParamToBizEventFieldEnum {

    ORDER_PAY_AMOUNT("order_pay_member_amount", "orderPayAmount"),
    vc_sign_order_price("vc_sign_order_price", "vcSignOrderPrice"),
    surprise_right_limit_amt_card_amount("surprise_right_limit_amt_card_amount", "surpriseRightLimitAmtCardAmount"),
    surprise_right_vip_fund("surprise_right_vip_fund", "surpriseRightVipFund"),
    distribute_current_available_total_quota("distribute_current_available_total_quota", "distributeCurrentAvailableTotalQuota"),
    ;

    /**
     * 短信模板中配置的参数
     */
    private final String tempParam;

    /**
     * bizEvent事件对象中的字段
     */
    private final String bizEventField;

    public static TempParamToBizEventFieldEnum getInstance(String tempParam) {
        for (TempParamToBizEventFieldEnum tempParamToBizEventFieldEnum : TempParamToBizEventFieldEnum.values()) {
            if (Objects.equals(tempParamToBizEventFieldEnum.getTempParam(), tempParam)) {
                return tempParamToBizEventFieldEnum;
            }
        }
        return null;
    }

    public static List<String> getAllTempParam(){
        return Arrays.stream(TempParamToBizEventFieldEnum.values()).map(TempParamToBizEventFieldEnum::getTempParam).collect(Collectors.toList());
    }
}