package com.xftech.cdp.infra.rabbitmq.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 规则命中结果
 */
@Data
public class HitResult {

    /**
     * 规则表达式
     */
    private String expression;
    /**
     * 规则表达式参数
     */
    private String expParam;
    /**
     * 命中结果，true命中，false未命中
     */
    private Boolean hit;

    /**
     * 执行时间
     */
    private LocalDateTime executeTime;

    public HitResult() {
        this.executeTime = LocalDateTime.now();
    }
}
