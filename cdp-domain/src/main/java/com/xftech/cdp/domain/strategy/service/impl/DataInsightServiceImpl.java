package com.xftech.cdp.domain.strategy.service.impl;

import java.util.List;
import java.util.Optional;

import javax.annotation.Resource;

import com.xftech.cdp.domain.strategy.exception.StrategyException;
import com.xftech.cdp.feign.DataInsightFeignClient;
import com.xftech.cdp.feign.common.BizBaseResponse;
import com.xftech.cdp.feign.model.datainsight.request.CrowPushResultRequest;
import com.xftech.cdp.feign.model.datainsight.response.CrowdPushResponse;
import com.xftech.cdp.feign.model.requset.DpsPageRequest;
import com.xftech.cdp.feign.model.response.DpsResponse;
import com.xftech.cdp.feign.model.response.MetaLabelDto;
import com.xftech.cdp.infra.client.ads.model.req.AdsCrowdReq;
import com.xftech.cdp.infra.client.ads.model.req.AdsCrowdTotalReq;
import com.xftech.cdp.infra.client.ads.model.resp.CrowdBaseResp;
import com.xftech.cdp.infra.client.ads.model.resp.CrowdPushResp;
import com.xftech.cdp.infra.client.ads.model.resp.CrowdTotalResp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;


@Service
@Slf4j
public class DataInsightServiceImpl {

    @Resource
    private DataInsightFeignClient dataInsightFeignClient;

    /**
     * 获取标签平台元数据
     *
     * @param request
     * @return
     */
    public List<MetaLabelDto> getMetaLabel(DpsPageRequest request) {
        if (request == null) {
            throw new StrategyException("request do not null");
        }
        DpsResponse<List<MetaLabelDto>> response = dataInsightFeignClient.queryLabelList(request);
        log.info("标签平台-元数据同步接口 req={}, response={}", JSONObject.toJSONString(request), JSONObject.toJSONString(response));

        if (response == null || !response.isSuccess()) {
            throw new StrategyException("标签平台-元数据同步接口 调用失败");
        }

        return response.getData();
    }

    /**
     * 人群推送
     *
     * @param request
     * @return
     */
    public CrowdBaseResp<List<CrowdPushResp>> pushCrowd(AdsCrowdReq request) {
        CrowdBaseResp<List<CrowdPushResp>> crowdBaseResp = new CrowdBaseResp<>();
        crowdBaseResp.setSuccess(false);
        crowdBaseResp.setPayload(Lists.newArrayList());
        if (request == null || CollectionUtils.isEmpty(request.getData())) {
            return crowdBaseResp;
        }

        try {
            BizBaseResponse<CrowdPushResponse> response = dataInsightFeignClient.pushCrowd(request);
            log.info("标签平台-人群推送接口 request={}, response={}", JSONObject.toJSONString(request), JSONObject.toJSONString(response));

            if (BizBaseResponse.successData(response)) {
                crowdBaseResp.setSuccess(true);
                List<CrowdPushResponse.CrowdPushItemResult> items = Optional.ofNullable(response.getData().getTaskResults()).orElse(Lists.newArrayList());
                for (CrowdPushResponse.CrowdPushItemResult item : items) {
                    CrowdPushResp crowdPushResp = new CrowdPushResp();
                    crowdPushResp.setCrowdId(item.getCrowdId());
                    crowdPushResp.setStatus(item.getCrowdStatus());

                    crowdBaseResp.getPayload().add(crowdPushResp);
                }
            }
        } catch (Exception e) {
            log.error("标签平台-人群推送接口 error", e);
        }

        log.info("标签平台-人群推送接口 crowdBaseResp={}", JSON.toJSONString(crowdBaseResp));
        return crowdBaseResp;
    }

    /**
     * 人群推送结果查询
     *
     * @param adsCrowdTotalReq
     * @return
     */
    public CrowdBaseResp<List<CrowdTotalResp>> getCrowdTotal(AdsCrowdTotalReq adsCrowdTotalReq) {
        CrowdBaseResp<List<CrowdTotalResp>> crowdBaseResp = new CrowdBaseResp<>();
        crowdBaseResp.setSuccess(false);
        crowdBaseResp.setPayload(Lists.newArrayList());
        if (adsCrowdTotalReq == null || CollectionUtils.isEmpty(adsCrowdTotalReq.getData())) {
            return crowdBaseResp;
        }

        try {
            List<Long> crowdIds = Lists.newArrayList();
            adsCrowdTotalReq.getData().forEach(x -> crowdIds.add(x.getCrowdId()));

            CrowPushResultRequest request = new CrowPushResultRequest(crowdIds);
            BizBaseResponse<List<CrowdTotalResp>> response = dataInsightFeignClient.getCrowdTotal(request);
            log.info("标签平台-人群推送结果查询接口 request={}, response={}", JSONObject.toJSONString(request), JSONObject.toJSONString(response));

            if (BizBaseResponse.successData(response)) {
                crowdBaseResp.setSuccess(true);
                for (CrowdTotalResp item : response.getData()) {
                    CrowdTotalResp crowdTotalResp = new CrowdTotalResp();
                    crowdTotalResp.setCrowdId(item.getCrowdId());
                    crowdTotalResp.setStatus(item.getStatus());
                    crowdTotalResp.setTotal(item.getTotal());

                    crowdBaseResp.getPayload().add(crowdTotalResp);
                }
            }
        } catch (Exception e) {
            log.error("标签平台-人群推送结果查询接口 error", e);
        }

        log.info("标签平台-人群推送结果查询接口 crowdBaseResp={}", JSON.toJSONString(crowdBaseResp));
        return crowdBaseResp;
    }

}