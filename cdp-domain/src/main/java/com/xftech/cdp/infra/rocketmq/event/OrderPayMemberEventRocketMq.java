/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.rocketmq.event;

import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSONObject;
import com.xftech.cdp.domain.strategy.service.dispatch.event.StrategyEventDispatchService;
import com.xftech.cdp.infra.aviator.util.DateTimeUtil;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventMessageVO;
import com.xftech.cdp.infra.rocketmq.dto.OrderPayResultMsgDTO;
import com.xftech.cdp.infra.utils.RedisUtils;
import com.xinfei.xfframework.common.starter.mq.MqConsumerListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * 会员卡扣费事件
 * @version $ OrderPayMemberEventRocketMq, v 0.1 2024/4/24 10:38 lingang.han Exp $
 */

@Component
@RocketMQMessageListener(consumerGroup = "cg_xyf_cdp_tp_batchdeduct_order_pay_result_notify", topic = "tp_batchdeduct_order_pay_result_notify", consumeMode = ConsumeMode.CONCURRENTLY)
@Slf4j
public class OrderPayMemberEventRocketMq extends MqConsumerListener<String> {

    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private StrategyEventDispatchService strategyEventDispatchService;

    @Override
    protected void doMessage(String s, String s1, MessageExt messageExt) {
        log.info("orderPayMemberEventRocketMq receive message topic={},messageId={},body={}", s, messageExt.getMsgId(), s1);
        String lockKey = null;
        if (StringUtils.isNotBlank(messageExt.getMsgId())) {
            lockKey = SecureUtil.md5("cg:extractQuotaCardGuide:" + messageExt.getMsgId());
        }
        try {
            try {
                if (StringUtils.isNotBlank(lockKey) && !redisUtils.lock(lockKey, "0", 5, TimeUnit.MINUTES)) {
                    log.warn("orderPayMemberEventRocketMq 重复消息,messageId={},body={}", messageExt.getMsgId(), s1);
                    return;
                }
            } catch (Exception e) {
                log.error("orderPayMemberEventRocketMq,处理异常", e);
            }
            OrderPayResultMsgDTO messageVO = JSONObject.parseObject(s1, OrderPayResultMsgDTO.class);

            //一定条件下丢弃消息
            if (StringUtils.isBlank(messageVO.getPayBizOrderNo()) || Objects.isNull(messageVO.getCurrentDeductNumber()) || Objects.isNull(messageVO.getFirstDeductExecutionTime())) {
                log.info("orderPayMemberEventRocketMq 无效消息 discard messages,messageId={},body={}", messageExt.getMsgId(), s1);
                return;
            }

            BizEventMessageVO bizEventMessageVO = transform(messageVO);
            bizEventMessageVO.setBizEventType("OrderPayMember");
            strategyEventDispatchService.prescreen(messageExt.getMsgId(), bizEventMessageVO);

        } catch (Exception e) {
            if (StringUtils.isNotEmpty(lockKey)) {
                redisUtils.unlock(lockKey);
            }
            log.error("orderPayMemberEventRocketMq consumer error, messageid={},body={}", messageExt.getMsgId(), s1, e);
        }
    }

    private BizEventMessageVO transform(OrderPayResultMsgDTO orderPayResultMsgDTO) {
        BizEventMessageVO bizEventMessageVO = new BizEventMessageVO();
        bizEventMessageVO.setAppUserId(Long.parseLong(orderPayResultMsgDTO.getUserNo()));
        bizEventMessageVO.setApp(orderPayResultMsgDTO.getApp());
        bizEventMessageVO.setInnerApp(orderPayResultMsgDTO.getInnerApp());
        bizEventMessageVO.setTriggerDatetime(DateTimeUtil.formatDateToStr(orderPayResultMsgDTO.getEventTime(), null));
        BizEventMessageVO.ExtrData extrData = new BizEventMessageVO.ExtrData();
        extrData.setOrderPayStatus(orderPayResultMsgDTO.getStatus());
        extrData.setCurrentDeductNumber(orderPayResultMsgDTO.getCurrentDeductNumber());
        extrData.setCompleteTime(orderPayResultMsgDTO.getCompleteTime());
        extrData.setFirstDeductExecutionTime(orderPayResultMsgDTO.getFirstDeductExecutionTime());
        extrData.setOrderPayAmount(new BigDecimal(orderPayResultMsgDTO.getAmount()));
        bizEventMessageVO.setExtrData(extrData);
        return bizEventMessageVO;
    }
}