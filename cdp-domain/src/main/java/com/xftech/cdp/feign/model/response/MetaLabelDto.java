/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.feign.model.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @version $ MetaLabelDto, v 0.1 2024/6/18 18:38 lingang.han Exp $
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MetaLabelDto implements Serializable {
    private static final long serialVersionUID = -56450939465260535L;

    private Long id;
    private String labelName;
    private String labelCode;
    private String dataType;
    private String aggDimension;
    private String srcTable;
    private String srcField;
    private Integer state;
    private List<Map<String,Object>> enumValues;
    private String createdTime;
    private String updatedTime;

}