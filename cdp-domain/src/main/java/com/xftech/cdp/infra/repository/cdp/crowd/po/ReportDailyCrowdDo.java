package com.xftech.cdp.infra.repository.cdp.crowd.po;

import com.xftech.cdp.domain.crowd.model.enums.CrowdExecResultEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReportDailyCrowdDo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     *
     * pk
     */
    private Long id;

    /**
     *
     * 统计日期
     */
    private Date date;

    /**
     *
     * 人群id
     */
    private Long crowdId;

    /**
     *
     * 人群名称
     */
    private String crowdName;

    /**
     *
     * 执行开始时间
     */
    private LocalDateTime execStartTime;

    /**
     *
     * 执行结束时间
     */
    private LocalDateTime execEndTime;

    /**
     *
     * 执行结果
     */
    private Integer execStatus;

    /**
     *
     * 失败原因
     */
    private String failReason;

    /**
     *
     * 人群包今日人数
     */
    private Integer crowdTodaySum;

    /**
     *
     * 人群包昨日人数
     */
    private Integer crowdYesterdaySum;

    /**
     *
     * 创建时间
     */
    private Date createdTime;

    /**
     *
     * 修改时间
     */
    private Date updatedTime;

    /**
     *
     * 逻辑删除
     */
    private Integer dFlag;

    /**
     *
     * 创建人
     */
    private String createdOp;

    /**
     *
     * 修改人
     */
    private String updatedOp;

    public int getStatusSort(){
        return CrowdExecResultEnum.getEnumSort(this.execStatus);
    }
}