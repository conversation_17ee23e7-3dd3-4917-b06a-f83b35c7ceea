<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="crowdExecLog">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdExecLogDo">
        <id column="id" property="id"/>
        <result column="crowd_id" property="crowdId"/>
        <result column="exec_time" property="execTime"/>
        <result column="finish_exec_time" property="finishExecTime"/>
        <result column="exec_result" property="execResult"/>
        <result column="exec_type" property="execType"/>
        <result column="fail_reason" property="failReason"/>
        <result column="exec_man" property="execMan"/>
        <result column="crowd_person_num" property="crowdPersonNum"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="d_flag" property="dFlag"/>
        <result column="retry_job_id" property="retryJobId"/>
        <result column="created_op" property="createdOp"/>
        <result column="updated_op" property="updatedOp"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, crowd_id, exec_time, finish_exec_time, exec_result, exec_type, fail_reason, exec_man, crowd_person_num,created_time, updated_time, d_flag, created_op, updated_op,retry_job_id
    </sql>

    <select id="findTodayExecLogsByCrowdIdAndStatus" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crowd_exec_log where crowd_id = #{crowdId} and d_flag = 0 and exec_result = #{execResult} and created_time
        &gt; #{createdTime}
    </select>

    <select id="selectCrowdMaxExecLogIds" resultType="map">
        select distinct crowd_id, max(id) id from crowd_exec_log where d_flag = 0
        and crowd_id in
        <foreach collection="crowdIds" index="index" item="crowdId"
                 separator="," open="(" close=")">
            #{crowdId}
        </foreach>
        group by crowd_id
    </select>

    <select id="selectCrowdMaxExecLogId" resultType="java.lang.Long">
        select max(id)
        from crowd_exec_log
        where d_flag = 0
          and crowd_id = #{crowdId}
    </select>

    <select id="selectCrowdMaxExecLogIdMap" resultType="map">
        select crowd_id, max(id) id
        from crowd_exec_log
        where d_flag = 0
          and exec_result = 1
          and crowd_id = #{crowdId}
    </select>

    <select id="selectCrowdMaxExecLogTimeMap" resultType="map">
        select crowd_id, max(finish_exec_time) finish_exec_time
        from crowd_exec_log
        where d_flag = 0
          and exec_result = 1
          and crowd_id = #{crowdId}
    </select>

    <select id="selectExecLogIdsByCrowdIds" resultType="java.lang.Long">
        select distinct id from crowd_exec_log where d_flag = 0
        and crowd_id in
        <foreach collection="crowdIds" index="index" item="crowdId"
                 separator="," open="(" close=")">
            #{crowdId}
        </foreach>
        and created_time &lt;= #{localDateTime}
    </select>

    <select id="selectMaxRefreshSuccessTimeByCrowdIds" resultType="java.time.LocalDateTime">
        select Max(updated_time)
        from crowd_exec_log
        where d_flag = 0
        and exec_result = 1
        and date(created_time) &lt;= #{date}
        and crowd_id in
        <foreach collection="crowdIds" index="index" item="crowd_id" open="(" separator="," close=")">
            #{crowd_id}
        </foreach>
    </select>

    <select id="countCrowdPackUserNum" resultType="integer">
        SELECT ifnull(sum(t1.crowd_person_num), 0)
        FROM crowd_exec_log t1
        INNER JOIN (SELECT MAX(id) AS id
        FROM crowd_exec_log
        WHERE d_flag = 0
        and exec_result = 1
        and date(created_time) &lt;= #{date}
        and crowd_id in
        <foreach collection="crowdIds" index="index" item="crowd_id" open="(" separator="," close=")">
            #{crowd_id}
        </foreach>
        GROUP BY crowd_id) t2 ON t1.id = t2.id
    </select>


    <select id="selectExecLogByCrowdIdsAndTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crowd_exec_log
        where d_flag = 0
        <if test="crowdIds !=null and crowdIds.size()>0">
            and crowd_id in
            <foreach collection="crowdIds" item="crowdId" open="(" close=")" separator=",">
                #{crowdId}
            </foreach>
        </if>
        and exec_time &gt;= #{startTime}
        and exec_time &lt;= #{endTime}
    </select>

    <select id="existLogByCrowdAndExecTime" resultType="integer">
        select count(*)
        from crowd_exec_log
        where d_flag = 0
        and crowd_id = #{crowdId}
        and exec_type = #{execType}
        and exec_time = #{execTime}
    </select>

    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdExecLogDo" useGeneratedKeys="true">
        insert into crowd_exec_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="crowdId != null">
                crowd_id,
            </if>
            <if test="execTime != null">
                exec_time,
            </if>
            <if test="finishExecTime != null">
                finish_exec_time,
            </if>
            <if test="execResult != null">
                exec_result,
            </if>
            <if test="execType != null">
                exec_type,
            </if>
            <if test="execMan != null">
                exec_man,
            </if>
            <if test="retryJobId != null">
                retry_job_id,
            </if>
            <if test="crowdPersonNum != null">
                crowd_person_num,
            </if>
            <if test="createdOp != null">
                created_op,
            </if>
            <if test="updatedOp != null">
                updated_op,
            </if>
            created_time,
            updated_time,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="crowdId != null">
                #{crowdId,jdbcType=BIGINT},
            </if>
            <if test="execTime != null">
                #{execTime,jdbcType=TIMESTAMP},
            </if>
            <if test="finishExecTime != null">
                #{finishExecTime,jdbcType=TIMESTAMP},
            </if>
            <if test="execResult != null">
                #{execResult},
            </if>
            <if test="execType != null">
                #{execType},
            </if>
            <if test="execMan != null">
                #{execMan,jdbcType=VARCHAR},
            </if>
            <if test="retryJobId != null">
                #{retryJobId,jdbcType=INTEGER},
            </if>
            <if test="crowdPersonNum != null">
                #{crowdPersonNum,jdbcType=INTEGER},
            </if>
            <if test="createdOp != null">
                #{createdOp,jdbcType=VARCHAR},
            </if>
            <if test="updatedOp != null">
                #{updatedOp,jdbcType=VARCHAR},
            </if>
            now(),
            now(),
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdExecLogDo">
        update crowd_exec_log
        <set>
            <if test="crowdId != null">
                crowd_id = #{crowdId,jdbcType=BIGINT},
            </if>
            <if test="execTime != null">
                exec_time = #{execTime,jdbcType=TIMESTAMP},
            </if>
            <if test="finishExecTime != null">
                finish_exec_time = #{finishExecTime,jdbcType=TIMESTAMP},
            </if>
            <if test="execResult != null">
                exec_result = #{execResult},
            </if>
            <if test="execType != null">
                exec_type = #{execType},
            </if>
            <if test="failReason != null">
                fail_reason = #{failReason},
            </if>
            <if test="execMan != null">
                exec_man = #{execMan,jdbcType=VARCHAR},
            </if>
            <if test="retryJobId != null">
                retry_job_id = #{retryJobId,jdbcType=INTEGER},
            </if>
            <if test="crowdPersonNum != null">
                crowd_person_num = #{crowdPersonNum,jdbcType=INTEGER},
            </if>
            <if test="updatedOp != null">
                updated_op = #{updatedOp,jdbcType=VARCHAR},
            </if>
            updated_time = now()
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByCrowdAndExecTime"
            parameterType="com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdExecLogDo">
        update crowd_exec_log
        <set>
            <if test="crowdId != null">
                crowd_id = #{crowdId,jdbcType=BIGINT},
            </if>
            <if test="execTime != null">
                exec_time = #{execTime,jdbcType=TIMESTAMP},
            </if>
            <if test="finishExecTime != null">
                finish_exec_time = #{finishExecTime,jdbcType=TIMESTAMP},
            </if>
            <if test="execResult != null">
                exec_result = #{execResult},
            </if>
            <if test="execType != null">
                exec_type = #{execType},
            </if>
            <if test="failReason != null">
                fail_reason = #{failReason},
            </if>
            <if test="execMan != null">
                exec_man = #{execMan,jdbcType=VARCHAR},
            </if>
            <if test="retryJobId != null">
                retry_job_id = #{retryJobId,jdbcType=INTEGER},
            </if>
            <if test="crowdPersonNum != null">
                crowd_person_num = #{crowdPersonNum,jdbcType=INTEGER},
            </if>
            updated_time = now()
        </set>
        where d_flag = 0
        and crowd_id = #{crowdId}
        and exec_type = #{execType}
        and exec_time = #{execTime}
    </update>
</mapper>
