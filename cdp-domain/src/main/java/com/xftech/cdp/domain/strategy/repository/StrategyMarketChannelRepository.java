package com.xftech.cdp.domain.strategy.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketChannelDo;
import com.xftech.cdp.infra.utils.RedisKeyUtils;
import com.xftech.cdp.infra.utils.RedisUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 策略营销渠道关系表操作
 *
 * <AUTHOR>
 * @since 2023/2/21
 */

@Component
public class StrategyMarketChannelRepository {

    @Autowired
    private RedisUtils redisUtils;

    /**
     * 根据策略id删除营销渠道记录
     *
     * @param strategyId 策略id
     */
    public void deleteByStrategyId(Long strategyId) {
        DBUtil.delete("strategyMarketChannel.deleteByStrategyId", strategyId);
    }

    /**
     * 根据策略id查询营销渠道记录
     *
     * @param strategyId 策略id
     * @return 该策略下的所有渠道列表
     */
    public List<StrategyMarketChannelDo> selectByStrategyId(Long strategyId) {
        return DBUtil.selectList("strategyMarketChannel.selectByStrategyId", strategyId);
    }

    /**
     * 根据策略id查询营销渠道记录
     *
     * @param list 策略id列表
     * @return 该策略下的所有渠道列表
     */
    public List<StrategyMarketChannelDo> selectByStrategyIdList(List<Long> list) {
        return DBUtil.selectList("strategyMarketChannel.selectByStrategyIdList", list);
    }

    /**
     * 根据策略分组id查询营销渠道记录
     *
     * @param strategyGroupId 策略分组id
     * @return 该策略某个分组下的所有营销渠道列表
     */
    public List<StrategyMarketChannelDo> selectByStrategyGroupId(Long strategyGroupId) {
        return DBUtil.selectList("strategyMarketChannel.selectByStrategyGroupId", strategyGroupId);

    }

    public List<StrategyMarketChannelDo> selectAllByStrategyGroupId(Long strategyGroupId) {
        return DBUtil.selectList("strategyMarketChannel.selectAllByStrategyGroupId", strategyGroupId);
    }

    /**
     * 根据策略id、营销渠道类型查询记录
     *
     * @param strategyId        策略id
     * @param marketChannelList 营销渠道类型列表（0: 不营销，1:短信，2:电销，3:优惠券）
     * @return 该策略下营销渠道类型在[marketChannelList]里的记录
     */
    public List<StrategyMarketChannelDo> selectByStrategyIdAndChannel(Long strategyId, List<Integer> marketChannelList) {
        Map<String, Object> param = new HashMap<>();
        param.put("strategyId", strategyId);
        param.put("marketChannelList", marketChannelList);
        return DBUtil.selectList("strategyMarketChannel.selectByStrategyIdAndChannel", param);

    }

    /**
     * 根据渠道id(主键)查询某一条记录
     *
     * @param channelId 渠道id
     * @return 该id对应的一条记录
     */
    public StrategyMarketChannelDo selectById(Long channelId) {
        if (channelId == null || channelId <= 0){
            return null;
        }
        return DBUtil.selectOne("strategyMarketChannel.selectByPrimaryKey", channelId);
    }

    public StrategyMarketChannelDo cacheSelectById(Long channelId) {
        String key = RedisKeyUtils.genStgyChnl(channelId);
        StrategyMarketChannelDo strategyMarketChannelDo = redisUtils.get(key, StrategyMarketChannelDo.class);
        if (strategyMarketChannelDo == null) {
            strategyMarketChannelDo = selectById(channelId);
            if (strategyMarketChannelDo != null) {
                redisUtils.set(key, strategyMarketChannelDo, RedisUtils.DEFAULT_EXPIRE_SECONDS);
            }
        }
        return strategyMarketChannelDo;
    }

    /**
     * 插入一条营销渠道记录
     *
     * @param strategyMarketChannelDo 营销渠道对象
     */
    public void insert(StrategyMarketChannelDo strategyMarketChannelDo) {
        DBUtil.insert("strategyMarketChannel.insertSelective", strategyMarketChannelDo);
    }

    /**
     * 根据渠道id(主键)更新某一条记录
     *
     * @param strategyMarketChannelDo 营销渠道对象
     */
    public void updateById(StrategyMarketChannelDo strategyMarketChannelDo) {
        DBUtil.update("strategyMarketChannel.updateByPrimaryKeySelective", strategyMarketChannelDo);
    }

    /**
     * 根据id批量删除营销渠道记录
     *
     * @param channelIdList 渠道id列表
     */
    public void deleteByIdBatch(List<Long> channelIdList) {
        DBUtil.deleteBatchWithoutTx("strategyMarketChannel.deleteByPrimaryKey", channelIdList);
    }

    public List<StrategyMarketChannelDo> selectByIds(List<Long> channelIdList) {
        return DBUtil.selectList("strategyMarketChannel.selectByIds", channelIdList);
    }

    public StrategyMarketChannelDo selectByStrategyGroupIdAndChannel(Long strategyGroupId, Integer marketChannel) {
        Map<String, Object> param = new HashMap<>();
        param.put("strategyGroupId", strategyGroupId);
        param.put("marketChannel", marketChannel);
        return DBUtil.selectOne("strategyMarketChannel.selectByStrategyGroupIdAndChannel", param);
    }

    public List<StrategyMarketChannelDo> selectStrategyIdsWithAppBannerChannel(Integer marketChannel) {
        Map<String, Object> param = new HashMap<>();
        param.put("marketChannel", marketChannel);
        return DBUtil.selectList("strategyMarketChannel.selectStrategyIdsWithAppBannerChannel", param);
    }
}
