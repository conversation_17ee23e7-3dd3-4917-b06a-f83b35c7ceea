package com.xftech.cdp.domain.strategy.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.infra.repository.cdp.strategy.po.EventMetaDataDo;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @<NAME_EMAIL>
 */
@Component
public class EventMetaDataRepository {

    public List<EventMetaDataDo> selectByBusinessType(String businessType) {
        return DBUtil.selectList("eventMetadataMapper.selectByBusinessType",businessType);
    }

    public List<EventMetaDataDo> selectCacheInfo() {
        return DBUtil.selectList("eventMetadataMapper.selectCacheInfo",null);
    }

    public EventMetaDataDo selectByEventName(String eventName) {
        return DBUtil.selectOne("eventMetadataMapper.selectByEventName",eventName);
    }
}
