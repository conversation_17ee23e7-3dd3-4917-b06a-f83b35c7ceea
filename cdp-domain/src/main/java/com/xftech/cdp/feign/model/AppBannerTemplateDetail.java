package com.xftech.cdp.feign.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @version $ AppBannerTemplateResp, v 0.1 2025/5/28 19:29 tianshuo.qiu Exp $
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AppBannerTemplateDetail  {

    @ApiModelProperty(value = "业务归属")
    private String business;

    @ApiModelProperty(value = "弹窗id")
    private String id;

    @ApiModelProperty(value = "弹框状态")
    private String status;

    @ApiModelProperty(value = "展示app")
    private String app;

    @ApiModelProperty(value = "弹框名称")
    private String name;

    @ApiModelProperty(value = "样式类型")
    private String style;
}