/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.rocketmq.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @version $ AiCallBackCallDetail, v 0.1 2024/8/26 11:01 lingang.han Exp $
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AiCallBackCallDetail implements Serializable {

    private static final long serialVersionUID = -3860333922409105773L;

    /**
     * 供应商拨打任务唯一标识
     */
    private String operationId;

    private Long userNo;
    private String app;

    /**
     * 呼叫id
     */
    private String call;
    /**
     * 外呼状态
     *  成功：1（已接听）
     *  失败：-1
     */
    private String callStatus;

    /**
     * 外呼结果码
     */
    private String callResult;

    /**
     * 外呼结果描述
     */
    private String callResultDesc;

    /**
     * 语音对话内容，或短信发送内容等
     */
    private String callContent;

    /**
     * 通话时长
     * 单位：毫秒
     */
    private Integer duration;

    /**
     * 振铃时长
     * 单位：毫秒
     */
    private Integer ringDuration;

    /**
     * 录音下载地址
     */
    private String recordingUrl;

    /**
     * 开始呼叫时间
     * 短信类型回调为短信发送时间
     * yyyy-MM-dd HH:mm:ss
     */
    private String beginTime;

    /**
     * 振铃时间
     * yyyy-MM-dd HH:mm:ss
     */
    private String ringTime;

    /**
     * 接听时间
     * yyyy-MM-dd HH:mm:ss
     */
    private String answerTime;

    /**
     * 挂机时间
     * yyyy-MM-dd HH:mm:ss
     */
    private String hangupTime;

}