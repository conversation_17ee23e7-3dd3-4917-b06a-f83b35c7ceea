# T0实时触达MQ消费者完整清单

## 1. 概述

本文档详细列出了T0实时触达系统中所有的MQ消费者，包括RocketMQ、RabbitMQ等消息队列的消费者配置和业务用途。

## 2. RocketMQ消费者（主要）

### 2.1 业务事件类消费者

| 序号 | 消费者类名 | Topic | Consumer Group | Tag/Selector | 业务事件类型 | 开关控制 |
|------|------------|-------|----------------|--------------|-------------|----------|
| 1 | `LifeRightsRocketMqConsumer` | `tp_xyf_cdp_notify` | `cg_xyf_cdp_notify_tg_liferights` | `tg_liferights` | 生活权益事件 | 无 |
| 2 | `XDayInterestFreeRocketMqConsumer` | `tp_xyf_cdp_notify` | `cg_xyf_cdp_notify_tg_xDayInterestFree` | `tg_xDayInterestFree` | X天免息事件 | 无 |
| 3 | `IncreaseAmtRocketMqConsumer` | `tp_xyf_cdp_notify` | `cg_xyf_cdp_notify_increaseamt` | `tg_increaseamt` | 提额事件 | 无 |
| 4 | `ExtractQuotaCardGuideEventRocketMq` | `tp_rcspt_risk_pre_amount_change_message` | `cg_xyf_cdp_tp_rcspt_risk_pre_amount_change_message` | 无 | 提额卡引导事件 | 无 |
| 5 | `OrderPayMemberEventRocketMq` | `tp_order_pay_member_event` | `cg_xyf_cdp_tp_order_pay_member_event` | 无 | 订单支付会员事件 | 无 |
| 6 | `UserLogOffEventRocketMq` | `tp_user_logoff_event` | `cg_xyf_cdp_tp_user_logoff_event` | 无 | 用户注销事件 | 无 |
| 7 | `VipCardSignEventRocketMq` | `tp_vip_card_sign_event` | `cg_xyf_cdp_tp_vip_card_sign_event` | 无 | VIP卡签约事件 | 无 |

### 2.2 风险调额类消费者

| 序号 | 消费者类名 | Topic | Consumer Group | Tag/Selector | 业务事件类型 | 开关控制 |
|------|------------|-------|----------------|--------------|-------------|----------|
| 8 | `NewRiskSingleAdjBusinessMsgConsumer` | `tp_risk_single_adj_business` | `cg_risk_single_adj_business_xyf_cdp` | 无 | 风险单笔调额-商户 | `NewRiskSingleAdjBusinessMsgConsumerEnable` |
| 9 | `NewRiskSingleAdjCarMsgConsumer` | `tp_risk_single_adj_car` | `cg_risk_single_adj_car_xyf_cdp` | 无 | 风险单笔调额-车贷 | `NewRiskSingleAdjCarMsgConsumerEnable` |
| 10 | `NewRiskSingleAdjSesameMsgConsumer` | `tp_risk_single_adj_sesame` | `cg_risk_single_adj_sesame_xyf_cdp` | 无 | 风险单笔调额-芝麻 | `NewRiskSingleAdjSesameMsgConsumerEnable` |
| 11 | `NewRiskTempCreditMsgConsumer` | `tp_risk_temp_credit` | `cg_risk_temp_credit_xyf_cdp` | 无 | 风险临时额度 | `NewRiskTempCreditMsgConsumerEnable` |

### 2.3 用户行为类消费者

| 序号 | 消费者类名 | Topic | Consumer Group | Tag/Selector | 业务事件类型 | 开关控制 |
|------|------------|-------|----------------|--------------|-------------|----------|
| 12 | `NewRegisterMsgConsumer` | `tp_user_register` | `cg_user_register_xyf_cdp` | 无 | 用户注册事件 | `NewRegisterMsgConsumerEnable` |
| 13 | `NewTrackingCardClickMsgConsumer` | `tp_tracking_card_click` | `cg_tracking_card_click_xyf_cdp` | 无 | 卡片点击事件 | `NewTrackingCardClickMsgConsumerEnable` |
| 14 | `NewTrackingLoanApplyViewMsgConsumer` | `tp_tracking_loan_apply_view` | `cg_tracking_loan_apply_view_xyf_cdp` | 无 | 借款申请页面浏览 | `NewTrackingLoanApplyViewMsgConsumerEnable` |

### 2.4 业务报告类消费者

| 序号 | 消费者类名 | Topic | Consumer Group | Tag/Selector | 业务事件类型 | 开关控制 |
|------|------------|-------|----------------|--------------|-------------|----------|
| 15 | `Login1RocketMsgConsumer` | `tp_biz_report_Login1` | `cg_xyf_cdp_tp_biz_report_Login1` | 无 | 登录事件上报 | `Login1RocketMsgConsumerEnable` |
| 16 | `Start1RocketMsgConsumer` | `tp_biz_report_Start1` | `cg_xyf_cdp_tp_biz_report_Start1` | 无 | 启动事件上报 | `Start1RocketMsgConsumerEnable` |

### 2.5 风控相关消费者

| 序号 | 消费者类名 | Topic | Consumer Group | Tag/Selector | 业务事件类型 | 开关控制 |
|------|------------|-------|----------------|--------------|-------------|----------|
| 17 | `AppLoginIncreaseCreditRocketMqConsumer` | `tp_rcspt_risk_amount_change_message` | `cg_xyf_cdp_tp_rcspt_risk_amount_change_message_tg_personal_api_fst_login_temp` | `tg_personal_api_fst_login_temp` | APP登录提额 | 无 |
| 18 | `CxhRepayXyf01IncreaseCreditRocketMqConsumer` | `tp_rcspt_risk_amount_change_message` | `cg_xyf_cdp_tp_rcspt_risk_amount_change_message_tg_cxh_repay_xyf01_increase_amount` | `tg_cxh_repay_xyf01_increase_amount` | 存小花还款提额 | 无 |
| 19 | `RcsptIncreaseCreditRocketMqConsumer` | `tp_rcspt_risk_amount_change_message` | `cg_xyf_cdp_tp_rcspt_risk_amount_change_message` | 无 | 风控提额通用 | 无 |
| 20 | `AccessControlDiversionMsgConsumer` | `tp_rcspt_risk_access_control_message` | `cg_xyf_cdp_tp_rcspt_risk_access_control_message` | 无 | 风控禁申分流 | 无 |

### 2.6 还款相关消费者

| 序号 | 消费者类名 | Topic | Consumer Group | Tag/Selector | 业务事件类型 | 开关控制 |
|------|------------|-------|----------------|--------------|-------------|----------|
| 21 | `RepaySuccessMsgConsumer` | `tp_repaytrade_repayResult` | `cg_xyf_cdp_tp_repaytrade_repayResult_repaySuccess` | `repaySuccess` | 还款成功事件 | 无 |
| 22 | `SettleSuccessMsgConsumer` | `tp_repaytrade_repayResult` | `cg_xyf_cdp_tp_repaytrade_repayResult_settleSuccess` | `settleSuccess` | 结清成功事件 | 无 |
| 23 | `LendtradeDerateRocketMqConsumer` | `tp_lendtrade_derating` | `cg_xyf_cdp_tp_lendtrade_derating` | 无 | 借贷减免事件 | 无 |
| 24 | `LendTradeOrderFinalStatusRocketMqConsumer` | `tp_lendtrade_order_final_status` | `cg_xyf_cdp_tp_lendtrade_order_final_status` | 无 | 借贷订单最终状态 | 无 |

### 2.7 其他业务消费者

| 序号 | 消费者类名 | Topic | Consumer Group | Tag/Selector | 业务事件类型 | 开关控制 |
|------|------------|-------|----------------|--------------|-------------|----------|
| 25 | `AdvanceOrderRocketMsgConsumer` | `tp_psenginecore_advance_order_create` | `cg_xyf_cdp_tp_psenginecore_advance_order_create` | 无 | 预借款提交成功 | 无 |
| 26 | `CreditApplyMsgConsumer` | `tp_apiopfcore_credit_result` | `cg_xyf_cdp_tp_apiopfcore_credit_result` | 无 | API进件结果 | 无 |
| 27 | `DistributionRocketMsgConsumer` | `tp_distributecore_approval_success` | `cg_xyf_cdp_tp_distributecore_approval_success` | 无 | 分发授信成功 | 无 |
| 28 | `CouponCallbackRocketMsgConsumer` | `tp_coupon_callback` | `cg_xyf_cdp_tp_coupon_callback` | 无 | 优惠券回调 | 无 |

### 2.8 API判断类消费者

| 序号 | 消费者类名 | Topic | Consumer Group | Tag/Selector | 业务事件类型 | 开关控制 |
|------|------------|-------|----------------|--------------|-------------|----------|
| 29 | `ApiCredentialStuffingMsgConsumer` | `tp_apiopfcore_api_judge_all` | `cg_xyf_cdp_tp_apiopfcore_api_judge_all` | 无 | API撞库事件 | 无 |
| 30 | `ApiEntryMsgConsumer` | `tp_ugchanprod_api_judge_engine` | `cg_xyf_cdp_tp_ugchanprod_api_judge_engine` | 无 | API进件判断 | 无 |
| 31 | `TpApiJudgeRocketMqConsumer` | `tp_api_judge` | `cg_xyf_cdp_tp_api_judge` | 无 | API判断通用 | 无 |

### 2.9 回调类消费者

| 序号 | 消费者类名 | Topic | Consumer Group | Tag/Selector | 业务事件类型 | 开关控制 |
|------|------------|-------|----------------|--------------|-------------|----------|
| 32 | `TeleCallRocketMqConsumer` | `tp_telemkt_name_import_result` | `cg_xyf_cdp_tp_telemkt_name_import_result` | 无 | 电销回调结果 | 无 |
| 33 | `PushCallRocketMqConsumer` | `tp_app_push_report_callback` | `cg_xyf_cdp_tp_app_push_report_callback_tg_xyf_cdp` | `tg_xyf_cdp` | Push回调结果 | 无 |
| 34 | `AiCallBackStatusRocketMqConsumer` | `tp_call_center_ai_callback` | `cg_xyf_cdp_tp_call_center_ai_callback_tg_telemkt` | `tg_telemkt` | AI外呼回调 | 无 |
| 35 | `AiProntoCallBackRocketMqConsumer` | `tp_telemkt_name_ai_call_result` | `cg_xyf_cdp_tp_telemkt_name_ai_call_result` | 无 | AI Pronto回调 | 无 |

## 3. RabbitMQ消费者（辅助）

| 序号 | 队列名称 | Exchange | Routing Key | 业务用途 | 配置类 |
|------|----------|----------|-------------|----------|--------|
| 1 | `sms_supplier_report_callback` | `sms_supplier_report_callback` | `sms_supplier_report_callback` | 短信回执处理 | `SmsMqConfig` |
| 2 | `coupon_center_cash_coupon_cdp_process` | `coupon_center_cash_coupon_cdp_process` | `coupon_center_cash_coupon_cdp_process` | 优惠券回调 | `CouponMqConfig` |
| 3 | `queue_biz_event_sms_dispatch_xyf_cdp` | `exchange_biz_event_dispatch_topic` | `key_biz_event_sms_dispatch_xyf_cdp` | 短信触达分发 | `BizEventMqConfig` |
| 4 | `queue_biz_event_tele_dispatch_xyf_cdp` | `exchange_biz_event_dispatch_topic` | `key_biz_event_tele_dispatch_xyf_cdp` | 电销触达分发 | `BizEventMqConfig` |
| 5 | `queue_biz_event_coupon_dispatch_xyf_cdp` | `exchange_biz_event_dispatch_topic` | `key_biz_event_coupon_dispatch_xyf_cdp` | 优惠券触达分发 | `BizEventMqConfig` |

## 4. Pulsar消费者（已注释，暂未使用）

当前Pulsar相关的消费者都已被注释掉，包括：
- 注册事件监听器
- 风险激活事件监听器
- 借款申请页面浏览监听器
- 卡片点击监听器
- 风险临时额度监听器
- 借款成功监听器

## 5. 统计汇总

- **RocketMQ消费者总数**：35个
- **RabbitMQ队列总数**：5个
- **Pulsar消费者**：0个（已注释）
- **总计MQ消费点**：40个

## 6. 关键特点

### 6.1 统一处理机制
- **统一入口**：大部分RocketMQ消费者都通过`EventMessageBaseProcessor.doMessageProcess()`进行统一处理
- **消息解析**：通过`EventMessageProcessor`进行消息字段映射和解析
- **业务分发**：最终调用`StrategyEventDispatchService.prescreen()`进行业务处理

### 6.2 可靠性保障
- **幂等控制**：使用Redis分布式锁防止重复消费，锁定时间10分钟
- **异常处理**：完善的异常捕获和日志记录机制
- **消息确认**：支持消息确认机制，保证消息不丢失

### 6.3 配置管理
- **开关控制**：部分消费者支持Apollo配置开关控制，便于灰度发布
- **动态配置**：支持运行时动态调整消费者开关状态
- **环境隔离**：不同环境使用不同的Consumer Group

### 6.4 业务分类
- **风险类**：风险调额、风控禁申等风险相关事件
- **用户行为类**：注册、登录、页面浏览等用户行为事件
- **交易类**：还款、结清、借贷等交易相关事件
- **回调类**：短信、Push、AI外呼等渠道回调事件

### 6.5 性能优化
- **并发消费**：使用`ConsumeMode.CONCURRENTLY`并发消费模式
- **批量处理**：RabbitMQ支持批量消费，提升处理效率
- **标签路由**：部分Topic使用Tag进行消息路由，实现精确消费

## 7. 重构建议

### 7.1 消费者整合
- **按业务域整合**：将相关业务的消费者整合到同一个服务中
- **统一消费框架**：建立统一的消息消费框架，简化开发
- **配置标准化**：统一消费者配置格式和命名规范

### 7.2 监控完善
- **消费监控**：增加消费者性能监控和告警
- **业务监控**：增加业务指标监控，如消费成功率、处理延迟等
- **链路追踪**：完善分布式链路追踪，便于问题排查

### 7.3 架构优化
- **服务拆分**：按照微服务原则，将消费者按业务域拆分到不同服务
- **接口标准化**：统一消息处理接口，提升可维护性
- **容错机制**：增强容错能力，支持消息重试和死信队列

## 8. 总结

T0实时触达系统的MQ消费者体系庞大且复杂，涵盖了营销平台的各个业务场景。通过统一的消息处理框架和完善的配置管理，系统具备了良好的可扩展性和可维护性。在后续的微服务重构中，需要重点考虑消费者的合理拆分和业务边界的清晰划分。
