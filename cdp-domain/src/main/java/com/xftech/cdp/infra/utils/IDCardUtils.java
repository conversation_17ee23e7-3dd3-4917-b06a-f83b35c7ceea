package com.xftech.cdp.infra.utils;

/**
 * <AUTHOR>
 * @version v1.0 2024/10/9
 * @description IDCardUtils
 */
public class IDCardUtils {

    /**
     * 根据身份证号码获取性别
     *
     * @param idCard 身份证号码
     * @return "男" 或 "女"
     */
    public static String getGenderByIdCard(String idCard) {
        // 身份证号必须是18位
        if (idCard == null || idCard.length() != 18) {
            throw new IllegalArgumentException("身份证号码不合法");
        }

        // 获取身份证倒数第二位字符
        char genderChar = idCard.charAt(16);
        // 转换为数字
        int gender = Character.getNumericValue(genderChar);
        // 奇数为男，偶数为女
        return (gender % 2 == 0) ? "女士" : "先生";
    }

    public static void main(String[] args) {
        String idCard = "110105194912310020";
        String gender = getGenderByIdCard(idCard);
        System.out.println("性别: " + gender);
    }

}
