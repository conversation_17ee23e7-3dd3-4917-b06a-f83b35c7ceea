package com.xftech.cdp.infra.client.increaseamt;

import java.util.Date;
import java.util.Optional;

import javax.annotation.Resource;

import com.xftech.cdp.domain.strategy.model.dto.IncreaseAmtDto;
import com.xftech.cdp.domain.strategy.model.dto.IncreaseAmtParamDto;
import com.xftech.cdp.infra.config.LogUtil;
import com.xftech.cdp.infra.utils.DateUtil;
import com.xftech.cdp.infra.utils.RedisUtils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @version v1.0 2024/10/29
 * @description IncreaseAmtUtils
 */
@Slf4j
@Repository
public class IncreaseAmtUtils {

    private static final String LOAN_UPTO_FULL_AMT = "loan_upto_fullamt";
    private static final String PERSONAL_API_FST_LOGIN_TEMP = "personal_api_fst_login_temp";
    private static final String INCREASE_TMP_AMOUNT_MARKETING = "increase_tmp_amount_marketing";
    private static final String PERSONAL_MARKETING_RELOAN_TEMP = "personal_marketing_reloan_temp";

    private static final String CACHE_PREFIX = "xyf-cdp:increaseamt:callcount:%s-%d";

    @Resource
    private RedisUtils redisUtils;

    /**
     * 调用计数，用于调用频控（不包括提临额类型）
     *
     * @param userNo            用户号
     * @param modify_stage_type 提额类型
     */
    public void recordToday(Long userNo, String modify_stage_type) {
        if (userNo == null || StringUtils.equals(modify_stage_type, INCREASE_TMP_AMOUNT_MARKETING)
                || StringUtils.equals(modify_stage_type, PERSONAL_MARKETING_RELOAN_TEMP)) {
            return;
        }
        try {
            String today = DateUtil.convertByFormat(new Date(), "yyyyMMdd");
            String cacheKey = String.format(CACHE_PREFIX, today, userNo);
            redisUtils.set(cacheKey, modify_stage_type, RedisUtils.DEFAULT_EXPIRE_DAYS);
            LogUtil.logDebug("IncreaseAmtClient recordToday cacheKey={} modify_stage_type={}", cacheKey, modify_stage_type);
        } catch (Exception e) {
            log.error("IncreaseAmtClient recordToday error={}", e.getMessage(), e);
        }
    }

    /**
     * 查询当日是否已调用风控提额接口（不包括提临额类型）
     *
     * @param userNo
     * @param modify_stage_type
     * @return
     */
    public boolean hasRecordToday(Long userNo, String modify_stage_type) {
        if (userNo == null || StringUtils.equals(modify_stage_type, INCREASE_TMP_AMOUNT_MARKETING)
                || StringUtils.equals(modify_stage_type, PERSONAL_MARKETING_RELOAN_TEMP)) {
            return false;
        }
        try {
            String today = DateUtil.convertByFormat(new Date(), "yyyyMMdd");
            String cacheKey = String.format(CACHE_PREFIX, today, userNo);
            String cacheValue = redisUtils.get(cacheKey);
            LogUtil.logDebug("IncreaseAmtClient hasRecordToday cacheKey={} cacheValue={}", cacheKey, cacheValue);
            return StringUtils.isNotBlank(cacheValue);
        } catch (Exception e) {
            log.error("IncreaseAmtClient hasRecordToday error={}", e.getMessage(), e);
        }
        return false;
    }

    public String getIncreaseType(IncreaseAmtParamDto increaseAmtParamDto) {
        String increaseType = Optional.ofNullable(increaseAmtParamDto).map(IncreaseAmtParamDto::getIncreaseType).orElse(StringUtils.EMPTY);
        if (IncreaseAmtDto.IncreaseType.PERSONAL_API_FST_LOGIN_TEMP.equals(increaseType)) {
            return PERSONAL_API_FST_LOGIN_TEMP;
        } else if (IncreaseAmtDto.IncreaseType.LOAN_UPTO_FULLAMT.equals(increaseType)) {
            return LOAN_UPTO_FULL_AMT;
        } else if(IncreaseAmtDto.IncreaseType.PERSONAL_MARKETING_RELOAN_TEMP.equals(increaseType)) {
            return PERSONAL_MARKETING_RELOAN_TEMP;
        }
        return INCREASE_TMP_AMOUNT_MARKETING;
    }

}
