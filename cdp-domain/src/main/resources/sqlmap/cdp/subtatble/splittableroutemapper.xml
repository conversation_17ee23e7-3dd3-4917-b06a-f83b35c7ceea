<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="splitTableRoute">
  <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.subtable.po.SplitTableRoute">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="crowd_id" jdbcType="BIGINT" property="crowdId" />
    <result column="crowd_exec_log_id" jdbcType="BIGINT" property="crowdExecLogId" />
    <result column="exec_date" jdbcType="DATE" property="execDate" />
    <result column="table_name" jdbcType="VARCHAR" property="tableName" />
    <result column="table_no" jdbcType="INTEGER" property="tableNo" />
    <result column="d_flag" jdbcType="SMALLINT" property="dFlag" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, crowd_id, crowd_exec_log_id, exec_date, `table_name`, table_no, d_flag, created_time, updated_time
  </sql>

  <select id="selectSplitTableRouteById" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from split_table_route
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectTableNos" resultMap="BaseResultMap">
    select table_no from split_table_route where crowd_exec_log_id = #{crowdExecLogId} group by table_no
  </select>

  <select id="selectExpireRecord" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from split_table_route
    where d_flag = 0 and `table_name` = #{tableName} and exec_date &lt; #{expireDate} order by table_no limit 1
  </select>

  <select id="existSplitTableRoute" resultType="integer">
    select 1
    from split_table_route
    where crowd_exec_log_id = #{crowdExecLogId} and table_no = #{tableNo} limit 1
  </select>

  <update id="updateDflagByCrowdExecLogId">
    update split_table_route set d_flag = 1 where crowd_exec_log_id = #{crowdExecLogId}
  </update>

  <update id="updateDflagByTableNo">
    update split_table_route set d_flag = 1 where d_flag = 0 and table_no = #{tableNo}
  </update>

  <insert id="insertSplitTableRoute" keyColumn="id" keyProperty="id" parameterType="com.xftech.cdp.infra.repository.subtable.po.SplitTableRoute" useGeneratedKeys="true">
    insert into split_table_route
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="crowdId != null">
        crowd_id,
      </if>
      <if test="crowdExecLogId != null">
        crowd_exec_log_id,
      </if>
      <if test="execDate != null">
        exec_date,
      </if>
      <if test="tableName != null">
        `table_name`,
      </if>
      <if test="tableNo != null">
        table_no,
      </if>
      <if test="dFlag != null">
        d_flag,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="updatedTime != null">
        updated_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="crowdId != null">
        #{crowdId,jdbcType=BIGINT},
      </if>
      <if test="crowdExecLogId != null">
        #{crowdExecLogId,jdbcType=BIGINT},
      </if>
      <if test="execDate != null">
        #{execDate,jdbcType=DATE},
      </if>
      <if test="tableName != null">
        #{tableName,jdbcType=VARCHAR},
      </if>
      <if test="tableNo != null">
        #{tableNo,jdbcType=INTEGER},
      </if>
      <if test="dFlag != null">
        #{dFlag,jdbcType=SMALLINT},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        #{updatedTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <update id="updateSplitTableRouteById" parameterType="com.xftech.cdp.infra.repository.subtable.po.SplitTableRoute">
    update split_table_route
    <set>
      <if test="crowdId != null">
        crowd_id = #{crowdId,jdbcType=BIGINT},
      </if>
      <if test="crowdExecLogId != null">
        crowd_exec_log_id = #{crowdExecLogId,jdbcType=BIGINT},
      </if>
      <if test="execDate != null">
        exec_date = #{execDate,jdbcType=DATE},
      </if>
      <if test="tableName != null">
        `table_name` = #{tableName,jdbcType=VARCHAR},
      </if>
      <if test="tableNo != null">
        table_no = #{tableNo,jdbcType=INTEGER},
      </if>
      <if test="dFlag != null">
        d_flag = #{dFlag,jdbcType=SMALLINT},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>