package com.xftech.cdp.infra.repository.cdp.strategy.po;

import java.io.Serializable;
import java.util.Date;

public class DispatchCrowdDo implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column dispatch_crowd_20231221.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column dispatch_crowd_20231221.flow_no
     *
     * @mbg.generated
     */
    private String flowNo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column dispatch_crowd_20231221.batch_no
     *
     * @mbg.generated
     */
    private String batchNo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column dispatch_crowd_20231221.pre_strategy_id
     *
     * @mbg.generated
     */
    private Long preStrategyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column dispatch_crowd_20231221.strategy_id
     *
     * @mbg.generated
     */
    private Long strategyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column dispatch_crowd_20231221.next_strategy_id
     *
     * @mbg.generated
     */
    private Long nextStrategyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column dispatch_crowd_20231221.user_id
     *
     * @mbg.generated
     */
    private Long userId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column dispatch_crowd_20231221.user_info
     *
     * @mbg.generated
     */
    private String userInfo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column dispatch_crowd_20231221.d_flag
     *
     * @mbg.generated
     */
    private Short dFlag;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column dispatch_crowd_20231221.created_time
     *
     * @mbg.generated
     */
    private Date createdTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column dispatch_crowd_20231221.updated_time
     *
     * @mbg.generated
     */
    private Date updatedTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table dispatch_crowd_20231221
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;


    private Integer tableNum;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column dispatch_crowd_20231221.id
     *
     * @return the value of dispatch_crowd_20231221.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column dispatch_crowd_20231221.id
     *
     * @param id the value for dispatch_crowd_20231221.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column dispatch_crowd_20231221.flow_no
     *
     * @return the value of dispatch_crowd_20231221.flow_no
     *
     * @mbg.generated
     */
    public String getFlowNo() {
        return flowNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column dispatch_crowd_20231221.flow_no
     *
     * @param flowNo the value for dispatch_crowd_20231221.flow_no
     *
     * @mbg.generated
     */
    public void setFlowNo(String flowNo) {
        this.flowNo = flowNo == null ? null : flowNo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column dispatch_crowd_20231221.batch_no
     *
     * @return the value of dispatch_crowd_20231221.batch_no
     *
     * @mbg.generated
     */
    public String getBatchNo() {
        return batchNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column dispatch_crowd_20231221.batch_no
     *
     * @param batchNo the value for dispatch_crowd_20231221.batch_no
     *
     * @mbg.generated
     */
    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo == null ? null : batchNo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column dispatch_crowd_20231221.pre_strategy_id
     *
     * @return the value of dispatch_crowd_20231221.pre_strategy_id
     *
     * @mbg.generated
     */
    public Long getPreStrategyId() {
        return preStrategyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column dispatch_crowd_20231221.pre_strategy_id
     *
     * @param preStrategyId the value for dispatch_crowd_20231221.pre_strategy_id
     *
     * @mbg.generated
     */
    public void setPreStrategyId(Long preStrategyId) {
        this.preStrategyId = preStrategyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column dispatch_crowd_20231221.strategy_id
     *
     * @return the value of dispatch_crowd_20231221.strategy_id
     *
     * @mbg.generated
     */
    public Long getStrategyId() {
        return strategyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column dispatch_crowd_20231221.strategy_id
     *
     * @param strategyId the value for dispatch_crowd_20231221.strategy_id
     *
     * @mbg.generated
     */
    public void setStrategyId(Long strategyId) {
        this.strategyId = strategyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column dispatch_crowd_20231221.next_strategy_id
     *
     * @return the value of dispatch_crowd_20231221.next_strategy_id
     *
     * @mbg.generated
     */
    public Long getNextStrategyId() {
        return nextStrategyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column dispatch_crowd_20231221.next_strategy_id
     *
     * @param nextStrategyId the value for dispatch_crowd_20231221.next_strategy_id
     *
     * @mbg.generated
     */
    public void setNextStrategyId(Long nextStrategyId) {
        this.nextStrategyId = nextStrategyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column dispatch_crowd_20231221.user_id
     *
     * @return the value of dispatch_crowd_20231221.user_id
     *
     * @mbg.generated
     */
    public Long getUserId() {
        return userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column dispatch_crowd_20231221.user_id
     *
     * @param userId the value for dispatch_crowd_20231221.user_id
     *
     * @mbg.generated
     */
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column dispatch_crowd_20231221.user_info
     *
     * @return the value of dispatch_crowd_20231221.user_info
     *
     * @mbg.generated
     */
    public String getUserInfo() {
        return userInfo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column dispatch_crowd_20231221.user_info
     *
     * @param userInfo the value for dispatch_crowd_20231221.user_info
     *
     * @mbg.generated
     */
    public void setUserInfo(String userInfo) {
        this.userInfo = userInfo == null ? null : userInfo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column dispatch_crowd_20231221.d_flag
     *
     * @return the value of dispatch_crowd_20231221.d_flag
     *
     * @mbg.generated
     */
    public Short getdFlag() {
        return dFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column dispatch_crowd_20231221.d_flag
     *
     * @param dFlag the value for dispatch_crowd_20231221.d_flag
     *
     * @mbg.generated
     */
    public void setdFlag(Short dFlag) {
        this.dFlag = dFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column dispatch_crowd_20231221.created_time
     *
     * @return the value of dispatch_crowd_20231221.created_time
     *
     * @mbg.generated
     */
    public Date getCreatedTime() {
        return createdTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column dispatch_crowd_20231221.created_time
     *
     * @param createdTime the value for dispatch_crowd_20231221.created_time
     *
     * @mbg.generated
     */
    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column dispatch_crowd_20231221.updated_time
     *
     * @return the value of dispatch_crowd_20231221.updated_time
     *
     * @mbg.generated
     */
    public Date getUpdatedTime() {
        return updatedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column dispatch_crowd_20231221.updated_time
     *
     * @param updatedTime the value for dispatch_crowd_20231221.updated_time
     *
     * @mbg.generated
     */
    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    public Integer getTableNum() {
        return tableNum;
    }

    public void setTableNum(Integer tableNum) {
        this.tableNum = tableNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table dispatch_crowd_20231221
     *
     * @mbg.generated
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        DispatchCrowdDo other = (DispatchCrowdDo) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getFlowNo() == null ? other.getFlowNo() == null : this.getFlowNo().equals(other.getFlowNo()))
            && (this.getBatchNo() == null ? other.getBatchNo() == null : this.getBatchNo().equals(other.getBatchNo()))
            && (this.getPreStrategyId() == null ? other.getPreStrategyId() == null : this.getPreStrategyId().equals(other.getPreStrategyId()))
            && (this.getStrategyId() == null ? other.getStrategyId() == null : this.getStrategyId().equals(other.getStrategyId()))
            && (this.getNextStrategyId() == null ? other.getNextStrategyId() == null : this.getNextStrategyId().equals(other.getNextStrategyId()))
            && (this.getUserId() == null ? other.getUserId() == null : this.getUserId().equals(other.getUserId()))
            && (this.getUserInfo() == null ? other.getUserInfo() == null : this.getUserInfo().equals(other.getUserInfo()))
            && (this.getdFlag() == null ? other.getdFlag() == null : this.getdFlag().equals(other.getdFlag()))
            && (this.getCreatedTime() == null ? other.getCreatedTime() == null : this.getCreatedTime().equals(other.getCreatedTime()))
            && (this.getUpdatedTime() == null ? other.getUpdatedTime() == null : this.getUpdatedTime().equals(other.getUpdatedTime()));
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table dispatch_crowd_20231221
     *
     * @mbg.generated
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getFlowNo() == null) ? 0 : getFlowNo().hashCode());
        result = prime * result + ((getBatchNo() == null) ? 0 : getBatchNo().hashCode());
        result = prime * result + ((getPreStrategyId() == null) ? 0 : getPreStrategyId().hashCode());
        result = prime * result + ((getStrategyId() == null) ? 0 : getStrategyId().hashCode());
        result = prime * result + ((getNextStrategyId() == null) ? 0 : getNextStrategyId().hashCode());
        result = prime * result + ((getUserId() == null) ? 0 : getUserId().hashCode());
        result = prime * result + ((getUserInfo() == null) ? 0 : getUserInfo().hashCode());
        result = prime * result + ((getdFlag() == null) ? 0 : getdFlag().hashCode());
        result = prime * result + ((getCreatedTime() == null) ? 0 : getCreatedTime().hashCode());
        result = prime * result + ((getUpdatedTime() == null) ? 0 : getUpdatedTime().hashCode());
        return result;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table dispatch_crowd_20231221
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", flowNo=").append(flowNo);
        sb.append(", batchNo=").append(batchNo);
        sb.append(", preStrategyId=").append(preStrategyId);
        sb.append(", strategyId=").append(strategyId);
        sb.append(", nextStrategyId=").append(nextStrategyId);
        sb.append(", userId=").append(userId);
        sb.append(", userInfo=").append(userInfo);
        sb.append(", dFlag=").append(dFlag);
        sb.append(", createdTime=").append(createdTime);
        sb.append(", updatedTime=").append(updatedTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}