package com.xftech.cdp.infra.rocketmq.risk;

import com.xftech.cdp.infra.rocketmq.EventMessageBaseProcessor;
import com.xftech.cdp.infra.utils.WhitelistSwitchUtil;

import com.xinfei.xfframework.common.starter.mq.MqConsumerListener;

import groovy.util.logging.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version $ RiskSingleAdjSesameMsgConsumer, v 0.1 2025/2/25 14:20 tianshuo.qiu Exp $
 */
@Slf4j
@Component
@RocketMQMessageListener(consumerGroup = "cg_xyf_cdp_tg_sesame_identification_increase_amount", topic = "tp_rcspt_risk_amount_change_message",selectorExpression = "tg_sesame_identification_increase_amount",  consumeMode = ConsumeMode.CONCURRENTLY)

public class NewRiskSingleAdjSesameMsgConsumer extends MqConsumerListener<String> {
    private static final Logger log = LoggerFactory.getLogger(NewRiskSingleAdjSesameMsgConsumer.class);
    @Autowired
    private EventMessageBaseProcessor eventMessageBaseProcessor;


    @Override
    public void doMessage(String topic, String message, MessageExt messageExt) {
        if (WhitelistSwitchUtil.boolSwitchByApollo("NewRiskSingleAdjSesameMsgConsumerEnable")) {
            try {
                RocketMQMessageListener messageListener = this.getClass().getAnnotation(RocketMQMessageListener.class);
                eventMessageBaseProcessor.doMessageProcess(messageListener, messageExt, message);
            } catch (Exception e) {
                log.error("message process error, topic={},message={}", topic, message, e);
            }
        }
    }
}
