package com.xftech.cdp.infra.client.ads.model.req;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * @<NAME_EMAIL>
 * @date 2023/4/27 10:55
 */
@Data
@AllArgsConstructor
public class ChannelIndexReq {
    /**
     * 用户id（一个或者一批逗号隔开）
     */
    private List<Long> appUserId;
    /**
     * 营销渠道（一个或者多个逗号隔开）
     */
    private List<String> marketChannel;

    /**
     * 开始日期（yyyy-MM-dd），包括开始日期这一天
     */
    private LocalDate startTime;
    /**
     * 结束日期（yyyy-MM-dd)，包括结束日期这一天
     */
    private LocalDate endTime;
}