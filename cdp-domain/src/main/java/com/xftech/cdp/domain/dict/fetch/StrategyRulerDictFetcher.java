package com.xftech.cdp.domain.dict.fetch;

import com.xftech.cdp.api.dto.resp.dict.Dict;
import com.xftech.cdp.domain.dict.model.enums.DictCode;
import com.xftech.cdp.domain.strategy.model.enums.StrategyRulerEnum;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @<NAME_EMAIL>
 */

@Component
public class StrategyRulerDictFetcher implements DictFetcher {

    @Override
    public List<Dict> getDict() {
        List<Dict> result = new ArrayList<>();
        List<StrategyRulerEnum> strategyStatusEnums = Arrays.stream(StrategyRulerEnum.values()).collect(Collectors.toList());
        for (StrategyRulerEnum strategyRulerEnum : strategyStatusEnums) {
            result.add(Dict.builder().dictCode(String.valueOf(strategyRulerEnum.getCode())).dictValue(strategyRulerEnum.getDescription()).build());
        }
        return result;
    }

    @Override
    public DictCode getDictCode() {
        return DictCode.STRATEGY_RULE_TYPE;
    }

    @Override
    public String getDescription() {
        return "触发类型";
    }
}
