package com.xftech.cdp.infra.aspect;


import com.alibaba.fastjson.JSON;
import com.xftech.cdp.infra.annotation.SysLog;
import com.xftech.cdp.infra.utils.HttpContextUtil;
import com.xftech.cdp.infra.utils.SsoUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 系统日志，切面处理类
 *
 * @<NAME_EMAIL>
 */
@Slf4j
@Aspect
@Component
public class SysLogAspect {


    @Pointcut("@annotation(com.xftech.cdp.infra.annotation.SysLog)")
    public void logPointCut() {

    }

    @Around("logPointCut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {

        HttpServletRequest request = HttpContextUtil.getHttpServletRequest();

        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        String className = point.getTarget().getClass().getName();
        String methodName = signature.getName();
        String fullMethodName = className + "." + methodName + "()";

        SysLog syslog = method.getAnnotation(SysLog.class);

        log.info("请求描述:{}", syslog.value());
        log.info("请求用户:{}", syslog.extApi() ? "ext-sys" : SsoUtil.get().getName());
        log.info("请求路径:{}", request == null ? null : request.getRequestURL());
        log.info("请求方法:{}", fullMethodName);
        log.info("请求参数:{}", syslog.printParam() ? JSON.toJSONString(filterRequestParameter(point)) : "******");

        long beginTime = System.currentTimeMillis();
        //执行方法
        Object result = point.proceed();
        //执行时长(毫秒)
        long time = System.currentTimeMillis() - beginTime;

        log.info("请求结果:{}", JSON.toJSONString(result));
        log.info("请求耗时:{}ms", time);

        return result;
    }

    private List<Object> filterRequestParameter(ProceedingJoinPoint point) {
        Object[] args = point.getArgs();
        Stream<?> stream = ArrayUtils.isEmpty(args) ? Stream.empty() : Arrays.stream(args.clone());
        return stream.filter(arg ->
                (!(arg instanceof HttpServletRequest) && !(arg instanceof HttpServletResponse))
        ).collect(Collectors.toList());
    }

    @AfterThrowing(value = "execution(* com.xftech.cdp.api.*.*(..))", throwing = "ex")
    public void doThrow(JoinPoint joinPoint, Exception ex) {
        log.warn("请求异常:{}", ex.getMessage());
    }

}
