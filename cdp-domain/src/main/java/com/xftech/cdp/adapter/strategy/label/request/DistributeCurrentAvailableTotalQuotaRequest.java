package com.xftech.cdp.adapter.strategy.label.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ DistributeCurrentAvailableTotalQuotaRequest, v 0.1 2025/04/02 14:07 tianshuo.qiu Exp $
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DistributeCurrentAvailableTotalQuotaRequest {
    private QuotaQueryDto args;
    private String ua;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class QuotaQueryDto {
        private Long userNo;

        private List<Integer> statusList;

        private List<String> matchTypeList;
    }
}
