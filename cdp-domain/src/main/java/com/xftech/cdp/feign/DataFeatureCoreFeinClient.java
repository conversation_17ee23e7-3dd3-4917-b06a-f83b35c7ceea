/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.feign;

import com.xftech.cdp.feign.contants.FeignConstants;
import com.xftech.cdp.feign.interceptor.DataFeatureCoreInterceptor;
import com.xftech.cdp.feign.model.requset.FeatureQueryRequest;
import com.xftech.cdp.feign.model.response.FeatureQueryResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/*
 * 注意FeignClient注解需要配置三个参数
 * name 为应用名
 * path 为父路径
 * url  为sevice对应的域名 域名可以配置在配置中心中，格式"xf." + ${name} + ".url"
 *
 * 使用了信飞的xfframework，框架会自动生成Feign的配置信息。详情参考源码。
 * com.xinfei.xfframework.common.starter.feign.XfServiceInstance.createUri()
 *
 * 注意contextId表示此feignclient对应的配置id，推荐的格式是contextId=name+"-"+类名
 * feign.client.config.${name}.connectTimeout= *** 默认是10s
 * feign.client.config.${name}.readTimeout= **** 默认是60s
 * 默认连接池默认存活时间是900秒
 * 理论上对外暴露的所有接口都是post类型的，需要指定使用PostMapping注解
 */
@FeignClient(name = FeignConstants.DATA_FEATURES_SERVICE,
        contextId = FeignConstants.DATA_FEATURES_SERVICE + ".FeignClient",
        configuration = DataFeatureCoreInterceptor.class)
public interface DataFeatureCoreFeinClient {

    @PostMapping("/api/feature_query")
    FeatureQueryResponse feature_query(@RequestBody FeatureQueryRequest request);

}
