/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.cache;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 *
 * <AUTHOR>
 * @version $ CaffeineCacheConfig, v 0.1 2024/1/16 15:39 lingang.han Exp $
 */

@Slf4j
@Configuration
public class CaffeineCacheConfig {

    @Bean(value = "flowBatchNoToStartTimeCache")
    public Cache<String, String> cache() {
        return Caffeine.newBuilder()
                //初始大小
                .initialCapacity(10)
                //最大条数
                .maximumSize(500)
                //expireAfterWrite和expireAfterAccess同时存在时，以expireAfterWrite为准
                //最后一次写操作后经过指定时间过期
                .expireAfterWrite(30, TimeUnit.SECONDS)
                //最后一次读或写操作后经过指定时间过期
                .expireAfterAccess(30, TimeUnit.SECONDS)
                //监听缓存被移除
                .removalListener((key, val, removalCause) -> {
                    log.info("flowBatchNoToStartTime淘汰缓存：key:{} val:{}", key, val);
                })
                //记录命中
                .recordStats()
                .build();
    }

    @Bean(value = "strategyDoLocalCache")
    public Cache<Long, StrategyDo> strategyDoLocalCache() {
        return Caffeine.newBuilder()
                //初始大小
                .initialCapacity(1000)
                //最大条数
                .maximumSize(3000)
                //expireAfterWrite和expireAfterAccess同时存在时，以expireAfterWrite为准
                //最后一次写操作后经过指定时间过期
                .expireAfterWrite(20, TimeUnit.SECONDS)
                //监听缓存被移除
                .removalListener((key, val, removalCause) -> {
                    log.info("strategyDoLocalCache淘汰缓存：key:{} val:{}", key, val);
                })
                //记录命中
                .recordStats()
                .build();
    }
}