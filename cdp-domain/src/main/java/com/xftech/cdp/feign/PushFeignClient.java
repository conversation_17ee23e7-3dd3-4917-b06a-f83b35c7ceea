package com.xftech.cdp.feign;

import com.xftech.cdp.feign.contants.FeignConstants;
import com.xftech.cdp.feign.model.PushTemplateDetail;
import com.xftech.cdp.feign.model.PushTemplateList;
import com.xftech.cdp.feign.model.SendPushInfo;
import com.xftech.cdp.feign.model.requset.PushBaseRequest;
import com.xftech.cdp.feign.model.requset.SendPushRequest;
import com.xftech.cdp.feign.model.requset.TemplateDetailRequest;
import com.xftech.cdp.feign.model.requset.TemplateListRequest;
import com.xftech.cdp.feign.model.response.PushResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 * @version $ PushFeignClient, v 0.1 2024/1/16 17:36 qu.lu Exp $
 */
@FeignClient(name = FeignConstants.PUSH_SERVICE,contextId = FeignConstants.PUSH_SERVICE+".FeignClient",path = "/")
public interface PushFeignClient {

    /**
     * 查询Push模板列表信息
     * 接口文档：https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001018423
     * @param request
     * @return
     */
    @PostMapping("/admin/app-push/template/tpl-list")
    PushResponse<PushTemplateList> queryPushTemplateList(PushBaseRequest<TemplateListRequest> request);


    /**
     * 根据Push模板id查询模板明细信息
     * 接口文档：https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001018423
     * @param request
     * @return
     */
    @PostMapping("/admin/app-push/template/tpl-detail")
    PushResponse<PushTemplateDetail> loadPushTemplateDetail(PushBaseRequest<TemplateDetailRequest> request);

    /**
     * 发送push
     * 接口文档：https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001018359
     * @param request
     * @return
     */
    @PostMapping("/app-push/send")
    PushResponse<SendPushInfo> sendPush(PushBaseRequest<SendPushRequest> request);
}
