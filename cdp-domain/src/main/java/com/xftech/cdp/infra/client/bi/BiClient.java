package com.xftech.cdp.infra.client.bi;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.xftech.cdp.infra.client.ads.AdsClient;
import com.xftech.cdp.infra.client.ads.model.req.BaseAdsRequest;
import com.xftech.cdp.infra.client.bi.config.BiConfig;
import com.xftech.cdp.infra.client.bi.model.req.BaseBiRequester;
import com.xftech.cdp.infra.client.bi.model.req.ModelMarketingReq;
import com.xftech.cdp.infra.client.bi.model.resp.BaseBiResponse;
import com.xftech.cdp.infra.client.bi.util.Signer;
import com.xftech.cdp.infra.client.coupon.model.resp.BaseCouponResponse;
import com.xftech.cdp.infra.client.coupon.model.resp.CouponActivityListResp;
import com.xftech.cdp.infra.utils.HttpClientUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.net.URISyntaxException;
import java.time.Instant;
import java.time.LocalDateTime;

@Component
public class BiClient {
    private static final Logger LOGGER = LoggerFactory.getLogger(AdsClient.class);

    @Autowired
    private Signer biSigner;

    @Autowired
    private BiConfig biConfig;

    @Autowired
    private HttpClientUtil httpClientUtil;




    public <T extends BaseBiRequester> BaseBiResponse modelMarketing(T requester) {
        String request = request(requester, biConfig.getMarketingRoute());
        return JSON.parseObject(request, new TypeReference<BaseBiResponse>() {
        });
    }



    private <T extends BaseBiRequester> String request(T requester, String uri) {
        String url = biConfig.getHost() + "/" + uri;
        try {
            LOGGER.info("bi send，url：{}，requester：{}", url, JSON.toJSONString(requester, SerializerFeature.SortField));
            long startTime = Instant.now().toEpochMilli();
            String result = httpClientUtil.postJson(url, requester);
            LOGGER.info("bi send，response：{}，time：{}ms", result, Instant.now().toEpochMilli() - startTime);
            return result;
        } catch (Exception e) {
            LOGGER.error("bi send error，url:{}，params:{}", url, JSON.toJSONString(requester, SerializerFeature.SortField), e);
            throw e;
        }
    }


    private static URI createUri(String str) {
        try {
            return new URI(str);
        } catch (URISyntaxException x) {
            throw new IllegalArgumentException(x.getMessage(), x);
        }
    }

}
