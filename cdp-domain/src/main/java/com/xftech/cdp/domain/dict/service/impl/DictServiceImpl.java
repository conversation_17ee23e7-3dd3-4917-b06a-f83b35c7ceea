package com.xftech.cdp.domain.dict.service.impl;

import com.xftech.cdp.api.dto.req.dict.DictReq;
import com.xftech.cdp.api.dto.req.dict.EventMetaDataReq;
import com.xftech.cdp.api.dto.resp.dict.Dict;
import com.xftech.cdp.api.dto.resp.dict.DictResp;
import com.xftech.cdp.api.dto.resp.dict.EventMetaDataResp;
import com.xftech.cdp.api.dto.resp.dict.SubEventData;
import com.xftech.cdp.domain.cache.CacheStrategyService;
import com.xftech.cdp.domain.dict.fetch.DictFetcher;
import com.xftech.cdp.domain.dict.repository.DictionaryRepository;
import com.xftech.cdp.domain.dict.service.DictService;
import com.xftech.cdp.domain.strategy.model.enums.StrategyStatusEnum;
import com.xftech.cdp.domain.strategy.model.enums.label.OperatorEnum;
import com.xftech.cdp.domain.strategy.repository.EventMetaDataRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyInstantLabelRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyMarketSubEventRepository;
import com.xftech.cdp.infra.repository.cdp.dictionary.po.DictionaryDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.EventMetaDataDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyInstantLabelDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketSubEventDo;
import com.xftech.cdp.infra.utils.RedisUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;


@Service
public class DictServiceImpl implements DictService {

    @Autowired
    DictionaryRepository dictionaryRepository;

    @Autowired
    EventMetaDataRepository eventMetaDataRepository;

    @Autowired
    StrategyInstantLabelRepository strategyInstantLabelRepository;

    @Autowired
    private StrategyMarketSubEventRepository strategyMarketSubEventRepository;

    @Autowired
    private CacheStrategyService cacheStrategyService;

    @Autowired
    RedisUtils redisUtils;

    private final List<DictFetcher> dictFetchers;

    public DictServiceImpl(List<DictFetcher> dictFetchers) {
        this.dictFetchers = dictFetchers;
    }


    @Override
    public List<DictResp> queryListByDictCodeList(List<String> dictCodes) {

        List<DictResp> result = new ArrayList<>();
        List<DictFetcher> enableDictFetchers = dictFetchers;
        if (!CollectionUtils.isEmpty(dictCodes)) {
            enableDictFetchers = dictFetchers.stream().filter(a -> dictCodes.contains(a.getDictCode().name().toUpperCase())).collect(Collectors.toList());
        }
        for (DictFetcher enableDictFetcher : enableDictFetchers) {
            result.add(DictResp.builder().description(enableDictFetcher.getDescription()).dictCode(enableDictFetcher.getDictCode().name()).dict(enableDictFetcher.getDict()).build());
        }
        return result;
    }

    @Override
    public List<DictResp> queryDictCodeList(DictReq dictReq) {
        List<DictionaryDo> dictionaryList = dictionaryRepository.selectByDictTypeListAndBusinessType(dictReq.getDictCodeList(), dictReq.getBusinessType());
        List<DictionaryDo> dictType = dictionaryList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(DictionaryDo::getDictType))), ArrayList::new));
        return dictType.stream().map(item -> {
            DictResp dictResp = DictResp.builder().build();
            dictResp.setDescription(item.getDictTypeName());
            dictResp.setDictCode(item.getDictType());
            List<Dict> childrenList = dictionaryList.stream().filter(tmp->tmp.getDictType().equals(item.getDictType())).map(t -> {
                Dict dict = Dict.builder().build();
                dict.setDictCode(t.getDictCode());
                dict.setDictValue(t.getDictValue());
                return dict;
            }).collect(Collectors.toList());
            dictResp.setDict(childrenList);
            return dictResp;
        }).collect(Collectors.toList());
    }


    @Override
    public List<EventMetaDataResp> queryEventMetaData(EventMetaDataReq eventMetaDataReq) {
        List<EventMetaDataResp> eventMetaDataRespList = new ArrayList<>();

        List<EventMetaDataDo> dictionaryList = eventMetaDataRepository.selectByBusinessType(eventMetaDataReq.getBusinessType());
        List<EventMetaDataDo> dictType = dictionaryList.stream().filter(item -> Objects.equals(-1L, item.getParentId())).collect(Collectors.toList());

        EventMetaDataResp eventMetaDataResp = new EventMetaDataResp();
        eventMetaDataResp.setDictType("101");
        eventMetaDataResp.setDictTypeName("事件类型");

        List<EventMetaDataResp.EventDict> eventDictList = dictType.stream().map(item -> {
            EventMetaDataResp.EventDict eventDict = new EventMetaDataResp.EventDict();
            eventDict.setEventDesc(item.getEventDesc());
            eventDict.setEventName(item.getEventName());
            eventDict.setEventType(item.getEventType());
            eventDict.setSubEventDataList(getChild(item.getId(), dictionaryList));
            for (SubEventData subEventData : eventDict.getSubEventDataList()) {
                String subEventName = subEventData.getSubEventName();
                if (subEventName.equals("apiHoldInnerApp")) {
                    List<String> usedValueList = calcUsedValueList(eventMetaDataReq, subEventName, eventMetaDataReq.getBusinessType());
                    subEventData.setUsedValueList(usedValueList);
                }
            }
            return eventDict;
        }).collect(Collectors.toList());
        eventMetaDataResp.setEventDictList(eventDictList);
        eventMetaDataRespList.add(eventMetaDataResp);


        List<StrategyInstantLabelDo> labelDoList = strategyInstantLabelRepository.selectByLabelTypeAndBusinessType(1, eventMetaDataReq.getBusinessType(),eventMetaDataReq.getStrategyType(),eventMetaDataReq.getStrategyModel());
        EventMetaDataResp subEventMetaDataResp = new EventMetaDataResp();
        subEventMetaDataResp.setDictType("102");
        subEventMetaDataResp.setDictTypeName("标签类型");

        List<EventMetaDataResp.EventDict> subEventDictList = new ArrayList<>();

        EventMetaDataResp.EventDict eventDict = new EventMetaDataResp.EventDict();
        eventDict.setEventDesc("");
        eventDict.setEventName("");

        List<SubEventData> labelList = labelDoList.stream().filter(t -> !Objects.equals(-1, t.getFillType())).map(item -> {
            SubEventData subEventData = new SubEventData();
            subEventData.setSubEventDesc(item.getFrontDesc());
            String subEventName = item.getLabelName();
            subEventData.setSubEventName(subEventName);
            //主要用于映射字典表的类型，用于前端获取枚举
            subEventData.setDictType(subEventName);
            subEventData.setFillType(item.getFillType());
            subEventData.setOptional(item.getOptional());
            subEventData.setOperateType(getOperateType(item.getOperateType()));
            return subEventData;
        }).collect(Collectors.toList());

        eventDict.setSubEventDataList(labelList);
        subEventDictList.add(eventDict);

        subEventMetaDataResp.setEventDictList(subEventDictList);

        eventMetaDataRespList.add(subEventMetaDataResp);

        return eventMetaDataRespList;
    }

    private List<String> calcUsedValueList(EventMetaDataReq eventMetaDataReq, String subEventName, String businessType) {
        List<StrategyMarketSubEventDo> subEventDoList = strategyMarketSubEventRepository.getByEventName(subEventName);
        Set<String> usedValueSet = new HashSet<>(subEventDoList.size());
        for (StrategyMarketSubEventDo subEventDo : subEventDoList) {
            String operateType = subEventDo.getOperateType();
            String[] eventValues = subEventDo.getEventValue().split(",");
            if (operateType.equals("contain")) {
                StrategyDo strategyDo = cacheStrategyService.selectById(subEventDo.getStrategyId());
                if (Objects.nonNull(strategyDo) &&
                        strategyDo.getBusinessType().equals(businessType) &&
                        StrategyStatusEnum.getDistinctInnerAppCodes().contains(strategyDo.getStatus()) ) {
                    Collections.addAll(usedValueSet, eventValues);
                }
//            } else if (operateType.equals("notcontain")){
//                List<DictionaryDo> dictionaryDos =
//                        dictionaryRepository.selectByDictTypeListAndBusinessType(
//                                Collections.singletonList("apiHoldInnerApp"), eventMetaDataReq.getBusinessType());
//                List<String> allValues = dictionaryDos.stream().map(DictionaryDo::getDictCode).collect(Collectors.toList());
//                for (String eventValue : eventValues) {
//                    if ( !allValues.contains(eventValue)) {
//                        usedValueSet.add(eventValue);
//                    }
//                }
            }
        }
        return new ArrayList<>(usedValueSet);
    }

    private List<SubEventData> getChild(Long id, List<EventMetaDataDo> dictionaryList) {
        return dictionaryList.stream()
                .filter(item -> Objects.nonNull(item.getParentId()) && item.getParentId().equals(id))
                .map(item -> {
                    SubEventData subEventData = new SubEventData();
                    subEventData.setSubEventDesc(item.getEventDesc());
                    String subEventName = item.getEventName();
                    subEventData.setSubEventName(subEventName);
                    subEventData.setFillType(item.getFillType());
                    subEventData.setDictType(item.getDictType());
                    subEventData.setLabelValueType(item.getLabelValueType());
                    subEventData.setOperateType(getOperateType(item.getOperateType()));
                    return subEventData;
                }).collect(Collectors.toList());
    }


    private List<Dict> getOperateType(String operateType) {
        List<Dict> operateTypeList = new ArrayList<>();
        String[] operateTypeArr = operateType.split(",");
        for (String item : operateTypeArr) {
            Dict dict = Dict.builder().build();
            dict.setDictCode(OperatorEnum.getInstance(item).getDescription());
            dict.setDictValue(item);
            operateTypeList.add(dict);
        }
        return operateTypeList;
    }


}
