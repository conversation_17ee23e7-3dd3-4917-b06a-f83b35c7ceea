/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ StrategyReportDailyResp, v 0.1 2023/12/6 14:38 lingang.han Exp $
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class StrategyReportDailyResp implements Serializable {
    private static final long serialVersionUID = -5434939723212748280L;

    @ApiModelProperty(value = "总策略数")
    private Integer strategySum;

    @ApiModelProperty(value = "总触达渠道数")
    private Integer channelSum;

    @ApiModelProperty(value = "当前完成策略数")
    private Integer successStrategyCount;

    @ApiModelProperty(value = "当前完成渠道数")
    private Integer successChannelCount;

    @ApiModelProperty(value = "失败渠道数")
    private Integer failChannelCount;

    @ApiModelProperty(value = "最近刷新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date refreshTime;

    @ApiModelProperty(value = "策略执行明细")
    private List<StrategyReportDailyDetailResp> detailList;
}