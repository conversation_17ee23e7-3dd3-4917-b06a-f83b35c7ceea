package com.xftech.cdp.adapter.strategy.label;

import com.xftech.cdp.infra.client.ads.model.resp.AdsLabelResp;

import java.util.Objects;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 *
 * <AUTHOR>
 */
public interface LabelHandler {

    default String getLabel() {
        return "default";
    }

    default boolean topicTagCompare(String label) {
        return Objects.equals(getLabel(), label);
    }

    AdsLabelResp execute(String label,QueryLabelRequest batchAdsLabelVO);
}
