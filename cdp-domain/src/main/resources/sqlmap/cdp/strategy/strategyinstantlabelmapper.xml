<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="strategyInstantLabelMapper">

    <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyInstantLabelDo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="labelName" column="label_name" jdbcType="VARCHAR"/>
        <result property="frontDesc" column="front_desc" jdbcType="VARCHAR"/>
        <result property="labelDesc" column="label_desc" jdbcType="VARCHAR"/>
        <result property="labelReqParam" column="label_req_param" jdbcType="VARCHAR"/>
        <result property="labelValueType" column="label_value_type" jdbcType="VARCHAR"/>
        <result property="customProcess" column="custom_process" jdbcType="SMALLINT"/>
        <result property="operateType" column="operate_type" jdbcType="VARCHAR"/>
        <result property="fillType" column="fill_type" jdbcType="SMALLINT"/>
        <result property="optional" column="optional" jdbcType="SMALLINT"/>
        <result property="labelType" column="label_type" jdbcType="SMALLINT"/>
        <result property="strategyType" column="strategy_type" jdbcType="SMALLINT"/>
        <result property="excludeType" column="exclude_type" jdbcType="SMALLINT"/>
        <result property="strategyModel" column="strategy_model" jdbcType="SMALLINT"/>
        <result property="dFlag" column="d_flag" jdbcType="SMALLINT"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,label_name,label_desc,front_desc,label_req_param,label_value_type,custom_process,operate_type,fill_type,`optional`,label_type,strategy_type,exclude_type, strategy_model,d_flag,created_time,updated_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_instant_label
        where id = #{id,jdbcType=BIGINT} and d_flag = 0
    </select>


    <select id="selectEventLabelByExcludeTypeAndBusinessType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_instant_label
        where d_flag = 0 and label_type = 1 and find_in_set(#{businessType}, business_type) and strategy_type = #{strategyType} and exclude_type in
        <foreach collection="excludeType" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectCacheInfo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_instant_label
        where d_flag = 0
    </select>

    <select id="selectByLabelTypeAndBusinessType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_instant_label
        where d_flag = 0 and label_type = #{labelType} and find_in_set(#{businessType}, business_type) and strategy_type = #{strategyType}
        <if test="strategyModel != null">
            and strategy_model = #{strategyModel}
        </if>
        <if test="strategyModel == null">
            and strategy_model = 1
        </if>
    </select>

    <select id="getByLabelNameAndLabelType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_instant_label
        where label_name = #{labelName}
        and label_type = #{labelType}
        <if test="strategyType != null">
            and strategy_type = #{strategyType}
        </if>
        <if test="optional != null">
            and optional = #{optional}
        </if>
        and d_flag = 0
        order by id desc
        limit 1
    </select>

    <select id="getByLabelNameList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_instant_label
        <where>
            and d_flag = 0 and label_name in
            <foreach collection="list" index="index" item="labelName" open="(" separator="," close=")">
                #{labelName}
            </foreach>
        </where>
    </select>

    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyInstantLabelDo"
            useGeneratedKeys="true">
        insert into strategy_instant_label
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="labelName != null">label_name,</if>
            <if test="frontDesc != null">front_desc,</if>
            <if test="labelDesc != null">label_desc,</if>
            <if test="labelReqParam != null">label_req_param,</if>
            <if test="labelValueType != null">label_value_type,</if>
            <if test="customProcess != null">custom_process,</if>
            <if test="dFlag != null">d_flag,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updatedTime != null">updated_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="labelName != null">#{labelName,jdbcType=VARCHAR},</if>
            <if test="frontDesc != null">#{frontDesc,jdbcType=VARCHAR},</if>
            <if test="labelDesc != null">#{labelDesc,jdbcType=VARCHAR},</if>
            <if test="labelReqParam != null">#{labelReqParam,jdbcType=VARCHAR},</if>
            <if test="labelValueType != null">#{labelValueType,jdbcType=VARCHAR},</if>
            <if test="customProcess != null">#{customProcess,jdbcType=SMALLINT},</if>
            <if test="dFlag != null">#{dFlag,jdbcType=SMALLINT},</if>
            <if test="createdTime != null">#{createdTime,jdbcType=TIMESTAMP},</if>
            <if test="updatedTime != null">#{updatedTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyInstantLabelDo">
        update strategy_instant_label
        <set>
            <if test="labelName != null">
                label_name = #{labelName,jdbcType=VARCHAR},
            </if>
            <if test="labelDesc != null">
                label_desc = #{labelDesc,jdbcType=VARCHAR},
            </if>
            <if test="frontDesc != null">
                front_desc = #{frontDesc,jdbcType=VARCHAR},
            </if>
            <if test="labelReqParam != null">
                label_req_param = #{labelReqParam,jdbcType=VARCHAR},
            </if>
            <if test="labelValueType != null">
                label_value_type = #{labelValueType,jdbcType=VARCHAR},
            </if>
            <if test="customProcess != null">
                custom_process = #{customProcess,jdbcType=SMALLINT},
            </if>
            <if test="dFlag != null">
                d_flag = #{dFlag,jdbcType=SMALLINT},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedTime != null">
                updated_time = #{updatedTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

</mapper>
