package com.xftech.cdp.adapter.external;

import com.alibaba.fastjson.JSON;
import com.xftech.cdp.api.dto.strategy.StrategyXxlJobParam;
import com.xftech.cdp.infra.constant.XxlJobConstants;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */

@Slf4j
@Component
public class RiskModelScoreDispatch {

    @Autowired
    RiskModelScoreHandle riskModelScoreHandle;

    /**
     * 策略执行
     *
     * @param param 执行参数
     * @return 执行结果
     */
    // @XxlJob(XxlJobConstants.STRATEGY_DISPATCH)
    public ReturnT<String> riskModelScoreDispatch(String param) {
        log.info("riskModelScore begin param:{}", param);
        XxlJobLogger.log("StrategyDispatch begin param:{}", param);
        if (StringUtils.isEmpty(param)) {
            log.warn("riskModelScore param error param:{}", param);
            XxlJobLogger.log("riskModelScore param error param:{}", param);
            return ReturnT.FAIL;
        }
        try {
            StrategyXxlJobParam strategyXxlJobParam = JSON.parseObject(param, StrategyXxlJobParam.class);
            riskModelScoreHandle.execute();
        } catch (Exception e) {
            log.warn("riskModelScore execute error param:{}, e: ", param, e);
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }

        return ReturnT.SUCCESS;
    }
}
