package com.xftech.cdp.infra.client.coupon.model.resp.getactivitycoupon;

import java.util.Date;
import java.util.Objects;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version v1.0 2024/10/25
 * @description GetActivityCouponDetailResponse
 */
@Slf4j
@Data
public class GetActivityCouponDetailResponse {

    /**
     * 券ID
     */
    private String id;
    private String coupon_type;
    private String sub_coupon_type;
    private String name;
    private String status;
    private String product_type;
    private String belongs_to;
    private String discount_type;
    private String product_periods;
    private String discount_periods_type;
    private String discount_periods;
    private String discount_amount;
    private String discount_rate;
    private String valid_days;
    /**
     * 券库存
     */
    private String coupon_count;
    /**
     * 券已发送数量
     */
    private String issued_coupon_count;
    private String lower_price;
    private String app;
    private String utm_source;
    private String is_limitless;
    private String tag;
    private String button_name;
    private String admin_name;
    private String desc;
    private Date created_time;
    private Date updated_time;
    private String discount_category;
    private String discount_rule;
    private String valid_days_type;
    private Date valid_start_time;
    private String valid_start_day;
    private String utm_source_type;
    private String coupon_style;
    private String edit_step;
    private String comment;
    private String discount_periods_limit;
    private String coupon_style_config;
    private String discount_limit;
    private String valid_days_after;
    private String use_amount_rate;
    private String user_type;
    private String send_level;
    /**
     * 活动状态: 0=正常
     */
    private String activity_status;
    /**
     * 券活动ID
     */
    private String activity_id;

    public boolean isValid() {
        if (StringUtils.isAnyBlank(id, activity_id, activity_status, coupon_count, issued_coupon_count)) {
            return false;
        }
        try {
            return StringUtils.equals(activity_status, "0") && (StringUtils.equals(is_limitless, "1") || Integer.parseInt(coupon_count) > Integer.parseInt(issued_coupon_count));
        } catch (Exception e) {
            log.warn("GetActivityCouponDetailResponse isValid error id={} coupon_count={}, issued_coupon_count={}", id, coupon_count, issued_coupon_count);
        }
        return false;
    }

}
