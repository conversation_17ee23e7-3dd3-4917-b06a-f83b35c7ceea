package com.xftech.cdp.infra.client.ads.model.resp;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 风险模型分
 * @link <a href="https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001016260">...</a>
 * <AUTHOR>
 * @since 2023/5/23
 */
@Data
public class DTRiskUserListResp {

    @JSONField(name = "totalNum")
    @JsonProperty("totalNum")
    private Integer totalNum;

    @JSONField(name = "pageSize")
    @JsonProperty("pageSize")
    private Integer pageSize;

    @JSONField(name = "pageNum")
    @JsonProperty("pageNum")
    private Integer pageNum;

    @JSONField(name = "rows")
    @JsonProperty("rows")
    private Rows[] rows;

    @Data
    public static class Rows {

        @JSONField(name = "current_loan_type")
        @JsonProperty("current_loan_type")
        private String currentLoanType;

        @JSONField(name = "id_card_number")
        @JsonProperty("id_card_number")
        private String idCardNumber;

        @JSONField(name = "app")
        @JsonProperty("app")
        private String app;

        @JSONField(name = "name")
        @JsonProperty("name")
        private String name;

        @JSONField(name = "mobile")
        @JsonProperty("mobile")
        private String mobile;

        @JSONField(name = "user_id")
        @JsonProperty("user_id")
        private Integer userId;

        @JSONField(name = "device_id")
        @JsonProperty("device_id")
        private String deviceId;

        @JSONField(name = "id_date_share")
        @JsonProperty("id_date_share")
        private String isDateShare;

        @JSONField(name = "user_status")
        @JsonProperty("user_status")
        private String userStatus;
    }
}