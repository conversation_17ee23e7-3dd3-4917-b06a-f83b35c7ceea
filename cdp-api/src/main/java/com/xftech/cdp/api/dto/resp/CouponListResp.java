package com.xftech.cdp.api.dto.resp;

import com.xftech.cdp.api.dto.base.PageResultResponse;
import lombok.Data;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @date 2023/4/14 18:47
 */
@Data
public class CouponListResp {

    private PageResultResponse<CouponListResp.Coupon> list;

    private List<CouponType> couponTypeList;


    @Data
    public static class Coupon {
        private String activityId;


        private String activityName;


        private String couponName;


        private String couponId;


        private String couponType;

        private String discountAmount;

        private String discountRate;

        private String expiredDays;

        private String rateEffectiveDays;

        private String validDays;
    }

    @Data
    public static class CouponType {

        private String name;

        private Integer value;
    }
}
