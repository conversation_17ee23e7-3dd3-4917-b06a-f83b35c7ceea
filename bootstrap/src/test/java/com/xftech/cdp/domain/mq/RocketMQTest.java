/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.mq;

import com.xftech.cdp.BaseTest;
import com.xftech.cdp.domain.strategy.model.dto.NotifyEventDto;
import com.xinfei.xfframework.common.JsonUtil;
import com.xinfei.xfframework.common.starter.mq.MqTemplate;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 *
 * <AUTHOR>
 * @version $ RocketMQTest, v 0.1 2024/3/15 16:07 yye.xu Exp $
 */

public class RocketMQTest extends BaseTest {

    @Autowired
    private MqTemplate mqTemplate;
    @Test
    public void test_push(){
        NotifyEventDto notifyEventDto = new NotifyEventDto();
        notifyEventDto.setApp("app");
        mqTemplate.syncSend("tp_xyf_cdp_notify:tg_increaseamt", JsonUtil.toJson(notifyEventDto));
    }
}