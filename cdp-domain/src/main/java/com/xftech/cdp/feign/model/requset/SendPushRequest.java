package com.xftech.cdp.feign.model.requset;

import com.xftech.cdp.feign.model.PushUserData;
import lombok.Data;

import java.util.List;

/**
 * push发送请求接口
 *
 * <AUTHOR>
 * @version $ SendPushRequest, v 0.1 2024/1/17 15:06 qu.lu Exp $
 */
@Data
public class SendPushRequest {
    /** app */
    private String app;
    /** innerApp */
    private String innerApp;
    /** 模板id */
    private String templateId;
    /**批次号，如果不传会默认生成一个，如果有传就用传过来的批次号，请务必保证唯一性 */
    private String batchNum;
    /** 推送数据信息，最多只能传1000条一次，注意userId不要重复了，deviceId也不要重复了 */
    private List<PushUserData> pushDataList;
}
