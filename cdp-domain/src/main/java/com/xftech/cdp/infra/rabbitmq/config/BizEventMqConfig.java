package com.xftech.cdp.infra.rabbitmq.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * RabbitMq 配置
 * <AUTHOR>
 * @since 2023-04-06
 */
@Configuration
@RefreshScope
@Getter
@Setter
public class BizEventMqConfig {
    /**
     * mq批量消费大小
     */
    @Value("${spring.rabbitmq.biz.batchSize:50}")
    private Integer batchSize;
    /**
     * mq消费超时时间
     */
    @Value("${spring.rabbitmq.biz.timeout:10000}")
    private Integer timeout;
    /**
     * mq预取消息条数
     */
    @Value("${spring.rabbitmq.biz.prefetchSize:3}")
    private Integer prefetchSize;

    @Value("${spring.rabbitmq.host3}")
    private String host3;
    @Value("${spring.rabbitmq.port3}")
    private int port3;
    @Value("${spring.rabbitmq.username3}")
    private String username3;
    @Value("${spring.rabbitmq.password3}")
    private String password3;
    @Value("${spring.rabbitmq.virtual-host3}")
    private String virtualHost3;

    @Value("${biz.event.hl.exchange}")
    private String bizEventHighLevelExchange;
    @Value("${biz.event.hl.exchangeType}")
    private String bizEventHighLevelExchangeType;
    @Value("${biz.event.hl.routingKey}")
    private String bizEventHighLevelRoutingKey;
    @Value("${biz.event.hl.queue.name}")
    private String bizEventHighLevelQueueName;

    @Value("${biz.event.ml.exchange}")
    private String bizEventMiddleLevelExchange;
    @Value("${biz.event.ml.exchangeType}")
    private String bizEventMiddleLevelExchangeType;
    @Value("${biz.event.ml.routingKey}")
    private String bizEventMiddleLevelRoutingKey;
    @Value("${biz.event.ml.queue.name}")
    private String bizEventMiddleLevelQueueName;

    @Value("${biz.event.atOnce.delay.time}")
    private Long bizEventAtOnceDelayTime;
    @Value("${biz.event.delay.exchange}")
    private String bizEventDelayExchange;
    @Value("${biz.event.delay.xDelayedType}")
    private String bizEventDelayXDelayedType;
    @Value("${biz.event.delay.exchangeType}")
    private String bizEventDelayExchangeType;
    @Value("${biz.event.delay.hl.routingKey}")
    private String bizEventHighLevelDelayRoutingKey;
    @Value("${biz.event.delay.hl.queue.name}")
    private String bizEventHighLevelDelayQueueName;
    @Value("${biz.event.delay.ml.routingKey}")
    private String bizEventMiddleLevelDelayRoutingKey;
    @Value("${biz.event.delay.ml.queue.name}")
    private String bizEventMiddleLevelDelayQueueName;
    @Value("${biz.event.delay.ll.routingKey}")
    private String bizEventLowLevelDelayRoutingKey;
    @Value("${biz.event.delay.ll.queue.name}")
    private String bizEventLowLevelDelayQueueName;

    @Value("${biz.event.dispatch.exchange}")
    private String bizEventDispatchExchange;
    @Value("${biz.event.dispatch.exchangeType}")
    private String bizEventDispatchExchangeType;
    @Value("${biz.event.sms.dispatch.routingKey}")
    private String bizEventSmsDispatchRoutingKey;
    @Value("${biz.event.sms.dispatch.queue.name}")
    private String bizEventSmsDispatchQueueName;
    @Value("${biz.event.tele.dispatch.routingKey}")
    private String bizEventTeleDispatchRoutingKey;
    @Value("${biz.event.tele.dispatch.queue.name}")
    private String bizEventTeleDispatchQueueName;
    @Value("${biz.event.coupon.dispatch.routingKey}")
    private String bizEventCouponDispatchRoutingKey;
    @Value("${biz.event.coupon.dispatch.queue.name}")
    private String bizEventCouponDispatchQueueName;
    @Value("${biz.event.noMarket.dispatch.routingKey}")
    private String bizEventNoMarketRoutingKey;
    @Value("${biz.event.noMarket.dispatch.queue.name}")
    private String bizEventNoMarketQueueName;
    @Value("${biz.event.push.dispatch.routingKey}")
    private String bizEventPushDispatchRoutingKey;
    @Value("${biz.event.push.dispatch.queue.name}")
    private String bizEventPushDispatchQueueName;

    @Value("${biz.event.increaseAmt.dispatch.routingKey:key_biz_event_increaseamt_dispatch_xyf_cdp}")
    private String bizEventIncreaseAmtDispatchRoutingKey;
    @Value("${biz.event.increaseAmt.dispatch.queue.name:queue_biz_event_increaseamt_dispatch_xyf_cdp}")
    private String bizEventIncreaseAmtDispatchQueueName;

    @Value("${biz.event.lifeRights.dispatch.routingKey:key_biz_event_liferights_dispatch_xyf_cdp}")
    private String bizEventLifeRightsDispatchRoutingKey;
    @Value("${biz.event.lifeRights.dispatch.queue.name:queue_biz_event_liferights_dispatch_xyf_cdp}")
    private String bizEventLifeRightsDispatchQueueName;

    @Value("${biz.event.xDayInterestFree.dispatch.routingKey:key_biz_event_xdayinterestfree_dispatch_xyf_cdp}")
    private String bizEventXDayInterestFreeDispatchRoutingKey;
    @Value("${biz.event.xDayInterestFree.dispatch.queue.name:queue_biz_event_xdayinterestfree_dispatch_xyf_cdp}")
    private String bizEventXDayInterestFreeDispatchQueueName;

    @Value("${biz.event.ai.dispatch.routingKey:key_biz_event_ai_dispatch_xyf_cdp}")
    private String bizEventAiDispatchRoutingKey;
    @Value("${biz.event.ai.dispatch.queue.name:queue_biz_event_ai_dispatch_xyf_cdp}")
    private String bizEventAiDispatchQueueName;

    @Value("${biz.event.decision.exchange}")
    private String bizEventDecisionExchange;
    @Value("${biz.event.decision.exchangeType}")
    private String bizEventDecisionExchangeType;
    @Value("${biz.event.decision.routingKey}")
    private String bizEventDecisionRoutingKey;
    @Value("${biz.event.decision.queue.name}")
    private String bizEventDecisionQueueName;
}
