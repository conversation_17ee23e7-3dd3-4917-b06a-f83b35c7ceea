package com.xftech.cdp.infra.rocketmq.rcspt;

import com.xftech.cdp.infra.rocketmq.MessageHandlerSelector;
import com.xinfei.xfframework.common.starter.mq.MqConsumerListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version $ AccessControlDiversionMsgConsumer, v 0.1 2024/11/11 15:02 tianshuo.qiu Exp $
 */
@Component
@Slf4j
@RocketMQMessageListener(consumerGroup = "cg_xyf_cdp_tp_rcspt_risk_access_control_message", topic = "tp_rcspt_risk_access_control_message", consumeMode = ConsumeMode.CONCURRENTLY)
public class AccessControlDiversionMsgConsumer extends MqConsumerListener<String> {
    @Resource
    private MessageHandlerSelector handlerSelector;

    @Override
    public void doMessage(String topic, String bodyMessage, MessageExt messageExt) {
        log.info("AccessControlDiversionMsgConsumer doMessage topic={} messageId={} body={}", topic, messageExt.getMsgId(), bodyMessage);
        try {
            handlerSelector.doMessage(topic, messageExt.getTags(), messageExt.getMsgId(), bodyMessage);
        } catch (Exception e) {
            log.error("AccessControlDiversionMsgConsumer doMessage error topic={} messageId={} body={}", topic, messageExt.getMsgId(), bodyMessage, e);
        }
    }
}
