<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="MqEventFieldMappingConfig">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.mqevent.MqEventFieldMappingConfigDo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="topic" jdbcType="VARCHAR" property="topic"/>
        <result column="consumer" jdbcType="VARCHAR" property="consumer"/>
        <result column="tag" jdbcType="VARCHAR" property="tag"/>
        <result column="event_type" jdbcType="VARCHAR" property="eventType"/>
        <result column="field_mapping" jdbcType="VARCHAR" property="fieldMapping"/>
        <result column="parse" jdbcType="VARCHAR" property="parse"/>
        <result column="d_flag" jdbcType="SMALLINT" property="dFlag"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime"/>
    </resultMap>

    <!-- 通用查询字段 -->
    <sql id="Base_Column_List">
        id
        , topic, consumer, tag,event_type, field_mapping, parse, d_flag, created_time, updated_time
    </sql>

    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.xftech.cdp.infra.repository.cdp.mqevent.MqEventFieldMappingConfigDo"
            useGeneratedKeys="true">
        insert into mq_event_field_mapping_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="topic != null">
                topic,
            </if>
            <if test="consumer != null">
                consumer,
            </if>
            <if test="tag != null">
                tag,
            </if>
            <if test="eventType != null">
                event_type,
            </if>
            <if test="fieldMapping != null">
                field_mapping,
            </if>
            <if test="parse != null">
                parse,
            </if>
            created_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="topic != null">
                #{topic,jdbcType=VARCHAR},
            </if>
            <if test="consumer != null">
                #{consumer,jdbcType=VARCHAR},
            </if>
            <if test="tag != null">
                #{tag,jdbcType=VARCHAR},
            </if>
            <if test="eventType != null">
                #{eventType,jdbcType=VARCHAR},
            </if>
            <if test="fieldMapping != null">
                #{fieldMapping,jdbcType=VARCHAR},
            </if>
            <if test="parse != null">
                #{parse,jdbcType=VARCHAR},
            </if>
            now()
        </trim>
    </insert>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mq_event_field_mapping_config
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByTopicAndConsumer" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from mq_event_field_mapping_config
        where d_flag = 0
          and topic = #{topic}
          and consumer = #{consumer}
          <if test="tag != null and tag != ''">
              and tag = #{tag}
          </if>
    </select>

    <update id="updateByPrimaryKey"
            parameterType="com.xftech.cdp.infra.repository.cdp.mqevent.MqEventFieldMappingConfigDo">
        update mq_event_field_mapping_config
        set topic         = #{topic,jdbcType=VARCHAR},
            consumer      = #{consumer,jdbcType=VARCHAR},
            tag           = #{tag,jdbcType=VARCHAR},
            event_type    = #{eventType,jdbcType=VARCHAR},
            field_mapping = #{fieldMapping,jdbcType=VARCHAR},
            parse         = #{parse,jdbcType=VARCHAR},
            d_flag        = #{dFlag,jdbcType=SMALLINT},
            created_time  = #{createdTime,jdbcType=TIMESTAMP},
            updated_time  = #{updatedTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.xftech.cdp.infra.repository.cdp.mqevent.MqEventFieldMappingConfigDo">
        update mq_event_field_mapping_config
        <set>
            <if test="topic != null">
                topic = #{topic,jdbcType=VARCHAR},
            </if>
            <if test="consumer != null">
                consumer = #{consumer,jdbcType=VARCHAR},
            </if>
            <if test="tag != null">
                tag = #{tag,jdbcType=VARCHAR},
            </if>
            <if test="eventType != null">
                event_type = #{eventType,jdbcType=VARCHAR},
            </if>
            <if test="fieldMapping != null">
                field_mapping = #{fieldMapping,jdbcType=VARCHAR},
            </if>
            <if test="parse != null">
                parse = #{parse,jdbcType=VARCHAR},
            </if>
            <if test="dFlag != null">
                d_flag = #{dFlag,jdbcType=SMALLINT},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedTime != null">
                updated_time = #{updatedTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        update mq_event_field_mapping_config set d_flag=1 where id=#{id,jdbcType=BIGINT};
    </delete>
</mapper>