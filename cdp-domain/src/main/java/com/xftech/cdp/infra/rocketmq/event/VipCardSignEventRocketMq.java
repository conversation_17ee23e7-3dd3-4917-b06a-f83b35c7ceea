/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.rocketmq.event;

import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSONObject;
import com.xftech.cdp.domain.strategy.service.dispatch.event.StrategyEventDispatchService;
import com.xftech.cdp.infra.aviator.util.DateTimeUtil;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventMessageVO;
import com.xftech.cdp.infra.rocketmq.EventEnum;
import com.xftech.cdp.infra.rocketmq.dto.BizEventRocketMessageVO;
import com.xftech.cdp.infra.utils.RedisUtils;
import com.xinfei.xfframework.common.starter.mq.MqConsumerListener;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 *
 * <AUTHOR>
 * @version $ VipCardSignCeremonyEventRocketMq, v 0.1 2024/5/15 14:52 lingang.han Exp $
 */

@Slf4j
@Component
@AllArgsConstructor
@RocketMQMessageListener(consumerGroup = "cg_xyf_cdp_notify_tg_vc_notify", topic = "tp_xyf_cdp_notify", selectorExpression = "tg_vc_notify", consumeMode = ConsumeMode.CONCURRENTLY)
public class VipCardSignEventRocketMq extends MqConsumerListener<String> {

    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private StrategyEventDispatchService strategyEventDispatchService;

    @Override
    protected void doMessage(String s, String s1, MessageExt messageExt) {
        log.info("vipCardSignRocketMqConsumer receive message topic={},messageId={},body={}", s, messageExt.getMsgId(), s1);
        String lockKey = null;
        if (StringUtils.isNotBlank(messageExt.getMsgId())) {
            lockKey = SecureUtil.md5("cg:vcSign:" + messageExt.getMsgId());
        }
        try {
            try {
                if (StringUtils.isNotBlank(lockKey) && !redisUtils.lock(lockKey, "0", 5, TimeUnit.MINUTES)) {
                    log.warn("vipCardSignRocketMqConsumer 重复消息,messageId={},body={}", messageExt.getMsgId(), s1);
                    return;
                }
            } catch (Exception e) {
                log.error("vipCardSignRocketMqConsumer,处理异常", e);
            }
            BizEventRocketMessageVO messageVO = JSONObject.parseObject(s1, BizEventRocketMessageVO.class);
            BizEventMessageVO bizEventMessageVO = transform(messageVO);
            strategyEventDispatchService.prescreen(messageExt.getMsgId(), bizEventMessageVO);
        } catch (Exception e) {
            if (StringUtils.isNotEmpty(lockKey)) {
                redisUtils.unlock(lockKey);
            }
            log.error("vipCardSignRocketMqConsumer consumer error, messageid={},body={}", messageExt.getMsgId(), s1, e);
        }
    }

    private BizEventMessageVO transform(BizEventRocketMessageVO bizEventRocketMessageVO) {
        BizEventMessageVO bizEventMessageVO = new BizEventMessageVO();
        bizEventMessageVO.setAppUserId(bizEventRocketMessageVO.getAppUserId());
        bizEventMessageVO.setApp(bizEventRocketMessageVO.getApp());
        bizEventMessageVO.setInnerApp(bizEventRocketMessageVO.getInnerApp());
        bizEventMessageVO.setTriggerDatetime(DateTimeUtil.formatDateToStr(new Date(bizEventRocketMessageVO.getEventTime()), null));
        BizEventMessageVO.ExtrData extrData = new BizEventMessageVO.ExtrData();
        if (bizEventRocketMessageVO.getExtraData()!= null && Objects.equals(bizEventRocketMessageVO.getExtraData().getType(),5) ){

            BizEventRocketMessageVO.SurpriseRightData surpriseRightData=bizEventRocketMessageVO.getSurpriseRightData();
            String surpriseRightType =surpriseRightData.getSurpriseRightType();
            extrData.setSurpriseRightType(surpriseRightType);


            String surpriseRightLimitAmtCardAmount=surpriseRightData.getSurpriseRightLimitAmtCardAmount();
            if (Objects.equals(surpriseRightType,"2")&& StringUtils.isNotBlank(surpriseRightLimitAmtCardAmount)){
                extrData.setSurpriseRightLimitAmtCardAmount(new BigDecimal(surpriseRightLimitAmtCardAmount));
            }

            String surpriseRightVipFund =surpriseRightData.getSurpriseRightVipFund();
            if (Objects.equals(surpriseRightType,"3") && StringUtils.isNotBlank(surpriseRightVipFund)){
                extrData.setSurpriseRightVipFund(new BigDecimal(surpriseRightVipFund));
            }


            bizEventMessageVO.setExtrData(extrData);
            bizEventMessageVO.setBizEventType(EventEnum.SURPRISE_RIGHT.getEventType());
            return bizEventMessageVO;
        }else{
        extrData.setVcSignType(bizEventRocketMessageVO.getExtraData().getType());
        extrData.setVcSignIsRenew(bizEventRocketMessageVO.getExtraData().getIsRenew());
        extrData.setVcSignCardType(bizEventRocketMessageVO.getExtraData().getCardType());
        extrData.setVcDeductTime(new Date(bizEventRocketMessageVO.getEventTime()));
        extrData.setVcSignOrderPrice(bizEventRocketMessageVO.getExtraData().getOrderPrice());
        extrData.setIsRenewActivity(bizEventRocketMessageVO.getExtraData().getIsRenewActivity());
        extrData.setIsVipFund(bizEventRocketMessageVO.getExtraData().getIsVipFund());
        bizEventMessageVO.setExtrData(extrData);
        bizEventMessageVO.setBizEventType("Notify_VcSign");
        return bizEventMessageVO;
        }
    }
}