package com.xftech.cdp.infra.client.loanmarket;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xftech.cdp.infra.client.loanmarket.config.LoanMarketConfig;
import com.xftech.cdp.infra.client.loanmarket.enums.UtmSourceTypeEnum;
import com.xftech.cdp.infra.client.loanmarket.model.BaseLoanMarketRequester;
import com.xftech.cdp.infra.client.loanmarket.model.GetUtmSourceRequester;
import com.xftech.cdp.infra.client.loanmarket.model.resp.GetUtmSourceAllResp;
import com.xftech.cdp.infra.client.loanmarket.model.resp.GetUtmSourceResp;
import com.xftech.cdp.infra.constant.Constants;
import com.xftech.cdp.infra.constant.RedisKeyConstants;
import com.xftech.cdp.infra.exception.InterfaceException;
import com.xftech.cdp.infra.utils.HttpClientUtil;
import com.xftech.cdp.infra.utils.RedisUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Instant;

@Component
public class LoanMarketClient {

    private static final Logger logger = LoggerFactory.getLogger(LoanMarketClient.class);

    @Autowired
    private LoanMarketConfig loanMarketConfig;
    @Autowired
    private HttpClientUtil httpClientUtil;
    @Autowired
    private RedisUtils redisUtils;

    public GetUtmSourceAllResp getUtmSourceInfo(String utmSource) {
        GetUtmSourceRequester requester = new GetUtmSourceRequester();
        requester.setUtmSource(utmSource);
        String result = request(requester, loanMarketConfig.getUtmSourceInfo());
        return JSON.parseObject(result, GetUtmSourceAllResp.class);
    }

    public GetUtmSourceResp cacheQueryUtmSourceInfo(String utmSource) {
        String redisKey = String.format(RedisKeyConstants.UTM_SOURCE_INFO, utmSource);
        GetUtmSourceResp utmSourceInfo = redisUtils.get(redisKey, GetUtmSourceResp.class);
        if (utmSourceInfo == null) {
            // 接口返回异常码，不可营销
            GetUtmSourceAllResp allResp = getUtmSourceInfo(utmSource);
            if (!allResp.isSuccess()) {
                logger.error("商户接口异常", allResp);
                throw new InterfaceException("商户接口异常");
            }
            utmSourceInfo = allResp.getData();
            redisUtils.set(redisKey, utmSourceInfo, RedisUtils.DEFAULT_EXPIRE_SECONDS * 30);
        }
        return utmSourceInfo;
    }

    private <T extends BaseLoanMarketRequester> String request(T requester, String uri) {
        String url = loanMarketConfig.getLoanMarketHost() + "/" + uri;
        try {
            logger.info("loanMarket send，url：{}，requester：{}", url, JSONObject.toJSON(requester));
            long startTime = Instant.now().toEpochMilli();
            String result = httpClientUtil.postJson(url, requester);
            logger.info("loanMarket send，time：{}ms，return：{}", Instant.now().toEpochMilli() - startTime, result);
            return result;
        } catch (Exception e) {
            logger.error("loanMarket send error，url:{}，params:{}", url, requester, e);
            throw e;
        }
    }
}
