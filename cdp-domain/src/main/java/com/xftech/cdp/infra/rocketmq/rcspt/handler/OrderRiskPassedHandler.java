/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.rocketmq.rcspt.handler;

import com.alibaba.fastjson.JSONObject;
import com.xftech.cdp.domain.mq.MqConsumeService;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventMessageVO;
import com.xftech.cdp.infra.rocketmq.MessageHandler;
import com.xftech.cdp.infra.rocketmq.dto.WithdrawApplyMessageDto;
import com.xftech.cdp.infra.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

import static com.xftech.cdp.infra.rocketmq.EventEnum.ORDER_RISK_PASSED;


@Slf4j
@Component
public class OrderRiskPassedHandler implements MessageHandler {

    @Autowired
    private MqConsumeService mqConsumeService;

    @Override
    public String getTopic() {
        return "tp_rcspt_risk_withdraw_audit_message";
    }

    @Override
    public boolean execute(String messageId, Object message) {
        try {
            WithdrawApplyMessageDto messageVO = JSONObject.parseObject(message.toString(), WithdrawApplyMessageDto.class);
            if (StringUtils.isAnyBlank(messageVO.getApp(),messageVO.getInnerApp(),messageVO.getUserNo(),messageVO.getStatus(),messageVO.getEventDatetime())) {
                log.info("OrderRiskPassedHandler 不符合[订单风险通过事件]事件条件 message={}", JSONObject.toJSONString(messageVO));
                return false;
            }
            BizEventMessageVO bizEventMessageVO = transform(messageVO);
            if (StringUtils.isNotBlank(bizEventMessageVO.getBizEventType())) {
                mqConsumeService.bizEventProcess(messageId, bizEventMessageVO);
                return true;
            } else {
                log.info("OrderRiskPassedHandler execute 不符合订单风险通过事件筛选条件, message={}", JSONObject.toJSONString(messageVO));
                return false;
            }
        } catch (Exception e) {
            log.info("OrderRiskPassedHandler execute error", e);
        }
        return false;
    }

    private BizEventMessageVO transform(WithdrawApplyMessageDto withdrawApplyMessageDto) {
        BizEventMessageVO bizEventMessageVO = new BizEventMessageVO();
        bizEventMessageVO.setApp(withdrawApplyMessageDto.getApp());
        bizEventMessageVO.setInnerApp(withdrawApplyMessageDto.getInnerApp());
        bizEventMessageVO.setAppUserId(Long.valueOf(withdrawApplyMessageDto.getUserNo()));
        bizEventMessageVO.setTriggerDatetime(DateUtil.convertStr(new Date()));
        bizEventMessageVO.setEventDatetime(withdrawApplyMessageDto.getEventDatetime());
        String status = withdrawApplyMessageDto.getStatus();
        if ((Objects.equals(status, "PS"))) {
            bizEventMessageVO.setBizEventType(ORDER_RISK_PASSED.getEventType());
        }
        return bizEventMessageVO;
    }

}