package com.xinfei.touch.infrastructure.repository.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 触达记录实体
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@TableName("touch_record")
public class TouchRecordEntity {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 请求唯一标识
     */
    private String requestId;
    
    /**
     * 批次号
     */
    private String batchNo;
    
    /**
     * 触达类型：1-实时普通，2-实时引擎，3-离线普通，4-离线引擎
     */
    private Integer touchType;
    
    /**
     * 触达渠道：1-短信，2-电销，3-Push，4-优惠券
     */
    private Integer channel;
    
    /**
     * 策略ID
     */
    private Long strategyId;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 业务事件类型
     */
    private String bizEventType;
    
    /**
     * 模板参数JSON
     */
    private Map<String, Object> templateParams;
    
    /**
     * 状态：0-待处理，1-成功，2-失败，3-流控
     */
    private Integer status;
    
    /**
     * 错误码
     */
    private String errorCode;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 发送时间
     */
    private LocalDateTime sendTime;
    
    /**
     * 回执时间
     */
    private LocalDateTime callbackTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
}
