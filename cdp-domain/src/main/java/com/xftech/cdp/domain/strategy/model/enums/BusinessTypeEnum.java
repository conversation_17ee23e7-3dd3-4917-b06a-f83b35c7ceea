package com.xftech.cdp.domain.strategy.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
@Getter
@AllArgsConstructor
public enum BusinessTypeEnum {

    NEW_CUST("new-cust", "新客"),
    OLD_CUST("old-cust", "老客"),
    TEST("test-cust", "测试专用"),
    LOAN_OVERLOAD("loan-overload-cust", "贷超");

    private final String code;
    private final String description;

    public static BusinessTypeEnum getByCode(String code) {
        return Arrays.stream(values())
                .filter(x -> StringUtils.equalsIgnoreCase(x.code, code)).findFirst()
                .orElse(null);
    }

    public static List<String> getAllCodes() {
        return Arrays.stream(values()).map(x -> x.code)
                .collect(Collectors.toList());
    }
}
