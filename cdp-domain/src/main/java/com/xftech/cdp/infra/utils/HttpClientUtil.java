package com.xftech.cdp.infra.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.xftech.base.common.util.LogUtil;
import com.xftech.base.log.IUdpLogger;
import com.xftech.cdp.infra.exception.InfraException;
import com.xftech.cdp.infra.exception.TimeoutException;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.SocketTimeoutException;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * HttpClient 工具类
 */
@Component
public class HttpClientUtil {
    private static final IUdpLogger LOGGER = LogUtil.getLogger(HttpClientUtil.class);

    @Autowired
    private CloseableHttpClient httpClient;

    /**
     * 发送GET请求
     */
    public String get(String path, List<NameValuePair> parametersBody) {
        return getRequest(path, parametersBody, new HashMap<>());
    }

    /**
     * 发送GET请求
     */
    public String get(String path, List<NameValuePair> parametersBody, Map<String, String> headers) {
        return getRequest(path, parametersBody, headers);
    }

    /**
     * 发送POST请求（普通表单形式）
     */
    public String postForm(String path, List<NameValuePair> parametersBody) throws InfraException {
        HttpEntity entity = new UrlEncodedFormEntity(parametersBody, StandardCharsets.UTF_8);
        return postRequest(path, "application/x-www-form-urlencoded", entity, new HashMap<>());
    }

    /**
     * 发送POST请求（普通表单形式）
     */
    public String postForm(String path, List<NameValuePair> parametersBody, Map<String, String> headers) {
        HttpEntity entity = new UrlEncodedFormEntity(parametersBody, StandardCharsets.UTF_8);
        return postRequest(path, "application/x-www-form-urlencoded", entity, headers);
    }

    /**
     * 发送POST请求（JSON形式）
     */
    public <T> String postJson(String path, T param) throws InfraException {
        return postJson(path, param, new HashMap<>());
    }

    /**
     * 发送POST请求（JSON形式）
     */
    public <T> String postJson(String path, T param, Map<String, String> headers) {
        StringEntity entity = new StringEntity(JSON.toJSONString(param, SerializerFeature.SortField), StandardCharsets.UTF_8);
        return postRequest(path, "application/json", entity, headers);
    }

    /**
     * 发送POST请求，请求已经是json，无需序列化方式
     */
    public String postForJson(String path, String param, Map<String, String> headers) {
        StringEntity entity = new StringEntity(param, ContentType.APPLICATION_JSON);
        return postRequest(path, "application/json", entity, headers);
    }


    //----------------------------------------------------------------------------------------------------------------------------------------------------

    /**
     * 发送POST请求
     */
    private String postRequest(String path, String mediaType, HttpEntity entity, Map<String, String> headers) {
        LOGGER.debug("[postRequest] resourceUrl: {}", path);
        HttpPost post = null;
        try {
            post = getHttpPost(path, mediaType, entity, headers);
            HttpResponse response = httpClient.execute(post);
            int code = response.getStatusLine().getStatusCode();
            if (code >= HttpStatus.SC_BAD_REQUEST) {
                throw new InfraException(EntityUtils.toString(response.getEntity()));
            }
            return EntityUtils.toString(response.getEntity());
        } catch (SocketTimeoutException se) {
            throw new TimeoutException(se);
        } catch (ClientProtocolException e) {
            throw new InfraException("postRequest -- Client protocol exception!", e);
        } catch (IOException e) {
            throw new InfraException(HttpStatus.SC_REQUEST_TIMEOUT, "postRequest -- IO error!", e);
        } catch (Exception e) {
            throw new InfraException(e);
        }
    }

    /**
     * 发送GET请求
     */
    private String getRequest(String path, List<NameValuePair> parametersBody, Map<String, String> headers) {
        HttpGet get = null;
        try {
            get = getHttpGet(path, parametersBody, headers);
            HttpResponse response = httpClient.execute(get);
            int code = response.getStatusLine().getStatusCode();
            if (code >= HttpStatus.SC_BAD_REQUEST) {
                throw new InfraException("Could not access protected resource. Server returned http code: " + code);
            }
            return EntityUtils.toString(response.getEntity());
        } catch (URISyntaxException e) {
            throw new InfraException("getRequest -- URI syntax exception!");
        } catch (ClientProtocolException e) {
            throw new InfraException("getRequest -- Client protocol exception!", e);
        } catch (IOException e) {
            throw new InfraException(HttpStatus.SC_REQUEST_TIMEOUT, "getRequest -- IO error!", e);
        } catch (Exception e) {
            throw new InfraException(e);
        } finally {
            Optional.ofNullable(get).ifPresent(HttpRequestBase::releaseConnection);
        }
    }

    private HttpPost getHttpPost(String path, String mediaType, HttpEntity entity, Map<String, String> headers) {
        HttpPost post = new HttpPost(path);
        post.setEntity(entity);
        post.addHeader("Content-Type", mediaType);
        post.addHeader("Accept", "application/json");
        headers.forEach(post::addHeader);
        return post;
    }

    private HttpGet getHttpGet(String path, List<NameValuePair> parametersBody, Map<String, String> headers) throws URISyntaxException {
        URIBuilder uriBuilder = new URIBuilder(path);
        uriBuilder.setParameters(parametersBody);
        HttpGet get = new HttpGet(uriBuilder.build());
        headers.forEach(get::addHeader);
        return get;
    }
}