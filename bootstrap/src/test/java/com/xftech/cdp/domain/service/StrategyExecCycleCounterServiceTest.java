/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.domain.service;

import com.xftech.cdp.BaseTest;
import com.xftech.cdp.domain.strategy.service.StrategyExecCycleCounterService;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyExecCycleCounterDo;
import com.xftech.cdp.infra.utils.JsonUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 *
 * <AUTHOR>
 * @version $ StrategyExecCycleCounterServiceTest, v 0.1 2023/11/6 10:48 yye.xu Exp $
 */

public class StrategyExecCycleCounterServiceTest extends BaseTest {
    @Autowired
    private StrategyExecCycleCounterService service;

    @Test
    public void test() {
        StrategyExecCycleCounterDo strategyExecCycleCounterDo = new StrategyExecCycleCounterDo();
        strategyExecCycleCounterDo.setStrategyId(0L);
        strategyExecCycleCounterDo.setSumVal(1);
        strategyExecCycleCounterDo.setDateValue(20231105);
        service.insert(strategyExecCycleCounterDo);
    }

    @Test
    public void update(){
        int v = service.updateStrategyCycleCounter(0L,20231106);
        System.out.println(v);
        StrategyExecCycleCounterDo strategyExecCycleCounterDo =  service.selectStrategyCycleCounter(0L);
        System.out.println(JsonUtil.toJson(strategyExecCycleCounterDo));
    }
}