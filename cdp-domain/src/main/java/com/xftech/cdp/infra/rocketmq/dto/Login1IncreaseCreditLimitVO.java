package com.xftech.cdp.infra.rocketmq.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0 2024/9/17
 * @description 登录事件消息上报
 */
@Data
public class Login1IncreaseCreditLimitVO {

    /**
     * credit_user.user_id
     * userid 是老的User表的主键ID 是手机号维度的唯一id 后面是会下线不在使用维护的 对应creditUser表里的 user_id字段
     */
    @JsonProperty("user_id")
    @JSONField(name = "user_id")
    private String userId;

    /**
     * credit_user.id
     * userno 是手机号+app唯一的  也是目前用的最广的 后面会持续使用的 等于原来creditUser表的主键ID
     */
    @JsonProperty("app_user_id")
    @JSONField(name = "app_user_id")
    private String appUserId;

    /**
     * 事件类型
     * topic = tp_biz_report_Login1 时固定为 Login1
     * topic = tp_biz_report_Start1 时固定为 Start1
     */
    @JsonProperty("biz_event_type")
    @JSONField(name = "biz_event_type")
    private String bizEventType;

    /**
     * 渠道类型: client-客户端 wap-h5 wxmnp-微信小程序
     */
    @JsonProperty("source_type")
    @JSONField(name = "source_type")
    private String sourceType;

    /**
     * 事件类型: login-登录 start-启动
     */
    private String type;

    /**
     * 客户端渠道: h5/miui/oppo/vivo/ios...
     */
    private String channel;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * xyf用户均以xyf01身份发送
     */
    private String app;

    /**
     * 系统: ios/android/other
     */
    private String os;

    /**
     * 如"xyf01"
     */
    @JsonProperty("inner_app")
    @JSONField(name = "inner_app")
    private String innerApp;

    /**
     * 注册时表中的utm-source, 如"QD-XXL-TT-DY-XYF01-WXH-T3-Z-2-44"
     */
    @JsonProperty("utm_source")
    @JSONField(name = "utm_source")
    private String utmSource;

    /**
     * 发送时间: 如2024-09-03 15:15:22
     */
    @JsonProperty("created_time")
    @JSONField(name = "created_time")
    private String createdTime;

    /**
     * 发行版本号: 如70102
     */
    @JsonProperty("version_code")
    @JSONField(name = "version_code")
    private String versionCode;

    /**
     * 用户登录时的渠道: 如"xyf_app"
     */
    @JsonProperty("login_utm_source")
    @JSONField(name = "login_utm_source")
    private String loginUtmSource;

    /**
     * 真实版本号: 如70103
     */
    @JsonProperty("test_code")
    @JSONField(name = "test_code")
    private String testCode;

    /**
     * 设备ID: 如"20230808b873a86cb9"
     */
    @JsonProperty("device_id")
    @JSONField(name = "device_id")
    private String deviceId;

    /**
     * 设备型号: iPhone 13/HUAWEIALN-AL00/OnePlus PJD110 ...
     */
    @JsonProperty("device_name")
    @JSONField(name = "device_name")
    private String deviceName;

    /**
     * IP地址
     */
    private String ip;

    /**
     * 上报系统: 如"xyf-app-api"
     */
    private String ua;

    /**
     * 登录方式
     * one_click        App易盾登录
     * password         App密码登录
     * sms_code         App短信登录
     * wechat_sms_code  微信短信登录
     * wechat_jscode    微信授权登录
     */
    @JsonProperty("login_type")
    @JSONField(name = "login_type")
    private String loginType;

    /**
     * 请求时间, 如"2024-09-03 15:15:22"
     */
    @JsonProperty("requested_time")
    @JSONField(name = "requested_time")
    private String requestedTime;

    /**
     * 手机号密文
     */
    @JsonProperty("mobile_protyle")
    @JSONField(name = "mobile_protyle")
    private String mobileProtyle;

    /**
     * 手机号密文微信授权标识
     */
    @JsonProperty("wechat_open_id")
    @JSONField(name = "wechat_open_id")
    private String wechatOpenId;

}
