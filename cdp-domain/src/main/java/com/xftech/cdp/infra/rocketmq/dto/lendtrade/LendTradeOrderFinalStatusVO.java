package com.xftech.cdp.infra.rocketmq.dto.lendtrade;

import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version v1.0 2025/1/10
 * @description LendTradeOrderFinalStatusVO
 */
@Data
public class LendTradeOrderFinalStatusVO {

    //private String serialNo;
    /**
     * 借款单号
     */
    private String orderNumber;
    /**
     * 借款金额(元)
     */
    private String loanAmt;
    //private String loanNo;
    /**
     * 借款期数
     */
    private Integer term;
    //private String custNo;
    /**
     * 用户号
     */
    private Long userNo;
    /**
     * 订单状态:
     * 00-待处理
     * 01-风控审核中
     * 11-风控审核通过
     * 20-放款处理中
     * 03-放款成功
     * 04-放款失败
     */
    private String status;
    private String failedCode;
    /**
     * 失败原因大类：
     * 风险失败-risk_failed
     * 资金失败-卡原因-fund_failed
     * 其他原因-other
     * 资金失败-其他原因-fund_failed_other
     */
    private String failedReason;
    //private String productGroupNo;
    //private String productCode;
    //private String subProductCode;
    //private Integer subProductVer;
    private String app;
    private String innerApp;
    //private String utmSource;
    //private String loanPurpose;
    /**
     * 用户综合定价类型. 如A,I
     */
    private String riskPriceType;
    /**
     * 用户综合定价. 如0.36,0.24
     */
    private Double riskPrice;
    //private String contractNumber;
    //private Integer couponId;
    //private String fundSource;
    //private String thirdCode;
    private String createTime;
    private String loanSuccessTime;
    //private String rpyType;
    //private BizData bizData;
    //private Integer system;
    /**
     * 是否复贷:0=首贷, 1=复贷
     */
    private Integer isAgainLoan;
    //private String cnlNo;
    /**
     * 端到端产品码
     */
    private String cnlPdCode;
    //private String cnlEvCode;
    //private ExtInfo extInfo;
    //private String freezeType;
    //private String freezeCode;
    //private String freezeOrderNumber;
    //private String bankcardId;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    static class BizData {
        private String lendConsultInfo;
        private String innerApp;
        private String cnlPdCode;
        private String isStuckOrder;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    static class ExtInfo {
        private String os;
        private String sourceType;
        private String ip;
        private String deviceId;
    }

}


