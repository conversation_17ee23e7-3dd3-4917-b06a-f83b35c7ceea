package com.xftech.cdp.infra.rocketmq;

import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 *
 * <AUTHOR>
 */
public interface MessageHandler {
    default String getTopic() {
        return "default";
    }

    default boolean topicTagCompare(String topic, String tag) {
        return Objects.equals(getTopic(), StringUtils.isBlank(tag) ? topic : topic + "_" + tag);
    }

    boolean execute(String messageId,Object message);
}
