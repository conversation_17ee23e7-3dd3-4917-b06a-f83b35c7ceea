package com.xftech.cdp.infra.repository.cdp.crowd.po;

import com.alibaba.fastjson.JSON;
import com.xftech.cdp.domain.crowd.exception.CrowdException;
import com.xftech.cdp.domain.crowd.model.enums.LabelEnum;
import com.xftech.cdp.domain.crowd.model.label.crowd.CrowdLabelOption;
import com.xftech.cdp.infra.repository.Do;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2023-05-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CrowdLabelSubDo extends Do {


    /**
     * 人群包id
     */
    private Long crowdId;

    /**
     * 人群包圈选二级标签id
     */
    private Long crowdLabelId;

    /**
     * 标签id
     */
    private Long labelId;

    /**
     * 标签值
     */
    private String labelValue;

    /**
     * 执行顺序号
     */
    private Integer execIndex;

    /**
     * 与上一个一级标签的关系，0:或，1:且
     */
    private Integer labelRelation;

    /**
     * 标签逻辑层数
     */
    private Integer logicNum;

    private LabelEnum.LabelOptionTypeEnum labelOptionType;

    public CrowdLabelOption getLabelV(LabelEnum.LabelOptionTypeEnum labelOptionTypeEnum) {
        return JSON.parseObject(labelValue, labelOptionTypeEnum.getClazz());
    }


    public void verify() {
        try {
            CrowdLabelOption crowdLabelOption = JSON.parseObject(labelValue, labelOptionType.getClazz());
            crowdLabelOption.verify();
        } catch (Exception e) {
            throw new CrowdException("数据格式有误：" + e.getMessage());
        }
    }

}
