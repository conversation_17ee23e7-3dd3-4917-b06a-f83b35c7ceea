package com.xftech.cdp.domain.strategy.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.xftech.cdp.domain.strategy.model.enums.StrategyExecStatusEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyMarketChannelEnum;
import com.xftech.cdp.domain.strategy.repository.*;
import com.xftech.cdp.domain.strategy.service.StrategyExecLogService;
import com.xftech.cdp.infra.constant.RedisKeyConstants;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyExecLogDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyGroupDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketChannelDo;
import com.xftech.cdp.infra.utils.RedisUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

/**
 * @<NAME_EMAIL>
 */
@Service
public class StrategyExecLogServiceImpl implements StrategyExecLogService {
    private static final Logger logger = LoggerFactory.getLogger(StrategyExecLogServiceImpl.class);

    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private StrategyRepository strategyRepository;
    @Autowired
    private StrategyExecLogRepository strategyExecLogRepository;
    @Autowired
    private UserDispatchDetailRepository userDispatchDetailRepository;
    @Autowired
    private EventPushBatchRepository eventPushBatchRepository;
    @Autowired
    private UserBlankGroupDetailRepository userBlankGroupDetailRepository;
    @Autowired
    private DecisionRecordRepository decisionRecordRepository;

    /**
     * 获取渠道对应的执行日志（默认当天的执行日志）
     *
     * @param group         策略分组
     * @param marketChannel 当前渠道
     * @return 执行日志
     */
    @Override
    public StrategyExecLogDo channelExecLog(StrategyGroupDo group, StrategyMarketChannelDo marketChannel) {
        Long groupId = group.getId();
        Integer channel = marketChannel.getMarketChannel();

        String logKey = String.format(RedisKeyConstants.EVENT_CHANNEL_EXEC_LOG, getCurrentDate(), groupId, channel);
        if (redisUtils.hasKey(logKey)) {
            String strategyExecLogJson = redisUtils.get(logKey);
            if (StringUtils.isNotBlank(strategyExecLogJson)) {
                return JSON.parseObject(strategyExecLogJson, StrategyExecLogDo.class);
            }
        }

        StrategyExecLogDo execLog = strategyExecLogRepository.selectByGroupIdAndChannelAndDate(groupId, channel, LocalDate.now());
        if (Objects.nonNull(execLog)) {
            return execLog;
        }

        if (redisUtils.lock(String.format(RedisKeyConstants.EVENT_CHANNEL_EXEC_LOG_LOCK, getCurrentDate(), groupId, channel), 1)) {
            execLog = StrategyExecLogDo.beginExecute(group, marketChannel);
            strategyExecLogRepository.insert(execLog);
            redisUtils.set(logKey, JSON.toJSONString(execLog), getSeconds(LocalDateTime.of(LocalDate.now(), LocalTime.MAX)));

            String date = LocalDateTimeUtil.format(execLog.getExecTime(), "yyyyMMdd");
            String execCountKey = String.format(RedisKeyConstants.EVENT_CHANNEL_EXEC_LOG_EXEC_COUNT, date, groupId, channel);
            redisUtils.set(execCountKey, 0, getSeconds(LocalDateTime.of(LocalDate.now().plusDays(1), LocalTime.MAX)));

            String sendCountKey = String.format(RedisKeyConstants.EVENT_CHANNEL_EXEC_LOG_SEND_COUNT, date, groupId, channel);
            redisUtils.set(sendCountKey, 0, getSeconds(LocalDateTime.of(LocalDate.now().plusDays(1), LocalTime.MAX)));

            String successCountKey = String.format(RedisKeyConstants.EVENT_CHANNEL_EXEC_LOG_SUCCESS_COUNT, date, group.getStrategyId(), channel, groupId);
            redisUtils.set(successCountKey, 0, getSeconds(LocalDateTime.of(LocalDate.now().plusDays(1), LocalTime.MAX)));

            String failCountKey = String.format(RedisKeyConstants.EVENT_CHANNEL_EXEC_LOG_FAIL_COUNT, date, group.getStrategyId(), channel, groupId);
            redisUtils.set(failCountKey, 0, getSeconds(LocalDateTime.of(LocalDate.now().plusDays(1), LocalTime.MAX)));
        }
        return execLog;
    }

    private String getCurrentDate() {
        return LocalDateTimeUtil.format(LocalDate.now(), "yyyyMMdd");
    }

    /**
     * 根据ID更新执行日志
     *
     * @param execLogDo 执行日志
     */
    @Override
    public void updateById(StrategyExecLogDo execLogDo) {
        strategyExecLogRepository.updateById(execLogDo);
    }

    /**
     * 当天结束后把日志状态修改为已结束
     */
    @Override
    public void strategyEventRefreshExecLogStatus() {
        LocalDateTime startTime = LocalDateTime.of(LocalDate.now().plusDays(-2), LocalTime.MIN);
        LocalDateTime endTime = LocalDateTime.of(LocalDate.now().plusDays(-1), LocalTime.MAX);
        List<StrategyExecLogDo> list = strategyExecLogRepository.selectEventExecutingRecord(startTime, endTime);
        for (StrategyExecLogDo execLogDo : list) {
            if (LocalDateTime.now().compareTo(execLogDo.getExecTime()) == 0) {
                continue;
            }
            execLogDo.setExecStatus(StrategyExecStatusEnum.FINISHED.getCode());
            this.refreshExecLogCount(execLogDo);
        }
        strategyExecLogRepository.batchUpdate(list);
    }


    @Override
    public void strategyEventExecLogRefresh(String param) {
        LocalDate startDate = LocalDate.now().plusMonths(-1);
        LocalDate endDate = LocalDate.now();

        if (StringUtils.isNotBlank(param)) {
            String[] jobParam = param.split(",");
            startDate = LocalDate.parse(jobParam[0], DateTimeFormatter.ISO_LOCAL_DATE);
            endDate = jobParam.length > 2 ? LocalDate.parse(jobParam[2], DateTimeFormatter.ISO_LOCAL_DATE) : startDate;
        }

        LocalDateTime startTime = LocalDateTime.of(startDate, LocalTime.parse("00:00:00"));
        LocalDateTime endTime = LocalDateTime.of(endDate, LocalTime.parse("23:59:59"));

        List<StrategyDo> eventStrategy = strategyRepository.getAllEventStrategy();
        for (StrategyDo strategyDo : eventStrategy) {
            List<StrategyExecLogDo> execLogList = strategyExecLogRepository.selectByStrategyIdAndExecTimeUngrouped(strategyDo.getId(), startTime, endTime, null);
            execLogList.forEach(this::refreshExecLogCount);
            logger.info("策略{}开始同步缓存", strategyDo.getId());
            if (!CollectionUtils.isEmpty(execLogList)) {
                strategyExecLogRepository.batchUpdate(execLogList);
            }
        }

    }

    @Override
    public List<StrategyExecLogDo> selectTodayByStrategyIds(List<Long> strategyIds) {
        return strategyExecLogRepository.selectByStrategyIdsAndExecTime(strategyIds,
                LocalDateTime.of(LocalDate.now(), LocalTime.MIN),
                LocalDateTime.of(LocalDate.now(), LocalTime.MAX));
    }


    private void refreshExecLogCount(StrategyExecLogDo execLog) {
        LocalDateTime execTime = execLog.getExecTime();
        String tableNameNo = LocalDateTimeUtil.format(execTime, "yyyyMM");
        StrategyMarketChannelEnum channelEnum = StrategyMarketChannelEnum.getInstance(execLog.getStrategyMarketChannel());
        if (channelEnum == StrategyMarketChannelEnum.NONE) {
            int count = userBlankGroupDetailRepository.countUserByChannelIdAndDispatchTime(tableNameNo, execLog.getStrategyMarketChannelId(), execTime.toLocalDate());
            execLog.setExecCount(count);
            logger.info("不营销组：{}，执行总数：{}", execLog.getId(), execLog.getExecCount());
        } else if (channelEnum != StrategyMarketChannelEnum.APP_BANNER) {
            Integer execCount = eventPushBatchRepository.selectCountByStatusAndExecLogId(tableNameNo, execLog.getId(), null);
            Integer sendCount = eventPushBatchRepository.selectCountByStatusAndExecLogId(tableNameNo, execLog.getId(), 1);

            execLog.setExecCount(execCount);
            execLog.setSendCount(sendCount);

            Integer succCount = userDispatchDetailRepository.countArriveByStatusAndExecLogId(tableNameNo, 1, execLog.getId());
            Integer failCount = userDispatchDetailRepository.countArriveByStatusAndExecLogId(tableNameNo, 0, execLog.getId());

            execLog.setSuccCount(succCount);
            execLog.setReceiveCount(succCount + failCount);

            //短信接收人数、供应商发送人数
            if (StrategyMarketChannelEnum.SMS.getCode() == execLog.getStrategyMarketChannel() || StrategyMarketChannelEnum.PUSH.getCode() == execLog.getStrategyMarketChannel()) {
                execLog.setSupplierCount(sendCount);
                execLog.setActualCount(sendCount);
            }

            //电销接收人数
            if (StrategyMarketChannelEnum.VOICE.getCode() == execLog.getStrategyMarketChannel()) {
                execLog.setReceiveCount(succCount + failCount);
            }

            if (StrategyMarketChannelEnum.SALE_TICKET.getCode() == execLog.getStrategyMarketChannel()) {
                //优惠券接收人数
                execLog.setActualCount(succCount + failCount);

                //优惠券使用人数
                Integer useCount = userDispatchDetailRepository.countUsedCountByExecLogId(tableNameNo, 1, execLog.getId());
                execLog.setUsedCount(useCount);
            }

        }
    }

    /**
     * 获取时间区间
     *
     * @param endTime 结束时间
     * @return 时间区间
     */
    private Long getSeconds(LocalDateTime endTime) {
        return Duration.between(LocalDateTime.now(), endTime).getSeconds();
    }
}
