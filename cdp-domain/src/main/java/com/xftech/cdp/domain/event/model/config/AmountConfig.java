package com.xftech.cdp.domain.event.model.config;

import com.xftech.cdp.domain.event.model.annotation.ProcessConfig;
import com.xftech.cdp.domain.event.model.enums.DataProcessEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 金额转换数据处理配置信息
 * <AUTHOR>
 * @version $ ConvertConfig, v 0.1 2024/11/12 17:56 snail Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ProcessConfig(processor = DataProcessEnum.AMOUNT)
public class AmountConfig extends FieldConfig {
    /** 操作方式：toYuan  or  toFen */
    private String operate;
}
