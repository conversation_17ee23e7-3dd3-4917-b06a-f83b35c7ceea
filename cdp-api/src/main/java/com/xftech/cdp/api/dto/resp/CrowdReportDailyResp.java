/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ CrowdReportDailyResp, v 0.1 2023/12/6 13:49 lingang.han Exp $
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CrowdReportDailyResp implements Serializable {

    private static final long serialVersionUID = 192394408932604467L;

    @ApiModelProperty(value = "总人群包数")
    private Integer crowdCount;

    @ApiModelProperty(value = "当日完成执行数")
    private Integer successCount;

    @ApiModelProperty(value = "失败人群数")
    private Integer failCount;

    @ApiModelProperty(value = "总人群数")
    private Long personSum;

    @ApiModelProperty(value = "最近刷新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date refreshTime;

    @ApiModelProperty(value = "执行明细")
    private List<CrowdReportDailyDetailResp> detailList;
}