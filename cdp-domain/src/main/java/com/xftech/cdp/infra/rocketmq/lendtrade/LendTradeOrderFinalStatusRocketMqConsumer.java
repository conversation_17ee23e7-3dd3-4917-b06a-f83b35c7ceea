package com.xftech.cdp.infra.rocketmq.lendtrade;

import javax.annotation.Resource;

import com.xinfei.xfframework.common.starter.mq.MqConsumerListener;

import com.xftech.cdp.infra.rocketmq.MessageHandlerSelector;

import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;

/**
 * 放宽结果MQ消息消费
 */
@Component
@Slf4j
@RocketMQMessageListener(consumerGroup = "cg_xyf_cdp_tp_lendtrade_order_final_status", topic = "tp_lendtrade_order_final_status", consumeMode = ConsumeMode.CONCURRENTLY)
public class LendTradeOrderFinalStatusRocketMqConsumer extends MqConsumerListener<String> {

    @Resource
    private MessageHandlerSelector handlerSelector;

    @Override
    protected void doMessage(String topic, String bodyMessage, MessageExt messageExt) {
        log.info("LendTradeOrderFinalStatusRocketMqConsumer receive message topic={}, messageId={}, body={}", topic, messageExt.getMsgId(), bodyMessage);
        try {
            handlerSelector.doMessage(topic, null, messageExt.getMsgId(), bodyMessage);
        } catch (Exception e) {
            log.error("LendTradeOrderFinalStatusRocketMqConsumer consumer error, topic={} messageId={}, body={}", topic, messageExt.getMsgId(), bodyMessage, e);
        }
    }
}