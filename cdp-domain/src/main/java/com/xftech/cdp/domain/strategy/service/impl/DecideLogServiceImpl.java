/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.service.impl;

import brave.Tracing;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.IdUtil;
import com.xftech.cdp.api.dto.req.DecideReq;
import com.xftech.cdp.domain.strategy.model.enums.StrategyMarketChannelEnum;
import com.xftech.cdp.domain.strategy.repository.DecisionRecordRepository;
import com.xftech.cdp.domain.strategy.repository.UserDispatchDetailRepository;
import com.xftech.cdp.domain.strategy.service.DecideContext;
import com.xftech.cdp.domain.strategy.service.DecideLogService;
import com.xftech.cdp.infra.rabbitmq.enums.DecisionResultEnum;
import com.xftech.cdp.infra.repository.cdp.strategy.po.DecisionRecordDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyGroupDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.UserDispatchDetailDo;
import com.xftech.cdp.infra.utils.JsonUtil;
import com.xftech.cdp.infra.utils.SerialNumberUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;

/**
 *
 * <AUTHOR>
 * @version $ DecideLogServiceImpl, v 0.1 2024/7/27 11:12 benlin.wang Exp $
 */

@Slf4j
@Service
public class DecideLogServiceImpl implements DecideLogService {
    @Autowired
    private SerialNumberUtil serialNumberUtil;
    @Autowired
    private DecisionRecordRepository decisionRecordRepository;
    @Autowired
    private UserDispatchDetailRepository userDispatchDetailRepository;

    @Override
    @Async("decideLogExecutorWrapper")
    public void saveLogs(DecideReq decideReq, List<DecideContext> decideContexts) {
        LocalDateTime now = LocalDateTime.now();
        String tableNumber = LocalDateTimeUtil.format(now, "yyyyMM");
        List<UserDispatchDetailDo> dispatchList = new ArrayList<>(decideContexts.size());
        List<DecisionRecordDo> decisionRecordDoList = new ArrayList<>(decideContexts.size());
        String traceId = "";
        if (Tracing.current().currentTraceContext() != null && Tracing.current().currentTraceContext().get() != null) {
            traceId = Tracing.current().currentTraceContext().get().traceIdString();
        }

        for (DecideContext ctx : decideContexts) {
            String unionId =IdUtil.fastSimpleUUID();
            if (ctx.getDecisionResult().equals(DecisionResultEnum.NONE)) {
                String batchNum = serialNumberUtil.batchNum();
                UserDispatchDetailDo dispatchDetailDo = makeUserDispatchDetailDo(batchNum,
                        ctx.getUserId(),
                        decideReq.getMobile(),
                        null,
                        ctx.getStrategyDo().getId(),
                        null,
                        Objects.nonNull(ctx.getHitCrowdPack()) ? ctx.getHitCrowdPack().getId() : null,
                        ctx.getChannelEnum(),
                        ctx.getStrategyGroup(),
                        now,
                        ctx.getStrategyDo().getBusinessType(),
                        unionId);
                dispatchList.add(dispatchDetailDo);

                DecisionRecordDo decisionRecord = new DecisionRecordDo();
                decisionRecord.setMessageId(batchNum);
                decisionRecord.setApp(decideReq.getApp());
                decisionRecord.setInnerApp(decideReq.getInnerApp());
                decisionRecord.setTraceId(traceId);
                decisionRecord.setEventName(decideReq.getCallingSource().getLabel());
                decisionRecord.setStrategyId(ctx.getStrategyDo().getId());
                decisionRecord.setCreatedTime(now);
                decisionRecord.setDecisionTime(now);
                decisionRecord.setFailCode(DecisionResultEnum.NONE.getFailCode());
                decisionRecord.setTriggerDatetime(now);
                decisionRecord.setDecisionResult(1);
                decisionRecord.setAppUserId(ctx.getUserId());
                if (ctx.getStrategyGroup() != null) {
                    decisionRecord.setGroupId(ctx.getStrategyGroup().getId());
                    decisionRecord.setGroupName(ctx.getStrategyGroup().getName().replaceAll("组", ""));
                }
                decisionRecord.setRegisterTime(now);
                decisionRecord.setRecordType(1);
                decisionRecord.setTableName("decision_record_" + tableNumber);
                decisionRecord.setUnionId(unionId);
                decisionRecordDoList.add(decisionRecord);
            } else {
                DecisionRecordDo decisionRecord = new DecisionRecordDo();
                decisionRecord.setMessageId(serialNumberUtil.batchNum());
                decisionRecord.setApp(decideReq.getApp());
                decisionRecord.setInnerApp(decideReq.getInnerApp());
                decisionRecord.setTraceId(traceId);
                decisionRecord.setEventName(decideReq.getCallingSource().getLabel());
                decisionRecord.setStrategyId(ctx.getStrategyDo().getId());
                decisionRecord.setCreatedTime(now);
                decisionRecord.setDecisionTime(now);
                decisionRecord.setTriggerDatetime(now);
                decisionRecord.setDecisionResult(0);
                if (Objects.nonNull(ctx.getUserParam())) {
                    Map<String, Object> detailMap = new HashMap<>(2);
                    detailMap.put("param", ctx.getUserParam());
                    detailMap.put("expression", ctx.getStrategyLabelExpression());
                    decisionRecord.setDecisionDetail(JsonUtil.toJson(detailMap));
                }

                decisionRecord.setFailCode(ctx.getDecisionResult().getFailCode());
                decisionRecord.setFailReason(ctx.getDecisionResult().getFailReason());
                decisionRecord.setAppUserId(ctx.getUserId());
                if (ctx.getStrategyGroup() != null) {
                    decisionRecord.setGroupId(ctx.getStrategyGroup().getId());
                    decisionRecord.setGroupName(ctx.getStrategyGroup().getName().replaceAll("组", ""));
                }
                decisionRecord.setRegisterTime(now);

                decisionRecord.setRecordType(1);
                decisionRecord.setUnionId(unionId);
                decisionRecord.setTableName("decision_record_" + tableNumber);
                decisionRecordDoList.add(decisionRecord);
            }
        }
        if ( !CollectionUtils.isEmpty(decisionRecordDoList)) {
            decisionRecordRepository.insertBatch(decisionRecordDoList);
        }
        if ( !CollectionUtils.isEmpty(dispatchList)) {
            userDispatchDetailRepository.saveBatchWithoutTx(tableNumber, dispatchList);
        }
    }


    private UserDispatchDetailDo makeUserDispatchDetailDo(String batchNum, Long userId, String mobile, String templateId,
                                                          Long strategyId, Long strategyChannelId, Long crowdPackId,
                                                          StrategyMarketChannelEnum channelEnum, StrategyGroupDo groupDo,
                                                          LocalDateTime triggerDatetime, String bizType, String unionId) {
        UserDispatchDetailDo dispatchDetail = new UserDispatchDetailDo();
        dispatchDetail.setUserId(userId);
        dispatchDetail.setBatchNum(batchNum);
        dispatchDetail.setCrowdPackId(crowdPackId);
        dispatchDetail.setStrategyId(strategyId);
        dispatchDetail.setBizType(bizType);
        dispatchDetail.setStrategyChannelId(strategyChannelId);
        dispatchDetail.setStrategyGroupId(groupDo.getId());
        dispatchDetail.setStrategyGroupName(StringUtils.isNotEmpty(groupDo.getName()) ? groupDo.getName() : "A组");
        dispatchDetail.setMarketChannel(channelEnum.getCode());
        dispatchDetail.setStrategyChannelId(-1L);
        dispatchDetail.setDispatchTime(triggerDatetime);
        dispatchDetail.setTriggerDatetime(triggerDatetime);
        dispatchDetail.setStrategyExecId("0");
        dispatchDetail.setStatus(1);
        dispatchDetail.setTemplateId(templateId);
        dispatchDetail.setUnionId(unionId);
        return dispatchDetail;
    }
}