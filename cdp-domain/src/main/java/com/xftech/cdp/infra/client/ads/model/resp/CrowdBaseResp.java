/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.infra.client.ads.model.resp;

import lombok.Data;
import java.util.Objects;


/**
 *
 * <AUTHOR>
 * @version $ CrowdBaseResp, v 0.1 2023/11/21 14:02 wancheng.qu Exp $
 */

@Data
public class CrowdBaseResp<T> {

    private boolean success;    //true：调用成功，false：调用失败	Boolean
    private String message;//	调用成功为null，失败为失败信息	String
    private T payload;// 人群重跑提交状态	List

    public boolean isSuccess() {
        return success && Objects.nonNull(payload);
    }



}