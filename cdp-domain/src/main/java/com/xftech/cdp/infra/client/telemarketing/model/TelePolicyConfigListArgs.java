/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.client.telemarketing.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xftech.cdp.api.dto.req.PolicyListReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 * @version $ TelePolicyConfigListArgs, v 0.1 2024/1/12 14:08 ****.**** Exp $
 */
@Data
public class TelePolicyConfigListArgs {
    @JsonProperty("policyId")
    @JSONField(name = "policyId")
    private Integer policyId;
    
    @JsonProperty("policyName")
    @JSONField(name = "policyName")
    private String policyName;
    
    @ApiModelProperty("标签类型 tag=标签策略 case=案件策略")
    @JsonProperty("type")
    @JSONField(name = "type")
    private String type;
    
    @JsonProperty("cdpBusinessLine")
    @JSONField(name = "cdpBusinessLine")
    private String cdpBusinessLine;
    
    @JsonProperty("currentPage")
    @JSONField(name = "currentPage")
    private Integer currentPage;
    
    @JsonProperty("pageSize")
    @JSONField(name = "pageSize")
    private Integer pageSize;
    
    
    public TelePolicyConfigListArgs(){}
    
    public TelePolicyConfigListArgs(PolicyListReq policyListReq) {
        if(StringUtils.isNoneBlank(policyListReq.getCdpBusinessLine()))this.cdpBusinessLine = policyListReq.getCdpBusinessLine();
        if(Objects.nonNull(policyListReq.getPolicyName()))this.policyName = policyListReq.getPolicyName();
        if(StringUtils.isNoneBlank(policyListReq.getType()))this.type = policyListReq.getType();
        if(Objects.nonNull(policyListReq.getPolicyId()))this.policyId = policyListReq.getPolicyId();
        this.currentPage = policyListReq.getCurrent();
        this.pageSize = policyListReq.getSize();
    }
}