package com.xftech.cdp.domain.event;

import com.alibaba.fastjson2.JSON;
import com.xftech.cdp.domain.event.model.annotation.ProcessConfig;
import com.xftech.cdp.domain.event.model.config.FieldConfig;
import lombok.extern.slf4j.Slf4j;
import org.reflections.Reflections;
import org.reflections.scanners.SubTypesScanner;
import org.reflections.scanners.TypeAnnotationsScanner;
import org.reflections.util.ClasspathHelper;
import org.reflections.util.ConfigurationBuilder;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * 数据处理器配置信息转换工厂
 * <AUTHOR>
 * @version $ FieldConfigParseFactory, v 0.1 2024/11/13 13:52 snail Exp $
 */
@Slf4j
public class FieldConfigParseFactory {
    /** 需要扫描的数据处理器包路径 */
    private static final Package BASE_CONFIG_PACKAGE = FieldConfig.class.getPackage();
    /** 数据处理器容器 */
    private static final Map<String,Class<?>> CONFIG_CLS_MAP = new HashMap<>();

    static {
        scannerCls();
    }

    /**
     * 将配置信息解析为对应的处理配置类
     * @param dataProcessType 处理器类型
     * @param configContent 处理器配置信息
     * @return 反序列化后的处理器配置信息
     * @param <T> 序列号后的数据类型
     */
    public static <T extends FieldConfig> T parse(String dataProcessType, String configContent){
        if(Objects.isNull(dataProcessType) || Objects.isNull(configContent)){
            log.warn("数据处理器配置信息异常，dataProcessType={},configContent={}",dataProcessType,configContent);
            return null;
        }

        Class<?> cls = CONFIG_CLS_MAP.get(dataProcessType);
        if(Objects.isNull(cls)){
            log.error("can't find data processor,pls check bean is already add @ProcessConfig");
            return null;
        }
        return (T)JSON.parseObject(configContent,cls);
    }

    /** 负责将BASE_CONFIG_PACKAGE路径下所有添加了@ProcessConfig注解的类加载到容器中 */
    private static void scannerCls(){
        Reflections reflections = new Reflections(new ConfigurationBuilder().setUrls(ClasspathHelper.forPackage(BASE_CONFIG_PACKAGE.getName()))
                .setScanners(new TypeAnnotationsScanner(),new SubTypesScanner()));
        Set<Class<?>> classes = reflections.getTypesAnnotatedWith(ProcessConfig.class);

        for (Class<?> cls : classes){
            if(!cls.isAnnotationPresent(ProcessConfig.class)){
                continue;
            }
            ProcessConfig processConfig = cls.getAnnotation(ProcessConfig.class);
            CONFIG_CLS_MAP.put(processConfig.processor().getName(),cls);
        }
    }
}
