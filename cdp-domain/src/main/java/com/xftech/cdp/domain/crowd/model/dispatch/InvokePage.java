package com.xftech.cdp.domain.crowd.model.dispatch;

import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/2/17 10:01
 */
@Data
public class InvokePage {

    private long appUserId;

    private int pageSize = 500;

    private long maxAppUserId;

    private transient BatchConsumer<CrowdDetailDo> batchConsumer;

    public InvokePage() {
    }

    public InvokePage(long appUserId, int pageSize, long maxAppUserId, BatchConsumer<CrowdDetailDo> batchConsumer) {
        this.appUserId = appUserId;
        this.pageSize = pageSize;
        this.maxAppUserId = maxAppUserId;
        this.batchConsumer = batchConsumer;
    }
}
