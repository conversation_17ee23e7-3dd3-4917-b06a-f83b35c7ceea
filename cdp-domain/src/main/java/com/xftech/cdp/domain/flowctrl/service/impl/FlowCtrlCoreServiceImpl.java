package com.xftech.cdp.domain.flowctrl.service.impl;

import com.xftech.cdp.domain.cache.CacheFlowCtrlSerivce;
import com.xftech.cdp.domain.flowctrl.model.dto.FlowCtrlDto;
import com.xftech.cdp.domain.flowctrl.model.dto.UserDispatchIndexDto;
import com.xftech.cdp.domain.flowctrl.service.FlowCtrlCoreService;
import com.xftech.cdp.domain.flowctrl.service.FlowCtrlInterceptionLogService;
import com.xftech.cdp.domain.strategy.model.enums.MarketChannelTypeEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyMarketChannelEnum;
import com.xftech.cdp.domain.strategy.service.UserDispatchDetailService;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.flowctrl.po.FlowCtrlDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketChannelDo;
import com.xftech.cdp.infra.utils.WhitelistSwitchUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * 流控核心逻辑
 *
 * @<NAME_EMAIL>
 * @date 2023/3/24 16:44
 */
@Slf4j
@Service
@Getter
public class FlowCtrlCoreServiceImpl implements FlowCtrlCoreService {

    @Autowired
    private UserDispatchDetailService userDispatchDetailService;
    @Autowired
    private FlowCtrlInterceptionLogService flowCtrlInterceptionLogService;
    @Autowired
    private CacheFlowCtrlSerivce cacheflowCtrlSerivce;

    /**
     * 获取当前策略渠道需要用到的流控规则
     * 新增业务线流控规则
     * @param strategyId 策略ID
     * @param channel    渠道
     * @return 当前策略渠道需要用到的流控规则
     */
    @Override
    public List<FlowCtrlDo> getFlowCtrlRule(Long strategyId, Integer channel, String bizType) {
        List<FlowCtrlDo> flowCtrlConfig = cacheflowCtrlSerivce.getFlowCtrlConfig(channel, strategyId, bizType);
        if(WhitelistSwitchUtil.boolSwitchByApollo("strategyFlowCtrlRuleLogEnable")) {
            log.info("当前策略渠道需要用到的流控规则: {}", flowCtrlConfig);
        }
        return flowCtrlConfig.stream().sorted(Comparator.comparing(FlowCtrlDo::getPriority)).collect(Collectors.toList());
    }

    /**
     * 根据配置的流控规则过滤用户
     *
     * @param flowCtrlDto 流控参数
     * @param statusList  用户状态列表 目前上游固定传 [-1，1]
     * @return 根据流控规则过滤后的用户集合
     */
    @Override
    public List<CrowdDetailDo> flowCtrl(FlowCtrlDto flowCtrlDto, List<Integer> statusList) {
        if (CollectionUtils.isEmpty(flowCtrlDto.getList()) || CollectionUtils.isEmpty(flowCtrlDto.getFlowCtrlRuleList())) {
            log.info("流量控制-当前批次用户数：{}，流控规则：{}", flowCtrlDto.getList().size(), flowCtrlDto.getFlowCtrlRuleList());
            return flowCtrlDto.getList();
        }
        StrategyMarketChannelDo strategyMarketChannelDo = flowCtrlDto.getMarketChannelDo();
        log.info("流控规则执行: 策略id:{}, 流控规则的数量:{}", strategyMarketChannelDo == null ? 0 : strategyMarketChannelDo.getStrategyId(), flowCtrlDto.getFlowCtrlRuleList().size());
        long startTime = Instant.now().toEpochMilli();
        List<Long> passUserIdList = new ArrayList<>();
        // 1.执行流控
        Long strategyId = strategyMarketChannelDo.getStrategyId();
        if(WhitelistSwitchUtil.commonGraySwitchByApollo("" + strategyId, "newFlowCtrlSwitch")) {
            log.info("流控规则执行: 新流控 strategyId:{} marketChannel:{}", strategyId, strategyMarketChannelDo.getMarketChannel());
            passUserIdList = this.newExecute(flowCtrlDto, statusList);
        } else {
            passUserIdList = this.execute(flowCtrlDto, statusList);
        }
        // 2.返回通过流控的用户
        Map<Long, CrowdDetailDo> detailMap = flowCtrlDto.getList().stream().collect(Collectors.toMap(CrowdDetailDo::getUserId, item -> item));
        List<CrowdDetailDo> result = passUserIdList.stream().map(detailMap::get).collect(Collectors.toList());
        log.info("流量控制-当前批次用户数：{}，拦截用户数：{}，当前明细表序号：{}，耗时：{}ms",
                flowCtrlDto.getList().size(), flowCtrlDto.getList().size() - result.size(), flowCtrlDto.getTableNo(), Instant.now().toEpochMilli() - startTime);
        return result;
    }

    @Override
    public List<FlowCtrlDo> getFlowCtrlRulesOnline(Integer strategyType, Long strategyId) {
        return cacheflowCtrlSerivce.getFlowCtrlConfigs(strategyType, strategyId);
    }

    /**
     * 新流控逻辑:策略维度按下发渠道进行单独控制，单渠道跨策略流控规则不变
     * add by fanxu 2025-03-07
     * @param flowCtrlDto
     * @param statusList
     * @return
     */
    private List<Long> newExecute(FlowCtrlDto flowCtrlDto, List<Integer> statusList) {
        // 获取当前渠道，不需要流控直接返回
        StrategyMarketChannelDo channelDo = flowCtrlDto.getMarketChannelDo();
        Integer marketChannel = channelDo.getMarketChannel();
        if (isNotFlowCtrlChannel(marketChannel)) { // 新增全局频控渠道
            log.info("newExecute当前渠道不需要流控直接返回 strategyId:{} marketChannel:{}", channelDo.getStrategyId(), marketChannel);
            return Collections.emptyList();
        }

        List<Long> resultList = flowCtrlDto.getList().stream().map(CrowdDetailDo::getUserId).collect(Collectors.toCollection(CopyOnWriteArrayList::new));
        Map<Long, String> userUnionIdMap = flowCtrlDto.getList().stream().filter(crowdDetailDo -> crowdDetailDo.getUnionId() != null)
                .collect(Collectors.toMap(CrowdDetailDo::getUserId, CrowdDetailDo::getUnionId));
        log.info("newExecute userUnionIdMap {}", userUnionIdMap);
        for (FlowCtrlDo flowCtrlDo : flowCtrlDto.getFlowCtrlRuleList()) {
            List<ImmutablePair<Long, Long>> flowCtrlRefuseList = new ArrayList<>();
            if (CollectionUtils.isEmpty(resultList)) {
                continue;
            }
            // 查询用户触达指标
            Long strategyId = flowCtrlDto.getMarketChannelDo().getStrategyId();
            List<UserDispatchIndexDto> dispatchIndexList = userDispatchDetailService.getUserIndexNew(flowCtrlDto.getTableNo(), Triple.of(strategyId, marketChannel, flowCtrlDo), resultList, statusList);
            // 获取被拦截的用户
            dispatchIndexList = dispatchIndexList.stream().filter(index -> this.interception(flowCtrlDo, index)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(dispatchIndexList)) {
                for (UserDispatchIndexDto index : dispatchIndexList) {
                    // 把被流控的用户添加到流控集合
                    flowCtrlRefuseList.add(ImmutablePair.of(flowCtrlDo.getId(), index.getUserId()));
                    // 从结果集中移除被流控的用户
                    resultList.remove(index.getUserId());
                }
                // 保存拦截日志
                this.saveInterceptionLogNew(flowCtrlDto, flowCtrlRefuseList, userUnionIdMap);
            }
        }
        // 返回通过流控的用户
        return resultList;
    }

    private boolean isNotFlowCtrlChannel(Integer channelCode) {
        List<Integer> noFlcCodeList = StrategyMarketChannelEnum.getNoFlcCodes();

        return noFlcCodeList.contains(channelCode);
    }

    /**
     * 匹配流控逻辑
     *
     * @param flowCtrlDto 流控参数
     * @return ImmutablePair<List < Long>, List<ImmutablePair<Long, Long>>> left-通过的用户 right-被流控的用户
     */
    private List<Long> execute(FlowCtrlDto flowCtrlDto, List<Integer> statusList) {
        List<Long> resultList = flowCtrlDto.getList().stream().map(CrowdDetailDo::getUserId).collect(Collectors.toCollection(CopyOnWriteArrayList::new));
        Map<Long, String> userUnionIdMap = flowCtrlDto.getList().stream().filter(crowdDetailDo -> crowdDetailDo.getUnionId() != null)
                .collect(Collectors.toMap(CrowdDetailDo::getUserId, CrowdDetailDo::getUnionId));
        for (FlowCtrlDo flowCtrlDo : flowCtrlDto.getFlowCtrlRuleList()) {
            List<ImmutablePair<Long, Long>> flowCtrlRefuseList = new ArrayList<>();
            if (CollectionUtils.isEmpty(resultList)) {
                continue;
            }
            // 查询用户触达指标
            Long strategyId = flowCtrlDto.getMarketChannelDo().getStrategyId();
            Integer marketChannel = flowCtrlDto.getMarketChannelDo().getMarketChannel();
            List<UserDispatchIndexDto> dispatchIndexList = userDispatchDetailService.getUserIndex(flowCtrlDto.getTableNo(), Triple.of(strategyId, marketChannel, flowCtrlDo), resultList, statusList);
            // 获取被拦截的用户
            dispatchIndexList = dispatchIndexList.stream().filter(index -> this.interception(flowCtrlDo, index)).collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(dispatchIndexList)) {
                for (UserDispatchIndexDto index : dispatchIndexList) {
                    // 把被流控的用户添加到流控集合
                    flowCtrlRefuseList.add(ImmutablePair.of(flowCtrlDo.getId(), index.getUserId()));
                    // 从结果集中移除被流控的用户
                    resultList.remove(index.getUserId());
                }
                // 保存拦截日志
                this.saveInterceptionLog(flowCtrlDto, flowCtrlRefuseList, userUnionIdMap);
            }
        }
        // 返回通过流控的用户
        return resultList;
    }

    /**
     * 用户指标与流控配置比较
     *
     * @param rule     流控配置
     * @param indexDto 用户指标
     * @return 是否流控 true-拦截 false-通过
     */
    private boolean interception(FlowCtrlDo rule, UserDispatchIndexDto indexDto) {
        return Objects.nonNull(rule.getLimitTimes()) && indexDto.getCount() >= rule.getLimitTimes();
    }

    /**
     * 保存拦截日志
     *
     * @param flowCtrlDto        流控参数
     * @param flowCtrlRefuseList 被流控的用户集合
     */
    private void saveInterceptionLog(FlowCtrlDto flowCtrlDto, List<ImmutablePair<Long, Long>> flowCtrlRefuseList, Map<Long, String> userUnionIdMap) {
        flowCtrlInterceptionLogService.saveInterceptionLog(flowCtrlDto, flowCtrlRefuseList, userUnionIdMap);
    }

    /**
     * 保存拦截日志-新增流控版本字段
     *
     * @param flowCtrlDto        流控参数
     * @param flowCtrlRefuseList 被流控的用户集合
     */
    private void saveInterceptionLogNew(FlowCtrlDto flowCtrlDto, List<ImmutablePair<Long, Long>> flowCtrlRefuseList, Map<Long, String> userUnionIdMap) {
        flowCtrlInterceptionLogService.saveInterceptionLogNew(flowCtrlDto, flowCtrlRefuseList, userUnionIdMap);
    }
}
