<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="statOfflineStrategyGroupDataMapper">

    <resultMap id="BaseResultMap" type="com.xftech.cdp.domain.stat.entity.StatOfflineStrategyGroupDataEntity">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="bizDate" column="biz_date" jdbcType="TIMESTAMP"/>
        <result property="strategyFlowDataId" column="strategy_flow_data_id" jdbcType="BIGINT"/>
        <result property="strategyId" column="strategy_id" jdbcType="BIGINT"/>
        <result property="strategyGroupId" column="strategy_group_id" jdbcType="BIGINT"/>
        <result property="groupName" column="group_name" jdbcType="VARCHAR"/>
        <result property="groupUserNum" column="group_user_num" jdbcType="INTEGER"/>
        <result property="flowControlNum" column="flow_control_num" jdbcType="INTEGER"/>
        <result property="filterLabelNum" column="filter_label_num" jdbcType="INTEGER"/>
        <result property="filterExcludeNum" column="filter_exclude_num" jdbcType="INTEGER"/>
        <result property="dispatchNum" column="dispatch_num" jdbcType="INTEGER"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,biz_date,strategy_flow_data_id,
        strategy_id,strategy_group_id,group_name,
        group_user_num,flow_control_num,filter_label_num,
        filter_exclude_num,dispatch_num,created_time,updated_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from stat_offline_strategy_group_data
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByStrategyIdAndFlowDataId" parameterType="java.util.Map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from stat_offline_strategy_group_data
        where strategy_id = #{strategyId} and strategy_flow_data_id = #{strategyFlowDataId} and d_flag = 0
    </select>

    <select id="getByStrategyGroupIdAndBizDateAndFlowDataId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from stat_offline_strategy_group_data
        where strategy_group_id = #{strategyGroupId}
        and biz_date = #{bizDate}
        and strategy_flow_data_id = #{strategyFlowDataId}
        and d_flag = 0
    </select>

    <select id="getLastByStrategyGroupIdAndBizDate" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from stat_offline_strategy_group_data
        where strategy_group_id = #{strategyGroupId}
        and biz_date &gt;= current_date()
        and d_flag = 0
        order by id desc
        limit 1
    </select>

    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.xftech.cdp.domain.stat.entity.StatOfflineStrategyGroupDataEntity"
            useGeneratedKeys="true">
        insert into stat_offline_strategy_group_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="bizDate != null">biz_date,</if>
            <if test="strategyFlowDataId != null">strategy_flow_data_id,</if>
            <if test="strategyId != null">strategy_id,</if>
            <if test="strategyGroupId != null">strategy_group_id,</if>
            <if test="groupName != null">group_name,</if>
            <if test="groupUserNum != null">group_user_num,</if>
            <if test="flowControlNum != null">flow_control_num,</if>
            <if test="filterLabelNum != null">filter_label_num,</if>
            <if test="filterExcludeNum != null">filter_exclude_num,</if>
            <if test="dispatchNum != null">dispatch_num,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updatedTime != null">updated_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="bizDate != null">#{bizDate,jdbcType=TIMESTAMP},</if>
            <if test="strategyFlowDataId != null">#{strategyFlowDataId,jdbcType=BIGINT},</if>
            <if test="strategyId != null">#{strategyId,jdbcType=BIGINT},</if>
            <if test="strategyGroupId != null">#{strategyGroupId,jdbcType=BIGINT},</if>
            <if test="groupName != null">#{groupName,jdbcType=VARCHAR},</if>
            <if test="groupUserNum != null">#{groupUserNum,jdbcType=INTEGER},</if>
            <if test="flowControlNum != null">#{flowControlNum,jdbcType=INTEGER},</if>
            <if test="filterLabelNum != null">#{filterLabelNum,jdbcType=INTEGER},</if>
            <if test="filterExcludeNum != null">#{filterExcludeNum,jdbcType=INTEGER},</if>
            <if test="dispatchNum != null">#{dispatchNum,jdbcType=INTEGER},</if>
            <if test="createdTime != null">#{createdTime,jdbcType=TIMESTAMP},</if>
            <if test="updatedTime != null">#{updatedTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.xftech.cdp.domain.stat.entity.StatOfflineStrategyGroupDataEntity">
        update stat_offline_strategy_group_data
        <set>
            <if test="bizDate != null">
                biz_date = #{bizDate,jdbcType=TIMESTAMP},
            </if>
            <if test="strategyFlowDataId != null">
                strategy_flow_data_id = #{strategyFlowDataId,jdbcType=BIGINT},
            </if>
            <if test="strategyId != null">
                strategy_id = #{strategyId,jdbcType=BIGINT},
            </if>
            <if test="strategyGroupId != null">
                strategy_group_id = #{strategyGroupId,jdbcType=BIGINT},
            </if>
            <if test="groupName != null">
                group_name = #{groupName,jdbcType=VARCHAR},
            </if>
            <if test="groupUserNum != null">
                group_user_num = #{groupUserNum,jdbcType=INTEGER},
            </if>
            <if test="flowControlNum != null">
                flow_control_num = #{flowControlNum,jdbcType=INTEGER},
            </if>
            <if test="filterLabelNum != null">
                filter_label_num = #{filterLabelNum,jdbcType=INTEGER},
            </if>
            <if test="filterExcludeNum != null">
                filter_exclude_num = #{filterExcludeNum,jdbcType=INTEGER},
            </if>
            <if test="dispatchNum != null">
                dispatch_num = #{dispatchNum,jdbcType=INTEGER},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedTime != null">
                updated_time = #{updatedTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

</mapper>
