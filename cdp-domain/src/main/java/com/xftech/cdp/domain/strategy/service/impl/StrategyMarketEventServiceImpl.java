package com.xftech.cdp.domain.strategy.service.impl;

import com.xftech.cdp.domain.strategy.repository.StrategyMarketEventRepository;
import com.xftech.cdp.domain.strategy.service.StrategyMarketEventService;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketEventDo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @<NAME_EMAIL>
 */
@Slf4j
@Service
public class StrategyMarketEventServiceImpl implements StrategyMarketEventService {

    @Autowired
    private StrategyMarketEventRepository strategyMarketEventRepository;

    /**
     * 获取所有该事件对应的配置
     *
     * @param bizEventType 事件名称
     * @return 所有配置
     */
    @Override
    public List<StrategyMarketEventDo> getByEventName(String bizEventType) {
        return strategyMarketEventRepository.getByEventName(bizEventType);
    }
}
