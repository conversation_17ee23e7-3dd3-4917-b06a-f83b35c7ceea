/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.repository;

import com.xftech.cdp.BaseTest;
import com.xftech.cdp.domain.crowd.repository.CrowdWereHouseSnapshotRepository;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdWereHouseSnapshotDo;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 *
 * <AUTHOR>
 * @version $ CrowdWereHouseSnapshotRepositoryTest, v 0.1 2023/11/16 16:15 yye.xu Exp $
 */

public class CrowdWereHouseSnapshotRepositoryTest extends BaseTest {

    @Autowired
    private CrowdWereHouseSnapshotRepository crowdWereHouseSnapshotRepository;

    @Test
    public void test_select(){
       /* CrowdWereHouseSnapshotDo crowdWereHouseSnapshotDo =
                crowdWereHouseSnapshotRepository.getTodayCrowd();
        if (crowdWereHouseSnapshotDo != null){
            System.out.println("false");
        }*/
    }

}