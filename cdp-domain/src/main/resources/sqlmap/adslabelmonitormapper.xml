<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="adsLabelMonitor">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.ads.po.AdsLabelMonitorDfDo">
        <result column="data_date" property="dataDate" />
<!--        <result column="is_success" property="isSuccess" />-->
        <result column="online_flag" property="onlineFlag" />
        <result column="table_name" property="tableName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        data_date,online_flag,`table_name`
    </sql>

    <select id="selectExistByDataDate" resultType="java.lang.Long">
        select count(1) from ads_label_monitor_df
        where online_flag = #{onlineFlag} and table_name = #{tableName} and data_date &gt;= #{dataDate} limit 1
    </select>
</mapper>
