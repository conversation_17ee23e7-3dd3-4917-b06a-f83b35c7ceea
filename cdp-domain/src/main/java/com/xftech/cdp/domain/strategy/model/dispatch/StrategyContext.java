package com.xftech.cdp.domain.strategy.model.dispatch;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xftech.cdp.api.dto.req.DispatchConfig;
import com.xftech.cdp.api.dto.resp.CyclePreviewDto;
import com.xftech.cdp.distribute.crowd.repository.model.CrowdSliceDo;
import com.xftech.cdp.domain.crowd.model.dispatch.CrowdContext;
import com.xftech.cdp.domain.strategy.model.dto.StrategyCycleDayConfig;
import com.xftech.cdp.domain.strategy.service.StrategyService;
import com.xftech.cdp.infra.client.randomnum.RandomNumClient;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.flowctrl.po.FlowCtrlDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyExecLogDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyGroupDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketChannelDo;
import com.xftech.cdp.infra.utils.JsonUtil;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutableTriple;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/2/20
 */
@Getter
@Setter
@Slf4j
public class StrategyContext {
    /**
     * 策略
     */
    private StrategyDo strategyDo;
    /**
     * 分组配置
     */
    private StrategyGroupDo strategyGroupDo;
    /**
     * 触达渠道
     */
    private StrategyMarketChannelDo strategyMarketChannelDo;
    /**
     * 执行日志
     */
    private StrategyExecLogDo strategyExecLogDo;
    /**
     * 流控规则
     */
    private List<FlowCtrlDo> flowCtrlList;
    /**
     * 人群包ID集合
     */
    private List<Long> crowdIds;
    /**
     * 人群包属性
     */
    private Map<Long, CrowdContext> crowdContent;

    /**
     * 左：分组人数
     * 中：应发人数
     * 右：实际发送总数
     */
    private ImmutableTriple<Integer, Integer, Integer> countTriple;

    /**
     * 当前策略执行，下发明细需要保存的表的序号 yyyy_MM
     */
    private String detailTableNo;
    /**
     * 是否完成下发
     */
    private boolean completeDispatch = false;

    private CycleStrategy cycleStrategy;

    /**
     * 策略执行失败原因
     */
    private String failReason;

    private FlowContext flowContext;

    private boolean notCheckFlowUserCount;

    private String engineCode;

    private List<CrowdSliceDo> crowdSliceList;

    private StrategyContext() {

    }

    @JsonIgnore
    @JSONField(serialize = false)
    public boolean isCycleStrategy() {
        return cycleStrategy != null;
    }
    public static StrategyContext initContext(StrategyDo strategyDo, StrategyGroupDo strategyGroupDo, StrategyMarketChannelDo strategyMarketChannelDo,
                                              List<FlowCtrlDo> flowCtrlList, List<Long> crowdIds, CycleStrategy cycleStrategy,Map<Long, CrowdContext> crowdContent,
                                              FlowContext flowContext) {
        StrategyContext strategyContext = new StrategyContext();
        strategyContext.setStrategyDo(strategyDo);
        strategyContext.setStrategyGroupDo(strategyGroupDo);
        strategyContext.setStrategyMarketChannelDo(strategyMarketChannelDo);
        strategyContext.setCrowdIds(crowdIds);
        strategyContext.setFlowCtrlList(flowCtrlList);
        strategyContext.setDetailTableNo(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMM")));
        strategyContext.setCycleStrategy(cycleStrategy);
        strategyContext.setCrowdContent(crowdContent);
        strategyContext.setFlowContext(flowContext);
        return strategyContext;
    }

    public static StrategyContext initContext(StrategyDo strategyDo, StrategyGroupDo strategyGroupDo, StrategyMarketChannelDo strategyMarketChannelDo,
                                              List<FlowCtrlDo> flowCtrlList, List<Long> crowdIds, CycleStrategy cycleStrategy,Map<Long, CrowdContext> crowdContent,
                                              FlowContext flowContext, List<CrowdSliceDo> crowdSliceList) {
        StrategyContext strategyContext = new StrategyContext();
        strategyContext.setStrategyDo(strategyDo);
        strategyContext.setStrategyGroupDo(strategyGroupDo);
        strategyContext.setStrategyMarketChannelDo(strategyMarketChannelDo);
        strategyContext.setCrowdIds(crowdIds);
        strategyContext.setFlowCtrlList(flowCtrlList);
        strategyContext.setDetailTableNo(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMM")));
        strategyContext.setCycleStrategy(cycleStrategy);
        strategyContext.setCrowdContent(crowdContent);
        strategyContext.setFlowContext(flowContext);
        strategyContext.setCrowdSliceList(crowdSliceList);
        return strategyContext;
    }

    @Data
    public static class CycleStrategy {
        private Integer cycleNum;
        private Integer currCycleNum;
        private StrategyCycleDayConfig cycleDayConfig;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FlowContext {
        private boolean isFlow;
        private boolean isRootNode;
        private String flowNo;
        private long nextStrategyId;
        private String batchNo;
        private int dayValue;
        // 默认上游下发的用户
        private DispatchConfig.FlowDispatchConfig flowDispatchConfig;
    }

    @JSONField(serialize = false)
    @JsonIgnore
    public boolean isFlow(){
        return flowContext != null && flowContext.isFlow;
    }

    @JSONField(serialize = false)
    @JsonIgnore
    public boolean isFlowRootNode(){
        return isFlow() && flowContext.isRootNode;
    }

    @JSONField(serialize = false)
    @JsonIgnore
    public Map getExtDataMap() {
        if (isFlow()) {
            if (StringUtils.isNotEmpty(flowContext.flowNo)) {
                Map<String, Object> data = new HashMap<>();
                data.put("flowNo", flowContext.getFlowNo());
                data.put("flowBatchNo", flowContext.getBatchNo());
                data.put("isRootNode", flowContext.isRootNode());

                Map<String, Object> objectMap = new HashMap<>();
                objectMap.put("flow", data);
                return objectMap;
            }
        }
        return null;
    }

    public List<CrowdDetailDo> cycleFilter(List<CrowdDetailDo> detailList, StrategyService strategyService, RandomNumClient randomNumClient) {
        if (CollectionUtils.isEmpty(detailList)) {
            return detailList;
        }
        if (this.isCycleStrategy()) {
            StrategyContext.CycleStrategy cycleStrategy = this.getCycleStrategy();
            List<CyclePreviewDto> cycleList = strategyService.cyclePreview(cycleStrategy.getCycleNum());
            CyclePreviewDto.Range range = cycleList.stream().filter(x -> Objects.equals(x.getOrder(), cycleStrategy.getCurrCycleNum()))
                    .map(CyclePreviewDto::getRange)
                    .findFirst()
                    .orElse(null);
            log.info("周期策略信息, 开始过滤, 渠道id:{}, 策略id:{}, 总周期:{}, 本周期:{}, 当前随机数值区间:{}", this.getStrategyMarketChannelDo().getId(),
                    this.getStrategyDo().getId(),
                    cycleStrategy.getCycleNum(), cycleStrategy.getCurrCycleNum(), JsonUtil.toJson(range));
            Set<Long> userIdSet = detailList.stream().map(CrowdDetailDo::getUserId).collect(Collectors.toSet());
            Map<Long, String> randomNumMap = randomNumClient.randomNumber(cycleStrategy.getCycleDayConfig().getBizKey(), new ArrayList<>(userIdSet), null);
            return detailList.stream().filter(x -> {
                String ab = randomNumMap.get(x.getUserId());
                if (StringUtils.isEmpty(ab)) {
                    return false;
                }
                assert range != null;
                if (Integer.parseInt(range.getBegin()) <= Integer.parseInt(ab) &&
                        Integer.parseInt(range.getEnd()) >= Integer.parseInt(ab)) {
                    return true;
                }
                return false;
            }).collect(Collectors.toList());
        }
        return detailList;
    }

}
