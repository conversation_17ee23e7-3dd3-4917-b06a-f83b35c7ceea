/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.req.label;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version $ ClearCheckResultReq, v 0.1 2024/6/25 11:46 lingang.han Exp $
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ClearCheckResultReq implements Serializable {
    private static final long serialVersionUID = 5462108363946611645L;

    @ApiModelProperty(value = "元数据标签code")
    @NotNull
    private Long metaLabelId;

}