/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.infra.client.telemarketing.model.resp;

import lombok.Data;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ TeleNameTypeArgs, v 0.1 2023/10/13 11:58 wancheng.qu Exp $
 */
@Data
public class TeleNameTypeResp extends BaseNewTeleResp<TeleNameTypeResp.ResponseData> {
    @Data
    public static class ResponseData {
        private Integer currentPage;
        private List<Item> list;
        private Integer pageSize;
        private Integer total;
        private Integer totalPage;
        @Data
        public static class Item {
            private String businessType;
            private String createBy;
            private String createdTime;
            private Integer id;
            private Object params;
            private String remark;
            private Integer status;     //状态1:启用,2:禁用
            private String typeName;
            private String updateBy;
            private String updatedTime;
        }
    }

}






