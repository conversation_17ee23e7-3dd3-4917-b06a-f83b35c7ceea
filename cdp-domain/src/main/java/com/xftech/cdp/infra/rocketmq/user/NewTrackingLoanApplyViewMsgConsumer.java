package com.xftech.cdp.infra.rocketmq.user;

import com.xftech.cdp.infra.rocketmq.EventMessageBaseProcessor;
import com.xftech.cdp.infra.rocketmq.MessageHandlerSelector;
import com.xftech.cdp.infra.utils.WhitelistSwitchUtil;

import com.xinfei.xfframework.common.starter.mq.MqConsumerListener;
import groovy.util.logging.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version $ TrackingLoanApplyViewMsgConsumer, v 0.1 2025/3/14 13:40 tianshuo.qiu Exp $
 */
@Slf4j
@Component
@RocketMQMessageListener(consumerGroup = "cg_xyf_cdp_tp_cis_event_report_1003806", topic = "tp_cis_event_report_1003806", consumeMode = ConsumeMode.CONCURRENTLY)
public class NewTrackingLoanApplyViewMsgConsumer extends MqConsumerListener<String> {
    private static final Logger log = LoggerFactory.getLogger(NewTrackingLoanApplyViewMsgConsumer.class);
    @Autowired
    private MessageHandlerSelector handlerSelector;

    @Override
    protected void doMessage(String topic, String bodyMessage, MessageExt messageExt) {
        if (WhitelistSwitchUtil.boolSwitchByApollo("NewTrackingLoanApplyViewMsgConsumerEnable")) {
            log.info("NewTrackingLoanApplyViewMsgConsumer receive message topic={},messageId={},body={}", topic, messageExt.getMsgId(), bodyMessage);
            try {
                handlerSelector.doMessage(topic, null, messageExt.getMsgId(), bodyMessage);
            } catch (Exception e) {
                log.error("NewTrackingLoanApplyViewMsgConsumer consumer error,topic={} messageId={},body={}", topic, messageExt.getMsgId(), bodyMessage, e);
            }
        }
    }
}
