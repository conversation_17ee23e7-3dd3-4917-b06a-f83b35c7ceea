package com.xftech.cdp.domain.strategy.model.enums;

import lombok.Getter;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 *
 * <AUTHOR>
 */
@Getter
public enum ReportDailyTypeEnum {

    CROWD(1, "人群包"),

    STRATEGY(2, "离线策略");

    private final Integer code;
    private final String desc;

    ReportDailyTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
