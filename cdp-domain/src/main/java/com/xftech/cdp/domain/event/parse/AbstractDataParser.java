package com.xftech.cdp.domain.event.parse;

import com.xftech.cdp.domain.event.model.annotation.Parser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 消息体解析的基类
 * <AUTHOR>
 * @version $ AbstractDataParser, v 0.1 2024/11/15 14:39 snail Exp $
 */
@Slf4j
public abstract class AbstractDataParser implements InitializingBean,DataParser {
    @Autowired
    private DataParserStrategy dataParserStrategy;

    @Override
    public void afterPropertiesSet() throws Exception {
        if(!this.getClass().isAnnotationPresent(Parser.class)){
            log.warn("DataParser miss @Parser annotation,pls check.");
            throw new IllegalArgumentException("DataParser need @Parser annotation.");
        }

        init();
    }

    private void init(){
        Parser parser = this.getClass().getAnnotation(Parser.class);
        dataParserStrategy.register(parser.parser().getType(),this);
    }
}
