/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.adapter.strategy.label.handler;

import com.xftech.cdp.adapter.strategy.label.LabelHandler;
import com.xftech.cdp.adapter.strategy.label.QueryLabelRequest;
import com.xftech.cdp.domain.strategy.service.impl.VipCoreServiceImpl;
import com.xftech.cdp.infra.client.ads.model.resp.AdsLabelResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $ VipCardRenewalStatusLabelHandler, v 0.1 2024/6/13 17:01 lingang.han Exp $
 */

@Slf4j
@Component
public class VipCardRenewalStatusLabelHandler implements LabelHandler {
    @Autowired
    private VipCoreServiceImpl vipCoreService;

    @Override
    public String getLabel() {
        return "vip_card_renewal_status";
    }

    @Override
    public AdsLabelResp execute(String label, QueryLabelRequest batchAdsLabelVO) {
        List<Long> userNoList = batchAdsLabelVO.getUserNoList();
        Map<Long, Boolean> userNoToResult = vipCoreService.getRenewStatusBatch(userNoList);
        List<AdsLabelResp.Param> result = userNoToResult.entrySet().stream().map(x -> new AdsLabelResp.Param(x.getKey(), null, x.getValue() ? "1" : "0")).collect(Collectors.toList());
        return new AdsLabelResp(label, result);
    }
}