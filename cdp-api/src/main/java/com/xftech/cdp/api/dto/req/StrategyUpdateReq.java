package com.xftech.cdp.api.dto.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xftech.cdp.api.dto.base.TimeFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class StrategyUpdateReq extends BaseReq {

    @ApiModelProperty(value = "id", required = true)
    private Long id;

    @Size(max = 20, message = "策略名称不能超过20个字符")
    @NotBlank
    @ApiModelProperty(value = "策略名称", required = true)
    private String name;

    @ApiModelProperty(value = "策略分组")
    private Integer strategyGroupType;

    @Min(0)
    @Max(1)
    @ApiModelProperty(value = "用户是否转换 0-否 1-是")
    private Integer userConvert;

    @Size(max = 100)
    @ApiModelProperty(value = "策略描述")
    private String detailDescription;


    @ApiModelProperty(value = "人群包id,多个人群\";\"分隔", required = true)
    private String crowdPackIds;

    @Min(0)
    @Max(1)
    @ApiModelProperty(value = "abTest 0-否 1-是", required = true)
    private Integer abTest;

    @Min(0)
    @ApiModelProperty(value = "分组方式，0：uid倒数两位 1：随机数")
    private Integer abType;

    @ApiModelProperty(value = "随机数场景key值")
    private String bizKey;

    @Min(0)
    @Max(3)
    @ApiModelProperty(value = "发送规则 0-单次 1-例行", required = true)
    private Integer sendRuler;

    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    @ApiModelProperty(value = "策略开始时间")
    private LocalDateTime validityBegin;

    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    @ApiModelProperty(value = "策略结束时间")
    private LocalDateTime validityEnd;

    @ApiModelProperty(value = "发送频次")
    private StrategyCreateReq.SendFrequency sendFrequency;

    @ApiModelProperty(value = "人群分组")
    private List<StrategyCreateReq.StrategyGroup> strategyGroups;

    @ApiModelProperty(value = "仍要执行 0校验 1不校验")
    private Integer forceExec;

    @ApiModelProperty(value = "限制天数")
    private Integer limitDays;

    @ApiModelProperty(value = "限制次数")
    private Integer limitTimes;

    @ApiModelProperty(value = "营销条件配置")
    List<InstantStrategyCreateReq.MarketCondition> marketCondition;

    @ApiModelProperty(value = "排除项类型")
    private List<Integer> excludeType;

    @ApiModelProperty(value = "业务线名称")
    private String businessTypeName;

    @ApiModelProperty(value = "策略类型 0-默认其它，1-引擎规则")
    private Integer type = 0;

    @ApiModelProperty(value = "引擎code")
    private String engineCode;

    // 触达类型: MKT-营销,  NOTIFY-通知
    @ApiModelProperty(value = "触达类型: MKT-营销,  NOTIFY-通知")
    private String dispatchType;

    @Data
    public static class StrategyGroup {

        @NotEmpty
        @ApiModelProperty(value = "组名")
        private String name;

        @ApiModelProperty(value = "组id")
        private Long groupId;

        @ApiModelProperty(value = "分组配置")
        private StrategyCreateReq.GroupConfig groupConfig;

        @ApiModelProperty(value = "触达渠道")
        private List<StrategyCreateReq.StrategyMarketChannel> strategyMarketChannels;
    }

    @Data
    public static class StrategyMarketChannel {

        @ApiModelProperty(value = "营销渠道，0: 不营销，1:短信，2:电销，3:优惠券，4:新电销，5:push 200:提额")
        private Integer marketChannel;

        @ApiModelProperty(value = "渠道id")
        private Long channelId;

        @ApiModelProperty(value = "模板id")
        private String templateId;

        @JsonFormat(pattern = TimeFormat.TIME_SEC)
        @ApiModelProperty(value = "发送时间")
        private LocalTime sendTime;

    }

    @Data
    public static class GroupConfig {

        private Integer digits;

        private Integer positionStart;

        private Integer positionEnd;

        private Object crowdLabelOption;
    }

    @Data
    public static class SendFrequency {

        @ApiModelProperty(value = "频率类型，0：每天，1：每周，2：每月 3-周期")
        private Integer type;

        @ApiModelProperty(value = "每周/每月第几天")
        private List<Integer> value;
    }
}
