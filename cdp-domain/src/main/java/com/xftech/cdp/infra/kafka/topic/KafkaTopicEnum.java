package com.xftech.cdp.infra.kafka.topic;

import java.util.Arrays;
import java.util.Objects;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
@Getter
@AllArgsConstructor
public enum KafkaTopicEnum {

    TP_CDP_T0_EVENT_STRATEGY_COUNT("tp_cdp_t0_event_strategy_count", "T0事件策略频次统计");

    private final String code;

    private final String description;

    public static KafkaTopicEnum from(String code) {
        return Arrays.stream(values())
                .filter(x -> Objects.equals(x.getCode(), code))
                .findFirst().orElse(null);
    }

}
