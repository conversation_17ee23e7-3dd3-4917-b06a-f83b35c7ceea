package com.xftech.cdp.domain.event.model.dto;

import com.xftech.cdp.domain.event.model.config.FieldConfig;
import lombok.Data;

import java.util.List;

/**
 * 字段映射配置信息
 * <AUTHOR>
 * @version $ FieldDetail, v 0.1 2024/11/12 17:35 snail Exp $
 */
@Data
public class FieldDetail {
    /** 原始字段名称 */
    private String originField;
    /** 目标字段名称 */
    private String targetField;
    /** 字段数据处理器 */
    private List<FieldConfig> processList;
}
