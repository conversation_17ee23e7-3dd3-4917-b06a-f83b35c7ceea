/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.label.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version $ LabelExchangeEnum, v 0.1 2024/6/20 17:53 lingang.han Exp $
 */
@Getter
@AllArgsConstructor
public enum LabelExchangeEnum {
    RADIO_BUTTON_NULL(1, "固定单选-null"),
    RADIO_BUTTON_TIME(2, "固定单选-time"),
    RADIO_BUTTON_BOOLEAN(3, "固定单选-boolean"),
    CHECKBOX(4, "固定多选"),
    INPUT(5, "文本输入"),
    TIMEFRAME_NEXT(6, "未来时间范围"),
    TIMEFRAME_PASS(7, "过去时间范围"),
    NUMERIC_RANGE_INTEGER(8, "数值范围-整数"),
    NUMERIC_RANGE_DOUBLE(9, "数值范围-小数"),
    RANDOM(90, "随机数"),
    NEW_RANDOM(100, "新随机数"),
    ;

    private final Integer code;
    private final String desc;

    public static LabelExchangeEnum getInstance(Integer code) {
        return Arrays.stream(LabelExchangeEnum.values()).filter(item -> item.getCode().equals(code)).findFirst().orElse(null);
    }
}