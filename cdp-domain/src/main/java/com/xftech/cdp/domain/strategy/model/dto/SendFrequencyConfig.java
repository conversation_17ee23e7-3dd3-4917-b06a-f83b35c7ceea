package com.xftech.cdp.domain.strategy.model.dto;

import com.xftech.cdp.domain.strategy.model.enums.StrategyFrequencyEnum;
import lombok.Data;

import java.time.LocalDate;
import java.util.Arrays;

/**
 * @<NAME_EMAIL>
 * @date 2023-07-04 13:59
 */
@Data
public class SendFrequencyConfig {
    /**
     * 0-每日，1-每周，2-每月
     */
    private Integer type;

    private Integer[] value;

    public StrategyFrequencyEnum getType() {
        return StrategyFrequencyEnum.getInstance(this.type);
    }

    public boolean isExecute(LocalDate date) {
        switch (getType()) {
            case EVERY_DAY:
                return true;
            case EVERY_WEEK:
                return Arrays.asList(this.value).contains(date.getDayOfWeek().getValue() + 1);
            default:
                return Arrays.asList(this.value).contains(date.getDayOfMonth());
        }
    }
}
