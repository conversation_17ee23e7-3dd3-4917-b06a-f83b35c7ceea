package com.xftech.cdp.domain.event.processor;

import com.xftech.cdp.domain.event.model.annotation.ProcessorRule;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

/**
 * 数据处理的抽象类
 * <AUTHOR>
 * @version $ AbstractDataProcessor, v 0.1 2024/11/18 11:27 snail Exp $
 */
public abstract class AbstractDataProcessor implements InitializingBean,DataProcessor{
    @Autowired
    private DataProcessorStrategy dataProcessorStrategy;

    @Override
    public void afterPropertiesSet() throws Exception {
        if(!this.getClass().isAnnotationPresent(ProcessorRule.class)){
            throw new IllegalArgumentException("DataProcessor must add @ProcessorRule annotation, pls check.");
        }

        init();
    }

    private void init(){
        ProcessorRule rule = this.getClass().getAnnotation(ProcessorRule.class);
        if(Objects.isNull(rule)){
            return;
        }

        dataProcessorStrategy.register(rule.processor().getName(),this);
    }
}
