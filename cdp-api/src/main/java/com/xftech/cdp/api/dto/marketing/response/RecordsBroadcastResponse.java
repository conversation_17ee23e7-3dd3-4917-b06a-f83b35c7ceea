/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2025. All Rights Reserved.
 */

package com.xftech.cdp.api.dto.marketing.response;

import com.xftech.cdp.api.dto.config.ActBroadcastVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ RecordsBroadcastResponse, v 0.1 2025/1/4 17:16 xu.fan Exp $
 */
@ApiModel("活动获奖信息轮播")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RecordsBroadcastResponse extends ActivityBaseResponse {

    @ApiModelProperty(value = "轮播详情")
    private List<ActBroadcastVo> records;

    public RecordsBroadcastResponse(String code, String message) {
        super(code, message);
    }
}
