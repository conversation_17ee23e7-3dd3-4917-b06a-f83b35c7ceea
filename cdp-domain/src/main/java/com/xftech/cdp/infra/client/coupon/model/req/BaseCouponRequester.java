package com.xftech.cdp.infra.client.coupon.model.req;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR> wanghu
 * @since : 2022/7/28 14:44:39x
 */
@Data
public class BaseCouponRequester<T> {

    @JsonProperty("ua")
    @JSONField(name = "ua")
    private String ua;

    @JsonProperty("sign")
    @JSONField(name = "sign")
    private String sign;

    @JsonProperty("args")
    @JSONField(name = "args")
    private T args;

    public BaseCouponRequester(T args) {
        this.args = args;
    }
}
