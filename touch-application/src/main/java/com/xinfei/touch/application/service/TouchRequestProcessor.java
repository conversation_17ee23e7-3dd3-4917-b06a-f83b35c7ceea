package com.xinfei.touch.application.service;

import com.xinfei.touch.application.dto.TouchResponse;
import com.xinfei.touch.application.command.TouchCommand;
import com.xinfei.touch.domain.service.FlowControlDomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 统一触达请求处理器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TouchRequestProcessor {
    
    private final FlowControlDomainService flowControlDomainService;
    private final ChannelRoutingService channelRoutingService;
    private final TouchRecordService touchRecordService;
    private final TouchMonitorService touchMonitorService;
    
    /**
     * 处理触达请求
     * 
     * @param command 触达命令
     * @return 触达响应
     */
    public TouchResponse process(TouchCommand command) {
        String requestId = command.getRequestId();
        
        try {
            log.info("开始处理触达请求: requestId={}, touchType={}, channel={}, userId={}",
                    requestId, command.getTouchType(), command.getChannel(), command.getUserId());
            
            // 1. 请求验证
            validateCommand(command);
            
            // 2. 频控检查
            if (!checkFlowControl(command)) {
                TouchResponse response = TouchResponse.flowControlled(requestId);
                recordTouchResult(command, response);
                return response;
            }
            
            // 3. 渠道路由
            ChannelPlugin channelPlugin = channelRoutingService.route(command.getChannel());
            if (channelPlugin == null) {
                TouchResponse response = TouchResponse.failed(requestId, "CHANNEL_NOT_AVAILABLE", "渠道不可用");
                recordTouchResult(command, response);
                return response;
            }
            
            // 4. 触达执行
            TouchResponse response = executeTouch(command, channelPlugin);
            
            // 5. 结果记录
            recordTouchResult(command, response);
            
            // 6. 监控上报
            reportMonitoring(command, response);
            
            log.info("触达请求处理完成: requestId={}, touchType={}, status={}",
                    requestId, command.getTouchType(), response.getStatus());
            
            return response;
            
        } catch (Exception e) {
            log.error("触达请求处理异常: requestId={}", requestId, e);
            TouchResponse response = TouchResponse.failed(requestId, "PROCESS_ERROR", e.getMessage());
            recordTouchResult(command, response);
            return response;
        }
    }
    
    /**
     * 验证命令
     */
    private void validateCommand(TouchCommand command) {
        if (command == null) {
            throw new IllegalArgumentException("触达命令不能为空");
        }
        
        if (command.getRequestId() == null || command.getRequestId().trim().isEmpty()) {
            throw new IllegalArgumentException("请求ID不能为空");
        }
        
        if (command.getChannel() == null) {
            throw new IllegalArgumentException("触达渠道不能为空");
        }
        
        if (command.getUserId() == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
    }
    
    /**
     * 频控检查
     */
    private boolean checkFlowControl(TouchCommand command) {
        try {
            // 事件级流控检查
            if (command.getBizEventType() != null) {
                if (!flowControlDomainService.checkEventFlowControl(command.getBizEventType(), command.getUserId())) {
                    log.info("事件级流控拦截: requestId={}, eventType={}, userId={}", 
                            command.getRequestId(), command.getBizEventType(), command.getUserId());
                    return false;
                }
            }
            
            // 触达级流控检查
            if (!flowControlDomainService.checkTouchFlowControl(
                    command.getChannel(), command.getStrategyId(), command.getUserId(), command.getBizEventType())) {
                log.info("触达级流控拦截: requestId={}, channel={}, strategyId={}, userId={}", 
                        command.getRequestId(), command.getChannel(), command.getStrategyId(), command.getUserId());
                return false;
            }
            
            return true;
            
        } catch (Exception e) {
            log.error("频控检查异常: requestId={}", command.getRequestId(), e);
            // 异常情况下放行，避免影响业务
            return true;
        }
    }
    
    /**
     * 执行触达
     */
    private TouchResponse executeTouch(TouchCommand command, ChannelPlugin channelPlugin) {
        try {
            // 调用渠道插件执行触达
            return channelPlugin.send(command);
            
        } catch (Exception e) {
            log.error("执行触达失败: requestId={}, channel={}", command.getRequestId(), command.getChannel(), e);
            return TouchResponse.failed(command.getRequestId(), "CHANNEL_ERROR", "渠道调用失败: " + e.getMessage());
        }
    }
    
    /**
     * 记录触达结果
     */
    private void recordTouchResult(TouchCommand command, TouchResponse response) {
        try {
            touchRecordService.record(command, response);
        } catch (Exception e) {
            log.error("记录触达结果失败: requestId={}", command.getRequestId(), e);
        }
    }
    
    /**
     * 监控上报
     */
    private void reportMonitoring(TouchCommand command, TouchResponse response) {
        try {
            touchMonitorService.report(command, response);
        } catch (Exception e) {
            log.error("监控上报失败: requestId={}", command.getRequestId(), e);
        }
    }
}
