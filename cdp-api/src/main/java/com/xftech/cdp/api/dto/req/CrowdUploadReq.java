package com.xftech.cdp.api.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;

@Data
@EqualsAndHashCode(callSuper = true)
public class CrowdUploadReq extends BaseReq {

    @Size(max = 20, message = "人群包名称不能为空")
    @NotEmpty(message = "人群包名称不能为空")
    @ApiModelProperty(value = "人群包名称", required = true)
    private String crowdName;

    @ApiModelProperty(value = "人群包文件上传日志id")
    private Long uploadLogId;

    @ApiModelProperty(value = "业务线 new-cust-新客  old-cust-老客")
    @NotEmpty(message = "业务线不能为空")
    private String businessType;
}
