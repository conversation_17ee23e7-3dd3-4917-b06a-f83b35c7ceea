package com.xftech.cdp.infra.repository.cdp.crowd.po;


import com.xftech.cdp.api.dto.crowd.CrowdXxlJobParam;
import com.xftech.cdp.domain.crowd.exception.CrowdException;
import com.xftech.cdp.domain.crowd.model.enums.CrowdFilterMethodEnum;
import com.xftech.cdp.domain.crowd.model.enums.CrowdRefreshTypeEnum;
import com.xftech.cdp.domain.crowd.model.enums.CrowdStatusEnum;
import com.xftech.cdp.infra.client.dingtalk.DingTalkUtil;
import com.xftech.cdp.infra.repository.Do;
import com.xftech.cdp.infra.utils.DateUtil;
import com.xftech.cdp.infra.utils.SsoUtil;
import com.xftech.xxljob.XxlJobAdminClient;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-02-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CrowdPackDo extends Do {


    /**
     * 主键id
     */
    private Long id;

    /**
     * 人群包名称
     */
    private String crowdName;

    /**
     * 筛选类型
     */
    private Integer filterMethod;

    /**
     * 刷新类型
     */
    private Integer refreshType;

    /**
     * 状态
     */
    private Integer status;

    private Integer groupType;

    private LocalDateTime validityBegin;

    private LocalDateTime validityEnd;

    private LocalTime refreshTime;

    private String cron;

    private Integer xxlJobId;

    private String h5Option;

    private Integer crowdPersonNum;

    private Integer pullType;
    /**
     * 最新刷新时间
     */
    private LocalDateTime latestRefreshTime;

    /**
     * 更新人手机号
     */
    private String updatedOpMobile;

    /**
     * 业务线
     */
    private String businessType;

    /**
     * 圈选sql
     */
    public String includeSql;
    /**
     * 排除sql
     */
    public String excludeSql;

    /**
     *人群sql
     */
    private String crowdSql;

    public void InitDefaultValue() {
        this.setCreatedOp(SsoUtil.get().getName());
        this.setUpdatedOp(SsoUtil.get().getName());
        this.setUpdatedOpMobile(SsoUtil.get().getMobile());
        this.setStatus(CrowdStatusEnum.INIT.getCode());
    }

    public void runCheck(){
        if (StringUtils.isBlank(crowdSql)) {
            throw new CrowdException("人群包sql为空");
        }

        if ( CrowdRefreshTypeEnum.AUTOMATIC.getCode() == refreshType ) {
            if ( validityBegin.isAfter( LocalDateTime.now() ) || validityEnd.isBefore( LocalDateTime.now() ) ) {
                throw new CrowdException( "人群包执行时间错误！" );
            }
        }

        // 暂停状态 && 手动刷新，触发来源 —> 前端更新接口人群包更新接口，不拦截
        if (status == CrowdStatusEnum.PAUSING.getCode()
                && refreshType == CrowdRefreshTypeEnum.MANUAL.getCode()) {
            return;
        }

        if (CrowdStatusEnum.EXECUTING.getCode() == status || CrowdStatusEnum.PAUSING.getCode() == status  || CrowdStatusEnum.DEPRECATED.getCode() == status) {
            throw new CrowdException("人群包状态错误!");
        }
    }

    /**未删除无效人群包检验统计*/
    public void startCheck(){
        if (StringUtils.isBlank(crowdSql)) {
            throw new CrowdException("人群包sql为空");
        }

        if(CrowdRefreshTypeEnum.MANUAL.getCode() == refreshType){
            throw new CrowdException( "手动刷新不需要执行 " );
        }

        if ( CrowdRefreshTypeEnum.AUTOMATIC.getCode() == refreshType ) {
            if ( validityBegin.isAfter( LocalDateTime.now() ) || validityEnd.isBefore( LocalDateTime.now() ) ) {
                throw new CrowdException( "人群包执行时间错误！" );
            }
        }

        if (CrowdStatusEnum.EXECUTING.getCode() == status || CrowdStatusEnum.PAUSING.getCode() == status || CrowdStatusEnum.ENDED.getCode() == status || CrowdStatusEnum.DEPRECATED.getCode() == status) {
            throw new CrowdException("人群包状态错误!");
        }
    }

    public boolean retry(){
        if (StringUtils.isNotBlank(crowdSql) && CrowdStatusEnum.FAILED.getCode() == status && DateUtil.isToday(latestRefreshTime)) {
           return true;
        }
        return false;
    }

    public boolean undo(){
        if (StringUtils.isBlank(crowdSql) || CrowdRefreshTypeEnum.MANUAL.getCode() == refreshType) {
            return false;
        }

        if ( CrowdRefreshTypeEnum.AUTOMATIC.getCode() == refreshType ) {
            if ( validityBegin.isAfter( LocalDateTime.now() ) || validityEnd.isBefore( LocalDateTime.now() ) ) {
                return false;
            }
        }

        if (CrowdStatusEnum.EXECUTING.getCode() == status || CrowdStatusEnum.PAUSING.getCode() == status || CrowdStatusEnum.ENDED.getCode() == status || CrowdStatusEnum.DEPRECATED.getCode() == status) {
            return false;
        }
        if(DateUtil.isToday(latestRefreshTime)){
            return false;
        }
        return true;
    }

    public void verifyCrowd( XxlJobAdminClient xxlJobAdminClient ) {
        if ( CrowdRefreshTypeEnum.AUTOMATIC.getCode() == refreshType ) {
            if ( validityBegin.isAfter( LocalDateTime.now() ) || validityEnd.isBefore( LocalDateTime.now() ) ) {
                xxlJobAdminClient.stopJob( xxlJobId );
                throw new CrowdException( "人群包执行时间错误！" );
            }
        }

        // 暂停状态 && 手动刷新，触发来源 —> 前端更新接口人群包更新接口，不拦截
        if (status == CrowdStatusEnum.PAUSING.getCode()
                && refreshType == CrowdRefreshTypeEnum.MANUAL.getCode()) {
            return;
        }

        if (CrowdStatusEnum.getPausedCodes().contains(status)) {
            throw new CrowdException("人群包状态错误!");
        }

        CrowdFilterMethodEnum filterMethodEnum = CrowdFilterMethodEnum.getInstance(filterMethod);
        if (filterMethodEnum == CrowdFilterMethodEnum.UPLOAD) {
            throw new CrowdException("文件上传模式的人群包不支持刷新");
        }
    }

    public void alarmDingTalk(String dingTalkUrl, List<String> atMobiles,String otherMsg) {
//        StringBuilder content = new StringBuilder();
//        content.append(String.format("人群包编号：%s", getId())).append("\n");
//        content.append(String.format("人群包名称：%s", getCrowdName())).append("\n");
//        content.append("人群刷新失败，请尽快排查并操作手动刷新!");
//        DingTalkUtil.sendTextToDingTalk(dingTalkUrl, content.toString(),atMobiles,false);
        DingTalkUtil.dingTalk(dingTalkUrl, getId(), getCrowdName(), atMobiles,otherMsg);
    }


    public void execFailed() {
        status = CrowdStatusEnum.FAILED.getCode();
    }

    public void execSuccess(Integer personNum) {
        status = CrowdStatusEnum.SUCCESS.getCode();
        crowdPersonNum = personNum;
    }

    public CrowdXxlJobParam toXxlJobParam() {
        return new CrowdXxlJobParam(getId());
    }

    public void execExecuting() {
        status = CrowdStatusEnum.EXECUTING.getCode();
    }
}
