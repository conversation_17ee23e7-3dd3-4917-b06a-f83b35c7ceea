package com.xftech.cdp.infra.rocketmq.judge.handler;

import java.util.Date;
import java.util.Objects;

import com.alibaba.fastjson.JSONObject;
import com.xftech.cdp.domain.mq.MqConsumeService;
import com.xftech.cdp.infra.aviator.util.DateTimeUtil;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventMessageVO;
import com.xftech.cdp.infra.rocketmq.MessageHandler;
import com.xftech.cdp.infra.rocketmq.judge.model.ApiCredentialStuffingVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


import static com.xftech.cdp.infra.rocketmq.EventEnum.*;
import static com.xftech.cdp.infra.rocketmq.RocketMQEnum.TP_UGCHANPROD_API_JUDGE_ENGINE;

/**
 * <AUTHOR>
 * @version $ apiCredentialStuffingHandler, v 0.1 2025/3/14 16:01 tianshuo.qiu Exp $
 */
@Slf4j
@Component
public class ApiCredentialStuffingHandler implements MessageHandler {
    @Autowired
    private MqConsumeService mqConsumeService;

    @Override
    public String getTopic() {
        return TP_UGCHANPROD_API_JUDGE_ENGINE.getTopic();
    }

    @Override
    public boolean execute(String messageId, Object message) {
        ApiCredentialStuffingVO messageVO = JSONObject.parseObject(message.toString(), ApiCredentialStuffingVO.class);
        BizEventMessageVO bizEventMessageVO = transform(messageVO);
        if (StringUtils.isNotBlank(bizEventMessageVO.getBizEventType())) {
            mqConsumeService.bizEventProcess(messageId, bizEventMessageVO);
            return true;
        } else {
            log.info("apiCredentialStuffingHandler execute 不符合API撞库事件上报筛选条件, message={}", JSONObject.toJSONString(messageVO));
            return false;
        }
    }

    private BizEventMessageVO transform(ApiCredentialStuffingVO apiCredentialStuffingVO) {
        BizEventMessageVO bizEventMessageVO = new BizEventMessageVO();
        if (apiCredentialStuffingVO.getUser_no() == null || apiCredentialStuffingVO.getCheck_time() == null || apiCredentialStuffingVO.getCheck_result() == null || apiCredentialStuffingVO.getCheck_type() == null ||
                StringUtils.isAnyBlank(apiCredentialStuffingVO.getInner_app(), apiCredentialStuffingVO.getApp())) {
            return bizEventMessageVO;
        }
        bizEventMessageVO.setAppUserId(apiCredentialStuffingVO.getUser_no());
        bizEventMessageVO.setInnerApp(apiCredentialStuffingVO.getInner_app());
        bizEventMessageVO.setApp(apiCredentialStuffingVO.getApp());
        if (Objects.nonNull(apiCredentialStuffingVO.getCheck_time())) {
            bizEventMessageVO.setTriggerDatetime(DateTimeUtil.formatDateToStr(apiCredentialStuffingVO.getCheck_time(), null));
        } else {
            bizEventMessageVO.setTriggerDatetime(DateTimeUtil.formatDateToStr(new Date(), null));
        }
        bizEventMessageVO.setBizEventType(EVENT_API_CREDENTIAL_STUFFING.getEventType());
        BizEventMessageVO.ExtrData extrData = new BizEventMessageVO.ExtrData();
        extrData.setApiCredentialStuffingResult(apiCredentialStuffingVO.getCheck_result());
        extrData.setCheckType(apiCredentialStuffingVO.getCheck_type());
        extrData.setCheckTime(apiCredentialStuffingVO.getCheck_time());
        // 新增筛选项是否新老客和撞库类型
        if (apiCredentialStuffingVO.getHas_loan_succ_record() != null) {
            extrData.setHasLoanSuccRecord(apiCredentialStuffingVO.getHas_loan_succ_record());
        }
        if (apiCredentialStuffingVO.getData_source() != null) {
            extrData.setDataSource(apiCredentialStuffingVO.getData_source());
        }
        bizEventMessageVO.setExtrData(extrData);
        return bizEventMessageVO;
    }
}
