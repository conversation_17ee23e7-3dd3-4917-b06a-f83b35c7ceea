package com.xftech.cdp.distribute.crowd.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version v1.0 2025/5/7
 * @description CrowdInfoEnum
 */
@Getter
@AllArgsConstructor
public enum CrowdStatusEnum {

    DELETED(0, "已删除(逻辑删除,不可逆)"),
    DRAFT(1, "草稿中"),
    UNDER_APPROVAL(2, "审批中"),
    OFFLINE(3, "已下线"),
    ONLINE(4, "已上线(不在有效期内,不会生成oss)");

    private final int status;

    private final String desc;

}
