/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2025. All Rights Reserved.
 */

package com.xftech.cdp.distribute.offline;

import cn.hutool.core.date.TimeInterval;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.xftech.cdp.api.dto.req.StrategyCreateReq;
import com.xftech.cdp.distribute.crowd.repository.CrowdSliceRepository;
import com.xftech.cdp.distribute.crowd.repository.model.CrowdSliceDo;
import com.xftech.cdp.distribute.offline.dto.StatisticInfo;
import com.xftech.cdp.distribute.offline.dto.StrategyExecuteContext;
import com.xftech.cdp.distribute.offline.enums.DistributeStrategyType;
import com.xftech.cdp.distribute.offline.repository.StrategySliceExecLogRepository;
import com.xftech.cdp.domain.ads.service.AdsStrategyLabelQueryService;
import com.xftech.cdp.domain.ads.service.impl.AbstractAdsStrategyLabelService;
import com.xftech.cdp.domain.cache.CacheStrategyService;
import com.xftech.cdp.domain.crowd.factory.AbsCrowdOptService;
import com.xftech.cdp.domain.crowd.factory.CrowdOptFactory;
import com.xftech.cdp.domain.crowd.model.dispatch.CrowdContext;
import com.xftech.cdp.domain.crowd.model.enums.CrowdPushBatchStatusEnum;
import com.xftech.cdp.domain.crowd.model.enums.SliceExecLogEnum;
import com.xftech.cdp.domain.crowd.repository.CrowdDetailRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdPackRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdPushBatchRepository;
import com.xftech.cdp.domain.crowd.service.CrowdPackService;
import com.xftech.cdp.domain.crowd.service.impl.CrowdPushBatchServiceImpl;
import com.xftech.cdp.domain.dispatch.dto.DispatchDto;
import com.xftech.cdp.domain.flowctrl.service.FlowCtrlCoreService;
import com.xftech.cdp.domain.param.service.TemplateParamQueryService;
import com.xftech.cdp.domain.randomnum.RandomNumService;
import com.xftech.cdp.domain.strategy.exception.StrategyException;
import com.xftech.cdp.domain.strategy.model.dispatch.StrategyContext;
import com.xftech.cdp.domain.strategy.model.dto.AiProntoChannelDto;
import com.xftech.cdp.domain.strategy.model.dto.IncreaseAmtDto;
import com.xftech.cdp.domain.strategy.model.dto.IncreaseAmtParamDto;
import com.xftech.cdp.domain.strategy.model.dto.StrategyCycleDayConfig;
import com.xftech.cdp.domain.strategy.model.enums.*;
import com.xftech.cdp.domain.strategy.repository.*;
import com.xftech.cdp.domain.strategy.service.*;
import com.xftech.cdp.feign.model.PushUserData;
import com.xftech.cdp.infra.client.dingtalk.config.DingTalkConfig;
import com.xftech.cdp.infra.client.randomnum.RandomNumClient;
import com.xftech.cdp.infra.client.sms.model.SmsBatchWithParamArgs;
import com.xftech.cdp.infra.config.AppConfigService;
import com.xftech.cdp.infra.config.StrategyConfig;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdExecLogDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPackDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPushBatchDo;
import com.xftech.cdp.infra.repository.cdp.slice.po.StrategySliceExecLogDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.*;
import com.xftech.cdp.infra.rocketmq.dto.AiUserData;
import com.xftech.cdp.infra.utils.*;
import com.xftech.xxljob.XxlJobAdminClient;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;


import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;


/**
 * 策略执行核心逻辑
 *
 * 1、离线策略公共方法，用户抽象的
 *
 */
@Slf4j
@Getter
public abstract class AbstractOfflineStrategyDispatchService {

    @Autowired
    private StrategyConfig strategyConfig;
    @Autowired
    private DingTalkConfig dingTalkConfig;
    @Autowired
    private StrategyService strategyService;
    @Autowired
    private CrowdPackService crowdPackService;
    @Autowired
    private RandomNumService randomNumService;
    @Autowired
    private XxlJobAdminClient xxlJobAdminClient;
    @Autowired
    private StrategyRepository strategyRepository;
    @Autowired
    private CrowdPackRepository crowdPackRepository;
    @Autowired
    private FlowCtrlCoreService flowCtrlCoreService;
    @Autowired
    private StrategyGroupService strategyGroupService;
    @Autowired
    private CacheStrategyService cacheStrategyService;
//    @Autowired
//    private CrowdDetailRepository crowdDetailRepository;
    @Autowired
    private StrategyGroupRepository strategyGroupRepository;
//    @Autowired
//    private CrowdPushBatchRepository crowdPushBatchRepository;
    @Autowired
    private StrategyExecLogRepository strategyExecLogRepository;
//    @Autowired
//    private UserDispatchDetailService userDispatchDetailService;
//    @Autowired
//    private StrategySnapshotRepository strategySnapshotRepository;
    @Autowired
    private StrategyCrowdPackRepository strategyCrowdPackRepository;
//    @Autowired
//    private UserBlankGroupDetailService userBlankGroupDetailService;
//    @Autowired
//    private StrategyMarketChannelRepository strategyMarketChannelRepository;
    @Autowired
    private TemplateParamQueryService templateParamQueryService;
    @Autowired
    private AdsStrategyLabelQueryService adsStrategyLabelQueryService;
    @Autowired
    private AppConfigService appConfigService;
    @Autowired
    private StrategyExecCycleCounterService strategyExecCycleCounterService;
    @Autowired
    private StrategyExecCycleService strategyExecCycleService;
    @Autowired
    private RandomNumClient randomNumClient;
    @Autowired
    private DispatchTaskService dispatchTaskService;
    @Autowired
    private CrowdOptFactory crowdOptFactory;
    @Autowired
    private AbstractAdsStrategyLabelService abstractAdsStrategyLabelService;
    @Autowired
    private CrowdSliceRepository crowdSliceRepository;
    @Autowired
    private StrategySliceExecLogRepository strategySliceExecLogRepository;
    @Autowired
    private RedisUtils redisUtils;

    /**
     * 策略执行入口 非离线引擎策略
     * 周期策略 普通离线策略
     *
     */
    public abstract void execute(StrategySliceExecLogDo sliceExecLogDo);


    protected StrategyExecuteContext initContext(StrategySliceExecLogDo sliceExecLogDo) {

        Long strategyId = sliceExecLogDo.getStrategyId();

        StrategyDo strategyDo = strategyRepository.selectById(strategyId);
        if (strategyDo == null) {
            throw new StrategyException(String.format("不存在该策略,策略id:%s", strategyId));
        }
        if (StrategyStatusEnum.getPausedCodes().contains(strategyDo.getStatus())) {
            throw new StrategyException(String.format("策略状态暂停或者结束不营销,策略id:%s", strategyId));
        }

        if (StringUtils.isEmpty(strategyDo.getEngineCode())) {
            throw new StrategyException(String.format("该策略不存在引擎code，策略id：%s", strategyDo.getId()));
        }

        List<StrategyCrowdPackDo> strategyCrowdPackDos = strategyCrowdPackRepository.selectByStrategyId(strategyDo.getId());
        if (CollectionUtils.isEmpty(strategyCrowdPackDos)) {
            throw new StrategyException(String.format("该策略不存在人群包 策略id:%s", strategyDo.getId()));
        }
        // 查分片
        CrowdSliceDo crowdSliceDo = crowdSliceRepository.selectByPrimaryKey(sliceExecLogDo.getCrowdSliceId());
        if(crowdSliceDo == null) {
            throw new StrategyException(String.format("人群包分片找不到 策略id:%s", strategyDo.getId()));
        }

        List<StrategyGroupDo> strategyGroupDo = strategyGroupRepository.selectListByStrategyId(strategyDo.getId());;
        if(CollectionUtils.isEmpty(strategyGroupDo)) {
            throw new StrategyException(String.format("人群分组找不到 策略id:%s", strategyDo.getId()));
        }

        Map<Long, CrowdContext> crowdContent = getCrowdContent(ListUtils.distinctMap(strategyCrowdPackDos, StrategyCrowdPackDo::getCrowdPackId));

        StrategyExecuteContext.CycleStrategy cycleStrategy = loadCycleStrategy(strategyDo);

        StrategyExecuteContext strategyExecuteContext = StrategyExecuteContext.build(
                strategyId,
                DistributeStrategyType.OFFLINE_ENGINE.getType(),
                strategyDo,
                strategyGroupDo,
                sliceExecLogDo,
                crowdSliceDo,
                cycleStrategy,
                new StatisticInfo(),
                crowdContent
        );
        return strategyExecuteContext;
    }

    public StrategyExecuteContext.CycleStrategy loadCycleStrategy(StrategyDo strategyDo) {
        Integer dataValue = DateUtil.dayOfInt(DateUtil.convert(LocalDateTime.now()));
        StrategyExecuteContext.CycleStrategy cycleStrategy = null;
        if (strategyDo.getSendRuler() ==
                StrategyRulerEnum.CYCLE_DAY.getCode()) {
            StrategyCreateReq.SendFrequency sendFrequency = JsonUtil.parse(strategyDo.getSendFrequency(),
                    StrategyCreateReq.SendFrequency.class);
            if (sendFrequency.getType() != 3) {
                throw new StrategyException("周期策略数据有问题！");
            }
            cycleStrategy = new StrategyExecuteContext.CycleStrategy();
            StrategyCycleDayConfig cycleDayConfig = appConfigService.getStrategyCycleDayConfig();
            if (cycleDayConfig == null || StringUtils.isEmpty(cycleDayConfig.getBizKey())) {
                throw new StrategyException("周期策略不存在！");
            }
            cycleStrategy.setCycleNum(sendFrequency.getValue().get(0));
            cycleStrategy.setCycleDayConfig(cycleDayConfig);
            StrategyExecCycleCounterDo strategyExecCycleCounterDo = strategyExecCycleCounterService
                    .selectStrategyCycleCounter(strategyDo.getId());
            if (strategyExecCycleCounterDo == null) {
                // 不存在则进行插入
                strategyExecCycleCounterDo = new StrategyExecCycleCounterDo();
                strategyExecCycleCounterDo.setStrategyId(strategyDo.getId());
                strategyExecCycleCounterDo.setSumVal(1);
                strategyExecCycleCounterDo.setDateValue(dataValue);
                strategyExecCycleCounterService.insert(strategyExecCycleCounterDo);
                int current = (1 % cycleStrategy.getCycleNum()) == 0 ? cycleStrategy.getCycleNum()
                        : 1 % cycleStrategy.getCycleNum();
                cycleStrategy.setCurrCycleNum(current);
            } else {
                // 存在则更新计数值
                strategyExecCycleCounterService
                        .updateStrategyCycleCounter(strategyDo.getId(), dataValue);
                strategyExecCycleCounterDo = strategyExecCycleCounterService
                        .selectStrategyCycleCounter(strategyDo.getId(), dataValue);
                int current = (strategyExecCycleCounterDo.getSumVal() % cycleStrategy.getCycleNum()) == 0 ? cycleStrategy.getCycleNum()
                        : strategyExecCycleCounterDo.getSumVal() % cycleStrategy.getCycleNum();
                cycleStrategy.setCurrCycleNum(current);
            }
            StrategyExecCycleDo strategyExecCycleDo = strategyExecCycleService.selectStrategyCycle(strategyDo.getId(), dataValue);
            if (strategyExecCycleDo == null) {
                strategyExecCycleDo = new StrategyExecCycleDo();
                strategyExecCycleDo.setStrategyId(strategyDo.getId());
                strategyExecCycleDo.setDateValue(dataValue);
                strategyExecCycleDo.setTotalVal(cycleStrategy.getCycleNum());
                strategyExecCycleDo.setCurVal(cycleStrategy.getCurrCycleNum());
                strategyExecCycleService.insert(strategyExecCycleDo);
            }
        }
        return cycleStrategy;
    }

    public Map<Long, CrowdContext> getCrowdContent(List<Long> ids) {
        Map<Long, CrowdContext> res = new HashMap<>(ids.size());
        try {
            for (Long cid : ids) {
                CrowdPackDo crowdPack = crowdPackRepository.selectById(cid);
                AbsCrowdOptService crowdOpt = crowdOptFactory.createOpt(crowdPack.getPullType());
                CrowdContext crowdContext = crowdOpt.initContext(false, crowdPack, crowdOpt);
                CrowdExecLogDo cd = new CrowdExecLogDo();
                cd.setId(-1l); //新人群包无execlogid，用-1
                crowdContext.setCrowdExecLog(cd);
                res.put(cid, crowdContext);
            }
        } catch (Exception e) {
            log.error("getCrowdContent error", e);
        }
        return res;
    }

    /**
     * 分组人数统计
     *
     * @param context    初始化数据
     * @param groupCount 分组人数
     * @param totalCount 应发总数
     * @param sendCount  发送总数
     * @param count      当前批次人群总数
     */
    public void groupCount(StrategyExecuteContext context, AtomicInteger groupCount, AtomicInteger totalCount, AtomicInteger sendCount, Integer count) {
        groupCount.updateAndGet(v -> v + count);
        //  分布式统计计算
//        StatisticInfo statisticInfo = context.getStatisticInfo();
    }

    public Pair<List<CrowdDetailDo>, List<Object>> labelAndTempParamQuery(StrategyExecuteContext strategyContext, String app,
                                                                          List<CrowdDetailDo> crowdDetailDoList,
                                                                          AtomicReference<List<CrowdDetailDo>> availableDetails) {
        List<Object> finalTemplateParamList = Lists.newArrayList();
        List<CrowdDetailDo> finalCrowdDetailList = Lists.newArrayList();
        for (List<CrowdDetailDo> partition : Lists.partition(crowdDetailDoList, strategyConfig.getLabelQueryBatchSize())) {
            // 1.标签查询
            List<CrowdDetailDo> queryLabelList = adsStrategyLabelQueryService.queryLabelHandler(
                    strategyContext.getStrategyDo().getId(),
                    0L, // 离线引擎无marketChannelId
                    -2,   // 离线引擎无marketChannel -2:空
                    app,
                    partition
            );
            availableDetails.get().addAll(queryLabelList);
            log.info("满足标签的用户数量:{}", queryLabelList.size());
            // 2.模板参数查询，普通离线分组配置有模版， 离线引擎用不到原有逻辑
            if (!CollectionUtils.isEmpty(queryLabelList)) {
//                Pair<List<CrowdDetailDo>, List<?>> paramQueryResult = this.templateParamQuery(strategyContext, app, queryLabelList);
                Pair<List<CrowdDetailDo>, List<?>> paramQueryResult = Pair.of(queryLabelList, Collections.emptyList());
                finalCrowdDetailList.addAll(paramQueryResult.getLeft());
                finalTemplateParamList.addAll(paramQueryResult.getRight());
            }
        }
        return Pair.of(finalCrowdDetailList, finalTemplateParamList);
    }


//    /**
//     * 模板参数查询
//     *
//     * @param context         策略执行初始化内容
//     * @param app             app
//     * @param crowdDetailList 需要执行下发的用户
//     * @return left-需要执行下发的用户，right-需要执行下发的用户的模板参数
//     */
//    private Pair<List<CrowdDetailDo>, List<?>> templateParamQuery(StrategyExecuteContext context, String app, List<CrowdDetailDo> crowdDetailList) {
//        final TimeInterval timer = new TimeInterval();
//        Pair<List<CrowdDetailDo>, List<?>> result = Pair.of(crowdDetailList, Collections.emptyList());
//        StrategyMarketChannelEnum marketChannelEnum = StrategyMarketChannelEnum.getInstance(context.getStrategyMarketChannelDo().getMarketChannel());
//        switch (marketChannelEnum) {
//            case SMS:
//                Triple<Boolean, List<CrowdDetailDo>, List<SmsBatchWithParamArgs.Sms>> triple = templateParamQueryService.smsParamBatchQuery(convertToDispatchDto(context), app, crowdDetailList);
//                result = Pair.of(triple.getMiddle(), triple.getRight());
//                break;
//            case PUSH:
//                Triple<Boolean, List<CrowdDetailDo>, List<PushUserData>> push = templateParamQueryService.pushParamBatchQuery(convertToDispatchDto(context), app, crowdDetailList);
//                result = Pair.of(push.getMiddle(), push.getRight());
//                break;
//            case AI_PRONTO:
//                Triple<Boolean, List<CrowdDetailDo>, List<AiUserData>> ai = templateParamQueryService.aiParamBatchQuery(convertToDispatchDto(context), app, crowdDetailList);
//                result = Pair.of(ai.getMiddle(), ai.getRight());
//                break;
//            default:
//                log.info("当前渠道[{}]不涉及模板参数查询,继续执行下发逻辑", marketChannelEnum.getDescription());
//                result = Pair.of(crowdDetailList, Collections.emptyList());
//        }
//        log.info("模板参数查询完成, 需要查询用户数:{}, 最终查询成功用户数:{},耗时:{}ms", crowdDetailList.size(), result.getLeft().size(), timer.interval());
//        return result;
//    }

    /**
     * 计数
     *
     * @param totalCount 执行总人数
     * @param sendCount  发送总数
     * @param respPair   当前批次人数
     */
    private void countTotalAndSend(AtomicInteger totalCount, AtomicInteger sendCount, Pair<Integer, Integer> respPair) {
//        totalCount.updateAndGet(v -> v + respPair.getLeft());
        sendCount.updateAndGet(v -> v + respPair.getRight());
    }


    /**
     * 执行成功
     */
    protected void successExecute(StrategyExecuteContext strategyContext) {
        // this.checkFinishExecUserNum(strategyContext);
        //  修改分片执行状态
        StrategySliceExecLogDo strategySliceExecLogDo = strategyContext.getStrategySliceExecLogDo();
        strategySliceExecLogRepository.updateStatus(strategySliceExecLogDo.getId(), SliceExecLogEnum.SUCCESS.getCode(),
                strategySliceExecLogDo.getRetryTimes(), SliceExecLogEnum.EXECUTING.getCode());
        log.info("successExecute 离线引擎分布式执行成功,策略ID:{},crowdId:{} 分片Id:{} 分片execId:{}", strategyContext.getStrategyDo().getId(),
                strategyContext.getStrategySliceExecLogDo().getCrowdId(),
                strategyContext.getStrategySliceExecLogDo().getCrowdSliceId(),
                strategyContext.getStrategySliceExecLogDo().getId());

        // 策略下所有分片都完成
        LocalDateTime start = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        LocalDateTime end = LocalDateTime.of(LocalDate.now(), LocalTime.MAX);
        List<StrategySliceExecLogDo> execLogList = strategySliceExecLogRepository.selectStrategyTodoList(strategyContext.getStrategyId(), start, end);
        if (CollectionUtils.isEmpty(execLogList)) {
            log.info("successExecute 策略ID:{},所有分片都执行成功", strategyContext.getStrategyId());
            // 查询strategy_exec_log，汇总写入统计信息
            StrategyExecLogDo lastExecLog = strategyExecLogRepository.selectLatestStatusByStrategyId(strategyContext.getStrategyId());

            // 查询所有分片的统计信息加总
            Map<String, Long> cntMap = strategySliceExecLogRepository.selectStrategyCnt(strategyContext.getStrategyId(),
                    start, end, strategyContext.getCrowdSliceDo().getCrowdVersion());
            log.info("successExecute 策略ID:{},统计信息:{} lastExecLog:{}", strategyContext.getStrategyId(), cntMap, lastExecLog);
            if (!CollectionUtils.isEmpty(cntMap) && lastExecLog != null) {
                long totalUserCnt = Optional.ofNullable(cntMap.get("totalUserCnt")).orElse(0L);
                long dispatchCnt = Optional.ofNullable(cntMap.get("dispatchCnt")).orElse(0L);
                long execUserCnt = Optional.ofNullable(cntMap.get("execUserCnt")).orElse(0L);
                lastExecLog.setExecCount((int) totalUserCnt);
                lastExecLog.setSendCount((int) dispatchCnt);
                lastExecLog.setGroupCount((int) execUserCnt);
            }

            try{
                // 修改策略执行状态为成功
                strategyContext.getStrategyDo().execSuccess();
                cacheStrategyService.updateById(strategyContext.getStrategyDo());

                if (lastExecLog != null) {
                    lastExecLog.execSuccess();
                    strategyExecLogRepository.updateById(lastExecLog);
                }

                List<DispatchTaskDo> executingTask = dispatchTaskService.selectLastExecutingTask("" + strategyContext.getStrategyId(),DispatchTaskStatusEnum.EXECUTING.getCode());
                if(!CollectionUtils.isEmpty(executingTask) && executingTask.size() == 1) {
                    dispatchTaskService.updateTaskStatus(executingTask.get(0).getId(), DispatchTaskStatusEnum.EXECUTING.getCode(),
                        DispatchTaskStatusEnum.SUCCEED.getCode(),"策略执行成功");
                } else {
                    log.warn("successExecute 策略ID:{}, dispatchTask 状态异常:{}", strategyContext.getStrategyId(), executingTask);
                }
            } catch (Exception e) {
                log.error("successExecute 策略ID:{},更新策略/执行日志状态记录异常", strategyContext.getStrategyId());
                throw new StrategyException("beginExecute()执行异常", e);
            }
        }

    }

    /**
     * 执行失败
     */
    protected void failedExecute(StrategyExecuteContext strategyContext, String errorMsg) {

        Long gapMins = appConfigService.getDistributeSliceTaskRetryWaitMins();

        TransactionUtil.transactional(() -> {
            StrategyDo strategyDo = strategyContext.getStrategyDo();
            strategyDo.execFailure(); // 单片失败，先修改策略执行失败，待排查失败原因
            cacheStrategyService.updateById(strategyDo);

//            StrategyExecLogDo strategyExecLogDo = strategyContext.getStrategyExecLogDo();
//            strategyExecLogDo.execFailure(strategyContext.getFailReason());
//            strategyExecLogRepository.updateById(strategyExecLogDo);

            // 修改分片执行状态为失败, 重置dispatchTime为15分钟后
            StrategySliceExecLogDo strategySliceExecLogDo = strategyContext.getStrategySliceExecLogDo();
            strategySliceExecLogRepository.updateNextDispatchTimeAndStatus(strategySliceExecLogDo.getId(),
                    SliceExecLogEnum.RETRY.getCode(), SliceExecLogEnum.EXECUTING.getCode(),
                    strategySliceExecLogDo.getDispatchTime().plusMinutes(gapMins), errorMsg); // 下次执行间隔20分钟

            log.info("策略{},策略ID:{}", StrategyStatusEnum.getInstance(strategyContext.getStrategyDo().getStatus()).getDescription(),
                    strategyContext.getStrategyId());
        }, e -> {
            throw new StrategyException("执行失败,更新策略/执行日志状态记录异常", e);
        });
    }


    /**
     * 生成dispatchDto 临时对象，这里应该需要优化，将一些信息进一步整合
     * @param context
     * @return
     */
    protected DispatchDto convertToDispatchDto(StrategyContext context) {
        DispatchDto reach = new DispatchDto();
        reach.setDetailTableNo(context.getDetailTableNo());
        reach.setStrategyId(context.getStrategyDo().getId());
        reach.setBizType(context.getStrategyDo().getBusinessType()); // 250401新增业务线字段
        reach.setStrategyGroupId(context.getStrategyGroupDo().getId());
        reach.setStrategyChannelId(context.getStrategyMarketChannelDo().getId());
        reach.setStrategyChannelXxlJobId(context.getStrategyMarketChannelDo().getXxlJobId());
        reach.setStrategyChannel(context.getStrategyMarketChannelDo().getMarketChannel());
        reach.setStrategyMarketChannelTemplateId(context.getStrategyMarketChannelDo().getTemplateId());
        reach.setNameTypeId(context.getStrategyMarketChannelDo().getTemplateId());
        reach.setStrategyExecLogId(context.getStrategyExecLogDo().getId());
        reach.setStrategyExecLogRetryId(context.getStrategyExecLogDo().getRetryId());
        reach.setStrategyRulerEnum(StrategyRulerEnum.getInstance(context.getStrategyDo().getSendRuler()));
        reach.setStrategyMarketChannelDo(context.getStrategyMarketChannelDo());
        reach.setFlowCtrlList(context.getFlowCtrlList());

        reach.setStrategyGroupName(StringUtils.replace(Optional.ofNullable(context.getStrategyGroupDo()
                .getName()).orElse(""), "组", ""));
        if (!Objects.isNull(context.getStrategyMarketChannelDo().getExtInfo())) {
            Map extInfo = JSONObject.parseObject(context.getStrategyMarketChannelDo().getExtInfo(), Map.class);
            if (!Objects.isNull(extInfo) && extInfo.containsKey("policyId")) {
                String policyId = String.valueOf(extInfo.get("policyId"));
                reach.setStrategyMarketChannelTemplateId(policyId);
            }
            if (!Objects.isNull(extInfo) && extInfo.containsKey("activityId")) {
                String activityId = String.valueOf(extInfo.get("activityId"));
                reach.setActivityId(activityId);
            }
            if (!Objects.isNull(extInfo) && extInfo.containsKey("signatureKey")) {
                String signature = String.valueOf(extInfo.get("signatureKey"));
                reach.setSignatureKey(signature);
            }
        }
        // 提额策略
        if (context.getStrategyMarketChannelDo().getMarketChannel() ==
                StrategyMarketChannelEnum.INCREASE_AMOUNT.getCode()) {
            IncreaseAmtDto increaseAmtDto = JsonUtil.parse(context.getStrategyMarketChannelDo().getExtInfo(),
                    IncreaseAmtDto.class);
            if (increaseAmtDto == null) {
                log.error("离线策略提额配置信息异常, 策略id:{}", reach.getStrategyId());
                throw new StrategyException("离线策略提额配置信息异常");
            }
            try {
                IncreaseAmtParamDto increaseAmtParamDto = increaseAmtDto.getIncreaseAmtConfig().getParamDto();
                if (!increaseAmtParamDto.isValid()) {
                    throw new StrategyException("离线策略提额配置信息异常");
                }
                reach.setIncreaseAmtParamDto(increaseAmtParamDto);
            } catch (Exception ex) {
                log.error("离线策略提额配置信息异常, 策略id:{}, error:{}", reach.getStrategyId(), ex.getMessage(), ex);
                throw new StrategyException("离线策略提额配置信息异常");
            }
        }
        //ai
        if (context.getStrategyMarketChannelDo().getMarketChannel() ==
                StrategyMarketChannelEnum.AI_PRONTO.getCode()) {
            AiProntoChannelDto aiProntoChannelDto = JsonUtil.parse(context.getStrategyMarketChannelDo().getExtInfo(),
                    AiProntoChannelDto.class);
            if (aiProntoChannelDto == null || StringUtils.isBlank(aiProntoChannelDto.getNameTypeId())) {
                log.error("离线策略ai_pronto配置信息异常, 策略id:{}", reach.getStrategyId());
                throw new StrategyException("离线策略ai_pronto配置信息异常");
            }
            reach.setAiProntoChannelDto(aiProntoChannelDto);
        }
        return reach;
    }

    /**
     * 设置批次大小
     *
     * @return 批次大小
     */
    protected abstract Integer setBatchSize();


    /**
     * 下发
     *
     * @param strategyContext 策略执行初始化内容
     * @param app             app
     * @param innerApp        innerApp
     * @param batch           按分组配置过滤出来的用户集合
     * @return ImmutablePair Left-下发成功数 Right-下发批次记录
     */
    protected abstract <T> ImmutablePair<Integer, CrowdPushBatchDo> dispatchHandler(StrategyExecuteContext strategyContext, StrategyGroupDo strategyGroupDo, String app,
                                                                                                   String innerApp, List<CrowdDetailDo> batch);

    /**
     * 执行下发
     *
     * @param context    初始化数据
     * @param totalCount 圈选总数
     * @param sendCount  发送成功总数
     * @param app        当前批APP
     * @param list       当前批人群明细
     */
    protected void dispatchHandler(StrategyExecuteContext context, StrategyGroupDo strategyGroupDo, AtomicInteger groupCount,
                                   AtomicInteger totalCount, AtomicInteger sendCount, String app, List<CrowdDetailDo> list) {
        Pair<Integer, Integer> respPair = Pair.of(list.size(), 0);
        try {
            // 1.执行下发逻辑
            respPair = this.executeDispatch(context, strategyGroupDo, app, app, list);
        } catch (StrategyException e) {
            // 2.下发异常
            throw new StrategyException(e.getCode(), e.getMessage());
        } finally {
            // 3.统计数量
            this.countTotalAndSend(totalCount, sendCount, respPair);
//            // 4.刷新执行日志 0513 改为外部一次记录
//            this.refreshExecLogCount(context, groupCount.get(), totalCount.get(), sendCount.get());

//            if (context.getStrategyMarketChannelDo().getMarketChannel() == StrategyMarketChannelEnum.NONE.getCode()) {
            log.info("[dispatchHandler统计数据] [更新后] 策略id：{}, groupCount: {}, totalCount: {}, sendCount: {}, respPair: {}",
                    context.getStrategyId(),
                    groupCount.get(),
                    totalCount.get(),
                    sendCount.get(),
                    JsonUtil.toJson(respPair));
//            }
        }
    }

    /**
     * 执行下发
     *
     * @param strategyContext 策略执行初始化内容
     * @param app              APP
     * @param innerApp              InnerApp
     * @param crowdList              需要执行下发的用户
     */
    private <T> ImmutablePair<Integer, Integer> executeDispatch(StrategyExecuteContext strategyContext,StrategyGroupDo strategyGroupDo, String app,String innerApp, List<CrowdDetailDo> crowdList) {
        if (CollectionUtils.isEmpty(crowdList)) {
            log.warn("当前批次没有需要下发的用户,策略ID:{}", strategyContext.getStrategyDo().getId());
            return ImmutablePair.of(0, 0);
        }
        // 离线引擎 app,innerApp 传参相同
        ImmutablePair<Integer, CrowdPushBatchDo> pair = this.dispatchHandler(strategyContext,strategyGroupDo, app,innerApp, crowdList);
        return ImmutablePair.of(pair.getLeft(), pair.getLeft());
    }

}
