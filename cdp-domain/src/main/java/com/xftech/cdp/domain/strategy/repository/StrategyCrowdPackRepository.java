package com.xftech.cdp.domain.strategy.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyCrowdPackDo;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 策略-人群包关系表操作
 *
 * <AUTHOR>
 * @since 2023/2/20
 */

@Component
public class StrategyCrowdPackRepository {

    /**
     * 根据策略id查询人群包列表
     *
     * @param strategyId 策略id
     * @return 该策略id下的人群包列表
     */
    public List<StrategyCrowdPackDo> selectByStrategyId(Long strategyId) {
        return DBUtil.selectList("strategyCrowdPack.selectByStrategyId", strategyId);
    }

    /**
     * 根据批量策略id查询
     *
     * @param strategyIdList 策略id列表
     * @return 该些策略id的人群包列表
     */
    public List<StrategyCrowdPackDo> selectCrowdPackIdByStrategyId(List<Long> strategyIdList) {
        Map<String, Object> params = new HashMap<>();
        params.put("strategyIdList", strategyIdList);
        return DBUtil.selectList("strategyCrowdPack.selectCrowdPackIdByStrategyId", params);
    }

    /**
     * 根据策略id删除人群包
     *
     * @param strategyId 策略id
     */
    public boolean deleteByStrategyId(Long strategyId) {
        return DBUtil.delete("strategyCrowdPack.deleteByStrategyId", strategyId) > 0;
    }

    /**
     * 批量插入策略人群包
     *
     * @param strategyCrowdPackDoList 策略人群包列表对象
     */
    public boolean saveBatch(List<StrategyCrowdPackDo> strategyCrowdPackDoList) {
        return DBUtil.insertBatchWithoutTx("strategyCrowdPack.insertSelective", strategyCrowdPackDoList) > 0;
    }

    public List<Long> selectCrowdPackIdsByStrategyId(Long strategyId){
        return DBUtil.selectList("strategyCrowdPack.selectCrowdPackIdsByStrategyId",strategyId);
    }

    /**
     * 根据人群包id查询
     * @param crowdPackId 人群包id
     * @return 对应记录
     */
    public List<StrategyCrowdPackDo> selectByCrowdPackId(Long crowdPackId){
       return  DBUtil.selectList("strategyCrowdPack.selectByCrowdPackId", crowdPackId);
    }

    public List<StrategyCrowdPackDo> selectByCrowdPackIds(List<Long> crowdIds) {
        Map<String, Object> params = new HashMap<>();
        params.put("crowdIds", crowdIds);
        return DBUtil.selectList("strategyCrowdPack.selectByCrowdPackIds", params);
    }
}
