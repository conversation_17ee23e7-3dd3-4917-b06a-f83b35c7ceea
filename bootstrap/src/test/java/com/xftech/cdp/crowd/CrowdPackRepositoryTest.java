package com.xftech.cdp.crowd;

import com.xftech.cdp.BaseTest;
import org.junit.Test;

/**
 * @<NAME_EMAIL>
 */
public class CrowdPackRepositoryTest extends BaseTest {

    @Test
    public void selectPage() {
    }

    @Test
    public void queryAllEffectiveCrowdsByTime() {
    }

    @Test
    public void selectMinExecTimeBo() {
    }

    @Test
    public void queryLeRefreshTime() {
    }

    @Test
    public void selectByName() {
    }

    @Test
    public void selectEffectiveCrowd() {
    }

    @Test
    public void countCrowdPackNum() {
    }

    @Test
    public void countCrowdPackUserNum() {
    }

    @Test
    public void selectAllCrowdIdsByTypeNoDFlag() {
    }

    @Test
    public void insert() {
    }

    @Test
    public void selectById() {
    }

    @Test
    public void updateById() {
    }

    @Test
    public void updateBatchById() {
    }

    @Test
    public void deleteById() {
    }
}