<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="strategyMarketEventConditionMapper">

    <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketEventConditionDo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="strategyId" column="strategy_id" jdbcType="BIGINT"/>
        <result property="timeType" column="time_type" jdbcType="SMALLINT"/>
        <result property="timeValue" column="time_value" jdbcType="INTEGER"/>
        <result property="labelName" column="label_name" jdbcType="VARCHAR"/>
        <result property="operateType" column="operate_type" jdbcType="VARCHAR"/>
        <result property="conditionValue" column="condition_value" jdbcType="VARCHAR"/>
        <result property="expression" column="expression" jdbcType="VARCHAR"/>
        <result property="relationship" column="relationship" jdbcType="SMALLINT"/>
        <result property="level" column="level" jdbcType="SMALLINT"/>
        <result property="parentId" column="parent_id" jdbcType="BIGINT"/>
        <result property="optional" column="optional" jdbcType="SMALLINT"/>
        <result property="dFlag" column="d_flag" jdbcType="SMALLINT"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="createdOp" column="created_op" jdbcType="VARCHAR"/>
        <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
        <result property="updatedOp" column="updated_op" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,strategy_id,
        time_type,time_value,label_name,
        operate_type,condition_value,expression,optional,
        relationship,`level`,parent_id,
        d_flag,created_time,created_op,
        updated_time,updated_op
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_market_event_condition
        where id = #{id,jdbcType=BIGINT} and d_flag = 0
    </select>

    <select id="getByStrategyId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_market_event_condition
        where strategy_id = #{strategyId} and d_flag = 0
    </select>

    <select id="getByStrategyIdAndOption" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_market_event_condition
        where strategy_id = #{strategyId} and optional != 2 and d_flag = 0
    </select>


    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketEventConditionDo"
            useGeneratedKeys="true">
        insert into strategy_market_event_condition
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="strategyId != null">strategy_id,</if>
            <if test="timeType != null">time_type,</if>
            <if test="timeValue != null">time_value,</if>
            <if test="labelName != null">label_name,</if>
            <if test="operateType != null">operate_type,</if>
            <if test="conditionValue != null">condition_value,</if>
            <if test="expression != null">expression,</if>
            <if test="relationship != null">relationship,</if>
            <if test="level != null">level,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="optional != null">optional,</if>
            <if test="dFlag != null">d_flag,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="createdOp != null">created_op,</if>
            <if test="updatedTime != null">updated_time,</if>
            <if test="updatedOp != null">updated_op,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="strategyId != null">#{strategyId,jdbcType=BIGINT},</if>
            <if test="timeType != null">#{timeType,jdbcType=SMALLINT},</if>
            <if test="timeValue != null">#{timeValue,jdbcType=INTEGER},</if>
            <if test="labelName != null">#{labelName,jdbcType=VARCHAR},</if>
            <if test="operateType != null">#{operateType,jdbcType=VARCHAR},</if>
            <if test="conditionValue != null">#{conditionValue,jdbcType=VARCHAR},</if>
            <if test="expression != null">#{expression,jdbcType=VARCHAR},</if>
            <if test="relationship != null">#{relationship,jdbcType=SMALLINT},</if>
            <if test="level != null">#{level,jdbcType=SMALLINT},</if>
            <if test="parentId != null">#{parentId,jdbcType=BIGINT},</if>
            <if test="optional != null">#{optional,jdbcType=SMALLINT},</if>
            <if test="dFlag != null">#{dFlag,jdbcType=SMALLINT},</if>
            <if test="createdTime != null">#{createdTime,jdbcType=TIMESTAMP},</if>
            <if test="createdOp != null">#{createdOp,jdbcType=VARCHAR},</if>
            <if test="updatedTime != null">#{updatedTime,jdbcType=TIMESTAMP},</if>
            <if test="updatedOp != null">#{updatedOp,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketEventConditionDo">
        update strategy_market_event_condition
        <set>
            <if test="strategyId != null">
                strategy_id = #{strategyId,jdbcType=BIGINT},
            </if>
            <if test="timeType != null">
                time_type = #{timeType,jdbcType=SMALLINT},
            </if>
            <if test="timeValue != null">
                time_value = #{timeValue,jdbcType=INTEGER},
            </if>
            <if test="labelName != null">
                label_name = #{labelName,jdbcType=VARCHAR},
            </if>
            <if test="operateType != null">
                operate_type = #{operateType,jdbcType=VARCHAR},
            </if>
            <if test="conditionValue != null">
                condition_value = #{conditionValue,jdbcType=VARCHAR},
            </if>
            <if test="expression != null">
                expression = #{expression,jdbcType=VARCHAR},
            </if>
            <if test="relationship != null">
                relationship = #{relationship,jdbcType=SMALLINT},
            </if>
            <if test="level != null">
                level = #{level,jdbcType=SMALLINT},
            </if>
            <if test="parentId != null">
                parent_id = #{parentId,jdbcType=BIGINT},
            </if>
            <if test="optional != null">
                optional = #{optional,jdbcType=SMALLINT},
            </if>
            <if test="dFlag != null">
                d_flag = #{dFlag,jdbcType=SMALLINT},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createdOp != null">
                created_op = #{createdOp,jdbcType=VARCHAR},
            </if>
            <if test="updatedTime != null">
                updated_time = #{updatedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedOp != null">
                updated_op = #{updatedOp,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        update
            strategy_market_event_condition
        set d_flag =1
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <update id="deleteByStrategyIdAndOptional" >
        update
            strategy_market_event_condition
        set d_flag =1
        where strategy_id = #{strategyId} and optional = #{optional} and d_flag = 0
    </update>

</mapper>
