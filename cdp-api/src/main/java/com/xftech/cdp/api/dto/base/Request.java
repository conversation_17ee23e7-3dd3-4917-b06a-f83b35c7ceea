package com.xftech.cdp.api.dto.base;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2023/2/9
 */
@Setter
@Getter
public class Request {


    /**
     * Token，可能为空
     */
    @ApiModelProperty(hidden = true)
    private transient String token;

    /**
     * 为系统分配的唯一标识
     */
    @ApiModelProperty(hidden = true)
    private String ua;

    /**
     * 外部传进来的请求流水号，可能会是空
     */
    @ApiModelProperty(hidden = true)
    private String seq;

    /**
     * 内部请求生成的RequestId，可以用户追踪日志记录
     */
    @ApiModelProperty(hidden = true)
    private String requestId;

    public String toJsonString() {
        return JSON.toJSONString(this);
    }
}
