package com.xftech.cdp.feign.model.aitel;

import java.util.List;

import lombok.Data;

@Data
public class BizSourceResponse {

    /**
     * 业务来源信息id
     */
    private int id;

    /**
     * 业务线类型
     */
    private String bizLine;

    /**
     * 业务线id
     */
    private String bizLineId;

    /**
     * 业务线类型名称
     */
    private String bizLineName;

    /**
     * 业务来源code
     */
    private String bizSourceCode;

    /**
     * 业务来源id
     */
    private String bizSourceId;

    /**
     * 业务来源名称
     */
    private String bizSourceName;

    /**
     * 线路组集合
     */
    private List<LineGroupInfo> lineGroupInfo;

    /**
     * 厂商策略集合
     */
    private List<BizSourceVendorStrategyVO> bizSourceVendorStrategyVOList;

    /**
     * 并发数
     */
    private int capacityNum;

    /**
     * 创建时间 yyyy-MM-dd HH:mm:ss
     */
    private String createdTime;

    /**
     * 更新时间 yyyy-MM-dd HH:mm:ss
     */
    private String updatedTime;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 路由规则 0:随机 1:手机路由 2:用户号 3:业务编码
     */
    private int routeKeyType;

    /**
     * 触发节点 逾期前提醒、逾期后提醒、首贷、复贷
     */
    private String triggerMode;

    /**
     * 外呼开始时间 格式：HH:mm
     */
    private String callStartTime;

    /**
     * 外呼结束时间 格式：HH:mm
     */
    private String callEndTime;

    /**
     * 外呼类型：1-点呼，2-预测外呼，3-AI，4-IVR
     */
    private int outboundCallType;

    /**
     * 触发节点描述
     */
    private String triggerModeDesc;

}