package com.xftech.cdp.infra.repository.subtable.po;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023-03-17
 */
@Data
public class SplitTableCur implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 自增id
     */
    private Long id;
    /**
     * 主表名
     */
    private String tableName;
    /**
     * 当前分表序号
     */
    private Integer curTableNo;
    /**
     * 创建时间
     */
    private Date createdTime;
    /**
     * 更新时间
     */
    private Date updatedTime;
}