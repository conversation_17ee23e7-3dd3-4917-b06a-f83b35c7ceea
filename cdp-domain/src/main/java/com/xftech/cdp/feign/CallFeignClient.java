package com.xftech.cdp.feign;

import java.util.List;

import com.xftech.cdp.feign.common.BizResponse;
import com.xftech.cdp.feign.model.aitel.BizSourceResponse;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 *
 * <AUTHOR>
 */
public interface CallFeignClient {

    /**
     * 用于根据业务线查询出可能用到的业务来源（查询外呼中台接口）
     */
    @GetMapping("/api/v1/businessSource/queryBizSourceByBizLine")
    BizResponse<List<BizSourceResponse>> queryBizSourceByBizLine(@RequestParam("bizLine") String bizLine);

}
