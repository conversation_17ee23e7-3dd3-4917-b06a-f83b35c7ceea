# 触达模型提纯版设计

## 1. 设计理念

### 1.1 业务本质分析

触达的业务本质是：**在合适的时机，通过合适的渠道，向目标用户传递有价值的信息或权益**。

触达业务的核心要素：
- **WHO**: 触达对象（用户/用户群体）
- **WHAT**: 触达内容（消息、权益、服务）
- **HOW**: 触达方式（渠道）
- **WHEN**: 触达时机（实时、延时、定时）

### 1.2 提纯原则

1. **业务纯粹性**: 模型只包含业务概念，不包含技术实现细节
2. **渠道无关性**: 模型不感知上游触达类型（T0/离线），专注触达执行
3. **领域完整性**: 包含触达业务的完整生命周期
4. **扩展友好性**: 支持新渠道、新业务场景的扩展

### 1.3 整体架构流程

```mermaid
graph TB
    %% 输入层 - 现有触达入口
    A1[T0普通触达<br/>execSend] --> C[触达请求转换器<br/>TouchRequestConverter]
    A2[T0引擎触达<br/>marketingSend] --> C
    A3[离线普通触达<br/>dispatchHandler] --> C
    A4[离线引擎触达<br/>marketingSend] --> C

    %% 转换层
    C --> D[统一触达请求<br/>TouchRequest]

    %% 核心业务模型
    D --> E[触达服务<br/>TouchService]

    %% 业务处理层
    E --> F{触达时机判断}
    F -->|立即| G1[立即处理]
    F -->|延时| G2[延时队列]
    F -->|定时| G3[定时调度]

    %% 时机处理
    G2 --> H[时机到达]
    G3 --> H
    G1 --> H

    %% 业务逻辑处理
    H --> I[业务上下文解析]
    I --> J[流控检查]
    J -->|通过| K{处理模式判断}
    J -->|拒绝| L[流控拒绝响应]

    %% 处理模式分支
    K -->|单个| K1[单个用户处理]
    K -->|批量| K2[批量用户处理]
    K1 --> M[渠道路由]
    K2 --> M

    %% 渠道处理
    M --> N{渠道类型}
    N -->|短信| N1[SMS渠道插件]
    N -->|电销| N2[VOICE渠道插件]
    N -->|推送| N3[PUSH渠道插件]
    N -->|优惠券| N4[COUPON渠道插件]
    N -->|AI外呼| N5[AI_CALL渠道插件]
    N -->|其他| N6[其他渠道插件]

    %% 渠道执行
    N1 --> O[渠道执行结果]
    N2 --> O
    N3 --> O
    N4 --> O
    N5 --> O
    N6 --> O

    %% 结果处理
    O --> P[触达响应<br/>TouchResponse]
    L --> P

    %% 输出层
    P --> Q[响应转换器<br/>TouchResponseConverter]
    Q --> R1[execSend响应]
    Q --> R2[marketingSend响应]
    Q --> R3[dispatchHandler响应]

    %% 样式定义
    classDef inputClass fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef coreClass fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef processClass fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef channelClass fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef outputClass fill:#fce4ec,stroke:#880e4f,stroke-width:2px

    class A1,A2,A3,A4 inputClass
    class D,E coreClass
    class F,G1,G2,G3,H,I,J,K,K1,K2,M processClass
    class N,N1,N2,N3,N4,N5,N6 channelClass
    class P,Q,R1,R2,R3 outputClass
```

## 2. 核心领域模型

### 2.1 模型关系图

```mermaid
classDiagram
  class TouchRequest {
    +String requestId
    +String businessScene
    +LocalDateTime requestTime
    +TouchTarget target
    +List~TouchTarget~ targetBatch
    +TouchMessage message
    +TouchChannel channel
    +TouchTiming timing
    +TouchConfig config
    +BusinessContext businessContext
    +List~TouchChain~ nextTouchChain
  }

  class TouchTarget {
    +Long userId
    +String mobile
    +String appId
    +String innerAppId
    +String deviceId
    +String abTestNumber
    +Map~String,String~ userTags
    +Map~String,Object~ extData
  }

  class TouchMessage {
    +String templateId
    +String title
    +String content
    +Map~String,Object~ templateParams
    +List~Object~ batchTemplateParams
    +Map~String,Object~ attachments
  }

  class TouchTiming {
    +TimingType timingType
    +ProcessingMode processingMode
    +Integer delaySeconds
    +LocalDateTime scheduledTime
    +BatchConfig batchConfig
    +TimeWindow timeWindow
  }

  class TouchConfig {
    +TouchPriority priority
    +Boolean allowDuplicate
    +Integer validityMinutes
    +Boolean requireReceipt
    +RetryConfig retryConfig
    +Map~String,Object~ channelSpecificConfig
  }

  class BusinessContext {
    +Long strategyId
    +Long strategyGroupId
    +Long strategyChannelId
    +Long strategyExecLogId
    +String activityId
    +String eventType
    +String abTestGroup
    +List~Long~ crowdIds
    +String detailTableNo
    +List~FlowControlRule~ flowControlRules
    +Map~String,String~ businessTags
    +Map~String,Object~ extProperties
  }

  class TouchResponse {
    +String requestId
    +TouchResult result
    +String batchNo
    +LocalDateTime responseTime
    +ChannelResponse channelResponse
    +BusinessFeedback businessFeedback
  }

  class TouchChannel {
    <<enumeration>>
    SMS
    VOICE
    PUSH
    COUPON
    AI_CALL
    LIFE_RIGHTS
    INTEREST_FREE
    CREDIT_INCREASE
  }

  class TimingType {
    <<enumeration>>
    IMMEDIATE
    DELAY
    SCHEDULED
  }

  class ProcessingMode {
    <<enumeration>>
    SINGLE
    BATCH
  }

  class TouchChain {
    +String chainId
    +ChainTrigger trigger
    +TouchRequest nextTouch
    +Integer priority
    +Boolean enabled
  }

  class ChainTrigger {
    +TriggerType triggerType
    +Integer delaySeconds
    +String conditionExpression
    +Integer maxWaitSeconds
  }

  class TriggerType {
    <<enumeration>>
    SUCCESS
    FAILED
    ALWAYS
    CONDITIONAL
  }

  class TouchResult {
    <<enumeration>>
    SUCCESS
    FAILED
    REJECTED
    PROCESSING
    CANCELLED
  }

  class BatchConfig {
    +Integer batchSize
    +Integer batchIntervalSeconds
    +Integer maxWaitSeconds
  }

  class TimeWindow {
    +String startTime
    +String endTime
    +List~Integer~ allowedWeekdays
    +String timezone
    +isInTimeWindow(LocalDateTime) Boolean
  }

  class RetryConfig {
    +Integer maxRetries
    +Integer retryIntervalSeconds
    +Boolean exponentialBackoff
  }

  class FlowControlRule {
    +Long id
    +String name
    +String description
    +FlowControlStatus status
    +FlowControlType type
    +String effectiveStrategy
    +String effectiveChannel
    +Integer dayCount
    +Integer weekCount
    +Integer monthCount
    +StrategyType strategyType
    +Integer limitDays
    +Integer limitTimes
    +String bizType
    +LocalDateTime createdTime
    +LocalDateTime updatedTime
    +String createdOp
    +String updatedOp
  }

%% 修正后的关系定义
  TouchRequest "1" -- "1" TouchTarget : contains
  TouchRequest "1" -- "*" TouchTarget : containsBatch
  TouchRequest "1" -- "1" TouchMessage : contains
  TouchRequest "1" -- "1" TouchChannel : uses
  TouchRequest "1" -- "1" TouchTiming : schedules
  TouchRequest "1" -- "1" TouchConfig : configures
  TouchRequest "1" -- "1" BusinessContext : context
  TouchRequest "1" -- "*" TouchChain : hasChain

  TouchTiming "1" -- "0..1" BatchConfig : has
  TouchTiming "1" -- "0..1" TimeWindow : has
  TouchConfig "1" -- "0..1" RetryConfig : has
  BusinessContext "1" -- "*" FlowControlRule : hasRules

  TouchTiming "1" -- "1" TimingType : type
  TouchTiming "1" -- "1" ProcessingMode : mode
  TouchChain "1" -- "1" ChainTrigger : hasTrigger
  TouchChain "1" -- "1" TouchRequest : nextTouch
  ChainTrigger "1" -- "1" TriggerType : type
  TouchRequest "1" --> "1" TouchResponse : generates
  TouchResponse "1" -- "1" TouchResult : status
```

### 2.2 触达请求模型 (TouchRequest)

```java
/**
 * 触达请求 - 纯粹的业务领域模型
 * 专注于"触达什么内容给谁"的业务语义
 */
public class TouchRequest {

    // ===== 请求标识 =====
    /**
     * 请求唯一标识
     */
    private String requestId;

    /**
     * 业务场景标识
     * 用于区分不同的业务场景：营销活动、风险提醒、服务通知等
     */
    private String businessScene;

    /**
     * 请求时间
     */
    private LocalDateTime requestTime;

    // ===== 触达对象 =====
    /**
     * 目标用户（单用户触达）
     */
    private TouchTarget target;

    /**
     * 目标用户群体（批量触达）
     * 用于离线普通触达的批量用户处理
     */
    private List<TouchTarget> targetBatch;

    // ===== 触达内容 =====
    /**
     * 触达消息
     */
    private TouchMessage message;

    // ===== 触达方式 =====
    /**
     * 触达渠道
     */
    private TouchChannel channel;

    // ===== 触达时机 =====
    /**
     * 触达时机配置
     */
    private TouchTiming timing;

    // ===== 触达配置 =====
    /**
     * 触达配置
     */
    private TouchConfig config;

    // ===== 业务上下文 =====
    /**
     * 业务上下文信息
     * 包含策略、活动、事件等业务相关信息
     */
    private BusinessContext businessContext;

    // ===== 组合触达 =====
    /**
     * 后续触达链
     * 用于支持时序组合触达，如：发券后通知短信
     */
    private List<TouchChain> nextTouchChain;
}
```

#### 2.2.1 触达对象 (TouchTarget)

```java

/**
 * 触达目标用户
 */
public class TouchTarget {
    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 应用标识
     */
    private String appId;

    /**
     * 内部应用标识
     * 用于离线普通触达的应用区分
     */
    private String innerAppId;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * AB测试编号
     */
    private String abTestNumber;

    /**
     * 用户标签
     * 用于个性化触达和AB测试
     */
    private Map<String, String> userTags;

    /**
     * 用户扩展数据
     * 用于存储模板参数等用户相关的扩展信息
     */
    private Map<String, Object> extData;
}
```

#### 2.2.2 触达内容 (TouchMessage)

```java

/**
 * 触达消息
 */
public class TouchMessage {
    /**
     * 消息模板ID
     */
    private String templateId;

    /**
     * 消息标题
     */
    private String title;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 模板参数（单用户）
     * 用于动态替换模板中的占位符
     */
    private Map<String, Object> templateParams;

    /**
     * 批量模板参数（批量触达）
     * 用于离线普通触达的批量模板参数处理
     * List中每个元素对应targetBatch中对应位置的用户
     */
    private List<Object> batchTemplateParams;

    /**
     * 附加数据
     * 渠道特定的消息数据，如优惠券ID、权益配置等
     */
    private Map<String, Object> attachments;
}
```

#### 2.2.3 触达方式 (TouchChannel)

```java

/**
 * 触达渠道
 */
public enum TouchChannel {
  /**
   * 短信渠道
   */
  SMS("短信", "通过短信发送文本消息"),

  /**
   * 语音外呼
   */
  VOICE("语音外呼", "通过电话语音传达信息"),

  /**
   * 应用推送
   */
  PUSH("应用推送", "通过APP推送消息"),

  /**
   * 优惠券
   */
  COUPON("优惠券", "发放优惠券权益"),

  /**
   * AI外呼
   */
  AI_CALL("AI外呼", "通过AI智能外呼"),

  /**
   * 生活权益
   */
  LIFE_RIGHTS("生活权益", "发放生活权益"),

  /**
   * 免息权益
   */
  INTEREST_FREE("免息权益", "提供免息服务"),

  /**
   * 额度提升
   */
  CREDIT_INCREASE("额度提升", "提升用户信用额度");

  private final String displayName;
  private final String description;

}
```

#### 2.2.4 触达时机 (TouchTiming)
```java

/**
 * 触达时机配置
 */
public class TouchTiming {
    /**
     * 触达时机类型：IMMEDIATE(立即)、DELAY(延时)、SCHEDULED(定时)
     */
    private TimingType timingType;

    /**
     * 处理模式：SINGLE(单个)、BATCH(批量)
     */
    private ProcessingMode processingMode;

    /**
     * 延时时间（秒）
     * 当timingType为DELAY时使用
     */
    private Integer delaySeconds;

    /**
     * 计划执行时间
     * 当timingType为SCHEDULED时使用
     */
    private LocalDateTime scheduledTime;

    /**
     * 批量处理配置
     * 当processingMode为BATCH时使用
     */
    private BatchConfig batchConfig;

    /**
     * 触达时间窗口
     * 定义允许触达的时间范围
     */
    private TimeWindow timeWindow;
}

/**
 * 触达时机类型 - 纯粹的时间概念
 */
public enum TimingType {
    /**
     * 立即触达 - 收到请求后立即执行
     */
    IMMEDIATE("立即触达"),

    /**
     * 延时触达 - 延迟指定时间后执行
     */
    DELAY("延时触达"),

    /**
     * 定时触达 - 在指定时间点执行
     */
    SCHEDULED("定时触达");

    private final String displayName;
}

/**
 * 触达处理模式 - 描述如何处理
 */
public enum ProcessingMode {
    /**
     * 单个处理 - 逐个处理用户
     */
    SINGLE("单个处理"),

    /**
     * 批量处理 - 批量处理用户群体
     */
    BATCH("批量处理");

    private final String displayName;
}
```
#### 2.2.5 触达配置 (TouchConfig)

```java
/**
 * 触达配置
 */
public class TouchConfig {
    /**
     * 触达优先级：HIGH(高)、NORMAL(普通)、LOW(低)
     */
    private TouchPriority priority;

    /**
     * 是否允许重复触达
     */
    private Boolean allowDuplicate;

    /**
     * 触达有效期（分钟）
     */
    private Integer validityMinutes;

    /**
     * 是否需要回执确认
     */
    private Boolean requireReceipt;

    /**
     * 重试配置
     */
    private RetryConfig retryConfig;

    /**
     * 渠道特定配置
     */
    private Map<String, Object> channelSpecificConfig;
}

/**
 * 触达优先级
 */
public enum TouchPriority {
    HIGH("高优先级"),
    NORMAL("普通优先级"),
    LOW("低优先级");

    private final String displayName;
}

/**
 * 重试配置
 */
public class RetryConfig {
    /**
     * 最大重试次数
     */
    private Integer maxRetries;

    /**
     * 重试间隔（秒）
     */
    private Integer retryIntervalSeconds;

    /**
     * 是否启用指数退避
     */
    private Boolean exponentialBackoff;
}

/**
 * 批量处理配置
 */
public class BatchConfig {
  /**
   * 批量大小
   */
  private Integer batchSize;

  /**
   * 批量处理间隔（秒）
   */
  private Integer batchIntervalSeconds;

  /**
   * 最大等待时间（秒）
   */
  private Integer maxWaitSeconds;
}

/**
 * 时间窗口配置
 */
public class TimeWindow {
  /**
   * 开始时间（HH:mm格式）
   */
  private String startTime;

  /**
   * 结束时间（HH:mm格式）
   */
  private String endTime;

  /**
   * 允许的星期几（1-7，1为周一）
   */
  private List<Integer> allowedWeekdays;

  /**
   * 时区
   */
  private String timezone;

  /**
   * 检查当前时间是否在允许的时间窗口内
   */
  public boolean isInTimeWindow(LocalDateTime currentTime) {
    // 实现时间窗口检查逻辑
    return true;
  }
}
```

#### 2.2.6 业务上下文 (BusinessContext)

```java
/**
 * 业务上下文
 */
public class BusinessContext {
    /**
     * 策略ID
     */
    private Long strategyId;

    /**
     * 策略分组ID
     */
    private Long strategyGroupId;

    /**
     * 策略渠道ID
     */
    private Long strategyChannelId;

    /**
     * 策略执行日志ID
     */
    private Long strategyExecLogId;

    /**
     * 活动ID
     */
    private String activityId;

    /**
     * 业务事件类型
     */
    private String eventType;

    /**
     * AB测试分组
     */
    private String abTestGroup;

    /**
     * 人群包ID列表
     * 用于离线普通触达的人群包信息
     */
    private List<Long> crowdIds;

    /**
     * 明细表序号
     * 用于离线普通触达的数据分表
     */
    private String detailTableNo;
    
    /**
     * 业务标签
     */
    private Map<String, String> businessTags;

    /**
     * 扩展属性
     */
    private Map<String, Object> extProperties;
}
```

#### 2.2.7 触达链 (TouchChain)

```java
/**
 * 触达链 - 支持时序组合触达
 * 用于定义当前触达完成后的后续触达动作
 */
public class TouchChain {
    /**
     * 链条ID，用于标识和追踪
     */
    private String chainId;

    /**
     * 触发条件
     */
    private ChainTrigger trigger;

    /**
     * 后续触达请求
     */
    private TouchRequest nextTouch;

    /**
     * 链条优先级
     */
    private Integer priority;

    /**
     * 是否启用
     */
    private Boolean enabled;
}

/**
 * 链条触发条件
 */
public class ChainTrigger {
    /**
     * 触发类型：SUCCESS(成功后)、FAILED(失败后)、ALWAYS(总是)、CONDITIONAL(条件触发)
     */
    private TriggerType triggerType;

    /**
     * 延时时间（秒）
     * 前一个触达完成后延时多久执行下一个
     */
    private Integer delaySeconds;

    /**
     * 触发条件表达式
     * 当triggerType为CONDITIONAL时使用，支持SpEL表达式
     */
    private String conditionExpression;

    /**
     * 最大等待时间（秒）
     * 超过此时间未触发则取消后续触达
     */
    private Integer maxWaitSeconds;
}

/**
 * 触发类型
 */
public enum TriggerType {
    /**
     * 成功后触发
     */
    SUCCESS("成功后触发"),

    /**
     * 失败后触发
     */
    FAILED("失败后触发"),

    /**
     * 总是触发
     */
    ALWAYS("总是触发"),

    /**
     * 条件触发
     */
    CONDITIONAL("条件触发");

    private final String displayName;
}
```

### 2.3 触达响应模型 (TouchResponse)

```java
/**
 * 触达响应 - 纯粹的业务结果
 */
public class TouchResponse {
    /**
     * 请求ID
     */
    private String requestId;
    
    /**
     * 触达结果
     */
    private TouchResult result;
    
    /**
     * 触达批次号
     * 用于追踪和查询
     */
    private String batchNo;
    
    /**
     * 响应时间
     */
    private LocalDateTime responseTime;
    
    /**
     * 渠道响应信息
     */
    private ChannelResponse channelResponse;
    
    /**
     * 业务反馈
     */
    private BusinessFeedback businessFeedback;
}

/**
 * 触达结果
 */
public enum TouchResult {
    /**
     * 成功
     */
    SUCCESS("成功"),
    
    /**
     * 失败
     */
    FAILED("失败"),
    
    /**
     * 被拒绝（频控、黑名单等）
     */
    REJECTED("被拒绝"),
    
    /**
     * 处理中
     */
    PROCESSING("处理中"),
    
    /**
     * 已取消
     */
    CANCELLED("已取消");
    
    private final String displayName;
    
    /**
     * 是否为最终状态
     */
    public boolean isFinalResult() {
        return this != PROCESSING;
    }
    
    /**
     * 是否为成功状态
     */
    public boolean isSuccess() {
        return this == SUCCESS;
    }
}

/**
 * 渠道响应信息
 */
public class ChannelResponse {
    /**
     * 渠道返回码
     */
    private String channelCode;
    
    /**
     * 渠道返回消息
     */
    private String channelMessage;
    
    /**
     * 渠道特定数据
     */
    private Map<String, Object> channelData;
}

/**
 * 业务反馈
 */
public class BusinessFeedback {
    /**
     * 业务处理结果
     */
    private String businessResult;
    
    /**
     * 业务消息
     */
    private String businessMessage;
    
    /**
     * 业务数据
     */
    private Map<String, Object> businessData;
}
```

## 3. 设计特点

### 3.1 业务语义清晰

- **TouchTarget**: 明确触达对象，包含用户标识和标签
- **TouchMessage**: 专注消息内容，支持模板化和个性化，不区分消息业务类型
- **TouchChannel**: 纯粹的渠道概念，不包含技术实现
- **TouchTiming**: 清晰的时机控制，分离时间概念和处理方式
  - `TimingType`: 回答"何时执行" - 立即、延时、定时
  - `ProcessingMode`: 回答"如何处理" - 单个、批量
  - 两者可以灵活组合，如"立即+批量"、"延时+单个"等
- **TouchChain**: 组合触达支持，实现时序化的触达流程
  - `ChainTrigger`: 灵活的触发条件（成功、失败、条件、总是）
  - `TriggerType`: 支持多种触发场景
  - 支持嵌套链条，实现复杂的触达流程
- **BusinessContext**: 业务上下文，支持策略、活动、事件等业务场景

### 3.2 渠道无关性

- 模型不感知T0/离线等技术分类
- 专注于触达的业务本质：向谁发什么内容
- 通过BusinessContext承载业务相关信息

### 3.3 扩展友好

- 支持新渠道类型的扩展
- 支持新消息类型的扩展
- 通过Map类型支持渠道特定配置
- 通过业务上下文支持新业务场景
- 支持组合触达场景的扩展，如多级营销流程

### 3.4 完整性

- 覆盖触达的完整生命周期
- 包含配置、重试、回执等完整业务要素
- 支持同步和异步处理模式
- 支持时序组合触达，满足复杂业务场景需求

## 4. 与原模型对比

| 对比维度 | 原模型（大而全） | 提纯版模型 |
|---------|----------------|-----------|
| **设计理念** | 技术参数集合 | 业务领域模型 |
| **参数来源** | 直接映射现有接口参数 | 抽象业务概念 |
| **技术感知** | 强感知T0/离线分类 | 技术无关 |
| **触达对象** | 单一用户模型 | 支持单用户+批量用户 |
| **时机控制** | 隐含在技术实现中 | 显式的时机配置模型 |
| **扩展性** | 参数堆叠式扩展 | 领域概念式扩展 |
| **可读性** | 技术导向 | 业务导向 |
| **维护性** | 与现有代码强耦合 | 业务稳定，技术可变 |
| **支持场景** | 需要4个不同模型 | 统一模型支持所有场景 |

## 5. 实施建议

### 5.1 渐进式迁移

1. **第一阶段**: 实现提纯版模型和转换器
2. **第二阶段**: 在新的xyf-touch-service中使用提纯版模型
3. **第三阶段**: 逐步迁移现有业务到新模型

### 5.2 兼容性保证

- 提供原模型到提纯版模型的转换器
- 保持现有接口不变，内部使用新模型
- 支持新旧模型并存的过渡期

### 5.3 业务价值

- **降低理解成本**: 业务人员可以直接理解模型含义
- **提高扩展性**: 新业务场景可以自然融入模型
- **减少维护成本**: 业务模型相对稳定，减少因技术变更导致的模型调整
- **支持业务创新**: 纯粹的业务模型为业务创新提供更好的基础

## 6. 使用示例

### 6.1 短信营销触达示例

```java
// 构建短信营销触达请求
TouchRequest smsRequest = TouchRequest.builder()
    .requestId("REQ_" + System.currentTimeMillis())
    .businessScene("MARKETING_PROMOTION")
    .requestTime(LocalDateTime.now())
    .target(TouchTarget.builder()
        .userId(12345L)
        .mobile("***********")
        .appId("xinfei-app")
        .userTags(Map.of("vip_level", "gold", "region", "beijing"))
        .build())
    .message(TouchMessage.builder()
        .templateId("SMS_PROMO_001")
        .title("限时优惠")
        .content("尊敬的${userName}，您有一张${amount}元优惠券即将过期")
        .templateParams(Map.of("userName", "张先生", "amount", "100"))
        .build())
    .channel(TouchChannel.SMS)
    .timing(TouchTiming.builder()
        .timingType(TimingType.IMMEDIATE)
        .processingMode(ProcessingMode.SINGLE)
        .build())
    .config(TouchConfig.builder()
        .priority(TouchPriority.NORMAL)
        .allowDuplicate(false)
        .validityMinutes(60)
        .requireReceipt(true)
        .build())
    .businessContext(BusinessContext.builder()
        .strategyId(1001L)
        .activityId("PROMO_2025_001")
        .eventType("LOGIN")
        .abTestGroup("GROUP_A")
        .build())
    .build();
```

### 6.2 AI外呼服务触达示例

```java
// 构建AI外呼服务触达请求
TouchRequest aiCallRequest = TouchRequest.builder()
    .requestId("REQ_AI_" + System.currentTimeMillis())
    .businessScene("CUSTOMER_SERVICE")
    .requestTime(LocalDateTime.now())
    .target(TouchTarget.builder()
        .userId(67890L)
        .mobile("***********")
        .appId("xinfei-app")
        .userTags(Map.of("risk_level", "high", "product", "credit"))
        .build())
    .message(TouchMessage.builder()
        .templateId("AI_CALL_RISK_001")
        .title("风险提醒")
        .content("关于您的账户安全提醒")
        .attachments(Map.of("call_script_id", "SCRIPT_001", "max_duration", 300))
        .build())
    .channel(TouchChannel.AI_CALL)
    .timing(TouchTiming.builder()
        .timingType(TimingType.IMMEDIATE)
        .processingMode(ProcessingMode.SINGLE)
        .build())
    .config(TouchConfig.builder()
        .priority(TouchPriority.HIGH)
        .allowDuplicate(true)
        .validityMinutes(30)
        .requireReceipt(true)
        .retryConfig(RetryConfig.builder()
            .maxRetries(3)
            .retryIntervalSeconds(300)
            .exponentialBackoff(true)
            .build())
        .build())
    .businessContext(BusinessContext.builder()
        .strategyId(2001L)
        .eventType("RISK_ALERT")
        .businessTags(Map.of("risk_type", "fraud", "urgency", "high"))
        .build())
    .build();
```

### 6.3 优惠券权益触达示例

```java
// 构建优惠券权益触达请求
TouchRequest couponRequest = TouchRequest.builder()
    .requestId("REQ_COUPON_" + System.currentTimeMillis())
    .businessScene("BENEFIT_GRANT")
    .requestTime(LocalDateTime.now())
    .target(TouchTarget.builder()
        .userId(11111L)
        .mobile("***********")
        .appId("xinfei-app")
        .deviceId("DEVICE_001")
        .userTags(Map.of("new_user", "true", "channel", "app"))
        .build())
    .message(TouchMessage.builder()
        .templateId("COUPON_WELCOME_001")
        .title("新用户专享优惠券")
        .content("恭喜您获得新用户专享优惠券")
        .attachments(Map.of(
            "coupon_type", "DISCOUNT",
            "coupon_amount", 50,
            "validity_days", 30,
            "applicable_products", List.of("LOAN", "CREDIT_CARD")
        ))
        .build())
    .channel(TouchChannel.COUPON)
    .timing(TouchTiming.builder()
        .timingType(TimingType.IMMEDIATE)
        .processingMode(ProcessingMode.SINGLE)
        .build())
    .config(TouchConfig.builder()
        .priority(TouchPriority.NORMAL)
        .allowDuplicate(false)
        .validityMinutes(1440) // 24小时
        .requireReceipt(false)
        .build())
    .businessContext(BusinessContext.builder()
        .strategyId(3001L)
        .activityId("NEW_USER_WELCOME")
        .eventType("USER_REGISTER")
        .abTestGroup("GROUP_B")
        .businessTags(Map.of("campaign", "2025_q1", "source", "app_download"))
        .build())
    .build();
```

### 6.4 离线批量短信触达示例

```java
// 构建离线批量短信触达请求
List<TouchTarget> userBatch = Arrays.asList(
    TouchTarget.builder()
        .userId(10001L)
        .mobile("***********")
        .appId("xinfei-app")
        .innerAppId("xinfei-app")
        .abTestNumber("A001")
        .userTags(Map.of("vip_level", "gold"))
        .build(),
    TouchTarget.builder()
        .userId(10002L)
        .mobile("***********")
        .appId("xinfei-app")
        .innerAppId("xinfei-app")
        .abTestNumber("A002")
        .userTags(Map.of("vip_level", "silver"))
        .build()
);

List<Object> batchTemplateParams = Arrays.asList(
    Map.of("userName", "张先生", "amount", "100"),
    Map.of("userName", "李女士", "amount", "200")
);

TouchRequest batchSmsRequest = TouchRequest.builder()
    .requestId("REQ_BATCH_SMS_" + System.currentTimeMillis())
    .businessScene("OFFLINE_MARKETING")
    .requestTime(LocalDateTime.now())
    .targetBatch(userBatch)
    .message(TouchMessage.builder()
        .templateId("SMS_BATCH_001")
        .content("尊敬的${userName}，您有${amount}元优惠券待领取")
        .batchTemplateParams(batchTemplateParams)
        .build())
    .channel(TouchChannel.SMS)
    .timing(TouchTiming.builder()
        .timingType(TimingType.IMMEDIATE)
        .processingMode(ProcessingMode.BATCH)
        .batchConfig(BatchConfig.builder()
            .batchSize(userBatch.size())
            .batchIntervalSeconds(0)
            .maxWaitSeconds(300)
            .build())
        .timeWindow(TimeWindow.builder()
            .startTime("09:00")
            .endTime("21:00")
            .allowedWeekdays(Arrays.asList(1, 2, 3, 4, 5)) // 工作日
            .timezone("Asia/Shanghai")
            .build())
        .build())
    .config(TouchConfig.builder()
        .priority(TouchPriority.NORMAL)
        .allowDuplicate(false)
        .validityMinutes(1440)
        .requireReceipt(true)
        .retryConfig(RetryConfig.builder()
            .maxRetries(3)
            .retryIntervalSeconds(60)
            .exponentialBackoff(false)
            .build())
        .build())
    .businessContext(BusinessContext.builder()
        .strategyId(4001L)
        .strategyGroupId(401L)
        .strategyChannelId(41L)
        .crowdIds(Arrays.asList(1001L, 1002L))
        .detailTableNo("202506")
        .flowControlRules(Arrays.asList(
            FlowControlRule.builder()
                .id(1001L)
                .name("短信日频控")
                .description("短信渠道每日最多发送3次")
                .status(FlowControlStatus.ACTIVE)
                .type(FlowControlType.CHANNEL)
                .effectiveStrategy("0") // 全部策略生效
                .effectiveChannel("1") // 短信渠道
                .dayCount(3)
                .bizType("new-cust")
                .build(),
            FlowControlRule.builder()
                .id(1002L)
                .name("策略月频控")
                .description("该策略每月最多触达用户5次")
                .status(FlowControlStatus.ACTIVE)
                .type(FlowControlType.STRATEGY)
                .effectiveStrategy("4001")
                .effectiveChannel("0") // 全部渠道
                .monthCount(5)
                .strategyType(StrategyType.OFFLINE)
                .bizType("new-cust")
                .build()
        ))
        .businessTags(Map.of("execution_type", "offline_batch", "campaign", "monthly_promo"))
        .build())
    .build();
```

### 6.5 延时触达示例

```java
// 构建延时触达请求（用户注册后30分钟发送欢迎短信）
TouchRequest delayedSmsRequest = TouchRequest.builder()
    .requestId("REQ_DELAY_SMS_" + System.currentTimeMillis())
    .businessScene("WELCOME_DELAYED")
    .requestTime(LocalDateTime.now())
    .target(TouchTarget.builder()
        .userId(20001L)
        .mobile("***********")
        .appId("xinfei-app")
        .userTags(Map.of("new_user", "true", "register_source", "app"))
        .build())
    .message(TouchMessage.builder()
        .templateId("SMS_WELCOME_DELAY_001")
        .title("欢迎使用信飞")
        .content("欢迎${userName}使用信飞，您的专属福利已准备好")
        .templateParams(Map.of("userName", "新用户"))
        .build())
    .channel(TouchChannel.SMS)
    .timing(TouchTiming.builder()
        .timingType(TimingType.DELAY)
        .processingMode(ProcessingMode.SINGLE)
        .delaySeconds(1800) // 30分钟后发送
        .timeWindow(TimeWindow.builder()
            .startTime("08:00")
            .endTime("22:00")
            .timezone("Asia/Shanghai")
            .build())
        .build())
    .config(TouchConfig.builder()
        .priority(TouchPriority.HIGH)
        .allowDuplicate(false)
        .validityMinutes(720) // 12小时有效期
        .requireReceipt(true)
        .build())
    .businessContext(BusinessContext.builder()
        .strategyId(5001L)
        .eventType("USER_REGISTER")
        .businessTags(Map.of("timing_type", "delayed", "welcome_flow", "true"))
        .build())
    .build();
```

### 6.6 组合触达示例（发券后通知短信）

```java
// 构建组合触达请求：先发优惠券，成功后5分钟发送通知短信
TouchRequest combinedTouchRequest = TouchRequest.builder()
    .requestId("REQ_COMBINED_" + System.currentTimeMillis())
    .businessScene("COUPON_WITH_NOTIFICATION")
    .requestTime(LocalDateTime.now())
    .target(TouchTarget.builder()
        .userId(30001L)
        .mobile("***********")
        .appId("xinfei-app")
        .userTags(Map.of("vip_level", "gold", "campaign", "spring_festival"))
        .build())

    // 第一步：发放优惠券
    .message(TouchMessage.builder()
        .templateId("COUPON_SPRING_001")
        .title("春节专享优惠券")
        .content("恭喜您获得春节专享优惠券")
        .attachments(Map.of(
            "coupon_type", "DISCOUNT",
            "coupon_amount", 100,
            "validity_days", 7,
            "applicable_products", List.of("LOAN", "CREDIT_CARD")
        ))
        .build())
    .channel(TouchChannel.COUPON)
    .timing(TouchTiming.builder()
        .timingType(TimingType.IMMEDIATE)
        .processingMode(ProcessingMode.SINGLE)
        .build())
    .config(TouchConfig.builder()
        .priority(TouchPriority.HIGH)
        .allowDuplicate(false)
        .validityMinutes(1440)
        .requireReceipt(true)
        .build())

    // 第二步：后续触达链 - 发券成功后发送通知短信
    .nextTouchChain(Arrays.asList(
        TouchChain.builder()
            .chainId("CHAIN_SMS_NOTIFICATION")
            .trigger(ChainTrigger.builder()
                .triggerType(TriggerType.SUCCESS) // 优惠券发放成功后触发
                .delaySeconds(300) // 延时5分钟
                .maxWaitSeconds(3600) // 最大等待1小时
                .build())
            .nextTouch(TouchRequest.builder()
                .requestId("REQ_SMS_NOTIFY_" + System.currentTimeMillis())
                .businessScene("COUPON_SUCCESS_NOTIFICATION")
                .requestTime(LocalDateTime.now())
                .target(TouchTarget.builder()
                    .userId(30001L)
                    .mobile("***********")
                    .appId("xinfei-app")
                    .build())
                .message(TouchMessage.builder()
                    .templateId("SMS_COUPON_NOTIFY_001")
                    .title("优惠券到账通知")
                    .content("您的${couponAmount}元优惠券已到账，有效期${validDays}天，快去使用吧！")
                    .templateParams(Map.of(
                        "couponAmount", "100",
                        "validDays", "7"
                    ))
                    .build())
                .channel(TouchChannel.SMS)
                .timing(TouchTiming.builder()
                    .timingType(TimingType.IMMEDIATE)
                    .processingMode(ProcessingMode.SINGLE)
                    .build())
                .config(TouchConfig.builder()
                    .priority(TouchPriority.NORMAL)
                    .allowDuplicate(false)
                    .validityMinutes(60)
                    .requireReceipt(true)
                    .build())
                .businessContext(BusinessContext.builder()
                    .strategyId(6001L)
                    .eventType("COUPON_SUCCESS")
                    .businessTags(Map.of("chain_type", "notification", "parent_touch", "coupon"))
                    .build())
                .build())
            .priority(1)
            .enabled(true)
            .build()
    ))

    .businessContext(BusinessContext.builder()
        .strategyId(6000L)
        .activityId("SPRING_FESTIVAL_2025")
        .eventType("MARKETING_CAMPAIGN")
        .businessTags(Map.of("campaign_type", "combined_touch", "season", "spring"))
        .build())
    .build();
```

### 6.7 复杂组合触达示例（多级链条）

```java
// 构建复杂组合触达：发券 → 通知短信 → 未使用提醒电话
TouchRequest complexCombinedTouch = TouchRequest.builder()
    .requestId("REQ_COMPLEX_" + System.currentTimeMillis())
    .businessScene("COMPLEX_MARKETING_FLOW")
    .requestTime(LocalDateTime.now())
    .target(TouchTarget.builder()
        .userId(40001L)
        .mobile("***********")
        .appId("xinfei-app")
        .userTags(Map.of("vip_level", "platinum", "risk_level", "low"))
        .build())

    // 第一步：发放优惠券
    .message(TouchMessage.builder()
        .templateId("COUPON_VIP_001")
        .title("VIP专享优惠券")
        .content("恭喜您获得VIP专享优惠券")
        .attachments(Map.of("coupon_amount", 200, "validity_hours", 48))
        .build())
    .channel(TouchChannel.COUPON)
    .timing(TouchTiming.builder()
        .timingType(TimingType.IMMEDIATE)
        .processingMode(ProcessingMode.SINGLE)
        .build())
    .config(TouchConfig.builder()
        .priority(TouchPriority.HIGH)
        .allowDuplicate(false)
        .requireReceipt(true)
        .build())

    // 多级触达链
    .nextTouchChain(Arrays.asList(
        // 链条1：发券成功后立即发送通知短信
        TouchChain.builder()
            .chainId("CHAIN_IMMEDIATE_SMS")
            .trigger(ChainTrigger.builder()
                .triggerType(TriggerType.SUCCESS)
                .delaySeconds(60) // 1分钟后
                .build())
            .nextTouch(TouchRequest.builder()
                .requestId("REQ_IMMEDIATE_SMS_" + System.currentTimeMillis())
                .businessScene("IMMEDIATE_NOTIFICATION")
                .target(TouchTarget.builder()
                    .userId(40001L)
                    .mobile("***********")
                    .appId("xinfei-app")
                    .build())
                .message(TouchMessage.builder()
                    .templateId("SMS_IMMEDIATE_NOTIFY")
                    .content("您的200元VIP优惠券已到账，48小时内有效")
                    .build())
                .channel(TouchChannel.SMS)
                .timing(TouchTiming.builder()
                    .timingType(TimingType.IMMEDIATE)
                    .processingMode(ProcessingMode.SINGLE)
                    .build())
                .config(TouchConfig.builder()
                    .priority(TouchPriority.NORMAL)
                    .requireReceipt(true)
                    .build())

                // 嵌套链条：短信发送成功后，24小时后检查是否使用，未使用则电话提醒
                .nextTouchChain(Arrays.asList(
                    TouchChain.builder()
                        .chainId("CHAIN_USAGE_REMINDER")
                        .trigger(ChainTrigger.builder()
                            .triggerType(TriggerType.CONDITIONAL)
                            .delaySeconds(86400) // 24小时后
                            .conditionExpression("#{couponUsageService.isUnused(#userId, #couponId)}")
                            .maxWaitSeconds(172800) // 最大等待48小时
                            .build())
                        .nextTouch(TouchRequest.builder()
                            .requestId("REQ_REMINDER_CALL_" + System.currentTimeMillis())
                            .businessScene("USAGE_REMINDER")
                            .target(TouchTarget.builder()
                                .userId(40001L)
                                .mobile("***********")
                                .build())
                            .message(TouchMessage.builder()
                                .templateId("VOICE_USAGE_REMINDER")
                                .content("提醒使用优惠券")
                                .attachments(Map.of("call_script", "您有一张200元优惠券即将过期"))
                                .build())
                            .channel(TouchChannel.AI_CALL)
                            .timing(TouchTiming.builder()
                                .timingType(TimingType.IMMEDIATE)
                                .processingMode(ProcessingMode.SINGLE)
                                .build())
                            .config(TouchConfig.builder()
                                .priority(TouchPriority.NORMAL)
                                .requireReceipt(true)
                                .build())
                            .build())
                        .priority(2)
                        .enabled(true)
                        .build()
                ))
                .build())
            .priority(1)
            .enabled(true)
            .build()
    ))

    .businessContext(BusinessContext.builder()
        .strategyId(7000L)
        .activityId("VIP_RETENTION_2025")
        .eventType("VIP_MARKETING")
        .businessTags(Map.of("flow_type", "complex_chain", "target", "vip_retention"))
        .build())
    .build();
```

## 7. 转换器设计

### 7.1 转换流程时序图

```mermaid
sequenceDiagram
    participant Client as 调用方
    participant Converter as TouchRequestConverter
    participant Builder as TouchRequest.Builder
    participant Validator as 参数验证器
    participant Service as TouchService
    participant Channel as 渠道插件
    participant Response as TouchResponse

    Note over Client, Response: 四种触达方式的统一转换流程

    %% T0普通触达转换
    Client->>+Converter: fromExecSendParams(dispatchDto, crowdDetail, channelEnum, channelDo, bizEvent)
    Converter->>+Builder: 创建TouchRequest构建器
    Converter->>Builder: 设置requestId, businessScene="T0_NORMAL"
    Converter->>Builder: 设置target(单用户)
    Converter->>Builder: 设置message(模板+参数)
    Converter->>Builder: 设置channel(渠道转换)
    Converter->>Builder: 设置timing(立即执行)
    Converter->>Builder: 设置config(触达配置)
    Converter->>Builder: 设置businessContext(业务上下文)
    Builder-->>-Converter: TouchRequest实例
    Converter-->>-Client: 返回统一TouchRequest

    %% 离线普通触达转换
    Client->>+Converter: fromDispatchHandlerParams(strategyContext, app, innerApp, batch, templateParam)
    Converter->>+Builder: 创建TouchRequest构建器
    Converter->>Builder: 设置requestId, businessScene="OFFLINE_BATCH"
    Converter->>Builder: 设置targetBatch(批量用户)
    Converter->>Builder: 设置message(批量模板参数)
    Converter->>Builder: 设置channel(策略渠道)
    Converter->>Builder: 设置timing(批量执行)
    Converter->>Builder: 设置config(离线配置)
    Converter->>Builder: 设置businessContext(策略上下文)
    Builder-->>-Converter: TouchRequest实例
    Converter-->>-Client: 返回统一TouchRequest

    %% 统一处理流程
    Client->>+Validator: 验证TouchRequest
    Validator-->>-Client: 验证通过

    Client->>+Service: processTouch(touchRequest)
    Service->>Service: 解析触达时机
    Service->>Service: 执行流控检查
    Service->>Service: 渠道路由选择
    Service->>+Channel: execute(touchRequest)
    Channel->>Channel: 渠道特定处理
    Channel-->>-Service: ChannelResponse
    Service->>+Response: 构建TouchResponse
    Response-->>-Service: TouchResponse实例
    Service-->>-Client: 返回统一TouchResponse

    Note over Client, Response: 所有触达方式最终都通过统一的TouchRequest/TouchResponse处理
```

### 7.2 从现有模型到提纯版模型的转换

```java
/**
 * 触达请求转换器
 * 将现有的各种触达入参转换为提纯版TouchRequest
 */
public class TouchRequestConverter {

    /**
     * T0普通触达参数转换
     */
    public TouchRequest fromExecSendParams(DispatchDto dispatchDto,
                                         CrowdDetailDo crowdDetail,
                                         StrategyMarketChannelEnum channelEnum,
                                         StrategyMarketChannelDo channelDo,
                                         BizEventVO bizEvent) {
        return TouchRequest.builder()
            .requestId(generateRequestId())
            .businessScene(determineBusinessScene(dispatchDto, bizEvent))
            .requestTime(LocalDateTime.now())
            .target(buildTouchTarget(crowdDetail))
            .message(buildTouchMessage(dispatchDto, channelDo, bizEvent))
            .channel(convertToTouchChannel(channelEnum))
            .config(buildTouchConfig(dispatchDto))
            .businessContext(buildBusinessContext(dispatchDto, bizEvent))
            .build();
    }

    /**
     * 引擎触达参数转换
     */
    public TouchRequest fromMarketingSendParams(DispatchDto dispatchDto,
                                              CrowdDetailDo crowdDetailDo,
                                              StrategyMarketChannelEnum channelEnum,
                                              String groupName,
                                              Map detailInfo,
                                              BizEventVO bizEventVO) {
        return TouchRequest.builder()
            .requestId(generateRequestId())
            .businessScene("ENGINE_DECISION")
            .requestTime(LocalDateTime.now())
            .target(buildTouchTarget(crowdDetailDo))
            .message(buildEngineMessage(dispatchDto, detailInfo))
            .channel(convertToTouchChannel(channelEnum))
            .timing(buildImmediateTiming()) // 引擎触达通常是立即执行
            .config(buildTouchConfig(dispatchDto))
            .businessContext(buildEngineBusinessContext(dispatchDto, groupName, bizEventVO))
            .build();
    }

    /**
     * 离线普通触达参数转换
     */
    public TouchRequest fromDispatchHandlerParams(StrategyContext strategyContext,
                                                String app,
                                                String innerApp,
                                                List<CrowdDetailDo> batch,
                                                List<Object> templateParam) {
        return TouchRequest.builder()
            .requestId(generateRequestId())
            .businessScene("OFFLINE_BATCH")
            .requestTime(LocalDateTime.now())
            .targetBatch(buildTouchTargetBatch(batch, app, innerApp))
            .message(buildBatchMessage(strategyContext, templateParam))
            .channel(convertToTouchChannel(strategyContext.getStrategyMarketChannelDo()))
            .timing(buildBatchTiming(batch.size()))
            .config(buildOfflineTouchConfig(strategyContext))
            .businessContext(buildOfflineBusinessContext(strategyContext))
            .build();
    }

    private TouchTarget buildTouchTarget(CrowdDetailDo crowdDetail) {
        return TouchTarget.builder()
            .userId(crowdDetail.getUserId())
            .mobile(crowdDetail.getMobile())
            .appId(crowdDetail.getApp())
            .deviceId(crowdDetail.getDeviceId())
            .userTags(Map.of("ab_num", crowdDetail.getAbNum()))
            .build();
    }

    private TouchMessage buildTouchMessage(DispatchDto dispatchDto,
                                         StrategyMarketChannelDo channelDo,
                                         BizEventVO bizEvent) {
        return TouchMessage.builder()
            .templateId(dispatchDto.getStrategyMarketChannelTemplateId())
            .content(extractMessageContent(channelDo))
            .templateParams(dispatchDto.getEventParamMap())
            .attachments(buildAttachments(dispatchDto, channelDo))
            .build();
    }

    private BusinessContext buildBusinessContext(DispatchDto dispatchDto, BizEventVO bizEvent) {
        return BusinessContext.builder()
            .strategyId(dispatchDto.getStrategyId())
            .strategyGroupId(dispatchDto.getStrategyGroupId())
            .strategyChannelId(dispatchDto.getStrategyChannelId())
            .activityId(dispatchDto.getActivityId())
            .eventType(dispatchDto.getBizEventType())
            .businessTags(extractBusinessTags(dispatchDto))
            .extProperties(buildExtProperties(dispatchDto, bizEvent))
            .build();
    }

    private List<TouchTarget> buildTouchTargetBatch(List<CrowdDetailDo> batch, String app, String innerApp) {
        return batch.stream()
            .map(crowdDetail -> TouchTarget.builder()
                .userId(crowdDetail.getUserId())
                .mobile(crowdDetail.getMobile())
                .appId(app)
                .innerAppId(StringUtils.isBlank(innerApp) ? app : innerApp)
                .deviceId(crowdDetail.getDeviceId())
                .abTestNumber(crowdDetail.getAbNum())
                .userTags(Map.of("crowd_id", String.valueOf(crowdDetail.getCrowdId())))
                .extData(buildUserExtData(crowdDetail))
                .build())
            .collect(Collectors.toList());
    }

    private TouchMessage buildBatchMessage(StrategyContext strategyContext, List<Object> templateParam) {
        return TouchMessage.builder()
            .templateId(strategyContext.getStrategyMarketChannelDo().getTemplateId())
            .batchTemplateParams(templateParam)
            .attachments(buildBatchAttachments(strategyContext))
            .build();
    }

    private TouchTiming buildBatchTiming(int batchSize) {
        return TouchTiming.builder()
            .timingType(TimingType.IMMEDIATE)
            .processingMode(ProcessingMode.BATCH)
            .batchConfig(BatchConfig.builder()
                .batchSize(batchSize)
                .batchIntervalSeconds(0) // 立即处理
                .maxWaitSeconds(300) // 5分钟超时
                .build())
            .build();
    }

    private TouchTiming buildImmediateTiming() {
        return TouchTiming.builder()
            .timingType(TimingType.IMMEDIATE)
            .processingMode(ProcessingMode.SINGLE)
            .build();
    }

    private TouchTiming buildDelayTiming(int delaySeconds) {
        return TouchTiming.builder()
            .timingType(TimingType.DELAY)
            .processingMode(ProcessingMode.SINGLE)
            .delaySeconds(delaySeconds)
            .build();
    }

    private TouchConfig buildOfflineTouchConfig(StrategyContext strategyContext) {
        return TouchConfig.builder()
            .priority(TouchPriority.NORMAL)
            .allowDuplicate(false)
            .validityMinutes(1440) // 24小时有效期
            .requireReceipt(true)
            .retryConfig(RetryConfig.builder()
                .maxRetries(3)
                .retryIntervalSeconds(60)
                .exponentialBackoff(false)
                .build())
            .build();
    }

    private BusinessContext buildOfflineBusinessContext(StrategyContext strategyContext) {
        return BusinessContext.builder()
            .strategyId(strategyContext.getStrategyDo().getId())
            .strategyGroupId(strategyContext.getStrategyGroupDo().getId())
            .strategyChannelId(strategyContext.getStrategyMarketChannelDo().getId())
            .strategyExecLogId(strategyContext.getStrategyExecLogDo() != null ?
                strategyContext.getStrategyExecLogDo().getId() : null)
            .crowdIds(strategyContext.getCrowdIds())
            .detailTableNo(strategyContext.getDetailTableNo())
            .flowControlRules(convertFlowControlRules(strategyContext.getFlowCtrlList()))
            .businessTags(Map.of("execution_type", "offline_batch"))
            .build();
    }

    private Map<String, Object> buildUserExtData(CrowdDetailDo crowdDetail) {
        Map<String, Object> extData = new HashMap<>();
        extData.put("crowd_exec_log_id", crowdDetail.getCrowdExecLogId());
        extData.put("register_time", crowdDetail.getRegisterTime());
        if (StringUtils.isNotEmpty(crowdDetail.getFlowNo())) {
            extData.put("flow_no", crowdDetail.getFlowNo());
            extData.put("pre_strategy_id", crowdDetail.getPreStrategyId());
            extData.put("next_strategy_id", crowdDetail.getNextStrategyId());
        }
        return extData;
    }

    private Map<String, Object> buildBatchAttachments(StrategyContext strategyContext) {
        Map<String, Object> attachments = new HashMap<>();
        attachments.put("strategy_exec_log_id", strategyContext.getStrategyExecLogDo() != null ?
            strategyContext.getStrategyExecLogDo().getId() : null);
        attachments.put("detail_table_no", strategyContext.getDetailTableNo());
        return attachments;
    }

    private List<FlowControlRule> convertFlowControlRules(List<FlowCtrlDo> flowCtrlList) {
        if (CollectionUtils.isEmpty(flowCtrlList)) {
            return Collections.emptyList();
        }
        return flowCtrlList.stream()
            .map(flowCtrl -> FlowControlRule.builder()
                .id(flowCtrl.getId())
                .name(flowCtrl.getName())
                .description(flowCtrl.getDescription())
                .status(convertFlowControlStatus(flowCtrl.getStatus()))
                .type(convertFlowControlType(flowCtrl.getType()))
                .effectiveStrategy(flowCtrl.getEffectiveStrategy())
                .effectiveChannel(flowCtrl.getEffectiveChannel())
                .dayCount(flowCtrl.getDayCount())
                .weekCount(flowCtrl.getWeekCount())
                .monthCount(flowCtrl.getMonthCount())
                .strategyType(convertStrategyType(flowCtrl.getStrategyType()))
                .limitDays(flowCtrl.getLimitDays())
                .limitTimes(flowCtrl.getLimitTimes())
                .bizType(flowCtrl.getBizType())
                .createdTime(flowCtrl.getCreatedTime())
                .updatedTime(flowCtrl.getUpdatedTime())
                .createdOp(flowCtrl.getCreatedOp())
                .updatedOp(flowCtrl.getUpdatedOp())
                .build())
            .collect(Collectors.toList());
    }

    private FlowControlStatus convertFlowControlStatus(Integer status) {
        if (status == null) return null;
        switch (status) {
            case 0: return FlowControlStatus.INIT;
            case 1: return FlowControlStatus.ACTIVE;
            case 2: return FlowControlStatus.CLOSED;
            default: return FlowControlStatus.INIT;
        }
    }

    private FlowControlType convertFlowControlType(Integer type) {
        if (type == null) return null;
        switch (type) {
            case 1: return FlowControlType.STRATEGY;
            case 2: return FlowControlType.CHANNEL;
            case 3: return FlowControlType.MULTI_STRATEGY;
            case 4: return FlowControlType.BIZ_CHANNEL;
            default: return FlowControlType.STRATEGY;
        }
    }

    private StrategyType convertStrategyType(Integer strategyType) {
        if (strategyType == null) return null;
        switch (strategyType) {
            case 1: return StrategyType.OFFLINE;
            case 2: return StrategyType.EVENT;
            default: return null;
        }
    }
}
```

## 8. 服务接口设计

### 8.1 触达执行流程图

```mermaid
flowchart TD
    A[TouchRequest] --> B{请求验证}
    B -->|失败| C[返回验证错误]
    B -->|成功| D{触达时机判断}

    %% 时机分支
    D -->|IMMEDIATE| E[立即处理]
    D -->|DELAY| F[延时队列]
    D -->|SCHEDULED| G[定时调度]

    %% 延时和定时处理
    F --> H[等待延时时间]
    G --> I[等待调度时间]
    H --> J[时机到达]
    I --> J
    E --> J

    %% 时间窗口检查
    J --> K{时间窗口检查}
    K -->|不在窗口内| L[延期到下个窗口]
    K -->|在窗口内| M[继续处理]
    L --> K

    %% 业务处理
    M --> N[解析业务上下文]
    N --> O{流控检查}
    O -->|被拒绝| P[流控拒绝响应]
    O -->|通过| Q[渠道路由]

    %% 渠道处理
    Q --> R{处理模式判断}
    R -->|SINGLE| S[单用户渠道处理]
    R -->|BATCH| T[批量渠道处理]

    %% 渠道执行
    S --> U[渠道插件执行]
    T --> V[批量渠道插件执行]
    U --> W[收集执行结果]
    V --> X[收集批量结果]

    %% 结果处理
    W --> Y[构建TouchResponse]
    X --> Z[构建批量TouchResponse]
    P --> AA[构建拒绝响应]
    C --> AA

    %% 后置处理
    Y --> BB[记录触达日志]
    Z --> BB
    AA --> BB
    BB --> CC[监控上报]
    CC --> DD[回执处理]
    DD --> EE[返回最终响应]

    %% 样式定义
    classDef startClass fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef processClass fill:#e3f2fd,stroke:#1565c0,stroke-width:2px
    classDef decisionClass fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    classDef channelClass fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef endClass fill:#ffebee,stroke:#c62828,stroke-width:2px

    class A startClass
    class E,F,G,H,I,J,M,N,U,V,BB,CC,DD processClass
    class B,D,K,O,Q,R decisionClass
    class S,T,W,X channelClass
    class C,P,Y,Z,AA,EE endClass
```

### 8.2 组合触达执行流程

组合触达通过 `TouchChain` 实现时序控制，执行流程如下：

```mermaid
sequenceDiagram
    participant Client as 调用方
    participant TouchService as 触达服务
    participant ChainManager as 链条管理器
    participant DelayQueue as 延时队列
    participant ConditionEngine as 条件引擎
    participant ChannelPlugin as 渠道插件

    Note over Client, ChannelPlugin: 组合触达执行流程

    %% 第一步：执行主触达
    Client->>+TouchService: touch(combinedTouchRequest)
    TouchService->>TouchService: 解析主触达请求
    TouchService->>+ChannelPlugin: 执行主触达（如发券）
    ChannelPlugin-->>-TouchService: 主触达结果

    %% 第二步：处理触达链
    TouchService->>+ChainManager: 处理触达链(nextTouchChain, 主触达结果)

    loop 遍历每个TouchChain
        ChainManager->>ChainManager: 检查触发条件

        alt 触发类型为SUCCESS且主触达成功
            ChainManager->>+DelayQueue: 添加延时任务(delaySeconds)
            DelayQueue-->>-ChainManager: 任务已添加
        else 触发类型为CONDITIONAL
            ChainManager->>+ConditionEngine: 评估条件表达式
            ConditionEngine-->>-ChainManager: 条件评估结果
            alt 条件满足
                ChainManager->>+DelayQueue: 添加延时任务
                DelayQueue-->>-ChainManager: 任务已添加
            end
        else 触发类型为ALWAYS
            ChainManager->>+DelayQueue: 添加延时任务
            DelayQueue-->>-ChainManager: 任务已添加
        end
    end

    ChainManager-->>-TouchService: 链条处理完成
    TouchService-->>-Client: 主触达响应

    Note over DelayQueue, ChannelPlugin: 异步执行后续触达

    %% 第三步：延时任务执行
    DelayQueue->>DelayQueue: 延时时间到达
    DelayQueue->>+TouchService: 执行后续触达(nextTouch)
    TouchService->>+ChannelPlugin: 执行后续触达（如发短信）
    ChannelPlugin-->>-TouchService: 后续触达结果

    %% 第四步：递归处理嵌套链条
    alt 后续触达也有nextTouchChain
        TouchService->>+ChainManager: 处理嵌套链条
        ChainManager->>ChainManager: 递归处理
        ChainManager-->>-TouchService: 嵌套链条处理完成
    end

    TouchService->>TouchService: 记录链条执行日志
    TouchService-->>-DelayQueue: 后续触达完成
```

#### 8.2.1 组合触达的核心特性

1. **异步执行**: 主触达立即返回，后续触达异步执行
2. **条件触发**: 支持基于主触达结果的条件判断
3. **延时控制**: 灵活的延时时间配置
4. **嵌套支持**: 支持多级触达链条
5. **失败处理**: 支持基于失败结果的补偿触达
6. **超时控制**: 防止链条无限等待

#### 8.2.2 实现要点

- **状态管理**: 需要持久化存储触达链条的执行状态
- **幂等性**: 确保重复执行不会产生副作用
- **监控告警**: 对链条执行异常进行监控和告警
- **性能优化**: 大量延时任务的高效调度

### 8.3 统一触达服务接口

```java
/**
 * 统一触达服务
 * 基于提纯版模型的纯业务接口
 */
public interface TouchService {

    /**
     * 执行触达
     *
     * @param request 触达请求
     * @return 触达响应
     */
    TouchResponse touch(TouchRequest request);

    /**
     * 批量触达
     *
     * @param requests 触达请求列表
     * @return 触达响应列表
     */
    List<TouchResponse> batchTouch(List<TouchRequest> requests);

    /**
     * 异步触达
     *
     * @param request 触达请求
     * @return 触达响应（包含批次号）
     */
    TouchResponse touchAsync(TouchRequest request);

    /**
     * 查询触达状态
     *
     * @param requestId 请求ID
     * @return 触达状态
     */
    TouchResult getTouchStatus(String requestId);

    /**
     * 取消触达
     *
     * @param requestId 请求ID
     * @return 是否成功取消
     */
    boolean cancelTouch(String requestId);

    /**
     * 执行组合触达
     * 支持主触达完成后的链式触达
     *
     * @param request 包含触达链的触达请求
     * @return 主触达响应（后续触达异步执行）
     */
    TouchResponse touchWithChain(TouchRequest request);

    /**
     * 查询触达链执行状态
     *
     * @param requestId 主触达请求ID
     * @return 触达链执行状态列表
     */
    List<ChainExecutionStatus> getChainStatus(String requestId);

    /**
     * 取消触达链
     *
     * @param requestId 主触达请求ID
     * @param chainId 链条ID，为空则取消所有链条
     * @return 是否成功取消
     */
    boolean cancelTouchChain(String requestId, String chainId);
}

/**
 * 触达链执行状态
 */
public class ChainExecutionStatus {
    /**
     * 链条ID
     */
    private String chainId;

    /**
     * 执行状态：WAITING(等待中)、EXECUTING(执行中)、SUCCESS(成功)、FAILED(失败)、CANCELLED(已取消)
     */
    private ChainStatus status;

    /**
     * 触发时间
     */
    private LocalDateTime triggerTime;

    /**
     * 执行时间
     */
    private LocalDateTime executeTime;

    /**
     * 完成时间
     */
    private LocalDateTime completeTime;

    /**
     * 执行结果
     */
    private TouchResponse touchResponse;

    /**
     * 错误信息
     */
    private String errorMessage;
}

/**
 * 触达链状态
 */
public enum ChainStatus {
    WAITING("等待中"),
    EXECUTING("执行中"),
    SUCCESS("成功"),
    FAILED("失败"),
    CANCELLED("已取消");

    private final String displayName;
}
```

### 8.3 渠道插件接口

```java
/**
 * 触达渠道插件接口
 * 各渠道实现此接口完成具体的触达逻辑
 */
public interface TouchChannelPlugin {

    /**
     * 支持的渠道类型
     */
    TouchChannel getSupportedChannel();

    /**
     * 执行触达
     *
     * @param request 触达请求
     * @return 渠道响应
     */
    ChannelResponse execute(TouchRequest request);

    /**
     * 渠道是否可用
     */
    boolean isAvailable();

    /**
     * 渠道配置验证
     */
    boolean validateConfig(TouchConfig config);
}
```

## 9. 总结

提纯版触达模型专注于触达的业务本质，通过清晰的业务语义和领域概念，为触达系统提供了一个稳定、可扩展、易理解的业务基础。

### 9.1 核心价值

1. **业务导向**: 模型直接反映业务概念，降低理解成本
2. **技术无关**: 不感知上游技术分类，专注触达执行
3. **场景完整**: 统一支持单用户、批量用户、实时、延时、定时等所有触达场景
4. **时机灵活**: 显式的时机配置，支持立即、延时、定时、批量等多种执行方式
5. **扩展友好**: 支持新渠道、新业务场景的自然扩展
6. **维护简单**: 业务模型相对稳定，减少技术变更影响

### 9.2 实施路径

1. **模型定义**: 完成提纯版模型的详细设计和实现
2. **转换器开发**: 实现现有模型到提纯版模型的转换
3. **服务实现**: 基于提纯版模型实现新的触达服务
4. **渐进迁移**: 逐步将现有业务迁移到新模型

### 9.3 支持的触达场景

提纯版模型通过统一的接口支持所有触达场景：

1. **T0普通触达**: 立即时机 + 单个处理 + 事件驱动
2. **T0引擎触达**: 立即时机 + 单个处理 + 引擎决策
3. **离线普通触达**: 立即时机 + 批量处理 + 人群包驱动
4. **离线引擎触达**: 立即时机 + 单个处理 + 引擎决策（离线场景）
5. **延时触达**: 延时时机 + 单个处理 + 时间窗口控制
6. **定时触达**: 定时时机 + 单个/批量处理 + 精确时间控制

**时机与处理模式的组合示例**：
- **立即 + 单个**: T0实时触达
- **立即 + 批量**: 离线批量触达
- **延时 + 单个**: 延时单用户触达
- **延时 + 批量**: 延时批量触达
- **定时 + 批量**: 定时营销活动

这个提纯版模型将成为新触达服务xyf-touch-service的核心，为触达系统的统一化和现代化奠定坚实的业务基础。通过统一的业务模型，不仅简化了系统架构，还为未来的业务创新提供了更好的技术支撑。
