package com.xftech.cdp.domain.crowd.service.impl;

import com.xftech.base.common.util.LogUtil;
import com.xftech.base.log.IUdpLogger;
import com.xftech.cdp.domain.crowd.model.enums.UtmTypeEnum;
import com.xftech.cdp.domain.crowd.service.UtmSourceService;
import com.xftech.cdp.domain.stat.entity.UtmResult;
import com.xftech.cdp.infra.client.dingtalk.DingTalkUtil;
import com.xftech.cdp.infra.client.loanmarket.LoanMarketClient;
import com.xftech.cdp.infra.client.loanmarket.enums.UtmSourceTypeEnum;
import com.xftech.cdp.infra.client.loanmarket.model.resp.GetUtmSourceResp;
import com.xftech.cdp.infra.exception.InterfaceException;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
public class UtmSourceServiceImpl implements UtmSourceService {

    private static final IUdpLogger logger = LogUtil.getLogger(UtmSourceService.class);

    @Autowired
    private LoanMarketClient loanMarketClient;

    /**
     * 渠道是否可营销
     * @param singleUtmSource 单个渠道
     * @param utmSourceTypeEnum 渠道类型
     * @return true可营销 false不可营销
     */
    @Override
    public UtmResult filterSingleUtmSource(String singleUtmSource, UtmSourceTypeEnum utmSourceTypeEnum, LocalDateTime registerTime, String utmType) {
        UtmResult utmResult = new UtmResult();
        utmResult.setUtmType(utmSourceTypeEnum.getType());
        utmResult.setUtmSource(singleUtmSource);
        if (StringUtils.isBlank(singleUtmSource)) {
            utmResult.setHit(true);
            return utmResult;
        }
        GetUtmSourceResp utmSourceResp = null;
        try {
            utmSourceResp = loanMarketClient.cacheQueryUtmSourceInfo(singleUtmSource);
        } catch (InterfaceException ie) {
            logger.warn("商户接口异常");
//            this.alarm(strategyId, utmSource, UtmSourceTypeEnum.getDesc(labelName));
            utmResult.setHit(false);
            utmResult.setNewMarketingType("商户接口异常");
            // 需要钉钉预警
            throw new InterfaceException("商户接口异常");
        }
        // 接口数据为空，可营销
        if (utmSourceResp == null) {
            utmResult.setHit(true);
            return utmResult;
        }
        // 规则配置字段为空，可营销
        if (StringUtils.isBlank(utmSourceResp.getNewMarketingType())) {
            utmResult.setHit(true);
            return utmResult;
        }

        String marketingType = null;
        Integer noMarketingDays = null;
        if (utmType.equals(UtmTypeEnum.NEW_CUST.getName())) {
            marketingType = utmSourceResp.getNewMarketingType();
            noMarketingDays = utmSourceResp.getNewNoMarketingDays();
            utmResult.setNewMarketingType(utmSourceResp.getNewMarketingType());
            utmResult.setNewNoMarketingDays(utmSourceResp.getNewNoMarketingDays());
        } else if(utmType.equals(UtmTypeEnum.OLD_CUST.getName())) {
            utmResult.setOldMarketingType(utmSourceResp.getOldMarketingType());
            utmResult.setOldNoMarketingDays(utmSourceResp.getOldNoMarketingDays());
            marketingType = utmSourceResp.getOldMarketingType();
            noMarketingDays = utmSourceResp.getOldNoMarketingDays();
        }

        // 如果是注册渠道
        switch (utmSourceTypeEnum) {
            case REGISTER:
                if (utmSourceTypeEnum.getType().equals(marketingType)) {
//                    Integer days = utmSourceResp.getNewNoMarketingDays();
                    if (noMarketingDays == null) {
                        logger.warn("商户接口days字段为空");
                        utmResult.setHit(false);
                        // 需要钉钉预警
                        throw new InterfaceException("商户接口days字段为空");
                    }
                    if (noMarketingDays < 0) {
                        utmResult.setHit(false);
                        return utmResult;
                    }
                    if (registerTime == null) {
                        utmResult.setHit(false);
                        return utmResult;
                    }
                    if (registerTime.plusDays(noMarketingDays).isBefore(LocalDateTime.now())) {
                        utmResult.setHit(true);
                    } else {
                        utmResult.setHit(false);
                    }
                    return utmResult;
                } else {
                    utmResult.setHit(true);
                    return utmResult;
                }
            case ENTRY:
            case LOAN:
                if(utmSourceTypeEnum.getType().equals(marketingType)) {
                    utmResult.setHit(false);
                } else {
                    utmResult.setHit(true);
                }
                return utmResult;
        }
        utmResult.setHit(true);
        return utmResult;
    }

    @Override
    public List<UtmResult> filterBatchUtmSource(String batchUtmSource, UtmSourceTypeEnum utmSourceTypeEnum, LocalDateTime registerTime, String utmType) {
        List<UtmResult> resultList = new ArrayList<>();
        UtmResult utmResult = new UtmResult();
        utmResult.setUtmType(utmSourceTypeEnum.getType());
        utmResult.setUtmSource(batchUtmSource);
        if (StringUtils.isBlank(batchUtmSource)) {
            utmResult.setHit(true);
            resultList.add(utmResult);
            return resultList;
        }
        String[] split = batchUtmSource.split(",");
        for (String item : split) {
            UtmResult itemResult = filterSingleUtmSource(item, utmSourceTypeEnum, registerTime, utmType);
            resultList.add(itemResult);
            if (!itemResult.getHit()) {
                return resultList;
            }
        }
        return resultList;
    }

//    /**
//     * 告警
//     */
//    private void alarm(Long strategyId, String utmSource, String sourceType) {
//        StrategyDo strategyDo = cacheStrategyService.selectById(strategyId);
//
//        StringBuilder content = new StringBuilder();
//        content.append(String.format("策略编号：%s", strategyDo.getId())).append("\n");
//        content.append(String.format("策略名称：%s", strategyDo.getName())).append("\n");
//        content.append(String.format("报警渠道：%s", sourceType)).append("\n");
//        content.append(String.format("渠道：%s", utmSource)).append("\n");
//        content.append("查询商户后台接口异常请立即排查");
//        DingTalkUtil.sendTextToDingTalk(dingTalkConfig.getAlarmUrl(), content.toString(), null, false);
//    }

}
