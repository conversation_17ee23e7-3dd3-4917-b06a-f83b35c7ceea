package com.xftech.cdp.application;

import com.xftech.cdp.domain.risk.service.RiskService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @since 2023/5/24
 */

@Component
public class RiskHandle {

    @Resource
    private RiskService riskService;

    public void execute(LocalDate refreshDate) {
        riskService.RefreshRiskScore(refreshDate);
    }
}
