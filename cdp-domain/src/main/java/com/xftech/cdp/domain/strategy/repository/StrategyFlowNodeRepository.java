/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyFlowNodeDo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @version $ StrategyFlowNodeRepository, v 0.1 2023/12/13 15:16 yye.xu Exp $
 */

@Repository
@AllArgsConstructor
public class StrategyFlowNodeRepository {

    public void batchInsert(List<StrategyFlowNodeDo> strategyFlowNodes) {
        if (CollectionUtils.isEmpty(strategyFlowNodes)) {
            return;
        }
        DBUtil.insertBatch("strategyFlowNodeDoMapper.insertSelective", strategyFlowNodes);
    }

    public List<StrategyFlowNodeDo> select(String flowNo) {
        Map<String, Object> param = new HashMap<>();
        param.put("flowNo", flowNo);
        return DBUtil.selectList("strategyFlowNodeDoMapper.selectByFlowNo", param);
    }

    public void deleteByFlowNo(String flowNo) {
        Map<String, Object> param = new HashMap<>();
        param.put("flowNo", flowNo);
        DBUtil.selectList("strategyFlowNodeDoMapper.deleteByFlowNo", param);
    }
}