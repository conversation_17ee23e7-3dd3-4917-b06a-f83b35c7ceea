/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.rocketmq.ai.handler;

import com.alibaba.fastjson.JSONObject;
import com.xftech.cdp.domain.mq.MqConsumeService;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventMessageVO;
import com.xftech.cdp.infra.rocketmq.EventEnum;
import com.xftech.cdp.infra.rocketmq.MessageHandler;
import com.xftech.cdp.infra.rocketmq.MessageTopicConstants;
import com.xftech.cdp.infra.rocketmq.dto.AiCallBackStatusRocketMessageDTO;
import com.xftech.cdp.infra.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;


@Slf4j
@Component
public class AiCallBackStatusHandler implements MessageHandler {

    @Resource
    private MqConsumeService mqConsumeService;

    @Override
    public String getTopic() {
        return MessageTopicConstants.CALL_CENTER_AI_CALLBACK;
    }

    @Override
    public boolean execute(String messageId, Object message) {
        try {
            AiCallBackStatusRocketMessageDTO messageVO = JSONObject.parseObject(message.toString(), AiCallBackStatusRocketMessageDTO.class);
            List<BizEventMessageVO> bizEventMessageVOList = transform(messageVO);
            int counter = 1; // 计数器初始化
            if (bizEventMessageVOList == null || bizEventMessageVOList.isEmpty()) {
                log.info("AiCallBackStatusHandler Invalid message content: {}", JSONObject.toJSONString(messageVO));
                return false;
            }
            log.info("AiCallBackStatusHandler bizEventMessageVOList: {}", JSONObject.toJSONString(bizEventMessageVOList));
            for (BizEventMessageVO bizEventMessageVO : bizEventMessageVOList) {
                bizEventMessageVO.setBizEventType(EventEnum.AI_CALL.getEventType());
                mqConsumeService.bizEventProcess(messageId + counter, bizEventMessageVO);
                counter++; // 递增计数器
            }

            return true;
        } catch (Exception e) {
            log.info("AiCallBackStatusHandler exception", e);
            return false;
        }
    }

    private List<BizEventMessageVO> transform(AiCallBackStatusRocketMessageDTO aiCallBackStatusRocketMessageDTO) {
        List<BizEventMessageVO> bizEventMessageVOList = new ArrayList<>();

        if (aiCallBackStatusRocketMessageDTO == null || aiCallBackStatusRocketMessageDTO.getContent() == null || aiCallBackStatusRocketMessageDTO.getContent().getBatchResult() == null || aiCallBackStatusRocketMessageDTO.getContent().getBatchResult().isEmpty()) {
            log.warn("AiCallBackStatusHandler Invalid message content: {}", JSONObject.toJSONString(aiCallBackStatusRocketMessageDTO));
            return null;
        }

        AiCallBackStatusRocketMessageDTO.BatchResultInfo content = aiCallBackStatusRocketMessageDTO.getContent();
        List<AiCallBackStatusRocketMessageDTO.JobResultInfo> batchResult = content.getBatchResult();

        // 使用Map来存储每个userNo对应的最新记录
        Map<String, BizEventMessageVO> userNoToLatestRecordMap = new HashMap<>();

        // 定义时间格式化工具
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        for (AiCallBackStatusRocketMessageDTO.JobResultInfo jobResult : batchResult) {
            if (StringUtils.isBlank(jobResult.getUserNo()) || StringUtils.isBlank(jobResult.getCallStatus()) || StringUtils.isBlank(jobResult.getBeginTime())) {
                log.warn("AiCallBackStatusHandler userNo or callStatus is empty: {}", JSONObject.toJSONString(jobResult));
                continue;
            }

            BizEventMessageVO bizEventMessageVO = new BizEventMessageVO();

            BizEventMessageVO.ExtrData extrData = new BizEventMessageVO.ExtrData();
            bizEventMessageVO.setAppUserId(Long.parseLong(jobResult.getUserNo()));
            bizEventMessageVO.setApp(jobResult.getProductType());

            bizEventMessageVO.setTriggerDatetime(DateUtil.convertStr(new Date()));
            extrData.setStart_time(jobResult.getBeginTime());
            extrData.setCallStatus(jobResult.getCallStatus());
            bizEventMessageVO.setExtrData(extrData);
            // 将beginTime字符串转换为LocalDateTime
            LocalDateTime currentBeginTime = null;
            try {
                currentBeginTime = LocalDateTime.parse(jobResult.getBeginTime(), formatter);
            } catch (DateTimeParseException e) {
                log.warn("AiCallBackStatusHandler Invalid beginTime format for userNo {}: {}", jobResult.getUserNo(), jobResult.getBeginTime());
                continue; // 跳过当前记录
            }

            // 检查是否已经有该userNo的记录
            String userNo = jobResult.getUserNo();
            if (userNoToLatestRecordMap.containsKey(userNo)) {
                // 如果已有记录，比较beginTime，保留较晚的记录
                BizEventMessageVO existingRecord = userNoToLatestRecordMap.get(userNo);
                LocalDateTime existingBeginTime = null;
                try {
                    existingBeginTime = LocalDateTime.parse(existingRecord.getExtrData().getStart_time(), formatter);
                } catch (DateTimeParseException e) {
                    log.warn("AiCallBackStatusHandler Invalid beginTime format for existing record of userNo {}: {}", userNo, existingRecord.getExtrData().getStart_time());
                    continue; // 跳过当前记录
                }
                if (currentBeginTime.isAfter(existingBeginTime)) {
                    userNoToLatestRecordMap.put(userNo, bizEventMessageVO);
                }
            } else {
                // 如果没有记录，直接添加
                userNoToLatestRecordMap.put(userNo, bizEventMessageVO);
            }
        }

        // 将Map中的记录转换为List
        bizEventMessageVOList.addAll(userNoToLatestRecordMap.values());
        return bizEventMessageVOList;
    }
}