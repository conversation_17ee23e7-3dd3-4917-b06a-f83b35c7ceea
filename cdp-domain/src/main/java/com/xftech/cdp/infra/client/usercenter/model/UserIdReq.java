package com.xftech.cdp.infra.client.usercenter.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/12/6 11:30
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class UserIdReq {
    private static final long serialVersionUID = 1L;


    @JsonProperty("ids")
    @SerializedName("ids")
    private List<Long> ids;
}
