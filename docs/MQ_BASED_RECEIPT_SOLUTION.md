# 基于MQ的回执统一处理方案

## 背景说明

根据您的反馈，我重新设计了回执处理方案。原来的HTTP API方式确实存在问题：
- **改造成本高** - 需要外部团队修改现有的回执推送逻辑
- **不符合现状** - 现有系统已经通过MQ方式集成
- **推广困难** - 外部团队不会配合改造

## 正确的解决方案

### 设计原则
1. **零改造成本** - 保持现有外部系统集成方式不变
2. **统一处理逻辑** - 内部统一回执处理流程和数据更新
3. **向后兼容** - 支持现有回执处理逻辑平滑迁移

### 架构设计

```
外部系统 → MQ消息队列 → 触达服务MQ消费者 → 应用服务 → 领域服务 → 数据更新
```

## 实现方案

### 1. 短信回执 - RabbitMQ方式

**外部系统**: 短信供应商
**集成方式**: RabbitMQ队列 `sms_supplier_report_callback`
**处理组件**: `SmsReceiptConsumer`

```java
@RabbitListener(queues = "sms_supplier_report_callback")
public void smsReportCallback(String message) {
    // 解析消息 → 转换DTO → 统一处理
    List<SmsReportVO> smsReportList = JSON.parseArray(message, SmsReportVO.class);
    List<SmsReportDTO> dtoList = convert(smsReportList);
    receiptApplicationService.processSmsReceipts(dtoList);
}
```

### 2. 优惠券回执 - RocketMQ方式

**外部系统**: 优惠券系统
**集成方式**: RocketMQ Topic `tp_xyf_cdp_coupon_callback`
**处理组件**: `CouponReceiptConsumer`

```java
@RocketMQMessageListener(topic = "tp_xyf_cdp_coupon_callback")
public void onMessage(String message) {
    // 解析消息 → 转换DTO → 统一处理
    List<CouponCallbackVO> callbackList = JSON.parseArray(message, CouponCallbackVO.class);
    List<CouponCallbackDTO> dtoList = convert(callbackList);
    receiptApplicationService.processCouponReceipts(dtoList);
}
```

### 3. AI外呼回执 - RocketMQ方式

**外部系统**: AI外呼中心
**集成方式**: RocketMQ Topic `CALL_CENTER_AI_CALLBACK`
**处理组件**: `AiCallbackConsumer`

```java
@RocketMQMessageListener(topic = "CALL_CENTER_AI_CALLBACK")
public void onMessage(String message) {
    // 解析消息 → 转换DTO → 统一处理
    AiCallBackMessageVO aiMessage = JSON.parseObject(message, AiCallBackMessageVO.class);
    AiCallBackMessageDTO dto = convert(aiMessage);
    receiptApplicationService.processAiCallbackReceipt(dto);
}
```

### 4. Push回执 - 直接方法调用

**外部系统**: Push推送系统
**集成方式**: 直接调用应用服务方法
**处理组件**: `ReceiptApplicationService.processPushReceipt()`

```java
// Push系统直接调用
public void processPushReceipt(Object pushReportReq) {
    // 转换DTO → 统一处理
    ReceiptMessage receiptMessage = convertPushReport(pushReportReq);
    receiptDomainService.processReceipt(receiptMessage);
}
```

## 核心优势

### 1. 零改造成本
- **外部系统无需修改** - 保持现有MQ推送方式
- **内部统一处理** - 通过适配器模式统一处理逻辑
- **平滑迁移** - 可以逐步替换现有回执处理逻辑

### 2. 统一处理流程
```
MQ消息 → 基础设施层消费者 → 应用层DTO转换 → 统一回执处理 → 领域服务 → 数据更新
```

### 3. 分层架构清晰
- **基础设施层**: MQ消费者，负责消息接收和DTO转换
- **应用层**: 统一回执处理服务，业务流程编排
- **领域层**: 回执处理核心逻辑，状态更新

### 4. 可扩展性强
- **新增渠道**: 只需添加新的MQ消费者
- **消息格式变化**: 只需修改DTO转换逻辑
- **业务逻辑变化**: 只需修改应用层或领域层

## 特殊业务处理

### 优惠券回执特殊处理
根据优惠券类型进行特殊业务回执：

```java
// 生活权益回执 (couponType = 4)
if (couponType == 4) {
    sendLifeRightsCallback(couponCallback);
    // 发送到 tp_xyf_cdp_notify:tg_liferights
}

// X天免息回执 (couponType = 5)  
if (couponType == 5) {
    sendXDayInterestFreeCallback(couponCallback);
    // 发送到 tp_xyf_cdp_notify:tg_xDayInterestFree
}
```

## 配置说明

### RabbitMQ配置
```yaml
sms:
  report:
    queue:
      name: sms_supplier_report_callback
```

### RocketMQ配置
```yaml
coupon:
  callback:
    topic: tp_xyf_cdp_coupon_callback
    consumer:
      group: cg_xyf_cdp_coupon_callback

ai:
  callback:
    consumer:
      group: cg_xyf_cdp_ai_callback
```

## 监控指标

### 回执处理监控
- `touch.receipt.sms.success` - 短信回执成功计数
- `touch.receipt.coupon.success` - 优惠券回执成功计数
- `touch.receipt.ai.success` - AI外呼回执成功计数
- `touch.receipt.push.success` - Push回执成功计数

### 消费者监控
- `touch.mq.consumer.lag` - 消费者延迟
- `touch.mq.consumer.error` - 消费者错误计数

## 总结

这个基于MQ的回执统一处理方案具有以下特点：

1. **实用性强** - 符合现有系统集成方式，无需外部团队配合
2. **架构清晰** - 分层明确，职责单一
3. **扩展性好** - 易于添加新渠道和新业务逻辑
4. **可维护性高** - 统一的处理流程和监控体系

这样的设计既满足了业务需求，又最大程度地减少了系统改造成本，是一个更加实际和可行的解决方案。
