# 触达流程表结构和数据流转分析报告
## 1. 概述
本文档详细分析三种触达入口方法执行后涉及的表结构和数据流向：

+ **T0普通触达**: `execSend()` 方法
+ **T0引擎触达 + 离线引擎触达**: `marketingSend()` 方法  
+ **离线普通触达**: `dispatchHandler()` 方法

## 2. 核心数据库表结构
### 2.1 事件推送批次表 (event_push_batch_YYYYMM)
**使用场景**: T0普通触达、T0引擎触达、离线引擎触达

```sql
CREATE TABLE event_push_batch_YYYYMM (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    strategy_id BIGINT,                    -- 策略ID
    market_channel_id BIGINT,              -- 策略渠道ID
    exec_log_id BIGINT,                    -- 策略执行日志ID
    market_channel INT,                    -- 触达渠道：0-不营销，1-短信，2-电销，3-优惠券
    user_id BIGINT,                        -- 用户ID
    mobile VARCHAR(20),                    -- 手机号
    app VARCHAR(50),                       -- APP标识
    template_id VARCHAR(100),              -- 模板ID
    batch_num VARCHAR(50),                 -- 批次号
    inner_batch_num VARCHAR(50),           -- 内部批次号
    send_code VARCHAR(20),                 -- 发送状态码
    send_msg VARCHAR(500),                 -- 发送描述
    status INT,                            -- 下发状态：1-成功，2-失败
    query_status INT,                      -- 结果查询状态：-1-无需查询，0-未查询，1-轮询中，2-已完成
    detail_table_no VARCHAR(10),           -- 下发明细表序号
    group_name VARCHAR(100),               -- 引擎返回groupId
    strategy_group_id BIGINT,              -- 麻雀分组ID
    strategy_group_name VARCHAR(100),      -- 麻雀分组名称
    biz_event_type VARCHAR(50),            -- 业务事件类型
    created_time DATETIME,                 -- 创建时间
    updated_time DATETIME,                 -- 更新时间
    d_flag TINYINT DEFAULT 0               -- 删除标记
);
```

### 2.2 人群推送批次表 (crowd_push_batch)
**使用场景**: 离线普通触达

```sql
CREATE TABLE crowd_push_batch (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    xxl_job_id INT,                        -- 执行记录ID
    strategy_id BIGINT,                    -- 策略ID
    strategy_group_id BIGINT,              -- 分组ID
    strategy_market_channel_id BIGINT,     -- 策略渠道ID
    strategy_exec_log_id BIGINT,           -- 策略执行日志ID
    market_channel INT,                    -- 触达渠道
    template_id VARCHAR(100),              -- 模板ID
    batch_num VARCHAR(50),                 -- 批次号
    batch_total INT,                       -- 批次数量
    sms_send_count INT,                    -- 短信发送数量
    succ_count INT,                        -- 成功数量
    fail_count INT,                        -- 失败数量
    used_count INT,                        -- 使用数量
    send_code VARCHAR(20),                 -- 发送状态码
    send_msg VARCHAR(500),                 -- 发送描述
    query_status INT,                      -- 结果查询状态
    exec_type INT,                         -- 执行类型：0-正常，1-重试
    batch_status VARCHAR(10),              -- 批次状态：0-已完成，1-可重试
    mobile_batch VARCHAR(50),              -- 手机号批次
    detail_table_no VARCHAR(10),           -- 明细表序号
    created_time DATETIME,                 -- 创建时间
    updated_time DATETIME,                 -- 更新时间
    d_flag TINYINT DEFAULT 0               -- 删除标记
);
```

### 2.3 用户触达明细表 (user_dispatch_detail_YYYYMM)
**使用场景**: 所有触达方式

```sql
CREATE TABLE user_dispatch_detail_YYYYMM (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT,                        -- 用户ID
    mobile VARCHAR(20),                    -- 手机号
    batch_num VARCHAR(50),                 -- 批次号
    crowd_pack_id BIGINT,                  -- 人群包ID
    strategy_id BIGINT,                    -- 策略ID
    strategy_channel_id BIGINT,            -- 策略渠道ID
    market_channel INT,                    -- 营销渠道
    strategy_exec_id VARCHAR(50),          -- 策略执行ID
    exec_log_id BIGINT,                    -- 执行日志ID
    status INT,                            -- 状态：0-失败，1-成功
    used_status INT,                       -- 使用状态
    dispatch_time DATETIME,                -- 触达时间
    message_id VARCHAR(100),               -- 消息ID
    trigger_datetime DATETIME,             -- 上报时间
    group_name VARCHAR(100),               -- 引擎返回groupId
    strategy_group_id BIGINT,              -- 麻雀分组ID
    strategy_group_name VARCHAR(100),      -- 麻雀分组名称
    biz_event_type VARCHAR(50),            -- 业务事件类型
    template_id VARCHAR(100),              -- 模板ID
    ext_detail TEXT,                       -- 扩展详情
    dispatch_type VARCHAR(20),             -- 分发类型
    biz_type VARCHAR(50),                  -- 业务线
    created_time DATETIME,                 -- 创建时间
    updated_time DATETIME,                 -- 更新时间
    d_flag TINYINT DEFAULT 0               -- 删除标记
);
```

### 2.4 流控拦截日志表 (flow_ctrl_interception_log)
**使用场景**: 所有触达方式的流控记录

```sql
CREATE TABLE flow_ctrl_interception_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT,                        -- 用户ID
    strategy_id BIGINT,                    -- 策略ID
    strategy_channel_id BIGINT,            -- 策略渠道ID
    market_channel INT,                    -- 营销渠道
    flow_ctrl_rule_id BIGINT,              -- 流控规则ID
    interception_reason VARCHAR(500),      -- 拦截原因
    interception_time DATETIME,            -- 拦截时间
    created_time DATETIME,                 -- 创建时间
    d_flag TINYINT DEFAULT 0               -- 删除标记
);
```

### 2.5 用户发送计数器表 (user_send_counter)
**使用场景**: 所有触达方式的统计计数

```sql
CREATE TABLE user_send_counter (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    app_user_id BIGINT,                    -- 用户ID
    mobile VARCHAR(20),                    -- 手机号
    strategy_id BIGINT,                    -- 策略ID
    market_channel SMALLINT,               -- 营销渠道
    date_value INT,                        -- 日期值(YYYYMMDD)
    sum_value INT,                         -- 发送总数
    failed_value INT,                      -- 失败总数
    created_time DATETIME,                 -- 创建时间
    updated_time DATETIME,                 -- 更新时间
    d_flag TINYINT DEFAULT 0               -- 删除标记
);
```

## 3. 数据流转分析
### 3.1 T0普通触达 (execSend) 数据流转
```mermaid
graph TD
    A[业务事件MQ] --> B[StrategyEventDispatchServiceImpl.dispatch]
    B --> C[流控检查 dispatchFlc]
    C --> D[execSend方法]
    D --> E[EventDispatchServiceImpl.sendSmsEvent]
    E --> F[模板参数查询 getSmsTempParam]
    F --> G[外部渠道调用]
    G --> H[保存event_push_batch记录]
    H --> I[保存user_dispatch_detail记录]
    I --> J[更新发送计数器]
    J --> K[回执处理]
```

**涉及表操作**:

1. **查询操作**:
    - `strategy` - 查询策略配置
    - `strategy_group` - 查询分组配置  
    - `strategy_market_channel` - 查询渠道配置
    - `template_param` - 查询模板参数配置
2. **插入操作**:
    - `event_push_batch_YYYYMM` - 插入批次记录
    - `user_dispatch_detail_YYYYMM` - 插入触达明细
    - `user_send_counter` - 插入/更新计数器
    - `flow_ctrl_interception_log` - 流控拦截时插入
3. **更新操作**:
    - `event_push_batch_YYYYMM` - 回执时更新状态
    - `user_dispatch_detail_YYYYMM` - 回执时更新状态

### 3.2 T0引擎触达 + 离线引擎触达 (marketingSend) 数据流转
```mermaid
graph TD
    A[引擎决策结果] --> B[StrategyEventDispatchServiceImpl.marketingSend]
    B --> C[生成批次号 serialNumberUtil.batchNum]
    C --> D[构建EventPushBatchDo对象]
    D --> E[分布式流控检查 dispatchFlcLock]
    E --> F[模板参数验证 checkTemplateContent]
    F --> G[渠道特定处理]
    G --> H[EventDispatchServiceImpl调用]
    H --> I[保存event_push_batch记录]
    I --> J[保存user_dispatch_detail记录]
    J --> K[更新发送计数器]
    K --> L[回执处理]
```

**涉及表操作**:

1. **查询操作**:
    - `dispatch_user_delay` - 离线引擎触达查询延迟任务
    - `crowd_detail_YYYYMM` - 查询用户明细
    - `strategy` - 查询策略配置
    - `template_param` - 查询模板参数
2. **插入操作**:
    - `event_push_batch_YYYYMM` - 插入批次记录
    - `user_dispatch_detail_YYYYMM` - 插入触达明细
    - `user_send_counter` - 插入/更新计数器
3. **更新操作**:
    - `dispatch_user_delay` - 更新延迟任务状态
    - `event_push_batch_YYYYMM` - 回执时更新状态
    - `user_dispatch_detail_YYYYMM` - 回执时更新状态

### 3.3 离线普通触达 (dispatchHandler) 数据流转
```mermaid
graph TD
    A[XxlJob定时任务] --> B[AbstractStrategyDispatchService.execute]
    B --> C[查询人群明细 crowd_detail]
    C --> D[分组过滤 coreLogicExecute]
    D --> E[dispatchHandler方法]
    E --> F[批量模板参数查询 getBatchSmsTempParam]
    F --> G[BatchDispatchServiceImpl调用]
    G --> H[保存crowd_push_batch记录]
    H --> I[批量保存user_dispatch_detail记录]
    I --> J[更新发送计数器]
    J --> K[回执处理]
```

**涉及表操作**:

1. **查询操作**:
    - `strategy` - 查询有效策略
    - `crowd_detail_YYYYMM` - 分页查询人群明细
    - `strategy_group` - 查询分组配置
    - `strategy_market_channel` - 查询渠道配置
    - `template_param` - 批量查询模板参数
2. **插入操作**:
    - `crowd_push_batch` - 插入批次记录
    - `user_dispatch_detail_YYYYMM` - 批量插入触达明细
    - `user_send_counter` - 批量插入/更新计数器
    - `dispatch_task` - 插入分发任务记录
3. **更新操作**:
    - `crowd_push_batch` - 回执时更新统计数据
    - `user_dispatch_detail_YYYYMM` - 回执时更新状态
    - `dispatch_task` - 更新任务状态

## 4. 分表策略
### 4.1 按月分表
+ `event_push_batch_YYYYMM` - 按年月分表
+ `user_dispatch_detail_YYYYMM` - 按年月分表
+ `crowd_detail_YYYYMM` - 按年月分表

### 4.2 分表序号管理
+ 通过 `split_table_cur` 表管理当前分表序号
+ Redis缓存分表序号: `CROWD_DETAIL_CUR_TABLE_NO`
+ 分表大小配置: `crowdConfig.getCrowdDetailTableSize()`

## 5. 流控机制
### 5.1 分布式锁
+ **锁Key格式**: `dispatch_flc_lock:{userId}`
+ **锁超时时间**: 6分钟 (360秒)
+ **实现方式**: Redis分布式锁

### 5.2 流控检查
+ **事件级流控**: `eventflc:{事件类型}:{用户ID}`
+ **触达级流控**: 基于流控规则表配置
+ **频次限制**: 日/周/月维度限制

## 6. 统计与监控
### 6.1 Redis统计
+ **发送统计**: `off_engine_send_num:{date}:{strategyId}`
+ **用户去重**: 使用HyperLogLog结构

### 6.2 Cat监控
+ **流控指标**: `dispatchFlc:{渠道}`、`dispatchFlcLock:{渠道}`
+ **失败指标**: `failed_sms_{strategyId}`
+ **重试指标**: `DispatchFlcTryLockAgain`

## 7. 回执处理
### 7.1 短信回执
+ **队列**: `sms_supplier_report_callback`
+ **状态更新**: `user_dispatch_detail` 表状态字段
+ **统计更新**: `strategy_exec_log` 表统计数据

### 7.2 电销回执
+ **队列**: `voice_supplier_report_callback`
+ **状态映射**: delivered->成功, failed->失败

### 7.3 优惠券回执
+ **队列**: `coupon_supplier_report_callback`
+ **业务回执**: 调用业务方回调接口

## 8. 数据一致性保障
### 8.1 事务管理
+ 批次记录和明细记录在同一事务中保存
+ 流控检查和数据保存原子性操作

### 8.2 异常处理
+ 外部服务调用失败时回滚本地事务
+ 回执处理失败时支持重试机制
+ 分布式锁超时释放机制

