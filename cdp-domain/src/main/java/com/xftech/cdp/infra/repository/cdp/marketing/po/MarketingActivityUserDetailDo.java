package com.xftech.cdp.infra.repository.cdp.marketing.po;

import java.util.Date;

import lombok.Data;

/**
 * 营销活动用户参与明细表
 *
 * @TableName marketing_activity_user_detail
 */
@Data
public class MarketingActivityUserDetailDo {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 奖品类型 1:优惠券
     */
    private Integer rewardType;

    /**
     * 奖品ID
     */
    private String rewardId;

    /**
     * 是否发放成功: -1=初始化;0=失败;1=成功
     */
    private Integer status;

    /**
     * 是否删除 0:未删除;1:已删除
     */
    private Integer dFlag;

    /**
     * 创建人
     */
    private String createdOp;

    /**
     * 修改人
     */
    private String updatedOp;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新时间
     */
    private Date updatedTime;

}