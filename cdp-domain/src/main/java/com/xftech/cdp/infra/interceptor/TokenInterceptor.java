package com.xftech.cdp.infra.interceptor;

import cn.hutool.extra.spring.SpringUtil;
import com.google.common.collect.Sets;
import com.xftech.cdp.domain.auth.annotation.Anonymous;
import com.xftech.cdp.infra.client.sso.SsoClient;
import com.xftech.cdp.infra.client.sso.model.CheckTokenResponse;
import com.xftech.cdp.infra.client.sso.model.LoginDetail;
import com.xftech.cdp.infra.client.sso.model.constants.SsoConstants;
import com.xftech.cdp.infra.constant.HeaderConstants;
import com.xftech.cdp.infra.exception.InfraException;
import com.xftech.cdp.infra.utils.SsoUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.method.HandlerMethod;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Collection;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> wanghu
 * @since : 2022/8/4 14:35:58
 */
@Slf4j
@Component
public class TokenInterceptor {
    @Resource
    private SsoClient ssoClient;

    @Resource
    private SsoUtil ssoUtil;


    void preHandle(HttpServletRequest request, HandlerMethod handlerMethod) {
        Anonymous annotation = handlerMethod.getMethodAnnotation(Anonymous.class);
        String uri = request.getRequestURI();
        String token = request.getHeader(HeaderConstants.TOKEN);
        if ("local".equals(SpringUtil.getProperty("spring.profiles.active"))) {
            return;
        }
        if (!StringUtils.isEmpty(token)) {
            if (annotation != null) {
                return;
            }
            CheckTokenResponse checkTokenResp = ssoClient.checkToken(token);
            if (!checkTokenResp.isSuccess()) {
                if (SsoConstants.UNAUTHORIZED_CODE.equals(checkTokenResp.getCode())) {
                    throw new InfraException(HttpStatus.UNAUTHORIZED.value(), checkTokenResp.getMessage());
                } else {
                    throw new InfraException(checkTokenResp.getMessage());
                }
            }

            LoginDetail data = checkTokenResp.getData();
            Map<String, Object> apis = data.getApi();
            if (!uriEnabled(apis, uri)) {
                throw new InfraException(HttpStatus.FORBIDDEN.value(), "没有权限访问当前资源.");
            }
            if (data.getRoleTag().contains("admin")) {
                data.getUser().setIfAdmin(true);
            }
            ssoUtil.set(data.getUser(), true);

        } else {

        }

    }


    void afterCompletion() {
        ssoUtil.remove();
    }


    private boolean uriEnabled(Map<String, Object> apis, String uri) {
        if (CollectionUtils.isEmpty(apis)) {
            return false;
        }

        if (uri.endsWith("/")) {
            uri = uri.substring(0, uri.length() - 1);
        }
        Set<String> enableApis = Sets.newHashSet();
        extractEnableUris(apis, enableApis);
        uri = uri.startsWith("/") ? "/sparrow" + uri : "/sparrow/" + uri;
        return enableApis.contains(uri);
    }

    private void extractEnableUris(Map<String, Object> apis, Set<String> enableApis) {
        StringBuilder sb = new StringBuilder("/");

        apis.forEach((k0, v0) -> {
            sb.append(k0);
            if (v0 instanceof Map) {
                ((Map) v0).forEach((k1, v1) -> {
                    int len1 = sb.length();
                    sb.append("/").append(k1);
                    if (v1 instanceof Collection) {
                        sb.append("/");
                        ((Collection) v1).forEach(ele -> {
                            int len2 = sb.length();
                            sb.append(ele);
                            enableApis.add(sb.toString());
                            sb.setLength(len2);
                        });
                    }
                    sb.setLength(len1);
                });
            }
            sb.setLength(1);
        });
    }


}
