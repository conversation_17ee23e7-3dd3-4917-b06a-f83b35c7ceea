/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.adapter.external;

import com.xftech.cdp.api.dto.base.Response;
import com.xftech.cdp.api.dto.resp.DecideResp;
import com.xftech.cdp.infra.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 *
 * <AUTHOR>
 * @version $ CommonExecptionAdvice, v 0.1 2024/7/12 14:54 benlin.wang Exp $
 */

@ControllerAdvice
@ResponseBody
@Slf4j
public class CommonExceptionAdvice {

    @ExceptionHandler(HttpMessageNotReadableException.class)
    public Response<String> handleHttpMessageNotReadableException(HttpMessageNotReadableException e) {
        log.error("参数解析失败", e);
        return Response.fail(DecideResp.CodeEnum.InvalidParam.getCode(), "参数解析失败: " + e.getLocalizedMessage());
    }
}