package com.xftech.cdp.domain.strategy.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @<NAME_EMAIL>
 */

@AllArgsConstructor
@Getter
public enum StrategyInstantLabelOptionEnum {
    /**
     * 下拉框可选择标签
     */
    INPUT_OPTIONAL(1, "下拉框可选择标签"),

    /**
     * 默认排除项标签(不可选)
     */
    EXCLUDE_NOT_OPTION(2, "默认排除项标签(不可选)"),

    /**
     * 排除项标签（可选）
     */
    EXCLUDE_OPTION(3, "排除项标签（可选）");

    private final int code;

    private final String description;

}
