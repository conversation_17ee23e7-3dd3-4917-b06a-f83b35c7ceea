package com.xftech.cdp.api;


import com.xftech.cdp.api.dto.base.Response;
import com.xftech.cdp.api.dto.req.auth.GetTokenReq;
import com.xftech.cdp.api.dto.resp.auth.GetTokenResp;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR> wanghu
 * @since : 2022/8/2 17:35:55
 */
@RequestMapping("login")
public interface AuthApi {
//
//    /**
//     * 获取图片验证码
//     * @param req 请求对象
//     * @return 返回对象
//     */
//    @PostMapping("/imgCode")
//    Result<GetImageCodeResp> getImageCode(@RequestBody GetImageCodeReq req);
//
//    /**
//     * 发送短信验证码
//     * @param req 请求对象
//     * @return 返回对象
//     */
//    @PostMapping("/captcha")
//    Result<Empty> sendCaptcha(@RequestBody SendCaptchaReq req);


    /**
     * 获取Token
     *
     * @param req 请求对象
     * @return 返回对象
     */
    @PostMapping("/getToken")
    Response<GetTokenResp> getToken(@RequestBody GetTokenReq req);

}
