package com.xftech.cdp.domain.strategy.repository;


import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.infra.repository.cdp.strategy.po.UserSendCounterDo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class UserSendCounterRepository {

    public void insert(UserSendCounterDo record) {
        DBUtil.insert("userSendCounterMapper.insert", record);
    }

    public long countSum(long userId, long strategyId, int marketChannel, int startDay, int endDay) {
        Map<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        params.put("strategyId", strategyId);
        params.put("marketChannel", marketChannel);
        params.put("startDay", startDay);
        params.put("endDay", endDay);
        List<Integer> rets = DBUtil.selectList("userSendCounterMapper.sumSucValues", params);
        if (CollectionUtils.isEmpty(rets)) {
            return 0L;
        }
        return rets.stream().mapToInt(x -> x).sum();
    }

    public int incrementSum(long userId, long strategyId, int marketChannel, int dateValue, int count) {
        Map<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        params.put("strategyId", strategyId);
        params.put("marketChannel", marketChannel);
        params.put("dateValue", dateValue);
        params.put("count", count);
        return DBUtil.update("userSendCounterMapper.incrementSum", params);
    }

    public int incrementFailed(long userId, long strategyId, int marketChannel, int dateValue) {
        Map<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        params.put("strategyId", strategyId);
        params.put("marketChannel", marketChannel);
        params.put("dateValue", dateValue);
        return DBUtil.update("userSendCounterMapper.incrementFailed", params);
    }
}
