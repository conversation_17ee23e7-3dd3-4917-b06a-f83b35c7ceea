/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2025. All Rights Reserved.
 */

package com.xftech.cdp.distribute.crowd.xxljob;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.xftech.cdp.distribute.crowd.enums.CrowdEffectiveEnum;
import com.xftech.cdp.distribute.crowd.enums.CrowdStatusEnum;
import com.xftech.cdp.distribute.crowd.repository.CrowdInfoRepository;
import com.xftech.cdp.distribute.crowd.repository.model.CrowdInfoDo;
import com.xftech.cdp.distribute.crowd.service.CrowdInfoService;
import com.xftech.cdp.infra.config.ApolloUtil;
import com.xftech.cdp.infra.utils.DateUtil;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.leo.datainsight.client.rr.api.request.ApiCrowdQueryListRequest;
import com.leo.datainsight.client.rr.api.response.ApiCrowdQueryListResponse;
import com.leo.datainsight.client.rr.response.PageResponse;
import com.leo.datainsight.client.rr.response.RestResponse;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import static com.xftech.cdp.infra.config.ApolloKeyConstants.INSIGHT_CROWD_PACK_BIZ_LINE_MAPPING;

/**
 * 人群元数据同步任务
 *
 * @detail: https://alidocs.dingtalk.com/i/nodes/QOG9lyrgJP3ADkjXc2eBDAoxVzN67Mw4?utm_scene=team_space
 */
@Slf4j
@Service
public class CrowdSyncTask {

    @Value("${CrowdSyncTask.pageSize:50}")
    private Integer pageSize;
    @Value("${CrowdSyncTask.crowdInsertBatchSize:50}")
    private Integer crowdInsertBatchSize;

    @Resource
    private CrowdInfoRepository crowdInfoRepository;
    @Resource
    private CrowdInfoService crowdInfoService;

    @XxlJob("CrowdSyncTask")
    public ReturnT<String> crowdSyncTask(String param) {
        if (ApolloUtil.switchStatus("CrowdSyncTaskClose")) {
            return ReturnT.SUCCESS;
        }

        Stopwatch stopwatch = Stopwatch.createStarted();
        log.info("CrowdSyncTask 同步元数据开始");
        try {
            Long pageNo = 1L;
            long total;
            // 已上线人群包
            Set<ApiCrowdQueryListResponse> crowdSyncSet = Sets.newHashSet();

            ApiCrowdQueryListRequest request = new ApiCrowdQueryListRequest();
            request.setPageNo(pageNo.intValue());
            request.setPageSize(pageSize);
            RestResponse<PageResponse<ApiCrowdQueryListResponse>> response = crowdInfoService.queryCrowdInfo(request);
            log.info("CrowdSyncTask pageNo={} request={} response={}", pageNo, JSONObject.toJSONString(request), JSONObject.toJSONString(response));

            if (response != null && Objects.equals(response.getCode(), 200) && response.getData() != null) {
                total = response.getData().getTotal();
                crowdSyncSet.addAll(Optional.ofNullable(response.getData().getRecords()).orElse(Lists.newArrayList()));
            } else {
                return ReturnT.FAIL;
            }

            while (pageNo * pageSize < total) {
                pageNo++;
                try {
                    request.setPageNo(pageNo.intValue());
                    response = crowdInfoService.queryCrowdInfo(request);
                    log.info("CrowdSyncTask pageNo={} request={} response={}", pageNo, JSONObject.toJSONString(request), JSONObject.toJSONString(response));
                    if (response != null && Objects.equals(response.getCode(), 200) && response.getData() != null) {
                        total = response.getData().getTotal();
                        crowdSyncSet.addAll(Optional.ofNullable(response.getData().getRecords()).orElse(Lists.newArrayList()));
                    }
                } catch (Exception e) {
                    log.warn("CrowdSyncTask pageNo={} 同步元数据执行异常", pageNo, e);
                }
            }
            log.info("CrowdSyncTask total={} crowdSyncSet.size={} crowdSyncSet={}", total, crowdSyncSet.size(), JSONObject.toJSONString(crowdSyncSet));

            // 已同步元数据
            List<CrowdInfoDo> crowdInfoDos = Optional.ofNullable(crowdInfoRepository.selectAll()).orElse(Lists.newArrayList());
            Set<Long> crowdInfoIds = crowdInfoDos.stream().map(CrowdInfoDo::getCrowdId).collect(Collectors.toSet());
            // 新增元数据
            List<CrowdInfoDo> additionCrowdInfoDos = crowdSyncSet.stream()
                    .filter(x -> isCdpNeedCrowd(x.getClassificationNames(), x.getId()))
                    .filter(x -> !crowdInfoIds.contains(x.getId()))
                    .map(this::convertCrowdInfoDo)
                    .collect(Collectors.toList());
            // 新增元数据-批量插入
            log.info("CrowdSyncTask additionCrowdInfoDos.size={} additionCrowdInfoDos={}", additionCrowdInfoDos.size(), JSONObject.toJSONString(additionCrowdInfoDos));
            Lists.partition(additionCrowdInfoDos, crowdInsertBatchSize).forEach(x -> crowdInfoRepository.insertBatch(x));
        } catch (Exception e) {
            log.error("CrowdSyncTask 同步元数据执行异常", e);
            return ReturnT.FAIL;
        }
        log.info("CrowdSyncTask 同步元数据执行结束, 总计耗时:{}", stopwatch.stop().elapsed(TimeUnit.MILLISECONDS));
        return ReturnT.SUCCESS;
    }

    private CrowdInfoDo convertCrowdInfoDo(ApiCrowdQueryListResponse crowd) {
        CrowdInfoDo crowdInfoDo = new CrowdInfoDo();
        crowdInfoDo.setCrowdId(crowd.getId());
        crowdInfoDo.setCrowdName(crowd.getCrowdName());
        crowdInfoDo.setCrowdDesc(crowd.getCrowdDesc());
        if (crowd.getEffectStartTime() != null) {
            crowdInfoDo.setValidityBegin(DateUtil.convert(crowd.getEffectStartTime()));
        }
        if (crowd.getEffectEndTime() != null) {
            crowdInfoDo.setValidityEnd(DateUtil.convert(crowd.getEffectEndTime()));
        }
        crowdInfoDo.setIsPermanent(crowd.isPermanent() ? CrowdEffectiveEnum.PERMANENT.getStatus() : CrowdEffectiveEnum.NO_PERMANENT.getStatus());
        crowdInfoDo.setCrowdCreateType(crowd.getCrowdCreateType());
        List<String> classificationNames = crowd.getClassificationNames();
        if (CollectionUtils.isNotEmpty(classificationNames)) {
            crowdInfoDo.setCrowdCategory(String.join(",", classificationNames));
        }
        crowdInfoDo.setCrowdStatus(CrowdStatusEnum.ONLINE.getStatus());
        return crowdInfoDo;
    }

    /**
     * 是否需要同步
     *
     * @param classificationNames 分类名称
     * @return true:需要同步
     */
    private boolean isCdpNeedCrowd(List<String> classificationNames, Long crowdId) {
        if (CollectionUtils.isEmpty(classificationNames)) {
            return false;
        }

        try {
            String categoryLevelOne = classificationNames.get(0); // 洞察平台一级分类

            String bizLineMapping = ApolloUtil.getAppProperty(INSIGHT_CROWD_PACK_BIZ_LINE_MAPPING, "{}");
            Map<String, String> bizLineMap = JSON.parseObject(bizLineMapping, new TypeReference<Map<String, String>>() {
            });

            boolean isNeed = bizLineMap.containsValue(categoryLevelOne);
            log.info("CrowdSyncTask isCdpNeedCrowd crowdId={} categoryLevelOne={} isNeed={}", crowdId, categoryLevelOne, isNeed);
            return isNeed;
        } catch (Exception e) {
            log.error("CrowdSyncTask isCdpNeedCrowd error", e);
        }

        return false;
    }

}