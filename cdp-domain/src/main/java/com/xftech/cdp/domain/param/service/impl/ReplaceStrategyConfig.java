/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.param.service.impl;

import com.xftech.cdp.domain.param.service.ReplaceStrategy;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Supplier;

/**
 *
 * <AUTHOR>
 * @version $ ReplaceStrategyConfig, v 0.1 2024/3/1 17:37 wancheng.qu Exp $
 */

@Configuration
public class ReplaceStrategyConfig {

    @Bean
    public TempParamReplacer stringReplacer() {
        return new TempParamReplacer(strategyMap());
    }

    @Bean
    public Map<String, Supplier<ReplaceStrategy>> strategyMap() {
        Map<String, Supplier<ReplaceStrategy>> map = new HashMap<>();
        map.put("#user_four_phone", PhoneReplaceStrategy::new);
        map.put("#user_mask_name", UserNameReplaceStrategy::new);
        return map;
    }
}