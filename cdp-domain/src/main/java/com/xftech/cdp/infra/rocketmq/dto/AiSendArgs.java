/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.rocketmq.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version $ AiSendArgs, v 0.1 2024/8/20 18:05 lingang.han Exp $
 */

@Data
@AllArgsConstructor
public class AiSendArgs implements Serializable {
    private static final long serialVersionUID = -3816688615419706745L;

    /**
     * 批次号
     */
    private String batchNumber;

    /**
     * 业务来源code
     */
    private String bizSourceCode;

    /**
     * 策略类型
     * offline/realtime
     */
    private String strategyType;

    /**
     * 麻雀策略id
     */
    private Long strategyId;

    /**
     * AI分类
     * AI-即时触达:ai_pronto
     */
    private String aiChannelType;

    private String traceId;

    private Long ts;

    /**
     * xyf-cdp
     */
    private String ua;

    private List<AiUserData> userDataList;

    public AiSendArgs() {
        ua = "xyf-cdp";
        ts = System.currentTimeMillis() / 1000;
        traceId = UUID.randomUUID().toString();
        aiChannelType = "ai_pronto";
    }


}