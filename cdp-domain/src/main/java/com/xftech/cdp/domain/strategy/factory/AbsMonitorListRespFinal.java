package com.xftech.cdp.domain.strategy.factory;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xftech.cdp.api.dto.base.TimeFormat;
import com.xftech.cdp.api.dto.resp.AbsMonitorListResp;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyExecLogDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;


/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AbsMonitorListRespFinal extends AbsMonitorListResp {
    @ApiModelProperty(value = "日期")
    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    @ExcelProperty(index = 0, value = "日期")
    protected LocalDateTime dateTime;

    @ApiModelProperty(value = "分组")
    @ExcelProperty(index = 1, value = "分组")
    protected String groupName;

    @ApiModelProperty(value = "发送渠道")
    @ExcelProperty(index = 2, value = "发送渠道")
    protected String marketChannel;

    @ExcelProperty(index = 4, value = "状态")
    @ApiModelProperty(value = "状态")
    protected String execStatus;

    public AbsMonitorListRespFinal convertRes(StrategyDo strategyDo, StrategyExecLogDo item){
        AbsMonitorListRespFinal absMonitorListRespFinal = new AbsMonitorListRespFinal();
        absMonitorListRespFinal.setDateTime(item.getExecTime());
        return new AbsMonitorListRespFinal();
    }
}
