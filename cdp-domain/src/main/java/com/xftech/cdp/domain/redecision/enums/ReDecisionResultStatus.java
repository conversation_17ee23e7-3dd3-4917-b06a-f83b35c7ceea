package com.xftech.cdp.domain.redecision.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version v1.0 2025/4/10
 * @description ReDecisionStatus
 */
@Getter
@AllArgsConstructor
public enum ReDecisionResultStatus {

    DEFAULT(0, ""),

    TAG_NOT_HIT(1, "实时标签未命中"),

    DELAYED_AGAIN(2, "引擎下发再延迟"),

    ENGINE_MARKETING(3, "引擎下发营销"),

    ENGINE_NOT_MARKETING(4, "引擎下发不营销");

    private final int status;

    private final String desc;

}
