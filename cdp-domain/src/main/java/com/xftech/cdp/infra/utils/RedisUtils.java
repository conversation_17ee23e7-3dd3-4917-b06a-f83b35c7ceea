package com.xftech.cdp.infra.utils;

import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson.JSON;
import com.dianping.cat.proxy.Tracer;
import com.dianping.cat.proxy.spi.Transaction;
import com.google.common.base.Stopwatch;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import com.xftech.cdp.infra.config.LogUtil;

/**
 * Redis工具类
 *
 * @<NAME_EMAIL>
 */
@Component
@Slf4j
public class RedisUtils {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired
    private ValueOperations<String, String> valueOperations;
    @Autowired
    private HashOperations<String, String, String> hashOperations;
    @Autowired
    private ListOperations<String, Object> listOperations;
    @Autowired
    private SetOperations<String, Object> setOperations;
    @Autowired
    private ZSetOperations<String, Object> zSetOperations;

    /**
     * 默认过期时长1天，单位：秒
     */
    public static final long DEFAULT_EXPIRE_DAYS = 60 * 60 * 24L;
    /**
     * 两天
     */
    public static final long DEFAULT_EXPIRE_TWO_DAYS = 60 * 60 * 24 * 2L;
    /**
     * 默认过期时长1月，单位：秒
     */
//    public static final long DEFAULT_EXPIRE_MONTH = 30 * 60 * 60 * 24L;

    /**
     * 1小时
     */
    public static final long DEFAULT_EXPIRE_HOUR = 60 * 60L;
    /**
     * 默认过期时长1分钟，单位：秒
     */
    public static final long DEFAULT_EXPIRE_SECONDS = 60;
    /**
     * 分布式锁默认过期时长1分钟，单位：秒
     */
    public static final long DEFAULT_LOCK_EXPIRE = 60;
    /**
     * 不设置过期时长
     */
    public static final long NOT_EXPIRE = -1;

    /**
     * 分布式锁，默认过期时间一天
     */
    public boolean lock(String key, Object value) {
        return lock(key, value, DEFAULT_EXPIRE_SECONDS);
    }

    /**
     * 分布式锁
     */
    public boolean lock(String key, Object value, long timeout) {
        return lock(key, value, timeout, TimeUnit.SECONDS);
    }

    /**
     * 分布式锁
     */
    public boolean lock(String key, Object value, long timeout, TimeUnit timeUnit) {
        if (timeout < 0) {
            timeout = DEFAULT_LOCK_EXPIRE;
        }
        return valueOperations.setIfAbsent(key, toJson(value), timeout, timeUnit);
    }

    /**
     * 给指定key重新上锁
     *
     * @param key           指定key
     * @param timeoutMillis 过期时间
     */
    public void relock(String key, long timeoutMillis) {
        redisTemplate.expire(key, timeoutMillis < 0 ? DEFAULT_EXPIRE_SECONDS : timeoutMillis, TimeUnit.MILLISECONDS);
    }

    /**
     * 设置key-value并设置过期时间
     */
    public void set(String key, Object value, long expire) {
        if (expire != NOT_EXPIRE) {
            valueOperations.set(key, toJson(value), expire, TimeUnit.SECONDS);
        } else {
            valueOperations.set(key, toJson(value));
        }
    }

    /**
     * 设置key-value 默认过期时间一天
     */
    public void set(String key, Object value) {
        set(key, value, DEFAULT_EXPIRE_SECONDS);
    }

    /**
     * 获取指定key的值并转成指定的对象类型并设置过期时间
     *
     * @param key    指定key
     * @param clazz  需要转换的类型
     * @param expire 过期时间
     * @param <T>    返回值类型
     * @return 指定key的值
     */
    public <T> T get(String key, Class<T> clazz, long expire) {
        String value = valueOperations.get(key);
        if (expire != NOT_EXPIRE) {
            redisTemplate.expire(key, expire, TimeUnit.SECONDS);
        }
        return value == null ? null : fromJson(value, clazz);
    }

    /**
     * 获取指定key的值并转成指定的对象类型
     *
     * @param key   指定key
     * @param clazz 需要转换的类型
     * @param <T>   返回值类型
     * @return 指定key的值
     */
    public <T> T get(String key, Class<T> clazz) {
        return get(key, clazz, NOT_EXPIRE);
    }

    /**
     * 获取指定key的值并转成指定的对象类型,如果发生异常，返回null;
     *
     * @param key   指定key
     * @param clazz 需要转换的类型
     * @param <T>   返回值类型
     * @return 指定key的值
     */
    public <T> T getIfExceptionNull(String key, Class<T> clazz) {
        T result = null;
        try {
            result = get(key, clazz, NOT_EXPIRE);
        } catch (Exception e) {
            log.warn("get info error:{}", e);
        }
        return result;
    }

    /**
     * 获取指定key的值并设置过期时间
     *
     * @param key    指定key
     * @param expire 过期时间
     * @return 指定key的值
     */
    public String get(String key, long expire) {
        String value = valueOperations.get(key);
        if (expire != NOT_EXPIRE) {
            redisTemplate.expire(key, expire, TimeUnit.SECONDS);
        }
        return value;
    }

    /**
     * 获取指定key的值
     *
     * @param key 指定key
     * @return key对应的值
     */
    public String get(String key) {
        return get(key, NOT_EXPIRE);
    }

    /**
     * 获取指定key的值
     *
     * @param key 指定key
     * @return key对应的值
     */
    public Integer getToInteger(String key) {
        String str = get(key, NOT_EXPIRE);
        if (StringUtils.isNotBlank(str)) {
            return Integer.parseInt(str);
        }
        return 0;
    }

    public Long getToLong(String key) {
        String str = get(key, NOT_EXPIRE);
        try {
            if (StringUtils.isNotBlank(str)) {
                return Long.parseLong(str);
            }
            return null;
        } catch (Exception e) {
            log.error("redis get to long fail", e);
            return null;
        }
    }

    /**
     * 删除key
     *
     * @param key 需要删除的key
     */
    public void delete(String key) {
        redisTemplate.delete(key);
    }

    /**
     * 判断是否存在该key
     *
     * @param key key
     * @return Boolean
     */
    public boolean hasKey(String key) {
        return Boolean.TRUE.equals(redisTemplate.hasKey(key));
    }

    /**
     * 设置过期时间
     *
     * @param key     key
     * @param timeout 过期时间
     * @param unit    时间单位
     */
    public void expire(String key, long timeout, TimeUnit unit) {
        redisTemplate.expire(key, timeout, unit);
    }

    /**
     * 获取并设置
     */
    public String getAndSet(String key, String value) {
        return valueOperations.getAndSet(key, value);
    }

    /**
     * 自增
     */
    public Long increment(String key, Long count) {
        return valueOperations.increment(key, count);
    }

    /**
     * 自增
     */
    public Long increment(String key, Long count, Long timeout) {
        Long result = valueOperations.increment(key, count);
        if (timeout != NOT_EXPIRE) {
            expire(key, timeout, TimeUnit.SECONDS);
        }
        return result;
    }

    /**
     * 自减
     */
    public Long decrement(String key, Long count) {
        return valueOperations.decrement(key, count);
    }

    /**
     * 获取过期时间
     */
    public Long getExpireTime(String key) {
        return redisTemplate.opsForValue().getOperations().getExpire(key);
    }

    /**
     * Object转成JSON数据
     */
    private String toJson(Object object) {
        if (object instanceof Integer || object instanceof Long || object instanceof Float ||
                object instanceof Double || object instanceof Boolean || object instanceof String) {
            return String.valueOf(object);
        }
        return JSON.toJSONString(object);
    }

    /**
     * JSON数据，转成Object
     */
    private <T> T fromJson(String json, Class<T> clazz) {
        return JSON.toJavaObject(JSON.parseObject(json), clazz);
    }

    public void unlock(String redisLockKey) {
        redisTemplate.delete(redisLockKey);
    }

    public Boolean add(String invitationRank, String mobile, double score) {
        return redisTemplate.opsForZSet().add(invitationRank, mobile, score);
    }

    public void increaseScore(String invitationRank, String mobile) {
        zSetOperations.incrementScore(invitationRank, mobile, 1.0);
    }


    public Long getPersonRank(String invitationRank, String mobile) {
        return redisTemplate.opsForZSet().reverseRank(invitationRank, mobile);
    }

    public Double getScore(String invitationRank, String mobile) {
        return redisTemplate.opsForZSet().score(invitationRank, mobile);
    }

    public Set<ZSetOperations.TypedTuple<Object>> getRangeWithScore(String invitationRank, long start, long end) {
        return redisTemplate.opsForZSet().reverseRangeWithScores(invitationRank, start, end);
    }

    public Long count(String invitationRank, double score) {
        return redisTemplate.opsForZSet().count(invitationRank, score, score);
    }

    public Long getSize(String invitationRank) {
        return redisTemplate.opsForZSet().size(invitationRank);
    }

/*    public Long rightPushAll(String key, Collection<String> valueList) {
        return listOperations.rightPushAll(key, valueList);
    }*/

    public String leftPopStr(String key) {
        Object value = listOperations.leftPop(key);
        return value == null ? null : value.toString();
    }

    public Long getListSize(String key) {
        return redisTemplate.opsForList().size(key);
    }

    /**
     * 向右添加,需要将value转换成Arr类型,否则会报错
     *
     * @param key
     * @param list
     */
    public void rightPushAll(String key, List<String> list) {
        //先删除旧的
        redisTemplate.delete(key);
        String[] objArr = new String[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objArr[i] = list.get(i);
        }
        redisTemplate.opsForList().rightPushAll(key, objArr);
    }

    public Map<String, String> hPutAll(String key, Map<String, String> map, long expire) {
        hashOperations.putAll(key, map);
        if (expire > 0) {
            redisTemplate.expire(key, expire, TimeUnit.SECONDS);
        }
        return hGetAll(key);
    }

    public void hPut(String key, String subKey, String value, long expire) {
        hashOperations.put(key, subKey, value);
        if (expire > 0) {
            redisTemplate.expire(key, expire, TimeUnit.SECONDS);
        }
    }

    public void hIncrease(String key, String subKey, Long value, long expire) {
        hashOperations.increment(key, subKey, value);
        if (expire > 0) {
            redisTemplate.expire(key, expire, TimeUnit.SECONDS);
        }
    }

    public void hDelete(String key, String subKey) {
        hashOperations.delete(key, subKey);
    }

    public String hGet(String key, String field) {
        return hashOperations.get(key, field);
    }

    public Map<String, String> hGetAll(String key) {
        return hashOperations.entries(key);
    }

    public void pfAddTwoDay(String key, Object value) {
        redisTemplate.opsForHyperLogLog().add(key, value);
        redisTemplate.expire(key, DEFAULT_EXPIRE_TWO_DAYS, TimeUnit.SECONDS);
    }

    public void pfAdd(String key, Object value, long expire) {
        redisTemplate.opsForHyperLogLog().add(key, value);
        redisTemplate.expire(key, expire, TimeUnit.SECONDS);
    }

    public Long pfCount(String key) {
        return redisTemplate.opsForHyperLogLog().size(key);
    }

    /**
     * 尝试获取分布式锁
     * @param lockKey 锁key
     * @param lockSeconds 锁超时时间,单位s
     * @param maxRetryTimes 未获取到锁时,最大重试次数
     * @param retryIntervalMillis 未获取到锁时,重试间隔时间,单位ms
     * @return
     */
    public boolean tryLock(String lockKey, int lockSeconds, int maxRetryTimes, long retryIntervalMillis) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        boolean lockSuccess = false;
        Transaction transaction = Tracer.newTransaction("RedisUtils", "tryLock");
        int retryTimes = 0;

        try {
            while (!lockSuccess && retryTimes < maxRetryTimes) {
                 try {
                     retryTimes++;
                     lockSuccess = lock(lockKey, "LOCKED", lockSeconds);
                     if (!lockSuccess) {
                         Tracer.logEvent("RedisUtils.tryLock.retry", lockKey);
                         ThreadUtil.sleep(retryIntervalMillis);
                     }
                 } catch (Exception e) {
                     log.error("获取分布式锁异常,lockKey={}", lockKey, e);
                 }
            }
            if (lockSuccess) {
                transaction.setStatus(Transaction.SUCCESS);
            }
        } catch (Exception e) {
            transaction.setStatus(e);
            log.error("获取分布式锁异常,lockKey={}", lockKey, e);
        }

        transaction.complete();
        LogUtil.logDebug("RedisUtils tryLock lockSuccess={}, retryTimes={}, {}ms. lockKey={} lockSeconds={} maxRetryTimes={} retryIntervalMillis={}", lockSuccess, retryTimes, stopwatch.elapsed(TimeUnit.MILLISECONDS), lockKey, lockSeconds, maxRetryTimes, retryIntervalMillis);
        return lockSuccess;
    }

}

