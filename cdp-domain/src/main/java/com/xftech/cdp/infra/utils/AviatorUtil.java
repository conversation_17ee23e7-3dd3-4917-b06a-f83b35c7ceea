package com.xftech.cdp.infra.utils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import com.xftech.cdp.infra.aviator.AviatorCommon;
import com.xftech.cdp.infra.config.ApolloUtil;
import com.xftech.cdp.infra.exception.AviatorException;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.AviatorEvaluatorInstance;
import com.googlecode.aviator.Expression;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Aviator工具类
 *
 * <AUTHOR>
 */
public class AviatorUtil {

    private static final Logger logger = LoggerFactory.getLogger(AviatorUtil.class);

    private static AviatorEvaluatorInstance aviatorEvaluatorInstance;

    static {
        AviatorCommon.loadFunction();
        aviatorEvaluatorInstance = AviatorEvaluator.getInstance();
        logger.info("AviatorUtil static init aviator function success!");
    }

    public static Boolean compute(String script, Map<String, Object> paramMap) {
        try {
            Expression expression = aviatorEvaluatorInstance.compile(script, true);
            if (logger.isDebugEnabled()) {
                logger.debug("AviatorUtil compute script={}, paramMap={}", script, paramMap);
            }
            // 参数检查
            if (!paramCheck(expression, paramMap)) {
                return false;
            }
            //参数转换
            paramReplace(paramMap);
            Object result = expression.execute(paramMap);
            if (logger.isDebugEnabled()) {
                logger.debug("AviatorUtil compute result={}", result);
            }
            return (Boolean) result;
        } catch (Exception ex) {
            logger.error("AviatorUtil compute error, script={}, paramMap={}", script, JsonUtil.toJson(paramMap), ex);
        }
        return false;
    }
    private static boolean paramCheck(Expression expression, Map<String, Object> dataMap) {
        if (expression == null) {
            throw new AviatorException("AviatorUtil paramCheck expression is null");
        }
        // 表达式使用到的字段名称
        List<String> dataNameList = expression.getVariableNames();
        if (CollectionUtils.isNotEmpty(dataNameList)) {
            for (String dataName : dataNameList) {
                if (!"nil".equals(dataName)) {
                    List<String> needIdCardNumberCodes = JSONObject.parseArray(ApolloUtil.getAppProperty("ads.feature.need.idcardnumber", "[]"), String.class);
                    if (needIdCardNumberCodes.contains(dataName) && !dataMap.containsKey(dataName)) {
                        return false;
                    }
                    List<String> needCustNoCodes = JSONObject.parseArray(ApolloUtil.getAppProperty("ads.feature.need.custno", "[]"), String.class);
                    if (needCustNoCodes.contains(dataName) && !dataMap.containsKey(dataName)) {
                        return false;
                    }
                    List<String> needAcctNoCodes = JSONObject.parseArray(ApolloUtil.getAppProperty("ads.feature.need.acctno", "[]"), String.class);
                    if (needAcctNoCodes.contains(dataName) && !dataMap.containsKey(dataName)) {
                        return false;
                    }
                    List<String> needUserNoCodes = JSONObject.parseArray(ApolloUtil.getAppProperty("ads.feature.need.userno", "[]"), String.class);
                    if (needUserNoCodes.contains(dataName) && !dataMap.containsKey(dataName)) {
                        return false;
                    }
                    if (!dataMap.containsKey(dataName)) {
                        // 获取不到特征值停止处理用户的判断，走兜底（false）逻辑
                        throw new AviatorException("AviatorUtil paramCheck 【" + dataName + "】 can not find");
                    }
                    // 标签异常兜底值处理 TODO 优雅处理
                    if (JSONObject.parseArray(ApolloUtil.getAppProperty("ads.feature.fallback.labels.paramCheckNull", "[]"), String.class).contains(dataName) && Objects.isNull(dataMap.get(dataName))) {
                        return false;
                    }
                }
            }
        } else {
            logger.warn("AviatorUtil paramCheck aviator not param");
        }
        return true;
    }
    private static Boolean paramReplace( Map<String, Object> dataMap) {
        try {
            List<String> keysToReplace = JSONObject.parseArray(ApolloUtil.getAppProperty("ads.feature.replace.config", "[]"), String.class);
            for (String key : keysToReplace) {
                if (dataMap.containsKey(key)) {
                    Object value = dataMap.get(key);
                    //判断是否是String的年月日时分秒日期类型，如果是日期类型，则判断是否是今天，如果是今天则返回1，否则返回0
                    if (value instanceof String && value.toString().matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}")) {
                        LocalDateTime dateTime = LocalDateTime.parse(value.toString(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                        LocalDate parsedDate = dateTime.toLocalDate();
                        LocalDate currentDate = LocalDate.now();
                        String status = parsedDate.isEqual(currentDate) ? "1" : "0";
                        dataMap.put(key, status);
                    }
                }
            }
            return true;
        } catch (Exception e) {
            logger.error("AviatorUtil paramReplace error,  paramMap={}", JsonUtil.toJson(dataMap), e);
            return false;
        }
    }

    public static void main(String[] args) {
        String expression = "contain(app,'xyf01')";
        Map<String, Object> labelValueMap = Maps.newHashMap();
        labelValueMap.put("app", "xyf,xyf01");

        Boolean result = AviatorUtil.compute(expression, labelValueMap);
        System.out.println(result);
    }

}
