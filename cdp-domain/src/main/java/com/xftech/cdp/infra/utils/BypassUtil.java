package com.xftech.cdp.infra.utils;


import org.springframework.util.DigestUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version $ BypassUtil, v 0.1 2025/3/14 14:56 mingwen.zang
 */
public class BypassUtil {

    public interface Group {
    }

    public enum BypassGroups implements Group {
        GroupA,
        GroupB,
        GroupC
    }

    public static void main(String[] args) {
        long d = System.currentTimeMillis(); // 示例请求ID
        int aCount = 0;
        int bCount = 0;
        for (int i = 0; i < 99999; i++) {
            if (choosePercentage(1)) {
                aCount++;
            } else {
                bCount++;
            }
        }
        System.out.println("aCount " + aCount + " bCount: " + bCount);

        aCount = 0;
        bCount = 0;
        for (int i = 0; i < 99999; i++) {
            if (choosePercentagePerThousand(1)) {
                aCount++;
            } else {
                bCount++;
            }
        }
        System.out.println("aCount " + aCount + " bCount: " + bCount);
    }


    /**
     * 百分比分流
     */
    public static boolean choosePercentage(double rate) {
        return Objects.equals(BypassGroups.GroupA, choose(Math.random(), 100, new Group[]{BypassGroups.GroupA, BypassGroups.GroupB}, new double[]{rate}));
    }


    /**
     * 千分比分流
     */
    public static boolean choosePercentagePerThousand(double rate) {
        return Objects.equals(BypassGroups.GroupA, choose(Math.random(), 1000, new Group[]{BypassGroups.GroupA, BypassGroups.GroupB}, new double[]{rate}));
    }


    /**
     * 万分比分流
     */
    public static boolean choosePartsPerTenThousand(double rate) {
        return Objects.equals(BypassGroups.GroupA, choose(Math.random(), 10000, new Group[]{BypassGroups.GroupA, BypassGroups.GroupB}, new double[]{rate}));
    }

    /**
     * 分流功能
     */
    public static Group choose(double requestId, int scale, Group[] groups, double[] rates) {
        String id = String.valueOf(requestId);
        String digest = DigestUtils.md5DigestAsHex(id.getBytes());
        int hashCode = Math.abs(digest.hashCode()) % scale; // 映射到0-99的范围

        double start = 0.0;
        for (int i = 0; i < rates.length; i++) {
            double end = start + rates[i];
            if (start <= hashCode && hashCode < end) {
                return groups[i];
            }
            start = end;
        }
        return null; // 如果没有匹配到任何组，返回null
    }

}
