/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version $ EngineStrategyGroupIdInfoDto, v 0.1 2023/12/22 17:51 lingang.han Exp $
 */

@Data
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class EngineStrategyGroupIdInfoDto implements Serializable {
    private static final long serialVersionUID = 2572944083885150782L;

    private String groupId;

    private String groupSource;

    private List<ChannelInfo> channelInfoList;

    @Data
    @EqualsAndHashCode
    public static class ChannelInfo {
        private Integer marketChannel;
        private String template;
    }
}