package com.xftech.cdp.domain.flowctrl.model.enums;

import com.xftech.cdp.infra.constant.StrategyDispatchConstants;
import com.xftech.cdp.infra.exception.BizException;
import lombok.Getter;

import java.util.Objects;

/**
 * 流控渠道类型
 *
 * @<NAME_EMAIL>
 */

@Getter
public enum ChannelTypeEnum {
    /**
     * 全部
     */
    ALL(0, "全部渠道"),
    /**
     * 渠道
     */
    MESSAGE(1, "短信"),

    /**
     * 电销
     */
    TELE(2, "电销"),

    /**
     * 优惠券
     */
    COUPON(3, "优惠券"),

    VOICE_NEW(4, "新电销"),

    PUSH(5, "push"),

    AI_PRONTO(6, "AI-即时触达"),


    // 200
    INCREASE_AMOUNT(200,  "提额"),
    X_DAY_INTEREST_FREE(201,  "X天还款免息"),
    LIFE_RIGHTS(202,  "生活权益"),
    ;

    private final Integer type;
    private final String desc;

    ChannelTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static ChannelTypeEnum getInstance(Integer type) {
        for (ChannelTypeEnum channelTypeEnum : ChannelTypeEnum.values()) {
            if (Objects.equals(channelTypeEnum.getType(), type)) {
                return channelTypeEnum;
            }
        }
        throw new BizException(String.format("流控渠道类型异常，状态：%s", type));
    }
}
