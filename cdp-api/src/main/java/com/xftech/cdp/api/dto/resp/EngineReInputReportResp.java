package com.xftech.cdp.api.dto.resp;

import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0 2025/4/10
 * @description EngineReInputReportResp
 */
@Data
public class EngineReInputReportResp {
    /**
     * 引擎分组ID
     */
    private String groupId;
    /**
     * 延迟轮次
     */
    private Integer reinputCount;
    /**
     * 本轮延迟决策人次
     */
    private Integer numberOfDelayed;
    /**
     * 本轮延迟决策人数
     */
    private Integer numberOfPeopleDelayed;
    /**
     * 本轮进入引擎人次
     */
    private Integer numberOfEntryEngine;
    /**
     * 本轮进入引擎人数
     */
    private Integer numberOfPeopleEntryEngine;
    /**
     * 本轮决策结果为营销人次
     */
    private Integer numberOfMarketing;
    /**
     * 本轮决策结果为营销人数
     */
    private Integer numberOfPeopleMarketing;
    /**
     * 本轮排除标签过滤人数
     */
    private Integer numberOfPeopleExcludedByTagFiltering;
    /**
     * 本轮应发用户人次
     */
    private Integer numberOfDistribution;
    /**
     * 本轮应发用户人数
     */
    private Integer numberOfPeopleDistribution;
    ///**
    // * 本轮决策结果为不营销人次
    // */
    //private Long numberOfNoMarketing;
    ///**
    // * 本轮决策结果为不营销人数
    // */
    //private Long numberOfPeopleNoMarketing;
    /**
     * 本轮决策失败人次
     */
    private Integer numberOfFail;
    /**
     * 本轮决策失败人数
     */
    private Integer numberOfPeopleFail;

}
