package com.xftech.cdp.api.dto.req.auth;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xftech.cdp.api.dto.base.Request;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> wanghu
 * @since : 2022/8/2 15:54:25
 */
@Setter
@Getter
public class GetTokenReq extends Request {

    /**
     * 手机号	否	15899394006
     */
    @JsonProperty("mobile")
    @JSONField(name = "mobile")
    private String mobile;

    /**
     * 短信验证码，当mobile有值，此项必填	否	1234
     */
    @JsonProperty("captcha")
    @JSONField(name = "captcha")
    private String captcha;

    /**
     * 登陆方式，如 dingding	否	空字符
     */
    @JsonProperty("type")
    @JSONField(name = "type")
    private String type;

    /**
     * 第三方登录code，当type有值，此项必填	否	daskdhshfudguifg34fdsfbk
     */
    @JsonProperty("code")
    @JSONField(name = "code")
    private String code;


}
