package com.xftech.cdp.domain.crowd.service.dispatch.impl;

import brave.Tracing;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.math.MathUtil;
import com.alibaba.fastjson.JSON;
import com.dianping.cat.proxy.Tracer;
import com.google.common.base.Stopwatch;
import com.xftech.cdp.api.dto.base.TimeFormat;
import com.xftech.cdp.domain.ads.repository.AdsOprtAutoMessageDfRepository;
import com.xftech.cdp.domain.cache.CacheCrowdExecLogService;
import com.xftech.cdp.domain.cache.CacheCrowdPackService;
import com.xftech.cdp.domain.crowd.exception.CrowdException;
import com.xftech.cdp.domain.crowd.factory.AbsCrowdOptService;
import com.xftech.cdp.domain.crowd.factory.CrowdOptFactory;
import com.xftech.cdp.domain.crowd.model.dispatch.BatchConsumer;
import com.xftech.cdp.domain.crowd.model.dispatch.CrowdContext;
import com.xftech.cdp.domain.crowd.model.dispatch.CrowdWereHouse;
import com.xftech.cdp.domain.crowd.model.dispatch.InvokePage;
import com.xftech.cdp.domain.crowd.model.enums.CrowdExecResultEnum;
import com.xftech.cdp.domain.crowd.model.enums.CrowdLabelEnum;
import com.xftech.cdp.domain.crowd.model.label.crowd.FixedRadioOption;
import com.xftech.cdp.domain.crowd.repository.AdsLabelMonitorDfRepository;
import com.xftech.cdp.domain.crowd.repository.AdsUserLabelDetailInfoDfRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdDetailRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdExecLogRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdExecSnapshotRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdLabelPrimaryRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdLabelRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdPackRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdWereHouseSnapshotRepository;
import com.xftech.cdp.domain.crowd.repository.LabelRepository;
import com.xftech.cdp.domain.crowd.service.CrowdDetailSubService;
import com.xftech.cdp.domain.crowd.service.UtmSourceService;
import com.xftech.cdp.domain.crowd.service.dispatch.CrowdDispatchService;
import com.xftech.cdp.domain.randomnum.RandomNumService;
import com.xftech.cdp.domain.stat.entity.CrowdUtmDecisionRecordEntity;
import com.xftech.cdp.domain.stat.entity.UtmResult;
import com.xftech.cdp.domain.stat.repository.CrowdUtmDecisionRecordDao;
import com.xftech.cdp.infra.client.datacenter.DataCenterClient;
import com.xftech.cdp.infra.client.dingtalk.config.DingTalkConfig;
import com.xftech.cdp.infra.client.loanmarket.enums.UtmSourceTypeEnum;
import com.xftech.cdp.infra.client.usercenter.UserCenterClient;
import com.xftech.cdp.infra.config.AppConfigService;
import com.xftech.cdp.infra.config.Config;
import com.xftech.cdp.infra.config.CrowdConfig;
import com.xftech.cdp.infra.constant.Constants;
import com.xftech.cdp.infra.exception.InterfaceException;
import com.xftech.cdp.infra.rabbitmq.enums.DecisionResultEnum;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdExecLogDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdExecSnapshotDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPackDo;
import com.xftech.cdp.infra.utils.AesUtil;
import com.xftech.cdp.infra.utils.JsonUtil;
import com.xftech.cdp.infra.utils.RedisKeyUtils;
import com.xftech.cdp.infra.utils.RedisUtils;
import com.xftech.xxljob.XxlJobAdminClient;
import com.xftech.xxljob.model.XxlJobDto;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.lang.NonNull;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/2/16
 */
@Slf4j
public abstract class AbstractCrowdDispatchService implements CrowdDispatchService {

    protected final CrowdPackRepository crowdPackRepository;
    protected final CrowdLabelRepository crowdLabelRepository;
    protected final CrowdLabelPrimaryRepository crowdLabelPrimaryRepository;
    protected final LabelRepository labelRepository;
    protected final CrowdDetailRepository crowdDetailRepository;
    protected final CrowdExecSnapshotRepository crowdExecSnapshotRepository;
    protected final AdsLabelMonitorDfRepository adsLabelMonitorDfRepository;
    protected final UserCenterClient userCenterClient;
    protected final DataCenterClient dataCenterClient;
    protected final CrowdExecLogRepository crowdExecLogRepository;
    protected final XxlJobAdminClient xxlJobAdminClient;
    protected final CrowdWereHouseSnapshotRepository crowdWereHouseSnapshotRepository;
    protected final CrowdConfig crowdConfig;

    protected final CrowdDetailSubService crowdDetailSubService;
    @Resource
    protected ThreadPoolExecutor crowdPoolExecutor;
    @Resource
    protected ThreadPoolExecutor crowdPoolFixedNumberPageExecutor;
    @Autowired
    AdsUserLabelDetailInfoDfRepository crowdWareHouseRepository;
    @Autowired
    protected AdsOprtAutoMessageDfRepository adsOprtAutoMessageDfRepository;
    @Autowired
    protected Config config;
    @Autowired
    private CrowdOptFactory crowdOptFactory;
    @Autowired
    private DingTalkConfig dingTalkConfig;
    @Autowired
    private CacheCrowdExecLogService cacheCrowdExecLogService;
    @Autowired
    private CacheCrowdPackService cacheCrowdPackService;
    @Autowired
    private UtmSourceService utmSourceService;
    @Autowired
    private CrowdUtmDecisionRecordDao crowdUtmDecisionRecordDao;
    @Autowired
    private AdsUserLabelDetailInfoDfRepository adsUserLabelDetailInfoDfRepository;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private RandomNumService randomNumService;
    @Autowired
    private AppConfigService appConfigService;
    private static ExpressionParser parpser = new SpelExpressionParser();

    protected AbstractCrowdDispatchService(CrowdPackRepository crowdPackRepository, CrowdLabelRepository crowdLabelRepository, CrowdLabelPrimaryRepository crowdLabelPrimaryRepository, LabelRepository labelRepository, CrowdDetailRepository crowdDetailRepository, CrowdExecSnapshotRepository crowdExecSnapshotRepository, AdsLabelMonitorDfRepository adsLabelMonitorDfRepository, UserCenterClient userCenterClient, DataCenterClient dataCenterClient, CrowdExecLogRepository crowdExecLogRepository, XxlJobAdminClient xxlJobAdminClient, CrowdWereHouseSnapshotRepository crowdWereHouseSnapshotRepository, CrowdConfig crowdConfig, CrowdDetailSubService crowdDetailSubService) {
        this.crowdPackRepository = crowdPackRepository;
        this.crowdLabelRepository = crowdLabelRepository;
        this.crowdLabelPrimaryRepository = crowdLabelPrimaryRepository;
        this.labelRepository = labelRepository;
        this.crowdDetailRepository = crowdDetailRepository;
        this.crowdExecSnapshotRepository = crowdExecSnapshotRepository;
        this.adsLabelMonitorDfRepository = adsLabelMonitorDfRepository;
        this.userCenterClient = userCenterClient;
        this.dataCenterClient = dataCenterClient;
        this.crowdExecLogRepository = crowdExecLogRepository;
        this.xxlJobAdminClient = xxlJobAdminClient;
        this.crowdWereHouseSnapshotRepository = crowdWereHouseSnapshotRepository;
        this.crowdConfig = crowdConfig;
        this.crowdDetailSubService = crowdDetailSubService;
    }

    @Override
    public void execute(@NonNull Long crowdId) {
        if(appConfigService.whetherGrayscaleCrowd(crowdId)){
            log.info("人群包 crowdId:{} 命中停跑配置, 已停止", crowdId);
            return;
        }

        log.info("人群包老方式监控, 人群包id:{}", crowdId);
        CrowdContext crowdContext = initContext(crowdId);
        try {
            // SQL组装
            crowdContext.getCrowdOpt().organizeSql(crowdContext);
            // 执行记录
            beginExecute(crowdContext);
            // 前置校验
            crowdContext.getCrowdOpt().preHandler(crowdContext);
            filterUserIds(crowdContext);
            successExecute(crowdContext);
        } catch (Exception e) {
            log.warn("crowd execute error", e);
            List<String> atMobileList = new ArrayList<>(dingTalkConfig.atMobileList());
            atMobileList.add(crowdContext.getCrowdPack().getUpdatedOpMobile());
            String newRandomFailMsg = crowdContext.isNewRandomfailed() ? "新随机数获取失败，" : "";
            crowdContext.getCrowdPack().alarmDingTalk(crowdConfig.getAlarmUrl(), atMobileList, newRandomFailMsg);
            if (e instanceof InterruptedException) {
                failedExecuteInterrupted(crowdContext);
            } else {
                failedExecute(crowdContext);
            }
        } finally {
            cleanLatestRefreshTimeKey(crowdId);
            long interval = System.currentTimeMillis() - crowdContext.getBeginMillis();
            log.info("人群包 crowdId:{} 总共耗时:{}", crowdId, interval);
            XxlJobLogger.log("人群包 crowdId:{} 总共耗时:{}", crowdId, interval);
        }

    }

    // 灰度，是否按照固定页大小拉取NTP
    private boolean isRunAsFixedNumberPage(Long crowdId) {
        boolean isRunAsFixedNumberPage = false;
        String express = appConfigService.getCrowdRunFixedNumerPageExpressionStr();
        if (!StringUtils.isEmpty(express)) {
            try {
                Expression expression = parpser.parseExpression(express);
                EvaluationContext context = new StandardEvaluationContext();
                context.setVariable("crowdId", crowdId);
                if (Objects.equals(true, expression.getValue(context))) {
                    isRunAsFixedNumberPage = true;
                    log.info("跑人群包按照固定分页大小, 人群包id:{}", crowdId);
                }
            } catch (Exception ex) {
                log.error("CrowdContext", ex);
            }
        }
        return isRunAsFixedNumberPage;
    }

    /**
     * 参数校验、初始化上下文
     *
     * @return 上下文
     */
    public CrowdContext initContext(@NonNull Long crowdId) {
        CrowdPackDo crowdPack = crowdPackRepository.selectById(crowdId);
        if (crowdPack == null) {
            throw new CrowdException("该人群包不存在");
        }

        LocalDateTime now = LocalDateTime.now();
        LocalDateTime endTime = LocalDateTime.now().with(LocalTime.of(23, 59, 59));
        redisUtils.set(RedisKeyUtils.genCrowdLatestRefreshTimeKey(crowdId), LocalDateTimeUtil.format(now, TimeFormat.DATE_TIME), Duration.between(now, endTime).getSeconds());

        crowdPack.verifyCrowd(xxlJobAdminClient);
        crowdPack.setLatestRefreshTime(now);
        AbsCrowdOptService crowdOpt = crowdOptFactory.createOpt(crowdPack.getPullType());

        boolean isRunAsFixedNumberPage = isRunAsFixedNumberPage(crowdId);
        try {
            return crowdOpt.initContext(isRunAsFixedNumberPage, crowdPack, crowdOpt);
        } catch (Exception e) {
            log.warn("上下文初始化失败，人群包：{}", crowdId, e);
        }
        return CrowdContext.init(isRunAsFixedNumberPage, crowdPack, crowdOpt);
    }

    protected void filterUserIds(CrowdContext crowdContext) throws InterruptedException {
        CountDownLatch countDownLatch = new CountDownLatch(crowdContext.getInvokePages().size());
        for (InvokePage invokePage : crowdContext.getInvokePages()) {
            (crowdContext.isRunAsFixedNumberPage() ? crowdPoolFixedNumberPageExecutor : crowdPoolExecutor).execute(() -> {
                BatchConsumer<CrowdDetailDo> batchConsumer;
                try {
                    batchConsumer = new BatchConsumer<>(crowdConfig.getBatchSaveSize(), crowdDetailSubService::splitTableBatchSave);
                    invokePage.setBatchConsumer(batchConsumer);
                    if (StringUtils.isEmpty(crowdContext.getLabelSqlPair().getRight())) {
                        saveIncludeUserIds(crowdContext, invokePage);
                    } else {
                        filterAllUserIds(crowdContext, invokePage);
                    }
                    batchConsumer.finallyList();
                } catch (Exception e) {
                    log.warn("crowd id:{}, name:{} 拉取人群包异常", crowdContext.getCrowdPack().getId(), crowdContext.getCrowdPack().getCrowdName(), e);
                    crowdContext.setFailReason("拉取人群包异常");
                    crowdContext.setFailed(true);
                } finally {
                    countDownLatch.countDown();
                    batchConsumer = null;
                }

            });
        }

        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            log.warn("crowd id:{}, name:{} 人群包被中断执行", crowdContext.getCrowdPack().getId(), crowdContext.getCrowdPack().getCrowdName(), e);
            crowdContext.setFailReason("人群包被中断执行");
            crowdContext.setFailed(true);
            throw e;
        }

        if (crowdContext.isFailed()) {
            throw new CrowdException("人群刷新失败，请尽快排查并操作手动刷新!");
        }
    }

    /**
     * 数据筛选
     */
    protected abstract void filterAllUserIds(CrowdContext crowdContext, InvokePage invokePage);

    protected void saveIncludeUserIds(CrowdContext crowdContext, InvokePage invokePage) {
        Pair<StringBuilder, StringBuilder> labelSqlPair = crowdContext.getLabelSqlPair();
        invokePageById(invokePage, labelSqlPair.getLeft(), crowdWereHouses ->
                        batchSave(crowdWereHouses.stream().distinct().collect(Collectors.toList()), crowdContext, invokePage.getBatchConsumer())
                , crowdContext);

    }

    /**
     * 批量插入
     */
    protected void batchSave(List<CrowdWereHouse> partitionWereHouses, CrowdContext crowdContext, BatchConsumer<CrowdDetailDo> batchConsumer) {
        if (CollectionUtils.isEmpty(partitionWereHouses)){
            return;
        }
        List<CrowdDetailDo> crowdDetailDos = new ArrayList<>(partitionWereHouses.size());
        for (CrowdWereHouse crowdWereHouse : partitionWereHouses) {
            CrowdDetailDo crowdDetailDo = new CrowdDetailDo();
            crowdDetailDo.setCrowdId(crowdContext.getCrowdPack().getId());
            crowdDetailDo.setCrowdExecLogId(crowdContext.getCrowdExecLog().getId());
            crowdDetailDo.setUserId(crowdWereHouse.getAppUserId());
            crowdDetailDo.setApp(crowdWereHouse.getApp());
            crowdDetailDo.setInnerApp(crowdWereHouse.getInnerApp());
            crowdDetailDo.setMobile(AesUtil.mobileDecrypt(crowdWereHouse.getMobile(), config.getAdsMobileAesKey()));
            crowdDetailDo.setAppUserIdLast2(crowdWereHouse.getAppUserIdLast2());
            crowdDetailDo.setAbNum(crowdWereHouse.getAbNum());
            crowdDetailDo.setRegisterTime(crowdWereHouse.getRegisterTime());
            crowdDetailDos.add(crowdDetailDo);
        }
        batchConsumer.invoke(crowdDetailDos);
        crowdContext.getPersonNum().getAndAdd(crowdDetailDos.size());
        crowdDetailDos = null;
    }

    @Deprecated
    private void invokePageByIdNew(InvokePage invokePage, StringBuilder sql, Consumer<List<CrowdWereHouse>> crowdWereHouseConsumer, CrowdContext crowdContext) {
        Stopwatch stopwatch = Stopwatch.createUnstarted();
        List<CrowdWereHouse> crowdWereHouses;
        // 分段信息
        long maxAppUserId = invokePage.getMaxAppUserId();
        InvokePage page = new InvokePage(invokePage.getAppUserId(), invokePage.getPageSize(), invokePage.getMaxAppUserId()
                , invokePage.getBatchConsumer());
        do {
            stopwatch.reset();
            if (crowdContext.isFailed()) {
                break;
            }
            long endAppUserId = Math.min(page.getAppUserId() + page.getPageSize(), maxAppUserId);
            page.setMaxAppUserId(endAppUserId);
            if (page.getAppUserId() >= maxAppUserId) {
                break;
            }
            StringBuilder newSql = new StringBuilder(sql);
            crowdContext.getCrowdOpt().andPageNew(newSql, page);
            stopwatch.start();
            crowdWereHouses = crowdContext.getCrowdOpt().queryAdsDataInfo(newSql.toString(), crowdContext.isRunAsFixedNumberPage());
            stopwatch.stop();
            log.info("invokePageByIdNew take:{}, executeIncludeSql:{}", stopwatch.elapsed(TimeUnit.MILLISECONDS), newSql);
            if (!CollectionUtils.isEmpty(crowdWereHouses)) {
                // 渠道过滤
                filterUtmSource(crowdContext, crowdWereHouses);
                filterNewRandom(crowdContext, crowdWereHouses);
                crowdWereHouseConsumer.accept(crowdWereHouses);
            }
            crowdWereHouses = null;
            page.setAppUserId(page.getMaxAppUserId());
        } while (page.getAppUserId() < maxAppUserId);
    }

    @Deprecated
    protected void invokePageById(InvokePage invokePage, StringBuilder sql, Consumer<List<CrowdWereHouse>> crowdWereHouseConsumer, CrowdContext crowdContext) {
        if (crowdContext.isRunAsFixedNumberPage()) {
            log.info("跑人群包数据, 执行新的分页逻辑, 人群:{}, 分页信息:{}", Optional.ofNullable(crowdContext.getCrowdPack()).orElse(new CrowdPackDo()).getId(),
                    JsonUtil.toJson(invokePage));
            invokePageByIdNew(invokePage, sql, crowdWereHouseConsumer, crowdContext);
            return;
        }
        List<CrowdWereHouse> crowdWereHouses;
        while (true) {
            if (crowdContext.isFailed()) {
                break;
            }
            StringBuilder newSql = new StringBuilder(sql);
            crowdContext.getCrowdOpt().andPage(newSql, invokePage);
            long beginMillis = System.currentTimeMillis();
            crowdWereHouses = crowdContext.getCrowdOpt().queryAdsDataInfo(newSql.toString(), crowdContext.isRunAsFixedNumberPage());
            long interval = System.currentTimeMillis() - beginMillis;
            log.info("crowd execute include sql:{} take:{}", newSql, interval);
            if (CollectionUtils.isEmpty(crowdWereHouses)) {
                break;
            }
            int crowdSize = crowdWereHouses.size();
            invokePage.setAppUserId(crowdWereHouses.get(crowdSize - 1).getAppUserId());
            // 渠道过滤
            filterUtmSource(crowdContext, crowdWereHouses);
            filterNewRandom(crowdContext, crowdWereHouses);
            crowdWereHouseConsumer.accept(crowdWereHouses);
            if (crowdSize < invokePage.getPageSize()) {
                break;
            }
            crowdWereHouses = null;
        }
    }

    /**
     * 人群包过滤渠道（首次注册渠道，历史进件渠道，最近一次放款成功渠道）
     *
     * @param crowdContext
     * @param crowdWereHouseList
     */
    protected void filterUtmSource(CrowdContext crowdContext, List<CrowdWereHouse> crowdWereHouseList) {
        if (crowdContext.getCustomLabel() == null) {
            return;
        }
        log.info("filterUtmSource customLabel:{}", crowdContext.getCustomLabel());
        if (!"source_is_not_marking_cust".equals(crowdContext.getCustomLabel().getDataWarehouseField())) {
            return;
        }
        if (!(crowdContext.getCustomLabel().getLabelValue() instanceof FixedRadioOption)) {
            return;
        }
        FixedRadioOption fixedRadioOption = (FixedRadioOption) crowdContext.getCustomLabel().getLabelValue();
        if (!fixedRadioOption.getResult()) {
            return;
        }
        log.info("filterUtmSource start");
        Iterator<CrowdWereHouse> iterator = crowdWereHouseList.iterator();
        List<CrowdUtmDecisionRecordEntity> list = new ArrayList<>();
        while (iterator.hasNext()) {
            CrowdWereHouse crowdWereHouse = iterator.next();

            CrowdUtmDecisionRecordEntity entity = new CrowdUtmDecisionRecordEntity();
            entity.setCrowdId(crowdContext.getCrowdPack().getId());
            entity.setCrowdExecLogId(crowdContext.getCrowdExecLog().getId());
            entity.setApp(crowdWereHouse.getApp());
            entity.setAppUserId(crowdWereHouse.getAppUserId());
            entity.setMobileUtmSource(crowdWereHouse.getMobileUtmSource());
            entity.setHistoryBorrowUtmSource(crowdWereHouse.getHistoryBorrowUtmSource());
            entity.setLastLoanSuccessUtmSource(crowdWereHouse.getLastLoanSuccessUtmSource());
            entity.setRegisterTime(crowdWereHouse.getRegisterTime());
            String traceId = "";
            if (Tracing.current().currentTraceContext() != null && Tracing.current().currentTraceContext().get() != null) {
                traceId = Tracing.current().currentTraceContext().get().traceIdString();
            }
            entity.setTraceId(traceId);
            entity.setTableNameNo(LocalDateTimeUtil.format(LocalDate.now(), "yyyyMM"));
            List<UtmResult> resultList = new ArrayList<>();
            if (!Constants.XYF01.equals(crowdWereHouse.getApp())) {
                filterXyf01UtmSource(resultList, entity, crowdWereHouse, crowdContext.getCrowdPack().getBusinessType());
            } else {
                filterUtmSource(resultList, entity, crowdWereHouse, crowdContext.getCrowdPack().getBusinessType());
            }
            if (resultList.size() > 0) {
                boolean result = resultList.stream().allMatch(a -> a.getHit());
                entity.setUtmResultList(resultList);
                entity.setDecisionResult(result ? 1 : 0);
                log.info("CrowdUtmDecisionRecordEntity:{}", entity);
                if (!result) {
                    list.add(entity);
                    iterator.remove();
                }
            }
        }
        // 保存人群包渠道决策结果
        if (list.size() > 0) {
            crowdUtmDecisionRecordDao.saveCrowdUtmDecisionRecord(list);
        }
    }

    private void filterUtmSource(List<UtmResult> resultList, CrowdUtmDecisionRecordEntity entity, CrowdWereHouse crowdWereHouse, String utmType) {
        try {
            resultList.addAll(utmSourceService.filterBatchUtmSource(crowdWereHouse.getMobileUtmSource(), UtmSourceTypeEnum.REGISTER, crowdWereHouse.getRegisterTime(), utmType));
            boolean mobileUtmFlag = resultList.stream().allMatch(a -> a.getHit());
            if (!mobileUtmFlag) {
                entity.setFailCode(DecisionResultEnum.MOBILE_UMT_FAIL.getFailCode());
                entity.setFailReason(DecisionResultEnum.MOBILE_UMT_FAIL.getFailReason());
            }
            if (mobileUtmFlag) {
                List<UtmResult> borrowList = utmSourceService.filterBatchUtmSource(crowdWereHouse.getHistoryBorrowUtmSource(), UtmSourceTypeEnum.ENTRY, null, utmType);
                boolean borrowUtmFlag = borrowList.stream().allMatch(a -> a.getHit());
                if (!borrowUtmFlag) {
                    entity.setFailCode(DecisionResultEnum.HISTORY_BORROW_UMT_FAIL.getFailCode());
                    entity.setFailReason(DecisionResultEnum.HISTORY_BORROW_UMT_FAIL.getFailReason());
                }
                resultList.addAll(borrowList);
                if (borrowUtmFlag) {
                    List<UtmResult> loanList = utmSourceService.filterBatchUtmSource(crowdWereHouse.getLastLoanSuccessUtmSource(), UtmSourceTypeEnum.LOAN, null, utmType);
                    boolean loanUtmFlag = loanList.stream().allMatch(a -> a.getHit());
                    if (!loanUtmFlag) {
                        entity.setFailCode(DecisionResultEnum.LAST_LOAN_SUCCESS_UMT_FAIL.getFailCode());
                        entity.setFailReason(DecisionResultEnum.LAST_LOAN_SUCCESS_UMT_FAIL.getFailReason());
                    }
                    resultList.addAll(loanList);
                }
            }
        } catch (InterfaceException ie) {
            UtmResult utmResult = new UtmResult();
            utmResult.setHit(false);
            utmResult.setNewMarketingType(ie.getMessage());
            resultList.add(utmResult);
        }
    }

    private void filterXyf01UtmSource(List<UtmResult> resultList, CrowdUtmDecisionRecordEntity entity, CrowdWereHouse crowdWereHouse, String utmType) {
        CrowdWereHouse crowdUtmInfo = adsUserLabelDetailInfoDfRepository.queryUtmByMobileAndApp(crowdWereHouse.getMobile(), Constants.XYF01);
        if (crowdUtmInfo == null) {
            entity.setDecisionResult(1);
            return;
        }
        crowdUtmInfo.setRegisterTime(crowdWereHouse.getRegisterTime());
        filterUtmSource(resultList, entity, crowdUtmInfo, utmType);

    }

    @Transactional(transactionManager = "transactionManager", propagation = Propagation.REQUIRES_NEW)
    protected void beginExecute(CrowdContext crowdContext) {
        CrowdExecLogDo execLog = CrowdExecLogDo.beginExecute(crowdContext.getCrowdPack().getId(), crowdContext.getCrowdPack().getUpdatedOp());
        crowdExecLogRepository.insert(execLog);
        crowdExecSnapshotRepository.insert(CrowdExecSnapshotDo.beginExecute(crowdContext.getCrowdPack().getId(), execLog.getId(), JSON.toJSONString(crowdContext)));
        crowdContext.setCrowdExecLog(execLog);
        crowdContext.getCrowdPack().execExecuting();
        cacheCrowdPackService.updateById(crowdContext.getCrowdPack());
    }

    @Transactional(transactionManager = "transactionManager", propagation = Propagation.REQUIRES_NEW)
    protected void failedExecute(CrowdContext crowdContext) {
        crowdContext.getCrowdPack().execFailed();
        cacheCrowdPackService.updateById(crowdContext.getCrowdPack());
        crowdContext.getCrowdExecLog().execFailed(crowdContext.getFailReason());
        // 失败重试
        List<CrowdExecLogDo> failTodayExecLogs = crowdExecLogRepository.findTodayExecLogsByCrowdIdAndStatus(crowdContext.getCrowdExecLog().getCrowdId(), CrowdExecResultEnum.FAIL);
        if (!CollectionUtils.isEmpty(failTodayExecLogs)) {
            Integer retryJobId = failTodayExecLogs.get(0).getRetryJobId();
            if (failTodayExecLogs.size() >= 2) {
                if (retryJobId != null && retryJobId > 0) {
                    xxlJobAdminClient.removeJob(retryJobId);
                }
            } else {
                XxlJobDto jobDto = crowdContext.getCrowdExecLog().failToRetry(xxlJobAdminClient.timeCron(LocalDateTime.now().plus(5, ChronoUnit.MINUTES)).asString(), crowdContext, failTodayExecLogs.size() + 1);
                jobDto.setId(retryJobId);
                xxlJobAdminClient.updateJob(jobDto);
                xxlJobAdminClient.startJob(retryJobId);
                crowdContext.getCrowdExecLog().setRetryJobId(retryJobId);
            }
        } else {
            XxlJobDto jobDto = crowdContext.getCrowdExecLog().failToRetry(xxlJobAdminClient.timeCron(LocalDateTime.now().plus(5, ChronoUnit.MINUTES)).asString(), crowdContext, failTodayExecLogs.size() + 1);
            int jobId = xxlJobAdminClient.addJob(jobDto);
            xxlJobAdminClient.startJob(jobId);
            crowdContext.getCrowdExecLog().setRetryJobId(jobId);
        }
        cacheCrowdExecLogService.updateById(crowdContext.getCrowdExecLog());
    }


    @Transactional(transactionManager = "transactionManager", propagation = Propagation.REQUIRES_NEW)
    protected void failedExecuteInterrupted(CrowdContext crowdContext) {
        crowdContext.getCrowdExecLog().execFailed(crowdContext.getFailReason());
        crowdContext.getCrowdPack().execFailed();
        cacheCrowdExecLogService.updateById(crowdContext.getCrowdExecLog());
        cacheCrowdPackService.updateById(crowdContext.getCrowdPack());
    }


    @Transactional(transactionManager = "transactionManager", propagation = Propagation.REQUIRES_NEW)
    protected void successExecute(CrowdContext crowdContext) {
        int personNum = crowdContext.getPersonNum().get();
        crowdContext.getCrowdPack().execSuccess(personNum);
        crowdContext.getCrowdExecLog().execSuccess(personNum);
        cacheCrowdExecLogService.updateById(crowdContext.getCrowdExecLog());
        cacheCrowdPackService.updateById(crowdContext.getCrowdPack());
    }

    private void cleanLatestRefreshTimeKey(Long crowdId) {
        try {
            String refreshTime = redisUtils.get(RedisKeyUtils.genCrowdLatestRefreshTimeKey(crowdId));
            if (StringUtils.isNotBlank(refreshTime)) {
                LocalDateTime latestRefreshTime = LocalDateTimeUtil.parse(refreshTime, TimeFormat.DATE_TIME);
                if (Duration.between(latestRefreshTime, LocalDateTime.now()).toMinutes() <= crowdConfig.getCrowdRefreshTimeoutLimit()) {
                    redisUtils.delete(RedisKeyUtils.genCrowdLatestRefreshTimeKey(crowdId));
                }
            }
        } catch (Exception e) {
            log.warn("cleanLatestRefreshTimeKey error, crowdId:{}", crowdId, e);
        }
    }

    // 已复制同样逻辑到OSS文件读取服务中
    void filterNewRandom(CrowdContext crowdContext, List<CrowdWereHouse> crowdWereHouseList) {
        List<String> includeLabelList = crowdContext.getIncludeCustomLabel();
        List<String> excludeLabelList = crowdContext.getExcludeCustomLabel();
        if (!CollectionUtils.isEmpty(includeLabelList)) {
            includeLabelList.forEach(item -> randomNumService.crowdFilterNewRandom(crowdContext, item, crowdWereHouseList, CrowdLabelEnum.INCLUDE_LABEL.getCode()));
        }

        if (!CollectionUtils.isEmpty(excludeLabelList)) {
            excludeLabelList.forEach(item -> randomNumService.crowdFilterNewRandom(crowdContext, item, crowdWereHouseList, CrowdLabelEnum.EXCLUDE_LABEL.getCode()));

        }
    }
}
