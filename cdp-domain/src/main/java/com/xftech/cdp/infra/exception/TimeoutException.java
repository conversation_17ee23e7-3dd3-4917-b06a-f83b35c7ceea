package com.xftech.cdp.infra.exception;

import lombok.Getter;
import lombok.Setter;

/**
 * 超时异常
 */
@Getter
@Setter
public class TimeoutException extends BizException {

    private int code;

    public TimeoutException(String message) {
        super(message);
    }

    public TimeoutException(int code, String message) {
        super(message);
        this.code = code;
    }

    public TimeoutException(String message, Throwable cause) {
        super(message, cause);
    }

    public TimeoutException(int code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }

    public TimeoutException(Throwable cause) {
        super(cause);
    }

}
