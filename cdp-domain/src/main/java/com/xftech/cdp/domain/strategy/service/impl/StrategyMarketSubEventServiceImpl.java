package com.xftech.cdp.domain.strategy.service.impl;

import com.xftech.cdp.domain.strategy.service.StrategyMarketSubEventService;
import com.xftech.cdp.domain.cache.CacheStrategyMarketSubEventService;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketSubEventDo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @<NAME_EMAIL>
 */
@Slf4j
@Service
public class StrategyMarketSubEventServiceImpl implements StrategyMarketSubEventService {

//    @Autowired
//    private StrategyMarketSubEventRepository strategyMarketSubEventRepository;
    @Autowired
    private CacheStrategyMarketSubEventService cacheStrategyMarketSubEventService;

    @Override
    public Map<Integer, List<StrategyMarketSubEventDo>> getByEventId(Long eventId) {
        List<StrategyMarketSubEventDo> strategyMarketSubEventDoList = cacheStrategyMarketSubEventService.getByEventId(eventId);
        return strategyMarketSubEventDoList
                .stream()
                .collect(Collectors.groupingBy(StrategyMarketSubEventDo::getEventType));
    }

    @Override
    public Map<Integer, List<StrategyMarketSubEventDo>> getByStrategyId(Long strategyId) {
        List<StrategyMarketSubEventDo> strategyMarketSubEventDoList = cacheStrategyMarketSubEventService.getByStrategyId(strategyId);
        return strategyMarketSubEventDoList
                .stream()
                .collect(Collectors.groupingBy(StrategyMarketSubEventDo::getEventType));
    }
}
