<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="userSendCounterMapper">
  <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.strategy.po.UserSendCounterDo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="app_user_id" jdbcType="BIGINT" property="appUserId" />
    <result column="strategy_id" jdbcType="BIGINT" property="strategyId" />
    <result column="market_channel" jdbcType="SMALLINT" property="marketChannel" />
    <result column="date_value" jdbcType="INTEGER" property="dateValue" />
    <result column="sum_value" jdbcType="INTEGER" property="sumValue" />
    <result column="failed_value" jdbcType="INTEGER" property="failedValue" />
    <result column="d_flag" jdbcType="SMALLINT" property="dFlag" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from user_send_counter
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.UserSendCounterDo">
    <!--
         WARNING - @mbg.generated
         This element is automatically generated by MyBatis Generator, do not modify.
       -->
    insert into user_send_counter
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="appUserId != null">
        app_user_id,
      </if>
      <if test="strategyId != null">
        strategy_id,
      </if>
      <if test="marketChannel != null">
        market_channel,
      </if>
      <if test="dateValue != null">
        date_value,
      </if>
      <if test="sumValue != null">
        sum_value,
      </if>
      <if test="failedValue != null">
        failed_value,
      </if>
      <if test="dFlag != null">
        d_flag,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="updatedTime != null">
        updated_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="appUserId != null">
        #{appUserId,jdbcType=BIGINT},
      </if>
      <if test="strategyId != null">
        #{strategyId,jdbcType=BIGINT},
      </if>
      <if test="marketChannel != null">
        #{marketChannel,jdbcType=SMALLINT},
      </if>
      <if test="dateValue != null">
        #{dateValue,jdbcType=INTEGER},
      </if>
      <if test="sumValue != null">
        #{sumValue,jdbcType=INTEGER},
      </if>
      <if test="failedValue != null">
        #{failedValue,jdbcType=INTEGER},
      </if>
      <if test="dFlag != null">
        #{dFlag,jdbcType=SMALLINT},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        #{updatedTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select id, app_user_id, strategy_id, market_channel, date_value, sum_value,
    failed_value, d_flag, created_time, updated_time
    from user_send_counter
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="sumSucValues" resultType="java.lang.Integer">
    SELECT (sum_value - failed_value)
    FROM user_send_counter
    where app_user_id = #{userId}
      and strategy_id = #{strategyId}
      and market_channel = #{marketChannel}
      and date_value between #{startDay} and #{endDay} and d_flag = 0
  </select>

  <update id="incrementSum">
    update user_send_counter
    set sum_value = sum_value + #{count}
    where app_user_id = #{userId}
      and strategy_id = #{strategyId}
      and market_channel = #{marketChannel}
      and date_value = #{dateValue} and d_flag = 0
  </update>

  <update id="incrementFailed">
    update user_send_counter
    set failed_value = failed_value + 1
    where app_user_id = #{userId}
      and strategy_id = #{strategyId}
      and market_channel = #{marketChannel}
      and date_value = #{dateValue} and d_flag = 0
  </update>

</mapper>