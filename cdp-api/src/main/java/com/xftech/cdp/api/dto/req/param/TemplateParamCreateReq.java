package com.xftech.cdp.api.dto.req.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;

@Data
public class TemplateParamCreateReq {

    @ApiModelProperty(value = "参数id", required = true)
    private Long id;

    @ApiModelProperty(value = "参数名称", required = true)
    @Size(max = 20,message = "参数名称不能超过20个字符")
    private String name;

    @ApiModelProperty(value = "参数备注")
    @Size(max = 100,message = "参数备注不能超过100个字符")
    private String description;

    @ApiModelProperty(value = "key值", required = true)
    @Size(max = 100,message = "key值不能超过100个字符")
    private String paramKey;

    @ApiModelProperty(value = "需求方", required = true)
    @Size(max = 20,message = "参数名称不能超过20个字符")
    private String demandSide;
}





