/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.feign.interceptor;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version $ DpsFeignClientInterceptor, v 0.1 2024/6/14 15:58 lingang.han Exp $
 */

/**
 * dps-service 拦截器
 */
@Slf4j
public class DpsFeignClientInterceptor implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate requestTemplate) {
        //设置请求头
        String requestId = UUID.randomUUID().toString();
        requestTemplate.header("requestId", requestId);
        requestTemplate.header("appId", "xyf-cdp");
        requestTemplate.header("requestTimestamp", String.valueOf(Instant.now().toEpochMilli()));
    }

}