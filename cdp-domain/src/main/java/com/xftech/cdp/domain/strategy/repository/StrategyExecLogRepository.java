package com.xftech.cdp.domain.strategy.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.base.database.Page;
import com.xftech.cdp.api.dto.req.MonitorListReq;
import com.xftech.cdp.domain.strategy.model.enums.StrategyExecStatusEnum;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyExecLogDo;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 策略执行日志表操作
 *
 * @<NAME_EMAIL>
 * @date 2023/2/25 15:55
 */

@Component
public class StrategyExecLogRepository {

    /**
     * 根据策略id分页查询执行日志
     *
     * @param monitorListReq 策略id、分页请求参数
     * @return 该策略的执行日志列表  根据执行时间降序排序
     */
    public Page<StrategyExecLogDo> selectPage(MonitorListReq monitorListReq) {
        return DBUtil.selectPage("strategyExecLog.selectByStrategyId", monitorListReq, monitorListReq.getBeginNum(), monitorListReq.getSize());
    }

    /**
     * 根据策略id、多个渠道类型、执行时间查询执行日记
     *
     * @param strategyId 策略ID
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @return 执行日志集合
     */
    public List<StrategyExecLogDo> selectAllByStrategyIdAndTime(Long strategyId, LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> param = new HashMap<>();
        param.put("strategyId", strategyId);
        param.put("startTime", startTime);
        param.put("endTime", endTime);
        return DBUtil.selectList("strategyExecLog.selectAllByStrategyIdAndTime", param);

    }

    /**
     * 根据策略id查询执行日志
     *
     * @param strategyId 策略id、
     * @return 该策略的所有执行日志列表 根据执行时间降序排序
     */
    public List<StrategyExecLogDo> selectByStrategyIdAndChannel(Long strategyId, Integer strategyMarketChannel) {
        Map<String, Object> param = new HashMap<>();
        param.put("strategyId", strategyId);
        param.put("strategyMarketChannel", strategyMarketChannel);
        return DBUtil.selectList("strategyExecLog.selectByStrategyIdAndChannel", param);

    }

    /**
     * 根据策略id、渠道类型、执行时间、执行状态查询策略执行日记
     *
     * @param strategyId      策略id
     * @param marketChannelId 渠道id
     * @param execDate        执行日期
     * @param execStatusEnum  执行状态
     * @return 该策略下某个渠道某天执行状态为{execStatus}的执行日志列表
     */
    public List<StrategyExecLogDo> selectByStrategyIdAndChannelAndExecStatus(Long strategyId, Long marketChannelId, LocalDate execDate, StrategyExecStatusEnum execStatusEnum) {
        Map<String, Object> param = new HashMap<>();
        param.put("strategyId", strategyId);
        param.put("marketChannelId", marketChannelId);
        param.put("execDate", execDate);
        param.put("execStatus", execStatusEnum.getCode());
        return DBUtil.selectList("strategyExecLog.selectByStrategyIdAndChannelAndExecStatus", param);

    }

    /**
     * 根据策略id、执行时间区间、执行状态查询执行日志 (渠道ID分组)
     *
     * @param strategyId     策略id
     * @param validityBegin  执行时间-开始值
     * @param validityEnd    执行时间-结束值
     * @param execStatusEnum 执行状态
     * @return 该策略下某个执行时间段，执行状态为{execStatus}的日志列表
     */
    public List<StrategyExecLogDo> selectByStrategyIdAndExecTime(Long strategyId, LocalDateTime validityBegin, LocalDateTime validityEnd, StrategyExecStatusEnum execStatusEnum) {
        Map<String, Object> param = new HashMap<>();
        param.put("strategyId", strategyId);
        param.put("validityBegin", validityBegin);
        param.put("validityEnd", validityEnd);
        param.put("execStatus", Objects.nonNull(execStatusEnum) ? execStatusEnum.getCode() : null);
        return DBUtil.selectList("strategyExecLog.selectByStrategyIdAndExecTime", param);

    }

    /**
     * 根据策略id、执行时间区间、执行状态查询执行日志 （不分组）
     *
     * @param strategyId     策略id
     * @param validityBegin  执行时间-开始值
     * @param validityEnd    执行时间-结束值
     * @param execStatusEnum 执行状态
     * @return 该策略下某个执行时间段，执行状态为{execStatus}的日志列表
     */
    public List<StrategyExecLogDo> selectByStrategyIdAndExecTimeUngrouped(Long strategyId, LocalDateTime validityBegin, LocalDateTime validityEnd, StrategyExecStatusEnum execStatusEnum) {
        Map<String, Object> param = new HashMap<>();
        param.put("strategyId", strategyId);
        param.put("validityBegin", validityBegin);
        param.put("validityEnd", validityEnd);
        param.put("execStatus", Objects.nonNull(execStatusEnum) ? execStatusEnum.getCode() : null);
        return DBUtil.selectList("strategyExecLog.selectByStrategyIdAndExecTimeUngrouped", param);

    }

    public List<StrategyExecLogDo> selectByGroupIdAndExecTime(Long strategyGroupId, LocalDateTime validityBegin, LocalDateTime validityEnd, StrategyExecStatusEnum execStatusEnum) {
        Map<String, Object> param = new HashMap<>();
        param.put("groupId", strategyGroupId);
        param.put("validityBegin", validityBegin);
        param.put("validityEnd", validityEnd);
        param.put("execStatus", Objects.nonNull(execStatusEnum) ? execStatusEnum.getCode() : null);
        return DBUtil.selectList("strategyExecLog.selectByGroupIdAndExecTime", param);

    }

    public List<StrategyExecLogDo> selectEventExecutingRecord(LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> param = new HashMap<>();
        param.put("validityBegin", startTime);
        param.put("validityEnd", endTime);
        return DBUtil.selectList("strategyExecLog.selectEventExecutingRecord", param);
    }

    /**
     * 根据策略id查询最新一条执行日志
     *
     * @param strategyId 策略id
     * @return 该策略下最新一条执行日志
     */
    public StrategyExecLogDo selectLatestStatusByStrategyId(Long strategyId) {
        return DBUtil.selectOne("strategyExecLog.selectLatestStatusByStrategyId", strategyId);
    }

    /**
     * 根据策略id查询执行状态为2-失败的执行日志（每个渠道最新一条失败的记录）
     *
     * @param strategyId 策略id
     * @return 该策略下所有渠道最新一条执行失败的记录
     */
    public List<StrategyExecLogDo> selectRetryList(Long strategyId) {
        return DBUtil.selectList("strategyExecLog.selectRetryList", strategyId);

    }

    /**
     * 根据执行日志id（主键）查询执行日志
     *
     * @param strategyExecLogId 执行日志id
     * @return 该id对应的一条记录
     */
    public StrategyExecLogDo selectById(Long strategyExecLogId) {
        return DBUtil.selectOne("strategyExecLog.selectByPrimaryKey", strategyExecLogId);

    }

    /**
     * 根据执行日志id（主键）查询更新日志
     *
     * @param updateStrategyExecLog 执行日志对象
     */
    public void updateById(StrategyExecLogDo updateStrategyExecLog) {
        updateStrategyExecLog.setUpdatedTime(LocalDateTime.now());
        DBUtil.update("strategyExecLog.updateByPrimaryKeySelective", updateStrategyExecLog);

    }

    public void batchUpdate(List<StrategyExecLogDo> list) {
        DBUtil.updateBatchWithoutTx("strategyExecLog.updateByPrimaryKeySelective", list);
    }

    /**
     * 插入一条执行日志记录
     *
     * @param strategyExecLogDo 执行日志DO对象
     */
    public void insert(StrategyExecLogDo strategyExecLogDo) {
        DBUtil.insert("strategyExecLog.insertSelective", strategyExecLogDo);
    }

    /**
     * 根据策略id分页查询执行日志
     *
     * @param monitorListReq 策略id、分页请求参数
     * @return 该策略的执行日志列表  根据执行时间降序排序
     */
    public Page<StrategyExecLogDo> selectPageByChannel(MonitorListReq monitorListReq) {
        return DBUtil.selectPage("strategyExecLog.selectByStrategyIdByChannel", monitorListReq, monitorListReq.getBeginNum(), monitorListReq.getSize());
    }

    public StrategyExecLogDo selectByGroupIdAndChannelAndDate(Long groupId, Integer marketChannel, LocalDate date) {
        Map<String, Object> param = new HashMap<>();
        param.put("groupId", groupId);
        param.put("marketChannel", marketChannel);
        param.put("date", date);
        return DBUtil.selectOne("strategyExecLog.selectByGroupIdAndChannelAndDate", param);
    }

    public StrategyExecLogDo selectByStrategyIdAndChannelAndDate(Long strategyId, Integer marketChannel, LocalDate date) {
        Map<String, Object> param = new HashMap<>();
        param.put("strategyId", strategyId);
        param.put("marketChannel", marketChannel);
        param.put("date", date);
        return DBUtil.selectOne("strategyExecLog.selectByStrategyIdAndChannelAndDate", param);
    }

    public void batchInsert(List<StrategyExecLogDo> strategyExecLogDoList) {
        DBUtil.insertBatch("strategyExecLog.insertSelective", strategyExecLogDoList);
    }

    public List<StrategyExecLogDo> getByStrategyIdAndExecTime(Long strategyId, LocalDate date) {
        Map<String, Object> param = new HashMap<>();
        param.put("strategyId", strategyId);
        param.put("date", date);
        return DBUtil.selectList("strategyExecLog.getByStrategyIdAndExecTime", param);
    }

    public List<StrategyExecLogDo> selectByStrategyIdsAndExecTime(List<Long> strategyIds, LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> param = new HashMap<>();
        param.put("strategyIds", strategyIds);
        param.put("startTime", startTime);
        param.put("endTime", endTime);
        return DBUtil.selectList("strategyExecLog.selectByStrategyIdsAndExecTime", param);
    }
}
