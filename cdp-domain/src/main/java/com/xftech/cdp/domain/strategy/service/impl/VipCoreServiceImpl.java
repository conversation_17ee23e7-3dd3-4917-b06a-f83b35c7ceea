/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.service.impl;

import com.xftech.cdp.feign.VipCoreFeignClient;
import com.xftech.cdp.feign.model.requset.VipCoreRenewStatusBatchRequest;
import com.xftech.cdp.feign.model.response.VipCoreResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version $ VipCoreServiceImpl, v 0.1 2024/6/13 19:36 lingang.han Exp $
 */

@Service
@Slf4j
public class VipCoreServiceImpl {
    @Autowired
    private VipCoreFeignClient vipCoreFeignClient;

    public Map<Long, Boolean> getRenewStatusBatch(List<Long> userIdList) {

        if (CollectionUtils.isEmpty(userIdList)) {
            return null;
        }
        VipCoreRenewStatusBatchRequest objectVipCoreBaseRequest = new VipCoreRenewStatusBatchRequest();
        objectVipCoreBaseRequest.setUserNos(userIdList);
        VipCoreResponse<Map<Long, Boolean>> mapVipCoreBaseRequest = vipCoreFeignClient.renewStatusBatch(objectVipCoreBaseRequest);
        log.info("vipcore url:/vip/renewStatusBatch req:{},resp:{}", objectVipCoreBaseRequest, mapVipCoreBaseRequest);
        if (mapVipCoreBaseRequest == null || !mapVipCoreBaseRequest.isSuccess()) {
            return null;
        }
        return mapVipCoreBaseRequest.getData();
    }

}