package com.xftech.cdp.infra.pulsar.service.impl;

import com.xftech.cdp.domain.strategy.service.dispatch.event.StrategyEventDispatchService;
import com.xftech.cdp.infra.pulsar.service.CdpPulsarService;
import com.xftech.cdp.infra.utils.JsonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


@Slf4j
@Service("loanFinalFailedService")
@AllArgsConstructor
public class LoanFinalFailedServiceImpl implements CdpPulsarService {

    private StrategyEventDispatchService strategyEventDispatchService;
    @Override
    public void consumer(String messageId, String eventMessage) {
        toBizEventMessageVO(eventMessage).ifPresent(eventMsg -> {
            log.info("LoanFinalFailed eventMessage toBizEventMessageVO={}", JsonUtil.toJson(eventMsg));
            strategyEventDispatchService.prescreen(messageId, eventMsg);
        });
    }
}
