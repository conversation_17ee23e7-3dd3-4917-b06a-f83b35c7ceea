package com.xftech.cdp.infra.client.dingtalk;

import com.alibaba.fastjson.JSON;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiRobotSendRequest;
import com.dingtalk.api.response.OapiRobotSendResponse;
import com.taobao.api.ApiException;
import com.xftech.cdp.infra.client.dingtalk.config.DingTalkConfig;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPackDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;
import com.xftech.cdp.infra.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
public class DingTalkUtil {

    public static void sendTextToDingTalk(String url, String content) {
        log.info("发送钉钉消息,url:{},内容:content:{}", url, content);
        DingTalkClient client = new DefaultDingTalkClient(url);
        OapiRobotSendRequest request = new OapiRobotSendRequest();
        request.setMsgtype("text");
        OapiRobotSendRequest.Text text = new OapiRobotSendRequest.Text();
        text.setContent(content);
        request.setText(text);

        try {
            client.execute(request);
        } catch (ApiException ae) {
            log.error("发送钉钉消息异常", ae);
        }

    }

    public static String urlSignProcess(String url, String secret) {
        try {
            Long timestamp = System.currentTimeMillis();
            String stringToSign = timestamp + "\n" + secret;
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(secret.getBytes("UTF-8"), "HmacSHA256"));
            byte[] signData = mac.doFinal(stringToSign.getBytes("UTF-8"));
            String sign = URLEncoder.encode(new String(Base64.encodeBase64(signData)), "UTF-8");
            System.out.println(sign);
            return url + "&timestamp=" + timestamp + "&sign=" + sign;
        } catch (Exception e) {
            log.error("DingTalkUtil urlSignProcess error", e);
        }
        return null;
    }

    /**
     * 发送普通文本消息
     *
     * @param content   文本消息
     * @param atMobiles 指定@ 联系人
     * @param isAtAll   是否@ 全部联系人
     * @return OapiRobotSendResponse
     */
    public static OapiRobotSendResponse sendTextToDingTalk(String url, String content, List<String> atMobiles, boolean isAtAll) {
        log.info("钉钉告警内容：{}", JSON.toJSONString(content));
        if (StringUtils.isBlank(content)) {
            return null;
        }

        OapiRobotSendRequest.Text text = new OapiRobotSendRequest.Text();
        text.setContent(content);
        OapiRobotSendRequest request = new OapiRobotSendRequest();
        if (!CollectionUtils.isEmpty(atMobiles)) {
            // 发送消息并@ 以下手机号联系人
            OapiRobotSendRequest.At at = new OapiRobotSendRequest.At();
            at.setAtMobiles(atMobiles);
            at.setIsAtAll(isAtAll);
            request.setAt(at);
        }
        request.setMsgtype("text");
        request.setText(text);

        OapiRobotSendResponse response = new OapiRobotSendResponse();
        try {
            response = new DefaultDingTalkClient(url).execute(request);
        } catch (Exception e) {
            log.error("钉钉告警异常", e);
        }
        log.info("钉钉告警发送结果，errcode：{}，errmsg：{}，isSuccess：{}", response.getErrcode(), response.getErrmsg(), response.isSuccess());
        return response;
    }

    public static void dingTalk(DingTalkConfig dingTalkConfig, StrategyDo strategyDo, Long appUserId, String tips) {
        StringBuilder content = new StringBuilder();
        content.append(String.format("策略编号：%s", strategyDo.getId())).append("\n");
        content.append(String.format("策略名称：%s", strategyDo.getName())).append("\n");
        if (Objects.nonNull(appUserId)) {
            content.append(String.format("userid：%s", appUserId)).append("\n");
        }
        content.append(tips);
        log.info("钉钉预警:{}", tips);
        DingTalkUtil.sendTextToDingTalk(dingTalkConfig.getAdsAlarmUrl(), content.toString(), dingTalkConfig.atMobileAdsList(), false);
    }

    public static void dingTalkStrategyBatch(DingTalkConfig dingTalkConfig, List<StrategyDo> strategyDoList, Long appUserId, String tips) {
        StringBuilder content = new StringBuilder();
        List<Long> idList = strategyDoList.stream().mapToLong(StrategyDo::getId).boxed().collect(Collectors.toList());
        List<String> nameList = strategyDoList.stream().map(StrategyDo::getName).collect(Collectors.toList());
        content.append(String.format("策略编号：%s", JsonUtil.toJson(idList))).append("\n");
        content.append(String.format("策略名称：%s", JsonUtil.toJson(nameList))).append("\n");
        if (Objects.nonNull(appUserId)) {
            content.append(String.format("userid：%s", appUserId)).append("\n");
        }
        content.append(tips);
        log.info("钉钉预警:{}", tips);
        DingTalkUtil.sendTextToDingTalk(dingTalkConfig.getAdsAlarmUrl(), content.toString(), dingTalkConfig.atMobileAdsList(), false);
    }

    public static void dingTalk(DingTalkConfig dingTalkConfig, StrategyDo strategyDo, Long appUserId, List<String> atMobiles) {
        StringBuilder content = new StringBuilder();
        content.append(String.format("策略编号：%s", strategyDo.getId())).append("\n");
        content.append(String.format("策略名称：%s", strategyDo.getName())).append("\n");
        content.append(String.format("uid：%s", appUserId)).append("\n");
        content.append("此用户实时短信参数查询值为空，不会下发");
        log.info("钉钉预警:此用户实时短信参数查询值为空，不会下发");
        DingTalkUtil.sendTextToDingTalk(dingTalkConfig.getAdsAlarmUrl(), content.toString(), dingTalkConfig.atMobileAdsList(), false);
    }

    public static void dingTalk(DingTalkConfig dingTalkConfig, StrategyDo strategyDo, String sourceType, String utmSource) {
        StringBuilder content = new StringBuilder();
        content.append(String.format("策略编号：%s", strategyDo.getId())).append("\n");
        content.append(String.format("策略名称：%s", strategyDo.getName())).append("\n");
        content.append(String.format("报警渠道：%s", sourceType)).append("\n");
        content.append(String.format("渠道：%s", utmSource)).append("\n");
        content.append("查询商户后台接口异常请立即排查");
        log.info("钉钉预警:查询商户后台接口异常请立即排查");
        DingTalkUtil.sendTextToDingTalk(dingTalkConfig.getAlarmUrl(), content.toString(), null, false);
    }

    public static void dingTalkBatch(DingTalkConfig dingTalkConfig, Collection<StrategyDo> strategyDoList, String sourceType, String utmSource) {
        StringBuilder content = new StringBuilder();
        List<Long> idList = strategyDoList.stream().mapToLong(StrategyDo::getId).boxed().collect(Collectors.toList());
        List<String> nameList = strategyDoList.stream().map(StrategyDo::getName).collect(Collectors.toList());
        content.append(String.format("策略编号：%s", JsonUtil.toJson(idList))).append("\n");
        content.append(String.format("策略名称：%s", JsonUtil.toJson(nameList))).append("\n");
        content.append(String.format("报警渠道：%s", sourceType)).append("\n");
        content.append(String.format("渠道：%s", utmSource)).append("\n");
        content.append("查询商户后台接口异常请立即排查");
        log.info("钉钉预警:查询商户后台接口异常请立即排查");
        DingTalkUtil.sendTextToDingTalk(dingTalkConfig.getAlarmUrl(), content.toString(), null, false);
    }

    public static void labelDingTalk(DingTalkConfig dingTalkConfig, String label, Long strategyId, Long appUserId, String labelInfo) {
        String content = "标签结果异常" + "\n" +
                String.format("标签(%s)结果异常", label) + "\n" +
                String.format("策略编号：%s", strategyId) + "\n" +
                String.format("appUserId：%s", appUserId) + "\n" +
                String.format("标签信息：%s", labelInfo) + "\n" +
                "实时标签返回结果异常，无需解决，请知晓和关注跟进！" + "\n";
        log.info(String.format("钉钉消息内容：%s", content));
        DingTalkUtil.sendTextToDingTalk(dingTalkConfig.getAlarmUrl(), content, null, false);
    }

    public static void dingTalk(String url, Long crowdId, String crowdName, List<String> atMobiles,String otherMsg) {
        StringBuilder content = new StringBuilder();
        content.append(String.format("人群包编号：%s", crowdId)).append("\n");
        content.append(String.format("人群包名称：%s", crowdName)).append("\n");
        content.append("人群刷新失败，");
        content.append(otherMsg).append("请尽快排查并操作手动刷新!");
        log.info("钉钉预警:人群刷新失败，请尽快排查并操作手动刷新!");
        DingTalkUtil.sendTextToDingTalk(url, content.toString(), atMobiles, false);
    }

    public static void dingTalk(String url, Long strategyId, String strategyName, String message, List<String> atMobiles) {
        StringBuilder content = new StringBuilder();
        content.append(String.format("策略编号：%s", strategyId)).append("\n");
        content.append(String.format("策略名称：%s", strategyName)).append("\n");
        content.append(message);
        log.info("钉钉预警:{}", message);
        DingTalkUtil.sendTextToDingTalk(url, content.toString(), atMobiles, false);
    }

    public static void dingTalk(String url, StrategyDo strategyDo) {
        StringBuilder content = new StringBuilder();
        content.append(String.format("策略编号：%s", strategyDo.getId())).append("\n");
        content.append(String.format("策略名称：%s", strategyDo.getName())).append("\n");
        content.append("查询实时标签接口异常请立即排查");
        log.info("钉钉预警:查询实时标签接口异常请立即排查");
        DingTalkUtil.sendTextToDingTalk(url, content.toString(), null, false);
    }

    public static void dingTalk(DingTalkConfig dingTalkConfig, CrowdPackDo crowdPackDo, String msg, List<String> atMobiles) {
        StringBuilder content = new StringBuilder();
        content.append(String.format("人群包编号：%s", crowdPackDo.getId())).append("\n");
        content.append(String.format("人群包名称：%s", crowdPackDo.getCrowdName())).append("\n");
        content.append(msg);
        DingTalkUtil.sendTextToDingTalk(dingTalkConfig.getAlarmUrl(), content.toString(), atMobiles, false);
    }

    public static void dingTalk(String url, String msg, List<String> atMobiles) {
        DingTalkUtil.sendTextToDingTalk(url, msg, atMobiles, false);
    }


}