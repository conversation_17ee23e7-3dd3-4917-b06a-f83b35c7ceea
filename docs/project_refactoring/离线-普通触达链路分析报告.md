# 离线-普通触达: AbstractStrategyDispatchService.dispatchHandler 整体链路分析报告V2

## 1. 概述

本文档详细分析离线普通触达的完整链路，从定时任务调度开始，到最终的营销触达执行，重点分析`AbstractStrategyDispatchService.dispatchHandler`方法的调用链路和业务逻辑。离线普通触达是一个基于定时任务的批量营销系统，通过分页查询人群数据、AB测试分组、流控管理等机制，实现大规模用户的精准营销触达。

## 2. 整体架构图

### 2.1 完整调用链路图

```mermaid
graph TD
    A["XxlJob定时任务<br/>STRATEGY_DISPATCH_TASK_GENERATOR<br/>策略执行任务生成器"] --> B["StrategyHandler<br/>.strategyDispatchTaskProducer()<br/>生成周期策略和离线策略任务"]
    B --> C["DispatchTaskService<br/>.createDispatchTask()<br/>创建dispatch_task表记录"]
    C --> D["插入dispatch_task表<br/>(status=0)<br/>待执行任务记录"]

    E["XxlJob定时任务<br/>STRATEGY_DISPATCH_TASK_EXECCUTE<br/>策略执行下发任务"] --> F["StrategyHandler<br/>.strategyDispatchTaskConsumer()<br/>分片查询待执行任务"]
    F --> G["DispatchTaskService<br/>.selectTodoList()<br/>查询待执行的dispatch_task"]
    G --> H["StrategyHandler<br/>.execute()<br/>根据渠道类型路由到具体实现"]
    H --> I["AbstractStrategyDispatchService<br/>.execute()<br/>策略执行入口"]

    I --> J["AbstractStrategyDispatchService<br/>.initContext()<br/>初始化策略上下文"]
    J --> K["AbstractStrategyDispatchService<br/>.beginExecute()<br/>写入strategy_exec_log执行记录"]
    K --> L["AbstractStrategyDispatchService<br/>.preHandler()<br/>人群包校验和预处理"]
    L --> M["AbstractStrategyDispatchService<br/>.coreLogicExecute()<br/>核心逻辑执行"]

    M --> N["AbstractStrategyDispatchService<br/>.queryAndGroupAndSend()<br/>分页查询+分组+下发"]
    N --> O["CrowdDetailRepository<br/>.selectByIdPage()<br/>分页查询crowd_detail表"]
    O --> P["RandomNumService<br/>.randomNum()<br/>获取随机数用于AB测试"]
    P --> Q["StrategyGroupService<br/>.matchGroupRule()<br/>AB测试分组匹配"]
    Q --> R["用户去重处理<br/>用户ID+手机号去重"]

    R --> S["AdsStrategyLabelQueryService<br/>.queryLabelHandler()<br/>查询用户标签数据"]
    S --> T["TemplateParamQueryService<br/>.templateParamQuery()<br/>查询模板参数"]
    T --> U["AbstractStrategyDispatchService<br/>.dispatchHandler()<br/>抽象方法调用具体实现"]

    U --> V["具体渠道实现<br/>StrategyDispatchForSmsServiceImpl等<br/>调用BatchDispatchService"]
    V --> W["BatchDispatchService<br/>.sendSms()/.sendTele()等<br/>调用外部服务接口"]
    W --> X["外部服务调用<br/>(SMS/电销/Push等)<br/>实际的营销触达执行"]
    X --> Y["记录执行结果<br/>更新crowd_push_batch和user_dispatch_detail"]

    style A fill:#e1f5fe
    style E fill:#e1f5fe
    style U fill:#ffeb3b
    style W fill:#f3e5f5
    style X fill:#e8f5e8
```

#### 业务功能分阶段说明

**第一阶段：任务生成阶段**
- **业务目标**: 定时扫描策略配置，为有效的普通策略和周期策略生成执行任务
- **核心逻辑**: 查询type=0的普通策略 → 过滤有效期内策略 → 为每个策略的每个渠道创建执行任务
- **输出结果**: dispatch_task表中插入待执行任务记录

**第二阶段：任务执行阶段**
- **业务目标**: 分片并行处理执行任务，实现大规模用户数据的高效处理
- **核心逻辑**: 多节点分片查询 → 策略上下文初始化 → 人群包校验 → 核心逻辑执行
- **输出结果**: 策略执行记录和用户触达明细

**第三阶段：人群处理阶段**
- **业务目标**: 分页查询人群数据，进行AB测试分组和数据过滤
- **核心逻辑**: 分页查询crowd_detail → 随机数生成 → AB分组匹配 → 用户去重
- **输出结果**: 符合条件的目标用户列表

**第四阶段：标签查询阶段**
- **业务目标**: 查询用户标签数据和模板参数，进行最终的数据验证
- **核心逻辑**: 标签数据查询 → 模板参数查询 → 数据完整性验证
- **输出结果**: 通过验证的用户数据和对应的模板参数

**第五阶段：营销执行阶段**
- **业务目标**: 调用具体渠道服务，执行实际的营销触达
- **核心逻辑**: 渠道路由分发 → 外部服务调用 → 执行结果记录
- **输出结果**: 营销触达执行和结果记录

### 2.2 关键类和方法对应关系

| 序号 | 类名 | 关键方法 | 代码位置 | 功能说明 |
|------|------|----------|----------|----------|
| 1 | `StrategyDispatch` | `strategyDispatchTaskProducer()` | `cdp-adapter/.../StrategyDispatch.java:36` | XxlJob任务生成入口 |
| 2 | `StrategyDispatch` | `strategyDispatchTaskConsumer()` | `cdp-adapter/.../StrategyDispatch.java:54` | XxlJob任务执行入口 |
| 3 | `StrategyHandler` | `strategyDispatchTaskProducer()` | `cdp-domain/.../StrategyHandler.java:39` | 策略任务生成逻辑 |
| 4 | `StrategyHandler` | `strategyDispatchTaskConsumer()` | `cdp-domain/.../StrategyHandler.java:602` | 策略任务消费逻辑 |
| 5 | `StrategyHandler` | `execute()` | `cdp-domain/.../StrategyHandler.java:703` | 策略执行路由 |
| 6 | `AbstractStrategyDispatchService` | `execute()` | `cdp-domain/.../AbstractStrategyDispatchService.java:195` | 策略执行入口 |
| 7 | `AbstractStrategyDispatchService` | `coreLogicExecute()` | `cdp-domain/.../AbstractStrategyDispatchService.java:631` | 核心逻辑执行 |
| 8 | `AbstractStrategyDispatchService` | `queryAndGroupAndSend()` | `cdp-domain/.../AbstractStrategyDispatchService.java:650` | 查询分组下发 |
| 9 | `AbstractStrategyDispatchService` | `dispatchHandler()` | `cdp-domain/.../AbstractStrategyDispatchService.java:915` | 抽象下发方法 |
| 10 | `StrategyDispatchForSmsServiceImpl` | `dispatchHandler()` | `cdp-domain/.../StrategyDispatchForSmsServiceImpl.java:85` | 短信渠道实现 |
| 11 | `BatchDispatchService` | `sendSms()` | `cdp-domain/.../BatchDispatchServiceImpl.java:106` | 短信发送服务 |
| 12 | `CrowdDetailRepository` | `selectByIdPage()` | `cdp-domain/.../CrowdDetailRepository.java:72` | 人群分页查询 |

### 2.3 方法调用时序图

```mermaid
sequenceDiagram
    participant XxlJob as XxlJob定时任务
    participant SD as StrategyDispatch
    participant SH as StrategyHandler
    participant ASDS as AbstractStrategyDispatchService
    participant CDR as CrowdDetailRepository
    participant RNS as RandomNumService
    participant SGS as StrategyGroupService
    participant ASLQS as AdsStrategyLabelQueryService
    participant TPQS as TemplateParamQueryService
    participant BDS as BatchDispatchService

    Note over XxlJob,BDS: 第一阶段：任务生成
    XxlJob->>SD: @XxlJob("STRATEGY_DISPATCH_TASK_GENERATOR")
    SD->>SH: strategyDispatchTaskProducer()
    SH->>SH: 查询有效策略，创建dispatch_task记录
    SH-->>XxlJob: 任务生成完成

    Note over XxlJob,BDS: 第二阶段：任务执行
    XxlJob->>SD: @XxlJob("STRATEGY_DISPATCH_TASK_EXECCUTE")
    SD->>SH: strategyDispatchTaskConsumer(total, index, bizType)
    SH->>SH: 分片查询待执行任务
    SH->>ASDS: execute(marketChannelId, dispatchTaskDo)

    Note over XxlJob,BDS: 第三阶段：策略执行
    ASDS->>ASDS: initContext() → beginExecute() → preHandler()
    ASDS->>ASDS: coreLogicExecute() → queryAndGroupAndSend()

    Note over XxlJob,BDS: 第四阶段：人群处理
    ASDS->>CDR: selectByIdPage(id, limit, crowdId, crowdExecLogId)
    CDR-->>ASDS: List<CrowdDetailDo>
    ASDS->>RNS: randomNum(context, detailList)
    RNS-->>ASDS: 带随机数的用户列表
    ASDS->>SGS: matchGroupRule(bizKey, matchFunction, detailList)
    SGS-->>ASDS: AB分组后的用户列表

    Note over XxlJob,BDS: 第五阶段：标签和模板查询
    ASDS->>ASLQS: queryLabelHandler(strategyId, marketChannelId, ...)
    ASLQS-->>ASDS: 标签过滤后的用户列表
    ASDS->>TPQS: templateParamQuery(strategyContext, app, partition)
    TPQS-->>ASDS: 模板参数列表

    Note over XxlJob,BDS: 第六阶段：营销执行
    ASDS->>ASDS: dispatchHandler() (抽象方法)
    ASDS->>BDS: sendSms()/sendTele()等具体渠道方法
    BDS-->>ASDS: 执行结果
    ASDS-->>XxlJob: 更新执行状态和记录
```

### 2.4 关键代码跳转路径

#### 2.4.1 任务生成阶段代码跳转
```java
// 1. XxlJob入口
@XxlJob("STRATEGY_DISPATCH_TASK_GENERATOR")
public ReturnT<String> strategyDispatchTaskProducer(String param) {
    strategyHandler.strategyDispatchTaskProducer(); // 跳转到步骤2
}

// 2. 策略任务生成
public void strategyDispatchTaskProducer() {
    List<StrategyDo> strategyList = strategyRepository.getStrategyList(); // 查询有效策略
    for (StrategyDo strategy : strategyList) {
        dispatchTaskService.createDispatchTask(strategy); // 跳转到步骤3
    }
}

// 3. 创建执行任务
public void createDispatchTask(StrategyDo strategy) {
    List<StrategyMarketChannelDo> channels = strategyMarketChannelRepository
        .selectListByStrategyId(strategy.getId());
    for (StrategyMarketChannelDo channel : channels) {
        DispatchTaskDo task = new DispatchTaskDo();
        task.setBizId(strategy.getId());
        task.setAssociationId(channel.getId());
        dispatchTaskRepository.insert(task); // 插入数据库
    }
}
```

#### 2.4.2 任务执行阶段代码跳转
```java
// 1. XxlJob入口
@XxlJob("STRATEGY_DISPATCH_TASK_EXECCUTE")
public ReturnT<String> strategyDispatchTaskConsumer(String param) {
    int bizType = Integer.parseInt(param);
    strategyHandler.strategyDispatchTaskConsumer(total, index, bizType); // 跳转到步骤2
}

// 2. 任务消费处理
public void strategyDispatchTaskConsumer(int total, int index, int bizType) {
    List<DispatchTaskDo> taskList = dispatchTaskService.selectTodoList(total, index, bizType);
    for (DispatchTaskDo task : taskList) {
        execute(Long.parseLong(task.getAssociationId()), task); // 跳转到步骤3
    }
}

// 3. 策略执行路由
public void execute(Long marketChannelId, DispatchTaskDo dispatchTaskDo) {
    StrategyMarketChannelDo channel = strategyMarketChannelRepository.selectById(marketChannelId);
    StrategyMarketChannelEnum marketChannel = StrategyMarketChannelEnum.getInstance(channel.getMarketChannel());
    this.build(marketChannel).execute(marketChannelId, dispatchTaskDo); // 跳转到步骤4
}

// 4. 具体策略执行
public void execute(@NonNull Long marketChannelId, DispatchTaskDo dispatchTaskDo) {
    StrategyContext strategyContext = initContext(marketChannelId, dispatchTaskDo);
    coreLogicExecute(strategyContext); // 跳转到步骤5
}
```

#### 2.4.3 核心逻辑执行阶段代码跳转
```java
// 1. 核心逻辑执行
protected void coreLogicExecute(StrategyContext strategyContext) {
    StrategyDo strategyDo = strategyContext.getStrategyDo();
    BiPredicate<String, Integer> matchFunction = null;
    if (StrategyAbTestEnum.getInstance(strategyDo.getAbTest()) == StrategyAbTestEnum.YES) {
        matchFunction = strategyContext.getStrategyGroupDo()
            .match(StrategyGroupTypeEnum.getInstance(strategyDo.getAbType()));
    }
    this.queryAndGroupAndSend(strategyContext, Triple.of(strategyDo.getBizKey(), matchFunction, blankGroup)); // 跳转到步骤2
}

// 2. 查询分组下发
protected void queryAndGroupAndSend(StrategyContext context, Triple<String, BiPredicate<String, Integer>, List<StrategyGroupDo>> funPair) {
    for (Long crowdId : context.getCrowdIds()) {
        Long crowdDetailId = 0L;
        while (true) {
            List<CrowdDetailDo> detailList = crowdDetailRepository
                .selectByIdPage(crowdDetailId, pageSize, crowdId, crowdExecLogId); // 跳转到步骤3
            if (CollectionUtils.isEmpty(detailList)) break;

            processAndDispatch(context, funPair, detailList); // 跳转到步骤4
            crowdDetailId = detailList.get(detailList.size() - 1).getId();
        }
    }
}

// 3. 数据处理和下发
private void processAndDispatch(StrategyContext context, Triple funPair, List<CrowdDetailDo> detailList) {
    // 获取随机数
    detailList = randomNumService.randomNum(context, detailList);
    // AB分组匹配
    detailList = strategyGroupService.matchGroupRule(funPair.getLeft(), funPair.getMiddle(), detailList);
    // 用户去重
    detailList = detailList.stream().filter(item -> !container.contains(item.getUserId())).collect(Collectors.toList());
    // 标签查询
    Pair<List<CrowdDetailDo>, List<Object>> pair = labelAndTempParamQuery(context, app, detailList, availableDetails);
    // 执行下发
    dispatchHandler(context, groupCount, totalCount, sendCount, app, pair.getLeft(), pair.getRight()); // 跳转到步骤4
}
```

### 2.5 数据表流转关系

```mermaid
graph TD
    A["strategy表<br/>SELECT查询有效策略"] --> B["strategy_group表<br/>SELECT查询分组配置"]
    B --> C["strategy_market_channel表<br/>SELECT查询渠道配置"]
    C --> D["dispatch_task表<br/>INSERT创建执行任务<br/>SELECT查询待执行任务<br/>UPDATE更新执行状态"]
    D --> E["strategy_crowd_pack表<br/>SELECT查询策略关联人群包"]
    E --> F["crowd_detail表<br/>SELECT分页查询人群明细"]
    F --> G["strategy_exec_log表<br/>INSERT记录执行日志<br/>UPDATE更新执行状态"]
    G --> H["crowd_push_batch表<br/>INSERT记录批次信息"]
    H --> I["user_dispatch_detail表<br/>INSERT记录触达明细"]

    J["Redis缓存<br/>SET/GET/DEL操作"] --> K["随机数缓存<br/>HGET获取用户随机数"]
    J --> L["流控计数<br/>INCR计数器<br/>EXPIRE设置过期"]
    J --> M["执行统计<br/>INCR计数<br/>PFADD去重统计"]

    style A fill:#e3f2fd
    style I fill:#e8f5e8
    style H fill:#e8f5e8
    style J fill:#fff3e0
```

#### 详细数据库操作说明

| 表名 | 操作类型 | 具体操作 | 代码位置 | XxlJob阶段 | 业务场景 |
|------|----------|----------|----------|------------|----------|
| **strategy** | SELECT | 查询有效普通策略 | `StrategyRepository.getStrategyList()` | STRATEGY_DISPATCH_TASK_GENERATOR | 任务生成阶段筛选策略 |
| **strategy** | UPDATE | 更新策略执行状态 | `StrategyRepository.updateById()` | STRATEGY_DISPATCH_TASK_EXECCUTE | 标记策略为执行中 |
| **strategy_group** | SELECT | 查询策略分组配置 | `StrategyGroupRepository.selectListByStrategyId()` | STRATEGY_DISPATCH_TASK_EXECCUTE | 获取AB测试分组规则 |
| **strategy_market_channel** | SELECT | 查询策略渠道配置 | `StrategyMarketChannelRepository.selectById()` | STRATEGY_DISPATCH_TASK_EXECCUTE | 获取渠道配置信息 |
| **strategy_crowd_pack** | SELECT | 查询策略人群包关联 | `StrategyCrowdPackRepository.selectListByStrategyId()` | STRATEGY_DISPATCH_TASK_EXECCUTE | 获取策略关联的人群包 |
| **dispatch_task** | INSERT | 创建执行任务 | `DispatchTaskRepository.insert()` | STRATEGY_DISPATCH_TASK_GENERATOR | 为每个策略渠道创建任务 |
| **dispatch_task** | SELECT | 查询待执行任务 | `DispatchTaskRepository.selectTodoList()` | STRATEGY_DISPATCH_TASK_EXECCUTE | 分片查询待执行任务 |
| **dispatch_task** | UPDATE | 更新任务状态 | `DispatchTaskRepository.updateById()` | STRATEGY_DISPATCH_TASK_EXECCUTE | 更新任务状态(执行中/成功/失败) |
| **crowd_detail** | SELECT | 分页查询人群明细 | `CrowdDetailRepository.selectByIdPage()` | STRATEGY_DISPATCH_TASK_EXECCUTE | 分页读取人群用户数据 |
| **strategy_exec_log** | INSERT | 记录执行日志 | `StrategyExecLogRepository.insert()` | STRATEGY_DISPATCH_TASK_EXECCUTE | 记录策略执行状态和统计 |
| **strategy_exec_log** | UPDATE | 更新执行统计 | `StrategyExecLogRepository.updateById()` | STRATEGY_DISPATCH_TASK_EXECCUTE | 更新执行人数和状态 |
| **crowd_push_batch** | INSERT | 记录批次信息 | `CrowdPushBatchRepository.insert()` | STRATEGY_DISPATCH_TASK_EXECCUTE | 记录每个批次的推送信息 |
| **user_dispatch_detail** | INSERT | 记录触达明细 | `UserDispatchDetailRepository.insert()` | STRATEGY_DISPATCH_TASK_EXECCUTE | 记录每次营销触达的详细信息 |

#### Redis操作详细说明

| Redis Key类型 | 操作命令 | 具体用途 | 代码位置 | XxlJob阶段 | 过期时间 |
|---------------|----------|----------|----------|------------|----------|
| **随机数缓存** | HGET | 获取用户随机数 | `RandomNumService.randomNum()` | STRATEGY_DISPATCH_TASK_EXECCUTE | 7天 |
| **随机数缓存** | HSET | 设置用户随机数 | `RandomNumService.randomNum()` | STRATEGY_DISPATCH_TASK_EXECCUTE | 7天 |
| **流控计数** | INCR | 用户触达次数统计 | `FlowCtrlCoreService.execute()` | STRATEGY_DISPATCH_TASK_EXECCUTE | 按配置 |
| **流控计数** | INCR | 策略级别流控统计 | `FlowCtrlCoreService.execute()` | STRATEGY_DISPATCH_TASK_EXECCUTE | 按配置 |
| **执行统计** | INCR | 策略执行人数统计 | `AbstractStrategyDispatchService.groupCount()` | STRATEGY_DISPATCH_TASK_EXECCUTE | 2天 |
| **执行统计** | PFADD | 去重用户数统计 | `AbstractStrategyDispatchService.groupCount()` | STRATEGY_DISPATCH_TASK_EXECCUTE | 2天 |

#### XxlJob阶段总览

| XxlJob任务名称 | 执行频率 | 主要职责 | 涉及的数据库操作 | 涉及的Redis操作 |
|----------------|----------|----------|------------------|-----------------|
| **STRATEGY_DISPATCH_TASK_GENERATOR** | 每分钟 | 扫描策略，生成执行任务 | strategy表SELECT<br/>dispatch_task表INSERT | 无 |
| **STRATEGY_DISPATCH_TASK_EXECCUTE** | 每分钟 | 执行策略任务，营销触达 | dispatch_task表SELECT/UPDATE<br/>crowd_detail表SELECT<br/>strategy_exec_log表INSERT/UPDATE<br/>crowd_push_batch表INSERT<br/>user_dispatch_detail表INSERT | 随机数缓存HGET/HSET<br/>流控计数INCR<br/>执行统计INCR/PFADD |

## 3. 数据流转流程分析

### 3.1 整体数据流转路径

离线普通触达的数据流转是一个基于定时任务的批量处理流程，从策略配置开始，经过任务生成、人群查询、数据处理、营销执行，最终到结果记录。

```mermaid
graph TD
    A[策略配置数据] --> B[任务生成阶段]
    B --> C[任务执行调度]
    C --> D[策略上下文初始化]
    D --> E[人群包校验]
    E --> F[分页查询人群数据]
    F --> G[随机数生成]
    G --> H[AB测试分组]
    H --> I[用户去重处理]
    I --> J[标签数据查询]
    J --> K[模板参数查询]
    K --> L[dispatchHandler调用]
    L --> M[具体渠道实现]
    M --> N[外部服务调用]
    N --> O[执行结果记录]
```

### 3.2 分页查询逻辑详细分析

#### 3.2.1 人群数据分页策略

离线普通触达采用基于ID的分页查询策略，避免传统OFFSET分页在大数据量下的性能问题。

**分页查询配置参数**:
```properties
# Apollo配置文件位置: /Users/<USER>/IdeaProjects/xyf-cdp/docs/麻雀apollo配置.txt
crowd.rock.page.size.new = 5000              # 人群查询分页大小
label.query.batch.size = 1000                # 标签查询批次大小
strategy.sms.batch.size = 1000               # 短信批次大小
strategy.tele.batch.size = 500               # 电销批次大小
```

**分页查询实现**:
```java
// 代码位置: cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/service/dispatch/offline/impl/AbstractStrategyDispatchService.java
// AbstractStrategyDispatchService.queryAndGroupAndSend()
protected void queryAndGroupAndSend(StrategyContext context, Triple<String, BiPredicate<String, Integer>, List<StrategyGroupDo>> funPair) {
    Roaring64Bitmap container = new Roaring64Bitmap(); // 用户ID去重容器

    // 1. 遍历策略关联的所有人群包
    for (Long crowdId : context.getCrowdIds()) {
        Long crowdDetailId = 0L; // 起始ID
        int pageSize = appConfigService.getCrowdRockPageSizeNew(); // 分页大小

        // 2. 基于ID的分页查询
        while (true) {
            List<CrowdDetailDo> detailList = crowdDetailRepository
                .selectByIdPage(crowdDetailId, pageSize, crowdId, crowdExecLogId);

            if (CollectionUtils.isEmpty(detailList)) {
                break; // 没有更多数据
            }

            // 3. 处理当前批次数据
            processCurrentBatch(context, funPair, detailList, container);

            // 4. 更新下次查询的起始ID
            crowdDetailId = detailList.get(detailList.size() - 1).getId();
        }
    }
}
```

#### 3.2.2 人群数据查询SQL实现

**数据库查询逻辑**:
```sql
-- 代码位置: cdp-domain/src/main/resources/sqlmap/cdp/crowd/crowddetailmapper.xml
-- CrowdDetailRepository.selectByIdPage()
SELECT id, user_id, mobile, app, inner_app, crowd_id, crowd_exec_log_id, create_time
FROM crowd_detail
WHERE id > #{id}
  AND crowd_id = #{crowdId}
  AND crowd_exec_log_id = #{crowdExecLogId}
  AND d_flag = 1
ORDER BY id ASC
LIMIT #{pageSize}
```

**查询优化策略**:
- **索引设计**: `(crowd_id, crowd_exec_log_id, id)` 复合索引
- **分页方式**: 基于ID的游标分页，避免OFFSET性能问题
- **数据过滤**: 通过d_flag字段过滤有效数据
- **排序优化**: 利用主键ID的自然排序

### 3.3 数据处理与过滤流程

#### 3.3.1 用户数据去重机制

系统采用多层去重策略，确保用户不会重复接收营销信息。

**去重处理流程**:
```java
// 代码位置: cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/service/dispatch/offline/impl/AbstractStrategyDispatchService.java
// AbstractStrategyDispatchService.processCurrentBatch()
private void processCurrentBatch(StrategyContext context, Triple funPair,
                                List<CrowdDetailDo> detailList, Roaring64Bitmap container) {

    // 1. 获取随机数(用于AB测试)
    detailList = randomNumService.randomNum(context, detailList);

    // 2. 麻雀-fxk老客转xyf01下发处理
    detailList = abstractAdsStrategyLabelService
        .convertCrowdList(detailList, context.getStrategyDo().getUserConvert());

    // 3. 根据用户ID全局去重(内存级别)
    detailList = detailList.stream()
        .filter(item -> !container.contains(item.getUserId()))
        .collect(Collectors.toList());

    // 4. 将去重后的用户ID加入到容器中
    detailList.stream()
        .map(CrowdDetailDo::getUserId)
        .forEachOrdered(container::add);

    // 5. 根据手机号去重(数据级别)
    detailList = detailList.stream()
        .filter(ListUtils.distinctByKey(CrowdDetailDo::getMobile))
        .collect(Collectors.toList());
}
```

#### 3.3.2 AB测试分组数据流转

AB测试分组通过随机数服务和分组规则匹配实现用户分流。

**随机数生成逻辑**:
```java
// 代码位置: cdp-domain/src/main/java/com/xftech/cdp/domain/randomnum/service/impl/RandomNumServiceImpl.java
// RandomNumService.randomNum()
public List<CrowdDetailDo> randomNum(StrategyContext context, List<CrowdDetailDo> detailList) {
    String bizKey = context.getStrategyDo().getBizKey();

    for (CrowdDetailDo crowdDetail : detailList) {
        String cacheKey = RedisKeyUtils.genRandomNumKey(bizKey, crowdDetail.getUserId());

        // 1. 先从Redis缓存获取随机数
        Integer randomNum = redisUtils.hGet(cacheKey, "randomNum", Integer.class);

        if (randomNum == null) {
            // 2. 缓存不存在，生成新的随机数
            randomNum = generateRandomNum(bizKey, crowdDetail.getUserId());

            // 3. 存入Redis缓存，7天过期
            redisUtils.hSet(cacheKey, "randomNum", randomNum, RedisUtils.DEFAULT_EXPIRE_SEVEN_DAYS);
        }

        // 4. 设置随机数到用户对象
        crowdDetail.setRandomNum(randomNum);
    }

    return detailList;
}

private Integer generateRandomNum(String bizKey, Long userId) {
    // 基于业务键和用户ID生成一致性随机数
    String seed = bizKey + "_" + userId;
    return Math.abs(seed.hashCode()) % 100;
}
```

**分组匹配流程**:
```java
// 代码位置: cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/service/group/impl/StrategyGroupServiceImpl.java
// StrategyGroupService.matchGroupRule()
public List<CrowdDetailDo> matchGroupRule(String bizKey, BiPredicate<String, Integer> matchFunction,
                                         List<CrowdDetailDo> detailList) {
    if (matchFunction == null) {
        return detailList; // 无AB测试，返回全部用户
    }

    return detailList.stream()
        .filter(crowdDetail -> matchFunction.test(bizKey, crowdDetail.getRandomNum()))
        .collect(Collectors.toList());
}

// 分组规则匹配函数
public BiPredicate<String, Integer> match(StrategyGroupTypeEnum groupType) {
    switch (groupType) {
        case HASH_MOD:
            // 哈希取模分组: randomNum % 100 < ratio
            return (bizKey, randomNum) -> randomNum % 100 < this.ratio;
        case RANGE:
            // 范围分组: minValue <= randomNum <= maxValue
            return (bizKey, randomNum) -> randomNum >= this.minValue && randomNum <= this.maxValue;
        default:
            return (bizKey, randomNum) -> true;
    }
}
```

#### 3.3.3 标签数据查询与过滤

标签数据查询是数据处理的关键环节，涉及实时标签查询和数据验证。

**标签查询流程**:
```java
// 代码位置: cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/service/dispatch/offline/impl/AbstractStrategyDispatchService.java
// AbstractStrategyDispatchService.labelAndTempParamQuery()
public Pair<List<CrowdDetailDo>, List<Object>> labelAndTempParamQuery(
        StrategyContext strategyContext, String app, List<CrowdDetailDo> crowdDetailDoList,
        AtomicReference<List<CrowdDetailDo>> availableDetails) {

    List<Object> finalTemplateParamList = Lists.newArrayList();
    List<CrowdDetailDo> finalCrowdDetailList = Lists.newArrayList();

    // 1. 按批次大小分批处理，避免单次查询数据量过大
    for (List<CrowdDetailDo> partition : Lists.partition(crowdDetailDoList,
                                                        strategyConfig.getLabelQueryBatchSize())) {

        // 2. 标签查询
        List<CrowdDetailDo> queryLabelList = adsStrategyLabelQueryService.queryLabelHandler(
            strategyContext.getStrategyDo().getId(),
            strategyContext.getStrategyMarketChannelDo().getId(),
            strategyContext.getStrategyMarketChannelDo().getMarketChannel(),
            app,
            partition
        );

        // 3. 模板参数查询
        Pair<List<CrowdDetailDo>, List<?>> paramQueryResult =
            templateParamQuery(strategyContext, app, queryLabelList);

        // 4. 合并结果
        finalCrowdDetailList.addAll(paramQueryResult.getLeft());
        finalTemplateParamList.addAll(paramQueryResult.getRight());
    }

    availableDetails.set(finalCrowdDetailList);
    return Pair.of(finalCrowdDetailList, finalTemplateParamList);
}
```

**模板参数查询逻辑**:
```java
// 代码位置: cdp-domain/src/main/java/com/xftech/cdp/domain/param/service/impl/TemplateParamQueryServiceImpl.java
// TemplateParamQueryService.templateParamQuery()
public Pair<List<CrowdDetailDo>, List<?>> templateParamQuery(StrategyContext strategyContext,
                                                           String app, List<CrowdDetailDo> crowdDetailList) {

    StrategyMarketChannelDo channelDo = strategyContext.getStrategyMarketChannelDo();
    StrategyMarketChannelEnum channelEnum = StrategyMarketChannelEnum.getInstance(channelDo.getMarketChannel());

    List<CrowdDetailDo> validatedList = new ArrayList<>();
    List<Object> templateParams = new ArrayList<>();

    for (CrowdDetailDo crowdDetail : crowdDetailList) {
        try {
            // 1. 根据渠道类型查询对应的模板参数
            Object templateParam = queryTemplateParamByChannel(channelEnum, channelDo, crowdDetail, app);

            // 2. 验证模板参数完整性
            if (validateTemplateParam(templateParam)) {
                validatedList.add(crowdDetail);
                templateParams.add(templateParam);
            }
        } catch (Exception e) {
            log.warn("模板参数查询失败, userId:{}, error:{}", crowdDetail.getUserId(), e.getMessage());
        }
    }

    return Pair.of(validatedList, templateParams);
}
```

### 3.4 核心数据表结构

#### 3.4.1 策略配置相关表
```sql
-- 策略主表 (数据库表位置: cdp数据库)
-- 对应实体类: cdp-domain/src/main/java/com/xftech/cdp/infra/repository/cdp/strategy/po/StrategyDo.java
strategy:
  - id: 策略ID (主键)
  - name: 策略名称
  - type: 策略类型(0=普通策略, 1=引擎策略)
  - ab_test: 是否AB测试(0=否, 1=是)
  - ab_type: AB测试类型(1=哈希取模, 2=范围分组)
  - biz_key: 业务键(用于随机数生成)
  - validity_begin: 有效期开始时间
  - validity_end: 有效期结束时间
  - status: 策略状态(0=草稿, 1=启用, 2=执行中, 3=暂停, 4=结束)
  - user_convert: 用户转换配置
  - create_time: 创建时间
  - update_time: 更新时间

-- 策略分组表 (数据库表位置: cdp数据库)
-- 对应实体类: cdp-domain/src/main/java/com/xftech/cdp/infra/repository/cdp/strategy/po/StrategyGroupDo.java
strategy_group:
  - id: 分组ID (主键)
  - strategy_id: 策略ID (外键)
  - name: 分组名称
  - ratio: 分组比例(哈希取模时使用)
  - min_value: 最小值(范围分组时使用)
  - max_value: 最大值(范围分组时使用)
  - status: 分组状态
  - create_time: 创建时间
  - update_time: 更新时间

-- 策略营销渠道表 (数据库表位置: cdp数据库)
-- 对应实体类: cdp-domain/src/main/java/com/xftech/cdp/infra/repository/cdp/strategy/po/StrategyMarketChannelDo.java
strategy_market_channel:
  - id: 渠道ID (主键)
  - strategy_id: 策略ID (外键)
  - strategy_group_id: 分组ID (外键)
  - market_channel: 营销渠道(1=短信, 2=电销, 3=Push, 4=生活权益等)
  - template_id: 模板ID
  - ext_info: 扩展信息(JSON格式)
  - signature_key: 签名密钥
  - status: 渠道状态
  - create_time: 创建时间
  - update_time: 更新时间
```

#### 3.4.2 任务执行相关表
```sql
-- 执行任务表 (数据库表位置: cdp数据库)
-- 对应实体类: cdp-domain/src/main/java/com/xftech/cdp/infra/repository/cdp/strategy/po/DispatchTaskDo.java
dispatch_task:
  - id: 任务ID (主键)
  - biz_id: 业务ID(策略ID)
  - association_id: 关联ID(渠道ID)
  - biz_type: 业务类型(101=离线策略, 1=周期策略)
  - status: 任务状态(0=待执行, 1=执行中, 2=成功, 3=失败)
  - retry_times: 重试次数
  - max_retry_times: 最大重试次数
  - error_msg: 错误信息
  - dispatch_time: 执行时间
  - start_time: 开始时间
  - end_time: 结束时间
  - create_time: 创建时间
  - update_time: 更新时间

-- 策略执行日志表 (数据库表位置: cdp数据库)
-- 对应实体类: cdp-domain/src/main/java/com/xftech/cdp/infra/repository/cdp/strategy/po/StrategyExecLogDo.java
strategy_exec_log:
  - id: 执行日志ID (主键)
  - strategy_id: 策略ID (外键)
  - strategy_group_id: 分组ID (外键)
  - market_channel_id: 渠道ID (外键)
  - exec_status: 执行状态(0=初始, 1=执行中, 2=成功, 3=失败)
  - group_count: 分组人数
  - send_count: 发送人数
  - success_count: 成功人数
  - fail_count: 失败人数
  - exec_time: 执行时间
  - start_time: 开始时间
  - end_time: 结束时间
  - error_msg: 错误信息
  - create_time: 创建时间
  - update_time: 更新时间

-- 人群包表 (数据库表位置: cdp数据库)
-- 对应实体类: cdp-domain/src/main/java/com/xftech/cdp/infra/repository/cdp/crowd/po/CrowdPackDo.java
crowd_pack:
  - id: 人群包ID (主键)
  - name: 人群包名称
  - business_type: 业务类型
  - pull_type: 拉取类型(1=实时, 2=离线)
  - crowd_size: 人群大小
  - oss_folder: OSS文件夹路径
  - oss_file: OSS文件名
  - status: 状态(1=有效, 0=无效)
  - create_time: 创建时间
  - update_time: 更新时间

-- 人群明细表 (数据库表位置: cdp数据库)
-- 对应实体类: cdp-domain/src/main/java/com/xftech/cdp/infra/repository/cdp/crowd/po/CrowdDetailDo.java
crowd_detail:
  - id: 明细ID (主键)
  - user_id: 用户ID
  - mobile: 手机号
  - app: 应用标识
  - inner_app: 内部应用标识
  - crowd_id: 人群包ID (外键)
  - crowd_exec_log_id: 人群执行日志ID
  - random_num: 随机数(用于AB测试)
  - d_flag: 删除标志(1=有效, 0=删除)
  - create_time: 创建时间
  - update_time: 更新时间
```

#### 3.4.3 执行记录相关表
```sql
-- 批次记录表 (数据库表位置: cdp数据库)
-- 对应实体类: cdp-domain/src/main/java/com/xftech/cdp/infra/repository/cdp/crowd/po/CrowdPushBatchDo.java
crowd_push_batch:
  - id: 批次ID (主键)
  - batch_num: 批次号(唯一标识)
  - strategy_id: 策略ID (外键)
  - strategy_group_id: 分组ID (外键)
  - market_channel_id: 渠道ID (外键)
  - market_channel: 营销渠道
  - mobile_batch: 手机号批次(JSON格式)
  - batch_size: 批次大小
  - success_count: 成功数量
  - fail_count: 失败数量
  - status: 批次状态(0=失败, 1=成功, 2=部分成功)
  - response_code: 响应码
  - response_msg: 响应消息
  - create_time: 创建时间
  - update_time: 更新时间

-- 用户触达明细表 (数据库表位置: cdp数据库)
-- 对应实体类: cdp-domain/src/main/java/com/xftech/cdp/infra/repository/cdp/strategy/po/UserDispatchDetailDo.java
user_dispatch_detail:
  - id: 明细ID (主键)
  - user_id: 用户ID
  - mobile: 手机号
  - batch_num: 批次号
  - strategy_id: 策略ID (外键)
  - strategy_group_id: 分组ID (外键)
  - market_channel_id: 渠道ID (外键)
  - market_channel: 营销渠道
  - template_id: 模板ID
  - dispatch_time: 触达时间
  - status: 状态(0=失败, 1=成功)
  - response_code: 响应码
  - response_msg: 响应消息
  - create_time: 创建时间
  - update_time: 更新时间

-- 策略人群包关联表 (数据库表位置: cdp数据库)
-- 对应实体类: cdp-domain/src/main/java/com/xftech/cdp/infra/repository/cdp/strategy/po/StrategyCrowdPackDo.java
strategy_crowd_pack:
  - id: 关联ID (主键)
  - strategy_id: 策略ID (外键)
  - crowd_id: 人群包ID (外键)
  - crowd_exec_log_id: 人群执行日志ID
  - status: 状态(1=有效, 0=无效)
  - create_time: 创建时间
  - update_time: 更新时间
```

## 4. 详细请求流程分析

### 4.1 第一阶段: 定时任务调度与任务生成

#### 4.1.1 XxlJob任务调度入口
```java
// 代码位置: cdp-adapter/src/main/java/com/xftech/cdp/adapter/strategy/StrategyDispatch.java
// StrategyDispatch.strategyDispatchTaskProducer()
// XxlJob: STRATEGY_DISPATCH_TASK_GENERATOR
@XxlJob("STRATEGY_DISPATCH_TASK_GENERATOR")
public ReturnT<String> strategyDispatchTaskProducer(String param) {
    log.info("Xxl-Job 策略执行任务生成器开始执行");
    Stopwatch stopwatch = Stopwatch.createStarted();

    // 1. 调用策略处理器生成任务
    strategyHandler.strategyDispatchTaskProducer();

    stopwatch.stop();
    log.info("Xxl-Job 策略执行任务生成器结束执行, 总计耗时:{}", stopwatch.elapsed(TimeUnit.SECONDS));
    return ReturnT.SUCCESS;
}
```

#### 4.1.2 策略任务生成逻辑
```java
// 代码位置: cdp-domain/src/main/java/com/xftech/cdp/application/StrategyHandler.java
// StrategyHandler.strategyDispatchTaskProducer()
public void strategyDispatchTaskProducer() {
    // 1. 查询有效的策略列表
    List<Integer> sendRulerList = Arrays.asList(1, 2); // 1=立即执行, 2=定时执行
    List<Integer> statusList = Arrays.asList(1, 2);    // 1=启用, 2=执行中
    List<StrategyDo> strategyDoList = strategyService.getStrategyList(sendRulerList, statusList);

    // 2. 过滤普通策略和周期策略
    strategyDoList = strategyDoList.stream()
        .filter(x -> x.getType() == 0) // type=0表示普通策略
        .filter(x -> {
            LocalDateTime now = LocalDateTime.now();
            return now.isAfter(x.getValidityBegin()) && now.isBefore(x.getValidityEnd());
        })
        .collect(Collectors.toList());

    // 3. 为每个策略创建执行任务
    for (StrategyDo strategy : strategyDoList) {
        createStrategyDispatchTasks(strategy);
    }
}

private void createStrategyDispatchTasks(StrategyDo strategy) {
    // 1. 查询策略的所有渠道配置
    List<StrategyMarketChannelDo> channelList = strategyMarketChannelRepository
        .selectListByStrategyId(strategy.getId());

    // 2. 为每个渠道创建执行任务
    for (StrategyMarketChannelDo channel : channelList) {
        DispatchTaskDo task = new DispatchTaskDo();
        task.setBizId(strategy.getId());                    // 策略ID
        task.setAssociationId(channel.getId());             // 渠道ID
        task.setBizType(101);                               // 101=离线策略
        task.setStatus(0);                                  // 0=待执行
        task.setDispatchTime(calculateDispatchTime());      // 计算执行时间
        task.setMaxRetryTimes(3);                          // 最大重试次数

        // 3. 插入任务表
        dispatchTaskService.insert(task);
    }
}
```

#### 4.1.3 Apollo配置参数
```properties
# Apollo配置文件位置: /Users/<USER>/IdeaProjects/xyf-cdp/docs/麻雀apollo配置.txt
# 任务调度相关配置
dispatch.task.page.size = 100                    # 任务查询分页大小
dispatch.task.max.retry.times = 3                # 最大重试次数
dispatch.task.timeout.minutes = 30               # 任务超时时间(分钟)

# 人群查询相关配置
crowd.rock.page.size.new = 5000                  # 人群查询分页大小
label.query.batch.size = 1000                    # 标签查询批次大小

# 渠道批次大小配置
strategy.sms.batch.size = 1000                   # 短信批次大小
strategy.tele.batch.size = 500                   # 电销批次大小
strategy.ai.pronto.batch.size = 200              # AI语音批次大小
strategy.life.rights.batch.size = 1000           # 生活权益批次大小
```

### 4.2 第二阶段: 任务执行调度

#### 4.2.1 任务查询与分发
```java
// 代码位置: cdp-adapter/src/main/java/com/xftech/cdp/adapter/strategy/StrategyDispatch.java
// StrategyDispatch.strategyDispatchTaskConsumer()
// XxlJob: STRATEGY_DISPATCH_TASK_EXECCUTE
@XxlJob("STRATEGY_DISPATCH_TASK_EXECCUTE")
public ReturnT<String> strategyDispatchTaskConsumer(String param) {
    Stopwatch stopwatch = Stopwatch.createStarted();

    // 1. 获取分片信息
    int total = ShardingUtil.getShardingVo().getTotal();
    int index = ShardingUtil.getShardingVo().getIndex();
    int bizType = Integer.parseInt(param);

    log.info("Xxl-Job 策略执行下发任务开始执行, 总分片数:{}, 当前分片数:{}, biz_type:{}",
             total, index, DispatchTaskBizTypeEnum.getByCode(bizType));

    // 2. 调用策略处理器消费任务
    strategyHandler.strategyDispatchTaskConsumer(total, index, bizType);

    stopwatch.stop();
    log.info("Xxl-Job 策略执行下发任务结束执行, 总计耗时:{}", stopwatch.elapsed(TimeUnit.SECONDS));
    return ReturnT.SUCCESS;
}
```

#### 4.2.2 任务消费处理逻辑
```java
// 代码位置: cdp-domain/src/main/java/com/xftech/cdp/application/StrategyHandler.java
// StrategyHandler.strategyDispatchTaskConsumer()
public void strategyDispatchTaskConsumer(int total, int index, int bizType) {
    int pageSize = appConfigService.getDispatchTaskPageSize(); // 默认100

    // 1. 分页查询待执行的任务
    List<DispatchTaskDo> taskList = dispatchTaskService.selectTodoList(total, index, bizType, pageSize);

    log.info("DispatchTask任务查询 --> 查询到待执行任务数量:{}, 分片信息:total={}, index={}, bizType={}",
             taskList.size(), total, index, bizType);

    // 2. 遍历执行每个任务
    for (DispatchTaskDo dispatchTaskDo : taskList) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            log.info("DispatchTask任务执行 --> 任务开始执行, 任务id:{}, 策略:{}, 渠道:{}",
                     dispatchTaskDo.getId(), dispatchTaskDo.getBizId(), dispatchTaskDo.getAssociationId());

            // 3. 执行具体任务
            execute(Long.parseLong(dispatchTaskDo.getAssociationId()), dispatchTaskDo);

            stopwatch.stop();
            log.info("DispatchTask任务执行 --> 任务结束执行, 总计耗时:{}s, 任务id:{}, 策略:{}, 渠道:{}",
                     stopwatch.elapsed(TimeUnit.SECONDS), dispatchTaskDo.getId(),
                     dispatchTaskDo.getBizId(), dispatchTaskDo.getAssociationId());
        } catch (Exception ex) {
            // 4. 异常处理和重试
            dispatchTaskService.updateDispatchTaskFailedRetry(dispatchTaskDo, ex.getMessage());

            if (stopwatch.isRunning()) {
                stopwatch.stop();
            }
            log.error("DispatchTask任务执行 --> 任务执行异常, 总计耗时:{}s, 任务id:{}, 策略:{}, 渠道:{}, 异常信息:{}",
                      stopwatch.elapsed(TimeUnit.SECONDS), dispatchTaskDo.getId(),
                      dispatchTaskDo.getBizId(), dispatchTaskDo.getAssociationId(), ex.getMessage(), ex);
        }
    }
}
```

### 4.3 第三阶段: 策略执行与上下文初始化

#### 4.3.1 策略执行路由
```java
// 代码位置: cdp-domain/src/main/java/com/xftech/cdp/application/StrategyHandler.java
// StrategyHandler.execute()
public void execute(Long marketChannelId, DispatchTaskDo dispatchTaskDo) {
    // 1. 查询渠道配置
    StrategyMarketChannelDo strategyMarketChannelDo = strategyMarketChannelRepository
            .selectById(marketChannelId);
    if (strategyMarketChannelDo == null) {
        log.info("触达渠道不存在, marketChannelId={}", marketChannelId);
        dispatchTaskService.updateTaskFailed(dispatchTaskDo, "触达渠道已不存在");
        return;
    }

    // 2. 获取渠道类型
    StrategyMarketChannelEnum marketChannel = StrategyMarketChannelEnum
            .getInstance(strategyMarketChannelDo.getMarketChannel());
    if (marketChannel == null) {
        log.info("触达渠道类型不存在, marketChannel={}", strategyMarketChannelDo.getMarketChannel());
        dispatchTaskService.updateTaskFailed(dispatchTaskDo, "触达渠道类型不存在");
        return;
    }

    try {
        // 3. 根据渠道类型路由到具体实现
        this.build(marketChannel).execute(marketChannelId, dispatchTaskDo);
    } catch (Exception ex) {
        if (ex instanceof StrategyException) {
            // 业务异常处理
            List<String> errors = appConfigService.getIgnoreStrategyJobExcutingErrors();
            if (!CollectionUtils.isEmpty(errors)) {
                boolean ignore = errors.stream().anyMatch(x -> ex.getMessage().contains(x));
                if (ignore) {
                    log.warn("strategy job execute error, exception ignore, marketChannelId:{}, errors:{}",
                             marketChannelId, ex.getMessage());
                    return;
                }
            }
        }
        throw ex;
    }
}

// 渠道实现构建器
private StrategyDispatchService build(StrategyMarketChannelEnum marketChannel) {
    String beanName = null;
    switch (marketChannel) {
        case SMS:
            beanName = StrategyDispatchConstants.SMS_SERVICE;
            break;
        case VOICE:
            beanName = StrategyDispatchConstants.TELE_SERVICE;
            break;
        case LIFE_RIGHTS:
            beanName = StrategyDispatchConstants.LIFE_RIGHTS_SERVICE;
            break;
        case AI_PRONTO:
            beanName = StrategyDispatchConstants.AI_PRONTO_SERVICE;
            break;
        default:
            throw new StrategyException(StrategyErrorCodeEnum.STRATEGY_MARKET_CHANNEL_NOT_SUPPORT);
    }
    return UdpUtil.getInstance().getBean(beanName, StrategyDispatchService.class);
}
```

#### 4.3.2 策略上下文初始化
```java
// 代码位置: cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/service/dispatch/offline/impl/AbstractStrategyDispatchService.java
// AbstractStrategyDispatchService.execute()
public void execute(@NonNull Long marketChannelId, DispatchTaskDo dispatchTaskDo) {
    // 1. 初始化策略上下文
    StrategyContext strategyContext = initContext(marketChannelId, dispatchTaskDo);

    try {
        // 2. 开始执行记录
        beginExecute(strategyContext);

        // 3. 人群包校验
        preHandler(strategyContext);

        // 4. 核心逻辑执行
        coreLogicExecute(strategyContext);

        // 5. 成功后处理
        successExecute(strategyContext);

        // 6. 更新任务状态为成功
        dispatchTaskService.updateTaskFinish(dispatchTaskDo, "SUCCEED");

    } catch (Exception e) {
        // 7. 异常处理和重试
        dispatchTaskService.updateDispatchTaskFailedRetry(dispatchTaskDo, e.getMessage());
        throw e;
    }
}

// 初始化策略上下文
private StrategyContext initContext(Long marketChannelId, DispatchTaskDo dispatchTaskDo) {
    StrategyContext strategyContext = new StrategyContext();

    // 1. 加载渠道配置
    StrategyMarketChannelDo channelDo = strategyMarketChannelRepository.selectById(marketChannelId);
    strategyContext.setStrategyMarketChannelDo(channelDo);

    // 2. 加载策略配置
    StrategyDo strategyDo = strategyRepository.selectById(channelDo.getStrategyId());
    strategyContext.setStrategyDo(strategyDo);

    // 3. 加载分组配置
    StrategyGroupDo groupDo = strategyGroupRepository.selectById(channelDo.getStrategyGroupId());
    strategyContext.setStrategyGroupDo(groupDo);

    // 4. 加载人群包配置
    List<StrategyCrowdPackDo> crowdPackList = strategyCrowdPackRepository
        .selectListByStrategyId(strategyDo.getId());
    List<Long> crowdIds = crowdPackList.stream()
        .map(StrategyCrowdPackDo::getCrowdId)
        .collect(Collectors.toList());
    strategyContext.setCrowdIds(crowdIds);

    // 5. 设置任务信息
    strategyContext.setDispatchTaskDo(dispatchTaskDo);

    return strategyContext;
}
```

#### 4.3.3 执行记录初始化
```java
// AbstractStrategyDispatchService.beginExecute()
protected void beginExecute(StrategyContext strategyContext) {
    // 1. 创建策略执行日志
    StrategyExecLogDo execLogDo = new StrategyExecLogDo();
    execLogDo.setStrategyId(strategyContext.getStrategyDo().getId());
    execLogDo.setStrategyGroupId(strategyContext.getStrategyGroupDo().getId());
    execLogDo.setMarketChannelId(strategyContext.getStrategyMarketChannelDo().getId());
    execLogDo.setExecStatus(1); // 1=执行中
    execLogDo.setStartTime(new Date());

    // 2. 插入执行日志
    StrategyExecLogDo savedExecLog = strategyExecLogRepository.insert(execLogDo);
    strategyContext.setStrategyExecLogDo(savedExecLog);

    // 3. 创建策略快照(记录执行时的策略配置)
    StrategySnapshotDo snapshotDo = new StrategySnapshotDo();
    snapshotDo.setStrategyExecLogId(savedExecLog.getId());
    snapshotDo.setStrategyConfig(JsonUtil.toJson(strategyContext.getStrategyDo()));
    snapshotDo.setGroupConfig(JsonUtil.toJson(strategyContext.getStrategyGroupDo()));
    snapshotDo.setChannelConfig(JsonUtil.toJson(strategyContext.getStrategyMarketChannelDo()));

    strategySnapshotRepository.insert(snapshotDo);
}
```

## 5. dispatchHandler方法详细分析

### 5.1 抽象方法定义
```java
// 代码位置: cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/service/dispatch/offline/impl/AbstractStrategyDispatchService.java
// AbstractStrategyDispatchService.dispatchHandler()
protected abstract <T> ImmutablePair<Integer, CrowdPushBatchDo> dispatchHandler(
    StrategyContext strategyContext, String app, String innerApp,
    List<CrowdDetailDo> batch, List<T> templateParam);
```

### 5.2 具体实现类详细分析

#### 5.2.1 短信渠道实现
```java
// 代码位置: cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/service/dispatch/offline/impl/StrategyDispatchForSmsServiceImpl.java
// StrategyDispatchForSmsServiceImpl.dispatchHandler()
@Override
protected <T> ImmutablePair<Integer, CrowdPushBatchDo> dispatchHandler(
    StrategyContext strategyContext, String app, String innerApp,
    List<CrowdDetailDo> batch, List<T> params) {

    innerApp = StringUtils.isBlank(innerApp) ? app : innerApp;

    // 1. 转换模板参数
    List<SmsBatchWithParamArgs.Sms> smsParams = Convert.convert(
        new TypeReference<List<SmsBatchWithParamArgs.Sms>>() {}, params);

    // 2. 调用短信发送服务
    return this.sendSms(strategyContext, app, innerApp, batch, smsParams);
}

// 短信发送实现
public ImmutablePair<Integer, CrowdPushBatchDo> sendSms(StrategyContext strategyContext,
                                                       String app, String innerApp,
                                                       List<CrowdDetailDo> batch,
                                                       List<SmsBatchWithParamArgs.Sms> params) {
    // 1. 判断是否有模板参数
    if (CollectionUtils.isEmpty(params)) {
        // 无参数短信
        return batchDispatchService.sendSms(convertToDispatchDto(strategyContext), app, innerApp, batch);
    } else {
        // 有参数短信
        return batchDispatchService.sendSmsWithParam(convertToDispatchDto(strategyContext), app, innerApp, batch, params);
    }
}

// 批次大小配置
@Override
protected Integer setBatchSize() {
    return strategyConfig.getSmsBatchSize(); // 默认1000
}
```

#### 5.2.2 电销渠道实现
```java
// 代码位置: cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/service/dispatch/offline/impl/StrategyDispatchForTeleServiceImpl.java
// StrategyDispatchForTeleServiceImpl.dispatchHandler()
@Override
protected <T> ImmutablePair<Integer, CrowdPushBatchDo> dispatchHandler(
    StrategyContext strategyContext, String app, String innerApp,
    List<CrowdDetailDo> batch, List<T> params) {

    // 电销渠道不需要模板参数
    return sendTele(strategyContext, app, innerApp, batch);
}

// 电销发送实现
public ImmutablePair<Integer, CrowdPushBatchDo> sendTele(StrategyContext strategyContext,
                                                        String app, String innerApp,
                                                        List<CrowdDetailDo> batch) {
    return batchDispatchService.sendTele(convertToDispatchDto(strategyContext), app, innerApp, batch);
}

// 批次大小配置
@Override
protected Integer setBatchSize() {
    return strategyConfig.getTeleBatchSize(); // 默认500
}
```

#### 5.2.3 生活权益渠道实现
```java
// 代码位置: cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/service/dispatch/offline/impl/StrategyDispatchForLifeRightsServiceImpl.java
// StrategyDispatchForLifeRightsServiceImpl.dispatchHandler()
@Override
protected <T> ImmutablePair<Integer, CrowdPushBatchDo> dispatchHandler(
    StrategyContext strategyContext, String app, String innerApp,
    List<CrowdDetailDo> batch, List<T> params) {

    return sendBatch(strategyContext, app, innerApp, batch);
}

// 生活权益发送实现
public ImmutablePair<Integer, CrowdPushBatchDo> sendBatch(StrategyContext strategyContext,
                                                         String app, String innerApp,
                                                         List<CrowdDetailDo> batch) {
    return batchDispatchService.sendLifeRights(convertToDispatchDto(strategyContext), app, innerApp, batch);
}

// 批次大小配置
@Override
protected Integer setBatchSize() {
    return strategyConfig.getLifeRightsBatchSize(); // 默认1000
}
```

#### 5.2.4 AI语音渠道实现
```java
// 代码位置: cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/service/dispatch/offline/impl/StrategyDispatchForAiProntoServiceImpl.java
// StrategyDispatchForAiProntoServiceImpl.dispatchHandler()
@Override
protected <T> ImmutablePair<Integer, CrowdPushBatchDo> dispatchHandler(
    StrategyContext strategyContext, String app, String innerApp,
    List<CrowdDetailDo> batch, List<T> param) {

    innerApp = StringUtils.isBlank(innerApp) ? app : innerApp;

    // 1. 转换AI语音参数
    List<AiUserData> aiParams = Convert.convert(
        new TypeReference<List<AiUserData>>() {}, param);

    // 2. 调用AI语音发送服务
    return sendAiPronto(strategyContext, app, innerApp, batch, aiParams);
}

// AI语音发送实现
private ImmutablePair<Integer, CrowdPushBatchDo> sendAiPronto(StrategyContext strategyContext,
                                                             String app, String innerApp,
                                                             List<CrowdDetailDo> batch,
                                                             List<AiUserData> param) {
    if (CollectionUtils.isEmpty(param)) {
        return batchDispatchService.sendAiProto(convertToDispatchDto(strategyContext), app, innerApp, batch);
    }
    return batchDispatchService.sendAiProtoWithParam(convertToDispatchDto(strategyContext), app, innerApp, batch, param);
}

// 批次大小配置
@Override
protected Integer setBatchSize() {
    return strategyConfig.getAiProntoBatchSize(); // 默认200
}
```

### 5.3 返回值说明
- `Integer`: 成功发送的数量
- `CrowdPushBatchDo`: 批次记录对象，包含批次信息和执行状态

### 5.4 批次处理机制

#### 5.4.1 批次大小控制
```java
// AbstractStrategyDispatchService.executeDispatch()
private <T> ImmutablePair<Integer, Integer> executeDispatch(StrategyContext strategyContext,
                                                           String k1, String k2,
                                                           List<CrowdDetailDo> v2,
                                                           List<T> templateParam) {
    if (CollectionUtils.isEmpty(v2)) {
        log.warn("当前批次没有需要下发的用户,策略ID:{},渠道ID:{}",
                 strategyContext.getStrategyDo().getId(),
                 strategyContext.getStrategyMarketChannelDo().getId());
        return ImmutablePair.of(0, 0);
    }

    // 1. 获取批次大小配置
    Integer batchSize = setBatchSize();

    // 2. 分批处理
    List<List<CrowdDetailDo>> batches = Lists.partition(v2, batchSize);
    List<List<T>> paramBatches = Lists.partition(templateParam, batchSize);

    int totalSuccess = 0;

    // 3. 逐批次执行
    for (int i = 0; i < batches.size(); i++) {
        List<CrowdDetailDo> batch = batches.get(i);
        List<T> paramBatch = paramBatches.get(i);

        // 4. 调用具体实现的dispatchHandler方法
        ImmutablePair<Integer, CrowdPushBatchDo> result =
            this.dispatchHandler(strategyContext, k1, k2, batch, paramBatch);

        totalSuccess += result.getLeft();

        // 5. 记录批次执行结果
        if (result.getRight() != null) {
            crowdPushBatchRepository.insert(result.getRight());
        }
    }

    return ImmutablePair.of(totalSuccess, totalSuccess);
}
```

## 6. 流控机制详细分析

### 6.1 流控规则配置
```java
// 代码位置: cdp-domain/src/main/java/com/xftech/cdp/domain/flowctrl/service/impl/FlowCtrlCoreServiceImpl.java
// FlowCtrlCoreService.flowCtrl()
@Override
public List<CrowdDetailDo> flowCtrl(FlowCtrlDto flowCtrlDto, List<Integer> statusList) {
    if (CollectionUtils.isEmpty(flowCtrlDto.getList()) ||
        CollectionUtils.isEmpty(flowCtrlDto.getFlowCtrlRuleList())) {
        log.info("流量控制-当前批次用户数：{}，流控规则：{}",
                 flowCtrlDto.getList().size(), flowCtrlDto.getFlowCtrlRuleList());
        return flowCtrlDto.getList();
    }

    StrategyMarketChannelDo strategyMarketChannelDo = flowCtrlDto.getMarketChannelDo();
    log.info("流控规则执行: 策略id:{}, 流控规则的数量:{}",
             strategyMarketChannelDo == null ? 0 : strategyMarketChannelDo.getStrategyId(),
             flowCtrlDto.getFlowCtrlRuleList().size());

    long startTime = Instant.now().toEpochMilli();
    List<Long> passUserIdList = new ArrayList<>();

    // 1. 执行流控
    Long strategyId = strategyMarketChannelDo.getStrategyId();
    if (WhitelistSwitchUtil.commonGraySwitchByApollo("" + strategyId, "newFlowCtrlSwitch")) {
        log.info("流控规则执行: 新流控 strategyId:{} marketChannel:{}",
                 strategyId, strategyMarketChannelDo.getMarketChannel());
        passUserIdList = this.newExecute(flowCtrlDto, statusList);
    } else {
        passUserIdList = this.execute(flowCtrlDto, statusList);
    }

    // 2. 过滤通过流控的用户
    List<CrowdDetailDo> result = flowCtrlDto.getList().stream()
        .filter(x -> passUserIdList.contains(x.getUserId()))
        .collect(Collectors.toList());

    long endTime = Instant.now().toEpochMilli();
    log.info("流控规则执行完成: 策略id:{}, 流控前用户数:{}, 流控后用户数:{}, 耗时:{}ms",
             strategyId, flowCtrlDto.getList().size(), result.size(), endTime - startTime);

    return result;
}
```

### 6.2 流控检查逻辑
```java
// FlowCtrlCoreService.execute()
private List<Long> execute(FlowCtrlDto flowCtrlDto, List<Integer> statusList) {
    List<Long> passUserIdList = new ArrayList<>();
    List<FlowCtrlRuleDo> flowCtrlRuleList = flowCtrlDto.getFlowCtrlRuleList();

    // 1. 遍历每个用户
    for (CrowdDetailDo crowdDetail : flowCtrlDto.getList()) {
        boolean pass = true;

        // 2. 检查每个流控规则
        for (FlowCtrlRuleDo rule : flowCtrlRuleList) {
            if (!checkFlowCtrlRule(crowdDetail, rule, flowCtrlDto.getMarketChannelDo())) {
                pass = false;
                break;
            }
        }

        // 3. 通过所有流控规则的用户加入结果列表
        if (pass) {
            passUserIdList.add(crowdDetail.getUserId());
        }
    }

    return passUserIdList;
}

// 单个流控规则检查
private boolean checkFlowCtrlRule(CrowdDetailDo crowdDetail, FlowCtrlRuleDo rule,
                                 StrategyMarketChannelDo channelDo) {

    // 1. 构建流控Key
    String flowCtrlKey = buildFlowCtrlKey(rule, crowdDetail, channelDo);

    // 2. 获取当前计数
    Long currentCount = redisUtils.get(flowCtrlKey, Long.class);
    if (currentCount == null) {
        currentCount = 0L;
    }

    // 3. 检查是否超过限制
    if (currentCount >= rule.getMaxCount()) {
        log.debug("用户触发流控: userId={}, rule={}, currentCount={}, maxCount={}",
                  crowdDetail.getUserId(), rule.getRuleName(), currentCount, rule.getMaxCount());
        return false;
    }

    // 4. 增加计数
    redisUtils.increment(flowCtrlKey, 1L, rule.getTimeWindow());

    return true;
}

// 构建流控Key
private String buildFlowCtrlKey(FlowCtrlRuleDo rule, CrowdDetailDo crowdDetail,
                               StrategyMarketChannelDo channelDo) {
    StringBuilder keyBuilder = new StringBuilder();
    keyBuilder.append("flow_ctrl:");

    switch (rule.getRuleType()) {
        case 1: // 用户级流控
            keyBuilder.append("user:").append(crowdDetail.getUserId());
            break;
        case 2: // 策略级流控
            keyBuilder.append("strategy:").append(channelDo.getStrategyId());
            break;
        case 3: // 渠道级流控
            keyBuilder.append("channel:").append(channelDo.getMarketChannel());
            break;
        case 4: // 全局流控
            keyBuilder.append("global");
            break;
    }

    keyBuilder.append(":").append(rule.getId());
    keyBuilder.append(":").append(DateUtil.format(new Date(), "yyyyMMddHH"));

    return keyBuilder.toString();
}
```

### 6.3 流控统计与监控
```java
// 流控统计记录
private void recordFlowCtrlStats(FlowCtrlDto flowCtrlDto, List<Long> passUserIdList) {
    StrategyMarketChannelDo channelDo = flowCtrlDto.getMarketChannelDo();
    String date = DateUtil.format(new Date(), "yyyyMMdd");

    // 1. 统计流控前用户数
    String beforeKey = RedisKeyUtils.genFlowCtrlBeforeCount(date, channelDo.getStrategyId());
    redisUtils.increment(beforeKey, flowCtrlDto.getList().size(), RedisUtils.DEFAULT_EXPIRE_TWO_DAYS);

    // 2. 统计流控后用户数
    String afterKey = RedisKeyUtils.genFlowCtrlAfterCount(date, channelDo.getStrategyId());
    redisUtils.increment(afterKey, passUserIdList.size(), RedisUtils.DEFAULT_EXPIRE_TWO_DAYS);

    // 3. 统计流控拦截用户数
    int blockedCount = flowCtrlDto.getList().size() - passUserIdList.size();
    String blockedKey = RedisKeyUtils.genFlowCtrlBlockedCount(date, channelDo.getStrategyId());
    redisUtils.increment(blockedKey, blockedCount, RedisUtils.DEFAULT_EXPIRE_TWO_DAYS);
}
```

## 7. 异常处理与重试机制

### 7.1 重试机制实现
```java
// 代码位置: cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/service/DispatchTaskService.java
// DispatchTaskService.updateDispatchTaskFailedRetry()
public void updateDispatchTaskFailedRetry(DispatchTaskDo dispatchTaskDo, String errorMsg) {
    // 1. 增加重试次数
    dispatchTaskDo.setRetryTimes(dispatchTaskDo.getRetryTimes() + 1);
    dispatchTaskDo.setErrorMsg(errorMsg);
    dispatchTaskDo.setUpdateTime(new Date());

    // 2. 检查是否超过最大重试次数
    if (dispatchTaskDo.getRetryTimes() >= dispatchTaskDo.getMaxRetryTimes()) {
        // 超过最大重试次数，标记为失败
        dispatchTaskDo.setStatus(3); // 3=失败
        dispatchTaskDo.setEndTime(new Date());

        log.error("任务重试次数超限，标记为失败: taskId={}, strategyId={}, channelId={}, retryTimes={}, maxRetryTimes={}",
                  dispatchTaskDo.getId(), dispatchTaskDo.getBizId(), dispatchTaskDo.getAssociationId(),
                  dispatchTaskDo.getRetryTimes(), dispatchTaskDo.getMaxRetryTimes());

        // 发送告警
        sendRetryExceededAlarm(dispatchTaskDo);
    } else {
        // 未超过最大重试次数，重置为待执行状态
        dispatchTaskDo.setStatus(0); // 0=待执行

        // 计算下次执行时间(指数退避)
        long delayMinutes = calculateRetryDelay(dispatchTaskDo.getRetryTimes());
        Date nextDispatchTime = DateUtil.addMinutes(new Date(), (int) delayMinutes);
        dispatchTaskDo.setDispatchTime(nextDispatchTime);

        log.warn("任务执行失败，将进行重试: taskId={}, strategyId={}, channelId={}, retryTimes={}, nextDispatchTime={}",
                 dispatchTaskDo.getId(), dispatchTaskDo.getBizId(), dispatchTaskDo.getAssociationId(),
                 dispatchTaskDo.getRetryTimes(), nextDispatchTime);
    }

    // 3. 更新数据库
    dispatchTaskRepository.updateById(dispatchTaskDo);
}

// 计算重试延迟时间(指数退避)
private long calculateRetryDelay(int retryTimes) {
    // 第1次重试: 5分钟
    // 第2次重试: 10分钟
    // 第3次重试: 20分钟
    return 5L * (1L << (retryTimes - 1));
}
```

### 7.2 异常分类处理
```java
// AbstractStrategyDispatchService.execute()
public void execute(@NonNull Long marketChannelId, DispatchTaskDo dispatchTaskDo) {
    StrategyContext strategyContext = initContext(marketChannelId, dispatchTaskDo);
    try {
        beginExecute(strategyContext);
        preHandler(strategyContext);
        coreLogicExecute(strategyContext);
        successExecute(strategyContext);
        dispatchTaskService.updateTaskFinish(dispatchTaskDo, "SUCCEED");
    } catch (Exception e) {
        // 1. 异常分类处理
        handleException(dispatchTaskDo, e);
        throw e;
    }
}

private void handleException(DispatchTaskDo dispatchTaskDo, Exception e) {
    String errorMsg = e.getMessage();

    if (e instanceof StrategyException) {
        // 业务异常
        StrategyException se = (StrategyException) e;
        switch (se.getCode()) {
            case CROWD_PACK_NOT_FOUND:
            case STRATEGY_NOT_FOUND:
            case CHANNEL_NOT_FOUND:
                // 配置错误，不重试
                dispatchTaskService.updateTaskFailed(dispatchTaskDo, errorMsg);
                break;
            default:
                // 其他业务异常，进行重试
                dispatchTaskService.updateDispatchTaskFailedRetry(dispatchTaskDo, errorMsg);
                break;
        }
    } else if (e instanceof SQLException || e instanceof DataAccessException) {
        // 数据库异常，进行重试
        dispatchTaskService.updateDispatchTaskFailedRetry(dispatchTaskDo, "数据库异常: " + errorMsg);
    } else if (e instanceof ConnectException || e instanceof SocketTimeoutException) {
        // 网络异常，进行重试
        dispatchTaskService.updateDispatchTaskFailedRetry(dispatchTaskDo, "网络异常: " + errorMsg);
    } else {
        // 其他系统异常，进行重试
        dispatchTaskService.updateDispatchTaskFailedRetry(dispatchTaskDo, "系统异常: " + errorMsg);
    }
}
```

## 8. 与其他触达链路的对比分析

### 8.1 与T0实时触达的区别

| 对比维度 | 离线-普通触达 | T0实时触达 |
|----------|---------------|------------|
| **触发方式** | 定时任务调度 | 实时事件触发 |
| **数据来源** | 人群包文件(OSS) | 实时事件流 |
| **处理模式** | 批量处理 | 单个用户处理 |
| **执行时机** | 预设时间执行 | 事件发生时立即执行 |
| **数据量** | 大批量(万级-千万级) | 单个用户 |
| **延迟要求** | 分钟级-小时级 | 秒级-毫秒级 |
| **AB测试** | 基于随机数分组 | 基于随机数分组 |
| **流控机制** | 批量流控 | 实时流控 |
| **重试机制** | 任务级重试 | 事件级重试 |
| **适用场景** | 营销活动、定期推送 | 实时营销、行为触发 |

### 8.2 与离线-引擎触达的区别

| 对比维度 | 离线-普通触达 | 离线-引擎触达 |
|----------|---------------|---------------|
| **策略类型** | type=0(普通策略) | type=1(引擎策略) |
| **决策方式** | 规则配置 | AI引擎决策 |
| **执行方式** | 直接执行 | 延迟执行 |
| **分片处理** | 无分片 | 人群包分片 |
| **引擎调用** | 无 | 异步调用预测中心 |
| **延迟任务** | 无 | dispatch_user_delay表 |
| **执行时间** | 立即执行 | 引擎指定时间执行 |
| **个性化程度** | 基于规则 | 基于AI模型 |
| **复杂度** | 相对简单 | 复杂(多阶段) |
| **适用场景** | 标准营销活动 | 智能营销、个性化推荐 |

### 8.3 核心代码路径对比

#### 8.3.1 离线-普通触达核心路径
```java
XxlJob → StrategyDispatch → StrategyHandler → AbstractStrategyDispatchService
→ queryAndGroupAndSend → dispatchHandler → BatchDispatchService → 外部服务
```

#### 8.3.2 T0实时触达核心路径
```java
MQ事件 → StrategyEventDispatchServiceImpl → prescreen → rescreen
→ dispatch → EventDispatchService → 外部服务
```

#### 8.3.3 离线-引擎触达核心路径
```java
XxlJob → OfflineStrategyJobDispatch → DistributeOfflineEngineDispatchServiceImpl
→ dispatchHandler → EnginePredictionClient → dispatch_user_delay
→ StrategyEventDispatchServiceImpl.marketingSend → 外部服务
```

## 9. 性能优化与监控

### 9.1 性能优化策略

#### 9.1.1 数据库优化
```sql
-- 关键索引设计
-- crowd_detail表索引
CREATE INDEX idx_crowd_detail_query ON crowd_detail(crowd_id, crowd_exec_log_id, id, d_flag);

-- dispatch_task表索引
CREATE INDEX idx_dispatch_task_todo ON dispatch_task(status, dispatch_time, biz_type);

-- strategy_exec_log表索引
CREATE INDEX idx_strategy_exec_log_strategy ON strategy_exec_log(strategy_id, create_time);

-- user_dispatch_detail表索引
CREATE INDEX idx_user_dispatch_detail_user ON user_dispatch_detail(user_id, strategy_id, market_channel);
```

#### 9.1.2 内存优化
```java
// 分页查询避免内存溢出
private void queryAndGroupAndSend(StrategyContext context, Triple funPair) {
    // 1. 使用Roaring64Bitmap进行高效去重
    Roaring64Bitmap container = new Roaring64Bitmap();

    // 2. 分页查询，避免一次性加载大量数据
    for (Long crowdId : context.getCrowdIds()) {
        Long crowdDetailId = 0L;
        int pageSize = appConfigService.getCrowdRockPageSizeNew(); // 5000

        while (true) {
            List<CrowdDetailDo> detailList = crowdDetailRepository
                .selectByIdPage(crowdDetailId, pageSize, crowdId, crowdExecLogId);

            if (CollectionUtils.isEmpty(detailList)) {
                break;
            }

            // 3. 及时处理和释放内存
            processCurrentBatch(context, funPair, detailList, container);
            detailList.clear(); // 显式清理

            crowdDetailId = detailList.get(detailList.size() - 1).getId();
        }
    }

    // 4. 清理去重容器
    container.clear();
}
```

#### 9.1.3 并发优化
```java
// 线程池配置
@Configuration
public class DispatchThreadPoolConfig {

    @Bean("dispatchTaskExecutor")
    public ThreadPoolTaskExecutor dispatchTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);           // 核心线程数
        executor.setMaxPoolSize(50);            // 最大线程数
        executor.setQueueCapacity(1000);        // 队列容量
        executor.setKeepAliveSeconds(60);       // 线程存活时间
        executor.setThreadNamePrefix("dispatch-task-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }
}

// 异步执行任务
@Async("dispatchTaskExecutor")
public void executeTaskAsync(DispatchTaskDo task) {
    try {
        execute(Long.parseLong(task.getAssociationId()), task);
    } catch (Exception e) {
        log.error("异步执行任务失败: taskId={}", task.getId(), e);
    }
}
```

### 9.2 监控指标

#### 9.2.1 执行指标监控
```java
// 执行统计记录
private void recordExecutionMetrics(StrategyContext context, int successCount, int failCount, long duration) {
    String date = DateUtil.format(new Date(), "yyyyMMdd");
    Long strategyId = context.getStrategyDo().getId();

    // 1. 成功数统计
    String successKey = RedisKeyUtils.genStrategySuccessCount(date, strategyId);
    redisUtils.increment(successKey, successCount, RedisUtils.DEFAULT_EXPIRE_TWO_DAYS);

    // 2. 失败数统计
    String failKey = RedisKeyUtils.genStrategyFailCount(date, strategyId);
    redisUtils.increment(failKey, failCount, RedisUtils.DEFAULT_EXPIRE_TWO_DAYS);

    // 3. 执行时长统计
    String durationKey = RedisKeyUtils.genStrategyDuration(date, strategyId);
    redisUtils.set(durationKey, duration, RedisUtils.DEFAULT_EXPIRE_TWO_DAYS);

    // 4. 发送监控指标到监控系统
    MetricsCollector.recordCounter("strategy.execution.success", successCount,
                                   Tags.of("strategy_id", String.valueOf(strategyId)));
    MetricsCollector.recordCounter("strategy.execution.fail", failCount,
                                   Tags.of("strategy_id", String.valueOf(strategyId)));
    MetricsCollector.recordTimer("strategy.execution.duration", duration, TimeUnit.MILLISECONDS,
                                 Tags.of("strategy_id", String.valueOf(strategyId)));
}
```

#### 9.2.2 业务指标监控
```java
// 业务指标统计
private void recordBusinessMetrics(StrategyContext context, List<CrowdDetailDo> processedUsers) {
    String date = DateUtil.format(new Date(), "yyyyMMdd");
    Long strategyId = context.getStrategyDo().getId();

    // 1. 处理用户数统计
    String processedKey = RedisKeyUtils.genStrategyProcessedCount(date, strategyId);
    redisUtils.increment(processedKey, processedUsers.size(), RedisUtils.DEFAULT_EXPIRE_TWO_DAYS);

    // 2. 去重用户数统计(使用HyperLogLog)
    String uniqueKey = RedisKeyUtils.genStrategyUniqueCount(date, strategyId);
    processedUsers.forEach(user ->
        redisUtils.pfAdd(uniqueKey, String.valueOf(user.getUserId())));
    redisUtils.expire(uniqueKey, RedisUtils.DEFAULT_EXPIRE_TWO_DAYS);

    // 3. 渠道分布统计
    Integer marketChannel = context.getStrategyMarketChannelDo().getMarketChannel();
    String channelKey = RedisKeyUtils.genChannelCount(date, marketChannel);
    redisUtils.increment(channelKey, processedUsers.size(), RedisUtils.DEFAULT_EXPIRE_TWO_DAYS);
}
```

## 10. 总结

离线普通触达链路是一个基于定时任务的批量营销系统，通过以下五个主要阶段实现大规模用户的精准营销触达：

1. **任务生成阶段**: 定时扫描有效策略，为每个策略的每个渠道创建执行任务
2. **任务执行阶段**: 分片并行处理执行任务，初始化策略上下文和执行记录
3. **人群处理阶段**: 分页查询人群数据，进行AB测试分组和多层去重处理
4. **标签查询阶段**: 查询用户标签数据和模板参数，进行数据完整性验证
5. **营销执行阶段**: 通过`dispatchHandler`抽象方法调用具体渠道实现，执行实际的营销触达

### 10.1 核心特性

- **高性能**: 基于ID的分页查询、Roaring64Bitmap去重、分批处理等优化策略
- **高可靠**: 完善的重试机制、异常分类处理、事务控制等保障机制
- **高扩展**: 抽象方法设计支持多种渠道扩展，策略模式实现渠道路由
- **高监控**: 详细的执行指标、业务指标、系统指标监控和告警机制

### 10.2 与其他链路的差异

- **vs T0实时触达**: 批量处理 vs 实时处理，定时触发 vs 事件触发
- **vs 离线引擎触达**: 规则决策 vs AI决策，直接执行 vs 延迟执行

`dispatchHandler`方法作为整个链路的核心抽象，通过不同的实现类支持短信、电销、生活权益、AI语音等多种触达渠道，体现了良好的面向对象设计原则。整个链路具有完善的AB测试、流控、重试和监控机制，能够支持复杂的离线营销场景。
