package com.xftech.cdp.infra.client.usercenter.model;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-05-12
 */
@Data
public class UserInfoResp {

    private String custName;
    /**
     * 是否是新注册用户
     */
    //private String isNew;
    /**
     * id标识
     */
    //private String id;
    /**
     * credit_user表id
     */
    private Long creditUserId;
    /**
     * user表id
     */
    //private String userId;
    /**
     * user表id
     */
    private String app;
    /**
     * inner_app
     */
    private String innerApp;
    /**
     * 手机号明文
     */
    private String mobile;
    /**
     * 手机号密文
     */
    //private String mobileProtyle;
    /**
     * 注册数据设备来源:client 客户端,wap h5端,other 其他
     */
    //private String sourceType;
    /**
     * 注册系统类型:android 安卓,ios 苹果,other 其他
     */
    //private String os;
    /**
     * 设备市场渠道
     */
    //private String channel;
    /**
     * 用户首次注册推广渠道
     */
    //private String utmSource;
    /**
     * 用户当前注册推广渠道
     */
    //private String currentUtmSource;
    /**
     * 注册ip
     */
    //private String createdIp;
    /**
     * 注册时间
     */
    private String createdTime;
    /**
     * app版本号
     */
    //private String appVersion;
    /**
     * jwt
     */
    //private String token;
    /**
     *
     */
    //private String bizEventData;
    /**
     * Person.id
     */
    //private String personId;
    /**
     *
     */
    //private String weixinOpenId;
    /**
     * 性别
     */
    //private String gender;
    /**
     *
     */
    //private String lastLoginTime;
    /**
     *
     */
    //private String lastLoginIp;
    /**
     * 昵称
     */
    //private String name;
    /**
     * 版本号
     */
    //private String versionCode;
    /**
     * app来源
     */
    //private String appSource;
    /**
     * 状态
     */
    //private String status;
    /**
     * 密码
     */
    //private String passwordHash;
    /**
     * 是否是首次注册1:是 0:否
     */
    //private String isFirstRegister;
    /**
     * 邀请码
     */
    //private String invitationCode;
    /**
     * 用到的邀请码
     */
    //private String usedInvitationCode;
    /**
     *
     */
    //private String billStatus;
}
