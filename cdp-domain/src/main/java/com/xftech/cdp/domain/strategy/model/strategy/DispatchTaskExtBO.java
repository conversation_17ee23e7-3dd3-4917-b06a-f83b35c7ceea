/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.model.strategy;

import com.xftech.cdp.domain.strategy.model.enums.StrategyMarketChannelEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 * @version $ DispatchTaskExt, v 0.1 2023/12/27 17:05 yye.xu Exp $
 */

@Data
public class DispatchTaskExtBO {

    @Data
    public static class DispatchTaskExt {
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FlowExt extends DispatchTaskExt {
        private String flowNo;
        private boolean isRootNode;
        private StrategyMarketChannelEnum marketChannelEnum;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class NoneMarketExt extends DispatchTaskExt {
        private StrategyMarketChannelEnum marketChannelEnum;
    }
}