package com.xftech.cdp.domain.crowd.model.enums;

import com.xftech.cdp.domain.crowd.exception.CrowdException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023/2/10
 */
@AllArgsConstructor
@Getter
public enum CrowdLabelEnum {

    INCLUDE_LABEL(0, "圈选标签"),

    EXCLUDE_LABEL(1, "排除标签");


    private final int code;

    private final String description;


    public static CrowdLabelEnum ofCode(int code) {
        for (CrowdLabelEnum crowdLabelEnum : values()) {
            if (crowdLabelEnum.code == code) {
                return crowdLabelEnum;
            }
        }
        throw new CrowdException(String.format("不存在该圈选标签: %s", code));
    }

}
