package com.xftech.cdp.domain.strategy.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.infra.repository.cdp.param.po.UserDispatchFailDetailDo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class UserDispatchFailDetailRepository {
    /**
     * 根据主键id查询
     *
     * @param id id
     * @return id对应的记录
     */
    public UserDispatchFailDetailDo selectById(Long id) {
        return DBUtil.selectOne("userDispatchFailDetailMapper.selectByPrimaryKey", id);
    }

    /**
     * 插入
     *
     * @param param 对象
     * @return 是否插入成功标识
     */
    public boolean insert(UserDispatchFailDetailDo param) {
        try {
            return DBUtil.insert("userDispatchFailDetailMapper.insertSelective", param) > 0;
        }catch (Exception ex){
            log.error("insert error", ex);
        }
        return false;
    }

    /**
     * 根据主键id更新
     *
     * @param param 对象
     * @return 是否更新成功标识
     */
    public boolean updateById(UserDispatchFailDetailDo param) {
        return DBUtil.update("userDispatchFailDetailMapper.updateByPrimaryKeySelective", param) > 0;
    }

    /**
     * 批量保存
     *
     * @param list 失败用户集合
     * @return 保存成功数
     */
    public int batchSave(List<UserDispatchFailDetailDo> list) {
        try {
            return DBUtil.insertBatch("userDispatchFailDetailMapper.insertSelective", list);
        } catch (Exception exception) {
            log.error("batchSave error", exception);
        }
        return 0;
    }
}
