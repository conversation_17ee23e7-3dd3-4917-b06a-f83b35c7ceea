/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.infra.client.usercenter.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @version $ CisConfig, v 0.1 2023/11/14 16:34 yye.xu Exp $
 */

@RefreshScope
@Data
@Component
public class CisConfig {
    @Value("${cis.queryCisUserSwitch:true}")
    private boolean queryCisUserSwitch;

    @Value("${cis.queryUserNoByMobileAndAppUrl:http://cis-query.xinfei.io/standard/queryUserNoByMobileAndApp}")
    private String queryUserNoByMobileAndAppUrl;

    @Value("${cis.queryRegisterInfoByUserNoUrl:http://cis-query.xinfei.io/standard/queryRegisterInfoByUserNo}")
    private String queryRegisterInfoByUserNo;

    @Value("${cis.queryMobileByUserNoUrl:http://cis-query.xinfei.io/standard/queryMobileByUserNo}")
    private String queryMobileByUserNo;

    @Value("${cis.queryBatchUserByUserNo:http://cis-query.xinfei.io/standard/queryBatchUserByUserNo}")
    private String queryBatchUserByUserNo;
}