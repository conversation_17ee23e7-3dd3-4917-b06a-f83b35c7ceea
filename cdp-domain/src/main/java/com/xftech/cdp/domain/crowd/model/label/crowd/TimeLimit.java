package com.xftech.cdp.domain.crowd.model.label.crowd;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.xftech.cdp.domain.crowd.exception.CrowdException;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/2/13 10:57
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TimeLimit extends CrowdLabelOption {

    private Integer begin;

    private Integer end;

    /**
     * type为空 或者 0， 表示过去时间
     * type为1，表示未来时间
     */
    private Integer type;


    /**
     * column between begin and end
     */
    @Override
    public StringBuilder condition(String column, String configOptionReflect) {
        LocalDateTime now = LocalDateTimeUtil.beginOfDay(LocalDateTime.now());

        if (Objects.nonNull(type) && type == 1) {
            return super.condition(column, configOptionReflect).append(" between '").append(now.plusDays(begin).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("' and '").append(LocalDateTimeUtil.endOfDay(now.plusDays(end)).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("' ");
        }

        return super.condition(column, configOptionReflect).append(" between '").append(now.plusDays(-end).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("' and '").append(LocalDateTimeUtil.endOfDay(now.plusDays(-begin)).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("' ");
    }

    @Override
    public void verify() {
        if (begin == null || end == null) {
            throw new CrowdException("时间段数据错误！");
        }
        if (begin > end) {
            throw new CrowdException("时间段数据错误！");
        }
    }
}
