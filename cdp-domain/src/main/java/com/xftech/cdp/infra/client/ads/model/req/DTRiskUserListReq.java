package com.xftech.cdp.infra.client.ads.model.req;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @since 2023/5/23
 */

@Data
public class DTRiskUserListReq implements Cloneable {

    // 请求日期  2023-05-06
    @JsonProperty("req_date")
    @JSONField(name = "req_date")
    private String reqDate;

    // 切片（0-27）
    @JsonProperty("section")
    @JSONField(name = "section")
    private String section;

    @JsonProperty("pageSize")
    @J<PERSON>NField(name = "pageSize")
    private Integer pageSize;

    @JsonProperty("pageNum")
    @JSONField(name = "pageNum")
    private Integer pageNum;

    @Override
    public DTRiskUserListReq clone() {
        try {
            return (DTRiskUserListReq) super.clone();
        } catch (CloneNotSupportedException e) {
            throw new AssertionError();
        }
    }
}
