<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="crowd">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPackDo">
        <id column="id" property="id"/>
        <result column="crowd_name" property="crowdName"/>
        <result column="filter_method" property="filterMethod"/>
        <result column="refresh_type" property="refreshType"/>
        <result column="status" property="status"/>
        <result column="group_type" property="groupType"/>
        <result column="validity_begin" property="validityBegin"/>
        <result column="validity_end" property="validityEnd"/>
        <result column="refresh_time" property="refreshTime"/>
        <result column="h5_option" property="h5Option"/>
        <result column="cron" property="cron"/>
        <result column="xxl_job_id" property="xxlJobId"/>
        <result column="crowd_person_num" property="crowdPersonNum"/>
        <result column="pull_type" property="pullType"/>
        <result column="business_type" property="businessType"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="d_flag" property="dFlag"/>
        <result column="created_op" property="createdOp"/>
        <result column="updated_op" property="updatedOp"/>
        <result column="latest_refresh_time" property="latestRefreshTime"/>
        <result column="updated_op_mobile" property="updatedOpMobile"/>
        <result column="crowd_sql" property="crowdSql"/>
        <result column="include_sql" property="includeSql"/>
        <result column="exclude_sql" property="excludeSql"/>

    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,crowd_sql,include_sql,exclude_sql,group_type
        , crowd_name, filter_method, refresh_type, status, validity_begin, validity_end, refresh_time,h5_option, cron, xxl_job_id,crowd_person_num, pull_type, business_type,created_time, updated_time,latest_refresh_time, d_flag, created_op, updated_op, updated_op_mobile
    </sql>
    <select id="selectAllByFilterMethod" resultType="java.lang.Long">
        select id
        from crowd_pack
        where filter_method = #{filterMethod}
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crowd_pack
        where id = #{id,jdbcType=BIGINT}
        and d_flag = 0
    </select>

    <select id="selectByIdAndNotFlag" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crowd_pack
        where id = #{id,jdbcType=BIGINT}
    </select>


    <select id="selectByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crowd_pack
        <where>
            <if test="crowdName != null and crowdName != ''">
                <bind name="crowdNameTmp" value='crowdName.replaceAll("%","/%")'/>
                and crowd_name like concat('%',#{crowdNameTmp},'%') escape '/'
            </if>
            <if test="updateOp != null and updateOp != ''">
                <bind name="updateOpTmp" value='updateOp.replaceAll("%","/%")'/>
                and updated_op like concat('%',#{updateOpTmp},'%') escape '/'
            </if>
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="status == null">
                and status != 6
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="groupType != null">
                and group_type = #{groupType}
            </if>
            <if test="refreshType != null">
                and refresh_type = #{refreshType}
            </if>
            <if test="filterMethod != null">
                and filter_method = #{filterMethod}
            </if>
            <if test="startTime != null and endTime != null">
                and updated_time between #{startTime} and #{endTime}
            </if>
            <if test="crowdPackIds != null and crowdPackIds.size() > 0">
                and id in
                <foreach collection="crowdPackIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            and d_flag = 0 and pull_type = 0 and business_type = #{businessType}
            order by updated_time desc
        </where>
    </select>

    <select id="queryPageByLabelId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from `crowd_pack` where status != 6 and id in(
        select distinct crowd_id from crowd_label
        where
        label_id = #{labelCodeId}
        and `d_flag` =0
        union
        select distinct crowd_id from crowd_label_sub
        where
        label_id = #{labelCodeId}
        and `d_flag` =0)
        order by business_type,id desc
    </select>

    <select id="querySizeByLabelId" resultType="integer">
        select
        count(*)
        from `crowd_pack` where status != 6 and id in(
        select distinct crowd_id from crowd_label
        where
        label_id = #{labelCodeId}
        and `d_flag` =0
        union
        select distinct crowd_id from crowd_label_sub
        where
        label_id = #{labelCodeId}
        and `d_flag` =0)
        order by id desc
    </select>

    <select id="selectNoStrategyIdCrowdPack" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crowd_pack cp left join (select distinct crowd_pack_id from strategy_crowd_pack where d_flag = 0) as scp on cp.id = scp.crowd_pack_id
        <where>
            scp.crowd_pack_id is null
            <if test="crowdName != null and crowdName != ''">
                <bind name="crowdNameTmp" value='crowdName.replaceAll("%","/%")'/>
                and crowd_name like concat('%',#{crowdNameTmp},'%') escape '/'
            </if>
            <if test="updateOp != null and updateOp != ''">
                <bind name="updateOpTmp" value='updateOp.replaceAll("%","/%")'/>
                and updated_op like concat('%',#{updateOpTmp},'%') escape '/'
            </if>
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="status == null">
                and status != 6
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="groupType != null">
                and group_type = #{groupType}
            </if>
            <if test="refreshType != null">
                and refresh_type = #{refreshType}
            </if>
            <if test="filterMethod != null">
                and filter_method = #{filterMethod}
            </if>
            <if test="startTime != null and endTime != null">
                and updated_time between #{startTime} and #{endTime}
            </if>
            and d_flag = 0 and pull_type = 0 and business_type = #{businessType}
            order by updated_time desc
        </where>
    </select>

    <select id="getCrowdPackList" resultMap="BaseResultMap">
        select id, crowd_name, updated_time
        from crowd_pack
        where filter_method = 1
        <if test="crowdName != null and crowdName != ''">
            <bind name="crowdNameTmp" value='crowdName.replaceAll("%","/%")'/>
            and crowd_name like concat('%',#{crowdNameTmp},'%') escape '/'
        </if>
        and status not in (5,6) and business_type = #{businessType} and d_flag = 0
        union
        select id, crowd_name, updated_time
        from crowd_pack
        where filter_method = 0
        <if test="crowdName != null and crowdName != ''">
            <bind name="crowdNameTmp" value='crowdName.replaceAll("%","/%")'/>
            and crowd_name like concat('%',#{crowdNameTmp},'%') escape '/'
        </if>
        and status = 5 and business_type = #{businessType} and d_flag = 0
        order by updated_time desc
    </select>

    <select id="queryAllEffectiveCrowdsByTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crowd_pack
        where validity_begin &lt;= #{time}
        and validity_end &gt;= #{time}
        and filter_method = 1
        and status not in (4,6)
        and d_flag = 0
    </select>

    <select id="getCrowdPackAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crowd_pack
        where
        filter_method = 1
        and d_flag = 0
        and pull_type=#{pullType}
    </select>

    <select id="startCrowdPackJob" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crowd_pack
        where validity_begin &lt;= #{time}
        and validity_end &gt;= #{time}
        and refresh_type = 1
        and filter_method = 1
        and pull_type = #{pullType}
        and status = 0
        and d_flag = 0
    </select>

    <select id="queryAllExpireCrowdsByTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crowd_pack
        where filter_method = 1
          and validity_end &lt; #{time}
          and status != 6
          and d_flag = 0
    </select>

    <select id="selectMinExecTimeBo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crowd_pack
        where d_flag = 0 and filter_method = 1 and pull_type = #{pullType} and validity_begin &lt;= #{time} and
        validity_end &gt;= #{time} and refresh_time is not null
        order by refresh_time
        limit 1
    </select>

    <select id="queryLeRefreshTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crowd_pack
        where refresh_time &lt;= #{time}
        and validity_begin &lt;= #{time}
        and validity_end &gt;= #{time}
        and refresh_type = 1
        and filter_method = 1
        and pull_type = #{pullType}
        and status = 0
        and d_flag = 0
    </select>

    <select id="selectByNameAndBusinessType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crowd_pack
        where crowd_name = #{crowdName} and business_type = #{businessType}
        and d_flag = 0
        limit 1
    </select>

    <select id="selectEffectiveCrowd" resultType="long">
        select id
        from crowd_pack
        where filter_method = 1 and status = 2 and d_flag = 0 and id in
        <foreach collection="crowdIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>

        union

        select id
        from crowd_pack
        where filter_method = 0 and status = 5 and d_flag = 0 and id in
        <foreach collection="crowdIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>

    </select>

    <select id="selectCrowdPackByIds" resultMap="BaseResultMap">
        select id, filter_method
        from crowd_pack
        where d_flag = 0 and id in
        <foreach collection="crowdIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and business_type = #{businessType}
    </select>

    <select id="selectByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crowd_pack
        where d_flag = 0 and id in
        <foreach collection="crowdIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="countCrowdPackNum" resultType="integer">
        select count(1)
        from crowd_pack
        where d_flag = 0 and id in
        <foreach collection="crowdIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="countCrowdPackUserNum" resultType="integer">
        select ifnull(sum(crowd_person_num), 0)
        from crowd_pack
        where d_flag = 0 and id in
        <foreach collection="crowdIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectAllCrowdIdsByTypeNoDFlag" resultType="long">
        select id
        from crowd_pack
        where filter_method = #{filterMethod}
          and d_flag = 0
    </select>

    <select id="existExpiredCrowd" resultType="integer">
        select 1 from crowd_pack
        where id in
        <foreach collection="crowdIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and filter_method = 1 and status = 5 and d_flag = 0 limit 1
    </select>

    <select id="refreshT0CrowdPack" resultMap="BaseResultMap">
        select * from crowd_pack where status in (2, 5)
    </select>

    <select id="selectTodayByStatus" resultMap="BaseResultMap">
        select * from crowd_pack where d_flag = 0 and date(latest_refresh_time) = current_date() and status in
        <foreach collection="list" item="status" index="index" open="(" separator="," close=")">
            #{status}
        </foreach>
    </select>

    <select id="selectTodayReportCrowd" resultMap="BaseResultMap">
        select * from crowd_pack
        where d_flag = 0
        and status not in (4,6)
        and filter_method = 1
        and refresh_type = 1
        and validity_begin &lt;= current_date()
        and validity_end &gt;= current_date()
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        update crowd_pack
        set d_flag = 1
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPackDo" useGeneratedKeys="true">
        insert into crowd_pack
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="crowdName != null">crowd_name,</if>
            <if test="filterMethod != null">filter_method,</if>
            <if test="refreshType != null">refresh_type,</if>
            <if test="status != null">status,</if>
            <if test="groupType != null">group_type,</if>
            <if test="validityBegin != null">validity_begin,</if>
            <if test="validityEnd != null">validity_end,</if>
            <if test="refreshTime != null">refresh_time,</if>
            <if test="h5Option != null">h5_option,</if>
            <if test="cron != null">cron,</if>
            <if test="xxlJobId != null">xxl_job_id,</if>
            <if test="crowdPersonNum != null">crowd_person_num,</if>
            <if test="pullType != null">pull_type,</if>
            <if test="latestRefreshTime != null">latest_refresh_time,</if>
            <if test="businessType != null">business_type,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updatedTime != null">updated_time,</if>
            <if test="dFlag != null">d_flag,</if>
            <if test="createdOp != null">created_op,</if>
            <if test="updatedOp != null">updated_op,</if>
            <if test="updatedOpMobile != null">updated_op_mobile,</if>
            <if test="crowdSql != null">crowd_sql,</if>
            <if test="includeSql != null">include_sql,</if>
            <if test="excludeSql != null">exclude_sql,</if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="crowdName != null">#{crowdName,jdbcType=VARCHAR},</if>
            <if test="filterMethod != null">#{filterMethod,jdbcType=SMALLINT},</if>
            <if test="refreshType != null">#{refreshType,jdbcType=SMALLINT},</if>
            <if test="status != null">#{status,jdbcType=SMALLINT},</if>
            <if test="groupType != null">#{groupType,jdbcType=SMALLINT},</if>
            <if test="validityBegin != null">#{validityBegin,jdbcType=TIMESTAMP},</if>
            <if test="validityEnd != null">#{validityEnd,jdbcType=TIMESTAMP},</if>
            <if test="refreshTime != null">#{refreshTime,jdbcType=TIME},</if>
            <if test="h5Option != null">#{h5Option,jdbcType=VARCHAR},</if>
            <if test="cron != null">#{cron,jdbcType=VARCHAR},</if>
            <if test="xxlJobId != null">#{xxlJobId,jdbcType=INTEGER},</if>
            <if test="crowdPersonNum != null">#{crowdPersonNum,jdbcType=INTEGER},</if>
            <if test="pullType != null">#{pullType,jdbcType=INTEGER},</if>
            <if test="latestRefreshTime != null">#{latestRefreshTime,jdbcType=TIMESTAMP},</if>
            <if test="businessType != null">#{businessType,jdbcType=VARCHAR},</if>
            <if test="createdTime != null">#{createdTime,jdbcType=TIMESTAMP},</if>
            <if test="updatedTime != null">#{updatedTime,jdbcType=TIMESTAMP},</if>
            <if test="dFlag != null">#{dFlag,jdbcType=SMALLINT},</if>
            <if test="createdOp != null">#{createdOp,jdbcType=VARCHAR},</if>
            <if test="updatedOp != null">#{updatedOp,jdbcType=VARCHAR},</if>
            <if test="updatedOpMobile != null">#{updatedOpMobile,jdbcType=VARCHAR},</if>
            <if test="crowdSql != null">#{crowdSql,jdbcType=VARCHAR},</if>
            <if test="includeSql != null">#{includeSql,jdbcType=VARCHAR},</if>
            <if test="excludeSql != null">#{excludeSql,jdbcType=VARCHAR},</if>

        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPackDo">
        update crowd_pack
        <set>
            <if test="crowdName != null">
                crowd_name = #{crowdName,jdbcType=VARCHAR},
            </if>
            <if test="filterMethod != null">
                filter_method = #{filterMethod,jdbcType=SMALLINT},
            </if>
            <if test="refreshType != null">
                refresh_type = #{refreshType,jdbcType=SMALLINT},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=SMALLINT},
            </if>
            <if test="groupType != null">
                group_type = #{groupType,jdbcType=SMALLINT},
            </if>
            <if test="validityBegin != null">
                validity_begin = #{validityBegin,jdbcType=TIMESTAMP},
            </if>
            <if test="validityEnd != null">
                validity_end = #{validityEnd,jdbcType=TIMESTAMP},
            </if>
            <if test="refreshTime != null">
                refresh_time = #{refreshTime,jdbcType=TIME},
            </if>
            <if test="h5Option != null">
                h5_option = #{h5Option,jdbcType=VARCHAR},
            </if>
            <if test="cron != null">
                cron = #{cron,jdbcType=VARCHAR},
            </if>
            <if test="xxlJobId != null">
                xxl_job_id = #{xxlJobId,jdbcType=INTEGER},
            </if>
            <if test="crowdPersonNum != null">
                crowd_person_num = #{crowdPersonNum,jdbcType=INTEGER},
            </if>
            <if test="pullType != null">
                pull_type = #{pullType,jdbcType=INTEGER},
            </if>
            <if test="latestRefreshTime != null">
                latest_refresh_time = #{latestRefreshTime,jdbcType=TIMESTAMP},
            </if>
            <if test="businessType != null">
                business_type = #{businessType,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedTime != null">
                updated_time = #{updatedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="dFlag != null">
                d_flag = #{dFlag,jdbcType=SMALLINT},
            </if>
            <if test="createdOp != null">
                created_op = #{createdOp,jdbcType=VARCHAR},
            </if>
            <if test="updatedOp != null">
                updated_op = #{updatedOp,jdbcType=VARCHAR},
            </if>
            <if test="updatedOpMobile != null">
                updated_op_mobile = #{updatedOpMobile,jdbcType=VARCHAR},
            </if>
            <if test="crowdSql != null">
                crowd_sql = #{crowdSql,jdbcType=VARCHAR},
            </if>
            <if test="includeSql != null">
                include_sql = #{includeSql,jdbcType=VARCHAR},
            </if>
            <if test="excludeSql != null">
                exclude_sql = #{excludeSql,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectBigDataErrorResultCrowdPacks" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from `crowd_pack`
        where date(`latest_refresh_time`) = #{date}
        and `crowd_person_num` &lt;= 0
        and `status` = 2
        and `filter_method` = 1
        and `refresh_type` = 1
        and `validity_begin` &lt;= current_date()
        and `validity_end` &gt;= current_date()
        and `d_flag` = 0
    </select>

    <update id="updateBigDataErrorResultCrowdPacks">
        update `crowd_pack` set `status` = 1, `crowd_person_num` = 0 where `id` in
        <foreach collection="crowdIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>
