package com.xftech.cdp.domain.strategy.service;

import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyExecLogDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyGroupDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketChannelDo;

import java.util.List;

/**
 * @<NAME_EMAIL>
 */
public interface StrategyExecLogService {
    /**
     * 获取渠道对应的执行日志（默认当天的执行日志）
     *
     * @param group         策略分组
     * @param marketChannel 当前渠道
     * @return 执行日志
     */
    StrategyExecLogDo channelExecLog(StrategyGroupDo group, StrategyMarketChannelDo marketChannel);

    /**
     * 根据ID更新执行日志
     *
     * @param execLogDo 执行日志
     */
    void updateById(StrategyExecLogDo execLogDo);

    /**
     * 当天结束后把日志状态修改为已结束
     */
    
    void strategyEventRefreshExecLogStatus();

    /**
     * 刷新执行日志
     */
    void strategyEventExecLogRefresh(String param);

    List<StrategyExecLogDo> selectTodayByStrategyIds(List<Long> strategyIds);
}
