/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2025. All Rights Reserved.
 */

package com.xftech.cdp.api.dto.marketing.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version $ ActivityDetailResponse, v 0.1 2025/1/4 15:14 你的邮箱前缀 Exp $
 */
@NoArgsConstructor
@Data
@ApiModel("查询用户活动详情页")
public class ActivityDetailResponse extends ActivityBaseResponse {

    @ApiModelProperty(value = "活动名称")
    private String activityName;

    @ApiModelProperty(value = "活动倒计时")
    private Long countdown;

    @ApiModelProperty(value = "是否命中人群：0-未命中，1-命中")
    private Integer hitCrowd;

    public ActivityDetailResponse(String bizCode, String bizMessage) {
        super(bizCode, bizMessage);
    }
}
