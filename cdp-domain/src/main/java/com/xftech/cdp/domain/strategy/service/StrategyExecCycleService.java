/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.service;

import cn.hutool.core.thread.ThreadUtil;
import com.xftech.cdp.domain.strategy.repository.StrategyExecCycleRepository;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyExecCycleDo;
import com.xftech.cdp.infra.utils.RedisUtils;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 * @version $ StrategyExecCycleService, v 0.1 2023/11/3 14:37 yye.xu Exp $
 */

@Service
@AllArgsConstructor
public class StrategyExecCycleService {
    private RedisUtils redisUtils;
    private StrategyExecCycleRepository strategyExecCycleRepository;

    public void insert(StrategyExecCycleDo cycleDo) {
        String lockKey = String.format("lockStrategyExecCycle:%s", cycleDo.getStrategyId());
        for (; ; ) {
            boolean ret = redisUtils.lock(lockKey, "1", 200);
            if (ret) {
                StrategyExecCycleDo cycleCounterDo = selectStrategyCycle(cycleDo.getStrategyId(), cycleDo.getDateValue());
                if (cycleCounterDo == null) {
                    strategyExecCycleRepository.insert(cycleDo);
                }
                redisUtils.unlock(lockKey);
                break;
            }
            ThreadUtil.sleep(10);
        }
    }

    public StrategyExecCycleDo selectStrategyCycle(Long strategyId, Integer dateValue) {
        return strategyExecCycleRepository.selectStrategyCycle(strategyId, dateValue);
    }
}