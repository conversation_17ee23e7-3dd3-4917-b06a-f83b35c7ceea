# 离线触达分析报告

## 1. 系统架构概述

### 1.1 离线触达两种模式

根据代码分析，离线触达系统包含两种不同的执行模式：

#### 1.1.1 离线-引擎触达
- **触发方式**: XxlJob定时任务 `DISPATCH_USER_DELAY_EXECUTE`
- **核心入口**: `StrategyHandler.dealDispatchUserDelayDo()` → `StrategyEventDispatchServiceImpl.marketingSend()`
- **特点**: 基于AI引擎决策结果的延迟执行触达，先调用引擎获得决策，再延迟执行
- **数据流**: 引擎决策 → dispatch_user_delay表 → 延迟执行 → marketingSend → EventDispatchService
- **存储表**: 使用event_push_batch表记录批次信息

#### 1.1.2 离线-普通触达
- **触发方式**: XxlJob定时任务 `STRATEGY_DISPATCH_TASK_EXECCUTE`
- **核心入口**: `AbstractStrategyDispatchService.dispatchHandler()`
- **特点**: 基于人群包的直接批量触达执行，无需AI引擎决策
- **数据流**: 人群包查询 → 分组过滤 → dispatchHandler → BatchDispatchService
- **存储表**: 使用crowd_push_batch表记录批次信息

## 2. 完整调用链路图

### 2.1 离线-引擎触达调用链路

```mermaid
graph TD
    A[XxlJob: DISPATCH_USER_DELAY_EXECUTE] --> B[StrategyHandler.strategyDispatchOfflineEngine]
    B --> C[StrategyHandler.dealDispatchUserDelayDos]
    C --> D[StrategyHandler.dealDispatchUserDelayDo]
    D --> E[StrategyEventDispatchServiceImpl.marketingSend]
    E --> F{渠道类型判断}
    F -->|SMS| G[EventDispatchServiceImpl.sendSmsEvent]
    F -->|VOICE| H[EventDispatchServiceImpl.sendTeleEvent]
    F -->|COUPON| I[EventDispatchServiceImpl.sendCouponEvent]
    F -->|PUSH| J[EventDispatchServiceImpl.sendPushEvent]
    G --> K[插入event_push_batch表]
    H --> K
    I --> K
    J --> K
    K --> L[插入user_dispatch_detail表]
    L --> M[更新dispatch_user_delay状态]
```

### 2.2 离线-普通触达调用链路

```mermaid
graph TD
    A[XxlJob: STRATEGY_DISPATCH_TASK_EXECCUTE] --> B[StrategyHandler.execute]
    B --> C[AbstractStrategyDispatchService.execute]
    C --> D[AbstractStrategyDispatchService.coreLogicExecute]
    D --> E[AbstractStrategyDispatchService.queryAndGroupAndSend]
    E --> F[AbstractStrategyDispatchService.dispatchHandler抽象方法]
    F --> G[AbstractStrategyDispatchService.executeDispatch]
    G --> H{具体实现类dispatchHandler}
    H -->|SMS| I[StrategyDispatchForSmsServiceImpl.dispatchHandler]
    H -->|VOICE| J[StrategyDispatchForTeleServiceImpl.dispatchHandler]
    H -->|COUPON| K[StrategyDispatchForCouponServiceImpl.dispatchHandler]
    I --> L[BatchDispatchServiceImpl.sendSms]
    J --> M[BatchDispatchServiceImpl.sendTele]
    K --> N[BatchDispatchServiceImpl.sendCoupon]
    L --> O[插入crowd_push_batch表]
    M --> O
    N --> O
    O --> P[插入user_dispatch_detail表]
```

## 3. 关键类和方法对应关系

### 3.1 离线-引擎触达核心类

| 类名 | 方法名 | 职责 | 代码位置 |
|------|--------|------|----------|
| **StrategyHandler** | dealDispatchUserDelayDo | 处理单个延迟任务 | cdp-domain/src/main/java/com/xftech/cdp/application/StrategyHandler.java:500-570 |
| **StrategyEventDispatchServiceImpl** | marketingSend | 执行营销触达 | cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/service/dispatch/event/impl/StrategyEventDispatchServiceImpl.java:1258-1400 |
| **EventDispatchServiceImpl** | sendSmsEvent | 单用户短信触达 | cdp-domain/src/main/java/com/xftech/cdp/domain/dispatch/impl/EventDispatchServiceImpl.java:118-140 |
| **EventDispatchServiceImpl** | sendTeleEvent | 单用户电销触达 | cdp-domain/src/main/java/com/xftech/cdp/domain/dispatch/impl/EventDispatchServiceImpl.java:154-180 |

### 3.2 离线-普通触达核心类

| 类名 | 方法名 | 职责 | 代码位置 |
|------|--------|------|----------|
| **AbstractStrategyDispatchService** | dispatchHandler(抽象方法) | 抽象下发处理方法 | cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/service/dispatch/offline/impl/AbstractStrategyDispatchService.java:1460 |
| **StrategyDispatchForSmsServiceImpl** | dispatchHandler | 短信渠道具体实现 | cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/service/dispatch/offline/impl/StrategyDispatchForSmsServiceImpl.java:85-88 |
| **StrategyDispatchForTeleServiceImpl** | dispatchHandler | 电销渠道具体实现 | cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/service/dispatch/offline/impl/StrategyDispatchForTeleServiceImpl.java:51-53 |
| **BatchDispatchServiceImpl** | sendSms/sendTele | 批量下发服务实现 | cdp-domain/src/main/java/com/xftech/cdp/domain/dispatch/impl/BatchDispatchServiceImpl.java:106-330 |

## 4. 方法调用时序图

### 4.1 离线-引擎触达时序图

```mermaid
sequenceDiagram
    participant XJ as XxlJob
    participant SH as StrategyHandler
    participant SEDS as StrategyEventDispatchServiceImpl
    participant EDS as EventDispatchServiceImpl
    participant DB as Database
    
    XJ->>SH: strategyDispatchOfflineEngine()
    SH->>SH: 查询dispatch_user_delay表
    loop 处理每个延迟任务
        SH->>SH: dealDispatchUserDelayDo()
        SH->>SEDS: marketingSend()
        SEDS->>SEDS: 构建DispatchDto和批次信息
        SEDS->>EDS: sendSmsEvent/sendTeleEvent()
        EDS->>DB: 插入event_push_batch表
        EDS->>DB: 插入user_dispatch_detail表
        EDS-->>SEDS: 返回发送结果
        SEDS->>DB: 更新dispatch_user_delay状态
    end
```

### 4.2 离线-普通触达时序图

```mermaid
sequenceDiagram
    participant XJ as XxlJob
    participant SH as StrategyHandler
    participant ASD as AbstractStrategyDispatchService
    participant SDS as StrategyDispatchServiceImpl
    participant BDS as BatchDispatchService
    participant DB as Database
    
    XJ->>SH: execute()
    SH->>ASD: execute()
    ASD->>ASD: 查询策略和人群包
    ASD->>ASD: coreLogicExecute()
    loop 分页处理人群
        ASD->>ASD: queryAndGroupAndSend()
        ASD->>ASD: dispatchHandler()
        ASD->>SDS: dispatchHandler()
        SDS->>BDS: sendSms/sendTele()
        BDS->>DB: 插入crowd_push_batch表
        BDS->>DB: 插入user_dispatch_detail表
    end
```

## 5. 数据表流转关系

### 5.1 离线-引擎触达数据流转

```mermaid
graph LR
    A[dispatch_user_delay<br/>延迟任务表] --> B[StrategyEventDispatchServiceImpl<br/>marketingSend方法]
    B --> C[EventDispatchServiceImpl<br/>单用户事件触达]
    C --> D[event_push_batch<br/>事件批次表]
    C --> E[user_dispatch_detail<br/>触达明细表]
    D --> F[外部渠道系统<br/>短信/电销/优惠券]
    E --> F
```

### 5.2 离线-普通触达数据流转

```mermaid
graph LR
    A[strategy<br/>策略表] --> B[crowd_detail<br/>人群明细表]
    B --> C[AbstractStrategyDispatchService<br/>dispatchHandler抽象方法]
    C --> D[BatchDispatchServiceImpl<br/>批量触达服务]
    D --> E[crowd_push_batch<br/>人群批次表]
    D --> F[user_dispatch_detail<br/>触达明细表]
    E --> G[外部渠道系统<br/>短信/电销/优惠券]
    F --> G
```

## 6. 详细数据库操作说明

### 6.1 离线-引擎触达数据库操作

#### 6.1.1 核心表结构
```sql
-- dispatch_user_delay表：延迟执行任务表
CREATE TABLE dispatch_user_delay (
    id BIGINT PRIMARY KEY,
    strategy_id BIGINT,           -- 策略ID
    user_id BIGINT,              -- 用户ID
    market_channel SMALLINT,      -- 营销渠道(1=短信,2=电销,3=优惠券)
    group_name VARCHAR(255),      -- 分组名称(引擎返回)
    ext_info TEXT,               -- 扩展信息(引擎决策结果)
    dispatch_time TIMESTAMP,     -- 执行时间
    status SMALLINT,             -- 状态(0=待执行,1=已执行)
    date_value INT,              -- 日期值
    create_time TIMESTAMP,
    updated_time TIMESTAMP
);

-- event_push_batch表：事件推送批次表
CREATE TABLE event_push_batch (
    id BIGINT PRIMARY KEY,
    strategy_id BIGINT,          -- 策略ID
    user_id BIGINT,              -- 用户ID
    mobile VARCHAR(20),          -- 手机号
    market_channel SMALLINT,     -- 营销渠道
    batch_num VARCHAR(50),       -- 批次号
    group_name VARCHAR(255),     -- 分组名称
    template_id VARCHAR(50),     -- 模板ID
    status SMALLINT,             -- 发送状态
    created_time TIMESTAMP
);
```

#### 6.1.2 查询操作
```sql
-- 1. 查询到期的延迟任务 (StrategyHandler.strategyDispatchOfflineEngine)
SELECT * FROM dispatch_user_delay
WHERE status = 0 AND dispatch_time <= NOW()
AND date_value = ?
ORDER BY id LIMIT 1000;

-- 2. 查询用户详细信息 (StrategyHandler.dealDispatchUserDelayDo)
SELECT * FROM crowd_detail
WHERE user_id = ? AND crowd_id = ?;

-- 3. 查询策略配置信息
SELECT * FROM strategy WHERE id = ?;
SELECT * FROM strategy_group WHERE id = ?;
SELECT * FROM strategy_market_channel WHERE strategy_group_id = ?;
```

#### 6.1.3 插入操作
```sql
-- 1. 插入事件批次记录 (EventDispatchServiceImpl.sendSmsEvent)
INSERT INTO event_push_batch (
    batch_num, strategy_id, user_id, mobile,
    market_channel, group_name, template_id,
    status, created_time
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW());

-- 2. 插入用户触达明细 (StrategyEventDispatchServiceImpl.marketingSend)
INSERT INTO user_dispatch_detail (
    user_id, mobile, batch_num, strategy_id,
    market_channel, dispatch_time, status,
    strategy_group_id, group_name, template_id
) VALUES (?, ?, ?, ?, ?, NOW(), ?, ?, ?, ?);
```

#### 6.1.4 更新操作
```sql
-- 更新延迟任务状态为已执行 (StrategyHandler.dealDispatchUserDelayDo)
UPDATE dispatch_user_delay
SET status = 1, updated_time = NOW()
WHERE id = ?;
```

### 6.2 离线-普通触达数据库操作

#### 6.2.1 核心表结构
```sql
-- crowd_push_batch表：人群推送批次表
CREATE TABLE crowd_push_batch (
    id BIGINT PRIMARY KEY,
    batch_num VARCHAR(50),       -- 批次号
    strategy_id BIGINT,          -- 策略ID
    market_channel SMALLINT,     -- 营销渠道
    batch_size INT,              -- 批次大小
    send_status SMALLINT,        -- 发送状态
    created_time TIMESTAMP
);

-- dispatch_task表：分发任务表
CREATE TABLE dispatch_task (
    id BIGINT PRIMARY KEY,
    strategy_id BIGINT,          -- 策略ID
    market_channel_id BIGINT,    -- 渠道ID
    status SMALLINT,             -- 任务状态
    biz_type VARCHAR(50),        -- 业务类型
    start_time TIMESTAMP,
    end_time TIMESTAMP
);
```

#### 6.2.2 查询操作
```sql
-- 1. 查询有效策略 (StrategyHandler.execute)
SELECT * FROM strategy
WHERE type = 0 AND status IN (1,2)
AND validity_begin <= NOW() AND validity_end >= NOW();

-- 2. 查询待执行任务
SELECT * FROM dispatch_task
WHERE status = 0 ORDER BY id LIMIT 100;

-- 3. 分页查询人群明细 (AbstractStrategyDispatchService.queryAndGroupAndSend)
SELECT * FROM crowd_detail
WHERE crowd_id = ?
ORDER BY id LIMIT ?, ?;

-- 4. 查询策略分组配置
SELECT * FROM strategy_group WHERE strategy_id = ?;
SELECT * FROM strategy_market_channel WHERE strategy_group_id = ?;
```

#### 6.2.3 插入操作
```sql
-- 1. 创建分发任务 (StrategyHandler.createDispatchTask)
INSERT INTO dispatch_task (
    strategy_id, market_channel_id, status,
    biz_type, start_time
) VALUES (?, ?, 0, ?, NOW());

-- 2. 插入人群批次记录 (BatchDispatchService.sendSms)
INSERT INTO crowd_push_batch (
    batch_num, strategy_id, market_channel,
    batch_size, send_status, created_time
) VALUES (?, ?, ?, ?, ?, NOW());

-- 3. 批量插入用户触达明细 (AbstractStrategyDispatchService.dispatchHandler)
INSERT INTO user_dispatch_detail (
    user_id, mobile, batch_num, strategy_id,
    market_channel, dispatch_time, status,
    strategy_channel_id, crowd_pack_id
) VALUES (?, ?, ?, ?, ?, NOW(), ?, ?, ?);

-- 4. 插入策略执行日志
INSERT INTO strategy_exec_log (
    strategy_id, strategy_group_id, market_channel_id,
    exec_status, start_time
) VALUES (?, ?, ?, 1, NOW());
```

#### 6.2.4 更新操作
```sql
-- 1. 更新任务状态为成功 (AbstractStrategyDispatchService.execute)
UPDATE dispatch_task
SET status = 2, end_time = NOW()
WHERE id = ?;

-- 2. 更新策略执行日志
UPDATE strategy_exec_log
SET exec_status = 2, end_time = NOW(),
    group_count = ?, total_count = ?, send_count = ?
WHERE id = ?;
```

## 7. 数据操作时序流程

### 7.1 离线-引擎触达时序流程

```sql
-- 阶段一：XxlJob任务执行 (每分钟执行)
-- 1. 查询到期延迟任务
SELECT * FROM dispatch_user_delay
WHERE status = 0 AND dispatch_time <= NOW()
ORDER BY id LIMIT 1000;

-- 阶段二：处理每个延迟任务
-- 2. 查询用户详细信息
SELECT * FROM crowd_detail WHERE user_id = ? AND crowd_id = ?;

-- 3. 查询策略配置
SELECT * FROM strategy WHERE id = ?;
SELECT * FROM strategy_group WHERE id = ?;

-- 4. 生成批次号并插入批次记录
INSERT INTO event_push_batch (...) VALUES (...);

-- 5. 调用外部渠道服务 (短信/电销/优惠券)

-- 6. 插入用户触达明细
INSERT INTO user_dispatch_detail (...) VALUES (...);

-- 7. 更新延迟任务状态
UPDATE dispatch_user_delay SET status = 1 WHERE id = ?;

-- 8. Redis统计操作
PFADD off_engine_send_num:{date}:{strategyId} {userId};
```

### 7.2 离线-普通触达时序流程

```sql
-- 阶段一：任务生成 (每分钟执行)
-- 1. 查询有效策略
SELECT * FROM strategy WHERE type = 0 AND status IN (1,2);

-- 2. 创建分发任务
INSERT INTO dispatch_task (...) VALUES (...);

-- 阶段二：任务执行 (每分钟执行)
-- 3. 查询待执行任务
SELECT * FROM dispatch_task WHERE status = 0;

-- 4. 查询策略配置
SELECT * FROM strategy_group WHERE strategy_id = ?;
SELECT * FROM strategy_market_channel WHERE strategy_group_id = ?;

-- 5. 插入执行日志
INSERT INTO strategy_exec_log (...) VALUES (...);

-- 6. 分页查询人群明细
SELECT * FROM crowd_detail WHERE crowd_id = ? LIMIT ?, ?;

-- 7. AB测试分组过滤 + 流控检查

-- 8. 插入批次记录
INSERT INTO crowd_push_batch (...) VALUES (...);

-- 9. 调用外部渠道服务

-- 10. 批量插入触达明细
INSERT INTO user_dispatch_detail (...) VALUES (...);

-- 11. 更新任务状态
UPDATE dispatch_task SET status = 2 WHERE id = ?;

-- 12. 更新执行日志
UPDATE strategy_exec_log SET exec_status = 2 WHERE id = ?;
```

## 8. Apollo配置说明

### 8.1 关键配置项

根据Apollo配置文件分析，离线触达相关的重要配置：

```properties
# 延迟任务异步处理开关
dispatchUserDelay.async.switch = 1

# 引擎策略执行下发任务开关
XxlJobTelStrategyDispatchDelayEnable = true

# 分发任务执行器线程池配置
DispatchUserDelayExecutor.pool.coreSize = 20
DispatchUserDelayExecutor.pool.maxSize = 60

# 各渠道分页大小配置
strategy.dispatch.channel.sms.pagesize = 1000
strategy.dispatch.channel.tele.pagesize = 1500
strategy.dispatch.channel.coupon.pagesize = 1000

# 流控相关配置
singleDispatchFlc.1 = false
singleDispatchFlc.2 = false
dispatchFlcExecutor.pool.coreSize = 8
dispatchFlcExecutor.pool.maxSize = 20
```

### 8.2 外部服务配置

```properties
# 短信服务配置
cdp.sms.host = http://sms.xinfei.io

# 电销服务配置
cdp.tele.host = http://inner-api-telemarketing-backend.xinfei.io/cdp
cdp.tele.newHost = http://telemkt.xinfei.io

# 优惠券服务配置
cdp.coupon.host = http://inner-coupon-api.xinyongfei.io
cdp.coupon.newHost = http://userassetcore.xinfei.io

# AI引擎平台配置
xf.model.platform.prediction = http://open-business-engine.xinfei.io/marketing_model/prediction
```

## 9. 关键差异对比分析

### 9.1 架构设计差异

| 对比维度 | 离线-引擎触达 | 离线-普通触达 |
|----------|---------------|---------------|
| **触发机制** | 基于延迟任务表的定时扫描 | 基于策略配置的任务生成 |
| **数据源** | dispatch_user_delay表 | crowd_detail表 |
| **执行方式** | 单个用户逐一处理 | 批量用户并行处理 |
| **决策逻辑** | AI引擎预决策结果 | 实时分组规则匹配 |
| **存储表** | event_push_batch | crowd_push_batch |
| **核心服务** | EventDispatchService(单用户) | BatchDispatchService(批量) |
| **调用方法** | marketingSend | dispatchHandler(抽象方法) |

### 9.2 性能特点差异

| 性能维度 | 离线-引擎触达 | 离线-普通触达 |
|----------|---------------|---------------|
| **并发度** | 中等(50个任务/批次) | 高(按人群包大小) |
| **延迟性** | 支持延迟执行 | 立即执行 |
| **吞吐量** | 中等(单用户处理) | 高(批量处理) |
| **资源消耗** | 中等 | 高 |
| **处理模式** | 逐个用户处理 | 批量用户处理 |

### 9.3 业务场景差异

| 业务场景 | 离线-引擎触达 | 离线-普通触达 |
|----------|---------------|---------------|
| **适用策略** | AI引擎策略 | 普通离线策略、周期策略 |
| **决策复杂度** | 高(AI模型决策) | 中等(规则匹配) |
| **个性化程度** | 高 | 中等 |
| **执行精度** | 精确到分钟级 | 批次级 |
| **触达服务** | 事件触达服务 | 批量触达服务 |

## 10. 核心技术差异总结

### 10.1 服务层差异

**离线-引擎触达**使用的是**EventDispatchService**，这是一个**单用户事件触达服务**：
- 处理单个用户的触达请求
- 使用`event_push_batch`表记录批次信息
- 调用`sendSmsEvent`、`sendTeleEvent`等单用户方法
- 适合个性化、精准的触达场景

**离线-普通触达**使用的是**BatchDispatchService**，这是一个**批量触达服务**：
- 处理批量用户的触达请求
- 使用`crowd_push_batch`表记录批次信息
- 调用`sendSms`、`sendTele`等批量方法
- 适合大规模、批量的触达场景

### 10.2 方法调用差异

**离线-引擎触达**：
```java
// 调用链路
StrategyHandler.dealDispatchUserDelayDo()
  → StrategyEventDispatchServiceImpl.marketingSend()
    → EventDispatchServiceImpl.sendSmsEvent() // 单用户方法
```

**离线-普通触达**：
```java
// 调用链路
AbstractStrategyDispatchService.dispatchHandler() // 抽象方法
  → StrategyDispatchForSmsServiceImpl.dispatchHandler() // 具体实现
    → BatchDispatchServiceImpl.sendSms() // 批量方法
```

### 10.3 设计模式差异

**离线-引擎触达**：
- 使用**策略模式**，通过`marketingSend`统一处理不同渠道
- 基于**事件驱动**的单用户处理模式

**离线-普通触达**：
- 使用**模板方法模式**，`dispatchHandler`是抽象方法，由子类实现
- 基于**批量处理**的高吞吐量模式

## 11. 总结

离线触达系统通过两种不同的执行模式，满足了不同业务场景的需求：

1. **离线-引擎触达**：适用于需要AI引擎决策的复杂营销场景，使用EventDispatchService进行单用户精准触达，支持延迟执行和个性化触达

2. **离线-普通触达**：适用于基于人群包的批量营销场景，使用BatchDispatchService进行批量处理，具有高吞吐量和并发处理能力

两种模式在服务层设计、方法调用、设计模式等方面存在显著差异，体现了不同的业务需求和技术实现思路。
