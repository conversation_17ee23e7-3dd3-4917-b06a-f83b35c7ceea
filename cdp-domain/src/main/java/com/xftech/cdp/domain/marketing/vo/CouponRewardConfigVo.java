/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2025. All Rights Reserved.
 */

package com.xftech.cdp.domain.marketing.vo;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version $ CouponRewardConfigVo, v 0.1 2025/1/8 13:44 xu.fan Exp $
 */
@Data
public class CouponRewardConfigVo {

    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 奖励有效时间
     */
    private Date startTime;

    /**
     * 奖励失效时间
     */
    private Date endTime;

    /**
     * 奖品名称
     */
    private String rewardName;

    /**
     * 奖品类型 1:优惠券
     */
    private Integer rewardType;

    /**
     * 奖品ID
     */
    private String rewardId;

    /**
     * 奖品优先级,数值越大优先级越高
     */
    private Integer rewardPriority;

    /**
     * 奖品人群包id,多个以英文逗号分隔
     */
    private String rewardCrowdPack;

    /**
     * 是否兜底奖品 0:否;1:是
     */
    private Integer rewardFallback;

}
