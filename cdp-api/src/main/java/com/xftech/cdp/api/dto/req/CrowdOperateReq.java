package com.xftech.cdp.api.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2023/2/24
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CrowdOperateReq extends BaseReq {

    @ApiModelProperty(value = "人群id")
    private Long crowdId;

    @ApiModelProperty(value = "执行类型：0-暂停,1-开启,2-刷新，3-删除，4-恢复")
    private Integer runType;
}
