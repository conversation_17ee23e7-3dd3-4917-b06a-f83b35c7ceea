//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.xftech.cdp.infra.exception;

import com.fasterxml.jackson.databind.exc.InvalidFormatException;
import com.xftech.cdp.api.dto.base.Response;
import com.xftech.cdp.domain.crowd.exception.CrowdException;
import com.xftech.cdp.domain.strategy.exception.StrategyException;
import com.xftech.cdp.infra.utils.MixedUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolationException;
import java.util.List;
import java.util.Objects;

@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    @ResponseBody
    @ExceptionHandler({BizException.class, CrowdException.class, InfraException.class, StrategyException.class})
    public Response<?> bizExceptionHandler(HttpServletRequest request, HttpServletResponse response, Exception e) {
        printErrorMsg(request, e);
        response.setStatus(HttpStatus.OK.value());
        int code = ((BizException) e).getCode();
        return code == 0 ? Response.fail(MixedUtil.getErrorMsg(e)) : Response.fail(code, MixedUtil.getErrorMsg(e));
    }

    @ResponseBody
    @ExceptionHandler({MethodArgumentNotValidException.class})
    public Response<?> methodArgumentNotValidExceptionHandler(HttpServletRequest request, HttpServletResponse response, Exception e) {
        printErrorMsg(request, e);
        response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
        return Response.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), getErrorMsg(e));
    }


    @ResponseBody
    @ExceptionHandler({BindException.class})
    public Response<?> bindExceptionExceptionHandler(HttpServletRequest request, HttpServletResponse response, Exception e) {
        printErrorMsg(request, e);
        response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
        return Response.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), getErrorMsg(e));
    }


    @ResponseBody
    @ExceptionHandler({ConstraintViolationException.class})
    public Response<?> constraintViolationExceptionExceptionHandler(HttpServletRequest request, HttpServletResponse response, Exception e) {
        printErrorMsg(request, e);
        response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
        return Response.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), getErrorMsg(e));
    }

    @ResponseBody
    @ExceptionHandler({IllegalArgumentException.class})
    public Response<?> illegalArgumentExceptionHandler(HttpServletRequest request, HttpServletResponse response, Exception e) {
        printErrorMsg(request, e);
        response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
        return Response.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), getErrorMsg(e));
    }

    @ResponseBody
    @ExceptionHandler({MissingServletRequestParameterException.class})
    public Response<?> missingServletRequestParameterExceptionHandler(HttpServletRequest request, HttpServletResponse response, Exception e) {
        printErrorMsg(request, e);
        response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
        return Response.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), getErrorMsg(e));
    }

    @ResponseBody
    @ExceptionHandler({InvalidFormatException.class})
    public Response<?> invalidFormatExceptionHandler(HttpServletRequest request, HttpServletResponse response, Exception e) {
        printErrorMsg(request, e);
        response.setStatus(HttpStatus.BAD_REQUEST.value());
        return Response.fail(HttpStatus.BAD_REQUEST.value(), "参数异常");
    }

    @ResponseBody
    @ExceptionHandler({Throwable.class})
    public Response<?> defaultErrorHandler(HttpServletRequest request, HttpServletResponse response, Exception e) {
        printErrorMsg(request, e);
        response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
        return Response.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), HttpStatus.INTERNAL_SERVER_ERROR.getReasonPhrase());
    }

    private void printErrorMsg(HttpServletRequest request, Exception e) {
        LOGGER.warn("Request[{}] error.", request.getRequestURI(), e);
    }

    private String getErrorMsg(Exception e) {
        if (e instanceof MethodArgumentNotValidException) {
            return doGetErrorMsg(((MethodArgumentNotValidException) e).getBindingResult().getAllErrors());
        } else if (e instanceof BindException) {
            return doGetErrorMsg(((BindException) e).getBindingResult().getAllErrors());
        }
        return MixedUtil.getErrorMsg(e);
    }

    private String doGetErrorMsg(List<ObjectError> allErrors) {
        Objects.requireNonNull(allErrors);
        StringBuilder sb = new StringBuilder();
        for (ObjectError error : allErrors) {
            if (error instanceof FieldError) {
                sb.append("[").append(((FieldError) error).getField()).append(error.getDefaultMessage()).append("]");
            } else {
                sb.append("[").append(error.getObjectName()).append(error.getDefaultMessage()).append("]");
            }
        }
        return sb.toString();
    }


}
