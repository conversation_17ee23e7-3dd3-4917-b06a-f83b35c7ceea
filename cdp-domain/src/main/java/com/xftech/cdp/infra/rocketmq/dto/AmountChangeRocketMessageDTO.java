package com.xftech.cdp.infra.rocketmq.dto;

import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version $ AmountChangeMqDTO, v 0.1 2025/3/27 14:47 tianshuo.qiu Exp $
 */
@Data
public class AmountChangeRocketMessageDTO {
    /**
     * 客户号
     */
    @JSONField(name = "cust_no")
    @JsonProperty("cust_no")
    String custNo;
    /**
     * 用户号
     */
    @JSONField(name = "user_no")
    @JsonProperty("user_no")
    String userNo;

    /**
     * app
     */
    @JSONField(name = "app")
    @JsonProperty("app")
    String app;
    /**
     * inner_app
     */
    @JSONField(name = "inner_app")
    @JsonProperty("inner_app")
    String innerApp;
    /**
     * 调额结果 S:成功,F:失败
     */
    @JSONField(name = "adjust_result")
    @JsonProperty("adjust_result")
    String adjustResult;

    /**
     * 调额类型
     * cxh用户还款提额到xyf01:   cxh_repay_xyf01_increase_amount
     * 首登调额: personal_api_fst_login_temp
     */
    @JSONField(name = "manage_type")
    @JsonProperty("manage_type")
    String manageType;

    @JSONField(name = "trigger_datetime")
    @JsonProperty("trigger_datetime")
    String triggerDatetime;

    /**
     * 调额额外数据
     */
    @JSONField(name = "extra_data")
    @JsonProperty("extra_data")
    AmountChangeExtraData extraData;

    //额度输出的额外数据如下：
    @Data
    public class AmountChangeExtraData {
        /**
         * 调整前的额用户额度，单位分
         */
        @JSONField(name = "ori_amount")
        @JsonProperty("ori_amount")
        String oriAmount;

        /**
         * 调整后的额用户额度，单位分
         */
        @JSONField(name = "final_amount")
        @JsonProperty("final_amount")
        String finalAmount;

        /**
         * 调整的额度，单位分
         */
        @JSONField(name = "adjust_amount")
        @JsonProperty("adjust_amount")
        String adjustAmount;
        /**
         * 调整后的可用额度，单位分
         */
        @JSONField(name = "available_amount")
        @JsonProperty("available_amount")
        String availableAmount;


        /**
         * 临时额度调整的开始时间
         */
        @JSONField(name = "start_time")
        @JsonProperty("start_time")
        String startTime;


        /**
         * 临时额度调整的结束时间
         */
        @JSONField(name = "end_time")
        @JsonProperty("end_time")
        String endTime;

        /**
         * 调整决策的结果返回
         */
        @JSONField(name = "decision_result")
        @JsonProperty("decision_result")
        JSONObject decisionResult;
    }
}
