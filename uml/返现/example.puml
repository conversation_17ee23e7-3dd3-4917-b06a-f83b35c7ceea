@startuml
left to right direction

' 定义参与者
actor 用户 as user
actor 管理员 as admin
' 定义系统边界
rectangle 系统边界 {
  ' 定义用例
  usecase (UC1: 登录系统) as login
  usecase (UC2: 查看信息) as viewInfo
  usecase (UC3: 编辑信息) as editInfo
  usecase (UC4: 删除信息) as deleteInfo
  usecase (UC5: 管理用户) as manageUsers
  ' 定义包含关系
  login .> viewInfo : 包含查看信息
  login .> editInfo : 包含编辑信息

  ' 定义泛化关系
  admin -|> user : 泛化
}
' 定义关联
user --> login
user --> viewInfo
user --> editInfo
admin --> manageUsers

@enduml