package com.xftech.cdp.infra.client.sms.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * <AUTHOR> Zch
 * @since : 2023-05-11
 */
@Getter
@Setter
public class SmsSingleSendArgs {

    @JsonProperty("mobile")
    @JSONField(name = "mobile")
    private String mobile;

    @JsonProperty("userNo")
    @JSONField(name = "userNo")
    private Long userNo;

    @JsonProperty("template_id")
    @JSONField(name = "template_id")
    private String templateId;

    @JsonProperty("data")
    @J<PERSON>NField(name = "data")
    private Map<String, Object> data;

    @JsonProperty("app")
    @JSONField(name = "app")
    private String app;

    @JsonProperty("inner_app")
    @JSONField(name = "inner_app")
    private String innerApp;

    @JsonProperty("signatureKey")
    @JSONField(name = "signatureKey")
    private String signatureKey;

}
