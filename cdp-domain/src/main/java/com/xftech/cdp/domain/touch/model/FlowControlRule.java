package com.xftech.cdp.domain.touch.model;

import lombok.Data;

/**
 * 流控规则模型
 * 对应现有的FlowCtrlDo，提供统一的流控规则定义
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-23
 */
@Data
public class FlowControlRule {
    
    /**
     * 规则ID
     */
    private Long ruleId;
    
    /**
     * 规则名称
     */
    private String ruleName;
    
    /**
     * 流控类型
     */
    private FlowControlType type;
    
    /**
     * 流控范围
     */
    private FlowControlScope scope;
    
    /**
     * 限制次数
     */
    private Integer limitTimes;
    
    /**
     * 限制时间（秒）
     */
    private Integer limitSeconds;
    
    /**
     * 规则状态
     */
    private RuleStatus status;
    
    /**
     * 规则优先级（数字越小优先级越高）
     */
    private Integer priority;
    
    /**
     * 规则描述
     */
    private String description;
    
    /**
     * 流控类型枚举
     */
    public enum FlowControlType {
        EVENT("EVENT", "事件级流控"),
        TOUCH("TOUCH", "触达级流控"),
        DISTRIBUTED("DISTRIBUTED", "分布式流控"),
        BATCH("BATCH", "批量流控");
        
        private final String code;
        private final String name;
        
        FlowControlType(String code, String name) {
            this.code = code;
            this.name = name;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getName() {
            return name;
        }
    }
    
    /**
     * 流控范围枚举
     */
    public enum FlowControlScope {
        USER("USER", "用户级"),
        STRATEGY("STRATEGY", "策略级"),
        CHANNEL("CHANNEL", "渠道级"),
        GLOBAL("GLOBAL", "全局级");
        
        private final String code;
        private final String name;
        
        FlowControlScope(String code, String name) {
            this.code = code;
            this.name = name;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getName() {
            return name;
        }
    }
    
    /**
     * 规则状态枚举
     */
    public enum RuleStatus {
        ACTIVE("ACTIVE", "生效"),
        INACTIVE("INACTIVE", "失效"),
        SUSPENDED("SUSPENDED", "暂停");
        
        private final String code;
        private final String name;
        
        RuleStatus(String code, String name) {
            this.code = code;
            this.name = name;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getName() {
            return name;
        }
    }
    
    /**
     * 创建事件级流控规则
     */
    public static FlowControlRule createEventRule(Long ruleId, Integer limitTimes, Integer limitSeconds) {
        FlowControlRule rule = new FlowControlRule();
        rule.setRuleId(ruleId);
        rule.setType(FlowControlType.EVENT);
        rule.setScope(FlowControlScope.USER);
        rule.setLimitTimes(limitTimes);
        rule.setLimitSeconds(limitSeconds);
        rule.setStatus(RuleStatus.ACTIVE);
        rule.setPriority(1);
        return rule;
    }
    
    /**
     * 创建触达级流控规则
     */
    public static FlowControlRule createTouchRule(Long ruleId, Integer limitTimes, Integer limitSeconds) {
        FlowControlRule rule = new FlowControlRule();
        rule.setRuleId(ruleId);
        rule.setType(FlowControlType.TOUCH);
        rule.setScope(FlowControlScope.USER);
        rule.setLimitTimes(limitTimes);
        rule.setLimitSeconds(limitSeconds);
        rule.setStatus(RuleStatus.ACTIVE);
        rule.setPriority(2);
        return rule;
    }
    
    /**
     * 创建分布式流控规则
     */
    public static FlowControlRule createDistributedRule(Long ruleId, Integer limitTimes, Integer limitSeconds) {
        FlowControlRule rule = new FlowControlRule();
        rule.setRuleId(ruleId);
        rule.setType(FlowControlType.DISTRIBUTED);
        rule.setScope(FlowControlScope.STRATEGY);
        rule.setLimitTimes(limitTimes);
        rule.setLimitSeconds(limitSeconds);
        rule.setStatus(RuleStatus.ACTIVE);
        rule.setPriority(3);
        return rule;
    }
    
    /**
     * 创建批量流控规则
     */
    public static FlowControlRule createBatchRule(Long ruleId, Integer limitTimes, Integer limitSeconds) {
        FlowControlRule rule = new FlowControlRule();
        rule.setRuleId(ruleId);
        rule.setType(FlowControlType.BATCH);
        rule.setScope(FlowControlScope.GLOBAL);
        rule.setLimitTimes(limitTimes);
        rule.setLimitSeconds(limitSeconds);
        rule.setStatus(RuleStatus.ACTIVE);
        rule.setPriority(4);
        return rule;
    }
    
    /**
     * 判断规则是否生效
     */
    public boolean isActive() {
        return RuleStatus.ACTIVE.equals(status);
    }
    
    /**
     * 判断是否为事件级流控
     */
    public boolean isEventFlowControl() {
        return FlowControlType.EVENT.equals(type);
    }
    
    /**
     * 判断是否为触达级流控
     */
    public boolean isTouchFlowControl() {
        return FlowControlType.TOUCH.equals(type);
    }
    
    /**
     * 判断是否为分布式流控
     */
    public boolean isDistributedFlowControl() {
        return FlowControlType.DISTRIBUTED.equals(type);
    }
    
    /**
     * 判断是否为批量流控
     */
    public boolean isBatchFlowControl() {
        return FlowControlType.BATCH.equals(type);
    }
    
    /**
     * 验证规则
     */
    public void validate() {
        if (ruleId == null) {
            throw new IllegalArgumentException("ruleId不能为空");
        }
        if (type == null) {
            throw new IllegalArgumentException("type不能为空");
        }
        if (scope == null) {
            throw new IllegalArgumentException("scope不能为空");
        }
        if (limitTimes == null || limitTimes <= 0) {
            throw new IllegalArgumentException("limitTimes必须大于0");
        }
        if (limitSeconds == null || limitSeconds <= 0) {
            throw new IllegalArgumentException("limitSeconds必须大于0");
        }
        if (status == null) {
            throw new IllegalArgumentException("status不能为空");
        }
    }
}
