<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="crowdUtmDecisionRecord">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xftech.cdp.domain.stat.entity.CrowdUtmDecisionRecordEntity">
        <id column="id" property="id"/>
        <result column="crowd_id" property="crowdId"/>
        <result column="crowd_exec_log_id" property="crowdExecLogId"/>
        <result column="app_user_id" property="appUserId"/>
        <result column="app" property="app"/>
        <result column="register_time" property="registerTime"/>
        <result column="mobile_utm_source" property="mobileUtmSource"/>
        <result column="history_borrow_utm_source" property="historyBorrowUtmSource"/>
        <result column="last_loan_success_utm_source" property="lastLoanSuccessUtmSource"/>
        <result column="trace_id" property="traceId"/>
        <result column="decision_result" property="decisionResult"/>
        <result column="fail_code" property="failCode"/>
        <result column="fail_reason" property="failReason"/>
        <result column="decision_detail" property="decisionDetail"/>
        <result column="created_time" property="createdTime"/>
    </resultMap>

    <insert id="saveCrowdUtmDecisionRecord" keyColumn="id" keyProperty="id"
            parameterType="com.xftech.cdp.domain.stat.entity.CrowdUtmDecisionRecordEntity"
            useGeneratedKeys="true">
        insert into ${tableName}
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="crowdId != null">crowd_id,</if>
            <if test="crowdExecLogId != null">crowd_exec_log_id,</if>
            <if test="appUserId != null">app_user_id,</if>
            <if test="app != null">app,</if>
            <if test="registerTime != null">register_time,</if>
            <if test="mobileUtmSource != null">mobile_utm_source,</if>
            <if test="historyBorrowUtmSource != null">history_borrow_utm_source,</if>
            <if test="lastLoanSuccessUtmSource != null">last_loan_success_utm_source,</if>
            <if test="traceId != null">trace_id,</if>
            <if test="decisionResult != null">decision_result,</if>
            <if test="failCode != null">fail_code,</if>
            <if test="failReason != null">fail_reason,</if>
            <if test="decisionDetail != null">decision_detail,</if>
            <if test="createdTime != null">created_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="crowdId != null">#{crowdId},</if>
            <if test="crowdExecLogId != null">#{crowdExecLogId},</if>
            <if test="appUserId != null">#{appUserId},</if>
            <if test="app != null">#{app},</if>
            <if test="registerTime != null">#{registerTime},</if>
            <if test="mobileUtmSource != null">#{mobileUtmSource},</if>
            <if test="historyBorrowUtmSource != null">#{historyBorrowUtmSource},</if>
            <if test="lastLoanSuccessUtmSource != null">#{lastLoanSuccessUtmSource},</if>
            <if test="traceId != null">#{traceId},</if>
            <if test="decisionResult != null">#{decisionResult},</if>
            <if test="failCode != null">#{failCode},</if>
            <if test="failReason != null">#{failReason},</if>
            <if test="decisionDetail != null">#{decisionDetail},</if>
            <if test="createdTime != null">#{createdTime},</if>
        </trim>
    </insert>

</mapper>
