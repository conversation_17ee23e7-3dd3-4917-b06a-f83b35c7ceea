package com.xftech.cdp.domain.strategy.factory;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xftech.cdp.api.dto.base.TimeFormat;
import com.xftech.cdp.api.dto.resp.AbsMonitorListResp;
import com.xftech.cdp.domain.strategy.model.enums.StrategyExecStatusEnum;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyExecLogDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * @<NAME_EMAIL>
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class MonitorListFilterGroupResp extends AbsMonitorListResp {
    @ApiModelProperty(value = "日期")
    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    @ExcelProperty(index = 0, value = "日期")
    protected LocalDateTime dateTime;

    @ApiModelProperty(value = "实时标签过滤总人数")
    @ExcelProperty(index = 1, value = "实时标签过滤总人数")
    protected String execCount;

    @ApiModelProperty(value = "被实时标签过滤用户数")
    @ExcelProperty(index = 2, value = "被实时标签过滤用户数")
    protected String sendCount;

    @ApiModelProperty(value = "被营销排除项过滤的用户数")
    @ExcelProperty(index = 3, value = "被营销排除项过滤的用户数")
    protected String receiveCount;

    public MonitorListFilterGroupResp convertRes(StrategyExecLogDo item) {
        MonitorListFilterGroupResp monitorListFilterGroupResp = new MonitorListFilterGroupResp();
        monitorListFilterGroupResp.setDateTime(item.getExecTime());
        monitorListFilterGroupResp.setExecCount(String.valueOf(item.getExecCount()));
        monitorListFilterGroupResp.setSendCount(String.valueOf(item.getSendCount()));
        monitorListFilterGroupResp.setReceiveCount(String.valueOf(item.getReceiveCount()));
        return monitorListFilterGroupResp;
    }
}
