package com.xinfei.touch.domain.model.unified;

/**
 * 触达模式枚举
 * 用于区分单用户触达和批量触达
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-23
 */
public enum TouchMode {
    
    /**
     * 单用户触达
     * 适用于：T0普通触达、T0引擎触达
     */
    SINGLE("SINGLE", "单用户触达", "针对单个用户的实时触达"),
    
    /**
     * 批量触达
     * 适用于：离线普通触达、离线引擎触达
     */
    BATCH("BATCH", "批量触达", "针对多个用户的批量触达");
    
    private final String code;
    private final String name;
    private final String description;
    
    TouchMode(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取枚举
     */
    public static TouchMode fromCode(String code) {
        for (TouchMode mode : values()) {
            if (mode.getCode().equals(code)) {
                return mode;
            }
        }
        throw new IllegalArgumentException("未知的触达模式代码: " + code);
    }
}
