package com.xftech.cdp.feign.common;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class BizResponse<T> {

    private String code;

    private String message;

    private T data;

    public static boolean isSuccess(BizResponse bizResponse) {
        return bizResponse != null && (StringUtils.equals(bizResponse.getCode(), "0000000"));
    }

    public static boolean successData(BizResponse bizResponse) {
        return isSuccess(bizResponse) && bizResponse.getData() != null;
    }

}
