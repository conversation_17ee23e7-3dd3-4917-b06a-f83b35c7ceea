<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="metaLabelMapper">
  <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.label.po.MetaLabelDo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="data_id" jdbcType="BIGINT" property="dataId" />
    <result column="label_name" jdbcType="VARCHAR" property="labelName" />
    <result column="label_code" jdbcType="VARCHAR" property="labelCode" />
    <result column="label_data_value_type" jdbcType="VARCHAR" property="labelDataValueType" />
    <result column="agg_dimension" jdbcType="VARCHAR" property="aggDimension" />
    <result column="available" jdbcType="SMALLINT" property="available" />
    <result column="online" jdbcType="SMALLINT" property="online" />
    <result column="check_result" jdbcType="VARCHAR" property="checkResult" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
    <result column="d_flag" jdbcType="SMALLINT" property="dFlag" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.xftech.cdp.infra.repository.cdp.label.po.MetaLabelDo">
    <result column="label_enum_value" jdbcType="LONGVARCHAR" property="labelEnumValue" />
  </resultMap>
  <resultMap id="joinLabelResultMap" type="com.xftech.cdp.domain.label.dto.MetaLabelJoinLabelDto">
    <id column="ml_id" jdbcType="BIGINT" property="mlId" />
    <result column="data_id" jdbcType="BIGINT" property="dataId" />
    <result column="label_name" jdbcType="VARCHAR" property="labelName" />
    <result column="label_code" jdbcType="VARCHAR" property="labelCode" />
    <result column="label_data_value_type" jdbcType="VARCHAR" property="labelDataValueType" />
    <result column="agg_dimension" jdbcType="VARCHAR" property="aggDimension" />
    <result column="available" jdbcType="SMALLINT" property="available" />
    <result column="label_enum_value" jdbcType="LONGVARCHAR" property="labelEnumValue" />
    <result column="online" jdbcType="SMALLINT" property="online" />
    <result column="check_result" jdbcType="VARCHAR" property="checkResult" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
    <result column="d_flag" jdbcType="SMALLINT" property="dFlag" />

    <result column="l_id"  property="lId"/>
    <result column="primary_label" property="primaryLabel"/>
    <result column="secondary_label" property="secondaryLabel"/>
    <result column="l_label_name" property="lLabelName"/>
    <result column="label_type" property="labelType"/>
    <result column="data_warehouse_field" property="dataWarehouseField"/>
    <result column="configuration_option_type" property="configurationOptionType"/>
    <result column="exchange_type" property="exchangeType"/>
    <result column="configuration_option" property="configurationOption"/>
    <result column="configuration_reflect" property="configurationReflect"/>
    <result column="description" property="description"/>
    <result column="business_type" property="businessType"/>
    <result column="l_created_time" property="lCreatedTime"/>
    <result column="l_updated_time" property="lUpdatedTime"/>
    <result column="l_d_flag" property="lDFlag"/>
    <result column="l_created_op" property="lCreatedOp"/>
    <result column="l_updated_op" property="lUpdatedOp"/>
  </resultMap>
  <sql id="Base_Column_List">
    `id`, `data_id`, `label_name`, `label_code`, `label_data_value_type`, `agg_dimension`, 
    `available`, `online`, `check_result`, `created_time`, `updated_time`,
    `d_flag`
  </sql>
  <sql id="join_label_Column_List">
    ml.id as ml_id, ml.data_id, ml.label_name, ml.label_code, ml.label_data_value_type, ml.label_enum_value, ml.agg_dimension,
    ml.available, ml.online, ml.check_result, ml.created_time, ml.updated_time, ml.d_flag,
	l.id as l_id, l.primary_label, l.secondary_label, l.label_name as l_label_name, l.label_type, l.data_warehouse_field,
	l.configuration_option_type,l.exchange_type, l.configuration_option, l.configuration_reflect, l.description,l.business_type,
    l.created_time as l_created_time, l.updated_time as l_updated_time, l.d_flag as l_d_flag, l.created_op as l_created_op, l.updated_op as l_updated_op
  </sql>
  <sql id="Blob_Column_List">
    `label_enum_value`
  </sql>

  <select id="queryJoinLabelPage"  resultMap="joinLabelResultMap">
    select
    <include refid="join_label_Column_List"/>
    from meta_label ml left join label l on ml.label_code = l.data_warehouse_field and l.d_flag=0 and ml.d_flag = 0 and ml.available = 1
    <where>
      ml.d_flag = 0 and ml.available = 1
      <if test="metaLabelCode != null and metaLabelCode != ''">
        <bind name="metaLabelCodeTmp" value='metaLabelCode.replaceAll("%","/%")'/>
        AND ml.label_code like concat('%',#{metaLabelCodeTmp},'%') escape '/'
      </if>
      <if test="metaLabelName != null and metaLabelName != ''">
        <bind name="metaLabelNameTmp" value='metaLabelName.replaceAll("%","/%")'/>
        AND ml.label_name like concat('%',#{metaLabelNameTmp},'%') escape '/'
      </if>
      <if test="online != null">
        and ml.online = #{online}
      </if>
      <if test="firstLevelLabel != null">
        and l.primary_label = #{firstLevelLabel}
      </if>
      <if test="secondLevelLabel != null">
        and l.secondary_label = #{secondLevelLabel}
      </if>
      order by ml.online,l.created_time desc
    </where>
  </select>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from meta_label
    where `id` = #{id,jdbcType=BIGINT}
  </select>

  <update id="clearCheckResultById" parameterType="java.lang.Long">
    update meta_label
    set check_result=null
    where `id` = #{id,jdbcType=BIGINT}
  </update>

  <select id="exitsMetaLabel" resultType="integer">
    select count(*)
    from meta_label
    where `label_code` = #{labelCode}
      and d_flag = 0
  </select>

  <select id="queryByLabelCode"  resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from meta_label
    where `label_code` = #{labelCode,jdbcType=VARCHAR}
    limit 1
  </select>

  <insert id="insert" parameterType="com.xftech.cdp.infra.repository.cdp.label.po.MetaLabelDo">
    insert into meta_label (`id`, `data_id`, `label_name`, 
      `label_code`, `label_data_value_type`, `agg_dimension`, 
      `available`, `online`,
      `check_result`, `created_time`, `updated_time`,
      `d_flag`, `label_enum_value`)
    values (#{id,jdbcType=BIGINT}, #{dataId,jdbcType=BIGINT}, #{labelName,jdbcType=VARCHAR}, 
      #{labelCode,jdbcType=VARCHAR}, #{labelDataValueType,jdbcType=VARCHAR}, #{aggDimension,jdbcType=VARCHAR}, 
      #{available,jdbcType=SMALLINT}, #{online,jdbcType=SMALLINT},
      #{checkResult,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{updatedTime,jdbcType=TIMESTAMP},
      #{dFlag,jdbcType=SMALLINT}, #{labelEnumValue,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.xftech.cdp.infra.repository.cdp.label.po.MetaLabelDo">
    insert into meta_label
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="dataId != null">
        `data_id`,
      </if>
      <if test="labelName != null">
        `label_name`,
      </if>
      <if test="labelCode != null">
        `label_code`,
      </if>
      <if test="labelDataValueType != null">
        `label_data_value_type`,
      </if>
      <if test="aggDimension != null">
        `agg_dimension`,
      </if>
      <if test="available != null">
        `available`,
      </if>
      <if test="online != null">
        `online`,
      </if>
      <if test="checkResult != null">
        `check_result`,
      </if>
      <if test="createdTime != null">
        `created_time`,
      </if>
      <if test="updatedTime != null">
        `updated_time`,
      </if>
      <if test="dFlag != null">
        `d_flag`,
      </if>
      <if test="labelEnumValue != null">
        `label_enum_value`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="dataId != null">
        #{dataId,jdbcType=BIGINT},
      </if>
      <if test="labelName != null">
        #{labelName,jdbcType=VARCHAR},
      </if>
      <if test="labelCode != null">
        #{labelCode,jdbcType=VARCHAR},
      </if>
      <if test="labelDataValueType != null">
        #{labelDataValueType,jdbcType=VARCHAR},
      </if>
      <if test="aggDimension != null">
        #{aggDimension,jdbcType=VARCHAR},
      </if>
      <if test="available != null">
        #{available,jdbcType=SMALLINT},
      </if>
      <if test="online != null">
        #{online,jdbcType=SMALLINT},
      </if>
      <if test="checkResult != null">
        #{checkResult,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dFlag != null">
        #{dFlag,jdbcType=SMALLINT},
      </if>
      <if test="labelEnumValue != null">
        #{labelEnumValue,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xftech.cdp.infra.repository.cdp.label.po.MetaLabelDo">
    update meta_label
    <set>
      <if test="dataId != null">
        `data_id` = #{dataId,jdbcType=BIGINT},
      </if>
      <if test="labelName != null">
        `label_name` = #{labelName,jdbcType=VARCHAR},
      </if>
      <if test="labelCode != null">
        `label_code` = #{labelCode,jdbcType=VARCHAR},
      </if>
      <if test="labelDataValueType != null">
        `label_data_value_type` = #{labelDataValueType,jdbcType=VARCHAR},
      </if>
      <if test="aggDimension != null">
        `agg_dimension` = #{aggDimension,jdbcType=VARCHAR},
      </if>
      <if test="available != null">
        `available` = #{available,jdbcType=SMALLINT},
      </if>
      <if test="online != null">
        `online` = #{online,jdbcType=SMALLINT},
      </if>
      <if test="checkResult != null">
        `check_result` = #{checkResult,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        `created_time` = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        `updated_time` = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dFlag != null">
        `d_flag` = #{dFlag,jdbcType=SMALLINT},
      </if>
      <if test="labelEnumValue != null">
        `label_enum_value` = #{labelEnumValue,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateByLabelCode" parameterType="com.xftech.cdp.infra.repository.cdp.label.po.MetaLabelDo">
    update meta_label
    <set>
      <if test="dataId != null">
        `data_id` = #{dataId,jdbcType=BIGINT},
      </if>
      <if test="labelName != null">
        `label_name` = #{labelName,jdbcType=VARCHAR},
      </if>
      <if test="labelCode != null">
        `label_code` = #{labelCode,jdbcType=VARCHAR},
      </if>
      <if test="labelDataValueType != null">
        `label_data_value_type` = #{labelDataValueType,jdbcType=VARCHAR},
      </if>
      <if test="aggDimension != null">
        `agg_dimension` = #{aggDimension,jdbcType=VARCHAR},
      </if>
      <if test="available != null">
        `available` = #{available,jdbcType=SMALLINT},
      </if>
      <if test="online != null">
        `online` = #{online,jdbcType=SMALLINT},
      </if>
      <if test="checkResult != null">
        `check_result` = #{checkResult,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        `created_time` = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        `updated_time` = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dFlag != null">
        `d_flag` = #{dFlag,jdbcType=SMALLINT},
      </if>
      <if test="labelEnumValue != null">
        `label_enum_value` = #{labelEnumValue,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where `label_code` = #{labelCode}
    and d_flag = 0
  </update>

  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.xftech.cdp.infra.repository.cdp.label.po.MetaLabelDo">
    update meta_label
    set `data_id` = #{dataId,jdbcType=BIGINT},
      `label_name` = #{labelName,jdbcType=VARCHAR},
      `label_code` = #{labelCode,jdbcType=VARCHAR},
      `label_data_value_type` = #{labelDataValueType,jdbcType=VARCHAR},
      `agg_dimension` = #{aggDimension,jdbcType=VARCHAR},
      `available` = #{available,jdbcType=SMALLINT},
      `online` = #{online,jdbcType=SMALLINT},
      `check_result` = #{checkResult,jdbcType=VARCHAR},
      `created_time` = #{createdTime,jdbcType=TIMESTAMP},
      `updated_time` = #{updatedTime,jdbcType=TIMESTAMP},
      `d_flag` = #{dFlag,jdbcType=SMALLINT},
      `label_enum_value` = #{labelEnumValue,jdbcType=LONGVARCHAR}
    where `id` = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xftech.cdp.infra.repository.cdp.label.po.MetaLabelDo">
    update meta_label
    set `data_id` = #{dataId,jdbcType=BIGINT},
      `label_name` = #{labelName,jdbcType=VARCHAR},
      `label_code` = #{labelCode,jdbcType=VARCHAR},
      `label_data_value_type` = #{labelDataValueType,jdbcType=VARCHAR},
      `agg_dimension` = #{aggDimension,jdbcType=VARCHAR},
      `available` = #{available,jdbcType=SMALLINT},
      `online` = #{online,jdbcType=SMALLINT},
      `check_result` = #{checkResult,jdbcType=VARCHAR},
      `created_time` = #{createdTime,jdbcType=TIMESTAMP},
      `updated_time` = #{updatedTime,jdbcType=TIMESTAMP},
      `d_flag` = #{dFlag,jdbcType=SMALLINT}
    where `id` = #{id,jdbcType=BIGINT}
  </update>
</mapper>