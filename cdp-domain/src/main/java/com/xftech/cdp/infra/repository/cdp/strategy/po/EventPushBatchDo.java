package com.xftech.cdp.infra.repository.cdp.strategy.po;

import com.xftech.cdp.infra.repository.Do;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 事件推送批次记录表
 *
 * @TableName event_push_batch_202305
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EventPushBatchDo extends Do {

    /**
     * 表名
     */
    private String tableName;

    /**
     * 策略id
     */
    private Long strategyId;

    /**
     * 策略渠道id
     */
    private Long marketChannelId;

    /**
     * 策略执行日志id
     */
    private Long execLogId;

    /**
     * 触达渠道：0: 不营销，1:短信，2:电销，3:优惠券
     */
    private Integer marketChannel;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * APP
     */
    private String app;

    /**
     * 模板id
     */
    private String templateId;

    /**
     * 批次号
     */
    private String batchNum;

    /**
     * 内部批次号
     */
    private String innerBatchNum;

    /**
     * 发送状态
     */
    private String sendCode;

    /**
     * 发送描述
     */
    private String sendMsg;

    /**
     * 下发状态 1-成功 2-失败
     */
    private Integer status;

    /**
     * 结果查询状态：-1:无需查询 0:未查询 1:轮询中 2:已完成
     */
    private Integer queryStatus;

    /**
     * 下发明细表序号
     */
    private String detailTableNo;

    /**
     * 引擎返回groupId
     */
    private String groupName;

    /**
     * 麻雀分组id
     */
    private Long strategyGroupId;

    /**
     * 麻雀分组name
     */
    private String strategyGroupName;

    private String bizEventType;

    private static final long serialVersionUID = 1L;
}