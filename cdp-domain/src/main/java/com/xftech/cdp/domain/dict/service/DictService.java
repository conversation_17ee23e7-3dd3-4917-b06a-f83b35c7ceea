package com.xftech.cdp.domain.dict.service;


import com.xftech.cdp.api.dto.req.dict.DictReq;
import com.xftech.cdp.api.dto.req.dict.EventMetaDataReq;
import com.xftech.cdp.api.dto.resp.dict.DictResp;
import com.xftech.cdp.api.dto.resp.dict.EventMetaDataResp;

import java.util.List;

public interface DictService {

    List<DictResp> queryListByDictCodeList(List<String> codeList);
    List<DictResp> queryDictCodeList(DictReq dictReq);
    List<EventMetaDataResp> queryEventMetaData(EventMetaDataReq eventMetaDataReq);
}
