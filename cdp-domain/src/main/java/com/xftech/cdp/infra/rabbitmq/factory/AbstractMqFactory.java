package com.xftech.cdp.infra.rabbitmq.factory;

import com.xftech.rabbitmq.build.BindParamBuilder;
import com.xftech.rabbitmq.build.ConnectionBuilder;
import com.xftech.rabbitmq.core.UdpMqConsumer;
import com.xftech.rabbitmq.core.UdpMqProducer;

import java.io.IOException;
import java.util.concurrent.TimeoutException;

public abstract class AbstractMqFactory {

    public abstract void init() throws IOException, TimeoutException;

    /**
     * 初始化生产者连接
     */
    public abstract UdpMqProducer initProducerConn(String connectionId);

    /**
     * 初始化消费者连接
     */
    public abstract UdpMqConsumer initConsumerConn(String connectionId);

    /**
     * 创建普通生产者
     */
    public abstract void createProducer(ConnectionBuilder builder);

    /**
     * 创建普通消费者
     */
    public abstract void createConsumer(BindParamBuilder builder);

    /**
     * 创建死信消费者
     */
    public abstract void createDeadLetterConsumer();

    /**
     * 创建延迟消息生产者
     */
    public abstract void createDelayProducer();

    /**
     * 创建延迟消息消费者
     */
    public abstract void createDelayConsumer();
}
