package com.xftech.cdp.domain.strategy.service;

import com.xftech.cdp.domain.dispatch.dto.DispatchDto;
import com.xftech.cdp.domain.strategy.model.dto.UserDispatchFailDetailDto;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;

import java.util.List;

public interface UserDispatchFailDetailService {

    /**
     * 批量保存用户下发失败明细
     *
     * @param dispatchFailDetail 失败明细记录
     * @return 保存成功数量
     */
    int batchSave(UserDispatchFailDetailDto dispatchFailDetail);

    void saveFailRecords(List<CrowdDetailDo> crowdDetails, DispatchDto dispatchDto, String failReason, String groupName);
}
