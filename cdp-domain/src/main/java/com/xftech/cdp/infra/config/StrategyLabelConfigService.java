/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.infra.config;

import com.xftech.cdp.domain.ads.model.MatchResult;
import com.xftech.cdp.infra.client.ads.model.req.label.AdsLabelReq;
import com.xftech.cdp.infra.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;


/**
 *
 * <AUTHOR>
 * @version $ StrategyLabelConfigService, v 0.1 2023/10/10 16:25 wancheng.qu Exp $
 */


@Slf4j
@Service
public class StrategyLabelConfigService {


    private static final String STRATEGY = "strategy";
    private static final String LABEL = "label";
    private final ExpressionParser parser = new SpelExpressionParser();

    @Autowired
    private AppConfigService appConfigService;

    /**
     * 2024-07-30 经过分析，实际上isMatch不需要strategyId这个参数。
     * 不过保险起见，仍保留原来的 isMatch 方法
     * @param labelReqs
     * @param isOffline
     * @return
     * @param <T>
     */
    public <T> MatchResult<T> isMatchWithoutStrategy(Collection<T> labelReqs, boolean isOffline) {
        String inclusionExpressionStr = null;
        String exclusionExpressionStr = null;
        if (isOffline){
            inclusionExpressionStr = appConfigService.getOfflineInclusionExpressionStr();
            exclusionExpressionStr = appConfigService.getOfflineExclusionExpressionStr();
        }else {
            inclusionExpressionStr = appConfigService.getInclusionExpressionStr();
            exclusionExpressionStr = appConfigService.getExclusionExpressionStr();
        }
        Expression inclusionExpression = parser.parseExpression(inclusionExpressionStr);
        Expression exclusionExpression = parser.parseExpression(exclusionExpressionStr);
        List<T> matchingLabels = new ArrayList<>();
        List<T> nonMatchingLabels = new ArrayList<>();
        if (CollectionUtils.isEmpty(labelReqs)){
            return new MatchResult<>(matchingLabels, nonMatchingLabels);
        }
        for (T labelReq : labelReqs) {
            String label = getLabelFromLabelReq(labelReq);

            StandardEvaluationContext context = new StandardEvaluationContext();
            context.setVariable(LABEL, label);

            boolean isInclusionMatch;
            boolean isExclusionMatch;

            try {
                isInclusionMatch = evaluateExpression(inclusionExpression, context);
                isExclusionMatch = evaluateExpression(exclusionExpression, context);
            } catch (Exception e) {
                log.error("StrategyLabelConfigService.isMatchWithoutStrategy is error, labelReqs={}",JsonUtil.toJson(labelReqs));
                isInclusionMatch = false;
                isExclusionMatch = false;
            }

            if (isInclusionMatch && !isExclusionMatch) {
                matchingLabels.add(labelReq);
            } else {
                nonMatchingLabels.add(labelReq);
            }
        }

        return new MatchResult<>(matchingLabels, nonMatchingLabels);
    }

    private boolean evaluateExpression(Expression expression, EvaluationContext context) {
        return Boolean.TRUE.equals(expression.getValue(context, Boolean.class));
    }

    private String getLabelFromLabelReq(Object labelReq) {
        if (labelReq instanceof String) {
            return (String) labelReq;
        } else if (labelReq instanceof Long) {
            return String.valueOf(labelReq);
        } else if (labelReq instanceof AdsLabelReq) {
            return ((AdsLabelReq) labelReq).getLabel();
        } else {
            log.error("unknow type from method getLabelFromLabelReq，type={}", JsonUtil.toJson(labelReq));
            return null;
        }
    }
}