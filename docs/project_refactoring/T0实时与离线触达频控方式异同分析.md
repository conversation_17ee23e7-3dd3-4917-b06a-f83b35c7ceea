# T0实时与离线触达频控方式异同分析

## 1. 概述

本文档详细分析T0实时触达和离线触达在频控机制方面的异同点，包括频控类型、实现方式、执行时机、配置管理等方面的对比。

## 2. 频控机制分类总览

### 2.1 按控制层级分类

| 频控类型 | 控制粒度 | 应用场景 | 实现方式 | T0实时 | 离线 |
|---------|---------|---------|---------|--------|------|
| **事件级流控** | 用户+事件类型 | MQ消息处理入口 | Redis分布式锁 | ✅ | ❌ |
| **触达级流控** | 用户+渠道+策略 | 触达执行前检查 | 数据库查询+Redis锁 | ✅ | ✅ |
| **分布式流控** | 用户级别 | 并发触达控制 | Redis分布式锁 | ✅ | ❌ |
| **批量流控** | 批量用户 | 离线批处理 | 数据库批量查询 | ❌ | ✅ |

### 2.2 按触达方式分类

| 触达方式 | 频控机制 | 执行时机 | 特点 |
|---------|---------|---------|------|
| **T0-普通触达** | 单用户预检查 | dispatch()开始前 | 实时性强，失败即终止 |
| **T0-引擎触达** | 内置流控逻辑 | marketingSend()内部 | 渠道级控制 |
| **离线-普通触达** | 批量流控 | 批处理过程中 | 支持新旧切换，批量处理 |
| **离线-引擎触达** | 内置流控逻辑 | marketingSend()内部 | 与T0引擎策略相同 |

## 3. T0实时触达频控机制

### 3.1 事件级流控 (T0独有)

#### 实现位置
- **类**: `MqConsumeServiceImpl`
- **方法**: `isReject(BizEventMessageVO bizEventMessageVO)`

#### 频控原理
```java
// 事件流控检查 - MQ消息处理入口
private boolean isReject(BizEventMessageVO bizEventMessageVO) {
    Long userId = Optional.ofNullable(bizEventMessageVO.getCreditUserId())
            .orElse(bizEventMessageVO.getUser_id());
    
    if (StringUtils.isNotEmpty(bizEventMessageVO.getBizEventType()) && userId != null && userId > 0) {
        EventFlcConfig eventFlcConfig = appConfigService.getEventFlcConfig();
        if (eventFlcConfig != null) {
            Integer limitSeconds = eventFlcConfig.getLimitSeconds(bizEventMessageVO.getBizEventType());
            if (limitSeconds != null && limitSeconds > 0) {
                String limitKey = String.format("eventflc:%s:%s", bizEventMessageVO.getBizEventType(), userId);
                boolean ret = redisUtils.lock(limitKey, "1", limitSeconds);
                if (!ret) {
                    return true; // 被流控拦截
                }
            }
        }
    }
    return false;
}
```

#### 配置方式
- **Redis Key格式**: `eventflc:{事件类型}:{用户ID}`
- **流控机制**: 分布式锁，锁定时间为配置的限制秒数
- **拦截逻辑**: 在限制时间内，同一用户的同一事件类型只能处理一次

### 3.2 触达级流控

#### 实现位置
- **类**: `DispatchFlcService`
- **方法**: `dispatchFlc(BizEventVO bizEventVO, CrowdDetailDo crowdDetail)`

#### 频控特点
- **执行时机**: dispatch()方法开始前
- **控制粒度**: 单用户级别
- **失败处理**: 流控失败直接终止，不执行触达
- **开关控制**: 支持按渠道独立控制

#### Apollo配置
```properties
# 复筛常规流控开关
singleDispatchFlc.1 = false  # 短信频控开关
singleDispatchFlc.2 = false  # 电销频控开关  
singleDispatchFlc.3 = false  # 优惠券频控开关
singleDispatchFlc.4 = false  # Push频控开关
```

### 3.3 分布式流控 (T0独有)

#### 实现位置
- **类**: `DispatchFlcService`
- **方法**: `dispatchFlcLock()`

#### 频控特点
- **目的**: 防止同一用户并发触达
- **机制**: Redis分布式锁
- **锁定时间**: 6分钟 (360秒)
- **重试机制**: 支持等待和重试

## 4. 离线触达频控机制

### 4.1 批量流控 (离线独有)

#### 实现位置
- **类**: `FlowCtrlCoreServiceImpl`
- **方法**: `flowCtrl(FlowCtrlDto flowCtrlDto, List<Integer> statusList)`

#### 频控特点
- **执行时机**: 批处理过程中
- **控制粒度**: 批量用户
- **失败处理**: 部分用户被流控，其他用户继续处理
- **版本切换**: 支持新旧流控逻辑切换

#### 新旧流控差异
```java
// 新旧流控切换逻辑
if(WhitelistSwitchUtil.commonGraySwitchByApollo("" + strategyId, "newFlowCtrlSwitch")) {
    log.info("流控规则执行: 新流控 strategyId:{} marketChannel:{}", strategyId, marketChannel);
    passUserIdList = this.newExecute(flowCtrlDto, statusList);
} else {
    passUserIdList = this.execute(flowCtrlDto, statusList);
}
```

| 版本 | 查询方式 | 特点 |
|------|---------|------|
| **旧版本** | `getUserIndex()` | 支持跨策略查询 |
| **新版本** | `getUserIndexNew()` | 按渠道查询，性能更好 |

### 4.2 离线引擎触达频控

#### 实现位置
- **类**: `DistributeOfflineEngineDispatchServiceImpl`
- **方法**: `filterAndDispatch()`

#### 频控特点
- **幂等性校验**: 防止重复触达
- **过滤开关**: `offlineEngineFilterSwitch` 紧急停止开关
- **去重机制**: 用户ID全局去重 + 手机号去重

## 5. 共同频控机制

### 5.1 引擎策略内置流控

#### 适用范围
- T0-引擎触达
- 离线-引擎触达

#### 实现位置
- **类**: `StrategyEventDispatchServiceImpl`
- **方法**: `marketingSend()`

#### 频控特点
- **执行时机**: marketingSend()方法内部
- **控制级别**: 渠道级控制
- **统一逻辑**: T0和离线引擎策略使用相同的流控逻辑

### 5.2 频控规则配置

#### 流控规则类型
| 类型 | 代码 | 描述 | 优先级 |
|------|------|------|--------|
| **策略级** | STRATEGY(1) | 单策略流控 | 2 |
| **渠道级** | CHANNEL(2) | 渠道流控 | 1 |
| **多策略共享** | MULTI_STRATEGY(3) | 跨策略流控 | 3 |
| **业务线级** | BIZ_LINE(4) | 业务线流控 | 4 |

#### 流控规则状态
- **INIT(0)**: 初始化
- **EFFECTIVE(1)**: 生效中
- **CLOSE(2)**: 已关闭
- **DELETED(3)**: 已删除

## 6. 主要差异对比

### 6.1 执行时机差异

| 触达类型 | 频控执行时机 | 频控方法 | 特点 |
|---------|-------------|---------|------|
| **T0-普通** | dispatch()开始前 | dispatchFlc() | 预先流控，失败直接终止 |
| **T0-引擎** | marketingSend()内部 | 内置流控 | 渠道级流控 |
| **离线-普通** | 批处理过程中 | flowCtrl() | 批量流控，支持新旧切换 |
| **离线-引擎** | marketingSend()内部 | 内置流控 | 与T0引擎相同 |

### 6.2 技术实现差异

| 特性 | T0实时触达 | 离线触达 |
|------|-----------|---------|
| **事件级流控** | ✅ Redis分布式锁 | ❌ 不支持 |
| **分布式锁** | ✅ 防并发触达 | ❌ 不需要 |
| **批量处理** | ❌ 单用户处理 | ✅ 批量用户处理 |
| **新旧切换** | ❌ 不支持 | ✅ 支持灰度切换 |
| **幂等校验** | ❌ 不需要 | ✅ 防重复执行 |

### 6.3 性能特点差异

| 方面 | T0实时触达 | 离线触达 |
|------|-----------|---------|
| **实时性** | 高 - 毫秒级响应 | 低 - 批处理延迟 |
| **吞吐量** | 低 - 单用户处理 | 高 - 批量处理 |
| **资源消耗** | 高 - Redis锁频繁 | 低 - 批量查询 |
| **并发控制** | 强 - 分布式锁 | 弱 - 批处理天然串行 |

## 7. 配置管理对比

### 7.1 Apollo配置项

#### T0实时触达配置
```properties
# 事件流控配置
eventFlc.{eventType} = {seconds}

# 渠道流控开关
singleDispatchFlc.1 = false  # 短信
singleDispatchFlc.2 = false  # 电销
singleDispatchFlc.3 = false  # 优惠券
singleDispatchFlc.4 = false  # Push

# 分布式锁配置
dispatchFlcExecutor.pool.coreSize = 8
dispatchFlcExecutor.pool.maxSize = 20
```

#### 离线触达配置
```properties
# 新旧流控切换
newFlowCtrlSwitch.{strategyId} = true

# 离线引擎过滤开关
offlineEngineFilterSwitch.{strategyId} = false

# 幂等校验开关
distributeOfflineDuplicateFilterEnable = true
```

### 7.2 数据库配置

#### 流控规则表 (flow_ctrl)
- **rule_name**: 规则名称
- **limit_days**: 限制天数
- **limit_times**: 限制次数
- **flow_ctrl_type**: 流控类型
- **status**: 规则状态

## 8. 监控和日志

### 8.1 监控指标

#### T0实时触达
- `RejectT0Event`: 事件流控拦截
- `dispatchFlc:{channel}`: 触达流控拦截
- `dispatchFlcLock:{channel}`: 分布式锁流控

#### 离线触达
- 流控拦截用户数统计
- 批量处理耗时监控
- 新旧流控切换监控

### 8.2 日志记录

#### 共同日志
- 流控规则执行日志
- 用户拦截详情日志
- 性能耗时日志

#### 差异化日志
- **T0**: 实时事件流控日志
- **离线**: 批量处理进度日志

## 9. 总结

### 9.1 相同点
1. **核心逻辑**: 都基于用户触达次数进行频控
2. **规则配置**: 使用相同的流控规则表和配置格式
3. **引擎策略**: T0和离线的引擎策略使用相同的流控逻辑
4. **监控体系**: 都有完整的监控和日志记录

### 9.2 不同点
1. **执行时机**: T0预先流控，离线批处理中流控
2. **处理粒度**: T0单用户，离线批量用户
3. **技术实现**: T0依赖Redis锁，离线依赖数据库查询
4. **性能特点**: T0实时性高但吞吐量低，离线吞吐量高但实时性低
5. **功能特性**: T0有事件级流控和分布式锁，离线有批量处理和版本切换

### 9.3 适用场景
- **T0实时触达**: 适合对实时性要求高、用户量相对较少的场景
- **离线触达**: 适合大批量用户处理、对实时性要求不高的场景

---

**文档版本**: v1.0  
**创建时间**: 2025-06-20  
**维护人员**: CDP开发团队
