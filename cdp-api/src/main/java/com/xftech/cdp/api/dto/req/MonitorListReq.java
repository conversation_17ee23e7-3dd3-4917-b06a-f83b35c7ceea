package com.xftech.cdp.api.dto.req;

import com.xftech.cdp.api.dto.base.PageRequestDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2023/2/23
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MonitorListReq extends PageRequestDto {

    @ApiModelProperty(value = "策略id")
    private Long strategyId;


    @ApiModelProperty(value = "策略渠道")
    private Integer strategyMarketChannel;
}
