package com.xftech.cdp.domain.cache.impl;

import com.alibaba.fastjson.JSONArray;
import com.xftech.cdp.domain.crowd.repository.CrowdDetailRepository;
import com.xftech.cdp.domain.cache.CacheSplitTableService;
import com.xftech.cdp.domain.subtable.service.SplitTableService;
import com.xftech.cdp.infra.utils.RedisKeyUtils;
import com.xftech.cdp.infra.utils.RedisUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
public class CacheSplitTableServiceImpl implements CacheSplitTableService {
    @Autowired
    public CrowdDetailRepository crowdDetailRepository;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private SplitTableService splitTableService;

    @Override
    public List<String> getTableNameByLogId(Long crowdExceLogId) {
        String redisKey = RedisKeyUtils.genTableNameByLogId(crowdExceLogId);
        String data = redisUtils.get(redisKey);
        List<String> tableName = JSONArray.parseArray(data, String.class);;
        if (CollectionUtils.isEmpty(tableName)) {
            tableName = splitTableService.getTableNameByLogId(crowdExceLogId);
            redisUtils.set(redisKey, tableName, RedisUtils.DEFAULT_EXPIRE_HOUR);
        }
        return tableName;
    }
}
