package com.xinfei.touch.domain.converter;

import com.xinfei.touch.domain.model.TouchChannel;
import com.xinfei.touch.domain.model.TouchType;
import com.xinfei.touch.domain.model.unified.*;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 触达请求转换器
 * 负责将现有的各种参数结构转换为统一的TouchRequest模型
 * 
 * 注意：这里使用Object类型来表示外部依赖，实际使用时需要替换为具体的类型
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-23
 */
@Component
public class TouchRequestConverter {
    
    /**
     * 转换T0普通触达参数（execSend方法）
     * 
     * @param dispatchDto 分发参数对象
     * @param crowdDetail 用户明细对象
     * @param channelEnum 渠道枚举
     * @param channelDo 渠道配置对象
     * @param bizEvent 业务事件对象
     * @return 统一触达请求
     */
    public TouchRequest convertFromExecSend(Object dispatchDto, 
                                          Object crowdDetail, 
                                          Object channelEnum, 
                                          Object channelDo, 
                                          Object bizEvent) {
        TouchRequest request = new TouchRequest();
        
        // 基础信息
        request.setRequestId(UUID.randomUUID().toString());
        request.setTouchType(TouchType.REALTIME_NORMAL);
        request.setTouchMode(TouchMode.SINGLE);
        request.setTimestamp(System.currentTimeMillis());
        
        // TODO: 从bizEvent中提取traceId
        // request.setTraceId(bizEvent.getTraceId());
        
        // 策略信息
        setStrategyInfoFromDispatchDto(request, dispatchDto, channelEnum);
        
        // 用户信息
        request.setUserInfo(convertToTouchUserInfo(crowdDetail));
        
        // 业务信息
        setBizEventInfo(request, bizEvent);
        
        // 触达配置
        request.setTouchConfig(convertTouchConfig(dispatchDto, channelDo));
        
        // 流控配置
        request.setFlowControlConfig(FlowControlConfig.createT0Config());
        
        return request;
    }
    
    /**
     * 转换T0引擎触达参数（marketingSend方法 - T0场景）
     */
    public TouchRequest convertFromT0MarketingSend(Object dispatchDto,
                                                 Object crowdDetailDo,
                                                 Object channelEnum,
                                                 String groupName,
                                                 Map<String, Object> detailInfo,
                                                 Object bizEventVO) {
        TouchRequest request = new TouchRequest();
        
        // 基础信息
        request.setRequestId(UUID.randomUUID().toString());
        request.setTouchType(TouchType.REALTIME_ENGINE);
        request.setTouchMode(TouchMode.SINGLE);
        request.setTimestamp(System.currentTimeMillis());
        
        // 策略信息
        setStrategyInfoFromDispatchDto(request, dispatchDto, channelEnum);
        
        // 用户信息
        request.setUserInfo(convertToTouchUserInfo(crowdDetailDo));
        
        // 引擎信息
        request.setEngineGroupName(groupName);
        request.setEngineDetail(detailInfo);
        
        // 业务信息（如果有）
        if (bizEventVO != null) {
            setBizEventInfo(request, bizEventVO);
            // TODO: 从bizEventVO中提取engineCode
            // request.setEngineCode(bizEventVO.getEngineCode());
        }
        
        // 触达配置
        request.setTouchConfig(convertTouchConfig(dispatchDto, null));
        
        // 流控配置
        request.setFlowControlConfig(FlowControlConfig.createT0EngineConfig());
        
        return request;
    }
    
    /**
     * 转换离线引擎触达参数（marketingSend方法 - 离线场景）
     */
    public TouchRequest convertFromOfflineMarketingSend(Object dispatchDto,
                                                      Object crowdDetailDo,
                                                      Object channelEnum,
                                                      String groupName,
                                                      Map<String, Object> detailInfo) {
        TouchRequest request = new TouchRequest();
        
        // 基础信息
        request.setRequestId(UUID.randomUUID().toString());
        request.setTouchType(TouchType.OFFLINE_ENGINE);
        request.setTouchMode(TouchMode.SINGLE);
        request.setTimestamp(System.currentTimeMillis());
        
        // 策略信息
        setStrategyInfoFromDispatchDto(request, dispatchDto, channelEnum);
        
        // 用户信息
        request.setUserInfo(convertToTouchUserInfo(crowdDetailDo));
        
        // 引擎信息
        request.setEngineGroupName(groupName);
        request.setEngineDetail(detailInfo);
        
        // 触达配置
        request.setTouchConfig(convertTouchConfig(dispatchDto, null));
        
        // 流控配置
        request.setFlowControlConfig(FlowControlConfig.createOfflineEngineConfig());
        
        return request;
    }
    
    /**
     * 转换离线普通触达参数（dispatchHandler方法）
     */
    public TouchRequest convertFromDispatchHandler(Object strategyContext,
                                                 String app,
                                                 List<Object> userList,
                                                 List<Object> templateParams) {
        TouchRequest request = new TouchRequest();
        
        // 基础信息
        request.setRequestId(UUID.randomUUID().toString());
        request.setTouchType(TouchType.OFFLINE_NORMAL);
        request.setTouchMode(TouchMode.BATCH);
        request.setTimestamp(System.currentTimeMillis());
        
        // 策略信息
        setStrategyInfoFromContext(request, strategyContext);
        
        // 用户信息列表
        List<TouchUserInfo> touchUserList = userList.stream()
                .map(this::convertToTouchUserInfo)
                .collect(Collectors.toList());
        request.setUserList(touchUserList);
        
        // 批量处理信息
        String batchNo = generateBatchNo();
        BatchInfo batchInfo = BatchInfo.create(batchNo, userList.size(), userList.size());
        // TODO: 从strategyContext中提取detailTableNo
        // batchInfo.setDetailTableNo(strategyContext.getDetailTableNo());
        request.setBatchInfo(batchInfo);
        
        // 模板参数
        request.setTemplateParams(convertTemplateParams(templateParams));
        
        // 触达配置
        request.setTouchConfig(convertTouchConfigFromContext(strategyContext));
        
        // 流控配置
        request.setFlowControlConfig(convertFlowControlConfigFromContext(strategyContext));
        
        return request;
    }
    
    /**
     * 从DispatchDto设置策略信息
     */
    private void setStrategyInfoFromDispatchDto(TouchRequest request, Object dispatchDto, Object channelEnum) {
        // TODO: 实际实现时需要根据具体的DispatchDto类型进行字段提取
        // request.setStrategyId(dispatchDto.getStrategyId());
        // request.setStrategyExecId(dispatchDto.getStrategyExecId());
        // request.setStrategyGroupId(dispatchDto.getStrategyGroupId());
        // request.setStrategyGroupName(dispatchDto.getStrategyGroupName());
        // request.setStrategyChannelId(dispatchDto.getStrategyChannelId());
        // request.setChannel(convertToTouchChannel(channelEnum));
        // request.setTemplateId(dispatchDto.getStrategyMarketChannelTemplateId());
        
        // 示例实现（需要根据实际情况调整）
        request.setChannel(TouchChannel.SMS); // 默认值，实际需要从channelEnum转换
    }
    
    /**
     * 从StrategyContext设置策略信息
     */
    private void setStrategyInfoFromContext(TouchRequest request, Object strategyContext) {
        // TODO: 实际实现时需要根据具体的StrategyContext类型进行字段提取
        // request.setStrategyId(strategyContext.getStrategyDo().getId());
        // request.setStrategyGroupId(strategyContext.getStrategyGroupDo().getId());
        // request.setStrategyGroupName(strategyContext.getStrategyGroupDo().getName());
        // request.setStrategyChannelId(strategyContext.getStrategyMarketChannelDo().getId());
        // request.setChannel(convertToTouchChannel(strategyContext.getStrategyMarketChannelDo().getMarketChannel()));
    }
    
    /**
     * 转换用户信息
     */
    private TouchUserInfo convertToTouchUserInfo(Object crowdDetail) {
        TouchUserInfo userInfo = new TouchUserInfo();
        
        // TODO: 实际实现时需要根据具体的CrowdDetailDo类型进行字段提取
        // userInfo.setUserId(crowdDetail.getUserId());
        // userInfo.setMobile(crowdDetail.getMobile());
        // userInfo.setApp(crowdDetail.getApp());
        // userInfo.setInnerApp(crowdDetail.getInnerApp());
        // userInfo.setDeviceId(crowdDetail.getDeviceId());
        // userInfo.setAbNum(crowdDetail.getAbNum());
        // userInfo.setAppUserIdLast2(crowdDetail.getAppUserIdLast2());
        // userInfo.setCrowdId(crowdDetail.getCrowdId());
        
        // 示例实现（需要根据实际情况调整）
        userInfo.setUserId(1L); // 默认值
        userInfo.setMobile("***********"); // 默认值
        userInfo.setApp("default"); // 默认值
        
        return userInfo;
    }
    
    /**
     * 设置业务事件信息
     */
    private void setBizEventInfo(TouchRequest request, Object bizEvent) {
        // TODO: 实际实现时需要根据具体的BizEventVO类型进行字段提取
        // request.setBizEventType(bizEvent.getBizEventType());
        // request.setBizEventData(convertBizEventToMap(bizEvent));
        // request.setTemplateParams(extractTemplateParams(bizEvent));
        
        // 示例实现
        request.setBizEventType("DefaultEvent");
        request.setBizEventData(new HashMap<>());
        request.setTemplateParams(new HashMap<>());
    }
    
    /**
     * 转换触达配置
     */
    private TouchConfig convertTouchConfig(Object dispatchDto, Object channelDo) {
        TouchConfig config = TouchConfig.createDefault();
        
        // TODO: 实际实现时需要根据具体类型进行字段提取
        // if (dispatchDto.getDispatchType() != null) {
        //     config.setDispatchType(dispatchDto.getDispatchType());
        // }
        // if (channelDo != null && channelDo.getExtInfo() != null) {
        //     config.setChannelConfig(Map.of("extInfo", channelDo.getExtInfo()));
        // }
        
        return config;
    }
    
    /**
     * 从StrategyContext转换触达配置
     */
    private TouchConfig convertTouchConfigFromContext(Object strategyContext) {
        TouchConfig config = TouchConfig.createDefault();
        // TODO: 根据strategyContext中的信息设置特定配置
        return config;
    }
    
    /**
     * 从StrategyContext转换流控配置
     */
    private FlowControlConfig convertFlowControlConfigFromContext(Object strategyContext) {
        FlowControlConfig config = FlowControlConfig.createOfflineBatchConfig();
        
        // TODO: 转换流控规则
        // if (strategyContext.getFlowCtrlList() != null && !strategyContext.getFlowCtrlList().isEmpty()) {
        //     List<FlowControlRule> rules = strategyContext.getFlowCtrlList().stream()
        //             .map(this::convertFlowCtrlDoToRule)
        //             .collect(Collectors.toList());
        //     config.setRules(rules);
        // }
        
        return config;
    }
    
    /**
     * 转换模板参数
     */
    private Map<String, Object> convertTemplateParams(List<Object> templateParams) {
        Map<String, Object> params = new HashMap<>();
        if (templateParams != null) {
            for (int i = 0; i < templateParams.size(); i++) {
                params.put("param" + i, templateParams.get(i));
            }
        }
        return params;
    }
    
    /**
     * 生成批次号
     */
    private String generateBatchNo() {
        return "BATCH_" + System.currentTimeMillis() + "_" + 
               UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }
    
    /**
     * 转换渠道枚举
     */
    private TouchChannel convertToTouchChannel(Object channelEnum) {
        // TODO: 实际实现时需要根据具体的渠道枚举类型进行转换
        // 这里提供一个示例实现
        if (channelEnum == null) {
            return TouchChannel.SMS;
        }
        
        String channelName = channelEnum.toString();
        try {
            return TouchChannel.valueOf(channelName);
        } catch (IllegalArgumentException e) {
            // 如果无法直接转换，返回默认值
            return TouchChannel.SMS;
        }
    }
}
