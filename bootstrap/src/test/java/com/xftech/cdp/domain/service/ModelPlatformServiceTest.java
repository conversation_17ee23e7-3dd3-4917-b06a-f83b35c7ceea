package com.xftech.cdp.domain.service;

import com.alibaba.fastjson.JSONObject;
import com.xftech.cdp.BaseTest;
import com.xftech.cdp.domain.ads.model.PredictDecisionDto;
import com.xftech.cdp.domain.ads.service.ModelPlatformService;
import com.xftech.cdp.domain.strategy.model.dto.AdbRealTimeVariableGrayConfig;
import com.xftech.cdp.infra.client.ads.model.req.ModelPredictionReq;
import com.xftech.cdp.infra.client.ads.model.req.label.AdbRealTimeReq;
import com.xftech.cdp.infra.client.ads.model.req.label.AdsLabelReq;
import com.xftech.cdp.infra.client.ads.model.resp.AdbRealTimeResp;
import com.xftech.cdp.infra.config.AppConfigService;
import com.xftech.cdp.infra.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.util.*;

@Slf4j
public class ModelPlatformServiceTest extends BaseTest {
    @Autowired
    private ModelPlatformService modelPlatformService;

    @Test
    public void testCall() {
       /* ModelPredictionReq modelPredictionReq = ModelPredictionReq.builder()
                .model_name("test_model")
                .biz_data(ModelPredictionReq.BizData.builder()
                        .requestId("222")
                        .app("")
                        .biz_type("123")
                        .app_user_id(1022091153L)
                        .trigger_datetime(new Date().getTime())
                        .timestamp(new Date().getTime())
                        .build())
                .build();
        log.info(JsonUtil.toJson(modelPlatformService.getModelNames()));
        log.info(JsonUtil.toJson(modelPlatformService.prediction(modelPredictionReq)));*/

        //
        String str = "{\n" +
                "    \"msg\":\"\",\n" +
                "    \"code\":2000,\n" +
                "    \"data\":{\n" +
                "        \"model_name\":\"marketing_test_model_v2\",\n" +
                "        \"output_data\":{\n" +
                "            \"marketing_test_model_v2\":\"{\\\"decision_code\\\": 200, \\\"decision_msg\\\": \\\"OK\\\", \\\"decision_data\\\": {\\\"delay\\\": {\\\"seconds\\\": 0}, \\\"actions\\\": [{\\\"group_id\\\": \\\"A\\\", \\\"dispatch\\\": [{\\\"order\\\": 0, \\\"type\\\": \\\"sms\\\", \\\"delay\\\": {\\\"seconds\\\": 100}, \\\"detail_info\\\": {\\\"template_id\\\": \\\"1122\\\", \\\"template_params\\\": {\\\"amount\\\": 10000}}}, {\\\"order\\\": 1, \\\"type\\\": \\\"coupon\\\", \\\"delay\\\": {\\\"seconds\\\": 100}, \\\"detail_info\\\": {\\\"batch\\\": [{\\\"activity_id\\\": 10001, \\\"batch_num\\\": \\\"222\\\"}]}}]}, {\\\"group_id\\\": \\\"B\\\", \\\"dispatch\\\": []}]}}\"\n" +
                "        }\n" +
                "    }\n" +
                "}\n" +
                "\n";
        JSONObject resp = JsonUtil.parse(str, JSONObject.class);
        Object decisionVal = resp.getJSONObject("data").getJSONObject("output_data")
                .get("marketing_test_model_v2");

        try {
            PredictDecisionDto predictDecisionDto = JsonUtil.parse(JsonUtil.toJson(decisionVal), PredictDecisionDto.class);
            System.out.println(predictDecisionDto.getDecision_code());
        } catch (Exception ex) {
            System.out.println(ex);
        }

    }

    @Test
    public void test_getAdbRealTime() {
        AdbRealTimeVariableGrayConfig.StrategyGrayConfig grayConfig = new AdbRealTimeVariableGrayConfig.StrategyGrayConfig();
        grayConfig.setAllHits(true);

        AdbRealTimeVariableGrayConfig.VariableGrayConfig variableGrayConfig = new AdbRealTimeVariableGrayConfig.VariableGrayConfig();
        variableGrayConfig.setAllHits(false);
        variableGrayConfig.setVariableList(Arrays.asList("user_cur_available_balance"));

        AppConfigService appConfigService = Mockito.mock(AppConfigService.class);
        Mockito.when(appConfigService.getAdbRealTimeVariableUrl()).thenReturn("https://qa1-xdecision.testxinfei.cn/api/data/data/realCollect");
        Mockito.when(appConfigService.getAdbStrategyGrayConfig()).thenReturn(grayConfig);
        Mockito.when(appConfigService.getAdbVariableGrayConfig()).thenReturn(variableGrayConfig);

        modelPlatformService = new ModelPlatformService(new RestTemplate(),appConfigService);

        AdbRealTimeReq realTimeReq = new AdbRealTimeReq();
        realTimeReq.setRequestId("requestId");
        realTimeReq.setRequestGroup("xyf-cdp");
        Map<String, Object> inputParams = new HashMap<>();
        inputParams.put("requestId", "requestId");
        inputParams.put("app_user_id", 590207979840614401L);
        inputParams.put("timestamp", new Date().getTime());

        List<String> varCodes = Arrays.asList("user_cur_available_balance");
        realTimeReq.setVarCodes(varCodes);
        realTimeReq.setInputParams(inputParams);

        AdbRealTimeResp adbRealTimeResp = modelPlatformService.getAdbRealTime(realTimeReq);
        System.out.println(JsonUtil.toJson(adbRealTimeResp));
    }
}
