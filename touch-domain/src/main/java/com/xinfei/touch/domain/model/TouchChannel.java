package com.xinfei.touch.domain.model;

import lombok.Getter;

/**
 * 触达渠道枚举
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
public enum TouchChannel {
    
    /**
     * 短信渠道
     */
    SMS(1, "短信", "sms"),
    
    /**
     * 电销渠道
     */
    VOICE(2, "电销", "voice"),

    /**
     * 新电销渠道
     */
    VOICE_NEW(4, "新电销", "voice_new"),

    /**
     * Push推送
     */
    PUSH(5, "Push推送", "push"),

    /**
     * 优惠券
     */
    COUPON(3, "优惠券", "coupon"),

    /**
     * AI即时触达
     */
    AI_PRONTO(6, "AI即时触达", "ai_pronto"),

    /**
     * 提额
     */
    INCREASE_AMOUNT(7, "提额", "increase_amount"),

    /**
     * APP横幅
     */
    APP_BANNER(8, "APP横幅", "app_banner"),

    /**
     * X天免息
     */
    X_DAY_INTEREST_FREE(9, "X天免息", "x_day_interest_free"),

    /**
     * 生活权益
     */
    LIFE_RIGHTS(10, "生活权益", "life_rights"),

    /**
     * AI外呼
     */
    AI_CALL(11, "AI外呼", "ai_call"),

    /**
     * 不营销
     */
    NONE(0, "不营销", "none");
    
    /**
     * 渠道编码
     */
    private final Integer code;
    
    /**
     * 渠道名称
     */
    private final String name;
    
    /**
     * 渠道标识
     */
    private final String identifier;
    
    TouchChannel(Integer code, String name, String identifier) {
        this.code = code;
        this.name = name;
        this.identifier = identifier;
    }
    
    /**
     * 根据编码获取渠道
     */
    public static TouchChannel getByCode(Integer code) {
        for (TouchChannel channel : values()) {
            if (channel.getCode().equals(code)) {
                return channel;
            }
        }
        throw new IllegalArgumentException("未知的渠道编码: " + code);
    }
    
    /**
     * 根据标识获取渠道
     */
    public static TouchChannel getByIdentifier(String identifier) {
        for (TouchChannel channel : values()) {
            if (channel.getIdentifier().equals(identifier)) {
                return channel;
            }
        }
        throw new IllegalArgumentException("未知的渠道标识: " + identifier);
    }

    /**
     * 根据编码获取渠道（兼容方法）
     */
    public static TouchChannel fromCode(Integer code) {
        return getByCode(code);
    }
}
