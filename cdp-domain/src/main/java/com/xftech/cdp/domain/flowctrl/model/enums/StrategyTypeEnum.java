package com.xftech.cdp.domain.flowctrl.model.enums;

import lombok.Getter;

/**
 * @<NAME_EMAIL>
 */

@Getter
public enum StrategyTypeEnum {
    /**
     * 批量
     */
    RULE_PAGE(1, "流控规则页面配置"),

    /**
     * 实时
     */
    REALTIME_STRATEGY(2, "T0实时策略"),
    /**
     * 离线策略
     */
    OFFLINE_STRATEGY(3, "离线策略");


    private final Integer code;

    private final String description;

    StrategyTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }
}
