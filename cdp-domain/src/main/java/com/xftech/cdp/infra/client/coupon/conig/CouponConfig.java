package com.xftech.cdp.infra.client.coupon.conig;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@RefreshScope
@Component
@Setter
@Getter
public class CouponConfig {

    @Value("${cdp.coupon.host}")
    private String host;

    // 新 Host 配置项
    @Value("${cdp.coupon.newHost:http://userassetcore.xinfei.io}")
    private String newHost;

    // 切换开关，默认关闭
    @Value("${cdp.coupon.useNewRoutes:false}")
    private boolean useNewRoutes;

    @Value("${cdp.coupon.route.activityList:v2/cash-activity/activity-list-sparrow}")
    private String activityListRoute;

    // 新活动列表路由
    @Value("${cdp.coupon.route.activityListNew:userassetcore/cash-coupon/activity/list-for-sparrow}")
    private String activityListRouteNew;

    @Value("${cdp.coupon.route.sendBatch:v2/cash-activity/send-user-coupon-batch-sparrow}")
    private String sendBatchRoute;

    // 新批量发送路由
    @Value("${cdp.coupon.route.sendBatchNew:userassetcore/cash-coupon/user/batch-send-coupon}")
    private String sendBatchRouteNew;

    @Value("${cdp.coupon.uniqueKey:6Bsaq24YEXqnZ9Mk}")
    private String key;

    @Value("${cdp.coupon.route.getUserCouponList:v2/cash-activity/get-user-coupon-list}")
    private String getUserCouponList;

    @Value("${cdp.coupon.route.getUserCouponList:userassetcore/cash-coupon/user/coupon-list}")
    private String getUserCouponListNew;

    @Value("${cdp.coupon.route.activity-list:v2/cash-activity/activity-list}")
    private String getActivityList;

    @Value("${cdp.coupon.route.activity-list:userassetcore/cash-coupon/activity/list}")
    private String getActivityListNew;

    @Value("${cdp.coupon.route.activity-detail-list:v2/cash-activity/get-coupon-activity-detail-list}")
    private String getActivityCouponList;

    @Value("${cdp.coupon.route.send-user-coupon:v2/cash-activity/send-user-coupon}")
    private String sendUserCoupon;

    @Value("${cdp.coupon.route.send-user-coupon:userassetcore/cash-coupon/user/send-coupon}")
    private String sendUserCouponNew;

    // 动态获取 Host
    public String getHost() {
        return useNewRoutes && !newHost.isEmpty() ? newHost : host;
    }

    // 动态获取活动列表路由
    public String getActivityListRoute() {
        return useNewRoutes ? activityListRouteNew : activityListRoute;
    }

    // 动态获取批量发送路由
    public String getSendBatchRoute() {
        return useNewRoutes ? sendBatchRouteNew : sendBatchRoute;
    }

    public String getGetUserCouponList() {
        return useNewRoutes ? getUserCouponListNew : getUserCouponList;
    }

    //未使用
    public String getGetActivityList() {
        return useNewRoutes ? getActivityListNew : getActivityList;
    }

    public String getSendUserCoupon() {
        return useNewRoutes ? sendUserCouponNew : sendUserCoupon;
    }
}
