# 策略分流逻辑分析报告

## 1. 策略分流的核心判断逻辑

### 1.1 策略类型判断的关键代码位置

**代码位置**: `StrategyEventDispatchServiceImpl.prescreen()` 第230-243行
```java
// 如果是决策引擎策略，则进行灰度判定
if (eventContext.getStrategyDo().isEngineStrategy()) {
    String engineCode = eventContext.getBizEventVO().getEngineCode();
    // 灰度判定逻辑
    if (!isInEngineGrayGroup(eventContext, group)) {
        eventContext.getBizEventVO().setIfIntoEngine(false);
    }
}
```

**代码位置**: `StrategyEventDispatchServiceImpl.rescreen()` 第495-502行
```java
if (StrategyTypeEnum.getInstance(event.getStrategyType()) == StrategyTypeEnum.EVENT_ENGINE 
    && !Objects.equals(event.getIfIntoEngine(), false)) {
    // 走引擎策略流程
    rescreenWithEngine(event);
    return;
}
// 走普通策略流程
```

### 1.2 策略类型的判断依据

**代码位置**: `StrategyDo.isEngineStrategy()` 第234-236行
```java
public boolean isEngineStrategy() {
    return Objects.equals(1, type) && StringUtils.isNotEmpty(engineCode);
}
```

**策略类型枚举**: `StrategyTypeEnum`
- `EVENT(0, "事件")` - 普通策略
- `EVENT_ENGINE(1, "事件引擎")` - 引擎策略

## 2. 引擎策略 vs 普通策略的判断条件

### 2.1 引擎策略的判断条件
一个策略会走引擎策略流程，需要同时满足以下条件：

1. **策略类型为引擎策略**: `strategy.type = 1` (EVENT_ENGINE)
2. **配置了引擎代码**: `strategy.engineCode` 不为空
3. **通过灰度判定**: `isInEngineGrayGroup()` 返回true，或者灰度判定后没有设置 `ifIntoEngine = false`

### 2.2 普通策略的判断条件
一个策略会走普通策略流程的情况：

1. **策略类型为普通事件**: `strategy.type = 0` (EVENT)
2. **引擎策略但未通过灰度**: `strategy.type = 1` 但 `ifIntoEngine = false`
3. **引擎策略但engineCode为空**: `strategy.type = 1` 但 `engineCode` 为空

## 3. 灰度控制机制详解

### 3.1 灰度判定逻辑
**代码位置**: `StrategyEventDispatchServiceImpl.isInEngineGrayGroup()` 第529-541行

```java
private boolean isInEngineGrayGroup(StrategyEventCheckContext eventContext, StrategyGroupDo grayGroup) {
    if (grayGroup != null) {
        StrategyCreateReq.GroupConfig groupConfig = JSON.parseObject(grayGroup.getGroupConfig(),
                StrategyCreateReq.GroupConfig.class);
        if (groupConfig == null || !Objects.equals(1, groupConfig.getSelected())) {
            return false; // 灰度组未选中，不走引擎
        }
    }
    return true; // 默认走引擎
}
```

### 3.2 灰度控制的作用
- **灰度开关**: 通过策略分组的 `groupConfig.selected` 字段控制
- **流量分配**: 只有被选中的灰度组才会走引擎策略
- **降级机制**: 灰度未命中时，引擎策略会降级为普通策略

## 4. 两种策略的执行流程差异

### 4.1 引擎策略执行流程
**入口**: `rescreenWithEngine()` 方法

```
1. 调用决策引擎 (modelPlatformService.prediction)
2. 解析引擎决策结果
3. 如果引擎返回延迟决策 → 延迟处理
4. 如果引擎决策成功 → 执行以下步骤：
   ├── 实时标签查询 (queryLabelHandler)
   ├── 策略复筛 (rescreeningHandler)
   └── 直接营销触达 (marketingSend) - 同步执行
```

### 4.2 普通策略执行流程
**入口**: `rescreen()` 方法的普通流程

```
1. 实时标签查询 (queryLabelHandler)
2. 策略复筛 (rescreeningHandler)  
3. 触达分发 (dispatchHandler)
   ├── 用户分组匹配 (userGroupMatch)
   ├── 流控检查 (flowCtrl)
   └── MQ队列投递 (mqProducerService.channelDelivery) - 异步执行
```

## 5. 关键差异总结

| 维度 | 引擎策略 | 普通策略 |
|------|----------|----------|
| **判断条件** | type=1 且 engineCode不为空 且 通过灰度 | type=0 或 引擎策略未通过灰度 |
| **决策方式** | 外部决策引擎 | 内部规则引擎 |
| **执行方式** | 同步执行marketingSend | 异步MQ队列处理 |
| **流控机制** | 引擎内部控制 | 系统流控 + 分组匹配 |
| **灰度控制** | 支持灰度降级 | 无灰度机制 |
| **容错机制** | 复筛失败仍可营销 | 复筛失败则终止 |

## 6. 实际业务场景

### 6.1 引擎策略适用场景
- 需要复杂决策逻辑的营销场景
- 需要实时AI决策的场景  
- 需要灰度验证效果的新策略
- 对营销精准度要求较高的场景

### 6.2 普通策略适用场景
- 规则相对固定的营销场景
- 大批量用户触达场景
- 对实时性要求不高的场景
- 成熟稳定的营销策略

## 7. 配置示例

### 7.1 引擎策略配置
```sql
-- 策略表配置
UPDATE strategy SET 
  type = 1,                    -- 引擎策略类型
  engineCode = 'risk_engine'   -- 引擎代码
WHERE id = 12345;

-- 策略分组灰度配置
UPDATE strategy_group SET
  groupConfig = '{"selected": 1}' -- 灰度开关开启
WHERE strategy_id = 12345;
```

### 7.2 普通策略配置  
```sql
-- 策略表配置
UPDATE strategy SET 
  type = 0,          -- 普通策略类型
  engineCode = NULL  -- 无引擎代码
WHERE id = 12346;
```
