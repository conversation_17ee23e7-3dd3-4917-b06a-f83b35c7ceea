/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.pulsar.service.impl;

import com.xftech.cdp.domain.strategy.service.dispatch.event.StrategyEventDispatchService;
import com.xftech.cdp.infra.pulsar.service.CdpPulsarService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version $ LoanSuccessServiceImpl, v 0.1 2024/1/8 14:10 lingang.han Exp $
 */

@Slf4j
@Service("loanSuccessService")
@AllArgsConstructor
public class LoanSuccessServiceImpl implements CdpPulsarService {

    private StrategyEventDispatchService strategyEventDispatchService;

    @Override
    public void consumer(String messageId, String eventMessage) {
        toBizEventMessageVO(eventMessage).ifPresent(eventMsg -> {
            log.info("LoanSuccess eventMessage toBizEventMessageVO={}", eventMsg);
            strategyEventDispatchService.prescreen(messageId, eventMsg);
        });
    }
}