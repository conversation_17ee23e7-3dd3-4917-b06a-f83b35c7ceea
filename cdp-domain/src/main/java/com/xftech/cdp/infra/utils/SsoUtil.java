package com.xftech.cdp.infra.utils;


import cn.hutool.extra.spring.SpringUtil;
import com.xftech.cdp.api.dto.resp.auth.LoginUser;
import io.netty.util.concurrent.FastThreadLocal;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR> wanghu
 * @since : 2022/8/9 11:26:09
 */
@Component
public class SsoUtil {

    private static final LoginUser MOCK_USER;
    private static final FastThreadLocal<LoginUser> THREAD_LOCAL = new FastThreadLocal<>();
    private static final FastThreadLocal<Boolean> IS_ANONYMOUS_INTERFACE = new FastThreadLocal<>();

    static {
        MOCK_USER = new LoginUser();
        MOCK_USER.setId(-110L);
        MOCK_USER.setName(HeaderUtil.Constants.APP_NAME);
        MOCK_USER.setMobile("13333333333");
        MOCK_USER.setEmail("<EMAIL>");
        MOCK_USER.setIsDeleted("true");
        LocalDateTime now = LocalDateTime.now();
        MOCK_USER.setCreatedTime(now);
        MOCK_USER.setUpdatedTime(now);
        MOCK_USER.setOpenId("");
        MOCK_USER.setLoginType("Self");
        MOCK_USER.setIsMobileLogin(1);
        MOCK_USER.setGroup("8");
        MOCK_USER.setThirdType("hzy");
        MOCK_USER.setThirdId("1101");
        MOCK_USER.setParentId(0L);
    }

    public static LoginUser get() {

        if ("local".equals(SpringUtil.getProperty("spring.profiles.active"))) {
            return MOCK_USER;
        }

        LoginUser user = THREAD_LOCAL.get();
        if (BooleanUtils.isTrue(IS_ANONYMOUS_INTERFACE.get())) {
            Objects.requireNonNull(user, "user is null.");
        }
        return user;
    }

    public void set(LoginUser loginUser, boolean anonymous) {
        Objects.requireNonNull(loginUser, "Login User Can't Be Null.");
        IS_ANONYMOUS_INTERFACE.set(anonymous);
        THREAD_LOCAL.set(loginUser);
    }

    public void remove() {
        THREAD_LOCAL.remove();
        IS_ANONYMOUS_INTERFACE.remove();
    }

}
