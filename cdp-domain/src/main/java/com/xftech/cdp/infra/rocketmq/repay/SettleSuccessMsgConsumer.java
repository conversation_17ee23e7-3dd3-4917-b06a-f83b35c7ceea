package com.xftech.cdp.infra.rocketmq.repay;

import com.xftech.cdp.infra.rocketmq.MessageHandlerSelector;
import com.xftech.cdp.infra.utils.WhitelistSwitchUtil;

import com.xinfei.xfframework.common.starter.mq.MqConsumerListener;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version $ RiskSingleAdjSesameMsgConsumer, v 0.1 2025/2/25 14:20 tianshuo.qiu Exp $
 */

@Component
@RocketMQMessageListener(consumerGroup = "cg_xyf_cdp_tp_repaytrade_repayResult_settleSuccess", topic = "tp_repaytrade_repayResult", consumeMode = ConsumeMode.CONCURRENTLY)
public class SettleSuccessMsgConsumer extends MqConsumerListener<String> {
    private static final Logger log = LoggerFactory.getLogger(SettleSuccessMsgConsumer.class);
    @Resource
    private MessageHandlerSelector handlerSelector;

    @Override
    protected void doMessage(String topic, String bodyMessage, MessageExt messageExt) {
        if (WhitelistSwitchUtil.boolSwitchByApollo("SettleSuccessMsgConsumerEnable")) {
            log.info("SettleSuccessMsgConsumer doMessage topic={}, messageId={}, body={}", topic, messageExt.getMsgId(), bodyMessage);
            try {
                handlerSelector.doMessage(topic, "settleSuccess", messageExt.getMsgId(), bodyMessage);
            } catch (Exception e) {
                log.error("RepaySuccessMsgConsumer doMessage error, topic={}, messageId={}, body={}", topic, messageExt.getMsgId(), bodyMessage, e);
            }
        }
    }
}
