package com.xftech.cdp.infra.repository.cdp.flowctrl.po;

import com.xftech.cdp.infra.repository.Do;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 流控拦截日志表
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FlowCtrlInterceptionLogDo extends Do {

    /**
     * 拦截用户ID
     */
    private Long userId;

    /**
     * 拦截规则ID
     */
    private Long flowCtrlId;

    /**
     * 策略ID
     */
    private Long strategyId;

    /**
     * 渠道ID
     */
    private Long strategyChannelId;

    /**
     * 拦截时间
     */
    private LocalDateTime interceptionTime;

    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 上报时间
     */
    private LocalDateTime triggerDatetime;


    private Integer marketChannel;

    private String bizEventType;

    private Integer newFlag;

    private String unionId;

    private static final long serialVersionUID = 1L;
}