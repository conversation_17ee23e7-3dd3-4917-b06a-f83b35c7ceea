package com.xftech.cdp.infra.client.datacenter.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> wanghu
 * @since : 2022/8/11 10:31:26
 */
@Setter
@Getter
public class BaseDataCenterResp<R> {

    @JsonProperty("status")
    @JSONField(name = "status")
    private Integer status;

    @JsonProperty("message")
    @JSONField(name = "message")
    private String message;

    @JsonProperty("data")
    @JSONField(name = "data")
    private transient R data;

    public boolean isSuccess() {
        return status != null && 1 == status;
    }

}
