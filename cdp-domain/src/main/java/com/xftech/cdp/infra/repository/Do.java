package com.xftech.cdp.infra.repository;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2023/2/8
 */
@Getter
@Setter
public class Do implements Serializable {
    /**
     * 自增ID主键
     */
    private Long id;
    /**
     * 是否删除：1删除0未删除
     */
    private Integer dFlag;
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    /**
     * 创建人
     */
    private String createdOp;
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
    /**
     * 更新人
     */
    private String updatedOp;
}
