package com.xftech.cdp.infra.repository.cdp.strategy.po;

import com.xftech.cdp.infra.repository.Do;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 用户触达明细表
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserDispatchDetailDo extends Do {
    /**
     * 表名
     */
    private String tableName;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 批次号
     */
    private String batchNum;

    /**
     * 人群包ID
     */
    private Long crowdPackId;

    /**
     * 策略ID
     */
    private Long strategyId;

    /**
     * 策略渠道ID
     */
    private Long strategyChannelId;

    /**
     * 营销渠道，0: 不营销，1:短信，2:电销，3:优惠券
     */
    private Integer marketChannel;

    /**
     * 策略执行ID
     */
    private String strategyExecId;

    /**
     * 执行日志id
     */
    private Long execLogId;

    /**
     * 状态 0-失败 1-成功
     */
    private Integer status;

    /**
     * 使用状态
     */
    private Integer usedStatus;

    /**
     * 触达时间
     */
    private LocalDateTime dispatchTime;

    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 上报时间
     */
    private LocalDateTime triggerDatetime;

    /**
     * 引擎返回groupId
     */
    private String groupName;

    /**
     * 麻雀分组id
     */
    private Long strategyGroupId;

    /**
     * 麻雀分组name
     */
    private String strategyGroupName;

    private String bizEventType;

    private String templateId;

    private String extDetail;

    private String dispatchType;

    /**
     * 业务线
     */
    private String bizType;

    private String unionId;

    private static final long serialVersionUID = 1L;
}