package com.xftech.cdp.domain.strategy.model.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 */

public enum SmsStatusEnum {
    /**
     * 成功
     */
    DELIVERED("delivered", "到达", 1, -1),
    /**
     * 错误
     */
    FAILED("failed", "错误", 0, -1);

    private final String finalStatus;
    private final String description;
    private final Integer status;
    private final Integer usedStatus;

    SmsStatusEnum(String finalStatus, String description, Integer status, Integer usedStatus){
        this.finalStatus = finalStatus;
        this.description = description;
        this.status = status;
        this.usedStatus = usedStatus;
    }

    public Integer getStatus() {
        return status;
    }

    public Integer getUsedStatus() {
        return usedStatus;
    }

    public String getFinalStatus() {
        return finalStatus;
    }

    public String getDescription() {
        return description;
    }

    public static SmsStatusEnum getEnum(String finalStatus) {
        for (SmsStatusEnum smsStatusEnum : SmsStatusEnum.values()) {
            if (Objects.equals(smsStatusEnum.getFinalStatus(), finalStatus)) {
                return smsStatusEnum;
            }
        }
        return null;
    }
}