package com.xftech.cdp.domain.crowd.service.dispatch.impl;

import com.dianping.cat.proxy.Tracer;
import com.xftech.cdp.domain.crowd.model.dispatch.CrowdContext;
import com.xftech.cdp.domain.crowd.model.dispatch.CrowdWereHouse;
import com.xftech.cdp.domain.crowd.model.dispatch.InvokePage;
import com.xftech.cdp.domain.crowd.repository.AdsLabelMonitorDfRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdDetailRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdExecLogRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdExecSnapshotRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdLabelPrimaryRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdLabelRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdPackRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdWereHouseSnapshotRepository;
import com.xftech.cdp.domain.crowd.repository.LabelRepository;
import com.xftech.cdp.domain.crowd.service.CrowdDetailSubService;
import com.xftech.cdp.domain.crowd.service.dispatch.CrowdDispatchService;
import com.xftech.cdp.infra.client.datacenter.DataCenterClient;
import com.xftech.cdp.infra.client.usercenter.UserCenterClient;
import com.xftech.cdp.infra.config.CrowdConfig;
import com.xftech.cdp.infra.utils.ListUtils;
import com.xftech.xxljob.XxlJobAdminClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/2/13
 */
@Slf4j
@Service("crowdDispatchStartRockService")
public class CrowdDispatchStartRockServiceImpl extends AbstractCrowdDispatchService implements CrowdDispatchService {


    public CrowdDispatchStartRockServiceImpl(CrowdPackRepository crowdPackRepository, CrowdLabelRepository crowdLabelRepository, CrowdLabelPrimaryRepository crowdLabelPrimaryRepository, LabelRepository labelRepository, CrowdDetailRepository crowdDetailRepository, CrowdExecSnapshotRepository crowdExecSnapshotRepository, AdsLabelMonitorDfRepository adsLabelMonitorDfRepository, UserCenterClient userCenterClient, DataCenterClient dataCenterClient, CrowdExecLogRepository crowdExecLogRepository, XxlJobAdminClient xxlJobAdminClient, CrowdWereHouseSnapshotRepository crowdWereHouseSnapshotRepository, CrowdConfig crowdConfig, CrowdDetailSubService crowdDetailSubService) {
        super(crowdPackRepository, crowdLabelRepository, crowdLabelPrimaryRepository, labelRepository, crowdDetailRepository, crowdExecSnapshotRepository, adsLabelMonitorDfRepository, userCenterClient, dataCenterClient, crowdExecLogRepository, xxlJobAdminClient, crowdWereHouseSnapshotRepository, crowdConfig, crowdDetailSubService);
    }

    @Override
    public void filterAllUserIds(CrowdContext crowdContext, InvokePage invokePage) {
        Pair<StringBuilder, StringBuilder> labelSqlPair = crowdContext.getLabelSqlPair();

        invokePageById(invokePage, labelSqlPair.getLeft(), includeWereHouses -> {
            if (CollectionUtils.isEmpty(includeWereHouses)) {
                return;
            }
            StringBuilder excludeSql = new StringBuilder(labelSqlPair.getRight());
            andFilter(excludeSql, ListUtils.distinctMap(includeWereHouses, CrowdWereHouse::getAppUserId));
            long beginMillis = System.currentTimeMillis();
            // List<CrowdWereHouse> excludeWereHouses = adsJdbcTemplate.query( excludeSql.toString(), crowdWereHouseBeanPropertyRowMapper );
            List<CrowdWereHouse> excludeWereHouses = crowdWareHouseRepository.queryWareHouseUserInfo(excludeSql.toString());
            long interval = System.currentTimeMillis() - beginMillis;
            log.info("crowd execute exclude sql:{} take:{}, isRunAsFixedNumberPage:{}", excludeSql, interval, crowdContext.isRunAsFixedNumberPage());
            Set<Long> excludeUserIds = ListUtils.toSet(excludeWereHouses, CrowdWereHouse::getAppUserId);
            //
            batchSave(includeWereHouses.stream().filter(includeWereHouse -> !excludeUserIds.contains(includeWereHouse.getAppUserId())).distinct().collect(Collectors.toList()), crowdContext, invokePage.getBatchConsumer());
            excludeWereHouses = null;
        }, crowdContext);
    }

    protected void andFilter(StringBuilder sql, List<Long> appUserIds) {
        String userIds = appUserIds.stream().map(String::valueOf).collect(Collectors.joining(","));
        sql.append(" ) and app_user_id in(").append(userIds).append(")");
    }

    public void filter(CrowdContext crowdContext, List<CrowdWereHouse> crowdWereHouses){
        filterUtmSource(crowdContext, crowdWereHouses); // TODO 初步排查线上无实际过滤效果的，待确认是否可移除
        filterNewRandom(crowdContext, crowdWereHouses);
    }


}
