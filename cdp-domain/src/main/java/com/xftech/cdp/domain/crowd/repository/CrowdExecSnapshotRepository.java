package com.xftech.cdp.domain.crowd.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdExecSnapshotDo;
import org.springframework.stereotype.Component;

/**
 * 人群包执行快照表操作
 *
 * <AUTHOR>
 * @since 2023/2/14
 */

@Component
public class CrowdExecSnapshotRepository {
    /**
     * 插入一条人群包执行快照表记录
     *
     * @param beginExecute 人群包执行快照对象
     */
    public void insert(CrowdExecSnapshotDo beginExecute) {
        DBUtil.insert("crowdExecSnapshot.insertSelective", beginExecute);
    }

    public String selectSnapshotByCrowdId(Long crowdId) {
        return DBUtil.selectOne("crowdExecSnapshot.selectSnapshotByCrowdId", crowdId);
    }
}
