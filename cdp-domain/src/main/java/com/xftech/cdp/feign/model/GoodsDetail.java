/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.feign.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version $ GoodsDetail, v 0.1 2024/5/7 10:28 lingang.han Exp $
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GoodsDetail {

    private Long goodsId;

    private String goodsName;

    private String brandName;

    private String goodsType;

    private Long goodsPriceOfficial;

    private Long goodsPriceSell;

    private String supplier;

    private Integer jumpType;

    private String jumpUrl;


}