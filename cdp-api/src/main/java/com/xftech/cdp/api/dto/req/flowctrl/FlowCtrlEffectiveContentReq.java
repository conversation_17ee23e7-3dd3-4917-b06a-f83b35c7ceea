package com.xftech.cdp.api.dto.req.flowctrl;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @<NAME_EMAIL>
 */

@Data
public class FlowCtrlEffectiveContentReq {
    @NotNull(message = "规则类型不能为空")
    @ApiModelProperty(value = "规则类型 1-策略 2-渠道 3多策略共享 4-业务线", required = true)
    private Integer type;

    @ApiModelProperty(value = "规则编号")
    private Long ruleId;

    @NotNull(message = "业务线不能为空")
    @ApiModelProperty(value = "业务线")
    private String bizType;

}
