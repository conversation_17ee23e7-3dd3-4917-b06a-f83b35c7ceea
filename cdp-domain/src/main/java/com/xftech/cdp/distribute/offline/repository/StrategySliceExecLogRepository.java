/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2025. All Rights Reserved.
 */

package com.xftech.cdp.distribute.offline.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.infra.repository.cdp.slice.po.StrategySliceExecLogDo;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version $ StrategySliceExecLogRepository, v 0.1 2025/5/4 11:01 xu.fan Exp $
 */
@Component
public class StrategySliceExecLogRepository {

    public int insert(StrategySliceExecLogDo execLogDo) {
        return DBUtil.insert("strategySliceExecLogMapper.insert", execLogDo);
    }

    public int batchInsert(List<StrategySliceExecLogDo> execLogDoList) {
        Map<String, Object> params = new HashMap<>();
        params.put("execLogDoList", execLogDoList);
        return DBUtil.insert("strategySliceExecLogMapper.batchInsert", params);
    }

    public void deleteById(Long id) {
        DBUtil.delete("strategySliceExecLogMapper.deleteByPrimaryKey", id);
    }

    public int insertSelective(StrategySliceExecLogDo execLogDo) {
        return DBUtil.insert("strategySliceExecLogMapper.insertSelective", execLogDo);
    }

    public StrategySliceExecLogDo selectByPrimaryKey(Long id) {
        return DBUtil.selectOne("strategySliceExecLogMapper.selectByPrimaryKey", id);
    }

    public List<StrategySliceExecLogDo> selectByParam(Long strategyId, LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> params = new HashMap<>();
        params.put("strategyId", strategyId);
        params.put("startTime", startTime);
        params.put("endTime", endTime);
        return DBUtil.selectList("strategySliceExecLogMapper.selectByParam", params);
    }

    public List<StrategySliceExecLogDo> selectTodoList(LocalDateTime startTime, LocalDateTime endTime, Integer limit) {
        Map<String, Object> params = new HashMap<>();
//        params.put("strategyId", strategyId);
        params.put("startTime", startTime);
        params.put("endTime", endTime);
        params.put("limit", limit);
        return DBUtil.selectList("strategySliceExecLogMapper.selectTodoList", params);
    }

    public List<StrategySliceExecLogDo> selectStrategyTodoList(Long strategyId, LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> params = new HashMap<>();
        params.put("strategyId", strategyId);
        params.put("startTime", startTime);
        params.put("endTime", endTime);
        return DBUtil.selectList("strategySliceExecLogMapper.selectStrategyTodoList", params);
    }

    public Map<String, Long> selectStrategyCnt(Long strategyId, LocalDateTime startTime, LocalDateTime endTime, Long crowdVersion) {
        Map<String, Object> params = new HashMap<>();
        params.put("strategyId", strategyId);
        params.put("startTime", startTime);
        params.put("endTime", endTime);
        params.put("crowdVersion", crowdVersion);
        return DBUtil.selectOne("strategySliceExecLogMapper.selectStrategyCnt", params);
    }

    public int updateByPrimaryKeySelective(StrategySliceExecLogDo execLogDo) {
        if(execLogDo == null || execLogDo.getId() == null) {
            throw new IllegalArgumentException("id cannot be null");
        }
        return DBUtil.update("strategySliceExecLogMapper.updateByPrimaryKeySelective", execLogDo);
    }

    public int updateByPrimaryKey(StrategySliceExecLogDo execLogDo) {
        if(execLogDo == null || execLogDo.getId() == null) {
            throw new IllegalArgumentException("id cannot be null");
        }
        return DBUtil.update("strategySliceExecLogMapper.updateByPrimaryKeySelective", execLogDo);
    }

    public int updateStatus(Long id, Integer status, Integer retryTimes, Integer fromStatus) {
        Map<String, Object> params = new HashMap<>();
        params.put("id", id);
        params.put("status", status);
        params.put("retryTimes", retryTimes);
        params.put("fromStatus", fromStatus);
        return DBUtil.update("strategySliceExecLogMapper.updateStatus", params);
    }

    public int updateNextDispatchTimeAndStatus(Long id, Integer status, Integer fromStatus, LocalDateTime nextDispatchTime, String errorMsg) {
        Map<String, Object> params = new HashMap<>();
        params.put("id", id);
        params.put("status", status);
        params.put("nextDispatchTime", nextDispatchTime);
        params.put("fromStatus", fromStatus);
        params.put("errorMsg", errorMsg);
        return DBUtil.update("strategySliceExecLogMapper.updateNextDispatchTimeAndStatus", params);
    }

    public int updateCntNum(Long id, Long totalUserCnt, Long execUserCnt, Long dispatchCnt) {
        Map<String, Object> params = new HashMap<>();
        params.put("id", id);
        params.put("totalUserCnt", totalUserCnt);
        params.put("execUserCnt", execUserCnt);
        params.put("dispatchCnt", dispatchCnt);
        return DBUtil.update("strategySliceExecLogMapper.updateCntNum", params);
    }
}



