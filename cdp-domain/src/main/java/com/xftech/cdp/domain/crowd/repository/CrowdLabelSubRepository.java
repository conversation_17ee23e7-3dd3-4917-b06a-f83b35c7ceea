package com.xftech.cdp.domain.crowd.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdLabelDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdLabelSubDo;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 人群包二级
 * <AUTHOR>
 * @since 2023-05-04
 */

@Component
public class CrowdLabelSubRepository {

    /**
     * 根据人群包id查询标签配置
     *
     * @param crowdId 人群包id
     * @return 人群包标签配置列表
     */
    public List<CrowdLabelSubDo> selectSubListByCrowdId(Long crowdId) {
        return DBUtil.selectList("crowdLabelSub.selectSubListByCrowdId", crowdId);
    }

    /**
     * 根据人群包id删除标签配置
     *
     * @param crowdId 人群包id
     */
    public void deleteByCrowdId(Long crowdId) {
        DBUtil.delete("crowdLabelSub.deleteByCrowdId", crowdId);
    }

    /**
     * 插入一条人群包标签配置记录
     *
     * @param crowdLabelSubDo 人群包标签配置
     */
    public void insert(CrowdLabelSubDo crowdLabelSubDo) {
        DBUtil.insert("crowdLabelSub.insertSelective", crowdLabelSubDo);
    }
}
