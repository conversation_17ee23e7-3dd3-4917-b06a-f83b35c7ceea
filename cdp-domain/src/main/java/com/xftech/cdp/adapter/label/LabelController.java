/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.adapter.label;

import com.xftech.cdp.api.LabelApi;
import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.base.Response;
import com.xftech.cdp.api.dto.req.CrowdCreateReq;
import com.xftech.cdp.api.dto.req.label.*;
import com.xftech.cdp.api.dto.resp.label.AssociatedCrowdsResp;
import com.xftech.cdp.api.dto.resp.label.LabelClassificationResp;
import com.xftech.cdp.api.dto.resp.label.LabelConfigListResp;
import com.xftech.cdp.api.dto.resp.label.LabelExchangeResp;
import com.xftech.cdp.domain.label.biz.LabelBiz;
import com.xftech.cdp.infra.annotation.OperateLogAnnotation;
import com.xftech.cdp.infra.aviator.enums.OperateModeEnum;
import com.xftech.cdp.infra.aviator.enums.OperateTypeEnum;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ LabelController, v 0.1 2024/6/20 15:02 lingang.han Exp $
 */

@RestController
@AllArgsConstructor
public class LabelController implements LabelApi {
    private final LabelBiz labelBiz;

    @Override
    public Response<List<LabelClassificationResp>> classification() {
        return Response.success(labelBiz.classification());
    }

    @Override
    public Response<List<LabelExchangeResp>> configList() {
        return Response.success(labelBiz.configList());
    }

    @Override
    public Response<PageResultResponse<LabelConfigListResp>> list(LabelConfigListReq labelConfigListReq) {
        return Response.success(labelBiz.list(labelConfigListReq));
    }

    @Override
    public Response<Boolean> detail(CrowdCreateReq crowdCreateReq) {
        return null;
    }

    @Override
    public Response<PageResultResponse<AssociatedCrowdsResp>> associatedCrowds(AssociatedCrowdsReq associatedCrowdsReq) {
        return Response.success(labelBiz.associatedCrowds(associatedCrowdsReq));
    }

    @Override
    @OperateLogAnnotation(description = "标签发布or重复发布", type = OperateTypeEnum.PUBLISH, mode = OperateModeEnum.LABEL)
    public Response<Boolean> labelPublish(LabelPublishReq labelPublishReq) {
        return Response.success(labelBiz.labelPublish(labelPublishReq));
    }

    @Override
    @OperateLogAnnotation(description = "标签备注修改", type = OperateTypeEnum.UPDATE, mode = OperateModeEnum.LABEL)
    public Response<Boolean> labelRemark(LabelRemarkReq labelRemarkReq) {
        return Response.success(labelBiz.labelRemark(labelRemarkReq));
    }

    @Override
    @OperateLogAnnotation(description = "下线标签", type = OperateTypeEnum.DELETE, mode = OperateModeEnum.LABEL)
    public Response<Boolean> labelDiscard(LabelDiscardReq labelDiscardReq) {
        return Response.success(labelBiz.labelDiscard(labelDiscardReq));
    }

    @Override
    public Response<Boolean> clearCheckResult(ClearCheckResultReq checkResultReq) {
        return Response.success(labelBiz.clearCheckResult(checkResultReq));
    }
}