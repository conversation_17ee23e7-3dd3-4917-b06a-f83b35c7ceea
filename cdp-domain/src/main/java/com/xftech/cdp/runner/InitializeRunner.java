package com.xftech.cdp.runner;

import com.xftech.base.common.util.LogUtil;
import com.xftech.base.log.IUdpLogger;
import com.xftech.cdp.domain.cache.CacheStrategyService;
import com.xftech.cdp.domain.strategy.service.dispatch.event.StrategyEventCatchService;
import com.xftech.cdp.infra.aviator.AviatorCommon;
import com.xftech.cdp.infra.utils.RedisKeyUtils;
import com.xftech.cdp.infra.utils.RedisUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * aviator自定义操作符加载类
 */
@Component
public class InitializeRunner implements ApplicationRunner {
    private static final IUdpLogger logger = LogUtil.getLogger(InitializeRunner.class);

    @Autowired
    StrategyEventCatchService strategyEventCatchService;
    @Autowired
    private CacheStrategyService cacheStrategyService;
    @Autowired
    private RedisUtils redisUtils;

    @Override
    public void run(ApplicationArguments args) {

        loadFunction();

        loadCache();


    }

    private void loadFunction() {
        AviatorCommon.loadFunction();
        logger.info("init aviator function success!");
    }

    private void loadCache() {
        // 项目启动时，设置事件触发时间
        strategyEventCatchService.initEventTriggerTime();

        String refreshKey = RedisKeyUtils.genRefreshKey();
        if (redisUtils.lock(refreshKey, 1, RedisUtils.DEFAULT_EXPIRE_DAYS)) {
            // 加载标签元数据
            strategyEventCatchService.catchMetaData();
            // 加载T0（事件策略）状态为执行中的策略列表
            cacheStrategyService.refreshT0ExecutingStrategy();
        }
    }
}
