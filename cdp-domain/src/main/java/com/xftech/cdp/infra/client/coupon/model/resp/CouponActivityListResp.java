package com.xftech.cdp.infra.client.coupon.model.resp;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class CouponActivityListResp {

    @JsonProperty("result")
    @JSONField(name = "result")
    private List<CouponInfo> result;


    @JsonProperty("coupon_type_map")
    @JSONField(name = "coupon_type_map")
    private Map<String, String> couponTypeMap;

    @JsonProperty("total")
    @JSONField(name = "total")
    private Integer total;

    @JsonProperty("page_info")
    @JSONField(name = "page_info")
    private PageInfo pageInfo;


    @Data
    public static class CouponInfo {

        @JsonProperty("id")
        @JSONField(name = "id")
        private String id;

        @JsonProperty("name")
        @JSONField(name = "name")
        private String name;

        @JsonProperty("coupon_name")
        @JSONField(name = "coupon_name")
        private String couponName;

        @JsonProperty("coupon_id")
        @JSONField(name = "coupon_id")
        private String couponId;

        @JsonProperty("coupon_type")
        @JSONField(name = "coupon_type")
        private String couponType;

        @JsonProperty("discount_amount")
        @JSONField(name = "discount_amount")
        private String discountAmount;

        /**
         * 	优惠折扣 50=打5折
         */
        @JsonProperty("discount_rate")
        @JSONField(name = "discount_rate")
        private String discountRate;

        /**
         * 	1立减 2折扣
         */
        @JsonProperty("discount_type")
        @JSONField(name = "discount_type")
        private String discountType;

        /**
         * (消费券)有效期   单位：天，发送成功后起算
         */
        @JsonProperty("expired_days")
        @JSONField(name = "expired_days")
        private String expiredDays;

        /**
         * （金融券）有效期   单位：天，发送成功后起算
         */
        @JsonProperty("valid_days")
        @JSONField(name = "valid_days")
        private String validDays;

        /**
         * 单位：天，结清免息天数
         */
        @JsonProperty("valid_days_after")
        @JSONField(name = "valid_days_after")
        private String validDaysAfter;
    }

    @Data
    public static class PageInfo {

        @JsonProperty("page")
        @JSONField(name = "page")
        private Integer page;

        @JsonProperty("count")
        @JSONField(name = "count")
        private Integer count;

        @JsonProperty("page_size")
        @JSONField(name = "page_size")
        private Integer pageSize;
    }

}
