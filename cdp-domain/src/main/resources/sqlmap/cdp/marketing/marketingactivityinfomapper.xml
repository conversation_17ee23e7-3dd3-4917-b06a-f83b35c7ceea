<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="marketingActivityInfo">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.marketing.po.MarketingActivityInfoDo">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="validity_begin" property="validityBegin"/>
        <result column="validity_end" property="validityEnd"/>
        <result column="status" property="status"/>
        <result column="d_flag" property="dFlag"/>
        <result column="created_op" property="createdOp"/>
        <result column="updated_op" property="updatedOp"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
    </resultMap>

    <!-- 通用查询字段 -->
    <sql id="Base_Column_List">
        id
        , name, validity_begin, validity_end, status, d_flag, created_op, updated_op, created_time, updated_time
    </sql>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from marketing_activity_info where id = #{id} and d_flag = 0
    </select>

    <insert id="insertSelective"
            parameterType="com.xftech.cdp.infra.repository.cdp.marketing.po.MarketingActivityInfoDo"
            keyColumn="id" keyProperty="id" useGeneratedKeys="true">
        insert into marketing_activity_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">
                name,
            </if>
            <if test="validityBegin != null">
                validity_begin,
            </if>
            <if test="validityEnd != null">
                validity_end,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="createdOp != null">
                created_op,
            </if>
            <if test="updatedOp != null">
                updated_op,
            </if>
            created_time,
            updated_time,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">
                #{name},
            </if>
            <if test="validityBegin != null">
                #{validityBegin},
            </if>
            <if test="validityEnd != null">
                #{validityEnd},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="createdOp != null">
                #{createdOp},
            </if>
            <if test="updatedOp != null">
                #{updatedOp},
            </if>
            now(),
            now(),
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.xftech.cdp.infra.repository.cdp.marketing.po.MarketingActivityInfoDo">
        update marketing_activity_info
        <set>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="validityBegin != null">
                validity_begin = #{validityBegin},
            </if>
            <if test="validityEnd != null">
                validity_end = #{validityEnd},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="createdOp != null">
                created_op = #{createdOp},
            </if>
            <if test="updatedOp != null">
                updated_op = #{updatedOp},
            </if>
            updated_time = now()
        </set>
        where id = #{id}
    </update>

    <delete id="deleteById">
        update marketing_activity_info
        set d_flag = 1
        where id = #{id}
    </delete>

</mapper>
