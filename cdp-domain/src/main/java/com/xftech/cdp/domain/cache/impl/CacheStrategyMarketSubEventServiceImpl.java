package com.xftech.cdp.domain.cache.impl;

import com.alibaba.fastjson.JSONArray;
import com.xftech.cdp.domain.strategy.repository.StrategyMarketSubEventRepository;
import com.xftech.cdp.domain.cache.CacheStrategyMarketSubEventService;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketSubEventDo;
import com.xftech.cdp.infra.utils.RedisKeyUtils;
import com.xftech.cdp.infra.utils.RedisUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

@Service
public class CacheStrategyMarketSubEventServiceImpl implements CacheStrategyMarketSubEventService {
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private StrategyMarketSubEventRepository strategyMarketSubEventRepository;

    private static final String DEFAULT_VALUE = "(null)";

//    /**
//     * 根据主键id查询
//     *
//     * @param id id
//     * @return id对应的记录
//     */
//    public StrategyMarketSubEventDo selectById(Long id) {
//        String redisKey = String.format(RedisKeyConstants.SMEC_ONE, id);
//        StrategyMarketSubEventDo strategyMarketSubEventDo = redisUtils.get(redisKey, StrategyMarketSubEventDo.class);
//        if (strategyMarketSubEventDo == null) {
//            strategyMarketSubEventDo = strategyMarketSubEventRepository.selectById(id);
//            redisUtils.set(redisKey, strategyMarketSubEventDo, RedisUtils.NOT_EXPIRE);
//        }
//        return strategyMarketSubEventDo;
//    }

    /**
     * 插入
     *
     * @param param 对象
     * @return 是否插入成功标识
     */
    public boolean insert(StrategyMarketSubEventDo param) {
        boolean isInsert = strategyMarketSubEventRepository.insert(param);
        if (isInsert) {
//            this.delListKey(param.getMarketEventId());
            refreshCacheListByEventId(param.getMarketEventId());
        }
        return isInsert;
    }

    /**
     * 根据主键id更新
     *
     * @param param 对象
     * @return 是否更新成功标识
     */
    public boolean updateById(StrategyMarketSubEventDo param) {
        boolean isUpdate = strategyMarketSubEventRepository.updateById(param);
        if (isUpdate) {
            refreshCacheListByEventId(param.getMarketEventId());

//            String redisKey = String.format(RedisKeyConstants.SMSE_ONE, param.getId());
//            redisUtils.set(redisKey, param, RedisUtils.NOT_EXPIRE);
//            this.delListKey(param.getStrategyId());
        }
        return isUpdate;
    }

    /**
     * 根据事件ID查询
     *
     * @param eventId 事件ID
     * @return 子事件集合
     */
    public List<StrategyMarketSubEventDo> getByEventId(Long eventId) {
        String redisKey = RedisKeyUtils.genEventIdListKey(eventId);
        String data = redisUtils.get(redisKey);
        if (StringUtils.isNotBlank(data) && data.equals(DEFAULT_VALUE)) {
            return Collections.emptyList();
        }

        List<StrategyMarketSubEventDo> strategyMarketSubEventDoList = Collections.emptyList();
        if (StringUtils.isNotBlank(data)) {
            strategyMarketSubEventDoList = JSONArray.parseArray(data, StrategyMarketSubEventDo.class);
        }

        if (CollectionUtils.isEmpty(strategyMarketSubEventDoList)) {
            strategyMarketSubEventDoList = strategyMarketSubEventRepository.getByEventId(eventId);
            if (CollectionUtils.isEmpty(strategyMarketSubEventDoList)) {
                redisUtils.set(redisKey, DEFAULT_VALUE, RedisUtils.DEFAULT_EXPIRE_SECONDS);
            } else {
                redisUtils.set(redisKey, strategyMarketSubEventDoList, RedisUtils.DEFAULT_EXPIRE_SECONDS);
            }
        }
        return strategyMarketSubEventDoList;
    }

    public List<StrategyMarketSubEventDo> getByStrategyId(Long strategyId) {
        String redisKey = RedisKeyUtils.genEventIdListKeyByStrategyId(strategyId);
        String data = redisUtils.get(redisKey);
        List<StrategyMarketSubEventDo> strategyMarketSubEventDoList = JSONArray.parseArray(data, StrategyMarketSubEventDo.class);
        if (CollectionUtils.isEmpty(strategyMarketSubEventDoList)) {
            strategyMarketSubEventDoList = strategyMarketSubEventRepository.getByStrategyId(strategyId);
            redisUtils.set(redisKey, strategyMarketSubEventDoList, RedisUtils.DEFAULT_EXPIRE_SECONDS);
        }
        return strategyMarketSubEventDoList;
    }

    /**
     * 插入
     *
     * @param strategyMarketSubEventList 对象
     * @return 是否插入成功标识
     */
    public boolean insertBatch(List<StrategyMarketSubEventDo> strategyMarketSubEventList ) {
        boolean isInsert = strategyMarketSubEventRepository.insertBatch(strategyMarketSubEventList);
        if (isInsert) {
            refreshCacheListByEventId(strategyMarketSubEventList.get(0).getMarketEventId());
        }
        return isInsert;
    }

    /**
     * 插入
     *
     * @param delSubEventIdList 对象
     */
    public void deleteBatch(List<Long> delSubEventIdList ) {
        StrategyMarketSubEventDo strategyMarketSubEventDo = strategyMarketSubEventRepository.selectById(delSubEventIdList.get(0));
        strategyMarketSubEventRepository.deleteBatch(delSubEventIdList);
        refreshCacheListByEventId(strategyMarketSubEventDo.getMarketEventId());
//        delSubEventIdList.forEach(delSubEventId -> {
//
//            this.delListKey(delSubEventId);
//        });
    }

//    private void delListKey(Long eventId) {
//        String redisKey = String.format(RedisKeyConstants.SMSE_LIST, eventId);
//        if (redisUtils.hasKey(redisKey)) {
//            redisUtils.delete(redisKey);
//        }
//    }

    public void refreshCacheListByEventId(Long eventId){
        String redisKey = RedisKeyUtils.genEventIdListKey(eventId);
        List<StrategyMarketSubEventDo> strategyMarketSubEventDoList = strategyMarketSubEventRepository.getByEventId(eventId);
        redisUtils.set(redisKey, strategyMarketSubEventDoList, RedisUtils.DEFAULT_EXPIRE_SECONDS);
    }



}
