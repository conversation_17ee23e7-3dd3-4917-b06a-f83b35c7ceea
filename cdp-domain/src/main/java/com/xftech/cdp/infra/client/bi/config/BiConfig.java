package com.xftech.cdp.infra.client.bi.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Getter
@Component
public class BiConfig {
    @Value("${cdp.bi.host:http://bi-model.xinyongfei.cn}")
    private String host;

    @Value("${cdp.bi.route.marketing:model_service/marketing}")
    private String marketingRoute;
}
