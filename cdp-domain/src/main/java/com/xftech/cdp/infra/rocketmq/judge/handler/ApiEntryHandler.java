package com.xftech.cdp.infra.rocketmq.judge.handler;

import com.alibaba.fastjson.JSONObject;
import com.xftech.cdp.domain.mq.MqConsumeService;
import com.xftech.cdp.infra.aviator.util.DateTimeUtil;
import com.xftech.cdp.infra.config.LogUtil;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventMessageVO;
import com.xftech.cdp.infra.rocketmq.EventEnum;
import com.xftech.cdp.infra.rocketmq.MessageHandler;
import com.xftech.cdp.infra.rocketmq.judge.model.ApiEntryVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

import static com.xftech.cdp.infra.rocketmq.RocketMQEnum.TP_APIOPFCORE_CREDIT_RESULT;

/**
 * <AUTHOR>
 * @version $ ApiEntryHandler, v 0.1 2025/4/24 16:01 tianshuo.qiu Exp $
 */
@Slf4j
@Component
public class ApiEntryHandler implements MessageHandler {
    @Autowired
    private MqConsumeService mqConsumeService;

    @Override
    public String getTopic() {
        return TP_APIOPFCORE_CREDIT_RESULT.getTopic();
    }

    @Override
    public boolean execute(String messageId, Object message) {
        try {
            ApiEntryVO messageVO = JSONObject.parseObject(message.toString(), ApiEntryVO.class);
            BizEventMessageVO bizEventMessageVO = transform(messageVO);
            if (bizEventMessageVO == null) {
                return false;
            }
            bizEventMessageVO.setBizEventType(EventEnum.API_ENTRY.getEventType());
            mqConsumeService.bizEventProcess(messageId, bizEventMessageVO);
            return true;
        } catch (Exception e) {
            log.error("ApiEntryHandler execute 不符合API撞库事件上报筛选条件 ID {}: {}", messageId, e.getMessage(), e);
            return false;
        }
    }

    private BizEventMessageVO transform(ApiEntryVO apiEntryVO) {
        BizEventMessageVO bizEventMessageVO = new BizEventMessageVO();
        if (apiEntryVO.getUserNo() == null || StringUtils.isAnyBlank(apiEntryVO.getInnerApp(), apiEntryVO.getApp(), apiEntryVO.getActivationStatus())) {
            LogUtil.logDebug("ApiEntryHandler transform 不符合API撞库事件上报筛选条件: {}", apiEntryVO);
            return null;
        }
        bizEventMessageVO.setAppUserId(apiEntryVO.getUserNo());
        bizEventMessageVO.setInnerApp(apiEntryVO.getInnerApp());
        bizEventMessageVO.setApp(apiEntryVO.getApp());

        bizEventMessageVO.setTriggerDatetime(DateTimeUtil.formatDateToStr(new Date(), null));


        BizEventMessageVO.ExtrData extrData = new BizEventMessageVO.ExtrData();

        String activationStatus = apiEntryVO.getActivationStatus();
        Integer historyCreditSuccessFlag = apiEntryVO.getHistoryCreditSuccessFlag();
        String failedType = apiEntryVO.getFailedType();

        int apiEntryResult = 0; // 初始化APIEntryResult

        /*
         * 成功+是历史授信成功=1 成功-历史已授信
         * 成功+否历史授信成功=2 成功-新授信
         * 授信失败+非风险被拒=3 失败-未完件
         * 授信失败+风险授信失败=4 失败-授信失败
         */
        if ("success".equals(activationStatus)) {
            if (historyCreditSuccessFlag != null && historyCreditSuccessFlag == 1) {
                apiEntryResult = 1;
            }
            if (historyCreditSuccessFlag != null && historyCreditSuccessFlag == 0) {
                apiEntryResult = 2;
            }
        } else if ("failed".equals(activationStatus) && StringUtils.isNotBlank(failedType)) {
            if ("risk_failed".equals(failedType)) {
                apiEntryResult = 4;
            } else if ("other_failed".equals(failedType)) {
                apiEntryResult = 3;
            }
        }

        extrData.setApiEntryResult(apiEntryResult);
        bizEventMessageVO.setExtrData(extrData);
        return bizEventMessageVO;
    }
}
