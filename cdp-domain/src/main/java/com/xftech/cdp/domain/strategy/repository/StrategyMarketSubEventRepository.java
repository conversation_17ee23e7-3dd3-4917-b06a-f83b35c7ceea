package com.xftech.cdp.domain.strategy.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketSubEventDo;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @<NAME_EMAIL>
 */
@Component
public class StrategyMarketSubEventRepository {

    /**
     * 根据主键id查询
     *
     * @param id id
     * @return id对应的记录
     */
    public StrategyMarketSubEventDo selectById(Long id) {
        return DBUtil.selectOne("strategyMarketSubEventMapper.selectByPrimaryKey", id);
    }

    /**
     * 插入
     *
     * @param param 对象
     * @return 是否插入成功标识
     */
    public boolean insert(StrategyMarketSubEventDo param) {
        return DBUtil.insert("strategyMarketSubEventMapper.insertSelective", param) > 0;
    }

    /**
     * 根据主键id更新
     *
     * @param param 对象
     * @return 是否更新成功标识
     */
    public boolean updateById(StrategyMarketSubEventDo param) {
        return DBUtil.update("strategyMarketSubEventMapper.updateByPrimaryKeySelective", param) > 0;
    }

    /**
     * 根据事件ID查询
     *
     * @param eventId 事件ID
     * @return 子事件集合
     */
    public List<StrategyMarketSubEventDo> getByEventId(Long eventId) {
        return DBUtil.selectList("strategyMarketSubEventMapper.getByEventId", eventId);
    }

    public List<StrategyMarketSubEventDo> getByStrategyId(Long eventId) {
        return DBUtil.selectList("strategyMarketSubEventMapper.getByStrategyId", eventId);
    }



    /**
     * 插入
     *
     * @param strategyMarketSubEventList 对象
     * @return 是否插入成功标识
     */
    public boolean insertBatch(List<StrategyMarketSubEventDo> strategyMarketSubEventList ) {
        return DBUtil.insertBatchWithoutTx("strategyMarketSubEventMapper.insertSelective", strategyMarketSubEventList) > 0;
    }


    /**
     * 插入
     *
     * @param delSubEventIdList 对象
     */
    public void deleteBatch(List<Long> delSubEventIdList ) {
        DBUtil.deleteBatchWithoutTx("strategyMarketSubEventMapper.deleteByPrimaryKey", delSubEventIdList);
    }

    public List<StrategyMarketSubEventDo> getByEventName(String eventName) {
        return DBUtil.selectList("strategyMarketSubEventMapper.getByEventName", eventName);
    }
}
