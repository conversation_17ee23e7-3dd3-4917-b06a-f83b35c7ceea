package com.xinfei.touch.domain.model;

import lombok.Getter;

/**
 * 频控范围枚举
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
public enum FlowControlScope {
    
    /**
     * 用户级
     */
    USER(1, "用户级", "针对单个用户的频控"),
    
    /**
     * 策略级
     */
    STRATEGY(2, "策略级", "针对特定策略的频控"),
    
    /**
     * 渠道级
     */
    CHANNEL(3, "渠道级", "针对特定渠道的频控"),
    
    /**
     * 全局级
     */
    GLOBAL(4, "全局级", "全局范围的频控");
    
    /**
     * 范围编码
     */
    private final Integer code;
    
    /**
     * 范围名称
     */
    private final String name;
    
    /**
     * 范围描述
     */
    private final String description;
    
    FlowControlScope(Integer code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }
    
    /**
     * 根据编码获取范围
     */
    public static FlowControlScope getByCode(Integer code) {
        for (FlowControlScope scope : values()) {
            if (scope.getCode().equals(code)) {
                return scope;
            }
        }
        throw new IllegalArgumentException("未知的频控范围编码: " + code);
    }
}
