/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.resp.label;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ LabelClassificationResp, v 0.1 2024/6/20 14:58 lingang.han Exp $
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LabelClassificationResp implements Serializable {
    private static final long serialVersionUID = 2832520539111878579L;

    @ApiModelProperty(value = "标签一级分类id")
    private Integer labelId;

    @Getter
    @ApiModelProperty(value = "标签一级分类名称")
    private String labelName;

    @ApiModelProperty(value = "标签二级分类列表")
    private List<LabelClassificationResp> items;

    public LabelClassificationResp(Integer labelId, String labelName) {
        this.labelId = labelId;
        this.labelName = labelName;
    }
}