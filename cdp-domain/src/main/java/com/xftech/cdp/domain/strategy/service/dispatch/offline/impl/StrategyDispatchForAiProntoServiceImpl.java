/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.service.dispatch.offline.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.TypeReference;
import com.xftech.cdp.domain.dispatch.BatchDispatchService;
import com.xftech.cdp.domain.strategy.model.dispatch.StrategyContext;
import com.xftech.cdp.domain.strategy.service.dispatch.offline.StrategyDispatchForAiProntoService;
import com.xftech.cdp.domain.strategy.service.dispatch.offline.StrategyDispatchService;
import com.xftech.cdp.infra.config.StrategyConfig;
import com.xftech.cdp.infra.constant.StrategyDispatchConstants;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPushBatchDo;
import com.xftech.cdp.infra.rocketmq.dto.AiUserData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ StrategyDispatchForAiProtonServiceImpl, v 0.1 2024/8/20 17:11 lingang.han Exp $
 */
@Slf4j
@RefreshScope
@Service(StrategyDispatchConstants.AI_PRONTO_SERVICE)
public class StrategyDispatchForAiProntoServiceImpl extends AbstractStrategyDispatchService implements StrategyDispatchService, StrategyDispatchForAiProntoService {

    @Autowired
    private StrategyConfig strategyConfig;
    @Autowired
    private BatchDispatchService batchDispatchService;

    @Override
    protected Integer setBatchSize() {
        return strategyConfig.getAiProntoBatchSize();
    }

    @Override
    protected <T> ImmutablePair<Integer, CrowdPushBatchDo> dispatchHandler(StrategyContext strategyContext, String app, String innerApp, List<CrowdDetailDo> batch, List<T> param) {
        innerApp = StringUtils.isBlank(innerApp) ? app : innerApp;
        return sendAiPronto(strategyContext, app, innerApp, batch, Convert.convert(new TypeReference<List<AiUserData>>() {
        }, param));
    }

    @Override
    protected <T> ImmutablePair<Integer, CrowdPushBatchDo> retryDispatchHandler(StrategyContext context, String tableNameNo, String app, String innerApp, List<CrowdDetailDo> detailList, List<T> param) {
        innerApp = StringUtils.isBlank(innerApp) ? app : innerApp;
        return sendAiPronto(context, app, innerApp, detailList, Convert.convert(new TypeReference<List<AiUserData>>() {
        }, param));
    }

    private ImmutablePair<Integer, CrowdPushBatchDo> sendAiPronto(StrategyContext strategyContext, String app, String innerApp, List<CrowdDetailDo> batch, List<AiUserData> param) {
        if (CollectionUtils.isEmpty(param)) {
            return batchDispatchService.sendAiProto(convertToDispatchDto(strategyContext), app, innerApp, batch);
        }
        return batchDispatchService.sendAiProtoWithParam(convertToDispatchDto(strategyContext), app, innerApp, batch, param);
    }
}