/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.domain.service;

import cn.hutool.core.util.IdUtil;
import com.xftech.cdp.BaseTest;
import com.xftech.cdp.domain.strategy.service.DispatchTaskService;
import com.xftech.cdp.infra.repository.cdp.strategy.po.DispatchTaskDo;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

/**
 *
 * <AUTHOR>
 * @version $ DispatchTaskServiceTest, v 0.1 2023/11/5 22:51 yye.xu Exp $
 */

public class DispatchTaskServiceTest extends BaseTest {
    @Autowired
    private DispatchTaskService dispatchTaskService;

    @Test
    public void add(){
        DispatchTaskDo dispatchTaskDo = new DispatchTaskDo();
        dispatchTaskDo.setBizId("0");
        dispatchTaskDo.setAssociationId("1");
        dispatchTaskDo.setSerialNo(IdUtil.fastSimpleUUID());
        dispatchTaskDo.setDateValue(20231105);
        dispatchTaskDo.setDispatchTime(new Date());
        dispatchTaskService.add(dispatchTaskDo);
    }

    @Test
    public void update() {
        int c = dispatchTaskService.updateTaskStatus(1, 0, 1);
        System.out.println(c);
        c = dispatchTaskService.updateTaskStatus(1, 0, 1);
        System.out.println(c);
        c = dispatchTaskService.updateTaskStatus(1, 1, 0);
        System.out.println(c);
    }
    @Test
    public void testSelect(){
        DispatchTaskDo dispatchTaskDo = new DispatchTaskDo();
        dispatchTaskDo.setId(1L);
        dispatchTaskDo.setRetryTimes(0);
        dispatchTaskService.updateDispatchTaskFailedRetry(dispatchTaskDo, "ERROR");
    }

}