package com.xftech.cdp.domain.crowd.model.dispatch;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2023/2/24
 */
@Data
public class CrowdWereHouse {

    private Long crowdId;

    private Long appUserId;

    private String app;

    private String innerApp;
    private String abNum;

    private Integer appUserIdLast2;


    private String mobile;
    /**
     * 注册时间
     */
    private LocalDateTime registerTime;
    /**
     * 首次注册渠道
     */
    private String mobileUtmSource;
    /**
     * 历史进件渠道
     */
    private String historyBorrowUtmSource;
    /**
     * 最近一次放款成功渠道
     */
    private String lastLoanSuccessUtmSource;

    private Long cno;

    public String getHistoryBorrowUtmSource() {
        if (StringUtils.isBlank(this.historyBorrowUtmSource)) {
            return this.historyBorrowUtmSource;
        }
        return this.historyBorrowUtmSource.replace("[", "")
                .replace("\"", "")
                .replace("]", "");
    }


}
