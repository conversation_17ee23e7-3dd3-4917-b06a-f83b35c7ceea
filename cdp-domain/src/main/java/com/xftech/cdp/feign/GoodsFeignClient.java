package com.xftech.cdp.feign;

import com.xftech.cdp.feign.contants.FeignConstants;
import com.xftech.cdp.feign.model.GoodsList;
import com.xftech.cdp.feign.model.PushTemplateList;
import com.xftech.cdp.feign.model.requset.GoodsBaseRequest;
import com.xftech.cdp.feign.model.requset.GoodsListRequest;
import com.xftech.cdp.feign.model.requset.PushBaseRequest;
import com.xftech.cdp.feign.model.requset.TemplateListRequest;
import com.xftech.cdp.feign.model.response.GoodsResponse;
import com.xftech.cdp.feign.model.response.PushResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 *
 * <AUTHOR>
 */
@FeignClient(name = FeignConstants.GOODS_SERVICE, contextId = FeignConstants.GOODS_SERVICE + ".FeignClient", path = "/")
public interface GoodsFeignClient {
    @PostMapping("/goods/queryGoodsList")
    GoodsResponse<GoodsList> queryGoodsList(@RequestBody GoodsListRequest request, @RequestHeader(name = "ua") String ua);
}
