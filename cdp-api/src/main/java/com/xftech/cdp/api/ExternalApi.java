package com.xftech.cdp.api;

import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.base.Response;
import com.xftech.cdp.api.dto.req.AppBannerReq;
import com.xftech.cdp.api.dto.req.DecideReq;
import com.xftech.cdp.api.dto.req.external.*;
import com.xftech.cdp.api.dto.resp.AppBannerResp;
import com.xftech.cdp.api.dto.resp.DecideResp;
import com.xftech.cdp.api.dto.resp.external.DispatchRecordQueryResp;
import com.xftech.cdp.api.dto.resp.external.ExistDispatchRecordResp;
import com.xftech.cdp.api.dto.resp.external.StrategyListResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 提供给其他系统的接口
 *
 * @<NAME_EMAIL>
 * @date 2023/4/4 9:48
 */
@Api(tags = "外部接口")
public interface ExternalApi {

    @ApiOperation("电销用户导入结果通知接口")
    @PostMapping("/tele/tele-import-result")
    Response<Boolean> teleImportResult(@Validated @RequestBody ExternalBaseRequest<TeleImportResultReq> request);

    @ApiOperation("查询用户是否存在下发记录接口")
    @PostMapping("/api/strategy/user/hasDispatchRecord")
    Response<DispatchRecordQueryResp> hasDispatchRecord(@Validated @RequestBody ExternalBaseRequest<DispatchRecordQueryReq> request);

    @ApiOperation("策略列表接口")
    @PostMapping("/api/strategy/getStrategyList")
    Response<PageResultResponse<StrategyListResp>> getStrategyList(@Validated @RequestBody ExternalBaseRequest<StrategyListReq> request);

    @ApiOperation("提额结果通知")
    @PostMapping("/api/strategy/increaseAmt-notify")
    Response increaseAmtNotify(@RequestBody IncreaseAmtCallbakRequest request);

    @ApiOperation("查询用户24小时内是否存在下发记录接口")
    @PostMapping("/api/strategy/user/existDispatchRecord")
    Response<ExistDispatchRecordResp> existDispatchRecord(@Validated @RequestBody ExternalBaseRequest<ExistDispatchRecordReq> request);

    @ApiOperation("为用户展示app弹窗")
    @PostMapping("/api/strategy/app-banner-recommend")
    Response<List<AppBannerResp>> recommendAppBannerList(@RequestBody ExternalBaseRequest<AppBannerReq> appBannerReq);

    @ApiOperation("策略决策")
    @PostMapping("/api/strategy/decide")
    Response<List<DecideResp>> decide(@RequestBody ExternalBaseRequest<DecideReq> decideReq);

}
