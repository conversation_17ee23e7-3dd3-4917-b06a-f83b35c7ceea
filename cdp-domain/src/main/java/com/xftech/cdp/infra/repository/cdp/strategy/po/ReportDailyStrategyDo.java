package com.xftech.cdp.infra.repository.cdp.strategy.po;

import com.xftech.cdp.domain.crowd.model.enums.CrowdExecResultEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyExecStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReportDailyStrategyDo implements Serializable {

    private static final long serialVersionUID = 720072754732233821L;
    /**
     *
     * pk
     */
    private Long id;

    /**
     *
     * 统计日期
     */
    private Date date;

    /**
     *
     * 策略id
     */
    private Long strategyId;

    /**
     *
     * 策略名称
     */
    private String strategyName;

    /**
     *
     * 策略分组Id
     */
    private Long strategyGroupId;

    /**
     *
     * 策略分组名称
     */
    private String strategyGroupName;

    /**
     *
     * 策略渠道id
     */
    private Long strategyMarketChannelId;

    /**
     *
     * 策略渠道类型
     */
    private Integer strategyMarketChannel;

    /**
     *
     * 执行开始时间
     */
    private LocalDateTime execStartTime;

    /**
     *
     * 执行结束时间
     */
    private LocalDateTime execEndTime;

    /**
     *
     * 执行状态
     */
    private Integer execStatus;

    /**
     *
     * 失败原因
     */
    private String failReason;

    /**
     *
     * 下发总数
     */
    private Integer sendCount;

    /**
     *
     * 回执成功数
     */
    private Integer succCount;

    /**
     *
     * 创建时间
     */
    private Date createdTime;

    /**
     *
     * 修改时间
     */
    private Date updatedTime;

    /**
     *
     * 逻辑删除标识
     */
    private Integer dFlag;

    /**
     *
     * 创建人
     */
    private String createdOp;

    /**
     *
     * 修改人
     */
    private String updatedOp;


    public int getStatusSort() {
        return StrategyExecStatusEnum.getEnumSort(this.getExecStatus());
    }
}