/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.stat.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.base.database.Page;
import com.xftech.cdp.domain.stat.entity.StatStrategyFlowDataEntity;
import com.xftech.cdp.domain.stat.entity.StatStrategyGroupDataEntity;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @version $ StatStrategyFlowDataRepository, v 0.1 2024/3/28 11:10 lingang.han Exp $
 */

@Component
public class StatStrategyFlowDataRepository {
    public StatStrategyGroupDataEntity selectById(Long id) {
        return DBUtil.selectOne("statStrategyFlowDataMapper.selectByPrimaryKey", id);
    }

    public boolean insert(StatStrategyFlowDataEntity entity) {
        return DBUtil.insert("statStrategyFlowDataMapper.insertSelective", entity) > 0;
    }

    public boolean updateById(StatStrategyFlowDataEntity entity) {
        return DBUtil.update("statStrategyFlowDataMapper.updateByPrimaryKeySelective", entity) > 0;
    }

    public Boolean exitsRecord(StatStrategyFlowDataEntity statStrategyFlowData) {
        Integer num = DBUtil.selectOne("statStrategyFlowDataMapper.exitsRecord", statStrategyFlowData);
        return num > 0;
    }

    public Page<StatStrategyFlowDataEntity> selectPage(String flowNo, Integer pageNum, Integer pageSize) {
        Map<String, Object> param = new HashMap<>();
        param.put("flowNo", flowNo);
        return DBUtil.selectPage("statStrategyFlowDataMapper.selectPageByFlowNo", param, pageNum, pageSize);
    }

    public List<StatStrategyFlowDataEntity> selectByBatchNo(String batchNo) {
        Map<String, Object> param = new HashMap<>();
        param.put("batchNo", batchNo);
        return DBUtil.selectList("statStrategyFlowDataMapper.selectByBatchNo", param);
    }

    public Boolean updateByRecord(StatStrategyFlowDataEntity statStrategyFlowDataEntity) {
        return DBUtil.update("statStrategyFlowDataMapper.updateByRecord", statStrategyFlowDataEntity) > 0;
    }
}