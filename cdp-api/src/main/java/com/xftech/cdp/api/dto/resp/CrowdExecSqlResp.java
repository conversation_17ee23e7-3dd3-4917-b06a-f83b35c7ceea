package com.xftech.cdp.api.dto.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xftech.cdp.api.dto.base.TimeFormat;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2023-06-09
 */
@Getter
@Setter
public class CrowdExecSqlResp extends BaseResp {
    /**
     * 刷新时间，yyyy-MM-dd HH:mm:ss
     */
    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    public LocalDateTime refreshTime;
    /**
     * 圈选sql
     */
    public String includeSql;
    /**
     * 排除sql
     */
    public String excludeSql;
}
