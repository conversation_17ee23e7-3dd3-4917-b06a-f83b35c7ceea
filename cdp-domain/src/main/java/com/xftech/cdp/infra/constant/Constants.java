package com.xftech.cdp.infra.constant;

/**
 * <AUTHOR> wh
 * @since : 2022/10/14 14:25
 */
public interface Constants {

    int D_FLAG_FALSE = 0;

    int D_FLAG_TRUE = 1;

    String SUCCESS = "success";

//    String TID = "traceId";
//
//    String JOB_ID = "jobId";


    String APP_NAME = "xyf-cdp";

    String SSO_APP_NAME = "cdp";

    // 成功状态
    int SUCCESS_STATUS = 1;

    // 失败状态
    int FAIL_STATUS = 0;

    // 人群用户表主表名
    String CROWD_DETAIL_STR = "crowd_detail";

    /**
     * 渠道标签决策结果，通过
     */
    int UTM_LABEL_PASS = 0;
    /**
     * 渠道标签决策结果，不通过
     */
    int UTM_LABEL_FAIL = 1;

    String XYF01 = "xyf01";
    String XYF = "xyf";

    /**
     * 短信金额格式转换（四舍五入保留1位小数，如果小数位为0则取值）
     * 举个栗子，提额金额为500元，取值为500；提额金额为500.01元，取值为500；提额金额为500.09元，取值为500.1
     */
    String SMS_CURRENCY_FORMAT = "SMS_CURRENCY_FORMAT";

}
