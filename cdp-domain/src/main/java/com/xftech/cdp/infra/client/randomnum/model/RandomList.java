package com.xftech.cdp.infra.client.randomnum.model;

import lombok.Data;


import java.util.List;

/**
 * @<NAME_EMAIL>
 */

@Data
public class RandomList {

    private Integer total;

    private Integer pages;

    private List<RandomItem> abTests;

    @Data
    public static class RandomItem{
        /**
         * 场景名称
         */
        private String testName;
        /**
         * 场景key值
         */
        private String bizKey;
        /**
         * 位数
         */
        private String numLength;
        /**
         * 备注
         */
        private String remark;
        /**
         * 归属业务线
         */
        private String bizName;
    }


}
