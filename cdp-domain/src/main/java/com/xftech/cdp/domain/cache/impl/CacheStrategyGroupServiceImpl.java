package com.xftech.cdp.domain.cache.impl;

import com.alibaba.fastjson.JSONArray;
import com.xftech.cdp.domain.strategy.repository.StrategyGroupRepository;
import com.xftech.cdp.domain.cache.CacheStrategyGroupService;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyGroupDo;
import com.xftech.cdp.infra.utils.RedisKeyUtils;
import com.xftech.cdp.infra.utils.RedisUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 策略分组信息表操作
 */
@Service
public class CacheStrategyGroupServiceImpl implements CacheStrategyGroupService {
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private StrategyGroupRepository strategyGroupRepository;

    /**
     * 根据策略id删除分组信息记录
     *
     * @param strategyId 策略id
     */
    public void deleteByStrategyId(Long strategyId) {
        strategyGroupRepository.deleteByStrategyId(strategyId);
        this.updateStrategyGroupCache(strategyId);
    }

    /**
     * 根据策略id查询分组信息记录
     *
     * @param strategyId 策略id
     * @return 该策略下的分组信息列表
     */
    public List<StrategyGroupDo> selectListByStrategyId(Long strategyId) {
        String redisKey = RedisKeyUtils.genStrategyGroupListKey(strategyId);
        String data = redisUtils.get(redisKey);
        List<StrategyGroupDo> strategyGroupDoList = JSONArray.parseArray(data, StrategyGroupDo.class);
        if (CollectionUtils.isEmpty(strategyGroupDoList)) {
            strategyGroupDoList = strategyGroupRepository.selectListByStrategyId(strategyId);
            redisUtils.set(redisKey, strategyGroupDoList, RedisUtils.DEFAULT_EXPIRE_SECONDS);
        }
        return strategyGroupDoList;
    }

    /**
     * 根据分组id（主键）查询某条记录
     *
     * @param strategyGroupId 分组id
     * @return 该id对应的一条记录
     */
    public StrategyGroupDo selectById(Long strategyGroupId) {
        String redisKey = RedisKeyUtils.genStrategyGroupOneKey(strategyGroupId);
        StrategyGroupDo strategyGroupDo = redisUtils.get(redisKey, StrategyGroupDo.class);
        if (strategyGroupDo == null) {
            strategyGroupDo = strategyGroupRepository.selectById(strategyGroupId);
            redisUtils.set(redisKey, strategyGroupDo, RedisUtils.DEFAULT_EXPIRE_SECONDS);
        }
        return strategyGroupDo;

    }

    /**
     * 插入一条分组信息记录
     *
     * @param strategyGroupDo 分组信息记录
     */
    public void insert(StrategyGroupDo strategyGroupDo) {
        strategyGroupRepository.insert(strategyGroupDo);

        String redisKey = RedisKeyUtils.genStrategyGroupOneKey(strategyGroupDo.getId());
        redisUtils.set(redisKey, strategyGroupDo, RedisUtils.DEFAULT_EXPIRE_SECONDS);

        this.updateStrategyGroupCache(strategyGroupDo.getStrategyId());
    }

    /**
     * 根据主键id更新分组信息记录
     *
     * @param strategyGroupDo 分组信息记录
     */
    public void update(StrategyGroupDo strategyGroupDo) {
        strategyGroupRepository.update(strategyGroupDo);

        String redisKey = RedisKeyUtils.genStrategyGroupOneKey(strategyGroupDo.getId());
        redisUtils.set(redisKey, strategyGroupDo, RedisUtils.DEFAULT_EXPIRE_SECONDS);

        this.updateStrategyGroupCache(strategyGroupDo.getStrategyId());
    }

    /**
     * 根据主键id更新分组信息记录
     *
     * @param ids id列表
     */
    public void deleteBatch(List<Long> ids) {
        StrategyGroupDo strategyGroupDo = strategyGroupRepository.selectById(ids.get(0));
        strategyGroupRepository.deleteBatch(ids);
        this.updateStrategyGroupCache(strategyGroupDo.getStrategyId());
    }

    public List<StrategyGroupDo> updateStrategyGroupCache(Long strategyId) {
        String redisKey = RedisKeyUtils.genStrategyGroupListKey(strategyId);
        List<StrategyGroupDo> strategyGroupDoList = strategyGroupRepository.selectListByStrategyId(strategyId);
        redisUtils.set(redisKey, strategyGroupDoList, RedisUtils.DEFAULT_EXPIRE_SECONDS);
        return strategyGroupDoList;
    }



}
