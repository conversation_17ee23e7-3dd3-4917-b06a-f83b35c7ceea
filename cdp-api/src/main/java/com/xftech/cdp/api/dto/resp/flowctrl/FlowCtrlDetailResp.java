package com.xftech.cdp.api.dto.resp.flowctrl;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/2/20
 */

@Data
public class FlowCtrlDetailResp {

    @ApiModelProperty("规则编号")
    public Long id;

    @ApiModelProperty(value = "规则名称")
    private String name;

    @ApiModelProperty("规则描述")
    public String description;

    @ApiModelProperty(value = "规则类型 1-策略 2-渠道")
    private Integer type;

    @ApiModelProperty("日触达次数")
    private Integer dayCount;

    @ApiModelProperty("周触达次数")
    private Integer weekCount;

    @ApiModelProperty("月触达次数")
    private Integer monthCount;

    @ApiModelProperty("状态")
    private Integer status;

    /**
     * 触达限制天数
     */
    @ApiModelProperty("触达限制天数")
    private Integer limitDays;

    /**
     * 触达限制次数
     */
    @ApiModelProperty("触达限制次数")
    private Integer limitTimes;

    @ApiModelProperty("业务线")
    private String bizType;

    @ApiModelProperty("生效渠道")
    private String effectiveChannel;

}
