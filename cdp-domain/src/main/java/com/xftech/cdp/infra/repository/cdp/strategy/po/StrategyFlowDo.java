package com.xftech.cdp.infra.repository.cdp.strategy.po;

import java.io.Serializable;
import java.util.Date;

public class StrategyFlowDo implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_flow.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_flow.flow_no
     *
     * @mbg.generated
     */
    private String flowNo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_flow.name
     *
     * @mbg.generated
     */
    private String name;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_flow.business_type
     *
     * @mbg.generated
     */
    private String businessType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_flow.validity_begin
     *
     * @mbg.generated
     */
    private Date validityBegin;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_flow.validity_end
     *
     * @mbg.generated
     */
    private Date validityEnd;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_flow.flow_type
     *
     * @mbg.generated
     */
    private Short flowType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_flow.send_ruler
     *
     * @mbg.generated
     */
    private Short sendRuler;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_flow.status
     *
     * @mbg.generated
     */
    private Short status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_flow.market_channels
     *
     * @mbg.generated
     */
    private String marketChannels;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_flow.created_op
     *
     * @mbg.generated
     */
    private String createdOp;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_flow.created_op_mobile
     *
     * @mbg.generated
     */
    private String createdOpMobile;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_flow.updated_op
     *
     * @mbg.generated
     */
    private String updatedOp;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_flow.updated_op_mobile
     *
     * @mbg.generated
     */
    private String updatedOpMobile;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_flow.d_flag
     *
     * @mbg.generated
     */
    private Short dFlag;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_flow.created_time
     *
     * @mbg.generated
     */
    private Date createdTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_flow.updated_time
     *
     * @mbg.generated
     */
    private Date updatedTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table strategy_flow
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_flow.id
     *
     * @return the value of strategy_flow.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_flow.id
     *
     * @param id the value for strategy_flow.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_flow.flow_no
     *
     * @return the value of strategy_flow.flow_no
     *
     * @mbg.generated
     */
    public String getFlowNo() {
        return flowNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_flow.flow_no
     *
     * @param flowNo the value for strategy_flow.flow_no
     *
     * @mbg.generated
     */
    public void setFlowNo(String flowNo) {
        this.flowNo = flowNo == null ? null : flowNo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_flow.name
     *
     * @return the value of strategy_flow.name
     *
     * @mbg.generated
     */
    public String getName() {
        return name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_flow.name
     *
     * @param name the value for strategy_flow.name
     *
     * @mbg.generated
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_flow.business_type
     *
     * @return the value of strategy_flow.business_type
     *
     * @mbg.generated
     */
    public String getBusinessType() {
        return businessType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_flow.business_type
     *
     * @param businessType the value for strategy_flow.business_type
     *
     * @mbg.generated
     */
    public void setBusinessType(String businessType) {
        this.businessType = businessType == null ? null : businessType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_flow.validity_begin
     *
     * @return the value of strategy_flow.validity_begin
     *
     * @mbg.generated
     */
    public Date getValidityBegin() {
        return validityBegin;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_flow.validity_begin
     *
     * @param validityBegin the value for strategy_flow.validity_begin
     *
     * @mbg.generated
     */
    public void setValidityBegin(Date validityBegin) {
        this.validityBegin = validityBegin;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_flow.validity_end
     *
     * @return the value of strategy_flow.validity_end
     *
     * @mbg.generated
     */
    public Date getValidityEnd() {
        return validityEnd;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_flow.validity_end
     *
     * @param validityEnd the value for strategy_flow.validity_end
     *
     * @mbg.generated
     */
    public void setValidityEnd(Date validityEnd) {
        this.validityEnd = validityEnd;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_flow.flow_type
     *
     * @return the value of strategy_flow.flow_type
     *
     * @mbg.generated
     */
    public Short getFlowType() {
        return flowType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_flow.flow_type
     *
     * @param flowType the value for strategy_flow.flow_type
     *
     * @mbg.generated
     */
    public void setFlowType(Short flowType) {
        this.flowType = flowType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_flow.send_ruler
     *
     * @return the value of strategy_flow.send_ruler
     *
     * @mbg.generated
     */
    public Short getSendRuler() {
        return sendRuler;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_flow.send_ruler
     *
     * @param sendRuler the value for strategy_flow.send_ruler
     *
     * @mbg.generated
     */
    public void setSendRuler(Short sendRuler) {
        this.sendRuler = sendRuler;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_flow.status
     *
     * @return the value of strategy_flow.status
     *
     * @mbg.generated
     */
    public Short getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_flow.status
     *
     * @param status the value for strategy_flow.status
     *
     * @mbg.generated
     */
    public void setStatus(Short status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_flow.market_channels
     *
     * @return the value of strategy_flow.market_channels
     *
     * @mbg.generated
     */
    public String getMarketChannels() {
        return marketChannels;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_flow.market_channels
     *
     * @param marketChannels the value for strategy_flow.market_channels
     *
     * @mbg.generated
     */
    public void setMarketChannels(String marketChannels) {
        this.marketChannels = marketChannels == null ? null : marketChannels.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_flow.created_op
     *
     * @return the value of strategy_flow.created_op
     *
     * @mbg.generated
     */
    public String getCreatedOp() {
        return createdOp;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_flow.created_op
     *
     * @param createdOp the value for strategy_flow.created_op
     *
     * @mbg.generated
     */
    public void setCreatedOp(String createdOp) {
        this.createdOp = createdOp == null ? null : createdOp.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_flow.created_op_mobile
     *
     * @return the value of strategy_flow.created_op_mobile
     *
     * @mbg.generated
     */
    public String getCreatedOpMobile() {
        return createdOpMobile;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_flow.created_op_mobile
     *
     * @param createdOpMobile the value for strategy_flow.created_op_mobile
     *
     * @mbg.generated
     */
    public void setCreatedOpMobile(String createdOpMobile) {
        this.createdOpMobile = createdOpMobile == null ? null : createdOpMobile.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_flow.updated_op
     *
     * @return the value of strategy_flow.updated_op
     *
     * @mbg.generated
     */
    public String getUpdatedOp() {
        return updatedOp;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_flow.updated_op
     *
     * @param updatedOp the value for strategy_flow.updated_op
     *
     * @mbg.generated
     */
    public void setUpdatedOp(String updatedOp) {
        this.updatedOp = updatedOp == null ? null : updatedOp.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_flow.updated_op_mobile
     *
     * @return the value of strategy_flow.updated_op_mobile
     *
     * @mbg.generated
     */
    public String getUpdatedOpMobile() {
        return updatedOpMobile;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_flow.updated_op_mobile
     *
     * @param updatedOpMobile the value for strategy_flow.updated_op_mobile
     *
     * @mbg.generated
     */
    public void setUpdatedOpMobile(String updatedOpMobile) {
        this.updatedOpMobile = updatedOpMobile == null ? null : updatedOpMobile.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_flow.d_flag
     *
     * @return the value of strategy_flow.d_flag
     *
     * @mbg.generated
     */
    public Short getdFlag() {
        return dFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_flow.d_flag
     *
     * @param dFlag the value for strategy_flow.d_flag
     *
     * @mbg.generated
     */
    public void setdFlag(Short dFlag) {
        this.dFlag = dFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_flow.created_time
     *
     * @return the value of strategy_flow.created_time
     *
     * @mbg.generated
     */
    public Date getCreatedTime() {
        return createdTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_flow.created_time
     *
     * @param createdTime the value for strategy_flow.created_time
     *
     * @mbg.generated
     */
    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_flow.updated_time
     *
     * @return the value of strategy_flow.updated_time
     *
     * @mbg.generated
     */
    public Date getUpdatedTime() {
        return updatedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_flow.updated_time
     *
     * @param updatedTime the value for strategy_flow.updated_time
     *
     * @mbg.generated
     */
    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table strategy_flow
     *
     * @mbg.generated
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        StrategyFlowDo other = (StrategyFlowDo) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getFlowNo() == null ? other.getFlowNo() == null : this.getFlowNo().equals(other.getFlowNo()))
            && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
            && (this.getBusinessType() == null ? other.getBusinessType() == null : this.getBusinessType().equals(other.getBusinessType()))
            && (this.getValidityBegin() == null ? other.getValidityBegin() == null : this.getValidityBegin().equals(other.getValidityBegin()))
            && (this.getValidityEnd() == null ? other.getValidityEnd() == null : this.getValidityEnd().equals(other.getValidityEnd()))
            && (this.getFlowType() == null ? other.getFlowType() == null : this.getFlowType().equals(other.getFlowType()))
            && (this.getSendRuler() == null ? other.getSendRuler() == null : this.getSendRuler().equals(other.getSendRuler()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getMarketChannels() == null ? other.getMarketChannels() == null : this.getMarketChannels().equals(other.getMarketChannels()))
            && (this.getCreatedOp() == null ? other.getCreatedOp() == null : this.getCreatedOp().equals(other.getCreatedOp()))
            && (this.getCreatedOpMobile() == null ? other.getCreatedOpMobile() == null : this.getCreatedOpMobile().equals(other.getCreatedOpMobile()))
            && (this.getUpdatedOp() == null ? other.getUpdatedOp() == null : this.getUpdatedOp().equals(other.getUpdatedOp()))
            && (this.getUpdatedOpMobile() == null ? other.getUpdatedOpMobile() == null : this.getUpdatedOpMobile().equals(other.getUpdatedOpMobile()))
            && (this.getdFlag() == null ? other.getdFlag() == null : this.getdFlag().equals(other.getdFlag()))
            && (this.getCreatedTime() == null ? other.getCreatedTime() == null : this.getCreatedTime().equals(other.getCreatedTime()))
            && (this.getUpdatedTime() == null ? other.getUpdatedTime() == null : this.getUpdatedTime().equals(other.getUpdatedTime()));
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table strategy_flow
     *
     * @mbg.generated
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getFlowNo() == null) ? 0 : getFlowNo().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getBusinessType() == null) ? 0 : getBusinessType().hashCode());
        result = prime * result + ((getValidityBegin() == null) ? 0 : getValidityBegin().hashCode());
        result = prime * result + ((getValidityEnd() == null) ? 0 : getValidityEnd().hashCode());
        result = prime * result + ((getFlowType() == null) ? 0 : getFlowType().hashCode());
        result = prime * result + ((getSendRuler() == null) ? 0 : getSendRuler().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getMarketChannels() == null) ? 0 : getMarketChannels().hashCode());
        result = prime * result + ((getCreatedOp() == null) ? 0 : getCreatedOp().hashCode());
        result = prime * result + ((getCreatedOpMobile() == null) ? 0 : getCreatedOpMobile().hashCode());
        result = prime * result + ((getUpdatedOp() == null) ? 0 : getUpdatedOp().hashCode());
        result = prime * result + ((getUpdatedOpMobile() == null) ? 0 : getUpdatedOpMobile().hashCode());
        result = prime * result + ((getdFlag() == null) ? 0 : getdFlag().hashCode());
        result = prime * result + ((getCreatedTime() == null) ? 0 : getCreatedTime().hashCode());
        result = prime * result + ((getUpdatedTime() == null) ? 0 : getUpdatedTime().hashCode());
        return result;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table strategy_flow
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", flowNo=").append(flowNo);
        sb.append(", name=").append(name);
        sb.append(", businessType=").append(businessType);
        sb.append(", validityBegin=").append(validityBegin);
        sb.append(", validityEnd=").append(validityEnd);
        sb.append(", flowType=").append(flowType);
        sb.append(", sendRuler=").append(sendRuler);
        sb.append(", status=").append(status);
        sb.append(", marketChannels=").append(marketChannels);
        sb.append(", createdOp=").append(createdOp);
        sb.append(", createdOpMobile=").append(createdOpMobile);
        sb.append(", updatedOp=").append(updatedOp);
        sb.append(", updatedOpMobile=").append(updatedOpMobile);
        sb.append(", dFlag=").append(dFlag);
        sb.append(", createdTime=").append(createdTime);
        sb.append(", updatedTime=").append(updatedTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}