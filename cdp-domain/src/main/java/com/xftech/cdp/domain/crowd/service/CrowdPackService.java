package com.xftech.cdp.domain.crowd.service;

import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.req.*;
import com.xftech.cdp.api.dto.resp.CrowdExecSqlResp;
import com.xftech.cdp.api.dto.resp.CrowdListResp;
import com.xftech.cdp.api.dto.resp.CrowdOneResp;
import com.xftech.cdp.api.dto.resp.CrowdParseResp;
import com.xftech.cdp.api.dto.resp.CrowdUploadResp;
import com.xftech.cdp.domain.crowd.model.dispatch.CrowdContext;
import com.xftech.cdp.api.dto.resp.*;
import com.xftech.cdp.domain.crowd.model.enums.CrowdStatusEnum;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdExecLogDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPackDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.ReportDailyCrowdDo;
import org.apache.commons.lang3.tuple.ImmutableTriple;
import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.commons.lang3.tuple.MutableTriple;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.lang.NonNull;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.*;


/**
 * <AUTHOR>
 * @since 2023/2/13
 */
public interface CrowdPackService {

    boolean createByCondition(CrowdCreateReq crowdCreateReq);

    boolean updateByCondition(CrowdUpdateReq crowdUpdateReq);

    PageResultResponse<CrowdListResp> queryList(CrowdListReq crowdListReq);

    CrowdOneResp getOne(Long crowdId);

    boolean createCrowdByExcel(CrowdUploadReq crowdUploadReq);

    boolean duplicate(Long crowdId);

    void crowdReset();

    void crowdWareHouseAlarm();

    boolean operate(CrowdOperateReq crowdOperateReq);

    String batchDelete(CrowdBatchDeleteReq crowdBatchDeleteReq);

    List<Long> selectEffectiveCrowd(@NonNull List<Long> crowdIds);

    List<Map<String, Long>> selectCrowdMaxExecLogIds(@NonNull List<Long> crowdIds);

    List<Triple<Long, Long, List<String>>> getExecLogIdAndTablePairList(List<Long> crowdIds,Map<Long, CrowdContext> crowdContent);

    Integer countCrowdPackUserNum(String crowdPackNos);

    CrowdUploadResp uploadExcel(MultipartFile file);
    CrowdParseResp parseExcel(CrowdDownLoadReq crowdDownLoadReq);

    void downLoadFile(Long crowdId, HttpServletResponse response);

    void clearHistoryCrowdDetail();

    void clearHistoryCrowdDetailByTruncate();

    CrowdExecSqlResp getExecSql(Long crowdId);

    List<CrowdPackDo> selectTodayByStatus(CrowdStatusEnum... statusEnum);

    String operateSql();

    ImmutableTriple<List<CrowdDetailDo>, Integer,Long> getCrowdDetailList(Long crowdId,String tableName, Long crowdMaxExecLogId, Long crowdDetailId, Long pageSize, CrowdContext crowdContent);

    Optional<CrowdDetailDo> hasUserRecord(Long crowdId,Long userId, Long crowdExecLogId, String tableName);

    CrowdDetailDo hasUserRecordByIn(Set<Long> crowdIds, Long userId, Long crowdExecLogId, String tableName);

    void reportDailyCrowd();

    CrowdReportDailyResp queryReportDailyList();

    PageResultResponse<CrowdRefreshInfoResp> crowdRefreshInfo(CrowdRefreshInfoReq crowdRefreshInfo);

    boolean verifyCrowdPackByNewRandom(Long userId, Collection<CrowdPackDo> crowdPackCol);

    Set<Long> resetBigDataErrorResultCrowdPacks();
}
