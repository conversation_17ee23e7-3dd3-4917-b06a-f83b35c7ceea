package com.xftech.cdp.domain.strategy.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketEventConditionDo;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @<NAME_EMAIL>
 */
@Component
public class StrategyMarketEventConditionRepository {

    /**
     * 根据主键id查询
     *
     * @param id id
     * @return id对应的记录
     */
    public StrategyMarketEventConditionDo selectById(Long id) {
        return DBUtil.selectOne("strategyMarketEventConditionMapper.selectByPrimaryKey", id);
    }

    /**
     * 插入
     *
     * @param param 对象
     * @return 是否插入成功标识
     */
    public boolean insert(StrategyMarketEventConditionDo param) {
        return DBUtil.insert("strategyMarketEventConditionMapper.insertSelective", param) > 0;
    }

    /**
     * 根据主键id更新
     *
     * @param param 对象
     * @return 是否更新成功标识
     */
    public boolean updateById(StrategyMarketEventConditionDo param) {
        return DBUtil.update("strategyMarketEventConditionMapper.updateByPrimaryKeySelective", param) > 0;
    }

    /**
     * 根据策略ID查询实时标签配置
     *
     * @param strategyId 策略ID
     * @return 实时标签配置
     */
    public List<StrategyMarketEventConditionDo> getByStrategyId(Long strategyId) {
        return DBUtil.selectList("strategyMarketEventConditionMapper.getByStrategyId", strategyId);
    }

    public List<StrategyMarketEventConditionDo> getByStrategyIdAndOption(Long strategyId) {
        return DBUtil.selectList("strategyMarketEventConditionMapper.getByStrategyIdAndOption", strategyId);
    }

    /**
     * 插入
     *
     * @param strategyMarketEventConditionDoList 对象
     * @return 是否插入成功标识
     */
    public boolean insertBatch(List<StrategyMarketEventConditionDo> strategyMarketEventConditionDoList) {
        return DBUtil.insertBatchWithoutTx("strategyMarketEventConditionMapper.insertSelective", strategyMarketEventConditionDoList) > 0;
    }

    public void deleteBatch(List<Long> delEventConditionIdList) {
        DBUtil.deleteBatchWithoutTx("strategyMarketEventConditionMapper.deleteByPrimaryKey", delEventConditionIdList);
    }

    public void deleteByStrategyIdAndOptional(Long strategyId,Integer optional) {
        Map<String,Object> param = new HashMap<>();
        param.put("strategyId",strategyId);
        param.put("optional",optional);
        DBUtil.update("strategyMarketEventConditionMapper.deleteByStrategyIdAndOptional", param);
    }
}
