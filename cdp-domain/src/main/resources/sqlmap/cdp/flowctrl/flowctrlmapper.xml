<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="flowCtrlMapper">

    <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.flowctrl.po.FlowCtrlDo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="SMALLINT"/>
        <result property="type" column="type" jdbcType="SMALLINT"/>
        <result property="type" column="type" jdbcType="SMALLINT"/>
        <result property="effectiveStrategy" column="effective_strategy" jdbcType="VARCHAR"/>
        <result property="effectiveChannel" column="effective_channel" jdbcType="VARCHAR"/>
        <result property="dayCount" column="day_count" jdbcType="INTEGER"/>
        <result property="weekCount" column="week_count" jdbcType="INTEGER"/>
        <result property="monthCount" column="month_count" jdbcType="INTEGER"/>
        <result property="priority" column="priority" jdbcType="INTEGER"/>
        <result property="strategyType" column="strategy_type" jdbcType="SMALLINT"/>
        <result property="limitDays" column="limit_days" jdbcType="INTEGER"/>
        <result property="limitTimes" column="limit_times" jdbcType="INTEGER"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
        <result property="dFlag" column="d_flag" jdbcType="SMALLINT"/>
        <result property="createdOp" column="created_op" jdbcType="VARCHAR"/>
        <result property="updatedOp" column="updated_op" jdbcType="VARCHAR"/>
        <result property="bizType" column="biz_type" jdbcType="VARCHAR"/>
    </resultMap>


    <sql id="Base_Column_List">
        id
        ,`name`,description,`status`,`type`,strategy_type,
        effective_strategy,effective_channel,day_count,
        week_count,month_count,limit_days,limit_times,created_time,
        updated_time,d_flag,created_op,
        updated_op,biz_type
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from flow_ctrl
        where id = #{id,jdbcType=BIGINT} and d_flag = 0
    </select>

    <select id="getFlowCtrlConfig" resultMap="BaseResultMap">
        select *, 1 as priority
        from flow_ctrl
        where status = 1
          and type = 2
          and d_flag = 0
          and find_in_set(#{marketChannel}, effective_channel)

        union

        select *, 2 as priority
        from flow_ctrl
        where status = 1
          and type = 2
          and d_flag = 0
          and effective_channel = 0

        union

        select *, 3 as priority
        from flow_ctrl
        where status = 1
          and type = 3
          and d_flag = 0
          and find_in_set(#{strategyId}, effective_strategy)

        union

        select *, 4 as priority
        from flow_ctrl
        where status = 1
          and type = 1
          and d_flag = 0
          and find_in_set(#{strategyId}, effective_strategy)

        union

        select *, 5 as priority
        from flow_ctrl
        where status = 1
          and type = 1
          and d_flag = 0
          and effective_strategy = 0
    </select>

    <select id="getFlowCtrlConfigNew" resultMap="BaseResultMap">
        select *, 1 as priority
        from flow_ctrl
        where status = 1
          and type = 2
          and d_flag = 0
          and effective_channel = #{marketChannel}
     union
        select *, 2 as priority
        from flow_ctrl
        where status = 1
          and type = 4
          and effective_channel = #{marketChannel}
          and biz_type = #{bizType} -- 业务线维度
          and d_flag = 0
     union
        select *, 3 as priority
        from flow_ctrl
        where status = 1
          and type = 1
          and d_flag = 0
          and find_in_set(#{strategyId}, effective_strategy)
    </select>

    <select id="getByType" resultMap="BaseResultMap">
        <choose>
            <when test="type == 2">
                <choose>
                    <when test="effective != 0">
                        select *
                        from flow_ctrl
                        where status = 1
                        and type = 2
                        and d_flag = 0
                        and find_in_set(#{effective}, effective_channel)
                    </when>
                    <otherwise>
                        select *
                        from flow_ctrl
                        where status = 1
                        and type = 2
                        and d_flag = 0
                        and effective_channel = #{effective}
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                <choose>
                    <when test="effective != 0">
                        select *
                        from flow_ctrl
                        where status = 1
                        and type = 1
                        and d_flag = 0
                        and find_in_set(#{effective}, effective_strategy)
                    </when>
                    <otherwise>
                        select *
                        from flow_ctrl
                        where status = 1
                        and type = 1
                        and d_flag = 0
                        and effective_strategy = #{effective}
                    </otherwise>
                </choose>
            </otherwise>
        </choose>
    </select>

    <select id="selectByFlowCtrlId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from flow_ctrl
        where id = #{flowCtrlId,jdbcType=BIGINT} and d_flag = 0 limit 1
    </select>

    <select id="selectInstantStrategyRule" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from flow_ctrl
        where strategy_type = #{strategyType} and effective_strategy = #{effectiveStrategy} and d_flag = 0 limit 1
    </select>

    <select id="selectInstantStrategyRulesOnline" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from flow_ctrl
        where strategy_type = #{strategyType} and effective_strategy = #{effectiveStrategy} and status = 1 and d_flag = 0 order by id desc
    </select>

    <select id="selectAllEffectContentByType" resultMap="BaseResultMap">
        select effective_strategy,effective_channel
        from flow_ctrl
        where d_flag = 0
        <if test="flowCtrlId != null">
            and id != #{id,jdbcType=BIGINT}
        </if>
        and status = 1 and `type` = #{type}
        <if test="bizType != null">
            and biz_type = #{bizType}
        </if>
    </select>

    <select id="selectAllRule" resultMap="BaseResultMap">
        select *
        from flow_ctrl
        where d_flag = 0
        and `type` in (2,4)
        <if test="name != null and name != ''">
            <bind name="name" value='name.replaceAll("%","/%")'/>
            and `name` like concat('%',#{name},'%') escape '/'
        </if>
        <if test="ruleId != null and ruleId != ''">
            and id = #{ruleId}
        </if>
        <if test="status != null">
            and status = #{status}
        </if>
        <if test="type != null">
            and `type` = #{type}
        </if>
        <if test="updatedOp != null and updatedOp != ''">
            <bind name="updatedOp" value='updatedOp.replaceAll("%","/%")'/>
            and updated_op like concat('%',#{updatedOp},'%') escape '/'
        </if>
        <if test="createdOp != null and createdOp != ''">
            <bind name="createdOp" value='createdOp.replaceAll("%","/%")'/>
            and created_op like concat('%',#{createdOp},'%') escape '/'
        </if>
        <if test="startTime != null">
            and updated_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            and updated_time &lt;= #{endTime}
        </if>
        <if test="channel != null">
            and find_in_set(#{channel}, effective_channel)
        </if>
        <if test="bizType != null">
            and biz_type = #{bizType}
        </if>

        <if test="strategyIds != null">
            and (
            <trim prefixOverrides="or">
                <foreach collection="strategyIds" item="strategyId">
                    or find_in_set(#{strategyId}, effective_strategy)
                </foreach>
            </trim>
            )
        </if>
        order by status,updated_time desc
    </select>

    <select id="selectByRuleName" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from flow_ctrl
        where `name` = #{name} and d_flag = 0
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        update flow_ctrl
        set d_flag = 1
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.xftech.cdp.infra.repository.cdp.flowctrl.po.FlowCtrlDo" useGeneratedKeys="true">
        insert into flow_ctrl
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="description != null">description,</if>
            <if test="status != null">status,</if>
            <if test="type != null">type,</if>
            <if test="effectiveStrategy != null">effective_strategy,</if>
            <if test="effectiveChannel != null">effective_channel,</if>
            <if test="dayCount != null">day_count,</if>
            <if test="weekCount != null">week_count,</if>
            <if test="monthCount != null">month_count,</if>
            <if test="strategyType != null">strategy_type,</if>
            <if test="limitDays != null">limit_days,</if>
            <if test="limitTimes != null">limit_times,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updatedTime != null">updated_time,</if>
            <if test="dFlag != null">d_flag,</if>
            <if test="createdOp != null">created_op,</if>
            <if test="updatedOp != null">updated_op,</if>
            <if test="bizType != null">biz_type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="name != null">#{name,jdbcType=VARCHAR},</if>
            <if test="description != null">#{description,jdbcType=VARCHAR},</if>
            <if test="status != null">#{status,jdbcType=SMALLINT},</if>
            <if test="type != null">#{type,jdbcType=SMALLINT},</if>
            <if test="effectiveStrategy != null">#{effectiveStrategy,jdbcType=VARCHAR},</if>
            <if test="effectiveChannel != null">#{effectiveChannel,jdbcType=VARCHAR},</if>
            <if test="dayCount != null">#{dayCount,jdbcType=INTEGER},</if>
            <if test="weekCount != null">#{weekCount,jdbcType=INTEGER},</if>
            <if test="monthCount != null">#{monthCount,jdbcType=INTEGER},</if>
            <if test="strategyType != null">#{strategyType,jdbcType=SMALLINT},</if>
            <if test="limitDays != null">#{limitDays,jdbcType=INTEGER},</if>
            <if test="limitTimes != null">#{limitTimes,jdbcType=INTEGER},</if>
            <if test="createdTime != null">#{createdTime,jdbcType=TIMESTAMP},</if>
            <if test="updatedTime != null">#{updatedTime,jdbcType=TIMESTAMP},</if>
            <if test="dFlag != null">#{dFlag,jdbcType=SMALLINT},</if>
            <if test="createdOp != null">#{createdOp,jdbcType=VARCHAR},</if>
            <if test="updatedOp != null">#{updatedOp,jdbcType=VARCHAR},</if>
            <if test="bizType != null">#{bizType,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.xftech.cdp.infra.repository.cdp.flowctrl.po.FlowCtrlDo">
        update flow_ctrl
        set `name`             = #{name,jdbcType=VARCHAR},
            description        = #{description,jdbcType=VARCHAR},
            status             = #{status,jdbcType=SMALLINT},
            `type`             = #{type,jdbcType=SMALLINT},
            effective_strategy = #{effectiveStrategy,jdbcType=VARCHAR},
            effective_channel  = #{effectiveChannel,jdbcType=VARCHAR},
            day_count          = #{dayCount,jdbcType=INTEGER},
            week_count         = #{weekCount,jdbcType=INTEGER},
            month_count        = #{monthCount,jdbcType=INTEGER},
            strategy_type      = #{strategyType,jdbcType=INTEGER},
            limit_days         = #{limitDays,jdbcType=INTEGER},
            limit_times        = #{limitTimes,jdbcType=INTEGER},
            <if test="createdTime != null">
                created_time       = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            d_flag             = #{dFlag,jdbcType=SMALLINT},
            <if test="createdOp != null">
                created_op         = #{createdOp,jdbcType=VARCHAR},
            </if>
            updated_op         = #{updatedOp,jdbcType=VARCHAR},
            updated_time       = now()
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateStatusByPrimaryKey" parameterType="com.xftech.cdp.infra.repository.cdp.flowctrl.po.FlowCtrlDo">
        update flow_ctrl
        <set>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="dFlag != null">
                d_flag = #{dFlag},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="closeByStrategyId">
        update flow_ctrl
        set status = 2
        where status = 1
          and type = 1
          and d_flag = 0
          and effective_strategy = #{strategyId}
    </update>

</mapper>
