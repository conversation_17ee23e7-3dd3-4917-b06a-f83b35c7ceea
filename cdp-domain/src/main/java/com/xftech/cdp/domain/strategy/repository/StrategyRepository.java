package com.xftech.cdp.domain.strategy.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.base.database.Page;
import com.xftech.cdp.api.dto.req.StrategyListReq;
import com.xftech.cdp.api.dto.req.external.CallingSourceEnum;
import com.xftech.cdp.domain.strategy.model.enums.BusinessTypeEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyStatusEnum;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;
import com.xftech.cdp.infra.utils.JsonUtil;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 策略表操作
 *
 * <AUTHOR>
 * @since 2023/2/9 01:38
 */

@Component
public class StrategyRepository {

    /**
     * 根据主键id查询某条策略
     *
     * @param strategyId 策略id
     * @return id对应的记录
     */
    public StrategyDo selectById(Long strategyId) {
        return DBUtil.selectOne("strategy.selectByPrimaryKey", strategyId);
    }

    public List<StrategyDo> selectByCallingSourceAndBusinessType(CallingSourceEnum callingSource, String businessType) {
        Map<String, Object> param = new HashMap<>();
        param.put("callingSource", callingSource.getCode());
        param.put("businessType", businessType);
        return DBUtil.selectList("strategy.selectByCallingSourceAndBusinessType", param);
    }

    /**
     * 插入一条策略记录
     *
     * @param strategyDo 策略对象
     * @return 是否插入成功标识
     */
    public boolean insert(StrategyDo strategyDo) {
        return DBUtil.insert("strategy.insertSelective", strategyDo) > 0;
    }

    /**
     * 根据主键id删除某条策略
     *
     * @param strategyId 策略主键id
     * @return 是否删除成功标识
     */
//    public boolean delete(Long strategyId) {
//        return DBUtil.delete("strategy.deleteByPrimaryKey", strategyId) > 0;
//    }

    /**
     * 根据策略名称查询策略
     *
     * @param strategyName 策略名称
     * @return 该名称对应的策略记录
     */
    public StrategyDo getByNameAndBusinessType(String strategyName, String businessType) {
        Map<String, Object> param = new HashMap<>();
        param.put("strategyName", strategyName);
        param.put("businessType", businessType);
        return DBUtil.selectOne("strategy.getByNameAndBusinessType", param);
    }

    /**
     * 根据前端条件分页查询策略列表
     *
     * @param strategyListReq 策略查询参数
     * @return 策略列表
     */
    public Page<StrategyDo> selectPage(StrategyListReq strategyListReq) {
        List<Integer> sendRulerList = null;
        StrategyListReq param = JsonUtil.parse(JsonUtil.toJson(strategyListReq), StrategyListReq.class);
        if (strategyListReq != null) {
            if (strategyListReq.getSendRuler() == null) {
                // not do
            } else if (strategyListReq.getSendRuler() == 3) {
                param.setSendRuler(2);
                param.setType(1);
            } else if (strategyListReq.getSendRuler() == 4) {
                param.setSendRuler(3);
                param.setType(0);
            } else if (strategyListReq.getSendRuler() == 5) {
                param.setSendRuler(null);
                sendRulerList = Arrays.asList(0, 1, 3);
                param.setType(1);
            } else {
                param.setType(0);
            }
        }
        Map<String, Object> map = null;
        if (param != null){
            map = JsonUtil.toMap(JsonUtil.toJson(param));
            map.put("sendRulerList", sendRulerList);
        }
        return DBUtil.selectPage("strategy.selectByMarketChannel", map, strategyListReq.getBeginNum(), strategyListReq.getSize());
    }

    /**
     * 分页查询策略列表
     *
     * @return 策略列表
     */
    public Page<StrategyDo> selectPageByStatusAndCallingSource(List<StrategyStatusEnum> statusList, Long strategyId,
                                                               String businessType, CallingSourceEnum callingSourceEnum,
                                                               Integer pageNum, Integer pageSize) {
        Map<String, Object> param = new HashMap<>();
        param.put("strategyId", strategyId);
        param.put("businessType", businessType);
        param.put("statusList", statusList.stream().map(StrategyStatusEnum::getCode).collect(Collectors.toList()));
        if (Objects.nonNull(callingSourceEnum)) {
            param.put("callingSource", callingSourceEnum.getCode());
        }
        return DBUtil.selectPage("strategy.selectPageByStatusAndCallingSource", param, pageNum, pageSize);
    }

    /**
     * 根据主键id更新某条策略记录
     *
     * @param updateStrategy 策略对象
     * @return 是否更新成功标识
     */
    public boolean updateById(StrategyDo updateStrategy) {
        updateStrategy.setUpdatedTime(LocalDateTime.now());
        return DBUtil.update("strategy.updateByPrimaryKeySelective", updateStrategy) > 0;
    }

    /**
     * 查询所有策略列表
     *
     * @return 所有策略列表
     */
    public List<StrategyDo> selectAll() {
        return DBUtil.selectList("strategy.selectAll", null);
    }

    /**
     * 根据T0(事件策略)状态为执行中的所有策略列表
     *
     * @return
     */
    public List<StrategyDo> listT0ExecutingStrategy() {
        return DBUtil.selectList("strategy.listT0ExecutingStrategy", null);
    }

    /**
     * 根据策略名称查询策略-模糊匹配
     *
     * @param strategyName 策略名称
     * @return 该名称对应的策略记录
     */
    public List<StrategyDo> getByNameLike(String strategyName) {
        return DBUtil.selectList("strategy.getByNameLike", strategyName);
    }

    /**
     * 根据策略id查询策略-模糊匹配
     *
     * @param ids 策略ids
     * @return 该名称对应的策略记录
     */
    public List<StrategyDo> getByIds(List<Long> ids) {
        Map<String, Object> param = new HashMap<>();
        param.put("ids", ids);
        return DBUtil.selectList("strategy.getByIds", param);
    }

    public List<StrategyDo> getBatchStrategy() {
        return DBUtil.selectList("strategy.getBatchStrategy", null);
    }

    public List<StrategyDo> getAllEventStrategy() {
        return DBUtil.selectList("strategy.getAllEventStrategy", null);
    }

    public List<StrategyDo> getEventStrategy() {
        return DBUtil.selectList("strategy.getEventStrategy", null);
    }

    public List<StrategyDo> getExecutingEventStrategy() {
        return DBUtil.selectList("strategy.getExecutingEventStrategy", null);
    }

    public void batchUpdate(List<StrategyDo> list) {
        DBUtil.updateBatchWithoutTx("strategy.updateByPrimaryKeySelective", list);
    }

    public List<Long> getExecutingEventStrategyIds() {
        return DBUtil.selectList("strategy.getExecutingEventStrategyIds", null);
    }

    public List<StrategyDo> getEventStrategyByValidity(LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> param = new HashMap<>();
        param.put("startTime", startTime);
        param.put("endTime", endTime);
        return DBUtil.selectList("strategy.getEventStrategyByValidity", param);
    }

    public List<StrategyDo> selectExistOfflineStrategy() {
        return DBUtil.selectList("strategy.selectExistOfflineStrategy", null);
    }

    public List<StrategyDo> getStrategyByExecTime(LocalDate date) {
        return DBUtil.selectList("strategy.getStrategyByExecTime", date);
    }

    public List<StrategyDo> getAllStrategyStatusAndCrowdPack() {
        return DBUtil.selectList("strategy.getAllStrategyStatusAndCrowdPack", null);
    }

    public List<StrategyDo> offlineStrategyCrowdStatusAlarm(LocalTime localTime, Integer minute) {
        Map<String, Object> param = new HashMap<>();
        param.put("localTime", localTime.format(DateTimeFormatter.ofPattern("HH:mm:00")));
        param.put("minute", minute);
        return DBUtil.selectList("strategy.offlineStrategyCrowdStatusAlarm", param);
    }

    public List<StrategyDo> getAllNotExpired() {
        return DBUtil.selectList("strategy.getAllNotExpired", null);
    }

    public List<StrategyDo> getStrategyList(List<Integer> sendRulerList, List<Integer> statusList) {
        Map<String, Object> param = new HashMap<>();
        param.put("sendRulerList", sendRulerList);
        param.put("statusList", statusList);
        return DBUtil.selectList("strategy.getStrategyList", param);
    }

    public int deleteById(Long id) {
        return DBUtil.update("strategy.deleteByPrimaryKey", id);
    }

    public List<StrategyDo> getAllStrategyList(List<Integer> sendRulerList, List<Integer> statusList) {
        Map<String, Object> param = new HashMap<>();
        param.put("sendRulerList", sendRulerList);
        param.put("statusList", statusList);
        return DBUtil.selectList("strategy.getAllStrategyList", param);
    }
}
