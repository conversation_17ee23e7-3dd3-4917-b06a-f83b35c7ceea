package com.xftech.cdp.domain.crowd.service;

import com.xftech.cdp.domain.crowd.service.impl.CrowdPushBatchServiceImpl;
import com.xftech.cdp.domain.dispatch.dto.DispatchDto;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPackDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPushBatchDo;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-02-28
 */
public interface CrowdPushBatchService {
    void crowdPushResultQuery();

    void updateById(CrowdPushBatchDo crowdPushBatchDo);

    <T> void initCrowdPushBatch(DispatchDto dispatchDto, CrowdPushBatchDo crowdPushBatchDo, String app, String innerApp, List<T> userIdList, String code, String message, String batchNum);

    void init(DispatchDto dispatchDto, CrowdPushBatchDo crowdPushBatchDo, String batchNum, CrowdPushBatchServiceImpl.BatchItem<?> item, String sendCode, String sendMsg);

    <T> void insert(DispatchDto dispatchDto, CrowdPushBatchDo crowdPushBatchDo, String app, String innerApp, List<T> idList, String code, String message, String flowNo);

    void pushCrowdSqlToAds(CrowdPackDo crowdPackDo);

    void getAllCrowTotal();

    void pushCrowdList();

    void pushCrowdListRetry();

    void pushCrowdListLog();
}
