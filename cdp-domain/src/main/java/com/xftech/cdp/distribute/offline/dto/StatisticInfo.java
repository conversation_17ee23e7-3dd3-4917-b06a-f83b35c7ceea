/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2025. All Rights Reserved.
 */

package com.xftech.cdp.distribute.offline.dto;

import lombok.Data;

import java.util.Map;

/**
 * 执行统计信息，各个环节拦截过滤人数，过滤原因
 * <AUTHOR>
 * @version $ StrategyStatisticInfo, v 0.1 2025/4/30 20:04 xu.fan Exp $
 */
@Data
public class StatisticInfo {

    private long total; // 分片总人数

    private long realtimeFilterCnt;

    private long sendCnt;

    private Map<String, Long> groupCountMap;



}
