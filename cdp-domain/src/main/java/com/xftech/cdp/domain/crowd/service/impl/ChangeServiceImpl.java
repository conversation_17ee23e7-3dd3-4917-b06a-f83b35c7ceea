/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xftech.cdp.domain.crowd.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.xftech.cdp.api.dto.req.*;
import com.xftech.cdp.domain.crowd.model.enums.CrowdFilterMethodEnum;
import com.xftech.cdp.domain.crowd.model.enums.CrowdOperateEnum;
import com.xftech.cdp.domain.crowd.model.enums.CrowdRefreshTypeEnum;
import com.xftech.cdp.domain.crowd.service.ChangeService;
import com.xftech.cdp.domain.strategy.model.enums.StrategyOperateEnum;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPackDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;
import com.xftech.cdp.infra.utils.SsoUtil;
import com.xinfei.chgctrlmng.sdk.rr.ChangeContent;
import com.xinfei.chgctrlmng.sdk.rr.req.ChangeEventRequest;
import com.xinfei.chgctrlmng.sdk.sdk.change.client.ChangeNotifyClient;
import com.xinfei.xfframework.common.SpringUtil;
import com.xinfei.xfframework.context.Context;
import org.apache.commons.lang3.StringUtils;
import com.xftech.cdp.domain.strategy.model.enums.BusinessTypeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $ ChangeServiceImpl, v 0.1 2025/4/7 10:25 chuan.zhuang Exp $
 */
@Service
public class ChangeServiceImpl implements ChangeService {
    private static final Logger logger = LoggerFactory.getLogger(ChangeServiceImpl.class);

    private static final String CREATE_CROWD_SCENE_CODE = "create-crowd";
    private static final String UPDATE_CROWD_SCENE_CODE = "update-crowd";
    private static final String BATCH_DELETE_CROWD_SCENE_CODE = "batch-delete-crowd";
    private static final String OPERATE_CROWD_SCENE_CODE = "operate-crowd";

    private static final String INSERT_STRATEGY_SCENE_CODE = "create-strategy";
    private static final String UPDATE_STRATEGY_SCENE_CODE = "update-strategy";
    private static final String OPERATE_STRATEGY_SCENE_CODE = "operate-strategy";
    private static final String BATCH_DELETE_STRATEGY_SCENE_CODE = "batch-delete-strategy";


    @Resource
    private ChangeNotifyClient changeNotifyClient;

    @Override
    public void asyncSubmitCreateCrowdChange(String bizId, CrowdCreateReq param, CrowdPackDo crowdPackDo) {
        try {
            // 初始化通用属性
            ChangeEventRequest request = initChangeEventRequest(bizId, param);

            // 变更场景 （英文字符）
            request.setSceneCode(CREATE_CROWD_SCENE_CODE);
            // 变更名称（会用于 cat 大盘展示）
            request.setName("新增人群包（" + param.getCrowdName() + "）");
            // 变更的地址（用户变更管控平台，查看详情）
            String host = SpringUtil.getPro("xf." + Context.APP_NAME + ".web.url");
            request.setUrl(host + "/fish-intestines/crowd/crowd-selection/edit?id=" + bizId + "&name=check&filterMethod=" + param.getFilterMethod());

            ChangeContent changeContent = request.getChangeContents().get(0);
            changeContent.setInstanceInfos(initCrowdInstanceInfo(crowdPackDo));

            changeNotifyClient.asyncSubmitChangeEvent(request, (result) -> {
                // if you need to do something
            });
        } catch (Exception e) {
            // 异常 catch 不影响主链路
            logger.error("Async submit change event failure.", e);
        }
    }

    @Override
    public void asyncSubmitCrowdCreateByExcelChange(CrowdUploadReq param, CrowdPackDo crowdPackDo) {
        try {
            // 初始化通用属性
            ChangeEventRequest request = initChangeEventRequest(param.getUploadLogId() + "", param);

            // 变更场景 （英文字符）
            request.setSceneCode(CREATE_CROWD_SCENE_CODE);
            // 变更名称（会用于 cat 大盘展示）
            request.setName("Excel上传人群包（" + param.getCrowdName() + "）");
            // 变更的地址（用户变更管控平台，查看详情）
            String host = SpringUtil.getPro("xf." + Context.APP_NAME + ".web.url");
            request.setUrl(host + "/fish-intestines/crowd/crowd-selection");

            ChangeContent changeContent = request.getChangeContents().get(0);
            changeContent.setOperateType("Upload");
            changeContent.setInstanceInfos(initCrowdInstanceInfo(crowdPackDo));

            changeNotifyClient.asyncSubmitChangeEvent(request, (result) -> {
                // if you need to do something
            });
        } catch (Exception e) {
            // 异常 catch 不影响主链路
            logger.error("Async submit change event failure.", e);
        }
    }

    @Override
    public void asyncSubmitOperateCrowdChange(CrowdOperateReq param, CrowdPackDo crowdPackDo) {
        try {
            // 初始化通用属性
            ChangeEventRequest request = initChangeEventRequest(param.getCrowdId() + "", param);

            // 变更场景 （英文字符）
            request.setSceneCode(OPERATE_CROWD_SCENE_CODE);
            // 变更名称（会用于 cat 大盘展示）
            CrowdOperateEnum operateEnum = CrowdOperateEnum.getInstance(param.getRunType());
            String operate = (operateEnum != null ? operateEnum.getDescription() : "");
            request.setName(operate + "人群包（" + crowdPackDo.getCrowdName() + "）");
            // 变更的地址（用户变更管控平台，查看详情）
            String host = SpringUtil.getPro("xf." + Context.APP_NAME + ".web.url");
            request.setUrl(host + "/fish-intestines/crowd/crowd-selection");

            // 出参
            request.setOutputResultJson(null);

            // 变更内容
            ChangeContent changeContent = request.getChangeContents().get(0);
            // 操作类型（推荐：英文首字母大写 Add/Update/Delete/Online/Offline 等）
            changeContent.setOperateType(operate);
            changeContent.setInstanceInfos(initCrowdInstanceInfo(crowdPackDo));

            changeNotifyClient.asyncSubmitChangeEvent(request, (result) -> {
                // if you need to do something
            });
        } catch (Exception e) {
            // 异常 catch 不影响主链路
            logger.error("Async submit change event failure.", e);
        }
    }

    @Override
    public void asyncSubmitUpdateCrowdChange(CrowdUpdateReq param, CrowdPackDo oldValue) {
        try {
            // 初始化通用属性
            ChangeEventRequest request = initChangeEventRequest(param.getId() + "", param);

            // 变更场景 （英文字符）
            request.setSceneCode(UPDATE_CROWD_SCENE_CODE);
            // 变更名称（会用于 cat 大盘展示）
            request.setName("修改人群包（" + param.getCrowdName() + "）");
            // 变更的地址（用户变更管控平台，查看详情）
            String host = SpringUtil.getPro("xf." + Context.APP_NAME + ".web.url");
            request.setUrl(host + "/fish-intestines/crowd/crowd-selection/edit?id=" + param.getId() + "&name=check&filterMethod=" + param.getFilterMethod());

            ChangeContent changeContent = request.getChangeContents().get(0);
            changeContent.setOperateType("Update");
            changeContent.setNewValue(JSON.toJSONString(param));
            changeContent.setOldValue(JSON.toJSONString(oldValue));

            changeContent.setInstanceInfos(initCrowdInstanceInfo(oldValue));

            changeNotifyClient.asyncSubmitChangeEvent(request, (result) -> {
                // if you need to do something
            });
        } catch (Exception e) {
            // 异常 catch 不影响主链路
            logger.error("Async submit change event failure.", e);
        }
    }

    @Override
    public void asyncSubmitBatchDeleteCrowdChange(CrowdBatchDeleteReq param, List<CrowdPackDo> crowdPackDos) {
        try {
            // 初始化通用属性
            ChangeEventRequest request = initChangeEventRequest(StringUtils.join(param.getCrowdIds(), ","), param);

            List<String> crowdNames = crowdPackDos.stream().map(CrowdPackDo::getCrowdName).collect(Collectors.toList());

            // 变更场景 （英文字符）
            request.setSceneCode(BATCH_DELETE_CROWD_SCENE_CODE);
            // 变更名称（会用于 cat 大盘展示）
            request.setName("批量删除人群包（" + StringUtils.join(crowdNames, ",") + "）");
            // 变更的地址（用户变更管控平台，查看详情）
            String host = SpringUtil.getPro("xf." + Context.APP_NAME + ".web.url");
            request.setUrl(host + "/fish-intestines/crowd/crowd-selection");

            List<ChangeContent> changeContents = new ArrayList<>();
            for (CrowdPackDo crowdPackDo : crowdPackDos) {
                ChangeContent changeContent = new ChangeContent();
                changeContent.setOperateType("Delete");
                changeContent.setExecutor(SsoUtil.get().getName());
                changeContent.setModifiedDate(DateUtil.formatDateTime(new Date()));
                changeContent.setInstanceInfos(initCrowdInstanceInfo(crowdPackDo));
                changeContents.add(changeContent);
            }
            request.setChangeContents(changeContents);

            changeNotifyClient.asyncSubmitChangeEvent(request, (result) -> {
                // if you need to do something
            });
        } catch (Exception e) {
            // 异常 catch 不影响主链路
            logger.error("Async submit change event failure.", e);
        }
    }

    @Override
    public void asyncSubmitInsertStrategyChange(String bizId, StrategyCreateReq param, StrategyDo strategyDo) {
        try {
            ChangeEventRequest request = initChangeEventRequest(bizId, param);
            // 变更场景 （英文字符）
            request.setSceneCode(INSERT_STRATEGY_SCENE_CODE);
            // 变更名称（会用于 cat 大盘展示）
            request.setName("添加策略（" + param.getName() + "）");
            // 变更的地址（用户变更管控平台，查看详情）
            String host = SpringUtil.getPro("xf." + Context.APP_NAME + ".web.url");
            if (BusinessTypeEnum.OLD_CUST.getCode().equalsIgnoreCase(param.getBusinessType())) {
                request.setUrl(host + "/fish-intestines/operate/strategy/check?id=" + bizId + "&name=check");
            } else {
                request.setUrl(host + "/fish-intestines/operate/strategy/checkT0?id=" + bizId + "&name=check");
            }

            ChangeContent changeContent = request.getChangeContents().get(0);
            changeContent.setInstanceInfos(initStrategyInstanceInfo(strategyDo));

            // 如果是批量操作（同时操作多条记录） changeContent 是多个
            request.setChangeContents(Lists.newArrayList(changeContent));

            changeNotifyClient.asyncSubmitChangeEvent(request, (result) -> {
                // if you need to do something
            });
        } catch (Exception e) {
            // 异常 catch 不影响主链路
            logger.error("Async submit change event failure.", e);
        }
    }

    @Override
    public void asyncSubmitInsertT0StrategyChange(String bizId, InstantStrategyCreateReq param, StrategyDo strategyDo) {
        try {
            ChangeEventRequest request = initChangeEventRequest(bizId, param);
            // 变更场景 （英文字符）
            request.setSceneCode(INSERT_STRATEGY_SCENE_CODE);
            // 变更名称（会用于 cat 大盘展示）
            request.setName("添加T0策略（" + param.getName() + "）");
            // 变更的地址（用户变更管控平台，查看详情）
            String host = SpringUtil.getPro("xf." + Context.APP_NAME + ".web.url");
            if (BusinessTypeEnum.OLD_CUST.getCode().equalsIgnoreCase(param.getBusinessType())) {
                request.setUrl(host + "/fish-intestines/operate/strategy/check?id=" + bizId + "&name=check");
            } else {
                request.setUrl(host + "/fish-intestines/operate/strategy/checkT0?id=" + bizId + "&name=check");
            }

            ChangeContent changeContent = request.getChangeContents().get(0);
            changeContent.setInstanceInfos(initStrategyInstanceInfo(strategyDo));

            // 如果是批量操作（同时操作多条记录） changeContent 是多个
            request.setChangeContents(Lists.newArrayList(changeContent));

            changeNotifyClient.asyncSubmitChangeEvent(request, (result) -> {
                // if you need to do something
            });
        } catch (Exception e) {
            // 异常 catch 不影响主链路
            logger.error("Async submit change event failure.", e);
        }
    }

    @Override
    public void asyncSubmitUpdateT0StrategyChange(InstantStrategyUpdateReq param, StrategyDo oldValue) {
        try {
            ChangeEventRequest request = initChangeEventRequest(param.getId() + "", param);
            // 变更场景 （英文字符）
            request.setSceneCode(UPDATE_STRATEGY_SCENE_CODE);
            // 变更名称（会用于 cat 大盘展示）
            request.setName("修改T0策略（" + param.getName() + "）");
            // 变更的地址（用户变更管控平台，查看详情）
            String host = SpringUtil.getPro("xf." + Context.APP_NAME + ".web.url");
            if (BusinessTypeEnum.OLD_CUST.getCode().equalsIgnoreCase(oldValue.getBusinessType())) {
                request.setUrl(host + "/fish-intestines/operate/strategy/check?id=" + param.getId() + "&name=check");
            } else {
                request.setUrl(host + "/fish-intestines/operate/strategy/checkT0?id=" + param.getId() + "&name=check");
            }

            ChangeContent changeContent = request.getChangeContents().get(0);
            changeContent.setOperateType("Update");
            changeContent.setNewValue(JSON.toJSONString(param));
            changeContent.setOldValue(JSON.toJSONString(oldValue));
            changeContent.setInstanceInfos(initStrategyInstanceInfo(oldValue));

            // 如果是批量操作（同时操作多条记录） changeContent 是多个
            request.setChangeContents(Lists.newArrayList(changeContent));

            changeNotifyClient.asyncSubmitChangeEvent(request, (result) -> {
                // if you need to do something
            });
        } catch (Exception e) {
            // 异常 catch 不影响主链路
            logger.error("Async submit change event failure.", e);
        }
    }

    @Override
    public void asyncSubmitUpdateStrategyChange(StrategyUpdateReq param, StrategyDo oldValue) {
        try {
            ChangeEventRequest request = initChangeEventRequest(param.getId() + "", param);
            // 变更场景 （英文字符）
            request.setSceneCode(UPDATE_STRATEGY_SCENE_CODE);
            // 变更名称（会用于 cat 大盘展示）
            request.setName("修改策略（" + param.getName() + "）");
            // 变更的地址（用户变更管控平台，查看详情）
            String host = SpringUtil.getPro("xf." + Context.APP_NAME + ".web.url");
            if (BusinessTypeEnum.OLD_CUST.getCode().equalsIgnoreCase(oldValue.getBusinessType())) {
                request.setUrl(host + "/fish-intestines/operate/strategy/check?id=" + param.getId() + "&name=check");
            } else {
                request.setUrl(host + "/fish-intestines/operate/strategy/checkT0?id=" + param.getId() + "&name=check");
            }

            ChangeContent changeContent = request.getChangeContents().get(0);
            changeContent.setOperateType("Update");
            changeContent.setNewValue(JSON.toJSONString(param));
            changeContent.setOldValue(JSON.toJSONString(oldValue));
            changeContent.setInstanceInfos(initStrategyInstanceInfo(oldValue));

            // 如果是批量操作（同时操作多条记录） changeContent 是多个
            request.setChangeContents(Lists.newArrayList(changeContent));

            changeNotifyClient.asyncSubmitChangeEvent(request, (result) -> {
                // if you need to do something
            });
        } catch (Exception e) {
            // 异常 catch 不影响主链路
            logger.error("Async submit change event failure.", e);
        }
    }

    @Override
    public void asyncSubmitOperateStrategyChange(StrategyOperateReq strategyOperateReq, StrategyDo oldValue) {
        try {
            ChangeEventRequest request = initChangeEventRequest(oldValue.getId() + "", strategyOperateReq);
            // 变更场景 （英文字符）
            request.setSceneCode(OPERATE_STRATEGY_SCENE_CODE);
            // 变更名称（会用于 cat 大盘展示）
            StrategyOperateEnum operateEnum = StrategyOperateEnum.getInstance(strategyOperateReq.getRunType());
            request.setName(operateEnum == null ? "" : operateEnum.getDescription() + "策略（" + oldValue.getName() + "）");
            // 变更的地址（用户变更管控平台，查看详情）
            String host = SpringUtil.getPro("xf." + Context.APP_NAME + ".web.url");
            if (BusinessTypeEnum.OLD_CUST.getCode().equalsIgnoreCase(oldValue.getBusinessType())) {
                request.setUrl(host + "/fish-intestines/operate/strategy/check?id=" + oldValue.getId() + "&name=check");
            } else {
                request.setUrl(host + "/fish-intestines/operate/strategy/checkT0?id=" + oldValue.getId() + "&name=check");
            }

            ChangeContent changeContent = request.getChangeContents().get(0);
            changeContent.setOperateType(operateEnum == null ? null : operateEnum.getDescription());
            changeContent.setInstanceInfos(initStrategyInstanceInfo(oldValue));

            // 如果是批量操作（同时操作多条记录） changeContent 是多个
            request.setChangeContents(Lists.newArrayList(changeContent));

            changeNotifyClient.asyncSubmitChangeEvent(request, (result) -> {
                // if you need to do something
            });
        } catch (Exception e) {
            // 异常 catch 不影响主链路
            logger.error("Async submit change event failure.", e);
        }
    }

    @Override
    public void asyncSubmitDeleteStrategyChange(StrategyBatchDeleteReq param, List<StrategyDo> strategyDoList) {
        try {
            String bizId = StringUtils.join(param.getStrategyIds(), ",");
            ChangeEventRequest request = initChangeEventRequest(bizId, param);
            // 变更场景 （英文字符）
            request.setSceneCode(BATCH_DELETE_STRATEGY_SCENE_CODE);
            // 变更名称（会用于 cat 大盘展示）
            List<String> names = strategyDoList.stream().map(StrategyDo::getName).collect(Collectors.toList());
            request.setName("批量删除策略（" + StringUtils.join(names, ",") + "）");
            // 变更的地址（用户变更管控平台，查看详情）
            String host = SpringUtil.getPro("xf." + Context.APP_NAME + ".web.url");

            request.setUrl(host + "/fish-intestines/operate/strategy");

            List<ChangeContent> changeContentList = new ArrayList<>();
            for (StrategyDo strategyDo : strategyDoList) {
                ChangeContent changeContent = new ChangeContent();
                changeContent.setOperateType("Delete");
                changeContent.setExecutor(SsoUtil.get().getName());
                changeContent.setModifiedDate(DateUtil.formatDateTime(new Date()));
                changeContent.setInstanceInfos(initStrategyInstanceInfo(strategyDo));
                changeContentList.add(changeContent);
            }
            // 如果是批量操作（同时操作多条记录） changeContent 是多个
            request.setChangeContents(changeContentList);

            changeNotifyClient.asyncSubmitChangeEvent(request, (result) -> {
                // if you need to do something
            });
        } catch (Exception e) {
            // 异常 catch 不影响主链路
            logger.error("Async submit change event failure.", e);
        }
    }

    private ChangeEventRequest initChangeEventRequest(String bizId, Object param) {
        ChangeEventRequest request = new ChangeEventRequest();
        // 业务唯一id,每次请求都不能一样。可以使用时间戳保证唯一性
        request.setBizId(bizId + "@" + System.currentTimeMillis());
        // 平台 code （推荐直接使用自己的应用名）
        request.setPlatformCode(Context.APP_NAME);
        // 变更对象（推荐使用应用名）
        request.setChangeTarget(Context.APP_NAME);

        // 变更应用（可以是多个）
        // 同一个应用在 cat/xxljob/devops/k8s 上应用名不同，可以全部填入进去
        // 后续可以通过应用名进行变更影响面分析
        request.setChangeApps(Lists.newArrayList(Context.APP_NAME));

        // 变更影响的环境
        request.setChangeEnvs(Lists.newArrayList(Context.ENV));

        // 入参
        request.setInputArgParamJson(JSON.toJSONString(param));
        // 出参
        request.setOutputResultJson(null);

        request.setEndTime(new Date());
        // 创建人
        request.setCreator(SsoUtil.get().getName());
        // 执行人
        request.setExecutor(SsoUtil.get().getName());


        // 变更内容
        ChangeContent changeContent = new ChangeContent();
        // 操作类型（推荐：英文首字母大写 Add/Update/Delete/Online/Offline 等）
        changeContent.setOperateType("Add");
        // 执行人
        changeContent.setExecutor(SsoUtil.get().getName());
        // 变更之前的值
        changeContent.setOldValue(null);
        // 变更之后的值
        changeContent.setNewValue(null);
        // 修改时间
        changeContent.setModifiedDate(DateUtil.formatDateTime(new Date()));
        // 描述信息
        changeContent.setDescription("");

        // 如果是批量操作（同时操作多条记录） changeContent 是多个
        request.setChangeContents(Lists.newArrayList(changeContent));
        return request;
    }

    private List<ChangeContent.InstanceInfo> initCrowdInstanceInfo(CrowdPackDo crowdPackDo) {
        // 重要：业务属性（会使用在钉钉通知展示）
        List<ChangeContent.InstanceInfo> instanceInfos = Lists.newArrayList();
        // 属性类型 （描述信息可不填）
        instanceInfos.add(new ChangeContent.InstanceInfo(Context.APP_NAME + ".crowdName", crowdPackDo.getCrowdName()));
        instanceInfos.add(new ChangeContent.InstanceInfo(Context.APP_NAME + ".groupType", crowdPackDo.getGroupType() + ""));
        instanceInfos.add(new ChangeContent.InstanceInfo(Context.APP_NAME + ".filterMethod", CrowdFilterMethodEnum.getInstance(crowdPackDo.getFilterMethod()).getDescription()));
        instanceInfos.add(new ChangeContent.InstanceInfo(Context.APP_NAME + ".refreshType", CrowdRefreshTypeEnum.getInstance(crowdPackDo.getRefreshType()).getDescription()));
        instanceInfos.add(new ChangeContent.InstanceInfo(Context.APP_NAME + ".refreshTime", crowdPackDo.getRefreshTime().toString()));
        return instanceInfos;
    }

    private List<ChangeContent.InstanceInfo> initStrategyInstanceInfo(StrategyDo strategyDo) {
        // 重要：业务属性（会使用在钉钉通知展示）
        List<ChangeContent.InstanceInfo> instanceInfos = Lists.newArrayList();
        // 属性类型 （描述信息可不填）
        instanceInfos.add(new ChangeContent.InstanceInfo(Context.APP_NAME + ".name", strategyDo.getName()));
        instanceInfos.add(new ChangeContent.InstanceInfo(Context.APP_NAME + ".id", strategyDo.getId() + ""));
        instanceInfos.add(new ChangeContent.InstanceInfo(Context.APP_NAME + ".businessType", strategyDo.getBusinessType()));
        instanceInfos.add(new ChangeContent.InstanceInfo(Context.APP_NAME + ".businessTypeName", BusinessTypeEnum.getByCode(strategyDo.getBusinessType()).getDescription()));
        instanceInfos.add(new ChangeContent.InstanceInfo(Context.APP_NAME + ".crowdPackIds", strategyDo.getCrowdPackId()));
        return instanceInfos;
    }
}