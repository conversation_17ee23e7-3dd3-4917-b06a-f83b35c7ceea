package com.xftech.cdp.domain.crowd.service;

import com.xftech.cdp.api.dto.req.*;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPackDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;

import java.util.List;

/**
 * 变更管控服务
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
public interface ChangeService {
    // 人群圈选
    void asyncSubmitCreateCrowdChange(String bizId, CrowdCreateReq crowdCreateReq, CrowdPackDo crowdPackDo);

    void asyncSubmitCrowdCreateByExcelChange(CrowdUploadReq crowdUploadReq, CrowdPackDo crowdPackDo);

    void asyncSubmitOperateCrowdChange(CrowdOperateReq crowdOperateReq, CrowdPackDo crowdPackDo);

    void asyncSubmitUpdateCrowdChange(CrowdUpdateReq crowdUpdateReq, CrowdPackDo oldValue);

    void asyncSubmitBatchDeleteCrowdChange(CrowdBatchDeleteReq crowdBatchDeleteReq, List<CrowdPackDo> crowdPackDos);

    // 策略配置
    void asyncSubmitInsertStrategyChange(String bizId, StrategyCreateReq strategyCreateReq, StrategyDo strategyDo);

    void asyncSubmitUpdateStrategyChange(StrategyUpdateReq strategyUpdateReq, StrategyDo oldValue);

    void asyncSubmitInsertT0StrategyChange(String bizId, InstantStrategyCreateReq strategyCreateReq, StrategyDo strategyDo);

    void asyncSubmitUpdateT0StrategyChange(InstantStrategyUpdateReq strategyUpdateReq, StrategyDo oldValue);

    void asyncSubmitOperateStrategyChange(StrategyOperateReq strategyOperateReq, StrategyDo oldValue);

    void asyncSubmitDeleteStrategyChange(StrategyBatchDeleteReq strategyBatchDeleteRe,  List<StrategyDo> strategyDoList);


}
