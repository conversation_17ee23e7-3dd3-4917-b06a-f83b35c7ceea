package com.xftech.cdp.domain.strategy.model.enums;

import lombok.Getter;

/**
 * 标签查询响应码枚举
 */
@Getter
public enum LabelRespCodeEnum {

    /**
     * 成功
     */
    SUCCESS(200, "成功"),

    /**
     * 接口内部超时
     */
    INTERNAL_TIMEOUT(6001, "接口内部超时"),
    ;


    private final Integer code;

    private final String description;

    LabelRespCodeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

}
