/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.req.label;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version $ LabelRemarkReq, v 0.1 2024/6/20 16:09 lingang.han Exp $
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LabelRemarkReq implements Serializable {
    private static final long serialVersionUID = -4925092940561197963L;

    @ApiModelProperty(value = "元数据标签code")
    @NotNull
    private Long labelId;


    @ApiModelProperty(value = "注释内容")
    private String description;

}