<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="statStrategyGroupData">
    <resultMap id="BaseResultMap" type="com.xftech.cdp.domain.stat.entity.StatStrategyGroupDataEntity">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="biz_date" jdbcType="TIMESTAMP" property="bizDate"/>
        <result column="strategy_id" jdbcType="BIGINT" property="strategyId"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="strategy_group_id" jdbcType="BIGINT" property="strategyGroupId"/>
        <result column="strategy_group_name" jdbcType="VARCHAR" property="strategyGroupName"/>
        <result column="if_into_engine" jdbcType="SMALLINT" property="ifIntoEngine"/>
        <result column="engine_group_id" jdbcType="VARCHAR" property="engineGroupId"/>
        <result column="group_source" jdbcType="VARCHAR" property="groupSource"/>
        <result column="market_channel" jdbcType="SMALLINT" property="marketChannel"/>
        <result column="template_id" jdbcType="VARCHAR" property="templateId"/>
        <result column="status" jdbcType="SMALLINT" property="status"/>
        <result column="group_count" jdbcType="INTEGER" property="groupCount"/>
        <result column="exec_count" jdbcType="INTEGER" property="execCount"/>
        <result column="send_count" jdbcType="INTEGER" property="sendCount"/>
        <result column="receive_count" jdbcType="INTEGER" property="receiveCount"/>
        <result column="succ_count" jdbcType="INTEGER" property="succCount"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`
        , `biz_date`, `strategy_id`, `type`, `strategy_group_id`, `strategy_group_name`,
    `if_into_engine`, `engine_group_id`, `market_channel`, `template_id`, `status`, `group_count`, 
    `exec_count`, `send_count`, `receive_count`, `succ_count`, `created_time`, `updated_time`, `group_source`
    </sql>
    <select id="exitsRecord" resultType="integer">
        select count(*)
        from stat_strategy_group_data
        where strategy_id = #{strategyId}
        and type = #{type}
        and strategy_group_id = #{strategyGroupId}
        and if_into_engine = #{ifIntoEngine}
        <if test="engineGroupId != null">
            and engine_group_id = #{engineGroupId}
        </if>
        <if test="marketChannel != null">
            and market_channel = #{marketChannel}
        </if>
        <if test="templateId != null">
            and template_id = #{templateId}
        </if>
        and date (biz_date) = current_date ()
    </select>

    <select id="selectByDateAndType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from stat_strategy_group_data
        where date (biz_date) = #{time}
        <if test="typeList !=null and typeList.size()>0">
            and type in
            <foreach collection="typeList" item="type" open="(" close=")" separator=",">
                #{type}
            </foreach>
        </if>
    </select>


    <select id="selectPageByStrategyGroupId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from stat_strategy_group_data
        where strategy_group_id = #{strategyGroupId}
        <if test="marketChannel != null">
            and market_channel = #{marketChannel}
        </if>
        <if test="templateId != null">
            and template_id = #{templateId}
        </if>
        order by biz_date desc
    </select>


    <insert id="insertSelective" parameterType="com.xftech.cdp.domain.stat.entity.StatStrategyGroupDataEntity">
        insert into stat_strategy_group_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                `id`,
            </if>
            <if test="bizDate != null">
                `biz_date`,
            </if>
            <if test="strategyId != null">
                `strategy_id`,
            </if>
            <if test="type != null">
                `type`,
            </if>
            <if test="strategyGroupId != null">
                `strategy_group_id`,
            </if>
            <if test="strategyGroupName != null">
                `strategy_group_name`,
            </if>
            <if test="ifIntoEngine != null">
                `if_into_engine`,
            </if>
            <if test="engineGroupId != null">
                `engine_group_id`,
            </if>
            <if test="marketChannel != null">
                `market_channel`,
            </if>
            <if test="templateId != null">
                `template_id`,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="groupCount != null">
                `group_count`,
            </if>
            <if test="execCount != null">
                `exec_count`,
            </if>
            <if test="sendCount != null">
                `send_count`,
            </if>
            <if test="receiveCount != null">
                `receive_count`,
            </if>
            <if test="succCount != null">
                `succ_count`,
            </if>
            <if test="createdTime != null">
                `created_time`,
            </if>
            <if test="updatedTime != null">
                `updated_time`,
            </if>
            <if test="groupSource != null">
                `group_source`,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="bizDate != null">
                #{bizDate,jdbcType=TIMESTAMP},
            </if>
            <if test="strategyId != null">
                #{strategyId,jdbcType=BIGINT},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="strategyGroupId != null">
                #{strategyGroupId,jdbcType=BIGINT},
            </if>
            <if test="strategyGroupName != null">
                #{strategyGroupName,jdbcType=VARCHAR},
            </if>
            <if test="ifIntoEngine != null">
                #{ifIntoEngine,jdbcType=SMALLINT},
            </if>
            <if test="engineGroupId != null">
                #{engineGroupId,jdbcType=VARCHAR},
            </if>
            <if test="marketChannel != null">
                #{marketChannel,jdbcType=SMALLINT},
            </if>
            <if test="templateId != null">
                #{templateId,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=SMALLINT},
            </if>
            <if test="groupCount != null">
                #{groupCount,jdbcType=INTEGER},
            </if>
            <if test="execCount != null">
                #{execCount,jdbcType=INTEGER},
            </if>
            <if test="sendCount != null">
                #{sendCount,jdbcType=INTEGER},
            </if>
            <if test="receiveCount != null">
                #{receiveCount,jdbcType=INTEGER},
            </if>
            <if test="succCount != null">
                #{succCount,jdbcType=INTEGER},
            </if>
            <if test="createdTime != null">
                #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedTime != null">
                #{updatedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="groupSource != null">
                #{groupSource,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.xftech.cdp.domain.stat.entity.StatStrategyGroupDataEntity">
        update stat_strategy_group_data
        <set>
            <if test="bizDate != null">
                `biz_date` = #{bizDate,jdbcType=TIMESTAMP},
            </if>
            <if test="strategyId != null">
                `strategy_id` = #{strategyId,jdbcType=BIGINT},
            </if>
            <if test="type != null">
                `type` = #{type,jdbcType=INTEGER},
            </if>
            <if test="strategyGroupId != null">
                `strategy_group_id` = #{strategyGroupId,jdbcType=BIGINT},
            </if>
            <if test="strategyGroupName != null">
                `strategy_group_name` = #{strategyGroupName,jdbcType=VARCHAR},
            </if>
            <if test="ifIntoEngine != null">
                `if_into_engine` = #{ifIntoEngine,jdbcType=SMALLINT},
            </if>
            <if test="engineGroupId != null">
                `engine_group_id` = #{engineGroupId,jdbcType=VARCHAR},
            </if>
            <if test="groupSource != null">
                `group_source` = #{groupSource,jdbcType=VARCHAR},
            </if>
            <if test="marketChannel != null">
                `market_channel` = #{marketChannel,jdbcType=SMALLINT},
            </if>
            <if test="templateId != null">
                `template_id` = #{templateId,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=SMALLINT},
            </if>
            <if test="groupCount != null">
                `group_count` = #{groupCount,jdbcType=INTEGER},
            </if>
            <if test="execCount != null">
                `exec_count` = #{execCount,jdbcType=INTEGER},
            </if>
            <if test="sendCount != null">
                `send_count` = #{sendCount,jdbcType=INTEGER},
            </if>
            <if test="receiveCount != null">
                `receive_count` = #{receiveCount,jdbcType=INTEGER},
            </if>
            <if test="succCount != null">
                `succ_count` = #{succCount,jdbcType=INTEGER},
            </if>
            <if test="createdTime != null">
                `created_time` = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedTime != null">
                `updated_time` = #{updatedTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where `id` = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByRecord" parameterType="com.xftech.cdp.domain.stat.entity.StatStrategyGroupDataEntity">
        update stat_strategy_group_data
        <set>
            <if test="bizDate != null">
                `biz_date` = #{bizDate,jdbcType=TIMESTAMP},
            </if>
            <if test="strategyId != null">
                `strategy_id` = #{strategyId,jdbcType=BIGINT},
            </if>
            <if test="type != null">
                `type` = #{type,jdbcType=INTEGER},
            </if>
            <if test="strategyGroupId != null">
                `strategy_group_id` = #{strategyGroupId,jdbcType=BIGINT},
            </if>
            <if test="strategyGroupName != null">
                `strategy_group_name` = #{strategyGroupName,jdbcType=VARCHAR},
            </if>
            <if test="ifIntoEngine != null">
                `if_into_engine` = #{ifIntoEngine,jdbcType=SMALLINT},
            </if>
            <if test="engineGroupId != null">
                `engine_group_id` = #{engineGroupId,jdbcType=VARCHAR},
            </if>
            <if test="groupSource != null">
                `group_source` = #{groupSource,jdbcType=VARCHAR},
            </if>
            <if test="marketChannel != null">
                `market_channel` = #{marketChannel,jdbcType=SMALLINT},
            </if>
            <if test="templateId != null">
                `template_id` = #{templateId,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=SMALLINT},
            </if>
            <if test="groupCount != null">
                `group_count` = #{groupCount,jdbcType=INTEGER},
            </if>
            <if test="execCount != null">
                `exec_count` = #{execCount,jdbcType=INTEGER},
            </if>
            <if test="sendCount != null">
                `send_count` = #{sendCount,jdbcType=INTEGER},
            </if>
            <if test="receiveCount != null">
                `receive_count` = #{receiveCount,jdbcType=INTEGER},
            </if>
            <if test="succCount != null">
                `succ_count` = #{succCount,jdbcType=INTEGER},
            </if>
            <if test="createdTime != null">
                `created_time` = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedTime != null">
                `updated_time` = #{updatedTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where strategy_id = #{strategyId}
        and type = #{type}
        and strategy_group_id = #{strategyGroupId}
        and if_into_engine = #{ifIntoEngine}
        <if test="engineGroupId != null">
            and engine_group_id = #{engineGroupId}
        </if>
        <if test="marketChannel != null">
            and market_channel = #{marketChannel}
        </if>
        <if test="templateId != null">
            and template_id = #{templateId}
        </if>
        and date (biz_date) = current_date ()
    </update>
</mapper>