package com.xftech.cdp.domain.touch.model;

import lombok.Data;
import java.util.List;

/**
 * 流控配置模型
 * 包含各种流控相关的配置信息
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-23
 */
@Data
public class FlowControlConfig {
    
    /**
     * 是否启用事件级流控
     * 对应现有的isReject()检查
     */
    private Boolean enableEventFlowControl;
    
    /**
     * 是否启用触达级流控
     * 对应现有的dispatchFlc()检查
     */
    private Boolean enableTouchFlowControl;
    
    /**
     * 是否启用分布式流控
     * 对应现有的dispatchFlcLock()检查
     */
    private Boolean enableDistributedFlowControl;
    
    /**
     * 是否启用批量流控
     * 对应现有的flowCtrl()检查
     */
    private Boolean enableBatchFlowControl;
    
    /**
     * 流控规则列表
     * 对应现有的FlowCtrlDo列表
     */
    private List<FlowControlRule> rules;
    
    /**
     * 流控超时时间（毫秒）
     */
    private Integer flowControlTimeout;
    
    /**
     * 分布式锁过期时间（秒）
     */
    private Integer distributedLockExpireSeconds;
    
    /**
     * 创建默认流控配置
     */
    public static FlowControlConfig createDefault() {
        FlowControlConfig config = new FlowControlConfig();
        config.setEnableEventFlowControl(true);
        config.setEnableTouchFlowControl(true);
        config.setEnableDistributedFlowControl(false);
        config.setEnableBatchFlowControl(false);
        config.setFlowControlTimeout(5000); // 5秒
        config.setDistributedLockExpireSeconds(60); // 60秒
        return config;
    }
    
    /**
     * 创建T0实时流控配置
     */
    public static FlowControlConfig createT0Config() {
        FlowControlConfig config = createDefault();
        config.setEnableEventFlowControl(true);
        config.setEnableTouchFlowControl(true);
        config.setEnableDistributedFlowControl(false);
        config.setEnableBatchFlowControl(false);
        return config;
    }
    
    /**
     * 创建T0引擎流控配置
     */
    public static FlowControlConfig createT0EngineConfig() {
        FlowControlConfig config = createDefault();
        config.setEnableEventFlowControl(true);
        config.setEnableTouchFlowControl(false); // 引擎触达使用内置流控
        config.setEnableDistributedFlowControl(true);
        config.setEnableBatchFlowControl(false);
        return config;
    }
    
    /**
     * 创建离线批量流控配置
     */
    public static FlowControlConfig createOfflineBatchConfig() {
        FlowControlConfig config = createDefault();
        config.setEnableEventFlowControl(false);
        config.setEnableTouchFlowControl(false);
        config.setEnableDistributedFlowControl(false);
        config.setEnableBatchFlowControl(true);
        return config;
    }
    
    /**
     * 创建离线引擎流控配置
     */
    public static FlowControlConfig createOfflineEngineConfig() {
        FlowControlConfig config = createDefault();
        config.setEnableEventFlowControl(false);
        config.setEnableTouchFlowControl(false);
        config.setEnableDistributedFlowControl(false);
        config.setEnableBatchFlowControl(false); // 引擎内置流控
        return config;
    }
    
    /**
     * 判断是否启用任何流控
     */
    public boolean isAnyFlowControlEnabled() {
        return Boolean.TRUE.equals(enableEventFlowControl) ||
               Boolean.TRUE.equals(enableTouchFlowControl) ||
               Boolean.TRUE.equals(enableDistributedFlowControl) ||
               Boolean.TRUE.equals(enableBatchFlowControl);
    }
    
    /**
     * 判断是否有流控规则
     */
    public boolean hasFlowControlRules() {
        return rules != null && !rules.isEmpty();
    }
    
    /**
     * 获取流控规则数量
     */
    public int getFlowControlRuleCount() {
        return rules != null ? rules.size() : 0;
    }
    
    /**
     * 添加流控规则
     */
    public void addFlowControlRule(FlowControlRule rule) {
        if (rules == null) {
            rules = new java.util.ArrayList<>();
        }
        rules.add(rule);
    }
    
    /**
     * 验证流控配置
     */
    public void validate() {
        if (flowControlTimeout != null && flowControlTimeout <= 0) {
            throw new IllegalArgumentException("flowControlTimeout必须大于0");
        }
        
        if (distributedLockExpireSeconds != null && distributedLockExpireSeconds <= 0) {
            throw new IllegalArgumentException("distributedLockExpireSeconds必须大于0");
        }
        
        if (rules != null) {
            for (FlowControlRule rule : rules) {
                if (rule != null) {
                    rule.validate();
                }
            }
        }
    }
}
