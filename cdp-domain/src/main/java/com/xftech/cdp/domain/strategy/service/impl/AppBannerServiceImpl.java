/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.service.impl;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.function.BiPredicate;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;

import cn.hutool.core.util.IdUtil;
import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.base.TimeFormat;
import com.xftech.cdp.api.dto.req.AppBannerReq;
import com.xftech.cdp.api.dto.req.AppBannerTemplateReq;
import com.xftech.cdp.api.dto.resp.AppBannerResp;
import com.xftech.cdp.api.dto.resp.AppBannerTemplateResp;
import com.xftech.cdp.distribute.crowd.service.CrowdInfoService;
import com.xftech.cdp.domain.ads.model.PredictDecisionDto;
import com.xftech.cdp.domain.ads.service.AdsStrategyLabelService;
import com.xftech.cdp.domain.cache.CacheStrategyGroupService;
import com.xftech.cdp.domain.cache.CacheStrategyService;
import com.xftech.cdp.domain.crowd.model.dispatch.CrowdContext;
import com.xftech.cdp.domain.crowd.repository.CrowdPackRepository;
import com.xftech.cdp.domain.crowd.service.CrowdPackService;
import com.xftech.cdp.domain.flowctrl.model.enums.StrategyTypeEnum;
import com.xftech.cdp.domain.stat.entity.OfflineDecisionRecordEntity;
import com.xftech.cdp.domain.strategy.exception.StrategyException;
import com.xftech.cdp.domain.strategy.model.enums.*;
import com.xftech.cdp.domain.strategy.repository.AppBannerEngineRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyMarketChannelRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyRepository;
import com.xftech.cdp.domain.strategy.service.AppBannerLogService;
import com.xftech.cdp.domain.strategy.service.AppBannerService;
import com.xftech.cdp.domain.strategy.service.StrategyGroupService;
import com.xftech.cdp.domain.strategy.service.StrategyMarketEventConditionService;
import com.xftech.cdp.domain.strategy.vo.BatchAdsLabelVO;
import com.xftech.cdp.feign.model.AppBannerTemplateList;
import com.xftech.cdp.feign.model.response.AppBannerResponse;
import com.xftech.cdp.infra.client.appbanner.AppBannerClient;
import com.xftech.cdp.infra.client.usercenter.UserCenterClient;
import com.xftech.cdp.infra.client.usercenter.model.UserAbNumResp;
import com.xftech.cdp.infra.client.usercenter.model.UserInfoResp;
import com.xftech.cdp.infra.config.ApolloUtil;
import com.xftech.cdp.infra.config.LogUtil;
import com.xftech.cdp.infra.rabbitmq.enums.DecisionResultEnum;
import com.xftech.cdp.infra.rabbitmq.vo.HitResult;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPackDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.*;
import com.xftech.cdp.infra.thread.AppBannerExecExecutor;
import com.xftech.cdp.infra.utils.*;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.xinfei.usergroup.random.client.AbClient;
import cn.xinfei.usergroup.random.client.bo.AbBO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.proxy.Tracer;
import com.dianping.cat.proxy.spi.Transaction;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import static com.xftech.cdp.domain.strategy.repository.AppBannerEngineRepository.*;

/**
 * <AUTHOR>
 * @version $ AppBannerServiceImpl, v 0.1 2024/4/18 17:55 benlin.wang Exp $
 */

@Slf4j
@Service
public class AppBannerServiceImpl implements AppBannerService {
    @Autowired
    public CrowdPackRepository crowdPackRepository;
    @Autowired
    private StrategyMarketEventConditionService strategyMarketEventConditionService;
    @Autowired
    private CrowdPackService crowdPackService;
    @Autowired
    private AdsStrategyLabelService adsStrategyLabelService;
    @Autowired
    private StrategyMarketChannelRepository strategyMarketChannelRepository;
    @Autowired
    private StrategyRepository strategyRepository;
    @Autowired
    private CacheStrategyGroupService cacheStrategyGroupService;
    @Autowired
    private StrategyGroupService strategyGroupService;
    @Autowired
    private UserCenterClient userCenterClient;
    @Autowired
    private AppBannerClient appBannerClient;
    @Autowired
    private SerialNumberUtil serialNumberUtil;
    @Autowired
    private AppBannerLogService appBannerLogService;
    @Autowired
    private AppBannerEngineRepository appBannerEngineRepository;
    @Autowired
    private CacheStrategyService cacheStrategyService;
    @Autowired
    private CrowdInfoService crowdInfoService;

    private volatile Map<String, List<StrategyDo>> mAppBannerId2StrategyListMap;
    private volatile List<StrategyDo> mStrategyDos;
    private volatile Map<Long, List<CrowdPackDo>> mStrategyId2CrowdPackListMap;
    private volatile Map<Long, List<StrategyGroupDo>> mStrategyId2StrategyGroupListMap;
    private volatile Map<Long, List<StrategyMarketChannelDo>> mStrategyGroupId2MarkeetChannelListMap;

    @PostConstruct
    public void setupCacheRefresh() {

        Executors.newSingleThreadScheduledExecutor(r -> {
            Thread thread = new Thread(r, "refresh-app-banner-market-strategy");
            thread.setDaemon(true);
            return thread;
        }).scheduleAtFixedRate(() -> {
                    try {
                        log.info("*******************************App弹窗资源数据开始加载*********************************************");
                        LocalDateTime nowTime = LocalDateTime.now();
                        Map<String, Set<Long>> appBannerId2StrategyIdSetMap = new HashMap<>();

                        List<StrategyMarketChannelDo> strategyMarketChannelDos = strategyMarketChannelRepository.selectStrategyIdsWithAppBannerChannel(StrategyMarketChannelEnum.APP_BANNER.getCode());
                        strategyMarketChannelDos.forEach(strategyMarketChannelDo -> {
                            Long strategyId = strategyMarketChannelDo.getStrategyId();
                            Set<Long> strategyIdList = appBannerId2StrategyIdSetMap.computeIfAbsent(strategyMarketChannelDo.getTemplateId(), appBannerId -> new HashSet<>());
                            strategyIdList.add(strategyId);
                        });
                        Map<String, List<Long>> appBannerId2StrategyIdListMap = new HashMap<>(appBannerId2StrategyIdSetMap.size());
                        appBannerId2StrategyIdSetMap.forEach((appBannerId, idSet) -> appBannerId2StrategyIdListMap.put(appBannerId, new ArrayList<>(idSet)));

                        Set<Long> strategyIdSet = new HashSet<>();
                        strategyMarketChannelDos.forEach(strategyMarketChannelDo -> strategyIdSet.add(strategyMarketChannelDo.getStrategyId()));
                        Set<Integer> activeCodeSet = ListUtils.toSet(StrategyStatusEnum.getActiveCodes(), Function.identity());
                        List<StrategyDo> strategyDos = strategyIdSet.isEmpty() ? new ArrayList<>() : strategyRepository.getByIds(new ArrayList<>(strategyIdSet));
                        strategyDos = strategyDos.stream()
                                .filter(strategyDo -> activeCodeSet.contains(strategyDo.getStatus()))
                                .filter(strategyDo -> strategyDo.getValidityBegin().isBefore(nowTime) && strategyDo.getValidityEnd().isAfter(nowTime))
                                .collect(Collectors.toList());
                        mStrategyDos = strategyDos;
                        Map<Long, StrategyDo> id2StrategyDoMap = MapUtils.listToMap(strategyDos, StrategyDo::getId);
                        //appBannerId -> List<StrategyDo>
                        Map<String, List<StrategyDo>> appBannerId2StrategyListMap = new HashMap<>();
                        appBannerId2StrategyIdListMap.forEach((appBannerId, idList) -> {
                            List<StrategyDo> strategyDoList = appBannerId2StrategyListMap.computeIfAbsent(appBannerId, k -> new ArrayList<>());
                            idList.forEach(id -> {
                                if (id2StrategyDoMap.containsKey(id)) {
                                    strategyDoList.add(id2StrategyDoMap.get(id));
                                }
                            });
                        });

                        //缓存策略的人群包数据
                        Map<Long, List<CrowdPackDo>> strategyId2CrowdPackListMap = new HashMap<>(mStrategyDos.size());
                        mStrategyDos.forEach(strategyDo -> {
                            if (StringUtils.isBlank(strategyDo.getCrowdPackId())) {
                                return;
                            }
                            String[] crowdPackIds = strategyDo.getCrowdPackId().split(";");

                            List<CrowdPackDo> crowdPackDos = new ArrayList<>(crowdPackIds.length);
                            for (String crowdPackId : crowdPackIds) {
                                CrowdPackDo crowdPackDo = crowdPackRepository.selectById(Long.parseLong(crowdPackId));
                                if (crowdPackDo != null)
                                    crowdPackDos.add(crowdPackDo);
                            }
                            strategyId2CrowdPackListMap.put(strategyDo.getId(), crowdPackDos);
                        });

                        //缓存策略的分组数据
                        Map<Long, List<StrategyGroupDo>> strategyId2StrategyGroupListMap = new HashMap<>(mStrategyDos.size());
                        mStrategyDos.forEach(strategyDo -> {
                            List<StrategyGroupDo> strategyGroupDos = cacheStrategyGroupService.selectListByStrategyId(strategyDo.getId());
                            strategyId2StrategyGroupListMap.put(strategyDo.getId(), strategyGroupDos);
                        });

                        //缓存ab分组对应的营销渠道数据
                        Map<Long, List<StrategyMarketChannelDo>> strategyGroupId2MarkeetChannelListMap = new HashMap<>();
                        strategyId2StrategyGroupListMap.forEach((k, groupList) -> {
                            groupList.forEach(strategyGroupDo -> {
                                List<StrategyMarketChannelDo> strategyMarketChannelDosByGroup = strategyMarketChannelRepository.selectByStrategyGroupId(strategyGroupDo.getId());
                                strategyGroupId2MarkeetChannelListMap.put(strategyGroupDo.getId(), strategyMarketChannelDosByGroup);
                            });
                        });

                        mAppBannerId2StrategyListMap = appBannerId2StrategyListMap;
                        log.info("[AppBanner] 成功更新由app资源位id到营销策略的映射:{}", mAppBannerId2StrategyListMap);
                        mStrategyId2CrowdPackListMap = strategyId2CrowdPackListMap;
                        log.info("[AppBanner] 成功更新策略的人群包缓存:{}", mStrategyId2CrowdPackListMap);
                        mStrategyId2StrategyGroupListMap = strategyId2StrategyGroupListMap;
                        log.info("[AppBanner] 成功更新策略的AB分组缓存:{}", mStrategyId2StrategyGroupListMap);
                        mStrategyGroupId2MarkeetChannelListMap = strategyGroupId2MarkeetChannelListMap;
                        log.info("[AppBanner] 成功更新策略AB分组对应的营销渠道缓存:{}", mStrategyGroupId2MarkeetChannelListMap);
                    } catch (Exception ex) {
                        log.error("[AppBanner] 初始化缓存报错", ex);
                    }
                },
                8L,
                60L,
                TimeUnit.SECONDS);//每30s刷新数据
    }

    @Override
    public PageResultResponse<AppBannerTemplateResp> getAppBannerList(AppBannerTemplateReq appBannerTemplateReq) {
        try {
            PageResultResponse<AppBannerTemplateResp> result = new PageResultResponse<>();
            LogUtil.logDebug("getAppBannerList参数:{}", JsonUtil.toJson(appBannerTemplateReq));
            AppBannerResponse<AppBannerTemplateList> respList = appBannerClient.requestAppBannerList(appBannerTemplateReq);
            LogUtil.logDebug("getAppBannerList返回:{}", JsonUtil.toJson(respList));
            if (Objects.isNull(respList) || !respList.isSuccess() || Objects.isNull(respList.getData())) {
                LogUtil.logDebug("getAppBannerList 请求失败，request：{}，resp：{}", appBannerTemplateReq, respList);
                return result;
            }
            AppBannerTemplateList appBannerResp = respList.getData();
            if (Objects.isNull(appBannerResp.getDataList())) {
                return result;
            }
            result.setRecords(appBannerResp.getDataList().stream().map(item -> {
                AppBannerTemplateResp appBannerTemplateResp = new AppBannerTemplateResp();
                BeanUtils.copyProperties(item, appBannerTemplateResp);
                return appBannerTemplateResp;
            }).collect(Collectors.toList()));
            if (Objects.nonNull(appBannerResp.getPageInfo()) && Objects.nonNull(appBannerResp.getPageInfo().getCount()) && Objects.nonNull(appBannerResp.getPageInfo().getPageSize()) && Objects.nonNull(appBannerResp.getPageInfo().getPage())) {
                result.setSize(appBannerResp.getPageInfo().getPageSize().longValue());
                result.setCurrent(appBannerResp.getPageInfo().getPage().longValue());
                result.setTotal(appBannerResp.getPageInfo().getCount());
            }
            return result;
        } catch (Exception e) {
            LogUtil.logDebug("获取app弹窗列表时发生异常", e);
            throw new StrategyException("查询appBanner列表失败");
        }
    }

    @Override
    public List<AppBannerResp> recommendAppBannerList(AppBannerReq appBannerReq) {
        if (JSONObject.parseArray(ApolloUtil.getAppProperty("mock.whiteList.appBanner", "[]"), String.class).contains(String.valueOf(appBannerReq.getUserId()))) {
            return mockAppBannerResp(appBannerReq);
        }

        Long userId = appBannerReq.getUserId();
        List<String> appBannerIdList = appBannerReq.getAppBannerIdList();
        Map<String, List<StrategyDo>> appBannerId2StrategyListMap = new HashMap<>(appBannerIdList.size());
        List<StrategyDo> emptyList = new ArrayList<>();
        appBannerIdList.forEach(appBannerId -> appBannerId2StrategyListMap.put(appBannerId, mAppBannerId2StrategyListMap.getOrDefault(appBannerId, emptyList)));
        List<AppBannerResp> respList = Collections.emptyList();
        Transaction transaction = Tracer.newTransaction("AppBannerService", "doRecommendAppBannerList");
        try {
            respList = doRecommendAppBannerList(userId, appBannerReq.getMobile(), appBannerReq.getApp(), appBannerId2StrategyListMap, appBannerReq.getQueryType(), appBannerReq.getLoanRetentionRequest(), appBannerReq.getSettlementRequest());
            transaction.setStatus(Transaction.SUCCESS);
        } catch (Exception e) {
            log.warn("recommendAppBannerList, 参数:{}", JsonUtil.toJson(appBannerReq));
//            transaction.setStatus(Transaction.SUCCESS);
        } finally {
            transaction.complete();
        }

        return respList;
    }

    private List<AppBannerResp> doRecommendAppBannerList(Long userId, String userMobile, String app,
                                                         Map<String, List<StrategyDo>> appBannerId2StrategyListMap,
                                                         Integer queryType, AppBannerReq.LoanRetentionRequest loanRetentionRequest, AppBannerReq.SettlementRequest settlementRequest) {
        List<AppBannerResp> respList = new ArrayList<>();
        LocalDateTime triggerDatetime = LocalDateTimeUtil.now();
        String tableNumber = LocalDateTimeUtil.format(triggerDatetime, "yyyyMM");

        List<Long> crowdNotHitStrategyIdList = new ArrayList<>();
        List<Long> labelNotHitStrategyIdList = new ArrayList<>();
        Set<StrategyDo> hitStrategySet = new HashSet<>();

        appBannerId2StrategyListMap.forEach((k, strategyList) -> hitStrategySet.addAll(strategyList));

        //strategy_id -> (app_banner_id -> <strategy_group, strategy_market_channel>)
        Map<Long, Map<String, Pair<StrategyGroupDo, StrategyMarketChannelDo>>> strategyId2BannerIdMarketChannelMap = buildAppBanner2ABGroupMapByStrategyId(hitStrategySet, userId);
        log.info("app弹窗资源位, 用户命中的弹窗分组信息, userId:{}, strategyId2BannerIdMarketChannelMap:{}", userId, JsonUtil.toJson(strategyId2BannerIdMarketChannelMap));

        List<UserDispatchDetailDo> dispatchDetailDoList = new ArrayList<>();
        List<Long> abGroupNotHitStrategyIdList = new ArrayList<>();
        Set<StrategyDo> totalHitStrategySet = new HashSet<>();
        Map<String, Set<Long>> appBanner2StrategySetMap = new HashMap<>();
        //app_banner_id -> (strategy_id -> strategy_market_channel)
        Map<String, Map<Long, StrategyMarketChannelDo>> appBannerId2StrategyId2StrategyChannelMap = new HashMap<>();
        //app_banner_id -> (strategy_id -> strategy_group)
        Map<String, Map<Long, StrategyGroupDo>> appBannerId2StrategyId2StrategyGroupMap = new HashMap<>();

        appBannerId2StrategyListMap.forEach((appBannerId, strategyDoList) -> {
            Set<Long> hitStrategyIds = new HashSet<>();
            strategyDoList.forEach(strategyDo -> {
                Long strategyId = strategyDo.getId();
                Map<String, Pair<StrategyGroupDo, StrategyMarketChannelDo>> appBannerId2ABGroupMap = strategyId2BannerIdMarketChannelMap.getOrDefault(strategyId, null);
                if (appBannerId2ABGroupMap != null) {
                    if (appBannerId2ABGroupMap.containsKey(appBannerId)) {
                        hitStrategyIds.add(strategyId);
                        totalHitStrategySet.add(strategyDo);
                        Pair<StrategyGroupDo, StrategyMarketChannelDo> groupChannelPair = appBannerId2ABGroupMap.get(appBannerId);
                        Map<Long, StrategyMarketChannelDo> subChannelM = appBannerId2StrategyId2StrategyChannelMap.computeIfAbsent(appBannerId, k -> new HashMap<>());
                        subChannelM.put(strategyId, groupChannelPair.getRight());
                        Map<Long, StrategyGroupDo> subGroupM = appBannerId2StrategyId2StrategyGroupMap.computeIfAbsent(appBannerId, k -> new HashMap<>());
                        subGroupM.put(strategyId, groupChannelPair.getLeft());
                    } else {
                        abGroupNotHitStrategyIdList.add(strategyDo.getId());
                    }
                }
            });

            appBanner2StrategySetMap.put(appBannerId, hitStrategyIds);
        });

        // 不需要手机号
        // BaseCisResp<MobileByUserNo.RespDto> baseCisResp = cisService.queryMobileByUserNo(userId);
        String mobile = Optional.ofNullable(userMobile).orElse(""); //baseCisResp.getData().getMobile();
        Map<Long, HitResult> strategyId2HitResultMap = new HashMap<>(totalHitStrategySet.size());

        List<Future<FilterResult>> tasks = new ArrayList<>(totalHitStrategySet.size());
        for (StrategyDo strategyDo : totalHitStrategySet) {
            Future<FilterResult> task = AppBannerExecExecutor.getPool().submit(() -> {
                FilterResult filterResult = new FilterResult();
                filterResult.setFilterTypeEnum(FilterTypeEnum.Passed);
                filterResult.setStrategyDo(strategyDo);

                if (!checkCrowdPacks(strategyDo, userId)) {
                    filterResult.setFilterTypeEnum(FilterTypeEnum.ByGroup);
                    return filterResult;
                }

                HitResult hitResult = checkStrategyLabels(strategyDo, userId, mobile, app);
                filterResult.setHitResult(hitResult);
                if (!hitResult.getHit()) {
                    filterResult.setFilterTypeEnum(FilterTypeEnum.ByLabel);
                }
                return filterResult;
            });
            tasks.add(task);
        }

        Set<Long> finalHitStrategySet = new HashSet<>();
        tasks.forEach(t -> {
            try {
                FilterResult filterResult = t.get();
                switch (filterResult.getFilterTypeEnum()) {
                    case ByGroup:
                        crowdNotHitStrategyIdList.add(filterResult.getStrategyDo().getId());
                        break;
                    case ByLabel:
                        strategyId2HitResultMap.put(filterResult.getStrategyDo().getId(), filterResult.getHitResult());
                        labelNotHitStrategyIdList.add(filterResult.getStrategyDo().getId());
                        break;
                    case Passed:
                        finalHitStrategySet.add(filterResult.getStrategyDo().getId());
                        break;
                }
            } catch (Exception e) {
                log.error("AppBannerExecExecutor result error", e);
            }
        });

        List<OfflineDecisionRecordEntity> succeedDecisionList = new ArrayList<>();
        appBanner2StrategySetMap.forEach((appBannerId, strategySet) -> {
            List<Long> idSet = strategySet.stream().filter(finalHitStrategySet::contains).collect(Collectors.toList());
            boolean isHit = !idSet.isEmpty();

            AppBannerResp resp = new AppBannerResp();
            resp.setBannerId(appBannerId);
            resp.setResult(isHit);
            Map<String, String> strategyId2CouponIdMap = new HashMap<>();
            if (isHit) {
                for (Long strategyId : idSet) {
                    Long strategyChannelId = 0L;
                    StrategyMarketChannelDo strategyMarketChannelDo;
                    boolean dispatchRecordFlag = true; // 是否保留下发结果
                    boolean engineRecordFlag = false; // 是否引擎决策
                    String engineRecordDetail = ""; // 引擎下发明细
                    if (appBannerId2StrategyId2StrategyChannelMap.containsKey(appBannerId)) {
                        Map<Long, StrategyMarketChannelDo> subM = appBannerId2StrategyId2StrategyChannelMap.get(appBannerId);
                        if (subM.containsKey(strategyId)) {
                            strategyMarketChannelDo = subM.get(strategyId);
                            strategyChannelId = strategyMarketChannelDo.getId();
                            String extInfo = strategyMarketChannelDo.getExtInfo();
                            if (StringUtils.isNotBlank(extInfo)) {
                                AppBannerExtInfo appBannerExtInfo = JSONObject.parseObject(extInfo, AppBannerExtInfo.class);
                                if (StringUtils.isNotBlank(appBannerExtInfo.getActivityId())) {
                                    strategyId2CouponIdMap.put(strategyId.toString(), appBannerExtInfo.getActivityId());
                                }
                                // 是否还要再通过决策引擎判断
                                if (appBannerExtInfo.isUseEngine() && StringUtils.isNotBlank(appBannerExtInfo.getEngineCode())) {
                                    engineRecordFlag = true;
                                    Map<String, Object> paramsMap = Maps.newHashMap();
                                    paramsMap.put(REQUEST_PARAM_MOBILE, mobile);
                                    paramsMap.put(REQUEST_PARAM_APP, app);
                                    if (Objects.equals(queryType, 4)) { // 结清挽留弹窗-引擎决策
                                        paramsMap.put(REQUEST_PARAM_APP_BANNER_ID, appBannerId);
                                        paramsMap.put(REQUEST_PARAM_CUST_NO, settlementRequest.getCustNo());
                                        paramsMap.put(REQUEST_PARAM_ORDER_NO, settlementRequest.getOrderNo());

                                        Map detailInfo = appBannerEngineRepository.appBannerEnginePredictResult(appBannerId, appBannerExtInfo.getEngineCode(), userId, paramsMap, strategyId);
                                        engineRecordDetail = Objects.nonNull(detailInfo) ? JSONObject.toJSONString(detailInfo) : "";
                                        AppBannerEngineRepository.AppBannerEngineResult appBannerEngineResult = AppBannerEngineRepository.convertToEngineResult(detailInfo, queryType);
                                        if (appBannerEngineResult == null) {
                                            resp.setResult(Boolean.FALSE);
                                            dispatchRecordFlag = false;
                                        } else {
                                            AppBannerResp.SettlementResponse settlementResponse = new AppBannerResp.SettlementResponse();
                                            settlementResponse.setAppBannerId(appBannerEngineResult.getBannerId());
                                            settlementResponse.setActivityId(appBannerEngineResult.getActivityId());
                                            settlementResponse.setActivityName(appBannerEngineResult.getActivityName());
                                            resp.setSettlementResponse(settlementResponse);
                                        }
                                    } else if (Objects.equals(queryType, 3)) { // 借款挽留弹窗-引擎决策
                                        paramsMap.put(REQUEST_PARAM_CHOICE_TITLE, loanRetentionRequest.getChoiceTitle());
                                        paramsMap.put(REQUEST_PARAM_INSTALLMENTS, loanRetentionRequest.getInstallments());
                                        paramsMap.put(REQUEST_PARAM_AMOUNT, loanRetentionRequest.getAmount());

                                        Map detailInfo = appBannerEngineRepository.appBannerEnginePredictResult(appBannerId, appBannerExtInfo.getEngineCode(), userId, paramsMap, strategyId);
                                        engineRecordDetail = Objects.nonNull(detailInfo) ? JSONObject.toJSONString(detailInfo) : "";
                                        AppBannerEngineRepository.AppBannerEngineResult appBannerEngineResult = AppBannerEngineRepository.convertToEngineResult(detailInfo, queryType);
                                        if (appBannerEngineResult == null) {
                                            resp.setResult(Boolean.FALSE);
                                            dispatchRecordFlag = false;
                                        } else {
                                            Long bannerId = appBannerEngineResult.getBannerId();
                                            String activityId = appBannerEngineResult.getActivityId();
                                            AppBannerResp.LoanRetentionResponse loanRetentionResponse = new AppBannerResp.LoanRetentionResponse();
                                            loanRetentionResponse.setAppBannerId(bannerId);
                                            loanRetentionResponse.setActivityId(activityId);
                                            resp.setLoanRetentionResponse(loanRetentionResponse);
                                        }
                                    } else { // 实时提额-引擎决策
                                        AppBannerEngineRepository.AppBannerEngineResult appBannerEngineResult = appBannerEngineRepository.appBannerEnginePredict(appBannerId, appBannerExtInfo.getEngineCode(), userId, mobile);
                                        engineRecordDetail = Objects.nonNull(appBannerEngineResult) ? JSONObject.toJSONString(appBannerEngineResult) : "";
                                        if (appBannerEngineResult == null || !appBannerEngineResult.isPopup()) { // 引擎决策不弹窗
                                            resp.setResult(Boolean.FALSE);
                                            dispatchRecordFlag = false;
                                        } else { // 引擎决策弹窗
                                            resp.setBeforeIncreaseAmount(appBannerEngineResult.getBeforeIncreaseAmount());
                                            resp.setAfterIncreaseAmount(appBannerEngineResult.getAfterIncreaseAmount());
                                            AppBannerResp.EngineIncreaseInfo engineIncreaseInfo = new AppBannerResp.EngineIncreaseInfo();
                                            engineIncreaseInfo.setIncreaseType(appBannerEngineResult.getIncreaseType());
                                            engineIncreaseInfo.setIncreaseAmount(appBannerEngineResult.getIncreaseAmount());
                                            engineIncreaseInfo.setIncreaseDays(appBannerEngineResult.getIncreaseDays());
                                            resp.setEngineIncreaseInfo(engineIncreaseInfo);
                                        }
                                    }
                                }
                            }
                        }
                    }

                    StrategyGroupDo groupDo = null;
                    if (appBannerId2StrategyId2StrategyGroupMap.containsKey(appBannerId)) {
                        Map<Long, StrategyGroupDo> subM = appBannerId2StrategyId2StrategyGroupMap.get(appBannerId);
                        if (subM.containsKey(strategyId)) {
                            groupDo = subM.get(strategyId);
                        }
                    }
                    String unionId = IdUtil.fastSimpleUUID();
                    if (dispatchRecordFlag) {
                        UserDispatchDetailDo dispatchDetail = makeUserDispatchDetailDo(userId, mobile, appBannerId, strategyId, strategyChannelId, groupDo, triggerDatetime, unionId);
                        dispatchDetailDoList.add(dispatchDetail);
                    }

                    OfflineDecisionRecordEntity recordEntity;
                    if (engineRecordFlag) { // 决策记录-引擎弹窗
                        recordEntity = makeEngineDecisionRecordEntity(userId, strategyId, strategyChannelId, tableNumber, triggerDatetime, engineRecordDetail, unionId);
                    } else { // 决策记录-普通弹窗
                        recordEntity = makeSucceedOfflineDecisionRecordEntity(userId, strategyId, strategyChannelId, strategyId2HitResultMap.getOrDefault(strategyId, null), tableNumber, triggerDatetime, unionId);
                    }
                    succeedDecisionList.add(recordEntity);
                }
            }
            resp.setStrategyIdList(idSet.stream().map(Object::toString).collect(Collectors.toList()));
            resp.setStrategyId2CouponIdMap(strategyId2CouponIdMap);
            respList.add(resp);
        });

        // AOP代理调用必须通过实例调用
        appBannerLogService.saveLogs(userId,
                appBannerId2StrategyListMap,
                appBannerId2StrategyId2StrategyChannelMap,
                strategyId2HitResultMap,
                crowdNotHitStrategyIdList, labelNotHitStrategyIdList, abGroupNotHitStrategyIdList,
                succeedDecisionList,
                dispatchDetailDoList,
                triggerDatetime, tableNumber);

        return respList;
    }

    /**
     * TODO bizType
     *
     * @param userId
     * @param mobile
     * @param appBannerId
     * @param strategyId
     * @param strategyChannelId
     * @param groupDo
     * @param triggerDatetime
     * @return
     */
    private UserDispatchDetailDo makeUserDispatchDetailDo(Long userId, String mobile, String appBannerId,
                                                          Long strategyId, Long strategyChannelId, StrategyGroupDo groupDo,
                                                          LocalDateTime triggerDatetime, String unionId) {
        UserDispatchDetailDo dispatchDetail = new UserDispatchDetailDo();
        dispatchDetail.setUserId(userId);
        dispatchDetail.setBatchNum(serialNumberUtil.batchNum());
        dispatchDetail.setStrategyId(strategyId);
        dispatchDetail.setBizType(getBizType(strategyId));
        dispatchDetail.setStrategyChannelId(strategyChannelId);
        if (Objects.nonNull(groupDo)) {
            dispatchDetail.setStrategyGroupId(groupDo.getId());
            dispatchDetail.setStrategyGroupName(StringUtils.isNotEmpty(groupDo.getName()) ? groupDo.getName() : "A组");
        }
        dispatchDetail.setMarketChannel(StrategyMarketChannelEnum.APP_BANNER.getCode());
        dispatchDetail.setDispatchTime(triggerDatetime);
        dispatchDetail.setTriggerDatetime(triggerDatetime);
        dispatchDetail.setStrategyExecId("0");
        dispatchDetail.setStatus(1);
        dispatchDetail.setTemplateId(appBannerId);
        dispatchDetail.setUnionId(unionId); // 弹窗统一ID
        return dispatchDetail;
    }

    private static OfflineDecisionRecordEntity makeSucceedOfflineDecisionRecordEntity(Long userId, Long strategyId,
                                                                                      Long strategyChannelId,
                                                                                      HitResult hitResult,
                                                                                      String tableNumber,
                                                                                      LocalDateTime triggerDatetime,
                                                                                      String unionId) {
        OfflineDecisionRecordEntity recordEntity = new OfflineDecisionRecordEntity();
        recordEntity.setTraceId("app_banner");
        recordEntity.setStrategyId(strategyId);
        recordEntity.setDecisionResult(1);
//        recordEntity.setDecisionDetail(reason);
        recordEntity.setAppUserId(userId);
        recordEntity.setCreatedTime(triggerDatetime);
        recordEntity.setFailCode(DecisionResultEnum.NONE.getFailCode());
//        recordEntity.setFailReason(reason);
        recordEntity.setHitResultList(Collections.singletonList(hitResult));
        recordEntity.setMarketChannel(StrategyMarketChannelEnum.APP_BANNER.getCode());
        recordEntity.setMarketChannelId(strategyChannelId);
        recordEntity.setTableNameNo(tableNumber);
        recordEntity.setUnionId(unionId);
        recordEntity.setTableName("offline_decision_record_" + tableNumber);
        return recordEntity;
    }

    private static OfflineDecisionRecordEntity makeEngineDecisionRecordEntity(Long userId, Long strategyId, Long strategyChannelId,
                                                                              String tableNumber, LocalDateTime triggerDatetime,
                                                                              String engineRecordDetail, String unionId) {
        OfflineDecisionRecordEntity recordEntity = new OfflineDecisionRecordEntity();
        recordEntity.setTraceId("app_banner");
        recordEntity.setStrategyId(strategyId);
        recordEntity.setDecisionResult(StringUtils.isNotBlank(engineRecordDetail) ? 1 : 0);
        recordEntity.setDecisionDetail(engineRecordDetail);
        recordEntity.setAppUserId(userId);
        recordEntity.setCreatedTime(triggerDatetime);
        recordEntity.setFailCode(DecisionResultEnum.NONE.getFailCode());
//        recordEntity.setFailReason(reason);
        HitResult hitResult = new HitResult();
        hitResult.setHit(StringUtils.isNotBlank(engineRecordDetail));
        hitResult.setExpParam(engineRecordDetail);
        recordEntity.setHitResultList(Collections.singletonList(hitResult));
        recordEntity.setMarketChannel(StrategyMarketChannelEnum.APP_BANNER.getCode());
        recordEntity.setMarketChannelId(strategyChannelId);
        recordEntity.setTableNameNo(tableNumber);
        recordEntity.setUnionId(unionId);
        recordEntity.setTableName("offline_decision_record_" + tableNumber);
        return recordEntity;
    }

    /**
     * 第一层key为策略的id
     * 第二层key为渠道的模板id（可能是短信模板id，也可能是app资源位模板id）
     * 但是第二层key存在的前提是，当前用户命中了策略的一个分组，并且这个分组的营销渠道中有app资源位的。
     *
     * @param hitStrategySet
     * @param userId
     * @return
     */
    private Map<Long, Map<String, Pair<StrategyGroupDo, StrategyMarketChannelDo>>> buildAppBanner2ABGroupMapByStrategyId(Set<StrategyDo> hitStrategySet, Long userId) {
        Map<Long, Map<String, Pair<StrategyGroupDo, StrategyMarketChannelDo>>> m = new HashMap<>(hitStrategySet.size());
        hitStrategySet.forEach(strategyDo -> {
            Map<String, Pair<StrategyGroupDo, StrategyMarketChannelDo>> subM = new HashMap<>();
            m.put(strategyDo.getId(), subM);
            List<StrategyGroupDo> strategyGroupDos = mStrategyId2StrategyGroupListMap.get(strategyDo.getId());
            if (strategyGroupDos == null || strategyGroupDos.isEmpty()) {
                log.warn("[app资源位] 策略{}没有任何分组", strategyDo.getId());
                return;
            }

            if (StrategyAbTestEnum.getInstance(strategyDo.getAbTest()) == StrategyAbTestEnum.NO) {  //无分组
                StrategyGroupDo strategyGroupDo = strategyGroupDos.get(0);
                List<StrategyMarketChannelDo> strategyMarketChannelDos =
                        mStrategyGroupId2MarkeetChannelListMap.get(strategyGroupDo.getId());
                if (strategyMarketChannelDos != null) {
                    strategyMarketChannelDos.forEach(strategyMarketChannelDo -> {
                        if (strategyMarketChannelDo.getMarketChannel() == StrategyMarketChannelEnum.APP_BANNER.getCode()) {
                            subM.put(strategyMarketChannelDo.getTemplateId(), Pair.of(strategyGroupDo, strategyMarketChannelDo));
                        }
                    });
                }
            } else {    //有分组
                StrategyGroupDo abGroup = findABGroup(strategyDo, userId, strategyGroupDos);
                if (abGroup != null) {
                    List<StrategyMarketChannelDo> strategyMarketChannelDoList =
                            mStrategyGroupId2MarkeetChannelListMap.get(abGroup.getId());
                    if (strategyMarketChannelDoList != null) {
                        strategyMarketChannelDoList.forEach(strategyMarketChannelDo -> {
                            if (strategyMarketChannelDo.getMarketChannel() == StrategyMarketChannelEnum.APP_BANNER.getCode()) {
                                subM.put(strategyMarketChannelDo.getTemplateId(), Pair.of(abGroup, strategyMarketChannelDo));
                            }
                        });
                    }
                } else {
                    log.warn("[app资源位] 用户:{}没有命中策略:{}的任何分组", userId, strategyDo.getId());
                }
            }
        });
        return m;
    }

    private StrategyGroupDo findABGroup(StrategyDo strategyDo, Long userId, List<StrategyGroupDo> strategyGroupDoList) {
        String abNum = getAbNum(strategyDo, userId);

        CrowdDetailDo crowdDetail = new CrowdDetailDo();
        crowdDetail.setUserId(userId);
        crowdDetail.setAbNum(abNum);
        crowdDetail.setAppUserIdLast2((int) (userId % 100));

        return strategyGroupDoList.stream()
                .filter(strategyGroupDo -> {
                    BiPredicate<String, Integer> matchFunc =
                            strategyGroupDo.match(StrategyGroupTypeEnum.getInstance(strategyDo.getAbType()));
                    List<CrowdDetailDo> matchList =
                            strategyGroupService.matchGroupRule(strategyDo.getBizKey(), matchFunc, Collections.singletonList(crowdDetail));
                    return !matchList.isEmpty();
                }).findFirst().orElse(null);
    }

    private String getAbNum(StrategyDo strategyDo, Long userId) {
        StrategyGroupTypeEnum abType = StrategyGroupTypeEnum.getInstance(strategyDo.getAbType());
        String abNum = "";
        if (abType == StrategyGroupTypeEnum.NEW_RANDOM) {   //随机数组件
            abNum = getRandomNumber(strategyDo.getBizKey(), userId);
        } else { //原随机数
            UserInfoResp userInfo = userCenterClient.getUserByUserId(userId);
            UserAbNumResp userAbNumResp = userCenterClient.getAbNum(Collections.singletonList(userInfo.getMobile()));
            if (!userAbNumResp.getAbNums().isEmpty()) {
                abNum = userAbNumResp.getAbNums().get(0).getAbNum();
            }
        }
        return abNum;
    }

    private String getRandomNumber(String bizKey, Long userId) {
        try {
            AbBO abBO = AbClient.ab(bizKey, String.valueOf(userId), true);
            String randomNumber = String.valueOf(abBO.getRandomNum());
            log.info("获取随机数成功,方法名:ab,场景值:{},用户ID:{},随机数:{}", bizKey, userId, randomNumber);
            return randomNumber;
        } catch (IllegalArgumentException e) {
            log.error("获取随机数异常,参数异常,场景值:{}", bizKey, e);
            return "";
        } catch (Exception e) {
            log.error("获取随机数异常,场景值:{},用户ID:{}", bizKey, userId, e);
            return "";
        }
    }

    private boolean checkCrowdPacks(StrategyDo strategyDo, Long userId) {
        //没有人群包，直接通过
        if (!mStrategyId2CrowdPackListMap.containsKey(strategyDo.getId()))
            return true;

        List<CrowdPackDo> crowdPackDos = mStrategyId2CrowdPackListMap.get(strategyDo.getId());
        if (crowdPackDos == null)
            return true;

        return checkInCrowdPacks(userId, crowdPackDos);
    }

    private HitResult checkStrategyLabels(StrategyDo strategyDo, Long userId, String mobile, String app) {
        HitResult hitResult = new HitResult();
        hitResult.setHit(false);

        Map<String, List<StrategyMarketEventConditionDo>> labelNameToList =
                strategyMarketEventConditionService.getStringToListByStrategyId(strategyDo.getId());
        if (labelNameToList.isEmpty()) {
            hitResult.setHit(true);
            return hitResult;
        }

        List<StrategyMarketEventConditionDo> list = labelNameToList.values().stream()
                .flatMap(List::stream)
                .collect(Collectors.toList());

        String expression = list.stream()
                .map(StrategyMarketEventConditionDo::getExpression)
                .collect(Collectors.joining(" && "));
        hitResult.setExpParam(expression);

        if (StringUtils.isBlank(expression)) {
            hitResult.setHit(true);
            return hitResult;
        }


        Map<Long, Map<String, Object>> labelValueMap = queryLabels(userId, mobile, app, strategyDo.getId(), labelNameToList);
        if (labelValueMap != null) {
            Map<String, Object> m = labelValueMap.get(userId);
            hitResult.setExpParam(m.toString());
            Boolean result;
            try {
                result = AviatorUtil.compute(expression, m);
                if (!result) {
                    hitResult.setHit(false);
                    return hitResult;
                }
            } catch (Exception e) {
                log.error("AviatorUtil.compute, expression={}, paramMap={}", expression, JsonUtil.toJson(m), e);
            }
        }
        hitResult.setHit(true);
        return hitResult;
    }

    private boolean checkInCrowdPacks(Long userId, List<CrowdPackDo> crowdPackDos) {
        // 通过洞察平台判断
        List<Long> crowdPackIds = crowdPackDos.stream().map(CrowdPackDo::getId).collect(Collectors.toList());
        if (crowdInfoService.isCheckByInsightPlatform(crowdPackIds)) {
            return crowdInfoService.checkByInsightPlatform(crowdPackIds, userId).getLeft();
        }

        //参考AbstractStrategyEventDispatchService.crowdPackVerify
        List<Triple<Long, Long, List<String>>> triples = crowdPackService.getExecLogIdAndTablePairList(
                crowdPackDos.stream().map(CrowdPackDo::getId).collect(Collectors.toList()),
                MapUtils.listToAttributeMap(crowdPackDos, CrowdPackDo::getId, crowdPackDo -> {
                    CrowdContext crowdContext = new CrowdContext();
                    crowdContext.setCrowdPack(crowdPackDo);
                    return crowdContext;
                }));
        if (triples.isEmpty()) {
            return false;
        }

        return triples.stream().anyMatch(triple -> triple.getRight().stream().anyMatch(
                tableName -> crowdPackService
                        .hasUserRecord(triple.getLeft(), userId, triple.getMiddle(), tableName)
                        .isPresent()
        ));
    }

    public String getBizType(Long strategyId) {
        StrategyDo strategyDo = cacheStrategyService.selectById(strategyId);
        if (strategyDo != null) {
            return strategyDo.getBusinessType();
        }
        return "";
    }

    private Map<Long, Map<String, Object>> queryLabels(Long appUserId, String mobile, String app,
                                                       Long strategyId,
                                                       Map<String, List<StrategyMarketEventConditionDo>> labelNameToList) {
//        Map<String, List<StrategyMarketEventConditionDo>> labelNameToList = strategyMarketEventConditionService.getStringToListByStrategyId(strategyId);
        if (CollectionUtils.isEmpty(labelNameToList))
            return null;

        // 截止本次营销前
        List<StrategyMarketEventConditionDo> list = labelNameToList.values().stream().flatMap(List::stream).filter(item -> Objects.nonNull(item.getTimeType())).collect(Collectors.toList());
        String startTime = getStartTime(CollectionUtils.isEmpty(list) ? null : list.get(0));

        BatchAdsLabelVO adsLabelVO = new BatchAdsLabelVO();
        adsLabelVO.setStrategyId(strategyId);
        adsLabelVO.setApp(app);
        adsLabelVO.setStartTime(startTime);

        BatchAdsLabelVO.UserInfo userInfo = new BatchAdsLabelVO.UserInfo();
        userInfo.setMobile(mobile);
        userInfo.setAppUserId(appUserId);

        adsLabelVO.setUserInfoList(Collections.singletonList(userInfo));
        return adsStrategyLabelService.queryBatch(adsLabelVO, labelNameToList.keySet(), StrategyInstantLabelTypeEnum.LABEL, StrategyTypeEnum.OFFLINE_STRATEGY);
    }

    private String getStartTime(StrategyMarketEventConditionDo eventCondition) {
        LocalDateTime startTime = LocalDateTime.now();
        if (Objects.nonNull(eventCondition)) {
            switch (Optional.ofNullable(eventCondition.getTimeType()).orElse(3)) {
                case 1:
                    startTime = startTime.plusMinutes(-eventCondition.getTimeValue());
                    break;
                case 2:
                    startTime = startTime.plusHours(-eventCondition.getTimeValue());
                    break;
                default:
                    startTime = LocalDate.now().atStartOfDay();
            }
        }
        return LocalDateTimeUtil.format(startTime, TimeFormat.DATE_TIME);
    }


    enum FilterTypeEnum {
        None,
        Passed,
        ByCrowd,
        ByLabel,
        ByGroup
    }

    @Data
    static class FilterResult {
        private FilterTypeEnum filterTypeEnum;
        private StrategyDo strategyDo;
        private HitResult hitResult;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    static class AppBannerExtInfo {
        /**
         * 活动ID
         */
        private String activityId;
        /**
         * 是否走引擎决策
         */
        private boolean useEngine;
        /**
         * 引擎规则code
         */
        private String engineCode;
    }

    /**
     * 弹窗推荐mock数据
     */
    private List<AppBannerResp> mockAppBannerResp(AppBannerReq appBannerReq) {
        List<AppBannerResp> appBannerRespList = Lists.newArrayList();
        String mockDataAppBanner = ApolloUtil.getAppProperty("mock.data.appBanner", "{}");
        List<String> appBannerIdList = appBannerReq.getAppBannerIdList();
        appBannerIdList.forEach(appBannerId -> {
            AppBannerResp appBannerResp = JSONObject.parseObject(mockDataAppBanner, AppBannerResp.class);
            appBannerResp.setBannerId(appBannerId);
            appBannerRespList.add(appBannerResp);
        });
        return appBannerRespList;
    }

}