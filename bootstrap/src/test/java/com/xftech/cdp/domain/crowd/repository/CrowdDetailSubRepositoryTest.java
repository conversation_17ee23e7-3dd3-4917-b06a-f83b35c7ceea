package com.xftech.cdp.domain.crowd.repository;

import com.xftech.cdp.BaseTest;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class CrowdDetailSubRepositoryTest extends BaseTest {

    @Autowired
    private CrowdDetailSubRepository crowdDetailSubRepository;

    @Test
    public void saveBatch() {
        List<CrowdDetailDo> list = new ArrayList<>();
        CrowdDetailDo crowdDetailDo = new CrowdDetailDo();
        crowdDetailDo.setCrowdId(1L);
        crowdDetailDo.setApp("xyf01");
        crowdDetailDo.setMobile("***********");
        crowdDetailDo.setAbNum("11");
        crowdDetailDo.setCrowdExecLogId(1L);
        crowdDetailDo.setInnerApp("xyf01");
        crowdDetailDo.setRegisterTime(LocalDateTime.now());
        crowdDetailDo.setAppUserIdLast2(12);
        crowdDetailDo.setCreatedOp("xx");
        crowdDetailDo.setUpdatedOp("xx");
        crowdDetailDo.setUserId(1L);
        crowdDetailDo.setDFlag(0);
        list.add(crowdDetailDo);
//        crowdDetailSubRepository.saveBatch(1, list);

        crowdDetailSubRepository.batchSaveCrowdDetailSub(1, list);
    }

    @Test
    public void selectTableLastRecord() {
    }

    @Test
    public void countCrowdDetail() {
    }

    @Test
    public void truncateTable() {
    }
}