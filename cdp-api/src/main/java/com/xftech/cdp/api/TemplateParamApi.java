package com.xftech.cdp.api;


import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.base.Response;
import com.xftech.cdp.api.dto.req.param.TemplateParamCreateReq;
import com.xftech.cdp.api.dto.req.param.TemplateParamDetailReq;
import com.xftech.cdp.api.dto.req.param.TemplateParamListReq;
import com.xftech.cdp.api.dto.resp.param.TemplateParamListResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 */
@Api(tags = "模板参数管理")
@RequestMapping("/tpl/param")
public interface TemplateParamApi {
    @ApiOperation("新建参数")
    @PostMapping("/create")
    Response<Boolean> create(@Validated @RequestBody TemplateParamCreateReq templateParamCreateReq);

    @ApiOperation("更新参数")
    @PostMapping("/update")
    Response<Boolean> update(@Validated @RequestBody TemplateParamCreateReq templateParamCreateReq);

    @ApiOperation("参数列表")
    @PostMapping("/list")
    Response<PageResultResponse<TemplateParamListResp>> list(@Validated @RequestBody TemplateParamListReq templateParamListReq);

    @ApiOperation("获取参数详情")
    @PostMapping("/detail")
    Response<TemplateParamListResp> getDetail(@Validated @RequestBody TemplateParamDetailReq templateParamDetailReq);
}
