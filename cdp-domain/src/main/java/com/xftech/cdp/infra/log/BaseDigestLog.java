package com.xftech.cdp.infra.log;

import java.util.Date;
import java.util.Map;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0 2025/2/25
 * @description 摘要日志基类, 参考https://alidocs.dingtalk.com/i/nodes/N7dx2rn0JbZQ3PNAHmb2bjAMJMGjLRb3?cid=63696417222&corpId=dingba457475007182e135c2f4657eb6378f&doc_type=wiki_doc&iframeQuery=utm_medium=im_card&utm_source=im&utm_medium=dingdoc_doc_plugin_card&utm_scene=team_space&utm_source=dingdoc_doc
 */
@Data
public class BaseDigestLog {

    /**
     * 链路跟踪ID
     */
    private String traceId;

    /**
     * 摘要日志时间，格式为：%d{yyyy-MM-dd HH:mm:ss.SSS}
     */
    private String time = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss:SSS");

    /**
     * 客户端应用名称
     */
    private String clientName = "xyf-cdp";

    /**
     * 客户端IP
     */
    private String clientIp;

    /**
     * 客户端线程
     */
    private String thread = Thread.currentThread().getName();

    /**
     * 服务端应用名称
     */
    private String applicationName;

    /**
     * 调用结果 Y/N
     */
    private boolean invokeResult;

    /**
     * 调用结果码
     */
    private String invokeCode;

    /**
     * 调用耗时 单位为：ms
     */
    private long elapseTime = -1L;

    /**
     * 业务摘要信息 Map格式
     */
    private Map<String, Object> bizInfoMap;

    /**
     * 业务摘要信息 json字符串格式
     */
    private JSON bizInfo;
}
