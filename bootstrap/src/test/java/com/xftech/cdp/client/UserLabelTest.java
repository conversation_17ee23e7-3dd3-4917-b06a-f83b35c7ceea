package com.xftech.cdp.client;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.xftech.cdp.BaseTest;
import com.xftech.cdp.domain.ads.service.AdsStrategyLabelService;
import com.xftech.cdp.domain.flowctrl.model.enums.StrategyTypeEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyInstantLabelTypeEnum;
import com.xftech.cdp.domain.strategy.vo.BatchAdsLabelVO;
import com.xftech.cdp.infra.utils.JsonUtil;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2023/2/16
 */
public class UserLabelTest extends BaseTest {

    @Autowired
    private AdsStrategyLabelService adsStrategyLabelService;

    @Test
    public void queryUserLabels() {
        Long strategyId = 149L;
        String app = "xyf01";
        LocalDateTime start = LocalDate.now().atStartOfDay();
        String mobile = "13701269296";
        Long appUserId = 1109792134L;
        BatchAdsLabelVO adsLabelVO = new BatchAdsLabelVO();
        adsLabelVO.setStrategyId(strategyId);
        adsLabelVO.setApp(app);
        String startTime = LocalDateTimeUtil.format(start, "yyyy-MM-dd HH:mm:ss");
        adsLabelVO.setStartTime(startTime);
        System.out.println(startTime);

        BatchAdsLabelVO.UserInfo userInfo = new BatchAdsLabelVO.UserInfo();
        userInfo.setMobile(mobile);
        userInfo.setAppUserId(appUserId);

        adsLabelVO.setUserInfoList(Collections.singletonList(userInfo));

        List<String> labelNames = new ArrayList<>();
        labelNames.add("not_start_borrow");
        labelNames.add("user_cur_available_balance");
        Map<Long, Map<String, Object>> longMapMap =
                adsStrategyLabelService.queryBatch(adsLabelVO, labelNames, StrategyInstantLabelTypeEnum.LABEL, StrategyTypeEnum.REALTIME_STRATEGY);
        longMapMap.forEach((k,v) -> {
            System.out.printf("%s:\n", k);
            v.forEach((sk, sv) -> {
                System.out.printf("\t%s\t->%s\n", sk, sv);
            });
        });
    }
    @Test
    public void labelMinValueCheck() {
        HashMap<String, Object> stringStringHashMap = new HashMap<>();
        stringStringHashMap.put("available_amt_appuser_id",99.99);
        stringStringHashMap.put("last_increase_temporary_amount",0);
        stringStringHashMap.put("user_cur_available_balance",500);
        stringStringHashMap.put("user_cur_balance_change",80);
        adsStrategyLabelService.filterLabelMinValue(stringStringHashMap,1L,1L);
        System.out.println(JsonUtil.toJson(stringStringHashMap));
    }
}
