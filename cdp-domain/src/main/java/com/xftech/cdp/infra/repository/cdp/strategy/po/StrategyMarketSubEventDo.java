package com.xftech.cdp.infra.repository.cdp.strategy.po;

import com.xftech.cdp.infra.repository.Do;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 策略营销子事件表
 *
 * @TableName strategy_market_sub_event
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class StrategyMarketSubEventDo extends Do {

    /**
     * 策略ID
     */
    private Long strategyId;

    /**
     * 策略营销节点ID
     */
    private Long marketEventId;

    /**
     * 事件英文名
     */
    private String eventName;

    /**
     * 条件操作符，eq、ge...
     */
    private String operateType;

    /**
     * 标签值，多个逗号分隔
     */
    private String eventValue;

    /**
     * 表达式
     */
    private String expression;

    /**
     * 关系，1-且 2-或
     */
    private Integer relationship;

    /**
     * 层级，1，2，3，n
     */
    private Integer level;

    /**
     * 上一级ID，如果没有上一级为-1
     */
    private Integer parentId;

    private Integer eventType;

    private static final long serialVersionUID = 1L;
}