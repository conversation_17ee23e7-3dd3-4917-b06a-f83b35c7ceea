package com.xftech.cdp.domain.cache;

import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyGroupDo;

import java.util.List;

/**
 * 策略分组信息表操作
 */
public interface CacheStrategyGroupService {
    /**
     * 根据策略id删除分组信息记录
     *
     * @param strategyId 策略id
     */
    void deleteByStrategyId(Long strategyId);

    /**
     * 根据策略id查询分组信息记录
     *
     * @param strategyId 策略id
     * @return 该策略下的分组信息列表
     */
    List<StrategyGroupDo> selectListByStrategyId(Long strategyId);

    /**
     * 根据分组id（主键）查询某条记录
     *
     * @param strategyGroupId 分组id
     * @return 该id对应的一条记录
     */
    StrategyGroupDo selectById(Long strategyGroupId);

    /**
     * 插入一条分组信息记录
     *
     * @param strategyGroupDo 分组信息记录
     */
    void insert(StrategyGroupDo strategyGroupDo);

    /**
     * 根据主键id更新分组信息记录
     *
     * @param strategyGroupDo 分组信息记录
     */
    void update(StrategyGroupDo strategyGroupDo);

    /**
     * 根据主键id更新分组信息记录
     *
     * @param ids id列表
     */
    void deleteBatch(List<Long> ids);

    List<StrategyGroupDo> updateStrategyGroupCache(Long strategyId);

}
