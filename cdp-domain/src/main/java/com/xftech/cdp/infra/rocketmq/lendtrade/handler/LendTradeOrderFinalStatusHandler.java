/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.rocketmq.lendtrade.handler;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;

import javax.annotation.Resource;

import com.xftech.cdp.domain.mq.MqConsumeService;
import com.xftech.cdp.infra.aviator.util.DateTimeUtil;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventMessageVO;
import com.xftech.cdp.infra.rocketmq.MessageHandler;
import com.xftech.cdp.infra.rocketmq.dto.lendtrade.LendTradeOrderFinalStatusVO;

import com.alibaba.fastjson.JSONObject;
import com.xftech.cdp.infra.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;


@Slf4j
@Component
public class LendTradeOrderFinalStatusHandler implements MessageHandler {

    @Resource
    private MqConsumeService mqConsumeService;

    @Override
    public String getTopic() {
        return "tp_lendtrade_order_final_status";
    }

    @Override
    public boolean execute(String messageId, Object message) {
        LendTradeOrderFinalStatusVO messageVO = JSONObject.parseObject(message.toString(), LendTradeOrderFinalStatusVO.class);
        BizEventMessageVO bizEventMessageVO = transform(messageVO);
        if (bizEventMessageVO == null) {
            return false;
        }
        bizEventMessageVO.setBizEventType("LendTradeOrder");
        mqConsumeService.bizEventProcess(messageId, bizEventMessageVO);
        return true;
    }

    private BizEventMessageVO transform(LendTradeOrderFinalStatusVO lendTradeOrderFinalStatusVO) {

        if (lendTradeOrderFinalStatusVO == null ||  StringUtils.isAnyBlank(lendTradeOrderFinalStatusVO.getOrderNumber()) || Objects.isNull(lendTradeOrderFinalStatusVO.getUserNo())
        || StringUtils.isAnyBlank(lendTradeOrderFinalStatusVO.getLoanAmt()) || Objects.isNull(lendTradeOrderFinalStatusVO.getTerm()) || StringUtils.isAnyBlank(lendTradeOrderFinalStatusVO.getRiskPriceType())
        || Objects.isNull(lendTradeOrderFinalStatusVO.getRiskPrice())  ) {
            return null;
        }

        BizEventMessageVO bizEventMessageVO = new BizEventMessageVO();
        bizEventMessageVO.setAppUserId(lendTradeOrderFinalStatusVO.getUserNo());
        bizEventMessageVO.setApp(lendTradeOrderFinalStatusVO.getApp());
        bizEventMessageVO.setInnerApp(lendTradeOrderFinalStatusVO.getInnerApp());

        bizEventMessageVO.setTriggerDatetime(DateUtil.convertStr(new Date()));

        BizEventMessageVO.ExtrData extrData = new BizEventMessageVO.ExtrData();
        // 放款金额
        extrData.setAmount(new BigDecimal(Optional.ofNullable(lendTradeOrderFinalStatusVO.getLoanAmt()).orElse("0")));
        /* 放款渠道大类

         * 微信小程序: cnlPdCode = MPP001000068
         * APP: cnlPdCode != MPP001000068 && app = innerApp
         * API: cnlPdCode != MPP001000068 && app != innerApp
         */
        if (StringUtils.equals(lendTradeOrderFinalStatusVO.getCnlPdCode(), "MPP001000068")) {
            extrData.setFund_source_type("wechat");
        } else if (!StringUtils.equals(lendTradeOrderFinalStatusVO.getCnlPdCode(), "MPP001000068") && StringUtils.equals(lendTradeOrderFinalStatusVO.getApp(), lendTradeOrderFinalStatusVO.getInnerApp())) {
            extrData.setFund_source_type("app");
        } else if (!StringUtils.equals(lendTradeOrderFinalStatusVO.getCnlPdCode(), "MPP001000068") && !StringUtils.equals(lendTradeOrderFinalStatusVO.getApp(), lendTradeOrderFinalStatusVO.getInnerApp())) {
            extrData.setFund_source_type("api");
        } else {
            extrData.setFund_source_type("other");
        }
        // 是否首贷 0=首贷, 1=复贷
        extrData.setIs_first_loan(Objects.equals(lendTradeOrderFinalStatusVO.getIsAgainLoan(), 0));
        // 端到端产品码
        extrData.setCnlPdCode(lendTradeOrderFinalStatusVO.getCnlPdCode());

        extrData.setCreateTime(lendTradeOrderFinalStatusVO.getCreateTime());
        extrData.setLoanSuccessTime(lendTradeOrderFinalStatusVO.getLoanSuccessTime());
        //订单状态
        extrData.setStatus(lendTradeOrderFinalStatusVO.getStatus());
        String failedCode = lendTradeOrderFinalStatusVO.getFailedCode();
        extrData.setFailedCode(failedCode);

        extrData.setTerm(lendTradeOrderFinalStatusVO.getTerm());
        extrData.setRiskPriceType(lendTradeOrderFinalStatusVO.getRiskPriceType());
        extrData.setRiskPrice(lendTradeOrderFinalStatusVO.getRiskPrice());
        extrData.setOrderNumber(lendTradeOrderFinalStatusVO.getOrderNumber());
        bizEventMessageVO.setExtrData(extrData);

        return bizEventMessageVO;
    }
}