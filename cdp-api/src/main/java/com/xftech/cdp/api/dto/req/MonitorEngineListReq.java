/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.req;

import com.xftech.cdp.api.dto.base.PageRequestDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version $ MonitorEngineListReq, v 0.1 2023/12/14 14:26 lingang.han Exp $
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MonitorEngineListReq extends PageRequestDto {

    @ApiModelProperty(value = "策略id")
    private Long strategyId;


    /**
     * 如果该字段为空，则表示查询实时数据统计
     */
    @ApiModelProperty(value = "策略id")
    private String strategyGroupId;

    @ApiModelProperty(value = "发送渠道")
    private Integer marketChannel;

    @ApiModelProperty(value = "模板编号")
    private String templateId;
}