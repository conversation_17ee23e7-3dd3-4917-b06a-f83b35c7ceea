package com.xftech.cdp.infra.client.sso.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xftech.cdp.api.dto.resp.auth.LoginUser;
import com.xftech.cdp.api.dto.resp.auth.Menu;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> wanghu
 * @since : 2022/8/2 15:28:19
 */
@Setter
@Getter
public class LoginDetail {

    @JsonProperty("user")
    @JSONField(name = "user")
    private LoginUser user;

//    @JsonProperty("api")
//    @JSO<PERSON>ield(name = "api")
//    private ApiOuter api;

    private Map<String, Object> api;

    @JsonProperty("menu")
    @JSONField(name = "menu")
    private List<Menu> menu;

    @JsonProperty("role_tag")
    @JSONField(name = "role_tag")
    private List<String> roleTag;

    @JsonProperty("role_id")
    @JSO<PERSON>ield(name = "role_id")
    private List<String> roleId;

    @JsonProperty("role_name")
    @JSONField(name = "role_name")
    private List<String> roleName;

    @JsonProperty("token")
    @JSONField(name = "token")
    private transient String token;

    @JsonProperty("api_all")
    @JSONField(name = "api_all")
    private List<String> apiAll;

}
