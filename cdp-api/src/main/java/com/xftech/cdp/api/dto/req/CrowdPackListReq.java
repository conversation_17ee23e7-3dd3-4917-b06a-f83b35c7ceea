package com.xftech.cdp.api.dto.req;

import com.xftech.cdp.api.dto.base.PageRequestDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2023-05-12
 */

@Data
public class CrowdPackListReq extends PageRequestDto {
    @ApiModelProperty(value = "人群名称")
    private String crowdName;

    @ApiModelProperty(value = "业务线 new-cust-新客  old-cust-老客")
    @NotBlank(message = "业务线不能为空")
    private String businessType;
}
