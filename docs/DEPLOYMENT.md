# Touch Service 部署指南

## 环境要求

### 基础环境
- **JDK**: 8+
- **<PERSON>ven**: 3.6+
- **Docker**: 20.10+
- **Kubernetes**: 1.20+

### 依赖服务
- **MySQL**: 8.0+
- **Redis**: 6.0+
- **RocketMQ**: 4.9+
- **Apollo**: 2.0+

## 本地开发环境

### 1. 环境准备

```bash
# 安装MySQL
docker run -d --name mysql \
  -p 3306:3306 \
  -e MYSQL_ROOT_PASSWORD=password \
  -e MYSQL_DATABASE=touch_service \
  mysql:8.0

# 安装Redis
docker run -d --name redis \
  -p 6379:6379 \
  redis:6.0

# 安装RocketMQ
docker run -d --name rocketmq-nameserver \
  -p 9876:9876 \
  apache/rocketmq:4.9.4 \
  sh mqnamesrv

docker run -d --name rocketmq-broker \
  -p 10909:10909 -p 10911:10911 \
  --link rocketmq-nameserver:namesrv \
  -e "NAMESRV_ADDR=namesrv:9876" \
  apache/rocketmq:4.9.4 \
  sh mqbroker -c /opt/rocketmq-4.9.4/conf/broker.conf
```

### 2. 数据库初始化

```bash
# 执行初始化脚本
mysql -h localhost -u root -p touch_service < sql/init.sql
```

### 3. 配置修改

编辑 `touch-starter/src/main/resources/application.yml`：

```yaml
spring:
  datasource:
    druid:
      url: *****************************************
      username: root
      password: password
  redis:
    host: localhost
    port: 6379

rocketmq:
  name-server: localhost:9876
```

### 4. 启动应用

```bash
# 编译项目
mvn clean compile

# 启动应用
mvn spring-boot:run -pl touch-starter
```

### 5. 验证启动

```bash
# 健康检查
curl http://localhost:8080/touch-service/actuator/health

# API测试
curl -X POST http://localhost:8080/touch-service/api/touch/send \
  -H "Content-Type: application/json" \
  -d '{
    "requestId": "test_001",
    "touchType": "REALTIME_NORMAL",
    "channel": "SMS",
    "strategyId": 1001,
    "userId": 123456
  }'
```

## Docker部署

### 1. 构建镜像

```bash
# 编译项目
mvn clean package -DskipTests

# 构建Docker镜像
docker build -f docker/Dockerfile -t touch-service:1.0.0 .
```

### 2. 运行容器

```bash
# 创建网络
docker network create touch-network

# 启动依赖服务
docker run -d --name mysql --network touch-network \
  -e MYSQL_ROOT_PASSWORD=password \
  -e MYSQL_DATABASE=touch_service \
  mysql:8.0

docker run -d --name redis --network touch-network \
  redis:6.0

# 启动应用
docker run -d --name touch-service --network touch-network \
  -p 8080:8080 \
  -e SPRING_PROFILES_ACTIVE=prod \
  -e DB_HOST=mysql \
  -e REDIS_HOST=redis \
  touch-service:1.0.0
```

### 3. Docker Compose部署

创建 `docker-compose.yml`：

```yaml
version: '3.8'
services:
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: touch_service
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql

  redis:
    image: redis:6.0
    ports:
      - "6379:6379"

  touch-service:
    image: touch-service:1.0.0
    ports:
      - "8080:8080"
    environment:
      SPRING_PROFILES_ACTIVE: prod
      DB_HOST: mysql
      REDIS_HOST: redis
    depends_on:
      - mysql
      - redis

volumes:
  mysql_data:
```

启动：
```bash
docker-compose up -d
```

## Kubernetes部署

### 1. 创建命名空间

```bash
kubectl create namespace touch
```

### 2. 部署依赖服务

```yaml
# mysql-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mysql
  namespace: touch
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mysql
  template:
    metadata:
      labels:
        app: mysql
    spec:
      containers:
      - name: mysql
        image: mysql:8.0
        env:
        - name: MYSQL_ROOT_PASSWORD
          value: "password"
        - name: MYSQL_DATABASE
          value: "touch_service"
        ports:
        - containerPort: 3306
        volumeMounts:
        - name: mysql-storage
          mountPath: /var/lib/mysql
      volumes:
      - name: mysql-storage
        emptyDir: {}

---
apiVersion: v1
kind: Service
metadata:
  name: mysql-service
  namespace: touch
spec:
  selector:
    app: mysql
  ports:
  - port: 3306
    targetPort: 3306
```

### 3. 部署应用

```bash
# 应用部署文件
kubectl apply -f k8s/deployment.yaml
```

### 4. 配置Ingress

```yaml
# ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: touch-service-ingress
  namespace: touch
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  rules:
  - host: touch-service.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: touch-service
            port:
              number: 8080
```

### 5. 配置HPA

```yaml
# hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: touch-service-hpa
  namespace: touch
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: touch-service
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

## 生产环境配置

### 1. 数据库配置

```yaml
spring:
  datasource:
    druid:
      url: ********************************************************************************
      username: ${DB_USERNAME}
      password: ${DB_PASSWORD}
      initial-size: 10
      min-idle: 10
      max-active: 50
      max-wait: 60000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
```

### 2. Redis集群配置

```yaml
spring:
  redis:
    cluster:
      nodes:
        - redis-node1:6379
        - redis-node2:6379
        - redis-node3:6379
      max-redirects: 3
    password: ${REDIS_PASSWORD}
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 20
        max-wait: -1ms
        max-idle: 10
        min-idle: 5
```

### 3. JVM参数优化

```bash
JAVA_OPTS="-Xms2g -Xmx4g \
  -XX:+UseG1GC \
  -XX:G1HeapRegionSize=16m \
  -XX:+UseStringDeduplication \
  -XX:+PrintGCDetails \
  -XX:+PrintGCTimeStamps \
  -Xloggc:/app/logs/gc.log \
  -XX:+HeapDumpOnOutOfMemoryError \
  -XX:HeapDumpPath=/app/logs/heapdump.hprof"
```

### 4. 监控配置

```yaml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
    tags:
      application: touch-service
      environment: prod
```

## 部署检查清单

### 部署前检查
- [ ] 依赖服务状态正常（MySQL、Redis、RocketMQ）
- [ ] 数据库初始化完成
- [ ] 配置文件正确
- [ ] 镜像构建成功
- [ ] 网络连通性测试

### 部署后检查
- [ ] 应用启动成功
- [ ] 健康检查通过
- [ ] API接口可访问
- [ ] 数据库连接正常
- [ ] 缓存连接正常
- [ ] 消息队列连接正常
- [ ] 监控指标正常
- [ ] 日志输出正常

### 性能测试
- [ ] 单个触达QPS测试
- [ ] 批量触达吞吐量测试
- [ ] 频控功能测试
- [ ] 故障恢复测试
- [ ] 压力测试

## 故障排查

### 常见问题

1. **应用启动失败**
   - 检查依赖服务状态
   - 检查配置文件
   - 查看启动日志

2. **数据库连接失败**
   - 检查数据库服务状态
   - 检查连接参数
   - 检查网络连通性

3. **Redis连接失败**
   - 检查Redis服务状态
   - 检查连接参数
   - 检查网络连通性

4. **API调用失败**
   - 检查应用状态
   - 检查请求参数
   - 查看应用日志

### 日志查看

```bash
# Docker环境
docker logs touch-service

# Kubernetes环境
kubectl logs -f deployment/touch-service -n touch

# 本地环境
tail -f logs/touch-service.log
```

### 监控指标

关键监控指标：
- 应用状态：UP/DOWN
- QPS：每秒请求数
- 响应时间：P95、P99
- 错误率：4xx、5xx错误比例
- 资源使用：CPU、内存、磁盘
- 依赖服务：数据库、缓存连接状态
