/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.feign.model.requset;

import com.xftech.cdp.api.dto.req.GoodsListReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version $ GoodsListRequest, v 0.1 2024/5/6 17:54 lingang.han Exp $
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GoodsListRequest {

    /**
     * 商品id
     */
    private Long goodsId;


    /**
     * 页数，默认值1
     */
    private Integer page;

    /**
     * 每页条目，默认值10
     */
    private Integer pageSize;

    public GoodsListRequest(GoodsListReq req) {
        this.goodsId = req.getGoodsId();
        this.page = req.getCurrent();
        this.pageSize = req.getSize();
    }
}