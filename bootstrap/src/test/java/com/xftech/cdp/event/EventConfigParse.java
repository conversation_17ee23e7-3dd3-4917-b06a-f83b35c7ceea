package com.xftech.cdp.event;

import cn.hutool.core.convert.Convert;
import com.xftech.cdp.domain.event.FieldConfigParseFactory;
import com.xftech.cdp.domain.event.model.config.FieldConfig;
import com.xftech.cdp.domain.event.model.constants.EventConfigConstants;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version $ EventConfigParse, v 0.1 2024/11/13 14:23 snail Exp $
 */
@Slf4j
public class EventConfigParse {
    public static void main(String[] args) {
        toYuan();
    }

    public static void toYuan(){
        String originVal = "10066";
        Long convertedVal = Convert.toLong(originVal);
        BigDecimal resultVal = BigDecimal.valueOf(convertedVal).divide(BigDecimal.valueOf(EventConfigConstants.YUAN_TO_FEN));
        System.out.println("result="+resultVal);
    }

    public static void toFen(){
        Double originVal = 100.66;
        BigDecimal convertedVal = Convert.toBigDecimal(originVal);
        BigDecimal resultVal = convertedVal.multiply(BigDecimal.valueOf(EventConfigConstants.YUAN_TO_FEN));
        System.out.println("result="+resultVal.longValue());
    }
    public static void convert(){
        String dateFormat = "2024/11/11 18:30:30";
        Date date = Convert.convert(Date.class,dateFormat,new Date());
        System.out.println("date="+date);
    }

    public static void configParse(){
        String config = "{\n" +
                "\"dataType\": \"int\",\n" +
                "\"mapping\": \"2_1\",\n" +
                "\"process\": \"mapping\"\n" +
                "}";

        FieldConfig mappingConfig = FieldConfigParseFactory.parse("mapping",config);
        log.info("mapping info={}",mappingConfig);
    }
}
