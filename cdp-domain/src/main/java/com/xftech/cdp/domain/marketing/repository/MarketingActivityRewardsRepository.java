package com.xftech.cdp.domain.marketing.repository;

import java.util.List;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.infra.repository.cdp.marketing.po.MarketingActivityRewardsDo;

import org.springframework.stereotype.Repository;


/**
 * 营销活动奖品表 marketing_activity_rewards dao
 */
@Repository
public class MarketingActivityRewardsRepository {

    /**
     * 根据活动ID查询活动奖品
     *
     * @param activityId 活动ID
     * @return
     */
    public List<MarketingActivityRewardsDo> selectByActivityId(Long activityId) {
        return DBUtil.selectList("marketingActivityRewards.selectByActivityId", activityId);
    }

}