# T0实时触达与离线触达频控流控机制分析报告

## 1. 概述

本报告详细分析T0实时触达和离线触达中涉及的频控（流控）机制，包括实现原理、使用差异、配置管理等方面的对比分析。

## 2. 频控类型分类

### 2.1 按控制层级分类

| 频控类型 | 控制粒度 | 应用场景 | 实现方式 |
|---------|---------|---------|---------|
| **事件级流控** | 用户+事件类型 | MQ消息处理入口 | Redis分布式锁 |
| **触达级流控** | 用户+渠道+策略 | 触达执行前检查 | 数据库查询+Redis锁 |
| **分布式流控** | 用户级别 | 并发触达控制 | Redis分布式锁 |
| **批量流控** | 批量用户 | 离线批处理 | 数据库批量查询 |

### 2.2 按触达方式分类

| 触达方式 | 频控机制 | 执行时机 | 特点 |
|---------|---------|---------|------|
| **T0-普通触达** | 单用户预检查 | dispatch()开始前 | 实时性强，失败即终止 |
| **T0-引擎触达** | 内置流控逻辑 | marketingSend()内部 | 渠道级控制 |
| **离线-普通触达** | 批量流控 | 批处理过程中 | 支持新旧切换，批量处理 |
| **离线-引擎触达** | 内置流控逻辑 | marketingSend()内部 | 与T0引擎策略相同 |

## 3. T0实时触达频控机制

### 3.1 事件级流控

#### 3.1.1 实现位置

**代码位置**: `cdp-domain/src/main/java/com/xftech/cdp/domain/mq/impl/MqConsumeServiceImpl.java`

```java
// 事件流控检查 - MQ消息处理入口
private boolean isReject(BizEventMessageVO bizEventMessageVO) {
    Long userId = Optional.ofNullable(bizEventMessageVO.getCreditUserId())
            .orElse(bizEventMessageVO.getUser_id());

    if (StringUtils.isNotEmpty(bizEventMessageVO.getBizEventType()) && userId != null && userId > 0) {
        EventFlcConfig eventFlcConfig = appConfigService.getEventFlcConfig();
        if (eventFlcConfig != null) {
            Integer limitSeconds = eventFlcConfig.getLimitSeconds(bizEventMessageVO.getBizEventType());
            if (limitSeconds != null && limitSeconds > 0) {
                String limitKey = String.format("eventflc:%s:%s", bizEventMessageVO.getBizEventType(), userId);
                boolean ret = redisUtils.lock(limitKey, "1", limitSeconds);
                if (!ret) {
                    return true; // 被流控拦截
                }
            }
        }
    }
    return false;
}
```

#### 3.1.2 流控配置

**代码位置**: `cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/model/dto/EventFlcConfig.java`

```java
@Data
public class EventFlcConfig {
    // 配置值， key:事件名, value: 多少秒1次;
    private Map<String, Integer> eventFlcMap;

    @JsonIgnore
    @JSONField(serialize = false)
    public Integer getLimitSeconds(String eventName) {
        if (eventFlcMap != null && eventFlcMap.containsKey(eventName)) {
            return eventFlcMap.get(eventName);
        }
        return null;
    }
}
```

#### 3.1.3 流控原理
- **Redis Key格式**: `eventflc:{事件类型}:{用户ID}`
- **流控机制**: 分布式锁，锁定时间为配置的限制秒数
- **拦截逻辑**: 在限制时间内，同一用户的同一事件类型只能处理一次

### 3.2 触达级流控

#### 3.2.1 实现位置

**代码位置**: `cdp-domain/src/main/java/com/xftech/cdp/domain/dispatch/DispatchFlcService.java`

```java
// T0普通触达频控检查
public boolean dispatchFlc(BizEventVO bizEventVO, CrowdDetailDo crowdDetail,
                          AbstractStrategyEventDispatchService abstractStrategyEventDispatchService) {
    StrategyMarketChannelEnum marketChannelEnum = StrategyMarketChannelEnum.getInstance(bizEventVO.getMarketChannel());
    boolean switchFlag = appConfigService.getSingleDispatchFlcSwitch(marketChannelEnum);
    if (!Objects.equals(true, switchFlag)) {
        return false; // 开关未打开，不进行流控
    }

    // 获取频控规则并执行检查
    StrategyMarketChannelDo channelDo = cacheStrategyMarketChannelService.selectById(bizEventVO.getMarketChannelId());
    List<Integer> sucStatus = Arrays.asList(-1, 1);
    List<CrowdDetailDo> crowdDetailList = abstractStrategyEventDispatchService.flowCtrl(
            bizEventVO.getMessageId(),
            Optional.ofNullable(bizEventVO.getTriggerDatetime()).orElse(LocalDateTime.now()),
            channelDo, crowdDetail, sucStatus, bizEventVO.getBizEventType());

    if (CollectionUtils.isEmpty(crowdDetailList)) {
        return true; // 被频控拦截
    }
    return false; // 通过频控检查
}
```

### 3.3 分布式流控

#### 3.3.1 分布式锁实现

**代码位置**: `cdp-domain/src/main/java/com/xftech/cdp/domain/dispatch/DispatchFlcService.java`

```java
// 分布式锁获取
private boolean tryLock(Long userId, String value) {
    boolean lockRet = false;
    final int lockSeconds = 60 * 6; // 6分钟超时
    final long sleepMills = appConfigService.getDispatchFlcLockPerWaitMills();
    String lockKey = getLockKey(userId);
    boolean isIgnore = appConfigService.getSingleIgnoreDispatchFlcLockSwitch();

    do {
        if (isIgnore) {
            return true; // 忽略锁检查
        }
        lockRet = redisUtils.lock(lockKey, value, lockSeconds);
        if (!lockRet) {
            ThreadUtil.sleep(sleepMills); // 等待重试
        }
    } while (!lockRet);
    return true;
}

// 分布式锁释放
private void unLock(Long userId, String value) {
    String lockKey = getLockKey(userId);
    if (StringUtils.equalsIgnoreCase(value, redisUtils.get(lockKey))) {
        redisUtils.delete(lockKey);
    }
}
```

## 4. 离线触达频控机制

### 4.1 批量流控核心逻辑

#### 4.1.1 流控入口

**代码位置**: `cdp-domain/src/main/java/com/xftech/cdp/domain/flowctrl/service/impl/FlowCtrlCoreServiceImpl.java`

```java
// 离线批量流控主入口
public List<CrowdDetailDo> flowCtrl(FlowCtrlDto flowCtrlDto, List<Integer> statusList) {
    if (CollectionUtils.isEmpty(flowCtrlDto.getList()) ||
        CollectionUtils.isEmpty(flowCtrlDto.getFlowCtrlRuleList())) {
        return flowCtrlDto.getList();
    }

    Long strategyId = flowCtrlDto.getMarketChannelDo().getStrategyId();
    List<Long> passUserIdList = new ArrayList<>();

    // 新旧流控切换逻辑
    if(WhitelistSwitchUtil.commonGraySwitchByApollo("" + strategyId, "newFlowCtrlSwitch")) {
        log.info("流控规则执行: 新流控 strategyId:{} marketChannel:{}",
                strategyId, flowCtrlDto.getMarketChannelDo().getMarketChannel());
        passUserIdList = this.newExecute(flowCtrlDto, statusList);
    } else {
        passUserIdList = this.execute(flowCtrlDto, statusList);
    }

    // 返回通过流控的用户
    Map<Long, CrowdDetailDo> detailMap = flowCtrlDto.getList().stream()
            .collect(Collectors.toMap(CrowdDetailDo::getUserId, item -> item));
    return passUserIdList.stream().map(detailMap::get).collect(Collectors.toList());
}
```

### 4.2 新旧流控差异

#### 4.2.1 新流控逻辑（按渠道独立控制）

**代码位置**: `cdp-domain/src/main/java/com/xftech/cdp/domain/flowctrl/service/impl/FlowCtrlCoreServiceImpl.java`

```java
// 新流控逻辑:策略维度按下发渠道进行单独控制
private List<Long> newExecute(FlowCtrlDto flowCtrlDto, List<Integer> statusList) {
    StrategyMarketChannelDo channelDo = flowCtrlDto.getMarketChannelDo();
    Integer marketChannel = channelDo.getMarketChannel();

    if (isNotFlowCtrlChannel(marketChannel)) {
        return Collections.emptyList(); // 不需要流控的渠道直接返回
    }

    List<Long> resultList = flowCtrlDto.getList().stream()
            .map(CrowdDetailDo::getUserId).collect(Collectors.toCollection(CopyOnWriteArrayList::new));

    for (FlowCtrlDo flowCtrlDo : flowCtrlDto.getFlowCtrlRuleList()) {
        List<ImmutablePair<Long, Long>> flowCtrlRefuseList = new ArrayList<>();
        if (CollectionUtils.isEmpty(resultList)) {
            continue;
        }

        // 查询用户触达指标（新版本按渠道查询）
        Long strategyId = flowCtrlDto.getMarketChannelDo().getStrategyId();
        List<UserDispatchIndexDto> dispatchIndexList = userDispatchDetailService
                .getUserIndexNew(flowCtrlDto.getTableNo(), Triple.of(strategyId, marketChannel, flowCtrlDo),
                               resultList, statusList);

        // 执行拦截逻辑
        dispatchIndexList = dispatchIndexList.stream()
                .filter(index -> this.interception(flowCtrlDo, index)).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(dispatchIndexList)) {
            for (UserDispatchIndexDto index : dispatchIndexList) {
                flowCtrlRefuseList.add(ImmutablePair.of(flowCtrlDo.getId(), index.getUserId()));
                resultList.remove(index.getUserId());
            }
            this.saveInterceptionLogNew(flowCtrlDto, flowCtrlRefuseList);
        }
    }
    return resultList;
}
```

#### 4.2.2 旧流控逻辑（跨策略流控）

**代码位置**: `cdp-domain/src/main/java/com/xftech/cdp/domain/flowctrl/service/impl/FlowCtrlCoreServiceImpl.java`

```java
// 旧流控逻辑：支持跨策略流控
private List<Long> execute(FlowCtrlDto flowCtrlDto, List<Integer> statusList) {
    List<Long> resultList = flowCtrlDto.getList().stream()
            .map(CrowdDetailDo::getUserId).collect(Collectors.toCollection(CopyOnWriteArrayList::new));

    for (FlowCtrlDo flowCtrlDo : flowCtrlDto.getFlowCtrlRuleList()) {
        List<ImmutablePair<Long, Long>> flowCtrlRefuseList = new ArrayList<>();
        if (CollectionUtils.isEmpty(resultList)) {
            continue;
        }

        // 查询用户触达指标（旧版本支持跨策略查询）
        Long strategyId = flowCtrlDto.getMarketChannelDo().getStrategyId();
        Integer marketChannel = flowCtrlDto.getMarketChannelDo().getMarketChannel();
        List<UserDispatchIndexDto> dispatchIndexList = userDispatchDetailService
                .getUserIndex(flowCtrlDto.getTableNo(), Triple.of(strategyId, marketChannel, flowCtrlDo),
                            resultList, statusList);

        // 执行拦截逻辑
        dispatchIndexList = dispatchIndexList.stream()
                .filter(index -> this.interception(flowCtrlDo, index)).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(dispatchIndexList)) {
            for (UserDispatchIndexDto index : dispatchIndexList) {
                flowCtrlRefuseList.add(ImmutablePair.of(flowCtrlDo.getId(), index.getUserId()));
                resultList.remove(index.getUserId());
            }
            this.saveInterceptionLog(flowCtrlDto, flowCtrlRefuseList);
        }
    }
    return resultList;
}
```

### 4.3 频控拦截判断逻辑

**代码位置**: `cdp-domain/src/main/java/com/xftech/cdp/domain/flowctrl/service/impl/FlowCtrlCoreServiceImpl.java`

```java
// 用户指标与流控配置比较
private boolean interception(FlowCtrlDo rule, UserDispatchIndexDto indexDto) {
    return Objects.nonNull(rule.getLimitTimes()) && indexDto.getCount() >= rule.getLimitTimes();
}
```

## 5. Apollo配置管理

### 5.1 频控开关配置

#### 5.1.1 触达级流控开关
```properties
# 复筛常规流控开关（按渠道配置）
singleDispatchFlc.1 = false  # 短信渠道频控开关
singleDispatchFlc.2 = false  # 电销渠道频控开关  
singleDispatchFlc.3 = false  # 优惠券渠道频控开关
singleDispatchFlc.4 = false  # Push渠道频控开关
singleDispatchFlc.5 = false  # Push渠道频控开关
```

#### 5.1.2 分布式锁流控开关
```properties
# 分布式锁流控开关（按渠道配置）
singleDispatchFlc.lock.1 = true   # 短信渠道分布式锁开关
singleDispatchFlc.lock.2 = true   # 电销渠道分布式锁开关
singleDispatchFlc.lock.3 = true   # 优惠券渠道分布式锁开关
singleDispatchFlc.lock.4 = true   # Push渠道分布式锁开关
singleDispatchFlc.lock.5 = true   # Push渠道分布式锁开关

# 分布式锁忽略开关（紧急情况使用）
singleDispatchFlc.lock.ignore = false

# 分布式锁等待时间配置
dispatchFlc.lock.perWaitMills = 50
```

### 5.2 事件流控配置
```properties
# 事件流控配置（JSON格式）
eventFlcConfig = {
    "Start": 600,                    # Start事件600秒内只能触发一次
    "Login": 600,                    # Login事件600秒内只能触发一次  
    "Start1IncreaseCreditLimit": 600, # 提额事件600秒内只能触发一次
    "RepaySuccess": 600              # 还款成功事件600秒内只能触发一次
}
```

### 5.3 新旧流控切换配置
```properties
# 新流控开关（按策略ID配置）
newFlowCtrlSwitch = ["策略ID1", "策略ID2", ...]
```

### 5.4 频控线程池配置
```properties
# 频控线程池配置
dispatchFlcExecutor.pool.coreSize = 8   # 核心线程数
dispatchFlcExecutor.pool.maxSize = 20   # 最大线程数
```

## 6. 频控机制对比分析

### 6.1 实现方式对比

| 对比维度 | T0实时触达 | 离线触达 | 差异说明 |
|---------|-----------|---------|---------|
| **流控粒度** | 单用户实时检查 | 批量用户检查 | T0注重实时性，离线注重批量效率 |
| **存储方式** | Redis分布式锁 | 数据库查询统计 | T0使用内存，离线使用持久化存储 |
| **执行时机** | 触达前预检查 | 批处理过程中 | T0是阻塞式，离线是过滤式 |
| **失败处理** | 立即终止 | 过滤继续 | T0失败即停，离线部分失败 |
| **配置管理** | Apollo实时配置 | 数据库规则配置 | T0配置灵活，离线规则复杂 |

### 6.2 性能特点对比

| 性能维度 | T0实时触达 | 离线触达 | 优劣分析 |
|---------|-----------|---------|---------|
| **响应时间** | 毫秒级 | 秒级 | T0响应快，离线处理慢 |
| **并发能力** | 受Redis性能限制 | 受数据库性能限制 | T0并发高，离线吞吐大 |
| **资源消耗** | 内存消耗大 | CPU和IO消耗大 | T0占内存，离线占计算资源 |
| **扩展性** | 水平扩展容易 | 垂直扩展为主 | T0扩展灵活，离线扩展复杂 |

### 6.3 适用场景对比

| 场景类型 | T0实时触达 | 离线触达 | 选择建议 |
|---------|-----------|---------|---------|
| **实时营销** | ✅ 适合 | ❌ 不适合 | 选择T0，响应及时 |
| **批量营销** | ❌ 效率低 | ✅ 适合 | 选择离线，成本低 |
| **高频触达** | ⚠️ 需要优化 | ✅ 适合 | 离线更稳定 |
| **个性化营销** | ✅ 适合 | ⚠️ 复杂度高 | T0实现简单 |

## 7. 频控规则配置

### 7.1 流控规则类型

**代码位置**: `cdp-domain/src/main/java/com/xftech/cdp/domain/flowctrl/model/enums/FlowCtrlTypeEnum.java`

```java
@Getter
public enum FlowCtrlTypeEnum {
    CHANNEL(2, 1, "渠道"),           // 渠道级流控
    STRATEGY(1, 2, "策略"),          // 策略级流控
    MULTI_STRATEGY(3, 3, "多策略共享"), // 多策略共享流控
    BIZ_LINE(4, 4, "业务线");        // 业务线级流控
}
```

### 7.2 流控规则数据结构

基于数据库表结构分析：

```sql
-- 流控规则表结构
CREATE TABLE flow_ctrl (
    id BIGINT PRIMARY KEY,
    name VARCHAR(255),              -- 规则名称
    description VARCHAR(500),       -- 规则描述
    status INT,                     -- 规则状态：1-启用，0-禁用
    type INT,                       -- 流控类型：1-策略，2-渠道，3-多策略，4-业务线
    strategy_type INT,              -- 策略类型
    effective_strategy VARCHAR(500), -- 生效策略范围
    effective_channel VARCHAR(500),  -- 生效渠道范围
    day_count INT,                  -- 日限制次数
    week_count INT,                 -- 周限制次数  
    month_count INT,                -- 月限制次数
    limit_days INT,                 -- 限制天数
    limit_times INT,                -- 限制次数
    biz_type VARCHAR(50),           -- 业务类型
    created_time DATETIME,          -- 创建时间
    updated_time DATETIME,          -- 更新时间
    d_flag TINYINT DEFAULT 0        -- 删除标记
);
```

## 8. 频控监控和日志

### 8.1 频控拦截日志

**代码位置**: `cdp-domain/src/main/java/com/xftech/cdp/domain/flowctrl/service/impl/FlowCtrlInterceptionLogServiceImpl.java`

```java
// 保存拦截日志
public void saveInterceptionLog(FlowCtrlDto flowCtrlDto, List<ImmutablePair<Long, Long>> flowCtrlRefuseList) {
    log.info("流控拦截用户数量：{}", flowCtrlRefuseList.size());
    if (CollectionUtils.isEmpty(flowCtrlRefuseList)) {
        return;
    }

    LocalDateTime now = LocalDateTime.now();
    List<FlowCtrlInterceptionLogDo> list = flowCtrlRefuseList.stream().map(pair -> {
        FlowCtrlInterceptionLogDo interceptionLogDo = new FlowCtrlInterceptionLogDo();
        interceptionLogDo.setFlowCtrlId(pair.getLeft());
        interceptionLogDo.setUserId(pair.getRight());
        interceptionLogDo.setStrategyId(flowCtrlDto.getMarketChannelDo().getStrategyId());
        interceptionLogDo.setCreatedTime(now);
        interceptionLogDo.setUpdatedTime(now);
        interceptionLogDo.setMarketChannel(flowCtrlDto.getMarketChannelDo().getMarketChannel());
        interceptionLogDo.setBizEventType(flowCtrlDto.getBizEventType());
        return interceptionLogDo;
    }).collect(Collectors.toList());

    statFlowCtrl(list, flowCtrlDto.getStrategyRulerEnum());
    flowCtrlInterceptionLogRepository.saveBatch(list);
}
```

### 8.2 监控指标

系统通过以下方式进行频控监控：

1. **拦截统计**: 记录每次频控拦截的用户数量和规则ID
2. **性能监控**: 记录频控执行耗时和成功率
3. **告警机制**: 频控拦截率过高时触发告警
4. **日志追踪**: 详细记录频控决策过程和结果

## 9. 总结与建议

### 9.1 频控机制总结

1. **T0实时触达**：采用Redis分布式锁实现，响应快速，适合实时场景
2. **离线触达**：采用数据库查询统计，支持复杂规则，适合批量场景
3. **配置管理**：通过Apollo实现动态配置，支持实时调整
4. **监控体系**：完善的日志记录和监控告警机制

### 9.2 优化建议

1. **性能优化**：
   - T0场景可考虑使用本地缓存减少Redis访问
   - 离线场景可优化数据库查询，使用分区表提升性能

2. **功能增强**：
   - 支持更灵活的频控规则配置
   - 增加频控规则的A/B测试能力
   - 完善频控效果的数据分析

3. **运维改进**：
   - 增加频控规则的可视化配置界面
   - 完善频控异常的自动恢复机制
   - 优化频控日志的查询和分析工具

### 9.3 架构演进方向

建议未来向统一的频控服务架构演进：
- 统一频控规则引擎
- 支持多种存储后端
- 提供标准化的频控API
- 实现频控规则的热更新机制
