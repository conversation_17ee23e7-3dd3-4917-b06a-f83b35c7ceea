package com.xftech.cdp.api.dto.resp;

import com.xftech.cdp.api.dto.base.PageResultResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class TeleNodeListResp extends BaseResp {

    private PageResultResponse<TeleNodeListResp.TeleNode> list;

    private List<TeleType> bizTypeList;

    private List<TeleType> nameTypeList;


    @Data
    public static class TeleNode {
        private String bizType;

        private String nameType;

        private Long nodeId;

        private String nodeName;
    }

    @Data
    public static class TeleType {

        private String name;

        private Integer value;
    }
}
