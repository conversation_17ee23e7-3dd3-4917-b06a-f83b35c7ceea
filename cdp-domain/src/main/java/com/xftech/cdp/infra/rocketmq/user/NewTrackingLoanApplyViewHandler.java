package com.xftech.cdp.infra.rocketmq.user;

import com.alibaba.fastjson.JSONObject;
import com.xftech.cdp.domain.mq.MqConsumeService;
import com.xftech.cdp.infra.client.usercenter.UserCenterClient;
import com.xftech.cdp.infra.client.usercenter.model.UserInfoResp;
import com.xftech.cdp.infra.config.LogUtil;
import com.xftech.cdp.infra.constant.Constants;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventMessageVO;
import com.xftech.cdp.infra.rocketmq.MessageHandler;
import com.xftech.cdp.infra.rocketmq.dto.NewTrackingLoanApplyViewVO;
import com.xftech.cdp.infra.utils.DateUtil;
import com.xyf.cis.SecureClient;
import com.xyf.cis.dto.SecureEncryptDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ NewTrackingLoanApplyViewHandler, v 0.1 2025/3/25 17:42 tianshuo.qiu Exp $
 */
@Slf4j
@Component
public class NewTrackingLoanApplyViewHandler implements MessageHandler {
    @Resource
    private MqConsumeService mqConsumeService;

    @Autowired
    private UserCenterClient userCenterClient;
    @Resource
    private SecureClient secureClient;

    @Override
    public String getTopic() {
        return "tp_cis_event_report_1003806";
    }

    @Override
    public boolean execute(String messageId, Object message) {
        NewTrackingLoanApplyViewVO messageVO = JSONObject.parseObject(message.toString(), NewTrackingLoanApplyViewVO.class);
        BizEventMessageVO bizEventMessageVO = transform(messageVO);

        if (bizEventMessageVO == null) {
            return false;
        }
        bizEventMessageVO.setBizEventType("NewTracking_LoanApplyView");
        mqConsumeService.bizEventProcess(messageId, bizEventMessageVO);
        return true;


    }

    private BizEventMessageVO transform(NewTrackingLoanApplyViewVO newTrackingLoanApplyViewVO) {
        String eventId = newTrackingLoanApplyViewVO.getEventId();
        String creditUserId = newTrackingLoanApplyViewVO.getCreditUserId();
        //如果没有event_id,则是一条错误的消息，不做处理
        //如果没有credit_user_id,则是未登录状态的上报信息，不做处理
        if (StringUtils.isBlank(eventId) || StringUtils.isBlank(creditUserId) || StringUtils.isBlank(newTrackingLoanApplyViewVO.getApp())) {
            log.info("NewTracking_LoanApplyView message is have event_id or credit_user_id or app, newTrackingLoanApplyViewVO:{}", JSONObject.toJSONString(newTrackingLoanApplyViewVO));
            return null;
        }

        BizEventMessageVO bizEventMessageVO = new BizEventMessageVO();
        bizEventMessageVO.setCreditUserId(Long.parseLong(creditUserId));
        bizEventMessageVO.setAppUserId(Long.parseLong(creditUserId));
        bizEventMessageVO.setApp(newTrackingLoanApplyViewVO.getApp());
        bizEventMessageVO.setInnerApp(newTrackingLoanApplyViewVO.getInnerApp());

        if (StringUtils.isNotBlank(newTrackingLoanApplyViewVO.getEventDateTime())) {
            bizEventMessageVO.setTriggerDatetime(newTrackingLoanApplyViewVO.getEventDateTime());
        } else {
            bizEventMessageVO.setTriggerDatetime(DateUtil.convertStr(new Date()));
        }
        bizEventMessageVO.setOs(newTrackingLoanApplyViewVO.getOs());
        if (StringUtils.equals(newTrackingLoanApplyViewVO.getCnlPdCode(), "MPP001000068")) { //小程序上报的app为xyf01,但上报的userno是xyf的,麻雀临时转换
            try {
                String mobile = "";
                String mobileCipher = newTrackingLoanApplyViewVO.getMobileCipher();
                if (StringUtils.isNotBlank(mobileCipher)) {
                    List<SecureEncryptDTO> secureEncryptDTOS = secureClient.batchDecrypt(Arrays.asList(mobileCipher));
                    if (CollectionUtils.isNotEmpty(secureEncryptDTOS)) {
                        mobile = secureEncryptDTOS.get(0).getPlainText();
                        log.info("NewTracking_LoanApplyView getMobile mobileCipher={}, mobile={}", mobileCipher, mobile);
                    }
                    if (StringUtils.isBlank(mobile)) {
                        return null;
                    }
                    UserInfoResp userInfoResp = userCenterClient.getUserByMobile(mobile, Constants.XYF01);
                    bizEventMessageVO.setApp(Constants.XYF01);
                    bizEventMessageVO.setMobile(mobile);
                    bizEventMessageVO.setCreditUserId(userInfoResp.getCreditUserId());
                    bizEventMessageVO.setAppUserId(userInfoResp.getCreditUserId());
                    bizEventMessageVO.setInnerApp(userInfoResp.getInnerApp());
                    log.info("NewTracking_LoanApplyView getUserInfo mobileCipher={}, mobile={}, userInfoResp={}", mobileCipher, mobile, userInfoResp);
                } else {
                    return null;
                }
            } catch (Exception e) {
                LogUtil.logDebug("Tracking_LoanApplyView convertToUserNoXcx error", e);
                return null;
            }
        } else if (Constants.XYF.equals(newTrackingLoanApplyViewVO.getApp())) { //xyf转xyf01
            try {
                String mobile = "";
                String mobileCipher = newTrackingLoanApplyViewVO.getMobileCipher();
                log.info("NewTracking_LoanApplyView mobileCipher={}", mobileCipher);
                if (StringUtils.isNotBlank(mobileCipher)) {
                    log.info("NewTracking_LoanApplyView getMobileStart mobileCipher={}", mobileCipher);
                    List<SecureEncryptDTO> secureEncryptDTOS = secureClient.batchDecrypt(Arrays.asList(mobileCipher));
                    log.info("NewTracking_LoanApplyView batchDecrypt secureEncryptDTOS={}", JSONObject.toJSONString(secureEncryptDTOS));
                    if (CollectionUtils.isNotEmpty(secureEncryptDTOS)) {
                        mobile = secureEncryptDTOS.get(0).getPlainText();
                        log.info("NewTracking_LoanApplyView getMobile mobileCipher={}, mobile={}", mobileCipher, mobile);
                    }
                    if (StringUtils.isBlank(mobile)) {
                        return null;
                    }
                    UserInfoResp userInfoResp = userCenterClient.getUserByMobile(mobile, Constants.XYF01);
                    bizEventMessageVO.setApp(Constants.XYF01);
                    bizEventMessageVO.setMobile(mobile);
                    bizEventMessageVO.setCreditUserId(userInfoResp.getCreditUserId());
                    bizEventMessageVO.setAppUserId(userInfoResp.getCreditUserId());
                    bizEventMessageVO.setInnerApp(userInfoResp.getInnerApp());
                    log.info("Tracking_LoanApplyView getUserInfo mobileCipher={}, mobile={}, userInfoResp={}", mobileCipher, mobile, userInfoResp);
                } else {
                    return null;
                }
            } catch (Exception e) {
                LogUtil.logDebug("Tracking_LoanApplyView convertToUserNo error", e);
                return null;
            }
        }
        return bizEventMessageVO;
    }
}
