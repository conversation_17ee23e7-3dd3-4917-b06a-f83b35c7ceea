package com.xftech.cdp.infra.repository.cdp.strategy.po;

import com.xftech.cdp.domain.strategy.model.enums.StrategyExecStatusEnum;
import com.xftech.cdp.infra.repository.Do;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 策略执行日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class StrategyExecLogDo extends Do {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 策略id
     */
    private Long strategyId;

    /**
     * 策略分组id
     */
    private Long strategyGroupId;

    /**
     * 策略分组name
     */
    private String strategyGroupName;

    /**
     * 策略渠道id
     */
    private Long strategyMarketChannelId;

    /**
     * 引擎版策略返回groupId
     */
    private String engineGroupId;

    /**
     * 策略渠道
     */
    private Integer strategyMarketChannel;

    /**
     * 模版id
     */
    private String templateId;

    /**
     * 执行状态
     */
    private Integer execStatus;

    /**
     * 执行失败原因
     */
    private String failReason;

    /**
     * 分组人数
     */
    private Integer groupCount;

    /**
     * 分组圈选人数
     */
    private Integer execCount;

    /**
     * 麻雀发送至下游人数
     */
    private Integer sendCount;

    /**
     * 下游接收人数
     */
    private Integer receiveCount;

    /**
     * 供应商接收人数
     */
    private Integer supplierCount;

    /**
     * 供应商实际发送人数
     */
    private Integer actualCount;

    /**
     * 回执成功人数
     */
    private Integer succCount;

    /**
     * 使用数量
     */
    private Integer usedCount;

    /**
     * 执行时间
     */
    private LocalDateTime execTime;

    /**
     * 执行结束时间
     */
    private LocalDateTime finishExecTime;

    /**
     * 重试ID
     */
    private Long retryId;

    private String extDetail;

    public static StrategyExecLogDo beginExecute(StrategyGroupDo group, StrategyMarketChannelDo marketChannel) {
        StrategyExecLogDo bo = new StrategyExecLogDo();
        bo.setStrategyId(group.getStrategyId());
        bo.setStrategyGroupId(group.getId());
        bo.setStrategyGroupName(group.getName());
        bo.setStrategyMarketChannelId(marketChannel.getId());
        bo.setStrategyMarketChannel(marketChannel.getMarketChannel());
        bo.setTemplateId(marketChannel.getTemplateId());
        bo.setExecStatus(StrategyExecStatusEnum.EXECUTING.getCode());
        bo.setExecTime(LocalDateTime.now());
        return bo;
    }

    public void execFailure(String failReason) {
        this.execStatus = StrategyExecStatusEnum.FAIL.getCode();
        this.failReason = failReason;
        this.finishExecTime = LocalDateTime.now();
    }


    public void execSuccess() {
        this.execStatus = StrategyExecStatusEnum.SUCCESS.getCode();
        this.finishExecTime = LocalDateTime.now();
    }
}
