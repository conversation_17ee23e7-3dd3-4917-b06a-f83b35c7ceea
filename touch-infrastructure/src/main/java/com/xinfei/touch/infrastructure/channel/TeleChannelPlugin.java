package com.xinfei.touch.infrastructure.channel;

import com.xinfei.touch.application.dto.TouchResponse;
import com.xinfei.touch.application.command.TouchCommand;
import com.xinfei.touch.application.service.ChannelConfig;
import com.xinfei.touch.application.service.ChannelPlugin;
import com.xinfei.touch.domain.model.TouchChannel;
import com.xinfei.touch.infrastructure.config.TouchConfigService;
import com.xinfei.touch.infrastructure.external.TeleService;
import com.xinfei.touch.infrastructure.external.dto.TeleRequest;
import com.xinfei.touch.infrastructure.external.dto.TeleResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 电销渠道插件
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TeleChannelPlugin implements ChannelPlugin {
    
    private final TeleService teleService;
    private final TouchConfigService touchConfigService;
    
    @Override
    public TouchChannel getChannel() {
        return TouchChannel.VOICE;
    }
    
    @Override
    public TouchResponse send(TouchCommand command) {
        String requestId = command.getRequestId();
        
        try {
            log.info("发送电销触达: requestId={}, userId={}", requestId, command.getUserId());
            
            // 1. 参数转换
            TeleRequest teleRequest = convertToTeleRequest(command);
            
            // 2. 调用电销服务
            TeleResponse teleResponse = teleService.call(teleRequest);
            
            // 3. 结果转换
            TouchResponse response = convertToTouchResponse(teleResponse, requestId);
            
            log.info("电销触达完成: requestId={}, status={}, batchNo={}", 
                    requestId, response.getStatus(), response.getBatchNo());
            
            return response;
            
        } catch (Exception e) {
            log.error("电销触达失败: requestId={}", requestId, e);
            return TouchResponse.failed(requestId, "TELE_CALL_FAILED", "电销呼叫失败: " + e.getMessage());
        }
    }
    
    @Override
    public boolean isAvailable() {
        try {
            return teleService.isServiceAvailable();
        } catch (Exception e) {
            log.warn("电销服务可用性检查失败", e);
            return false;
        }
    }
    
    @Override
    public ChannelConfig getConfig() {
        return touchConfigService.getChannelConfig(TouchChannel.VOICE);
    }
    
    /**
     * 转换为电销请求
     */
    private TeleRequest convertToTeleRequest(TouchCommand command) {
        TeleRequest request = new TeleRequest();
        request.setRequestId(command.getRequestId());
        request.setUserId(command.getUserId());
        request.setStrategyId(command.getStrategyId());
        
        // 从模板参数中提取电销相关参数
        if (command.getTemplateParams() != null) {
            request.setMobile((String) command.getTemplateParams().get("mobile"));
            request.setScriptId((String) command.getTemplateParams().get("scriptId"));
            request.setScriptParams(command.getTemplateParams());
            request.setCallbackUrl((String) command.getTemplateParams().get("callbackUrl"));
        }
        
        // 设置扩展参数
        if (command.getExtParams() != null) {
            request.setExtParams(command.getExtParams());
        }
        
        return request;
    }
    
    /**
     * 转换为触达响应
     */
    private TouchResponse convertToTouchResponse(TeleResponse teleResponse, String requestId) {
        if (teleResponse.isSuccess()) {
            return TouchResponse.success(requestId, teleResponse.getCallId());
        } else {
            return TouchResponse.failed(requestId, teleResponse.getErrorCode(), teleResponse.getErrorMessage());
        }
    }
}
