package com.xftech.cdp.domain.dict.fetch;

import com.xftech.cdp.api.dto.resp.dict.Dict;
import com.xftech.cdp.domain.crowd.model.enums.LabelEnum;
import com.xftech.cdp.domain.dict.model.enums.DictCode;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/2/18
 */
@Component
public class LabelOptionTypeDictFetcher implements DictFetcher {
    @Override
    public List<Dict> getDict() {
        List<Dict> result = new ArrayList<>();
        List<LabelEnum.LabelOptionTypeEnum> crowdStatusEnums = Arrays.stream(LabelEnum.LabelOptionTypeEnum.values()).collect(Collectors.toList());
        for (LabelEnum.LabelOptionTypeEnum crowdStatusEnum : crowdStatusEnums) {
            result.add(Dict.builder().dictCode(String.valueOf(crowdStatusEnum.getCode())).dictValue(crowdStatusEnum.getDescription()).build());
        }
        return result;
    }

    @Override
    public DictCode getDictCode() {
        return DictCode.LABEL_OPTION_TYPE;
    }

    @Override
    public String getDescription() {
        return "人群圈选值类型";
    }
}
