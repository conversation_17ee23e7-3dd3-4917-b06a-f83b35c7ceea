<h2 id="SiUOJ">5. 复筛阶段 (rescreen) - 详细分析</h2>
<h3 id="zkmY9">5.1 复筛概述</h3>
复筛阶段是T0引擎触达链路的核心环节，负责对通过预筛的用户进行精细化筛选，包括实时标签查询、策略条件匹配、引擎决策调用等关键步骤。

<h3 id="H6ERR">5.2 复筛流程图</h3>
![](https://cdn.nlark.com/yuque/__mermaid_v3/627a0ef70ae920cb5dbe6128e351bceb.svg)

<h3 id="VfrlT">5.3 复筛入口方法</h3>
<h4 id="HlYCm">5.3.1 方法签名</h4>
**代码位置**: `StrategyEventDispatchServiceImpl.rescreen()` 第482-526行

```java
@Override
public void rescreen(BizEventVO event) {
    long startTime = Instant.now().toEpochMilli();
    log.info("实时策略-复筛开始,事件:{},用户ID:{},消息ID:{},引擎名称:{}",
             event.getBizEventType(), event.getAppUserId(), event.getMessageId(), event.getEngineCode());

    StrategyLabelCheckContext eventContext = new StrategyLabelCheckContext(event);
    try {
        // 1. 策略状态检查
        StrategyDo strategyDo = strategyRepository.selectById(event.getStrategyId());
        if (StrategyStatusEnum.getPausedCodes().contains(strategyDo.getStatus())) {
            log.info("实时策略-复筛,策略状态:{},不执行复筛流程,strategyId:{}",
                     StrategyStatusEnum.getInstance(strategyDo.getStatus()).getDescription(), event.getStrategyId());
            return;
        }

        // 2. 设置触达类型
        eventContext.getBizEventVO().setDispatchType(strategyDo.getDispatchType());

        // 3. 引擎策略处理
        if (StrategyTypeEnum.getInstance(event.getStrategyType()) == StrategyTypeEnum.EVENT_ENGINE
            && !Objects.equals(event.getIfIntoEngine(), false)) {
            event.setIfIntoEngine(true);
            rescreenWithEngine(event);
            return;
        }

        // 4. 普通策略处理
        this.queryLabelHandler(eventContext);    // 实时标签查询
        this.rescreeningHandler(eventContext);   // 策略复筛

        // 5. 触达分发
        if (StrategyTypeEnum.getInstance(event.getStrategyType()) == StrategyTypeEnum.EVENT) {
            this.dispatchHandler(eventContext);
        }

        // 6. 成功处理
        strategyEventCatchService.removeHasEventButNoMatchFlag(event.getBizEventType(), event.getStrategyId());
        eventContext.getBizEventVO().addDecisionSucResult();
        bizEventMqService.sendDecisionResultMessage(eventContext.getBizEventVO());

    } catch (Exception be) {
        // 7. 失败处理
        log.warn("未通过复筛,用户:{}, e:", event.getAppUserId(), be);
        DecisionResultEnum decisionResultEnum = DecisionResultEnum.REPEAT_FILTER_FAIL;
        if (StringUtils.isNotBlank(be.getMessage())) {
            decisionResultEnum.setFailReason(CharSequenceUtil.sub(be.getMessage(), 0, 150));
        }
        if (eventContext.getBizEventVO().getFailCode() == null) {
            eventContext.getBizEventVO().addDecisionFailResult(decisionResultEnum);
        }
        rescreenFailProcess(eventContext);
    }
}
```

<h3 id="hmQN3">5.4 引擎策略复筛 (rescreenWithEngine)</h3>
<h4 id="ke8Z3">5.4.1 引擎决策调用</h4>
**代码位置**: `StrategyEventDispatchServiceImpl.rescreenWithEngine()` 第561-698行

```java
public void rescreenWithEngine(BizEventVO event) {
    Stopwatch stopwatch = Stopwatch.createStarted();

    // 1. 引擎调用次数统计
    if (event.getEngineCallerCount() == null || event.getEngineCallerCount() < 0) {
        event.setEngineCallerCount(1L);
    }

    // 2. 构建引擎请求参数
    StrategyLabelCheckContext eventContext = new StrategyLabelCheckContext(event);
    ModelPredictionReq modelPredictionReq = ModelPredictionReq.builder()
            .model_name(event.getEngineCode())
            .biz_data(ModelPredictionReq.BizData.builder()
                    .requestId(event.getMessageId())
                    .app(event.getApp())
                    .biz_type(event.getBizEventType())
                    .mobile(event.getMobile())
                    .app_user_id(event.getAppUserId())
                    .user_no(event.getAppUserId())
                    .trigger_datetime(DateUtil.getMills(event.getTriggerDatetime()))
                    .timestamp(new Date().getTime())
                    .callerCount(event.getEngineCallerCount())
                    .device_id(event.getDeviceId())
                    .extMap(MapUtils.isNotEmpty(event.getExt()) ? event.getExt() : Maps.newHashMap())
                    .requestType("ONLINE")
                    .build())
            .build();

    // 3. 添加策略ID参数（新增功能）
    addStrategyIdParam(modelPredictionReq, event.getStrategyId());
    event.setEngineCallerCount(event.getEngineCallerCount() + 1);

    // 4. 调用决策引擎
    JSONObject resp = modelPlatformService.prediction(modelPredictionReq);
    event.addEngineDetail("enginePredictionResp", resp);

    // 5. 解析引擎决策结果
    Object decisionVal = resp.getJSONObject("data").getJSONObject("output_data").get(event.getEngineCode());
    PredictDecisionDto predictDecisionDto = JsonUtil.parse(JsonUtil.toJson(decisionVal), PredictDecisionDto.class);

    if (predictDecisionDto != null && predictDecisionDto.isSucced()) {
        // 6. 处理延迟决策
        if (predictDecisionDto.isDelay()) {
            log.info("引擎决策结果:延迟,引擎名称:{},用户ID:{},策略ID:{},延迟时间:{}s",
                     event.getEngineCode(), event.getAppUserId(), event.getStrategyId(), predictDecisionDto.getDelaySeconds());
            // 重新放入延迟队列
            return;
        }

        // 7. 设置营销标识
        event.setIfMarket(predictDecisionDto.ifMarket());

        try {
            // 8. 实时标签筛选
            this.queryLabelHandler(eventContext);
            // 9. 策略复筛
            this.rescreeningHandler(eventContext);
        } catch (Exception ex) {
            log.info("策略引擎版本复筛失败,开始营销触达动作,事件id:{},策略id:{},用户id:{},引擎code:{},错误内容:{}",
                     event.getMessageId(), event.getStrategyId(), event.getAppUserId(), event.getEngineCode(), ex.toString());
            // 复筛失败仍继续执行营销动作
        }

        // 10. 执行营销动作（引擎策略直接调用marketingSend，不走dispatchHandler）
        List<PredictDecisionDto.DecisionData.Action> actions = predictDecisionDto.getDecisionData().getActions();
        for (PredictDecisionDto.DecisionData.Action action : actions) {
            List<PredictDecisionDto.DecisionData.Action.Dispatch> dispatches = action.getOrderedDispatch();
            for (PredictDecisionDto.DecisionData.Action.Dispatch dispatch : dispatches) {
                StrategyMarketChannelEnum strategyMarketChannelEnum = StrategyMarketChannelEnum.getInstance(dispatch.getType());
                // 直接调用营销触达，不经过MQ队列
                int sendRet = marketingSend(dispatchDto, crowdDetailDo, strategyMarketChannelEnum,
                                           action.getGroup_id(), dispatch.getDetail_info(), eventCopy);
                // 处理发送结果
            }
        }
    } else {
        // 引擎决策失败处理
        if (eventContext.getBizEventVO().getFailCode() == null) {
            eventContext.getBizEventVO().addDecisionFailResult(DecisionResultEnum.EVENT_ENGINE_FAIL);
        }
        rescreenFailProcess(eventContext);
    }
}
```

**重要说明**: 引擎策略在`rescreenWithEngine`方法中完成所有处理，包括：
1. 调用决策引擎获取营销动作
2. 执行实时标签查询和策略复筛
3. **直接调用marketingSend进行营销触达**
4. **不会执行5.7章节的dispatchHandler流程**

<h4 id="NkWT6">5.4.2 决策引擎服务调用</h4>
**外部接口**: `ModelPlatformService.prediction()`

**服务地址**: `http://enginepredictcenter.xinfei.io` (来自Apollo配置 `xf.enginepredictcenter.url`)

**请求参数结构**:

```java
public class ModelPredictionReq {
    private String model_name;        // 引擎模型名称
    private BizData biz_data;         // 业务数据

    public static class BizData {
        private String requestId;      // 请求ID
        private String app;           // 应用标识
        private String biz_type;      // 业务类型
        private String mobile;        // 手机号
        private Long app_user_id;     // 用户ID
        private Long user_no;         // 用户编号
        private Long trigger_datetime; // 触发时间
        private Long timestamp;       // 时间戳
        private Long callerCount;     // 调用次数
        private String device_id;     // 设备ID
        private Map<String, Object> extMap; // 扩展参数
        private String requestType;   // 请求类型: ONLINE
        private Long strategy_id;     // 策略ID（新增）
    }
}
```

**响应结构**:

```json
{
    "code": 2000,
    "data": {
        "model_name": "引擎名称",
        "output_data": {
            "引擎名称": {
                "isSucced": true,
                "ifMarket": true,
                "isDelay": false,
                "delaySeconds": 0,
                "decisionData": {
                    "actions": [
                        {
                            "group_id": "分组ID",
                            "detail_info": {
                                "渠道配置": "值"
                            }
                        }
                    ]
                }
            }
        }
    }
}
```

**线上示例**:

```json
{
  "msg": "",
  "code": 2000,
  "data": {
    "model_name": "notCredit_sms_push_APP",
    "output_data": {
      "notCredit_sms_push_APP": "{\"decision_code\": 200, \"decision_msg\": \"OK\", \"decision_data\": {\"delay\": {\"seconds\": 0}, \"actions\": [{\"group_id\": \"nothing\", \"dispatch\": [{\"order\": 0, \"type\": \"none\", \"dispatch_time\": \"2025-06-17 16:41:44\", \"delay\": {}, \"detail_info\": {}}]}]}}"
    },
    "extra_data": {}
  }
}
```

<h3 id="DeD1h">5.5 实时标签查询 (queryLabelHandler)</h3>
<h4 id="a10EF">5.5.1 标签查询入口</h4>
**代码位置**: `StrategyEventDispatchServiceImpl.queryLabelHandler()` 第823-844行

```java
private void queryLabelHandler(StrategyLabelCheckContext eventContext) {
    BizEventVO bizEventVO = eventContext.getBizEventVO();

    // 1. 查询策略配置的实时标签相关配置
    Map<String, List<StrategyMarketEventConditionDo>> labelNameToList =
        strategyMarketEventConditionService.getStringToListByStrategyId(bizEventVO.getStrategyId());

    if (CollectionUtils.isEmpty(labelNameToList)) {
        log.warn("复筛,不存在需要查询的标签.策略ID:{}", bizEventVO.getStrategyId());
        return;
    }

    // 2. 设置查询时间范围（截止本次营销前）
    List<StrategyMarketEventConditionDo> list = labelNameToList.values().stream()
        .flatMap(List::stream)
        .filter(item -> Objects.nonNull(item.getTimeType()))
        .collect(Collectors.toList());
    bizEventVO.setStartTime(getStartTime(CollectionUtils.isEmpty(list) ? null : list.get(0)));

    // 3. 请求数仓查询标签
    Map<Long, Map<String, Object>> labelValueMap = adsStrategyLabelService.query(
        bizEventVO, labelNameToList.keySet(), StrategyInstantLabelTypeEnum.LABEL);

    // 4. 设置事件策略标签对象
    eventContext.setLabelValueMap(labelValueMap);
    eventContext.setMarketEventConditionMap(labelNameToList);

    // 5. 异步钉钉告警
    labelAlarm(eventContext);

    // 6. 设置分发当前可用总额度
    setDistributeCurrentAvailableTotalQuota(bizEventVO, labelValueMap);
}
```

<h4 id="B9esH">5.5.2 标签配置数据表结构</h4>
**数据表**: `strategy_market_event_condition`

```sql
-- 策略事件条件表（实时标签配置）
CREATE TABLE strategy_market_event_condition (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,        -- 主键ID
    strategy_id BIGINT NOT NULL,                 -- 策略ID
    market_event_id BIGINT,                      -- 策略营销节点ID
    time_type INTEGER,                           -- 时间类型(1-相对时间, 2-绝对时间)
    time_value INTEGER,                          -- 时间值
    time_unit INTEGER,                           -- 时间单位(1-分钟, 2-小时, 3-天)
    label_name VARCHAR(100) NOT NULL,            -- 标签名称
    operate_type VARCHAR(20) NOT NULL,           -- 操作类型(eq-等于, ge-大于等于, le-小于等于, gt-大于, lt-小于, in-包含, not_in-不包含)
    condition_value TEXT,                        -- 条件值
    expression VARCHAR(500),                     -- 表达式
    relationship INTEGER DEFAULT 1,              -- 关系(1-且, 2-或)
    optional INTEGER DEFAULT 1,                  -- 可选项(1-实时标签, 2-排除标签)
    d_flag SMALLINT DEFAULT 0,                   -- 删除标识
    created_time TIMESTAMP,                      -- 创建时间
    updated_time TIMESTAMP                       -- 更新时间
);
```

**实体类结构**:

```java
public class StrategyMarketEventConditionDo {
    private Long id;                    // 主键ID
    private Long strategyId;            // 策略ID
    private Long marketEventId;         // 策略营销节点ID
    private Integer timeType;           // 时间类型
    private Integer timeValue;          // 时间值
    private Integer timeUnit;           // 时间单位
    private String labelName;           // 标签名称
    private String operateType;         // 操作类型
    private String conditionValue;      // 条件值
    private String expression;          // 表达式
    private Integer relationship;       // 关系
    private Integer optional;           // 可选项
}
```

<h4 id="Lkyv9">5.5.3 ADS数仓标签查询</h4>
**服务接口**: `AdsStrategyLabelService.query()`

**代码位置**: `AdsStrategyLabelServiceImpl.query()` 第94-200行

```java
@Override
public Map<Long, Map<String, Object>> query(BizEventVO bizEventVO, Collection<String> labelNameList,
                                           StrategyInstantLabelTypeEnum queryType) {
    final TimeInterval timer = new TimeInterval();
    Map<Long, Map<String, Object>> resultMap = new HashMap<>(16);

    try {
        List<AdsLabelReq> reqs = new ArrayList<>();
        Map<String, StrategyInstantLabelDo> labelMetaMap = new HashMap<>();
        List<String> queryInterfaceLabels = new ArrayList<>();

        // 1. 构建标签查询请求
        for (String labelName : labelNameList) {
            AdsLabelReq adsLabelReq = new AdsLabelReq();
            adsLabelReq.setLabel(labelName);

            // 获取标签元数据配置
            StrategyInstantLabelDo labelInfo = strategyInstantLabelService.getByLabelNameAndLabelType(
                labelName, queryType, StrategyTypeEnum.EVENT.getCode(), bizEventVO.getStrategyId());

            if (labelInfo == null) {
                log.warn("T0策略标签查询 标签元数据不存在, 策略id={}, 标签名称={}", bizEventVO.getStrategyId(), labelName);
                continue;
            }

            labelMetaMap.put(labelName, labelInfo);

            // 2. 构建查询参数
            List<Map<String, Object>> params = new ArrayList<>();
            Map<String, Object> map = new HashMap<>();
            Arrays.stream(labelInfo.getLabelReqParam().split(","))
                .forEach(param -> map.put(param, ReflectGetFieldUtil.getFieldValue(bizEventVO, param)));
            params.add(map);

            adsLabelReq.setParams(params);
            reqs.add(adsLabelReq);
        }

        // 3. 发送请求到ADS数仓
        BaseAdsResponse<List<AdsLabelResp>> response = this.queryAdsWrapper(
            queryType, bizEventVO.getStrategyId(), bizEventVO.getAppUserId(), bizEventVO.getMobile(), reqs);

        // 4. 处理响应结果
        Map<String, Object> subResultMap = new HashMap<>(16);
        List<AdsLabelResp> payload = response.getPayload();

        if (!CollectionUtils.isEmpty(payload)) {
            for (AdsLabelResp adsLabelResp : payload) {
                String labelName = adsLabelResp.getLabel();
                List<AdsLabelResp.Param> paramList = adsLabelResp.getParams();

                if (!CollectionUtils.isEmpty(paramList)) {
                    for (AdsLabelResp.Param param : paramList) {
                        if (param.getApp_user_id().equals(bizEventVO.getAppUserId())) {
                            // 数据类型转换
                            StrategyInstantLabelDo labelInfo = labelMetaMap.get(labelName);
                            ValueTypeEnum valueTypeEnum = ValueTypeEnum.getInstance(labelInfo.getLabelValueType());
                            Object value = valueTypeEnum.normalizeValue(param.getResult());
                            subResultMap.put(labelName, value);
                            break;
                        }
                    }
                }
            }
        }

        resultMap.put(bizEventVO.getAppUserId(), subResultMap);

        log.info("T0策略标签查询 策略id={}, 查询类型={}, 标签名称={}, 查询结果={}, 查询耗时={}ms",
                 bizEventVO.getStrategyId(), queryType.getType(), JsonUtil.toJson(labelNameList),
                 JsonUtil.toJson(resultMap), timer.interval());

        return resultMap;

    } catch (Exception e) {
        log.error("T0策略标签查询异常", e);
        throw new StrategyException("T0策略标签查询 查询异常", e);
    }
}
```

<h4 id="TAPmR">5.5.4 变量中心调用</h4>
**外部接口**: 变量中心 (特征平台)

**调用方式**: 根据Apollo配置 `strategyDataFeatureSwitch` 灰度开关决定调用新版特征平台还是旧版ADS数仓

**代码位置**: `ModelPlatformService.getAdbRealTime()` 第248-257行

```java
public AdbRealTimeResp getAdbRealTime(AdbRealTimeReq realTimeReq, Long strategyId) {
    if(strategyId != null && WhitelistSwitchUtil.commonGraySwitchByApollo("" + strategyId,"strategyDataFeatureSwitch")) {
        log.info("ModelPlatformService getAdbRealTime 策略ID={} 灰度策略开关通过，灰度调用特征平台", strategyId);
        return getAdbRealTime(realTimeReq);  // 调用新版特征平台
    } else {
        log.info("ModelPlatformService getAdbRealTime 策略ID={} 灰度策略开关未通过", strategyId);
        return doGetAdbRealTime(realTimeReq); // 调用旧版ADS数仓
    }
}
```

**请求参数结构**:

```java
public class AdbRealTimeReq {
    private Long app_user_id;           // 用户ID
    private String mobile;              // 手机号
    private String app;                 // 应用标识
    private List<String> varCodes;      // 变量代码列表
    private Map<String, Object> inputParams; // 输入参数
}
```

<h4 id="KuhvS">5.5.5 标签元数据配置</h4>
**数据表**: `strategy_instant_label`

```sql
-- 策略实时标签元数据表
CREATE TABLE strategy_instant_label (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,        -- 主键ID
    label_name VARCHAR(100) NOT NULL,            -- 标签名称
    label_desc VARCHAR(200),                     -- 标签描述
    label_type INTEGER NOT NULL,                 -- 标签类型(1-实时标签, 2-短信参数, 3-排除标签)
    label_value_type INTEGER DEFAULT 1,          -- 标签值类型(1-字符串, 2-数字, 3-布尔, 4-日期)
    label_req_param TEXT,                        -- 标签请求参数(逗号分隔)
    strategy_type INTEGER,                       -- 策略类型(0-事件, 1-事件引擎)
    query_type INTEGER DEFAULT 1,                -- 查询类型(1-ADS数仓, 2-接口查询)
    interface_code VARCHAR(100),                 -- 接口代码(query_type=2时使用)
    d_flag SMALLINT DEFAULT 0,                   -- 删除标识
    created_time TIMESTAMP,                      -- 创建时间
    updated_time TIMESTAMP                       -- 更新时间
);
```

<h4 id="Wiu61">5.5.6 标签告警机制</h4>
**代码位置**: `StrategyEventDispatchServiceImpl.labelAlarm()` 第867-882行

```java
private void labelAlarm(StrategyLabelCheckContext eventContext) {
    Long strategyId = eventContext.getBizEventVO().getStrategyId();
    Long appUserId = eventContext.getBizEventVO().getAppUserId();
    Map<Long, Map<String, Object>> labelValueMap = eventContext.getLabelValueMap();

    labelValueMap.forEach((k, v) -> {
        // API首贷额度使用率>1告警
        String labelName = "api_down_loan_usage_rate";
        Object apiDownLoanUsageRate = v.get(labelName);
        if (configService.getLabelDingAlarmSwitch() && Objects.nonNull(apiDownLoanUsageRate)
            && Double.compare(Double.parseDouble(apiDownLoanUsageRate.toString()), 1) > 0) {
            BigDecimal firstLoanSucAmt = eventContext.getBizEventVO().getFirst_loan_suc_amt();
            String content = String.format("事件上报放款金额:%s,标签返回值api_down_loan_usage_rate:%s",
                                          firstLoanSucAmt, apiDownLoanUsageRate);
            // 异步发送钉钉告警消息
            DispatchAlarmExecutor.getPool().submit(() ->
                DingTalkUtil.labelDingTalk(dingTalkConfig, labelName, strategyId, appUserId, content));
        }
    });
}
```

<h3 id="woeoK">5.6 策略复筛 (rescreeningHandler)</h3>
<h4 id="cyy6H">5.6.1 复筛入口方法</h4>
**代码位置**: `StrategyEventDispatchServiceImpl.rescreeningHandler()` 第889-903行

```java
private void rescreeningHandler(StrategyLabelCheckContext eventContext) {
    if (CollectionUtils.isEmpty(eventContext.getMarketEventConditionMap())) {
        log.warn("复筛,不存在标签配置.策略ID:{}", eventContext.getBizEventVO().getStrategyId());
        return;
    }

    // 1. 获取用户的标签值
    Map<String, Object> labelValueMap = eventContext.getLabelValueMap().get(eventContext.getBizEventVO().getAppUserId());

    // 2. 根据标签类型分组
    Map<Integer, List<StrategyMarketEventConditionDo>> optionalMap = eventContext.getMarketEventConditionMap().values()
            .stream()
            .flatMap(List::stream)
            .collect(Collectors.groupingBy(item -> item.getOptional() == 1 ? 1 : 2));

    // 3. 依次检查 实时标签、排序项标签
    new TreeMap<>(optionalMap).forEach((optional, list) ->
        labelCheck(eventContext.getBizEventVO(), list, labelValueMap, optional));
}
```

<h4 id="szll3">5.6.2 标签条件检查</h4>
**代码位置**: `StrategyEventDispatchServiceImpl.labelCheck()` 第918-927行

```java
private void labelCheck(BizEventVO bizEventVO, List<StrategyMarketEventConditionDo> list,
                       Map<String, Object> labelValueMap, Integer type) {
    boolean isInstant = type == 1;  // 1-实时标签, 2-排除标签

    // 1. 构建表达式：将所有条件用 && 连接
    String expression = list.stream()
        .map(StrategyMarketEventConditionDo::getExpression)
        .collect(Collectors.joining(" && "));

    // 2. 使用Aviator引擎计算表达式
    Boolean result = AviatorUtil.compute(expression, labelValueMap);

    // 3. 记录命中结果
    bizEventVO.addHitResult(expression, labelValueMap, result);

    // 4. 判断是否通过
    if (Objects.equals(result, Boolean.FALSE)) {
        bizEventVO.addDecisionFailResult(isInstant ?
            DecisionResultEnum.INSTANT_LABEL_FILTER_FAIL : DecisionResultEnum.EXCLUDE_LABEL_FILTER_FAIL);
        throw new StrategyException(String.format("不满足%s标签条件,用户:%s",
                                                isInstant ? "实时" : "排除", bizEventVO.getAppUserId()));
    }
}
```

<h4 id="gGFhv">5.6.3 表达式计算引擎</h4>
**工具类**: `AviatorUtil`

**代码位置**: `AviatorUtil.compute()` 第38-59行

```java
public static Boolean compute(String script, Map<String, Object> paramMap) {
    try {
        // 1. 编译表达式
        Expression expression = aviatorEvaluatorInstance.compile(script, true);

        if (logger.isDebugEnabled()) {
            logger.debug("AviatorUtil compute script={}, paramMap={}", script, paramMap);
        }

        // 2. 参数检查
        if (!paramCheck(expression, paramMap)) {
            return false;
        }

        // 3. 参数转换
        paramReplace(paramMap);

        // 4. 执行表达式计算
        Object result = expression.execute(paramMap);

        if (logger.isDebugEnabled()) {
            logger.debug("AviatorUtil compute result={}", result);
        }

        return (Boolean) result;
    } catch (Exception ex) {
        logger.error("AviatorUtil compute error, script={}, paramMap={}", script, JsonUtil.toJson(paramMap), ex);
    }
    return false;
}
```

<h4 id="J0bLo">5.6.4 表达式语法示例</h4>
**支持的操作符**:

+ **比较操作**: `==`, `!=`, `>`, `>=`, `<`, `<=`
+ **逻辑操作**: `&&`, `||`, `!`
+ **包含操作**: `contain(字段, 值)` - 检查字段是否包含指定值
+ **范围操作**: `in(字段, [值1, 值2, 值3])` - 检查字段是否在指定范围内

**表达式示例**:

```java
// 1. 简单比较
"age >= 18"

// 2. 逻辑组合
"age >= 18 && income > 5000"

// 3. 包含检查
"contain(app, 'xyf01')"

// 4. 范围检查
"in(city, ['北京', '上海', '深圳'])"

// 5. 复杂表达式
"age >= 18 && income > 5000 && contain(app, 'xyf01') && credit_score >= 600"
```

<h4 id="ynh3Z">5.6.5 参数检查和转换</h4>
**参数检查**: `AviatorUtil.paramCheck()` 第63-98行

```java
private static Boolean paramCheck(Expression expression, Map<String, Object> dataMap) {
    // 1. 获取表达式使用到的字段名称
    List<String> dataNameList = expression.getVariableNames();

    if (CollectionUtils.isNotEmpty(dataNameList)) {
        for (String dataName : dataNameList) {
            if (!"nil".equals(dataName)) {
                // 2. 检查身份证号必需字段
                List<String> needIdCardNumberCodes = JSONObject.parseArray(
                    ApolloUtil.getAppProperty("ads.feature.need.idcardnumber", "[]"), String.class);
                if (needIdCardNumberCodes.contains(dataName) && !dataMap.containsKey(dataName)) {
                    return false;
                }

                // 3. 检查客户号必需字段
                List<String> needCustNoCodes = JSONObject.parseArray(
                    ApolloUtil.getAppProperty("ads.feature.need.custno", "[]"), String.class);
                if (needCustNoCodes.contains(dataName) && !dataMap.containsKey(dataName)) {
                    return false;
                }
            }
        }
    }
    return true;
}
```

**参数转换**: `AviatorUtil.paramReplace()` 第100-121行

```java
private static Boolean paramReplace(Map<String, Object> dataMap) {
    try {
        // 获取需要转换的字段列表
        List<String> keysToReplace = JSONObject.parseArray(
            ApolloUtil.getAppProperty("ads.feature.replace.config", "[]"), String.class);

        for (String key : keysToReplace) {
            if (dataMap.containsKey(key)) {
                Object value = dataMap.get(key);

                // 判断是否是日期类型，如果是今天则返回1，否则返回0
                if (value instanceof String && value.toString().matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}")) {
                    LocalDateTime dateTime = LocalDateTime.parse(value.toString(),
                        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    LocalDate parsedDate = dateTime.toLocalDate();
                    LocalDate currentDate = LocalDate.now();
                    String status = parsedDate.isEqual(currentDate) ? "1" : "0";
                    dataMap.put(key, status);
                }
            }
        }
        return true;
    } catch (Exception e) {
        logger.error("AviatorUtil paramReplace error, paramMap={}", JsonUtil.toJson(dataMap), e);
        return false;
    }
}
```

<h4 id="wQXiB">5.6.6 Apollo配置</h4>
**相关配置项**:

```properties
# 需要身份证号的特征字段
ads.feature.need.idcardnumber = ["idcard_feature1", "idcard_feature2"]

# 需要客户号的特征字段
ads.feature.need.custno = ["cust_feature1", "cust_feature2"]

# 需要参数转换的字段
ads.feature.replace.config = ["last_login_time", "register_time"]

# 标签钉钉告警开关
label.ding.alarm.switch = true

# 策略数据特征开关（灰度配置）
strategyDataFeatureSwitch.{策略ID} = {"percentNum": 50, "whitelist": "用户ID列表"}
```

<h3 id="StrategyComparison">5.7 策略类型对比总结</h3>

<h4 id="FlowComparison">5.7.1 流程对比</h4>

| 处理阶段 | 普通策略 (EVENT) | 引擎策略 (EVENT_ENGINE) |
|----------|------------------|-------------------------|
| **复筛入口** | rescreen() → queryLabelHandler() → rescreeningHandler() | rescreen() → rescreenWithEngine() |
| **决策来源** | 基于配置的标签条件 | 决策引擎AI决策 |
| **标签查询时机** | 复筛开始时立即执行 | 引擎决策成功后执行 |
| **复筛失败处理** | 直接终止，不进行触达 | 记录失败但继续执行营销动作 |
| **触达方式** | dispatchHandler() → MQ队列 → 异步dispatch() | 直接同步调用marketingSend() |
| **分组渠道来源** | 数据库配置表 | 引擎决策返回 |

<h4 id="CodePathComparison">5.7.2 代码路径对比</h4>

**普通策略执行路径**:
```
rescreen()
├── queryLabelHandler() - 实时标签查询
├── rescreeningHandler() - 策略复筛
└── dispatchHandler() - 触达分发
    ├── userGroupMatch() - 用户分组匹配
    ├── flowCtrl() - 流控检查
    └── mqProducerService.channelDelivery() - MQ投递
        └── 异步处理 → dispatch() → marketingSend()
```

**引擎策略执行路径**:
```
rescreen()
└── rescreenWithEngine() - 引擎策略处理
    ├── modelPlatformService.prediction() - 调用决策引擎
    ├── 解析引擎决策结果
    ├── queryLabelHandler() - 实时标签查询（引擎决策后）
    ├── rescreeningHandler() - 策略复筛（引擎决策后）
    └── 直接循环调用 marketingSend() - 同步营销触达
```

<h3 id="DkEVH">5.8 普通策略触达分发 (dispatchHandler)</h3>

**注意**: 此章节仅适用于**普通策略**，引擎策略不会执行此流程。

<h4 id="wEuoq">5.7.1 触达分发入口</h4>
**代码位置**: `StrategyEventDispatchServiceImpl.dispatchHandler()` 第934-970行
**适用策略**: 仅普通策略 (StrategyTypeEnum.EVENT)

```java
private void dispatchHandler(StrategyLabelCheckContext eventContext) {
    BizEventVO bizEventVO = eventContext.getBizEventVO();
    CrowdDetailDo crowdDetail = convertToCrowdDetail(bizEventVO);
    boolean notFlow = StringUtils.equals("NOTIFY", bizEventVO.getDispatchType());

    // 1. 获取该策略下所有分组
    List<StrategyGroupDo> strategyGroupDoList = cacheStrategyGroupService.selectListByStrategyId(bizEventVO.getStrategyId());

    // 2. 用户分组匹配
    strategyGroupDoList = userGroupMatch(bizEventVO, crowdDetail, strategyGroupDoList);

    // 3. 循环分组渠道调用下发
    strategyGroupDoList.forEach(strategyGroupDo -> {
        List<StrategyMarketChannelDo> marketChannelList = cacheStrategyMarketChannelService.selectByStrategyGroupId(strategyGroupDo.getId());

        marketChannelList.forEach(marketChannelDo -> {
            bizEventVO.setMarketChannel(marketChannelDo.getMarketChannel());
            bizEventVO.setMarketChannelId(marketChannelDo.getId());
            bizEventVO.setStrategyGroupId(strategyGroupDo.getId());
            bizEventVO.setStrategyGroupName(strategyGroupDo.getName());

            try {
                // 4. 触达时间判定
                if (!notFlow && !isInTime(marketChannelDo)) {
                    log.info("不在有效的触达时间内, 丢弃不进行触达, 策略id:{}, 渠道id:{}",
                             bizEventVO.getStrategyId(), marketChannelDo.getId());
                    return;
                }

                // 5. 调用触达方法
                this.dispatch(bizEventVO);

            } catch (Exception e) {
                log.error("触达异常, 策略id:{}, 渠道id:{}, 用户id:{}",
                         bizEventVO.getStrategyId(), marketChannelDo.getId(), bizEventVO.getAppUserId(), e);
            }
        });
    });
}
```

<h4 id="d6Uzo">5.7.2 用户分组匹配</h4>
**代码位置**: `AbstractStrategyEventDispatchService.userGroupMatch()` 第1000-1032行

```java
protected List<StrategyGroupDo> userGroupMatch(BizEventVO bizEventVO, CrowdDetailDo crowdDetail,
                                              List<StrategyGroupDo> strategyGroupDoList) {
    List<StrategyGroupDo> matchedGroups = new ArrayList<>();

    for (StrategyGroupDo strategyGroupDo : strategyGroupDoList) {
        if (!strategyGroupDo.getIsExecutable()) {
            continue; // 跳过不可执行的分组
        }

        try {
            // 1. 解析分组配置
            StrategyCreateReq.GroupConfig groupConfig = JSON.parseObject(
                strategyGroupDo.getGroupConfig(), StrategyCreateReq.GroupConfig.class);

            if (groupConfig == null) {
                continue;
            }

            // 2. 根据分组类型进行匹配
            boolean isMatched = false;

            switch (groupConfig.getGroupType()) {
                case "random":
                    // 随机分组：基于用户ID哈希
                    isMatched = isUserInRandomGroup(crowdDetail.getUserId(), groupConfig.getPercent());
                    break;

                case "whitelist":
                    // 白名单分组：检查用户是否在白名单中
                    isMatched = isUserInWhitelist(crowdDetail.getUserId(), groupConfig.getWhitelist());
                    break;

                case "abtest":
                    // AB测试分组：基于AB号进行分组
                    isMatched = isUserInABGroup(bizEventVO.getAbNum(), groupConfig);
                    break;

                default:
                    log.warn("未知的分组类型: {}", groupConfig.getGroupType());
                    break;
            }

            if (isMatched) {
                matchedGroups.add(strategyGroupDo);
            }

        } catch (Exception e) {
            log.error("用户分组匹配异常, 分组ID:{}, 用户ID:{}",
                     strategyGroupDo.getId(), crowdDetail.getUserId(), e);
        }
    }

    return matchedGroups;
}
```

<h4 id="ZeCk6">5.7.3 分组配置结构</h4>
**数据表**: `strategy_group`

```sql
-- 策略分组表
CREATE TABLE strategy_group (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,        -- 分组ID
    strategy_id BIGINT NOT NULL,                 -- 策略ID
    name VARCHAR(100) NOT NULL,                  -- 分组名称
    group_config TEXT,                           -- 分组配置(JSON格式)
    is_executable TINYINT DEFAULT 1,             -- 是否可执行(0-否, 1-是)
    d_flag SMALLINT DEFAULT 0,                   -- 删除标识
    created_time TIMESTAMP,                      -- 创建时间
    updated_time TIMESTAMP                       -- 更新时间
);
```

**分组配置JSON结构**:

```java
public static class GroupConfig {
    private String groupType;        // 分组类型: random-随机, whitelist-白名单, abtest-AB测试
    private Integer percent;         // 分组比例(随机分组使用)
    private String whitelist;        // 白名单用户ID列表(逗号分隔)
    private Integer selected;        // 是否选中(引擎灰度使用): 1-进入引擎, 0-不进入引擎
    private String abRange;          // AB测试范围(如: "0-49")
    private Integer priority;        // 分组优先级
}
```

**分组配置示例**:

```json
{
    "groupType": "random",
    "percent": 50,
    "selected": 1,
    "priority": 1
}

{
    "groupType": "whitelist",
    "whitelist": "123456,789012,345678",
    "selected": 1,
    "priority": 2
}

{
    "groupType": "abtest",
    "abRange": "0-49",
    "selected": 1,
    "priority": 3
}
```

<h4 id="YoPFT">5.7.4 触达时间判定</h4>
**代码位置**: `AbstractStrategyEventDispatchService.isInTime()` 第985-999行

```java
private boolean isInTime(StrategyMarketChannelDo marketChannelDo) {
    if (StringUtils.isBlank(marketChannelDo.getSendTime())) {
        return true; // 没有配置发送时间，默认允许
    }

    try {
        // 解析发送时间配置
        SendTimeConfig sendTimeConfig = JSON.parseObject(marketChannelDo.getSendTime(), SendTimeConfig.class);

        if (sendTimeConfig == null) {
            return true;
        }

        LocalTime now = LocalTime.now();
        LocalTime startTime = LocalTime.parse(sendTimeConfig.getStartTime());
        LocalTime endTime = LocalTime.parse(sendTimeConfig.getEndTime());

        // 判断当前时间是否在允许的发送时间范围内
        return now.isAfter(startTime) && now.isBefore(endTime);

    } catch (Exception e) {
        log.error("触达时间判定异常, 渠道ID:{}", marketChannelDo.getId(), e);
        return true; // 异常情况下默认允许
    }
}
```

**发送时间配置结构**:

```java
public static class SendTimeConfig {
    private String startTime;    // 开始时间 (如: "09:00")
    private String endTime;      // 结束时间 (如: "21:00")
    private List<String> weekdays; // 允许的星期 (如: ["1","2","3","4","5"])
}
```

<h3 id="XaDtd">5.9 复筛失败处理</h3>
<h4 id="D5NCQ">5.9.1 失败处理方法</h4>
**代码位置**: `StrategyEventDispatchServiceImpl.rescreenFailProcess()` 第813-816行

```java
private void rescreenFailProcess(StrategyLabelCheckContext eventContext) {
    // 1. 发送决策结果消息
    bizEventMqService.sendDecisionResultMessage(eventContext.getBizEventVO());

    // 2. 设置事件无匹配标识
    strategyEventCatchService.hasEventButNoMatchFlag(
        eventContext.getBizEventVO().getBizEventType(),
        eventContext.getBizEventVO().getStrategyId());
}
```

<h4 id="fSUQN">5.9.2 决策结果枚举</h4>
**失败原因分类**:

```java
public enum DecisionResultEnum {
    REPEAT_FILTER_FAIL("复筛失败"),
    INSTANT_LABEL_FILTER_FAIL("实时标签筛选失败"),
    EXCLUDE_LABEL_FILTER_FAIL("排除标签筛选失败"),
    EVENT_ENGINE_FAIL("引擎决策失败"),
    MARKET_SUB_FAIL("营销节点限制条件验证不通过"),
    INSTANT_CROWD_FILTER_FAIL("实时人群筛选不通过");
}
```

<h3 id="UZ1wW">5.10 数据流转总结</h3>
<h4 id="RJEin">5.10.1 普通策略数据流转图</h4>
```mermaid
graph LR
    A[延迟队列] --> B[rescreen方法]
    B --> C[strategy表]
    C --> D[strategy_market_event_condition表]
    D --> E[strategy_instant_label表]
    E --> F[ADS数仓/特征平台]
    F --> G[标签数据]
    G --> H[Aviator表达式引擎]
    H --> I[复筛结果]
    I --> J[strategy_group表]
    J --> K[strategy_market_channel表]
    K --> L[RabbitMQ队列]
    L --> M[异步dispatch处理]
    M --> N[marketingSend方法]
```

<h4 id="EngineDataFlow">5.10.2 引擎策略数据流转图</h4>

```mermaid
graph LR
    A[延迟队列] --> B[rescreen方法]
    B --> C[rescreenWithEngine]
    C --> D[决策引擎接口]
    D --> E[引擎决策结果]
    E --> F[strategy_market_event_condition表]
    F --> G[strategy_instant_label表]
    G --> H[ADS数仓/特征平台]
    H --> I[标签数据]
    I --> J[Aviator表达式引擎]
    J --> K[复筛结果]
    K --> L[引擎Action列表]
    L --> M[直接marketingSend]
```

<h4 id="GNXJY">5.10.3 关键数据表</h4>
| 数据表 | 作用 | 关键字段 |
| --- | --- | --- |
| `strategy` | 策略主表 | id, type, status, dispatch_type |
| `strategy_market_event_condition` | 实时标签配置 | strategy_id, label_name, expression, optional |
| `strategy_instant_label` | 标签元数据 | label_name, label_type, query_type, label_req_param |
| `strategy_group` | 策略分组 | strategy_id, group_config, is_executable |
| `strategy_market_channel` | 渠道配置 | strategy_group_id, market_channel, send_time |


<h4 id="wJKBr">5.10.4 外部接口依赖</h4>

| 接口 | 地址 | 普通策略 | 引擎策略 | 作用 |
|------|------|----------|----------|------|
| 决策引擎 | `http://enginepredictcenter.xinfei.io` | ❌ | ✅ | 引擎决策调用 |
| ADS数仓 | 配置的数仓地址 | ✅ | ✅ | 标签数据查询 |
| 特征平台 | 变量中心地址 | ✅ | ✅ | 新版标签查询 |
| RabbitMQ | 配置的MQ地址 | ✅ | ❌ | 触达队列投递 |
| 业务引擎数据库 | `**********************************************************************************` | ❌ | ✅ | 引擎相关数据 |

<h3 id="oNe7G">5.11 性能优化点</h3>
<h4 id="XWRAa">5.11.1 缓存机制</h4>
+ **策略配置缓存**: `CacheStrategyService`
+ **标签配置缓存**: `CacheStrategyMarketEventConditionService`
+ **分组配置缓存**: `CacheStrategyGroupService`
+ **渠道配置缓存**: `CacheStrategyMarketChannelService`

<h4 id="vYlwm">5.11.2 异步处理</h4>
+ **钉钉告警**: 异步发送告警消息
+ **决策结果**: 异步发送决策结果到MQ
+ **监控埋点**: 异步记录监控数据
+ **普通策略触达**: 通过MQ队列异步处理

<h4 id="LeSVy">5.11.3 批量优化</h4>
+ **标签查询**: 支持批量查询多个标签
+ **用户查询**: 支持批量查询多个用户
+ **表达式编译**: 缓存编译后的表达式

<h4 id="PerformanceDiff">5.11.4 策略类型性能对比</h4>

| 性能维度 | 普通策略 | 引擎策略 |
|----------|----------|----------|
| **处理延迟** | 高（异步队列） | 低（同步处理） |
| **吞吐量** | 高 | 中等 |
| **资源消耗** | 低（分布式处理） | 高（同步阻塞） |
| **可扩展性** | 好（队列缓冲） | 一般（依赖引擎性能） |
| **实时性** | 差 | 好 |
| **适用场景** | 批量营销 | 实时精准营销 |

