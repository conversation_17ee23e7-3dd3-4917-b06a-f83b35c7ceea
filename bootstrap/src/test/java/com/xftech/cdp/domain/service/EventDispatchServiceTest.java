package com.xftech.cdp.domain.service;

import com.xftech.cdp.BaseTest;
import com.xftech.cdp.domain.dispatch.EventDispatchService;
import com.xftech.cdp.infra.client.sms.model.SmsSingleSendRequester;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class EventDispatchServiceTest extends BaseTest {

    @Autowired
    EventDispatchService eventDispatchService;
    @Test
    public void test_sms(){
        eventDispatchService.sendSms(new SmsSingleSendRequester());
    }
}
