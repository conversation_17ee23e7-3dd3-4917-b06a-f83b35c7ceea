# T0实时和离线触达回执动作清单

## 1. 概述

本文档详细列出了T0实时触达和离线触达完成后的所有回执动作类型、处理流程和相关配置。

## 2. 回执动作分类

### 2.1 短信回执 (SMS Report)

#### 基本信息
- **触发条件**: 短信发送完成后
- **队列配置**: `sms_supplier_report_callback`
- **处理类**: `MqConsumeServiceImpl`
- **处理方法**: `smsReportProcess(List<SmsReportVO> smsReportVOList)`
- **代码位置**:
  - 主要处理类: `cdp-domain/src/main/java/com/xftech/cdp/domain/mq/impl/MqConsumeServiceImpl.java:82-168`
  - 新架构消费者: `touch-infrastructure/src/main/java/com/xinfei/touch/infrastructure/mq/SmsReceiptConsumer.java:36-56`
  - VO类定义: `touch-infrastructure/src/main/java/com/xinfei/touch/infrastructure/mq/dto/SmsReportVO.java:1-70`

#### 回执状态枚举
| 状态值 | 描述 | status | usedStatus |
|--------|------|--------|------------|
| `delivered` | 到达成功 | 1 | -1 |
| `failed` | 发送失败 | 0 | -1 |
- **代码位置**: `cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/model/enums/SmsStatusEnum.java:9-46`

#### 处理动作清单
- [ ] 根据批次号查询 `crowd_push_batch` 表获取表序号
  - **代码位置**: `MqConsumeServiceImpl.java:99` - `crowdPushBatchRepository.selectTableNoByBatchNum()`
- [ ] 更新 `user_dispatch_detail_{tableNo}` 表状态
  - **代码位置**: `MqConsumeServiceImpl.java:123-133` - 构建UserDispatchDetailDo对象
- [ ] 更新 `strategy_exec_log` 表统计数据
  - **代码位置**: `MqConsumeServiceImpl.java:136-167` - 统计逻辑处理
- [ ] 失败时记录Cat监控指标: `failed_sms_{strategyId}`
  - **代码位置**: `MqConsumeServiceImpl.java:113` - `Tracer.logMetricForCount("failed_sms_" + strategyId)`
- [ ] 更新用户发送计数器 (成功/失败)
  - **代码位置**: `MqConsumeServiceImpl.java:114-117` - `userSendCounterService.counterIncrementFailed()`

#### Apollo配置
```properties
# 短信回执队列配置
sms.report.exchange = exchange_report_callback_topic
sms.report.exchangeType = topic
sms.report.routingKey = sms_center_callback_app_xyf-cdp
sms.report.queue.name = sms_supplier_report_callback
sms.report.over.time = 60
```

### 2.2 推送回执 (Push Report)

#### 基本信息
- **触发条件**: App推送发送完成后
- **处理方法**: `pushReportProcess(PushReportReq pushReportReq)`
- **代码位置**:
  - 主要处理方法: `cdp-domain/src/main/java/com/xftech/cdp/domain/mq/impl/MqConsumeServiceImpl.java:169-244`
  - 请求VO类: `cdp-api/src/main/java/com/xftech/cdp/api/dto/req/external/PushReportReq.java:1-60`

#### 回执状态枚举
| 状态码 | 状态名 | 描述 | 对应status |
|--------|--------|------|------------|
| 1 | FAILED | 推送失败 | 0 |
| 2 | UNKNOWN | 状态未知 | null |
| 3 | SUCCESS | 推送成功 | 1 |
- **代码位置**: `cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/model/enums/PushCallbackStatusEnum.java:19-42`

#### 处理动作清单
- [ ] 验证回执状态有效性
  - **代码位置**: `MqConsumeServiceImpl.java:179` - `PushCallbackStatusEnum.getEnum(pushReportReq.getStatus())`
- [ ] 根据批次号查询触达记录
  - **代码位置**: `MqConsumeServiceImpl.java:184-204` - 查询crowd_push_batch和event_push_batch表
- [ ] 更新触达明细表状态和时间
  - **代码位置**: `MqConsumeServiceImpl.java:209-217` - 构建UserDispatchDetailDo对象
- [ ] 失败时记录Cat监控: `failed_push_{strategyId}`
  - **代码位置**: `MqConsumeServiceImpl.java:198` - `Tracer.logMetricForCount("failed_push_" + strategyId)`
- [ ] 更新用户发送计数器
  - **代码位置**: `MqConsumeServiceImpl.java:199-202` - `userSendCounterService.counterIncrementFailed()`

### 2.3 优惠券回执 (Coupon Callback)

#### 基本信息
- **触发条件**: 优惠券发放完成后
- **消息队列**: RocketMQ
- **处理方法**: `couponCallbackProcess(List<CouponCallbackVO> couponCallbackVOList)`
- **代码位置**:
  - 主要处理方法: `cdp-domain/src/main/java/com/xftech/cdp/domain/mq/impl/MqConsumeServiceImpl.java:246-505`
  - 新架构消费者: `touch-infrastructure/src/main/java/com/xinfei/touch/infrastructure/mq/CouponReceiptConsumer.java:26-60`
  - VO类定义: `touch-infrastructure/src/main/java/com/xinfei/touch/infrastructure/mq/dto/CouponCallbackVO.java:1-80`
  - RocketMQ消费者: `cdp-domain/src/main/java/com/xftech/cdp/infra/rocketmq/couponCallback/ReceivedCouponHandler.java:24-42`

#### 回执状态枚举
| 状态值 | 描述 | status | usedStatus |
|--------|------|--------|------------|
| `successed` | 发送成功 | 1 | 0 |
| `failed` | 发送失败 | 0 | -1 |
| `used` | 使用成功 | 1 | 1 |
- **代码位置**: `cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/model/enums/CouponStatusEnum.java:8-58`

#### 处理动作清单
- [ ] 解析优惠券回执消息
  - **代码位置**: `MqConsumeServiceImpl.java:252-262` - 解析CouponCallbackVO消息
- [ ] 更新触达明细表发放状态
  - **代码位置**: `MqConsumeServiceImpl.java:293-370` - 构建UserDispatchDetailDo对象
- [ ] 更新优惠券使用状态
  - **代码位置**: `MqConsumeServiceImpl.java:295-326` - 设置status和usedStatus
- [ ] 失败时记录Cat监控: `failed_coupon_{strategyId}`
  - **代码位置**: `MqConsumeServiceImpl.java:283` - `Tracer.logMetricForCount("failed_coupon_" + strategyId)`
- [ ] 特殊业务回执处理:
  - [ ] 生活权益回执: 发送到 `tp_xyf_cdp_notify:tg_liferights`
    - **代码位置**: `MqConsumeServiceImpl.java:328-370` - 生活权益特殊处理逻辑
  - [ ] X天免息回执: 发送到 `tp_xyf_cdp_notify:tg_xDayInterestFree`
    - **代码位置**: `MqConsumeServiceImpl.java:328-370` - X天免息特殊处理逻辑
  - [ ] 提额回执: 发送到 `tp_xyf_cdp_notify:tg_increaseamt`

#### 特殊优惠券类型
- **生活权益** (couponType=4): 发送生活权益回执消息
  - **代码位置**: `MqConsumeServiceImpl.java:268-270` - 渠道判断逻辑
- **X天免息** (couponType=5): 发送X天免息回执消息
  - **代码位置**: `MqConsumeServiceImpl.java:270-272` - 渠道判断逻辑

### 2.4 AI外呼回执 (AI Callback)

#### 基本信息
- **触发条件**: AI外呼任务完成后
- **Topic**: `CALL_CENTER_AI_CALLBACK`
- **处理方法**: `aiCallbackProcess(AiCallBackMessageVO aiCallBackMessageVO)`
- **代码位置**:
  - 主要处理方法: `cdp-domain/src/main/java/com/xftech/cdp/domain/mq/impl/MqConsumeServiceImpl.java:569-629`
  - 新架构消费者: `touch-infrastructure/src/main/java/com/xinfei/touch/infrastructure/mq/AiCallbackConsumer.java:32-56`
  - VO类定义: `cdp-domain/src/main/java/com/xftech/cdp/infra/rocketmq/dto/AiCallBackMessageVO.java:1-47`

#### 回执状态枚举
| 状态码 | 状态名 | 描述 |
|--------|--------|------|
| `FILTER` | 被过滤 | 用户被过滤，未拨打 |
| `CALLED` | 已拨打 | 外呼已完成 |
| `FAILURE` | 推送失败 | 推送AI中台失败 |
- **代码位置**: `cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/model/enums/AiProntoCallbackStatusEnum.java:1-47`

#### 处理动作清单
- [ ] 解析AI回执消息
  - **代码位置**: `MqConsumeServiceImpl.java:575-586` - 解析和验证AiCallBackMessageVO
- [ ] 验证必要字段 (batchNo, status)
  - **代码位置**: `MqConsumeServiceImpl.java:579-585` - 字段验证逻辑
- [ ] 更新AI外呼触达状态
  - **代码位置**: `MqConsumeServiceImpl.java:587-629` - 状态更新逻辑
- [ ] 统计外呼结果
  - **代码位置**: `MqConsumeServiceImpl.java:619-625` - 失败计数统计
- [ ] 更新批次执行状态
  - **代码位置**: `MqConsumeServiceImpl.java:587-629` - 批次状态更新

### 2.5 语音外呼回执

#### 基本信息
- **触发条件**: 语音外呼完成后
- **特殊处理**: 语音渠道 (`StrategyMarketChannelEnum.VOICE`) 直接设置完成状态
- **代码位置**:
  - 渠道枚举定义: `cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/model/enums/StrategyMarketChannelEnum.java:25` - VOICE(2, "电销")
  - 特殊处理逻辑: `cdp-domain/src/main/java/com/xftech/cdp/infra/repository/cdp/crowd/po/CrowdPushBatchDo.java:152-154`

#### 处理动作清单
- [ ] 发送成功时直接设置 `queryStatus = COMPLETED`
  - **代码位置**: `CrowdPushBatchDo.java:152-154` - `if (channelEnum == StrategyMarketChannelEnum.VOICE) { this.setQueryStatus(CrowdPushQueryStatusEnum.COMPLETED.getCode()); }`
- [ ] 无需额外回执查询流程
  - **代码位置**: `CrowdPushBatchDo.java:156` - 其他渠道设置为WAIT_QUERIED状态，语音渠道直接完成

## 3. 通用回执处理流程

### 3.1 数据库更新操作

#### user_dispatch_detail_{tableNo} 表更新
- [ ] `status`: 发送状态 (1=成功, 0=失败, -1=异常)
- [ ] `used_status`: 使用状态 (仅优惠券)
- [ ] `dispatch_time`: 实际发送时间
- [ ] `updated_time`: 更新时间
- **代码位置**:
  - Repository类: `cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/repository/UserDispatchDetailRepository.java`
  - 更新操作: `MqConsumeServiceImpl.java` 各回执处理方法中的UserDispatchDetailDo对象构建

#### strategy_exec_log 表统计更新
- [ ] `receive_count`: 下游接收人数
- [ ] `supplier_count`: 供应商接收人数
- [ ] `actual_count`: 实际发送人数
- [ ] `succ_count`: 成功人数
- **代码位置**:
  - Repository类: `cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/repository/StrategyExecLogRepository.java`
  - 统计更新: `MqConsumeServiceImpl.java` 各回执处理方法中的统计逻辑

#### crowd_push_batch 表状态更新
- [ ] `batch_status`: 批次状态
- [ ] `query_status`: 查询状态
- **代码位置**:
  - Repository类: `cdp-domain/src/main/java/com/xftech/cdp/domain/crowd/repository/CrowdPushBatchRepository.java:25-124`
  - 状态初始化: `CrowdPushBatchDo.java:148-161` - `initQueryStatus()`方法

### 3.2 计数器更新

#### 成功计数
- [ ] 调用 `userSendCounterService.counterIncrementSuccess()`
- [ ] 参数: userId, strategyId, marketChannel, date
- **代码位置**:
  - 服务接口: `cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/service/UserSendCounterService.java:1-9`
  - 实现类: `cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/service/impl/UserSendCounterServiceImpl.java:22-62`

#### 失败计数
- [ ] 调用 `userSendCounterService.counterIncrementFailed()`
- [ ] 记录Cat监控指标
- [ ] 参数: userId, strategyId, marketChannel, date
- **代码位置**:
  - 失败计数方法: `UserSendCounterServiceImpl.java:64-70` - `counterIncrementFailed()`
  - 调用位置: `MqConsumeServiceImpl.java` 各回执处理方法中的失败处理逻辑

### 3.3 监控和日志

#### Cat监控指标
- [ ] `failed_sms_{strategyId}`: 短信发送失败
  - **代码位置**: `MqConsumeServiceImpl.java:113` - `Tracer.logMetricForCount("failed_sms_" + strategyId)`
- [ ] `failed_push_{strategyId}`: 推送发送失败
  - **代码位置**: `MqConsumeServiceImpl.java:198` - `Tracer.logMetricForCount("failed_push_" + strategyId)`
- [ ] `failed_coupon_{strategyId}`: 优惠券发放失败
  - **代码位置**: `MqConsumeServiceImpl.java:283` - `Tracer.logMetricForCount("failed_coupon_" + strategyId)`
- [ ] `failed_ai_{strategyId}`: AI外呼失败
  - **代码位置**: `MqConsumeServiceImpl.java:621` - `Tracer.logMetricForCount("failed_ai_" + strategyId)`

#### 日志记录
- [ ] 回执消息接收日志
  - **代码位置**: 各MQ消费者类中的log.info()语句
- [ ] 状态更新操作日志
  - **代码位置**: `MqConsumeServiceImpl.java` 各处理方法中的日志记录
- [ ] 异常处理日志
  - **代码位置**: 各消费者类的catch块中的log.error()语句
- [ ] 业务回执发送日志
  - **代码位置**: `MqConsumeServiceImpl.java` 特殊业务回执处理中的日志

## 4. 回执数据流图

```
触达渠道发送 → 第三方服务商 → 回执消息 → MQ队列 → CDP系统处理 → 数据库更新 → 业务回执(可选)
     ↓              ↓              ↓           ↓            ↓             ↓
   短信/推送      供应商回调      RabbitMQ    回执处理器    状态更新      下游通知
   优惠券/AI      状态通知       RocketMQ    状态解析     计数更新      业务系统
```

## 5. 异常处理机制

### 5.1 回执解析异常
- [ ] 消息格式错误处理
- [ ] 必要字段缺失处理
- [ ] 状态值无效处理

### 5.2 数据库操作异常
- [ ] 批次号不存在处理
- [ ] 表序号获取失败处理
- [ ] 更新操作失败重试

### 5.3 业务回执发送异常
- [ ] 下游系统不可用处理
- [ ] 消息发送失败重试
- [ ] 回执格式错误处理

## 6. 配置管理

### 6.1 Apollo配置项
- [ ] 短信回执队列配置
- [ ] 回执超时时间配置
- [ ] 开关控制配置

### 6.2 数据库配置
- [ ] 分表策略配置
- [ ] 批次号生成规则
- [ ] 状态枚举映射

## 7. 回执处理检查清单

### 7.1 开发检查项
- [ ] 回执消息格式定义
- [ ] 状态枚举完整性
- [ ] 数据库表结构
- [ ] 监控指标配置

### 7.2 测试检查项
- [ ] 各渠道回执功能测试
- [ ] 异常场景处理测试
- [ ] 性能压力测试
- [ ] 监控告警测试

### 7.3 上线检查项
- [ ] Apollo配置验证
- [ ] 队列连接测试
- [ ] 数据库权限确认
- [ ] 监控大盘配置

---

**文档版本**: v1.0  
**创建时间**: 2025-06-20  
**维护人员**: CDP开发团队
