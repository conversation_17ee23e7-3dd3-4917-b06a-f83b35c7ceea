package com.xftech.cdp.infra.client.coupon.model.resp.getactivitylist;

import java.util.Date;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
@Data
public class ActivityDetail {

    private String id;
    private String name;
    private String activity_desc;
    private String mode;
    private String batch_id;
    // 库存
    private String coupon_count;
    // 已发送数量
    private String issued_coupon_count;
    private String coupon_id;
    private String coupon_type;
    private String sms_template_id;
    // 活动状态: 0=正常
    private String activity_status;
    private String start_time;
    private Date end_time;
    private String admin_name;
    private Date created_time;
    private Date updated_time;
    private String overdue_sms_template_id;
    private String overdue_unit;
    private String overdue_value;
    private String send_level;
    private String send_rate;

    public boolean isValid() {
        if (StringUtils.isAnyBlank(id, activity_status, coupon_count, issued_coupon_count)) {
            return false;
        }
        try {
            return StringUtils.equals(activity_status, "0") && Integer.parseInt(coupon_count) > Integer.parseInt(issued_coupon_count);
        } catch (Exception e) {
            log.warn("ActivityDetail isValid error id={} coupon_count={}, issued_coupon_count={}", id, coupon_count, issued_coupon_count);
        }
        return false;
    }

}