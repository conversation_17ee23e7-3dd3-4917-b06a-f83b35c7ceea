package com.xinfei.touch.domain.model.unified;

import com.xinfei.touch.domain.model.TouchChannel;
import com.xinfei.touch.domain.model.TouchType;
import lombok.Data;
import java.util.List;
import java.util.Map;

/**
 * 触达请求统一模型
 * 用于统一所有触达方式的入参结构
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-23
 */
@Data
public class TouchRequest {
    
    // ========== 基础信息 ==========
    /**
     * 请求唯一标识
     */
    private String requestId;
    
    /**
     * 触达类型：标识触达方式
     * T0_NORMAL - T0普通触达
     * T0_ENGINE - T0引擎触达
     * OFFLINE_NORMAL - 离线普通触达
     * OFFLINE_ENGINE - 离线引擎触达
     */
    private TouchType touchType;
    
    /**
     * 触达模式：SINGLE(单用户), BATCH(批量)
     */
    private TouchMode touchMode;
    
    /**
     * 请求时间戳
     */
    private Long timestamp;
    
    /**
     * 链路追踪ID
     */
    private String traceId;
    
    // ========== 策略信息 (源于DispatchDto + StrategyContext) ==========
    /**
     * 策略ID (源于DispatchDto.strategyId)
     */
    private Long strategyId;

    /**
     * 策略执行ID (源于DispatchDto.strategyExecId)
     */
    private String strategyExecId;

    /**
     * 策略分组ID (源于DispatchDto.strategyGroupId)
     */
    private Long strategyGroupId;

    /**
     * 策略分组名称 (源于DispatchDto.strategyGroupName)
     */
    private String strategyGroupName;

    /**
     * 策略渠道ID (源于DispatchDto.strategyChannelId)
     */
    private Long strategyChannelId;

    /**
     * 策略渠道XXL Job ID (源于DispatchDto.strategyChannelXxlJobId)
     */
    private Integer strategyChannelXxlJobId;

    /**
     * 策略渠道编码 (源于DispatchDto.strategyChannel)
     */
    private Integer strategyChannel;

    /**
     * 策略日志ID (源于DispatchDto.strategyExecLogId)
     */
    private Long strategyExecLogId;

    /**
     * 策略日志重试ID (源于DispatchDto.strategyExecLogRetryId)
     */
    private Long strategyExecLogRetryId;

    /**
     * 明细表序号 (源于DispatchDto.detailTableNo)
     */
    private String detailTableNo;

    /**
     * 消息ID (源于DispatchDto.messageId)
     */
    private String messageId;

    /**
     * 触达渠道：SMS, VOICE, PUSH, COUPON等 (源于StrategyMarketChannelEnum)
     */
    private TouchChannel channel;

    /**
     * 模板ID (源于DispatchDto.strategyMarketChannelTemplateId)
     */
    private String templateId;
    
    // ========== 用户信息 ==========
    /**
     * 单用户信息（单用户模式）
     */
    private TouchUserInfo userInfo;
    
    /**
     * 用户列表（批量模式）
     */
    private List<TouchUserInfo> userList;
    
    // ========== 业务信息 (源于BizEventVO + DispatchDto) ==========
    /**
     * 业务事件类型（T0触达必填）(源于DispatchDto.bizEventType)
     */
    private String bizEventType;

    /**
     * 业务事件数据（T0触达）(源于BizEventVO转换)
     */
    private Map<String, Object> bizEventData;

    /**
     * 事件参数映射 (源于DispatchDto.eventParamMap)
     */
    private Map<String, Object> eventParamMap;

    /**
     * 模板参数 (源于dispatchHandler.templateParam或渠道扩展信息)
     */
    private Map<String, Object> templateParams;

    /**
     * 扩展数据
     */
    private Map<String, Object> extData;

    // ========== 渠道配置信息 (源于StrategyMarketChannelDo + DispatchDto) ==========
    /**
     * 渠道扩展信息 (源于StrategyMarketChannelDo.extInfo)
     */
    private String channelExtInfo;

    /**
     * 短信签名 (源于DispatchDto.signatureKey)
     */
    private String signatureKey;

    /**
     * 优惠券活动ID (源于DispatchDto.activityId)
     */
    private String activityId;

    /**
     * 名单类型ID (源于DispatchDto.nameTypeId)
     */
    private String nameTypeId;

    /**
     * 触达类型：NOTIFY为通知不流控 (源于DispatchDto.dispatchType)
     */
    private String dispatchType;

    /**
     * 业务线类型 (源于DispatchDto.bizType)
     */
    private String bizType;
    
    // ========== 引擎信息 (源于marketingSend的groupName + detailInfo + BizEventVO) ==========
    /**
     * 引擎编码（引擎触达）(源于BizEventVO.engineCode)
     */
    private String engineCode;

    /**
     * 引擎分组名称（引擎触达）(源于marketingSend.groupName)
     */
    private String engineGroupName;

    /**
     * 引擎详细信息（引擎触达）(源于marketingSend.detailInfo)
     */
    private Map<String, Object> engineDetail;
    
    // ========== 触达配置 ==========
    /**
     * 触达配置
     */
    private TouchConfig touchConfig;

    /**
     * 流控配置
     */
    private FlowControlConfig flowControlConfig;

    // ========== 批量处理信息 (源于dispatchHandler的batch) ==========
    /**
     * 批量处理信息（批量模式）
     */
    private BatchInfo batchInfo;

    // ========== 特殊参数对象 (用于兼容现有系统) ==========
    /**
     * 提额参数DTO (源于DispatchDto.increaseAmtParamDto)
     */
    private Object increaseAmtParamDto;

    /**
     * AI即时触达参数DTO (源于DispatchDto.aiProntoChannelDto)
     */
    private Object aiProntoChannelDto;
    
    /**
     * 判断是否为单用户模式
     */
    public boolean isSingleMode() {
        return TouchMode.SINGLE.equals(touchMode);
    }
    
    /**
     * 判断是否为批量模式
     */
    public boolean isBatchMode() {
        return TouchMode.BATCH.equals(touchMode);
    }
    
    /**
     * 判断是否为T0触达
     */
    public boolean isT0Touch() {
        return TouchType.REALTIME_NORMAL.equals(touchType) || TouchType.REALTIME_ENGINE.equals(touchType);
    }

    /**
     * 判断是否为离线触达
     */
    public boolean isOfflineTouch() {
        return TouchType.OFFLINE_NORMAL.equals(touchType) || TouchType.OFFLINE_ENGINE.equals(touchType);
    }

    /**
     * 判断是否为引擎触达
     */
    public boolean isEngineTouch() {
        return TouchType.REALTIME_ENGINE.equals(touchType) || TouchType.OFFLINE_ENGINE.equals(touchType);
    }

    /**
     * 判断是否为普通触达
     */
    public boolean isNormalTouch() {
        return TouchType.REALTIME_NORMAL.equals(touchType) || TouchType.OFFLINE_NORMAL.equals(touchType);
    }
    
    /**
     * 获取用户数量
     */
    public int getUserCount() {
        if (isSingleMode()) {
            return userInfo != null ? 1 : 0;
        } else {
            return userList != null ? userList.size() : 0;
        }
    }
    
    /**
     * 验证请求参数
     */
    public void validate() {
        if (requestId == null || requestId.trim().isEmpty()) {
            throw new IllegalArgumentException("requestId不能为空");
        }
        if (touchType == null) {
            throw new IllegalArgumentException("touchType不能为空");
        }
        if (touchMode == null) {
            throw new IllegalArgumentException("touchMode不能为空");
        }
        if (strategyId == null) {
            throw new IllegalArgumentException("strategyId不能为空");
        }
        if (channel == null) {
            throw new IllegalArgumentException("channel不能为空");
        }
        
        // 验证用户信息
        if (isSingleMode()) {
            if (userInfo == null) {
                throw new IllegalArgumentException("单用户模式下userInfo不能为空");
            }
            userInfo.validate();
        } else {
            if (userList == null || userList.isEmpty()) {
                throw new IllegalArgumentException("批量模式下userList不能为空");
            }
            for (TouchUserInfo user : userList) {
                user.validate();
            }
        }
        
        // T0触达必须有业务事件类型
        if (isT0Touch() && (bizEventType == null || bizEventType.trim().isEmpty())) {
            throw new IllegalArgumentException("T0触达必须指定bizEventType");
        }
    }
}
