/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2025. All Rights Reserved.
 */

package com.xftech.cdp.distribute.oss;


import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.alibaba.fastjson2.JSON;
import com.dianping.cat.proxy.Tracer;
import com.dianping.cat.proxy.spi.Transaction;
import com.xftech.cdp.distribute.config.BucketConfig;
import com.xftech.cdp.distribute.config.DistributeConfigService;
import com.xftech.cdp.distribute.oss.dto.OssContentResponse;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.*;
import com.aliyun.oss.common.auth.CredentialsProvider;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;
import com.aliyun.oss.common.comm.SignVersion;
import com.aliyun.oss.model.GetObjectRequest;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.SimplifiedObjectMeta;
import com.xftech.cdp.infra.utils.WhitelistSwitchUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 读取OSS文件范围下载，封装向后补全功能
 */
@Slf4j
@Component
public class RangeReadFileService {

    @Autowired
    private DistributeConfigService distributeConfigService;

    private final String HEADER_NAME = "__header__";
    private final String HEADER_STR = "|#DI_HEADER#|";
    private final String END_NAME = "__footer__";
    private final String END_STR = "|#DI_FOOTER#|";

    /**
     * @param ossUri
     * @param startPos
     * @param endPos
     * @return 文件按行读取，返回List<String>
     * @throws Exception
     */
    public OssContentResponse readByRange(String ossUri, long startPos, long endPos) throws Exception {
        Transaction transaction = Tracer.newTransaction("OssReadFileService", "readByRange");
        BucketConfig bucketConfig = distributeConfigService.getOssBucketConfig();
        if (bucketConfig == null) {
            throw new Exception("oss bucket config is null");
        }
        log.info("readByRange ossUri:{} startPos:{} endPos:{}", ossUri, startPos, endPos);
        // 使用DefaultCredentialProvider方法直接设置AK和SK
        CredentialsProvider credentialsProvider = new DefaultCredentialProvider(bucketConfig.getAccessKeyId(), bucketConfig.getAccessKeySecret());

        // 创建OSSClient实例
        ClientBuilderConfiguration clientBuilderConfiguration = new ClientBuilderConfiguration();
        clientBuilderConfiguration.setSignatureVersion(SignVersion.V4);
        OSS ossClient = OSSClientBuilder.create()
                .endpoint(bucketConfig.getEndpoint())
                .credentialsProvider(credentialsProvider)
                .clientConfiguration(clientBuilderConfiguration)
                .region(bucketConfig.getRegion())
                .build();

        InputStream bodyInputStream = null;
        OssContentResponse ossContentResponse = new OssContentResponse(true, "success");
        try {
            // 范围下载体范围
            GetObjectRequest getBodyRequest = buildRequest(bucketConfig.getBucketName(), ossUri, startPos, endPos);
            OSSObject ossBodyObject = ossClient.getObject(getBodyRequest);

            int resultCode = ossBodyObject.getResponse().getStatusCode();
            if (resultCode != 200 && resultCode != 206) { // 206:范围末端取值不在有效区间
                log.error("范围下载失败，状态码：{} fileUri:{} startPos:{} endPos:{}", resultCode, ossUri, startPos, endPos);
                return new OssContentResponse(false, "范围下载失败，状态码：" + resultCode);
            }
            // 读取文件主体内容
            boolean fixTailLine = buildBodyResponse(ossBodyObject, ossContentResponse, startPos);
            if(!ossContentResponse.isSuccess()) {
                throw new Exception("OSS文件读取文件体失败！"+ ossUri + " " + startPos + " " + endPos);
            }
            // 读取header
            if(CollectionUtils.isEmpty(ossContentResponse.getColumnNames())) {
                GetObjectRequest getHeaderRequest = buildRequest(bucketConfig.getBucketName(), ossUri, 0L,
                        distributeConfigService.getOssSpecialLineReadLength());
                OSSObject ossHeaderObject = ossClient.getObject(getHeaderRequest);
                buildHeaderResponse(ossHeaderObject, ossContentResponse);
            }

            // 补充最后一行
            if(fixTailLine) {
                GetObjectRequest getTailRequest = buildRequest(bucketConfig.getBucketName(), ossUri, endPos + 1L,
                        endPos + distributeConfigService.getOssSpecialLineReadLength());
                OSSObject ossHeaderObject = ossClient.getObject(getTailRequest);
                fixTailLine(ossHeaderObject, ossContentResponse);
            }
            if(WhitelistSwitchUtil.boolSwitchByApollo("readByRangeContentEnable")) {
                log.info("readByRange ossContentResponse:{}", ossContentResponse);
            }
            return ossContentResponse;
        } catch (OSSException oe) {
            log.error("OSSException, request rejected with an error response:{} errorCode:{} requestId:{} hostId:{}", oe.getErrorMessage(),
                    oe.getErrorCode(), oe.getRequestId(), oe.getHostId(), oe);
            transaction.setStatus(oe);
            return new OssContentResponse(false, "OSSException范围下载失败，状态码:" +oe.getErrorCode() + " " + oe.getMessage());
        } catch (ClientException ce) {
            log.error("ClientException errorCode:{} message:{}", ce.getErrorCode(), ce.getMessage());
            transaction.setStatus(ce);
            return new OssContentResponse(false, "ClientException范围下载失败，状态码:" + ce.getErrorCode() + " " + ce.getMessage());
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
            transaction.complete();
        }
    }

    private void buildHeaderResponse(OSSObject ossObject, OssContentResponse ossContentResponse) throws Exception {
        log.info("buildHeaderResponse ossContentResponse ColumnNames:{} contentSize:{}", ossContentResponse.getColumnNames(),
                ossContentResponse.getContentList() == null ? 0 : ossContentResponse.getContentList().size());
        InputStream headerInputStream = null;
        try {
            headerInputStream = ossObject.getObjectContent();
            List<String> fileContentList = bufferProcess(headerInputStream);
            // 解析header字段名列表
            if(CollectionUtils.isNotEmpty(fileContentList) && fileContentList.size() > 1) {
                String headerStr = fileContentList.get(0);
                if(StringUtils.isBlank(headerStr)) {
                    ossContentResponse.setSuccess(Boolean.FALSE);
                    return;
                }
                List<String> headerList = Arrays.asList(headerStr.split(","));
                if(headerList.size() > 2 && headerList.get(0).equals(HEADER_NAME) && headerList.get(headerList.size() -1).equals(END_NAME) ){
                    ossContentResponse.setColumnNames(headerList);
                } else {
                    // 不满足条件，标记读取文件失败
                    ossContentResponse.setSuccess(Boolean.FALSE);
                }
            }
        } finally {
            ossObject.close();
            if (headerInputStream != null) {
                headerInputStream.close();
            }
        }
    }

    private void fixTailLine(OSSObject ossObject, OssContentResponse ossContentResponse) throws Exception {
        log.info("fixTailLine ossContentResponse:{}", ossContentResponse.getColumnNames());
        InputStream fixTailInputStream = null;
        try {
            fixTailInputStream = ossObject.getObjectContent();
            List<String> fixTailContentList = bufferProcess(fixTailInputStream);
            List<String> contentList = ossContentResponse.getContentList();
            String lastLine = contentList.get(contentList.size() - 1);
            String fixTailContent = fixTailContentList.get(0);
            String fullLine = lastLine + fixTailContent;
            // 解析header字段名列表
            if(StringUtils.isNotBlank(fullLine) && fullLine.startsWith(HEADER_STR) && fullLine.endsWith(END_STR)) {
                String[] lastLineArr = fullLine.split(",");
                if(lastLineArr.length == ossContentResponse.getColumnNames().size()) {
                    // 补全满足校验
                    contentList.set(contentList.size() - 1, fullLine);
                }
            } else {
                // 不满足条件，标记读取文件失败
                ossContentResponse.setSuccess(Boolean.FALSE);
            }

        } finally {
            ossObject.close();
            if (fixTailInputStream != null) {
                fixTailInputStream.close();
            }
        }
    }

    /**
     *
     * @param ossObject
     * @param ossContentResponse
     * @return 是否继续补充最后一行
     * @throws Exception
     */
    private boolean buildBodyResponse(OSSObject ossObject, OssContentResponse ossContentResponse, long bodyStartPos) throws Exception {
        log.info("buildBodyResponse ossObject:{} ossContentResponse:{} bodyStartPos:{}", ossObject, ossContentResponse, bodyStartPos);
        InputStream bodyInputStream = null;
        boolean requestNextPart = true;
        try {
            String bucketName = ossObject.getBucketName();
            bodyInputStream = ossObject.getObjectContent();
            List<String> fileContentList = bufferProcess(bodyInputStream);
            if(CollectionUtils.isNotEmpty(fileContentList) && fileContentList.size() >= 1) {
                ossContentResponse.setContentList(fileContentList);
                String firstLine = fileContentList.get(0);
                if(StringUtils.isNotBlank(firstLine) && !firstLine.startsWith(HEADER_STR)) {
                    log.info("buildBodyResponse firstLine:{}", firstLine);
                    // 第一行不符合预期，从body去除
                    fileContentList.remove(0);
                    if(bodyStartPos == 0L && firstLine.startsWith(HEADER_NAME) && firstLine.endsWith(END_NAME)) {
                        // 完整的header行
                        ossContentResponse.setColumnNames(Arrays.asList(firstLine.split(",")));
                    }
                    if(bodyStartPos == 0L && CollectionUtils.isEmpty(fileContentList)) {
                        // 空人群包文件内容为空
                        requestNextPart = false;
                        return requestNextPart;
                    }
                }


                String endLine = fileContentList.get(fileContentList.size() -1);
                if(StringUtils.isNotBlank(endLine) && endLine.endsWith(END_STR)) {
                    // 完整行结尾内容
                    ossContentResponse.setContentList(fileContentList);
                    requestNextPart = false;
                }
            } else {
                ossContentResponse.setSuccess(Boolean.FALSE);
            }

            ossContentResponse.setBucketName(bucketName);
        } finally {
            ossObject.close();
            if(bodyInputStream != null) {
                bodyInputStream.close();
            }
        }
        return requestNextPart;
    }

    private GetObjectRequest buildRequest(String bucketName, String ossUri, Long startPos, Long endPos) throws Exception {
        GetObjectRequest getObjectRequest = new GetObjectRequest(bucketName, ossUri);
        getObjectRequest.addHeader("x-oss-range-behavior", "standard");
        getObjectRequest.setRange(startPos, endPos);

        return getObjectRequest;
    }

//    private void processByte(InputStream inputStream) throws Exception {
//        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
//        // 读取文件内容到字节数组。
//        byte[] readBuffer = new byte[1024];
//        int bytesRead;
//        while ((bytesRead = inputStream.read(readBuffer)) != -1) {
//            byteArrayOutputStream.write(readBuffer, 0, bytesRead);
//        }
//        // 获取最终的字节数组。
//        byte[] fileBytes = byteArrayOutputStream.toByteArray();
//        // 打印字节数组的长度。
//        log.info("Downloaded file size: " + fileBytes.length + " bytes");
//        // 数据读取完成后，获取的流必须关闭，否则会造成连接泄漏，导致请求无连接可用，程序无法正常工作。
//        inputStream.close();
//
//    }

    /**
     * 按行读取文件内容，返回List<String>
     * __header__,inner_app,ab_num,app_user_id,register_time,mobile,app,app_user_id_last2,run_version,__footer__
     * |#DI_HEADER#|,fxk_h5,675328575607675727091268899100,1012100359,2020-12-10 16:48:35+08,lM4YqI7lgsuL9LfVKRikWA==,fxk,59,20250509_2,|#DI_FOOTER#|
     * |#DI_HEADER#|,fxk_h5,585444860242417746128484036068,1012190698,2020-12-12 07:49:01+08,UK8507iWSkOPjDZZKTTQow==,fxk,98,20250509_2,|#DI_FOOTER#|
     * |#DI_HEADER#|,fxk_h5,422349510646310323143047733609,1012789153,2020-12-21 20:13:36+08,4Jw3s/y1Px1ezA3S73CR7Q==,fxk,53,20250509_2,|#DI_FOOTER#|
     */
    private List<String> bufferProcess(InputStream inputStream) throws Exception {
        if (inputStream == null) {
            throw new Exception("input stream is null");
        }
        List<String> fileContentList = new ArrayList<>();
        BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream));
        String line;
        while ((line = bufferedReader.readLine()) != null) {
            if (StringUtils.isNotBlank(line)) {

                fileContentList.add(line);
            }
        }

        return fileContentList;
    }

    /**
     * 获取文件大小
     *
     * @param ossFilePath oss文件路径:目录/文件名
     * @return
     */
    public long getOssFileSize(String ossFilePath) {
        if (StringUtils.isBlank(ossFilePath)) {
            return 0;
        }
        BucketConfig bucketConfig = distributeConfigService.getOssBucketConfig();
        // 使用DefaultCredentialProvider方法直接设置AK和SK
        CredentialsProvider credentialsProvider = new DefaultCredentialProvider(bucketConfig.getAccessKeyId(), bucketConfig.getAccessKeySecret());
        // 创建OSSClient实例
        ClientBuilderConfiguration clientBuilderConfiguration = new ClientBuilderConfiguration();
        clientBuilderConfiguration.setSignatureVersion(SignVersion.V4);
        OSS ossClient = OSSClientBuilder.create()
                .endpoint(bucketConfig.getEndpoint())
                .credentialsProvider(credentialsProvider)
                .clientConfiguration(clientBuilderConfiguration)
                .region(bucketConfig.getRegion())
                .build();
        try {
            // 获取文件的部分元数据
            SimplifiedObjectMeta objectMeta = ossClient.getSimplifiedObjectMeta(bucketConfig.getBucketName(), ossFilePath);
            log.info("RangeReadFileService getOssFileSize ossFilePath={} objectMeta={}", ossFilePath, JSONObject.toJSONString(objectMeta));
            if (objectMeta != null) {
                return objectMeta.getSize();
            }
        } catch (OSSException oe) {
            log.error("RangeReadFileService getOssFileSize OSSException ossFilePath={} errorCode:{} errorMessage:{} requestId:{} hostId:{}", ossFilePath, oe.getErrorCode(), oe.getErrorMessage(), oe.getRequestId(), oe.getHostId());
        } catch (ClientException ce) {
            log.error("RangeReadFileService getOssFileSize ClientException ossFilePath={} errorCode:{} errorMessage:{}", ossFilePath, ce.getErrorCode(), ce.getMessage());
        } catch (Exception e) {
            log.error("RangeReadFileService getOssFileSize Exception ossFilePath={}", ossFilePath, e);
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return 0;
    }

}
