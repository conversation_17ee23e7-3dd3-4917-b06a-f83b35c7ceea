package com.xftech.cdp.infra.pulsar;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import com.xftech.cdp.BaseTest;

import com.alibaba.fastjson.JSONObject;
import com.xyf.cis.SecureClient;
import com.xyf.cis.dto.SecureEncryptDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Test;

public class TestSecureClient extends BaseTest {

    @Resource
    private SecureClient secureClient;

    @Test
    public void batchEncrypt() {
        String mobile = "19937587207";
        List<SecureEncryptDTO> secureEncryptDTOS = secureClient.batchEncrypt(Arrays.asList(mobile));
        if (CollectionUtils.isNotEmpty(secureEncryptDTOS)) {
            logger.info("==========secureEncryptDTOS==========:{}", JSONObject.toJSONString(secureEncryptDTOS));
        }
    }

    @Test
    public void batchDecrypt() {
        String mobile_cipher = "t2XSmylIfvOiC86apCDkEw==";
        List<SecureEncryptDTO> secureEncryptDTOS = secureClient.batchDecrypt(Arrays.asList(mobile_cipher));
        if (CollectionUtils.isNotEmpty(secureEncryptDTOS)) {
            logger.info("==========secureEncryptDTOS==========:{}", JSONObject.toJSONString(secureEncryptDTOS));
        }
    }

}
