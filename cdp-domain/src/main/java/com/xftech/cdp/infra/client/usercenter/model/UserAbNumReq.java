package com.xftech.cdp.infra.client.usercenter.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-05-15
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class UserAbNumReq {
    private static final long serialVersionUID = 1L;

    @JsonProperty("mobiles")
    @SerializedName("mobiles")
    private List<String> mobiles;
}
