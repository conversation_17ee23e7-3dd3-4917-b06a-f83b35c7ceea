<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="userBlankGroupDetailMapper">

    <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.strategy.po.UserBlankGroupDetailDo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="userId" column="user_id" jdbcType="BIGINT"/>
        <result property="batchNum" column="batch_num" jdbcType="VARCHAR"/>
        <result property="crowdPackId" column="crowd_pack_id" jdbcType="BIGINT"/>
        <result property="strategyId" column="strategy_id" jdbcType="BIGINT"/>
        <result property="strategyChannelId" column="strategy_channel_id" jdbcType="BIGINT"/>
        <result property="marketChannel" column="market_channel" jdbcType="SMALLINT"/>
        <result property="strategyExecId" column="strategy_exec_id" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="SMALLINT"/>
        <result property="dispatchTime" column="dispatch_time" jdbcType="TIMESTAMP"/>
        <result property="messageId" column="message_id" jdbcType="VARCHAR"/>
        <result property="triggerDatetime" column="trigger_datetime" jdbcType="TIMESTAMP"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
        <result property="groupName" column="group_name" jdbcType="VARCHAR"/>
        <result property="strategyGroupId" column="strategy_group_id" jdbcType="BIGINT"/>
        <result property="strategyGroupName" column="strategy_group_name" jdbcType="VARCHAR"/>
        <result column="ext_detail" jdbcType="VARCHAR" property="extDetail" />
        <result column="union_id" jdbcType="VARCHAR" property="unionId"/>
    </resultMap>

    <resultMap id="FlowCtrlGroupNum" type="com.xftech.cdp.infra.repository.cdp.flowctrl.po.FlowCtrlGroupNum">
        <result property="strategyChannelId" column="strategy_channel_id" jdbcType="BIGINT"/>
        <result property="userNum" column="user_num" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,
        batch_num,crowd_pack_id,strategy_id,
        strategy_channel_id,market_channel,strategy_exec_id,
        status,dispatch_time,message_id,trigger_datetime,created_time,
        updated_time,group_name,strategy_group_id,strategy_group_name,union_id
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ${tableName}
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="todayExecuted" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ${tableName}
        where strategy_id = #{strategyId} and date(updated_time) = current_date() limit 1
    </select>

    <select id="countUserByChannelIdAndDispatchTime" resultType="java.lang.Integer">
        select count(user_id)
        from ${tableName}
        where strategy_channel_id = #{channelId}
          and dispatch_time between #{dispatchDate} and #{endDispatchDate}
    </select>

    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.UserBlankGroupDetailDo"
            useGeneratedKeys="true">
        insert into ${tableName}
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null">user_id,</if>
            <if test="batchNum != null">batch_num,</if>
            <if test="crowdPackId != null">crowd_pack_id,</if>
            <if test="strategyId != null">strategy_id,</if>
            <if test="strategyChannelId != null">strategy_channel_id,</if>
            <if test="marketChannel != null">market_channel,</if>
            <if test="strategyExecId != null">strategy_exec_id,</if>
            <if test="status != null">status,</if>
            <if test="dispatchTime != null">dispatch_time,</if>
            <if test="messageId != null">message_id,</if>
            <if test="triggerDatetime != null">trigger_datetime,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updatedTime != null">updated_time,</if>
            <if test="groupName != null">group_name,</if>
            <if test="bizEventType != null">biz_event_type,</if>
            <if test="strategyGroupId != null">strategy_group_id,</if>
            <if test="strategyGroupName != null">strategy_group_name,</if>
            <if test="extDetail != null">ext_detail,</if>
            <if test="unionId != null">union_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="userId != null">#{userId,jdbcType=BIGINT},</if>
            <if test="batchNum != null">#{batchNum,jdbcType=VARCHAR},</if>
            <if test="crowdPackId != null">#{crowdPackId,jdbcType=BIGINT},</if>
            <if test="strategyId != null">#{strategyId,jdbcType=BIGINT},</if>
            <if test="strategyChannelId != null">#{strategyChannelId,jdbcType=BIGINT},</if>
            <if test="marketChannel != null">#{marketChannel,jdbcType=SMALLINT},</if>
            <if test="strategyExecId != null">#{strategyExecId,jdbcType=VARCHAR},</if>
            <if test="status != null">#{status,jdbcType=SMALLINT},</if>
            <if test="dispatchTime != null">#{dispatchTime,jdbcType=TIMESTAMP},</if>
            <if test="messageId != null">#{messageId,jdbcType=VARCHAR},</if>
            <if test="triggerDatetime != null">#{triggerDatetime,jdbcType=TIMESTAMP},</if>
            <if test="createdTime != null">#{createdTime,jdbcType=TIMESTAMP},</if>
            <if test="updatedTime != null">#{updatedTime,jdbcType=TIMESTAMP},</if>
            <if test="groupName != null">#{groupName,jdbcType=VARCHAR},</if>
            <if test="bizEventType != null">#{bizEventType,jdbcType=VARCHAR},</if>
            <if test="strategyGroupId != null">#{strategyGroupId,jdbcType=BIGINT},</if>
            <if test="strategyGroupName != null">#{strategyGroupName,jdbcType=VARCHAR},</if>
            <if test="extDetail != null">#{extDetail,jdbcType=VARCHAR},</if>
            <if test="unionId != null">#{unionId,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.UserBlankGroupDetailDo">
        update ${tableName}
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="batchNum != null">
                batch_num = #{batchNum,jdbcType=VARCHAR},
            </if>
            <if test="crowdPackId != null">
                crowd_pack_id = #{crowdPackId,jdbcType=BIGINT},
            </if>
            <if test="strategyId != null">
                strategy_id = #{strategyId,jdbcType=BIGINT},
            </if>
            <if test="strategyChannelId != null">
                strategy_channel_id = #{strategyChannelId,jdbcType=BIGINT},
            </if>
            <if test="marketChannel != null">
                market_channel = #{marketChannel,jdbcType=SMALLINT},
            </if>
            <if test="strategyExecId != null">
                strategy_exec_id = #{strategyExecId,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=SMALLINT},
            </if>
            <if test="dispatchTime != null">
                dispatch_time = #{dispatchTime,jdbcType=TIMESTAMP},
            </if>
            <if test="messageId != null">
                message_id = #{messageId,jdbcType=VARCHAR},
            </if>
            <if test="triggerDatetime != null">
                trigger_datetime = #{triggerDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedTime != null">
                updated_time = #{updatedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="groupName != null">
                group_name = #{groupName,jdbcType=VARCHAR},
            </if>
            <if test="strategyGroupId != null">
                strategy_group_id = #{strategyGroupId,jdbcType=BIGINT},
            </if>
            <if test="strategyGroupName != null">
                strategy_group_name = #{strategyGroupName,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="setFailStatusByStrategyId">
        update ${tableName}
        set status       = #{status},
            updated_time = NOW()
        where strategy_id = #{strategyId}
          and status = -1
          and date(created_time) = current_date()
        limit 500
    </update>

    <!-- 策略留白分组数据统计 -->
    <select id="countUserBlankGroupData" resultType="integer">
        select count(distinct user_id) as user_num
        from ${tableName}
        where strategy_id = #{strategyId}
        <if test="strategyChannelIds !=null and strategyChannelIds.size()>0">
            and strategy_channel_id in
            <foreach collection="strategyChannelIds" item="strategyChannelId" open="(" close=")" separator=",">
                #{strategyChannelId}
            </foreach>
        </if>
        and trigger_datetime &gt;= #{startDate}
        and trigger_datetime &lt; #{endDate}
    </select>
</mapper>
