package com.xftech.cdp.feign;

import java.util.List;

import com.xftech.cdp.feign.common.BizBaseResponse;
import com.xftech.cdp.feign.interceptor.DataInsightFeignClientInterceptor;
import com.xftech.cdp.feign.model.datainsight.request.CrowPushResultRequest;
import com.xftech.cdp.feign.model.datainsight.response.CrowdPushResponse;
import com.xftech.cdp.feign.model.requset.DpsPageRequest;
import com.xftech.cdp.feign.model.response.DpsResponse;
import com.xftech.cdp.feign.model.response.MetaLabelDto;
import com.xftech.cdp.infra.client.ads.model.req.AdsCrowdReq;
import com.xftech.cdp.infra.client.ads.model.resp.CrowdTotalResp;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "datainsight-service", contextId = "datainsight-service" + ".FeignClient", path = "/mng", configuration = DataInsightFeignClientInterceptor.class)
public interface DataInsightFeignClient {

    /**
     * 查询标签元数据
     */
    @PostMapping("/api/crowd_label/by_page")
    DpsResponse<List<MetaLabelDto>> queryLabelList(@RequestBody DpsPageRequest request);

    /**
     * 人群包sql推送
     */
    @PostMapping("/crowd/runCrowdTask")
    BizBaseResponse<CrowdPushResponse> pushCrowd(@RequestBody AdsCrowdReq request);

    /**
     * 人群包sql推送-结果查询
     */
    @PostMapping("/crowd/queryCrowdJobStatus")
    BizBaseResponse<List<CrowdTotalResp>> getCrowdTotal(@RequestBody CrowPushResultRequest request);

}
