package com.xftech.cdp.infra.client.ads.model.resp;

import java.util.*;

import com.xftech.cdp.infra.config.ApolloUtil;
import com.xftech.cdp.infra.utils.JsonUtil;

import com.google.common.collect.Sets;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

@Data
public class AdbRealTimeResp {

    private static final String NULL = "null";
    private static final Set<Object> FAIL9999 = Sets.newHashSet("-9999", "-9999.0", -9999, -9999.0);
    private static final Set<Object> FAIL999 = Sets.newHashSet("-999", "-999.0", -999, -999.0);

    private String requestId;
    private String variablesResponse;
    private String message;
    private String code;
    private String stage;

    public boolean isSuccess() {
        return Objects.equals("10000", code);
    }

    public List<AdsLabelResp> convertVariables(List<String> labels, Long appUserId, String mobile) {
        List<AdsLabelResp> adsLabelResps = new ArrayList<>();
        Map<String, Object> labelMap;
        if (StringUtils.isNotEmpty(variablesResponse)) {
            labelMap = JsonUtil.parse(variablesResponse, Map.class);
            if (!CollectionUtils.isEmpty(labelMap)) {
                for (String lb : labels) {
                    AdsLabelResp adsLabelResp = new AdsLabelResp();
                    List<AdsLabelResp.Param> params = new ArrayList<>();
                    adsLabelResp.setLabel(lb);
                    AdsLabelResp.Param param = new AdsLabelResp.Param();
                    if (labelMap.containsKey(lb)) {
                        Object v = labelMap.get(lb);
                        if (v != null && !StringUtils.equalsIgnoreCase(NULL, v.toString())) {
                            param.setResult(v.toString());
                        }
                        // 变量中心无值返回 结果置为null
                        if (v != null && StringUtils.isBlank(v.toString()) && ApolloUtil.getAppListProperty("ads.feature.fallback.labels.blank").contains(lb)) {
                            param.setResult(null);
                        }
                        // 变量中心异常兜底值-9999 结果置为null
                        if (FAIL9999.contains(v) && ApolloUtil.getAppListProperty("ads.feature.fallback.labels.9999").contains(lb)) {
                            param.setResult(null);
                        }
                        // 变量中心异常兜底值-999 结果置为null
                        if (FAIL999.contains(v) && ApolloUtil.getAppListProperty("ads.feature.fallback.labels.999").contains(lb)) {
                            param.setResult(null);
                        }
                        param.setMobile(mobile);
                        param.setAppUserId(appUserId);
                        params.add(param);
                        adsLabelResp.setParams(params);
                        adsLabelResps.add(adsLabelResp);
                    }
                }
            }
        }
        if (!CollectionUtils.isEmpty(adsLabelResps)) {
            return adsLabelResps;
        }
        return null;
    }

}
