package com.xftech.cdp.domain.marketing.repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.infra.repository.cdp.marketing.po.MarketingActivityUserDetailDo;

import org.springframework.stereotype.Repository;


/**
 * 营销活动用户参与明细表 marketing_activity_user_detail dao
 */
@Repository
public class MarketingActivityUserDetailRepository {

    /**
     * 根据活动ID和用户ID查询参与明细
     *
     * @param activityId 活动ID
     * @param userId     用户ID
     * @return
     */
    public List<MarketingActivityUserDetailDo> selectByActivityAndUserId(Long activityId, Long userId, Integer rewardType) {
        Map<String, Object> param = new HashMap<>();
        param.put("activityId", activityId);
        param.put("userId", userId);
        param.put("rewardType", rewardType);
        return DBUtil.selectList("marketingActivityUserDetail.selectByActivityAndUserId", param);
    }

    public void insert(MarketingActivityUserDetailDo marketingActivityUserDetailDo) {
        DBUtil.insert("marketingActivityUserDetail.insertSelective", marketingActivityUserDetailDo);
    }

}