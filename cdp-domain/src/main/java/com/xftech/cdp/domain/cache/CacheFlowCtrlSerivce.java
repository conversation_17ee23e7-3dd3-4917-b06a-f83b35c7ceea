package com.xftech.cdp.domain.cache;

import com.xftech.cdp.infra.repository.cdp.flowctrl.po.FlowCtrlDo;

import java.util.List;

public interface CacheFlowCtrlSerivce {

    /**
     * 插入
     *
     * @param flowCtrlDo 对象
     * @return 是否插入成功标识
     */
    boolean insert(FlowCtrlDo flowCtrlDo);

    /**
     * 根据主键id更新
     *
     * @param flowCtrlDo 对象
     * @return 是否更新成功标识
     */
    boolean updateById(FlowCtrlDo flowCtrlDo);

    boolean updateStatusById(FlowCtrlDo flowCtrlDo);

    void closeByStrategyId(Long strategyId);

    /**
     * 获取流控规则配置
     *
     * @param marketChannel 渠道
     * @param strategyId    策略ID
     * @return 流控规则配置
     */
    List<FlowCtrlDo> getFlowCtrlConfig(Integer marketChannel, Long strategyId, String bizType);

   void updateFlowCtrlRuleCache(String effectiveStrategy);

    List<FlowCtrlDo> getFlowCtrlConfigs(Integer strategyType, Long strategyId);
}
