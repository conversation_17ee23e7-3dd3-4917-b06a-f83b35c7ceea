package com.xftech.cdp.infra.rabbitmq.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * RabbitMq 配置
 * <AUTHOR>
 * @since 2023-04-06
 */
@Configuration
@RefreshScope
@Getter
@Setter
public class CouponMqConfig {
    /**
     * mq批量消费大小
     */
    @Value("${spring.rabbitmq.coupon.batchSize:50}")
    private Integer batchSize;
    /**
     * mq消费超时时间
     */
    @Value("${spring.rabbitmq.coupon.timeout:10000}")
    private Integer timeout;

    @Value("${spring.rabbitmq.host2}")
    private String host2;
    @Value("${spring.rabbitmq.port2}")
    private int port2;
    @Value("${spring.rabbitmq.username2}")
    private String username2;
    @Value("${spring.rabbitmq.password2}")
    private String password2;
    @Value("${spring.rabbitmq.virtual-host2}")
    private String virtualHost2;

    @Value("${coupon.callback.exchange}")
    private String couponCallbackExchange;
    @Value("${coupon.callback.exchangeType}")
    private String couponExchangeType;
    @Value("${coupon.callback.routingKey}")
    private String couponCallbackRoutingKey;
    @Value("${coupon.callback.queue.name}")
    private String couponCallbackQueueName;

    @Value("${coupon.callback.deadLetterExchange}")
    private String deadLetterExchange;
    @Value("${coupon.callback.deadLetterExchangeType}")
    private String deadLetterExchangeType;
    @Value("${coupon.callback.deadLetterRoutingKey}")
    private String deadLetterRoutingKey;
    @Value("${coupon.callback.deadLetterQueueName}")
    private String deadLetterQueueName;
}
