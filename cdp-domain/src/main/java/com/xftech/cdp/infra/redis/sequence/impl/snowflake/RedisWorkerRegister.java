package com.xftech.cdp.infra.redis.sequence.impl.snowflake;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.DependsOn;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

@Component
@DependsOn("redisTemplate")
public class RedisWorkerRegister implements WorkerIdRegister {

    private final static String WORKER_REGISTRY = "worker_registry";
    private final static String WORKER_ID_SET = "worker_id_set";
    private final Logger logger = LoggerFactory.getLogger(RedisWorkerRegister.class);
    private Instance instance;

    private String uniqueKey;

    @Resource
    private RedisTemplate redisTemplate;

    @Override
    public Optional<Integer> registerWorker(String ip, int port, String applicationName) {
        uniqueKey = ip + ":" + port;
        instance = (Instance) redisTemplate.opsForHash().get(WORKER_REGISTRY, uniqueKey);

        if (instance != null) {

            if (System.currentTimeMillis() < instance.getUpTime()) {
                logger.warn("时钟回退IP：{}，PORT:{}", ip, port);
                return Optional.empty();
            }

            instance.setUpTime(System.currentTimeMillis());
            redisTemplate.opsForHash().put(WORKER_REGISTRY, uniqueKey, instance);
            ScheduledUploadData();

            return Optional.of(instance.getWorkerId());
        } else {

            Set<Integer> set = redisTemplate.opsForSet().members(WORKER_ID_SET);
            int workId = set.stream().map(i -> i + 1).max(Integer::compareTo).orElse(1);
            Long result = redisTemplate.opsForSet().add(WORKER_ID_SET, workId);

            if (result == 0) {
                return Optional.empty();
            } else {
                instance = new Instance();
                instance.setUpTime(System.currentTimeMillis());
                instance.setWorkerId(workId);

                instance.setApplicationName(applicationName);
                instance.setIp(ip);
                instance.setPort(port);

                redisTemplate.opsForHash().put(WORKER_REGISTRY, uniqueKey, instance);

                ScheduledUploadData();
                return Optional.of(workId);
            }

        }
    }

    private void ScheduledUploadData() {
        Executors.newSingleThreadScheduledExecutor(r -> {
            Thread thread = new Thread(r, "schedule-upload-time");
            thread.setDaemon(true);
            return thread;
        }).scheduleWithFixedDelay(() -> upTime(), 1L, 3L, TimeUnit.SECONDS);//每3s上报数据

    }

    private void upTime() {
        instance.setUpTime(System.currentTimeMillis());
        redisTemplate.opsForHash().put(WORKER_REGISTRY, uniqueKey, instance);
    }


}
