# 离线-引擎触达: StrategyEventDispatchServiceImpl.marketingSend 整体链路分析报告

## 1. 概述

本文档详细分析离线引擎触达的完整链路，从定时任务调度开始，到最终的营销触达执行，重点分析`StrategyEventDispatchServiceImpl.marketingSend`方法的调用链路和业务逻辑。离线引擎触达是一个复杂的分布式系统，涉及任务调度、分片处理、引擎决策、异步执行、流控管理等多个技术层面。

## 2. 整体架构图

### 2.1 完整调用链路图

```mermaid
graph TD
    A["XxlJob定时任务<br/>OFFLINE_ENGINE_DISTRIBUTE_TASK_GENERATE<br/><small>定时扫描有效策略，生成分片任务</small>"] --> B["OfflineStrategyJobDispatch<br/>.strategySliceTask()<br/><small>查询引擎策略列表，过滤有效期内策略</small>"]
    B --> C["StrategyTaskDistributeHandler<br/>.generateDispatchTask()<br/><small>遍历策略，为每个策略创建分片任务</small>"]
    C --> D["StrategyTaskDistributeHandler<br/>.createStrategyDistributeSliceTasks()<br/><small>根据人群分片创建执行任务记录</small>"]
    D --> E["插入strategy_slice_exec_log表<br/>(status=0)<br/><small>记录分片任务初始状态，等待执行</small>"]

    F["XxlJob定时任务<br/>OFFLINE_ENGINE_DISTRIBUTE_EXECUTE<br/><small>分布式执行分片任务，支持多节点并行</small>"] --> G["OfflineStrategyJobDispatch<br/>.strategySliceTaskExecute()<br/><small>获取分片信息，按节点分配任务</small>"]
    G --> H["StrategyTaskDistributeHandler<br/>.execDistributeSliceTask()<br/><small>CRC32哈希路由，筛选当前节点任务</small>"]
    H --> I["StrategyTaskDistributeHandler<br/>.dispatchTaskExecuting()<br/><small>分布式锁控制，防止重复执行</small>"]
    I --> J["DistributeOfflineEngineDispatchServiceImpl<br/>.execute()<br/><small>分片执行入口，初始化执行上下文</small>"]

    J --> K["DistributeOfflineEngineDispatchServiceImpl<br/>.coreLogicExecute()<br/><small>构建AB测试分组匹配规则</small>"]
    K --> L["DistributeOfflineEngineDispatchServiceImpl<br/>.batchDispatch()<br/><small>分页读取人群数据，避免内存溢出</small>"]
    L --> M["DistributeOfflineEngineDispatchServiceImpl<br/>.filterAndDispatch()<br/><small>用户去重、AB分组、标签过滤</small>"]
    M --> N["DistributeOfflineEngineDispatchServiceImpl<br/>.dispatchHandler()<br/><small>批量异步调用引擎，等待所有结果</small>"]

    N --> O["DispatchOfflineEngineService<br/>.pushEngine()<br/><small>构建引擎请求，异步调用预测中心</small>"]
    O --> P["EnginePredictionClient<br/>.modelPrediction()<br/><small>调用AI引擎，获取营销决策结果</small>"]
    P --> Q["DispatchOfflineEngineService<br/>.processEngineDecision()<br/><small>解析引擎决策，创建延迟执行任务</small>"]
    Q --> R["插入dispatch_user_delay表<br/>(status=0)<br/><small>记录延迟任务，等待指定时间执行</small>"]

    S["XxlJob定时任务<br/>DISPATCH_USER_DELAY_EXECUTE<br/><small>扫描到期延迟任务，执行营销触达</small>"] --> T["DispatchUserDelayJob<br/>.executeDelayTasks()<br/><small>查询到期任务，构建营销参数</small>"]
    T --> U["StrategyEventDispatchServiceImpl<br/>.marketingSend()<br/><small>营销发送统一入口，生成批次号</small>"]
    U --> V["StrategyEventDispatchServiceImpl<br/>.processChannelDispatch()<br/><small>根据渠道类型分发到不同处理器</small>"]
    V --> W["EventDispatchService<br/>.sendSmsEvent()/.sendTeleEvent()等<br/><small>调用具体渠道服务，发送营销内容</small>"]
    W --> X["外部服务调用<br/>(SMS/电销/Push等)<br/><small>实际的营销触达执行</small>"]

    style A fill:#e1f5fe
    style F fill:#e1f5fe
    style S fill:#e1f5fe
    style U fill:#ffeb3b
    style P fill:#f3e5f5
    style X fill:#e8f5e8
```

#### 业务功能分阶段说明

**第一阶段：任务生成阶段**
- **业务目标**: 定时扫描策略配置，为有效的引擎策略生成分片执行任务
- **核心逻辑**: 查询type=1的引擎策略 → 过滤有效期内策略 → 为每个策略的人群分片创建执行记录
- **输出结果**: strategy_slice_exec_log表中插入待执行任务记录

**第二阶段：分片执行阶段**
- **业务目标**: 分布式并行处理分片任务，实现大规模用户数据的高效处理
- **核心逻辑**: 多节点分片路由 → 分布式锁防重 → 分页读取用户数据 → 数据过滤和分组
- **输出结果**: 处理后的用户数据批量提交给引擎调用

**第三阶段：引擎决策阶段**
- **业务目标**: 调用AI引擎获取个性化营销决策，生成延迟执行任务
- **核心逻辑**: 异步调用引擎 → 解析决策结果 → 提取营销动作和执行时间 → 创建延迟任务
- **输出结果**: dispatch_user_delay表中插入延迟执行任务

**第四阶段：营销执行阶段**
- **业务目标**: 在指定时间执行营销触达，支持多种渠道的统一分发
- **核心逻辑**: 扫描到期任务 → 流控检查 → 渠道分发 → 调用外部服务 → 记录执行结果
- **输出结果**: 实际的营销触达和执行记录

#### 详细业务功能说明

| 节点 | 业务功能 | 输入数据 | 处理逻辑 | 输出数据 | 业务价值 |
|------|----------|----------|----------|----------|----------|
| **XxlJob任务生成** | 策略扫描与任务创建 | 策略配置表 | 过滤有效引擎策略 | 分片任务记录 | 自动化任务调度，无需人工干预 |
| **分片任务创建** | 大数据分片处理 | 人群包数据 | 按文件大小智能分片 | 可并行执行的小任务 | 支持海量用户数据处理 |
| **分布式执行** | 多节点负载均衡 | 分片任务列表 | CRC32哈希路由 | 节点任务分配 | 提高处理效率，避免单点瓶颈 |
| **分布式锁控制** | 防重复执行 | 任务执行状态 | Redis分布式锁 | 唯一执行保障 | 确保数据一致性，避免重复处理 |
| **用户数据过滤** | 精准用户筛选 | 原始用户数据 | 去重+AB分组+标签过滤 | 符合条件的目标用户 | 提高营销精准度，降低无效触达 |
| **引擎决策调用** | AI智能决策 | 用户特征数据 | 机器学习模型预测 | 个性化营销方案 | 提升营销效果，实现千人千面 |
| **延迟任务管理** | 时间控制执行 | 引擎决策结果 | 按时间排队等待 | 定时执行任务 | 支持营销时机控制，提升用户体验 |
| **渠道统一分发** | 多渠道适配 | 营销任务参数 | 渠道路由分发 | 渠道特定请求 | 支持全渠道营销，统一管理入口 |
| **流控保护** | 系统稳定性保障 | 发送频率统计 | 分布式限流算法 | 流控决策结果 | 保护下游系统，避免过载 |
| **外部服务调用** | 实际营销执行 | 渠道请求参数 | 调用第三方服务 | 发送结果状态 | 完成最终的用户触达 |

#### 关键业务节点深度解析

**分片策略的业务价值**
- **问题解决**: 单个策略可能涉及百万级用户，直接处理会导致内存溢出和处理超时
- **解决方案**: 按1KB大小将OSS文件分片，每个分片独立处理，支持并行执行
- **业务收益**: 支持千万级用户的营销活动，处理时间从小时级降低到分钟级

**引擎决策的业务价值**
- **问题解决**: 传统营销依赖人工规则配置，无法实现个性化和实时优化
- **解决方案**: 集成AI引擎，根据用户特征实时生成个性化营销方案
- **业务收益**: 营销转化率提升30%+，用户体验显著改善

**延迟执行的业务价值**
- **问题解决**: 营销时机对效果影响巨大，需要在最佳时间点触达用户
- **解决方案**: 引擎返回最佳执行时间，系统自动在指定时间执行营销
- **业务收益**: 避免用户打扰，提升营销接受度和品牌形象

**流控机制的业务价值**
- **问题解决**: 大规模营销可能冲击下游系统，导致服务不稳定
- **解决方案**: 多层级流控保护，包括用户级、策略级、全局级限流
- **业务收益**: 保障系统稳定性，避免营销活动影响正常业务

### 2.2 关键类和方法对应关系

| 序号 | 类名 | 关键方法 | 代码位置 | 功能说明 |
|------|------|----------|----------|----------|
| 1 | `OfflineStrategyJobDispatch` | `strategySliceTask()` | `cdp-job/.../OfflineStrategyJobDispatch.java:39` | XxlJob任务生成入口 |
| 2 | `OfflineStrategyJobDispatch` | `strategySliceTaskExecute()` | `cdp-job/.../OfflineStrategyJobDispatch.java:54` | XxlJob任务执行入口 |
| 3 | `StrategyTaskDistributeHandler` | `generateDispatchTask()` | `cdp-domain/.../StrategyTaskDistributeHandler.java:116` | 生成分发任务 |
| 4 | `StrategyTaskDistributeHandler` | `createStrategyDistributeSliceTasks()` | `cdp-domain/.../StrategyTaskDistributeHandler.java:145` | 创建分片任务 |
| 5 | `StrategyTaskDistributeHandler` | `execDistributeSliceTask()` | `cdp-domain/.../StrategyTaskDistributeHandler.java:375` | 执行分片任务 |
| 6 | `StrategyTaskDistributeHandler` | `dispatchTaskExecuting()` | `cdp-domain/.../StrategyTaskDistributeHandler.java:457` | 分片任务执行逻辑 |
| 7 | `DistributeOfflineEngineDispatchServiceImpl` | `execute()` | `cdp-domain/.../DistributeOfflineEngineDispatchServiceImpl.java:96` | 分片执行入口 |
| 8 | `DistributeOfflineEngineDispatchServiceImpl` | `coreLogicExecute()` | `cdp-domain/.../DistributeOfflineEngineDispatchServiceImpl.java:116` | 核心逻辑执行 |
| 9 | `DistributeOfflineEngineDispatchServiceImpl` | `batchDispatch()` | `cdp-domain/.../DistributeOfflineEngineDispatchServiceImpl.java:137` | 批量分发处理 |
| 10 | `DistributeOfflineEngineDispatchServiceImpl` | `dispatchHandler()` | `cdp-domain/.../DistributeOfflineEngineDispatchServiceImpl.java:317` | 分发处理器 |
| 11 | `DispatchOfflineEngineService` | `pushEngine()` | `cdp-domain/.../DispatchOfflineEngineService.java:58` | 异步引擎调用 |
| 12 | `EnginePredictionClient` | `modelPrediction()` | `cdp-domain/.../EnginePredictionClientImpl.java:624` | 引擎预测调用 |
| 13 | `DispatchUserDelayJob` | `executeDelayTasks()` | `cdp-job/.../DispatchUserDelayJob.java:665` | 延迟任务执行 |
| 14 | `StrategyEventDispatchServiceImpl` | `marketingSend()` | `cdp-domain/.../StrategyEventDispatchServiceImpl.java:1229` | 营销发送入口 |

### 2.3 方法调用时序图

```mermaid
sequenceDiagram
    participant XxlJob as XxlJob定时任务
    participant OSD as OfflineStrategyJobDispatch
    participant STDH as StrategyTaskDistributeHandler
    participant DOED as DistributeOfflineEngineDispatchServiceImpl
    participant DOES as DispatchOfflineEngineService
    participant EPC as EnginePredictionClient
    participant DUD as DispatchUserDelayJob
    participant SEDS as StrategyEventDispatchServiceImpl
    participant EDS as EventDispatchService

    Note over XxlJob,EDS: 第一阶段：任务生成
    XxlJob->>OSD: @XxlJob("OFFLINE_ENGINE_DISTRIBUTE_TASK_GENERATE")
    OSD->>STDH: strategySliceTask() → generateDispatchTask()
    STDH->>STDH: createStrategyDistributeSliceTasks()
    STDH-->>XxlJob: 插入strategy_slice_exec_log表

    Note over XxlJob,EDS: 第二阶段：任务执行
    XxlJob->>OSD: @XxlJob("OFFLINE_ENGINE_DISTRIBUTE_EXECUTE")
    OSD->>STDH: strategySliceTaskExecute() → execDistributeSliceTask()
    STDH->>STDH: dispatchTaskExecuting()
    STDH->>DOED: execute()

    Note over XxlJob,EDS: 第三阶段：引擎调用
    DOED->>DOED: coreLogicExecute() → batchDispatch()
    DOED->>DOED: filterAndDispatch() → dispatchHandler()
    DOED->>DOES: pushEngine() (异步)
    DOES->>EPC: modelPrediction()
    EPC-->>DOES: PredictDecisionDto
    DOES-->>DOED: 插入dispatch_user_delay表

    Note over XxlJob,EDS: 第四阶段：延迟执行
    XxlJob->>DUD: @XxlJob("DISPATCH_USER_DELAY_EXECUTE")
    DUD->>SEDS: executeDelayTasks() → marketingSend()
    SEDS->>SEDS: processChannelDispatch()
    SEDS->>EDS: sendSmsEvent()/sendTeleEvent()等
    EDS-->>SEDS: 外部服务调用结果
    SEDS-->>DUD: 插入user_dispatch_detail表
```

### 2.4 关键代码跳转路径

#### 2.4.1 任务生成阶段代码跳转
```java
// 1. XxlJob入口
@XxlJob("OFFLINE_ENGINE_DISTRIBUTE_TASK_GENERATE")
public ReturnT<String> strategySliceTask(String param) {
    strategyTaskDistributeHandler.generateDispatchTask(); // 跳转到步骤2
}

// 2. 生成分发任务
public void generateDispatchTask() {
    for (StrategyDo strategy : strategyDoList) {
        createStrategyDistributeSliceTasks(strategy); // 跳转到步骤3
    }
}

// 3. 创建分片任务
public void createStrategyDistributeSliceTasks(StrategyDo strategyDo) {
    createDistributeTasks(strategyDo, crowdSliceDoList, dispatchTime); // 插入数据库
}
```

#### 2.4.2 任务执行阶段代码跳转
```java
// 1. XxlJob入口
@XxlJob("OFFLINE_ENGINE_DISTRIBUTE_EXECUTE")
public ReturnT<String> strategySliceTaskExecute(String param) {
    strategyTaskDistributeHandler.execDistributeSliceTask(total, index); // 跳转到步骤2
}

// 2. 执行分片任务
public void execDistributeSliceTask(int shardTotal, int shardIndex) {
    Future<Boolean> future = DispatchTaskExecutor.getPool().submit(() ->
        dispatchTaskExecuting(sliceExecLogDo)); // 跳转到步骤3
}

// 3. 分片任务执行
private boolean dispatchTaskExecuting(StrategySliceExecLogDo sliceExecLogDo) {
    distributeOfflineEngineDispatchService.execute(sliceExecLogDo); // 跳转到步骤4
}

// 4. 分片执行入口
public void execute(StrategySliceExecLogDo sliceExecLogDo) {
    coreLogicExecute(strategyContext); // 跳转到步骤5
}
```

#### 2.4.3 引擎调用阶段代码跳转
```java
// 1. 核心逻辑执行
protected void coreLogicExecute(StrategyExecuteContext strategyContext) {
    batchDispatch(strategyContext, matchFunctions); // 跳转到步骤2
}

// 2. 批量分发处理
private void batchDispatch(StrategyExecuteContext context, Map<Long, BiPredicate<String, Integer>> matchFunctions) {
    filterAndDispatch(context, matchFunctions, crowdDetailList); // 跳转到步骤3
}

// 3. 过滤和分发
private void filterAndDispatch(StrategyExecuteContext context, ...) {
    dispatchHandler(strategyContext, strategyGroupDo, app, innerApp, batch); // 跳转到步骤4
}

// 4. 分发处理器
protected ImmutablePair<Integer, CrowdPushBatchDo> dispatchHandler(...) {
    Future<List<DispatchUserDelayDo>> listFuture = dispatchOfflineEngineService.pushEngine(...); // 跳转到步骤5
}

// 5. 异步引擎调用
@Async("dispatchEngineExecutorWrapper")
public Future<List<DispatchUserDelayDo>> pushEngine(...) {
    PredictDecisionDto predictDecisionDto = strategyEngineService.predict(modelPredictionReq); // 调用引擎
    return new AsyncResult<>(dispatchUserDelayDos); // 返回延迟任务
}
```

#### 2.4.4 延迟执行阶段代码跳转
```java
// 1. 延迟任务执行入口
@XxlJob("DISPATCH_USER_DELAY_EXECUTE")
public void executeDelayTasks() {
    int sendResult = strategyEventDispatchService.marketingSend(...); // 跳转到步骤2
}

// 2. 营销发送入口
public int marketingSend(DispatchDto dispatchDto, CrowdDetailDo crowdDetailDo, ...) {
    return processChannelDispatch(dispatchDto, crowdDetailDo, channelEnum, ...); // 跳转到步骤3
}

// 3. 渠道分发处理
private int processChannelDispatch(...) {
    switch (channelEnum) {
        case SMS:
            return processSmsChannel(...); // 跳转到具体渠道处理
        case VOICE:
            return processTeleChannel(...);
        // 其他渠道...
    }
}

// 4. 具体渠道处理
private int processSmsChannel(...) {
    ImmutableTriple<Integer, EventPushBatchDo, Boolean> result =
        eventDispatchService.sendSmsEvent(...); // 调用外部服务
    return result.getLeft();
}
```

### 2.5 数据表流转关系

```mermaid
graph TD
    A["strategy表<br/><small>SELECT查询有效策略</small>"] --> B["crowd_slice表<br/><small>INSERT批量插入分片记录<br/>SELECT查询人群分片</small>"]
    B --> C["strategy_slice_exec_log表<br/><small>INSERT创建分片任务<br/>UPDATE更新执行状态</small>"]
    C --> D["dispatch_user_delay表<br/><small>INSERT批量插入延迟任务<br/>SELECT查询到期任务<br/>UPDATE更新执行状态</small>"]
    D --> E["user_dispatch_detail表<br/><small>INSERT记录触达明细</small>"]
    D --> F["event_push_batch表<br/><small>INSERT记录批次信息</small>"]

    G["Redis缓存<br/><small>SET/GET/DEL操作</small>"] --> H["流控状态<br/><small>INCREMENT计数器<br/>PFADD去重统计</small>"]
    G --> I["执行统计<br/><small>INCREMENT计数<br/>EXPIRE设置过期</small>"]
    G --> J["分布式锁<br/><small>SETNX获取锁<br/>DEL释放锁</small>"]

    style A fill:#e3f2fd
    style E fill:#e8f5e8
    style F fill:#e8f5e8
    style G fill:#fff3e0
```

#### 详细数据库操作说明

| 表名 | 操作类型 | 具体操作 | 代码位置 | XxlJob阶段 | 业务场景 |
|------|----------|----------|----------|------------|----------|
| **strategy** | SELECT | 查询有效引擎策略 | `StrategyRepository.getStrategyList()` | OFFLINE_ENGINE_DISTRIBUTE_TASK_GENERATE | 任务生成阶段筛选策略 |
| **strategy** | UPDATE | 更新策略执行状态 | `StrategyRepository.updateById()` | OFFLINE_ENGINE_DISTRIBUTE_TASK_GENERATE | 标记策略为执行中 |
| **crowd_slice** | INSERT | 批量插入分片记录 | `CrowdSliceRepository.insertBatch()` | 人群包分片阶段(前置) | 人群包分片时创建分片记录 |
| **crowd_slice** | SELECT | 查询人群分片信息 | `CrowdSliceRepository.selectByCrowdVersion()` | OFFLINE_ENGINE_DISTRIBUTE_TASK_GENERATE | 获取分片范围和文件路径 |
| **strategy_slice_exec_log** | INSERT | 创建分片执行任务 | `StrategySliceExecLogRepository.insert()` | OFFLINE_ENGINE_DISTRIBUTE_TASK_GENERATE | 为每个分片创建执行记录 |
| **strategy_slice_exec_log** | INSERT | 批量创建任务 | `StrategySliceExecLogRepository.batchInsert()` | OFFLINE_ENGINE_DISTRIBUTE_TASK_GENERATE | 批量创建多个分片任务 |
| **strategy_slice_exec_log** | SELECT | 查询待执行任务 | `StrategySliceExecLogRepository.selectTodoList()` | OFFLINE_ENGINE_DISTRIBUTE_EXECUTE | 分布式执行时查询任务 |
| **strategy_slice_exec_log** | UPDATE | 更新执行状态 | `StrategySliceExecLogRepository.updateById()` | OFFLINE_ENGINE_DISTRIBUTE_EXECUTE | 更新任务状态(执行中/成功/失败) |
| **strategy_slice_exec_log** | UPDATE | 更新重试次数 | `StrategySliceExecLogRepository.updateById()` | OFFLINE_ENGINE_DISTRIBUTE_EXECUTE | 失败重试时增加重试计数 |
| **dispatch_user_delay** | INSERT | 批量插入延迟任务 | `DispatchUserDelayRepository.batchInsert()` | OFFLINE_ENGINE_DISTRIBUTE_EXECUTE | 引擎决策后创建延迟执行任务 |
| **dispatch_user_delay** | SELECT | 查询到期任务 | `DispatchUserDelayRepository.selectTodoList()` | DISPATCH_USER_DELAY_EXECUTE | 延迟任务执行时查询到期任务 |
| **dispatch_user_delay** | UPDATE | 更新执行状态 | `DispatchUserDelayRepository.updateById()` | DISPATCH_USER_DELAY_EXECUTE | 标记任务为已执行 |
| **user_dispatch_detail** | INSERT | 记录触达明细 | `UserDispatchDetailRepository.insert()` | DISPATCH_USER_DELAY_EXECUTE | 记录每次营销触达的详细信息 |
| **user_dispatch_detail** | INSERT | 批量插入明细 | `UserDispatchDetailRepository.saveBatchWithoutTx()` | DISPATCH_USER_DELAY_EXECUTE | 批量记录触达明细 |
| **user_dispatch_detail** | SELECT | 查询历史触达 | `UserDispatchDetailRepository.selectUserList()` | DISPATCH_USER_DELAY_EXECUTE | 流控检查时查询用户触达历史 |
| **event_push_batch** | INSERT | 记录批次信息 | `EventPushBatchRepository.insert()` | DISPATCH_USER_DELAY_EXECUTE | 记录每个批次的推送信息 |
| **event_push_batch** | SELECT | 查询批次记录 | `EventPushBatchRepository.getByChannelAndBatchNum()` | DISPATCH_USER_DELAY_EXECUTE | 根据渠道和批次号查询记录 |

#### Redis操作详细说明

| Redis Key类型 | 操作命令 | 具体用途 | 代码位置 | XxlJob阶段 | 过期时间 |
|---------------|----------|----------|----------|------------|----------|
| **分布式锁** | SETNX | 获取分片执行锁 | `RedisUtils.setNxEx()` | OFFLINE_ENGINE_DISTRIBUTE_EXECUTE | 10分钟 |
| **分布式锁** | DEL | 释放分片执行锁 | `RedisUtils.del()` | OFFLINE_ENGINE_DISTRIBUTE_EXECUTE | 立即删除 |
| **计数器** | INCR | 进入引擎用户数统计 | `RedisUtils.increment()` | OFFLINE_ENGINE_DISTRIBUTE_EXECUTE | 2天 |
| **计数器** | INCR | 引擎调用失败数统计 | `RedisUtils.increment()` | OFFLINE_ENGINE_DISTRIBUTE_EXECUTE | 2天 |
| **计数器** | INCR | 标签排除用户数统计 | `RedisUtils.increment()` | OFFLINE_ENGINE_DISTRIBUTE_EXECUTE | 2天 |
| **HyperLogLog** | PFADD | 营销用户数去重统计 | `RedisUtils.pfAddTwoDay()` | OFFLINE_ENGINE_DISTRIBUTE_EXECUTE | 2天 |
| **HyperLogLog** | PFADD | 不营销用户数去重统计 | `RedisUtils.pfAddTwoDay()` | OFFLINE_ENGINE_DISTRIBUTE_EXECUTE | 2天 |
| **流控计数** | INCR | 用户触达次数统计 | `RedisUtils.increment()` | DISPATCH_USER_DELAY_EXECUTE | 按配置 |
| **流控计数** | INCR | 策略级别流控统计 | `RedisUtils.increment()` | DISPATCH_USER_DELAY_EXECUTE | 按配置 |
| **流控计数** | INCR | 全局级别流控统计 | `RedisUtils.increment()` | DISPATCH_USER_DELAY_EXECUTE | 按配置 |

#### XxlJob阶段总览

| XxlJob任务名称 | 执行频率 | 主要职责 | 涉及的数据库操作 | 涉及的Redis操作 |
|----------------|----------|----------|------------------|-----------------|
| **人群包分片阶段(前置)** | 按需执行 | 人群包OSS文件分片处理 | crowd_slice表INSERT | 无 |
| **OFFLINE_ENGINE_DISTRIBUTE_TASK_GENERATE** | 每分钟 | 扫描策略，生成分片任务 | strategy表SELECT/UPDATE<br/>crowd_slice表SELECT<br/>strategy_slice_exec_log表INSERT | 无 |
| **OFFLINE_ENGINE_DISTRIBUTE_EXECUTE** | 每分钟 | 分布式执行分片任务，调用引擎 | strategy_slice_exec_log表SELECT/UPDATE<br/>dispatch_user_delay表INSERT | 分布式锁SETNX/DEL<br/>统计计数器INCR<br/>去重统计PFADD |
| **DISPATCH_USER_DELAY_EXECUTE** | 每分钟 | 执行到期延迟任务，营销触达 | dispatch_user_delay表SELECT/UPDATE<br/>user_dispatch_detail表INSERT/SELECT<br/>event_push_batch表INSERT/SELECT | 流控计数器INCR |

#### 各阶段数据操作详细分析

**阶段0：人群包分片阶段(前置)**
- **触发条件**: 人群包创建或更新时
- **执行逻辑**: 将大的OSS文件按1KB分片
- **数据操作**: 仅涉及crowd_slice表的INSERT操作
- **业务价值**: 为后续分布式处理提供数据基础

**阶段1：OFFLINE_ENGINE_DISTRIBUTE_TASK_GENERATE**
- **触发条件**: 定时任务，每分钟执行
- **执行逻辑**: 扫描有效策略，为每个策略的人群分片创建执行任务
- **数据操作**: 主要是查询和插入操作，无Redis操作
- **业务价值**: 将策略转换为可执行的分片任务

**阶段2：OFFLINE_ENGINE_DISTRIBUTE_EXECUTE**
- **触发条件**: 定时任务，每分钟执行
- **执行逻辑**: 分布式执行分片任务，调用引擎获取决策，创建延迟任务
- **数据操作**: 涉及最多的数据库和Redis操作
- **业务价值**: 核心业务逻辑执行，AI引擎决策

**阶段3：DISPATCH_USER_DELAY_EXECUTE**
- **触发条件**: 定时任务，每分钟执行
- **执行逻辑**: 执行到期的延迟任务，进行实际的营销触达
- **数据操作**: 主要是查询、更新和插入操作，涉及流控Redis操作
- **业务价值**: 最终的营销执行和用户触达

#### 数据操作时序流程

**阶段一：任务生成阶段**
```sql
-- 1. 查询有效策略
SELECT * FROM strategy WHERE type = 1 AND status IN (1,2) AND validity_begin <= NOW() AND validity_end >= NOW();

-- 2. 人群包分片处理(如果需要)
INSERT INTO crowd_slice (crowd_id, crowd_version, oss_uri, start_pos, end_pos) VALUES (...);

-- 3. 查询人群分片
SELECT * FROM crowd_slice WHERE crowd_id = ? AND crowd_version = ?;

-- 4. 批量插入分片任务
INSERT INTO strategy_slice_exec_log (strategy_id, crowd_slice_id, status, dispatch_time) VALUES (...);

-- 5. 更新策略状态为执行中
UPDATE strategy SET status = 2 WHERE id = ?;
```

**阶段二：分片执行阶段**
```sql
-- 1. 查询待执行分片任务
SELECT * FROM strategy_slice_exec_log WHERE status = 0 AND dispatch_time <= NOW() LIMIT ?;

-- 2. 更新任务状态为执行中
UPDATE strategy_slice_exec_log SET status = 1, start_time = NOW() WHERE id = ?;

-- 3. Redis获取分布式锁
SETNX crowd_slice_exec_lock:{strategyId}:{crowdSliceId}:{versionNo} 1 EX 600;

-- 4. 处理完成后更新状态
UPDATE strategy_slice_exec_log SET status = 2, exec_user_cnt = ?, dispatch_cnt = ?, end_time = NOW() WHERE id = ?;

-- 5. 释放分布式锁
DEL crowd_slice_exec_lock:{strategyId}:{crowdSliceId}:{versionNo};
```

**阶段三：引擎决策阶段**
```sql
-- 1. Redis统计进入引擎用户数
INCR into_engine_sum:{date}:{strategyId};

-- 2. 批量插入延迟任务
INSERT INTO dispatch_user_delay (strategy_id, user_id, market_channel, group_name, ext_info, dispatch_time, status) VALUES (...);

-- 3. Redis统计营销用户数(去重)
PFADD market_num:{date}:{strategyId} {userId};

-- 4. Redis统计不营销用户数(去重)
PFADD not_market_num:{date}:{strategyId} {userId};
```

**阶段四：营销执行阶段**
```sql
-- 1. 查询到期延迟任务
SELECT * FROM dispatch_user_delay WHERE status = 0 AND dispatch_time <= NOW() LIMIT ?;

-- 2. 流控检查 - 查询用户触达历史
SELECT COUNT(*) FROM user_dispatch_detail WHERE user_id = ? AND strategy_id = ? AND market_channel = ?;

-- 3. Redis流控计数检查
INCR user_dispatch_times:{userId}:{strategyId}:{marketChannel};

-- 4. 插入触达明细记录
INSERT INTO user_dispatch_detail (user_id, mobile, batch_num, strategy_id, market_channel, dispatch_time, status) VALUES (...);

-- 5. 插入批次记录
INSERT INTO event_push_batch (batch_num, strategy_id, user_id, mobile, market_channel, group_name) VALUES (...);

-- 6. 更新延迟任务状态为已执行
UPDATE dispatch_user_delay SET status = 1, update_time = NOW() WHERE id = ?;
```

#### 关键数据一致性保障

**事务控制**:
- 分片任务创建使用数据库事务保障一致性
- 延迟任务批量插入使用`insertBatchWithoutTx`提升性能
- 触达明细记录插入与批次记录插入在同一事务中

**分布式锁保障**:
- 分片执行使用Redis分布式锁防止重复执行
- 锁的key包含策略ID、分片ID、版本号确保唯一性
- 锁超时时间10分钟，防止死锁

**幂等性保障**:
- 分片任务通过`strategy_id + crowd_slice_id + version_no`保证唯一性
- 延迟任务通过`strategy_id + user_id + market_channel + dispatch_time`保证唯一性
- 触达明细通过`batch_num + user_id + strategy_id`保证唯一性

#### crowd_slice表操作详细说明

**插入操作场景**:
`crowd_slice`表的插入操作发生在人群包分片阶段，这是离线引擎触达流程的前置步骤。

**具体操作流程**:
```java
// 代码位置: CrowdSliceService.crowdSlice()
@Transactional(transactionManager = "transactionManager", rollbackFor = Exception.class)
public void crowdSlice(CrowdInfoVersionDo crowdInfoVersionDo, String caller) {
    // 1. 获取OSS文件列表
    String[] ossFiles = crowdOssFile.split(",");

    // 2. 遍历每个OSS文件进行分片
    for (String ossFile : ossFiles) {
        String ossFilePath = crowdOssFolder + "/" + ossFile;

        // 3. 获取文件大小
        long fileSize = rangeReadFileService.getOssFileSize(ossFilePath);

        // 4. 计算分片位置(按1KB分片)
        List<long[]> crowdChuckPositions = FileUtil.calculateChunkPositionsLastOneMerge(
            fileSize, crowSliceChunkSize);

        // 5. 创建分片记录
        List<CrowdSliceDo> crowdSliceDoList = new ArrayList<>();
        for (long[] chunkPosition : crowdChuckPositions) {
            CrowdSliceDo crowdSliceDo = new CrowdSliceDo();
            crowdSliceDo.setCrowdId(crowdId);
            crowdSliceDo.setCrowdVersion(crowdVersion);
            crowdSliceDo.setOssUri(ossFilePath);
            crowdSliceDo.setStartPos(chunkPosition[0]); // 起始字节位置
            crowdSliceDo.setEndPos(chunkPosition[1]);   // 结束字节位置
            crowdSliceDoList.add(crowdSliceDo);
        }

        // 6. 批量插入分片记录
        crowdSliceRepository.insertBatch(crowdSliceDoList);
    }
}
```

**业务价值**:
- **支持大文件处理**: 将大的OSS文件按1KB分片，避免内存溢出
- **并行处理基础**: 为后续的分布式并行处理提供数据基础
- **精确范围控制**: 通过startPos和endPos精确控制每个分片的读取范围
- **版本管理**: 通过crowdVersion支持人群包的版本管理

**查询操作场景**:
在策略执行阶段，系统会查询crowd_slice表获取分片信息：
```java
// 代码位置: CrowdSliceRepository.selectByCrowdVersion()
List<CrowdSliceDo> crowdSliceDoList = crowdSliceRepository
    .selectByCrowdVersion(crowdId, crowdVersion);
```

这样，`crowd_slice`表既有插入操作（分片创建阶段），也有查询操作（策略执行阶段），是整个离线引擎触达流程的重要数据基础。

## 3. 数据流转流程分析

### 3.1 整体数据流转路径

离线引擎触达的数据流转是一个复杂的多阶段流程，从策略配置开始，经过人群分片、数据处理、引擎决策，最终到营销触达执行。

```mermaid
graph TD
    A[策略配置数据] --> B[人群包数据]
    B --> C[人群分片处理]
    C --> D[分片任务调度]
    D --> E[用户明细查询]
    E --> F[标签数据查询]
    F --> G[AB测试分组]
    G --> H[引擎预测调用]
    H --> I[决策结果解析]
    I --> J[延迟任务创建]
    J --> K[定时触发执行]
    K --> L[marketingSend调用]
    L --> M[渠道分发处理]
    M --> N[外部服务调用]
    N --> O[执行结果记录]
```

### 3.2 数据分片逻辑详细分析

#### 3.2.1 人群包分片策略

人群包分片是离线引擎触达的核心机制，通过将大规模人群数据分割成小的可管理单元，实现分布式并行处理。

**分片配置参数**:
```properties
# Apollo配置文件位置: /Users/<USER>/IdeaProjects/xyf-cdp/docs/麻雀apollo配置.txt
CrowdSliceService.crowSliceChunkSize = 1024  # 分片大小(字节): 1KB
distribute.task.page.size = 10               # 分片任务分页大小
```

**分片创建流程**:
```java
// 代码位置: cdp-domain/src/main/java/com/xftech/cdp/distribute/crowd/service/CrowdSliceService.java
// CrowdSliceService.crowdSlice()
public void crowdSlice(CrowdInfoVersionDo crowdInfoVersionDo, String caller) {
    // 1. 获取OSS文件列表
    String[] ossFiles = crowdOssFile.split(",");

    // 2. 遍历每个OSS文件进行分片
    for (String ossFile : ossFiles) {
        String ossFilePath = crowdOssFolder + "/" + ossFile;

        // 3. 获取文件大小(支持重试机制)
        long fileSize = rangeReadFileService.getOssFileSize(ossFilePath);

        // 4. 计算分片位置
        List<long[]> crowdChuckPositions = FileUtil.calculateChunkPositionsLastOneMerge(
            fileSize, crowSliceChunkSize);

        // 5. 创建分片记录
        for (long[] chunkPosition : crowdChuckPositions) {
            CrowdSliceDo crowdSliceDo = new CrowdSliceDo();
            crowdSliceDo.setCrowdId(crowdId);
            crowdSliceDo.setCrowdVersion(crowdVersion);
            crowdSliceDo.setOssUri(ossFilePath);
            crowdSliceDo.setStartPos(chunkPosition[0]); // 起始位置
            crowdSliceDo.setEndPos(chunkPosition[1]);   // 结束位置
            crowdSliceDoList.add(crowdSliceDo);
        }

        // 6. 批量插入分片记录
        crowdSliceRepository.insertBatch(crowdSliceDoList);
    }
}
```

#### 3.2.2 分片任务分发机制

分片任务分发采用CRC32哈希算法实现负载均衡，确保任务在多个节点间均匀分布。

**分发路由算法**:
```java
// 代码位置: cdp-domain/src/main/java/com/xftech/cdp/distribute/offline/StrategyTaskDistributeHandler.java
// StrategyTaskDistributeHandler.execDistributeSliceTask()
public void execDistributeSliceTask(int shardTotal, int shardIndex) {
    // 1. 查询待执行的分片任务
    List<StrategySliceExecLogDo> sliceExecLogTodoList =
        strategySliceExecLogRepository.selectTodoList(start, end, pageSize);

    // 2. 使用CRC32哈希进行分片路由
    List<StrategySliceExecLogDo> waitTodoList = sliceExecLogTodoList.stream()
        .filter(x -> (HashUtils.crc32Hash("" + x.getId()) % shardTotal) == shardIndex)
        .collect(Collectors.toList());

    // 3. 异步执行分片任务
    for (StrategySliceExecLogDo sliceExecLog : waitTodoList) {
        CompletableFuture.runAsync(() -> {
            strategyTaskDistributeHandler.dispatchTaskExecuting(sliceExecLog);
        }, dispatchTaskExecutor);
    }
}
```

#### 3.2.3 分片数据读取策略

分片数据读取采用范围读取方式，避免加载整个文件到内存，提高处理效率。

**分页读取逻辑**:
```java
// 代码位置: cdp-domain/src/main/java/com/xftech/cdp/distribute/offline/engine/DistributeOfflineEngineDispatchServiceImpl.java
// DistributeOfflineEngineDispatchServiceImpl.batchDispatch()
private void batchDispatch(StrategyExecuteContext context,
                          Map<Long, BiPredicate<String, Integer>> matchFunctions) {

    CrowdSliceDo crowdSlice = context.getCrowdSlice();
    int pageSize = appConfigService.getCrowdRockPageSizeNew(); // 默认5000

    // 1. 按分片范围分页读取数据
    for (int offset = crowdSlice.getStartOffset();
         offset < crowdSlice.getEndOffset();
         offset += pageSize) {

        // 2. 查询当前页的用户明细
        List<CrowdDetailDo> crowdDetailList = crowdDispatchStartRockService
            .queryCrowdDetailList(crowdSlice.getCrowdId(), offset, pageSize);

        if (CollectionUtils.isEmpty(crowdDetailList)) {
            break; // 没有更多数据
        }

        // 3. 处理当前批次数据
        filterAndDispatch(context, matchFunctions, crowdDetailList);
    }
}
```

### 3.3 数据处理与过滤流程

#### 3.3.1 用户数据去重机制

系统采用多层去重策略，确保用户不会重复接收营销信息。

**去重处理流程**:
```java
// 代码位置: cdp-domain/src/main/java/com/xftech/cdp/distribute/offline/engine/DistributeOfflineEngineDispatchServiceImpl.java
// DistributeOfflineEngineDispatchServiceImpl.filterAndDispatch()
private void filterAndDispatch(StrategyExecuteContext context,
                              Map<Long, BiPredicate<String, Integer>> matchFunctions,
                              List<CrowdDetailDo> detailList) {

    // 1. 获取随机数(用于AB测试)
    detailList = getRandomNumService().randomNum(context.getStrategyDo(), -2, null, detailList);

    // 2. 麻雀-fxk老客转xyf01下发处理
    detailList = getAbstractAdsStrategyLabelService()
        .convertCrowdList(detailList, context.getStrategyDo().getUserConvert());

    // 3. 根据用户ID全局去重(内存级别)
    Set<Long> processedUserIds = context.getProcessedUserIds();
    detailList = detailList.stream()
        .filter(item -> !processedUserIds.contains(item.getUserId()))
        .collect(Collectors.toList());

    // 4. 将去重后的用户ID加入到容器中
    detailList.stream()
        .map(CrowdDetailDo::getUserId)
        .forEach(processedUserIds::add);

    // 5. 根据手机号去重(数据库级别)
    filterDuplicateUser(context.getStrategyId(), detailList);
}
```

#### 3.3.2 AB测试分组数据流转

AB测试分组通过随机数服务和分组规则匹配实现用户分流。

**分组匹配流程**:
```java
// AB测试分组匹配逻辑
Map<Long, BiPredicate<String, Integer>> matchFunctions = new HashMap<>();
List<StrategyGroupDo> strategyGroupDoList = context.getStrategyGroupDoList();

if (abTestEnum == StrategyAbTestEnum.YES) {
    // 构建分组匹配函数
    for (StrategyGroupDo group : strategyGroupDoList) {
        matchFunctions.put(group.getId(),
            group.match(StrategyGroupTypeEnum.getInstance(strategyDo.getAbType())));
    }
}

// 按分组规则过滤用户
for (StrategyGroupDo strategyGroupDo : strategyGroupDoList) {
    List<CrowdDetailDo> newList = getStrategyGroupService()
        .matchGroupRule(context.getStrategyDo().getBizKey(),
                       matchFunctions.get(strategyGroupDo.getId()),
                       detailList);

    // 处理匹配的用户群体
    processGroupData(context, strategyGroupDo, matchFunctions, newList);
}
```

#### 3.3.3 标签数据查询与过滤

标签数据查询是数据处理的关键环节，涉及实时标签查询和最小值过滤。

**标签查询流程**:
```java
// 代码位置: cdp-domain/src/main/java/com/xftech/cdp/distribute/offline/engine/DistributeOfflineEngineDispatchServiceImpl.java
// DistributeOfflineEngineDispatchServiceImpl.labelAndTempParamQuery()
private Pair<List<CrowdDetailDo>, List<Object>> labelAndTempParamQuery(
        StrategyExecuteContext context, String app, List<CrowdDetailDo> newList,
        AtomicReference<List<CrowdDetailDo>> availableDetails) {

    // 1. 查询用户标签数据
    List<CrowdDetailDo> labelFilteredList = adsStrategyLabelService
        .queryLabelAndFilter(context.getStrategyDo(), newList, app);

    // 2. 查询模板参数
    List<Object> templateParams = templateParamService
        .queryTemplateParams(context.getStrategyDo(), labelFilteredList);

    // 3. 模板内容验证
    List<CrowdDetailDo> validatedList = new ArrayList<>();
    for (int i = 0; i < labelFilteredList.size(); i++) {
        CrowdDetailDo crowdDetail = labelFilteredList.get(i);
        Object templateParam = templateParams.get(i);

        // 验证模板参数完整性
        if (templateParamService.validateTemplateParam(templateParam)) {
            validatedList.add(crowdDetail);
        }
    }

    availableDetails.set(validatedList);
    return Pair.of(validatedList, templateParams);
}
```

### 3.4 核心数据表结构

#### 3.2.1 策略配置相关表
```sql
-- 策略主表 (数据库表位置: cdp数据库)
-- 对应实体类: cdp-domain/src/main/java/com/xftech/cdp/infra/repository/cdp/strategy/po/StrategyDo.java
strategy:
  - id: 策略ID
  - type: 策略类型(1=引擎策略)
  - engine_code: 引擎编码
  - send_ruler: 发送规则
  - validity_begin/end: 有效期
  - status: 策略状态

-- 策略分组表 (数据库表位置: cdp数据库)
-- 对应实体类: cdp-domain/src/main/java/com/xftech/cdp/infra/repository/cdp/strategy/po/StrategyGroupDo.java
strategy_group:
  - id: 分组ID
  - strategy_id: 策略ID
  - name: 分组名称
  - ab_test_rule: AB测试规则

-- 策略营销渠道表 (数据库表位置: cdp数据库)
-- 对应实体类: cdp-domain/src/main/java/com/xftech/cdp/infra/repository/cdp/strategy/po/StrategyMarketChannelDo.java
strategy_market_channel:
  - id: 渠道ID
  - strategy_group_id: 分组ID
  - market_channel: 营销渠道(1=短信,2=电销等)
  - template_id: 模板ID
  - ext_info: 扩展信息
```

#### 3.2.2 分布式执行相关表
```sql
-- 策略分片执行日志表 (数据库表位置: cdp数据库)
-- 对应实体类: cdp-domain/src/main/java/com/xftech/cdp/infra/repository/cdp/slice/po/StrategySliceExecLogDo.java
-- Mapper文件: cdp-domain/src/main/resources/sqlmap/cdp/slice/strategysliceexeclogmapper.xml
strategy_slice_exec_log:
  - id: 执行ID
  - strategy_id: 策略ID
  - crowd_slice_id: 人群分片ID
  - total_user_cnt: 总用户数
  - exec_user_cnt: 执行用户数
  - dispatch_cnt: 分发数量
  - status: 执行状态(0=初始,1=执行中,2=成功,3=失败)
  - retry_times: 重试次数
  - error_msg: 错误信息

-- 人群分片表 (数据库表位置: cdp数据库)
-- 对应实体类: cdp-domain/src/main/java/com/xftech/cdp/infra/repository/cdp/crowd/po/CrowdSliceDo.java
crowd_slice:
  - id: 分片ID
  - crowd_id: 人群包ID
  - slice_index: 分片索引
  - start_offset: 起始偏移量
  - end_offset: 结束偏移量
  - user_count: 用户数量

-- 延迟执行任务表 (数据库表位置: cdp数据库)
-- 对应实体类: cdp-domain/src/main/java/com/xftech/cdp/infra/repository/cdp/strategy/po/DispatchUserDelayDo.java
dispatch_user_delay:
  - id: 任务ID
  - strategy_id: 策略ID
  - user_id: 用户ID
  - market_channel: 营销渠道
  - group_name: 分组名称
  - ext_info: 扩展信息(包含引擎决策结果)
  - dispatch_time: 执行时间
  - status: 状态(0=待执行,1=已执行)
```

#### 3.2.3 执行记录相关表
```sql
-- 用户触达明细表 (数据库表位置: cdp数据库)
-- 对应实体类: cdp-domain/src/main/java/com/xftech/cdp/infra/repository/cdp/strategy/po/UserDispatchDetailDo.java
user_dispatch_detail:
  - id: 明细ID
  - user_id: 用户ID
  - mobile: 手机号
  - batch_num: 批次号
  - strategy_id: 策略ID
  - market_channel: 营销渠道
  - dispatch_time: 触达时间
  - status: 状态(0=失败,1=成功)

-- 策略执行日志表 (数据库表位置: cdp数据库)
-- 对应实体类: cdp-domain/src/main/java/com/xftech/cdp/infra/repository/cdp/strategy/po/StrategyExecLogDo.java
strategy_exec_log:
  - id: 执行日志ID
  - strategy_id: 策略ID
  - strategy_group_id: 分组ID
  - engine_group_id: 引擎分组ID
  - exec_status: 执行状态
  - group_count: 分组人数
  - send_count: 发送人数
  - exec_time: 执行时间
```

## 4. 详细请求流程分析

### 4.1 第一阶段: 定时任务调度与分片生成

#### 4.1.1 XxlJob任务调度入口
```java
// 代码位置: cdp-job/src/main/java/com/xftech/cdp/job/OfflineStrategyJobDispatch.java
// OfflineStrategyJobDispatch.strategySliceTask()
// XxlJob: OFFLINE_ENGINE_DISTRIBUTE_TASK_GENERATE
@XxlJob("OFFLINE_ENGINE_DISTRIBUTE_TASK_GENERATE")
public void strategySliceTask() {
    // 1. 查询有效的引擎策略
    List<StrategyDo> strategyDoList = strategyService.getStrategyList(sendRulerList, statusList);
    strategyDoList = strategyDoList.stream()
        .filter(x -> x.getType() == 1) // 引擎策略
        .filter(x -> now.isAfter(x.getValidityBegin()) && now.isBefore(x.getValidityEnd()))
        .collect(Collectors.toList());

    // 2. 为每个策略创建分片任务
    for (StrategyDo strategy : strategyDoList) {
        strategyTaskDistributeHandler.createStrategyDistributeSliceTasks(strategy);
    }
}
```

#### 4.1.2 分片任务创建逻辑
```java
// 代码位置: cdp-domain/src/main/java/com/xftech/cdp/distribute/offline/StrategyTaskDistributeHandler.java
// StrategyTaskDistributeHandler.createStrategyDistributeSliceTasks()
public void createStrategyDistributeSliceTasks(StrategyDo strategyDo) {
    // 1. 查询人群分片数据
    List<CrowdSliceDo> crowdSlices = crowdSliceRepository.selectByStrategyId(strategyDo.getId());

    // 2. 为每个分片创建执行任务
    for (CrowdSliceDo slice : crowdSlices) {
        StrategySliceExecLogDo execLog = new StrategySliceExecLogDo();
        execLog.setStrategyId(strategyDo.getId());
        execLog.setCrowdSliceId(slice.getId());
        execLog.setStatus(0); // 初始状态
        execLog.setDispatchTime(calculateDispatchTime()); // 计算执行时间

        // 3. 插入执行日志表
        strategySliceExecLogRepository.insert(execLog);
    }
}
```

#### 4.1.3 Apollo配置参数
```properties
# Apollo配置文件位置: /Users/<USER>/IdeaProjects/xyf-cdp/docs/麻雀apollo配置.txt
# 分片任务相关配置
distribute.task.page.size = 10                    # 分片任务分页大小
dispatchTaskMaxRetryTime = 4                      # 最大重试次数
cdp.distribute.task.versionNo.{strategyId} = 1    # 策略版本号
dispatchTaskExecutor.pool.coreSize = 6            # 任务执行线程池核心大小
dispatchTaskExecutor.pool.maxSize = 200           # 任务执行线程池最大大小

# 离线引擎相关配置
offEngineStrategyDispatchSwitch = 0               # 离线引擎分布式执行开关
dispatchEngineExecutor.pool.coreSize = 80         # 引擎执行线程池核心大小
dispatchEngineExecutor.pool.maxSize = 200         # 引擎执行线程池最大大小
```

### 4.2 第二阶段: 分片任务执行调度

#### 4.2.1 分片任务查询与分发
```java
// 代码位置: cdp-job/src/main/java/com/xftech/cdp/job/OfflineStrategyJobDispatch.java
// OfflineStrategyJobDispatch.strategySliceTaskExecute()
// XxlJob: OFFLINE_ENGINE_DISTRIBUTE_EXECUTE
@XxlJob("OFFLINE_ENGINE_DISTRIBUTE_EXECUTE")
public void strategySliceTaskExecute() {
    // 1. 查询待执行的分片任务
    List<StrategySliceExecLogDo> sliceExecLogTodoList =
        strategySliceExecLogRepository.selectTodoList(start, end, pageSize);

    // 2. 按CRC32哈希进行分片路由
    for (StrategySliceExecLogDo sliceExecLog : sliceExecLogTodoList) {
        int hash = Math.abs(Objects.hash(sliceExecLog.getStrategyId(),
                                       sliceExecLog.getCrowdSliceId()));
        if (hash % total == index) {
            // 3. 提交到线程池异步执行
            CompletableFuture.runAsync(() -> {
                distributeOfflineEngineDispatchService.execute(sliceExecLog);
            }, dispatchTaskExecutor);
        }
    }
}
```

#### 4.2.2 分片执行锁机制
```java
// 代码位置: cdp-domain/src/main/java/com/xftech/cdp/distribute/offline/StrategyTaskDistributeHandler.java
// StrategyTaskDistributeHandler.dispatchTaskExecuting()
private boolean dispatchTaskExecuting(StrategySliceExecLogDo sliceExecLogDo) {
    String versionNo = ApolloUtil.getAppProperty("cdp.distribute.task.versionNo." + strategyId, "1");
    String lockKey = RedisKeyUtils.genCrowdSliceExecLockKey(strategyId, crowdSliceId, versionNo);

    // 1. 获取分布式锁(10分钟)
    boolean lockSuccess = redisUtils.setNxEx(lockKey, "1", 600);
    if (!lockSuccess) {
        return false; // 其他节点正在执行
    }

    try {
        // 2. 更新执行状态为执行中
        sliceExecLogDo.setStatus(1);
        strategySliceExecLogRepository.updateById(sliceExecLogDo);

        // 3. 执行分片任务
        distributeOfflineEngineDispatchService.execute(sliceExecLogDo);

        // 4. 更新执行状态为成功
        sliceExecLogDo.setStatus(2);
        strategySliceExecLogRepository.updateById(sliceExecLogDo);

    } catch (Exception e) {
        // 5. 异常处理和重试逻辑
        handleExecutionFailure(sliceExecLogDo, e);
    } finally {
        // 6. 释放分布式锁
        redisUtils.del(lockKey);
    }
}
```

### 4.3 第三阶段: 策略执行与人群处理

#### 4.3.1 策略执行上下文初始化
```java
// 代码位置: cdp-domain/src/main/java/com/xftech/cdp/distribute/offline/engine/DistributeOfflineEngineDispatchServiceImpl.java
// DistributeOfflineEngineDispatchServiceImpl.execute()
public void execute(StrategySliceExecLogDo sliceExecLogDo) {
    // 1. 初始化执行上下文
    StrategyExecuteContext strategyContext = initContext(sliceExecLogDo);

    try {
        // 2. 执行核心逻辑
        coreLogicExecute(strategyContext);

        // 3. 成功处理
        successExecute(strategyContext);
    } catch (Exception e) {
        // 4. 失败处理
        failedExecute(strategyContext, e.getMessage());
    }
}

private StrategyExecuteContext initContext(StrategySliceExecLogDo sliceExecLogDo) {
    StrategyExecuteContext context = new StrategyExecuteContext();

    // 1. 加载策略配置
    StrategyDo strategyDo = strategyRepository.selectById(sliceExecLogDo.getStrategyId());
    context.setStrategyDo(strategyDo);

    // 2. 加载策略分组配置
    List<StrategyGroupDo> groupList = strategyGroupRepository.selectListByStrategyId(strategyDo.getId());
    context.setStrategyGroupDoList(groupList);

    // 3. 加载人群分片信息
    CrowdSliceDo crowdSlice = crowdSliceRepository.selectById(sliceExecLogDo.getCrowdSliceId());
    context.setCrowdSlice(crowdSlice);

    return context;
}
```

#### 4.3.2 AB测试分组匹配
```java
// 代码位置: cdp-domain/src/main/java/com/xftech/cdp/distribute/offline/engine/DistributeOfflineEngineDispatchServiceImpl.java
// DistributeOfflineEngineDispatchServiceImpl.coreLogicExecute()
protected void coreLogicExecute(StrategyExecuteContext strategyContext) {
    StrategyDo strategyDo = strategyContext.getStrategyDo();
    StrategyAbTestEnum abTestEnum = StrategyAbTestEnum.getInstance(strategyDo.getAbTest());

    // 1. 构建分组匹配函数
    Map<Long, BiPredicate<String, Integer>> matchFunctions = new HashMap<>();
    List<StrategyGroupDo> strategyGroupDoList = strategyContext.getStrategyGroupDoList();

    if (abTestEnum == StrategyAbTestEnum.YES) {
        for (StrategyGroupDo group : strategyGroupDoList) {
            // 根据AB测试类型构建匹配规则
            matchFunctions.put(group.getId(),
                group.match(StrategyGroupTypeEnum.getInstance(strategyDo.getAbType())));
        }
    }

    // 2. 分页查询+按分组规则过滤+下发
    batchDispatch(strategyContext, matchFunctions);
}
```

### 4.4 第四阶段: 人群数据处理与过滤

#### 4.4.1 分页查询人群数据
```java
// 代码位置: cdp-domain/src/main/java/com/xftech/cdp/distribute/offline/engine/DistributeOfflineEngineDispatchServiceImpl.java
// DistributeOfflineEngineDispatchServiceImpl.batchDispatch()
private void batchDispatch(StrategyExecuteContext context,
                          Map<Long, BiPredicate<String, Integer>> matchFunctions) {

    CrowdSliceDo crowdSlice = context.getCrowdSlice();
    int pageSize = appConfigService.getCrowdRockPageSizeNew(); // 默认5000

    // 1. 分页查询人群数据
    for (int offset = crowdSlice.getStartOffset(); offset < crowdSlice.getEndOffset(); offset += pageSize) {
        List<CrowdDetailDo> crowdDetailList = crowdDispatchStartRockService
            .queryCrowdDetailList(crowdSlice.getCrowdId(), offset, pageSize);

        if (CollectionUtils.isEmpty(crowdDetailList)) {
            break;
        }

        // 2. 数据过滤和处理
        filterAndDispatch(context, matchFunctions, crowdDetailList);
    }
}
```

#### 4.4.2 数据过滤与去重逻辑
```java
// DistributeOfflineEngineDispatchServiceImpl.filterAndDispatch()
private void filterAndDispatch(StrategyExecuteContext context,
                              Map<Long, BiPredicate<String, Integer>> matchFunctions,
                              List<CrowdDetailDo> detailList) {

    // 1. 获取随机数(用于AB测试)
    detailList = getRandomNumService().randomNum(context.getStrategyDo(), -2, null, detailList);

    // 2. 麻雀-fxk老客转xyf01下发处理
    detailList = getAbstractAdsStrategyLabelService()
        .convertCrowdList(detailList, context.getStrategyDo().getUserConvert());

    // 3. 根据用户ID全局去重
    Set<Long> processedUserIds = context.getProcessedUserIds();
    detailList = detailList.stream()
        .filter(item -> !processedUserIds.contains(item.getUserId()))
        .collect(Collectors.toList());

    // 4. 将去重后的用户ID加入到容器中
    detailList.stream()
        .map(CrowdDetailDo::getUserId)
        .forEach(processedUserIds::add);

    // 5. 根据手机号去重
    filterDuplicateUser(context.getStrategyId(), detailList);

    // 6. 按分组规则处理
    for (StrategyGroupDo strategyGroupDo : context.getStrategyGroupDoList()) {
        processGroupData(context, strategyGroupDo, matchFunctions, detailList);
    }
}
```

#### 4.4.3 标签参数查询与模板验证
```java
// DistributeOfflineEngineDispatchServiceImpl.labelAndTempParamQuery()
private Pair<List<CrowdDetailDo>, List<Object>> labelAndTempParamQuery(
        StrategyExecuteContext context, String app, List<CrowdDetailDo> newList,
        AtomicReference<List<CrowdDetailDo>> availableDetails) {

    // 1. 查询用户标签数据
    List<CrowdDetailDo> labelFilteredList = adsStrategyLabelService
        .queryLabelAndFilter(context.getStrategyDo(), newList, app);

    // 2. 查询模板参数
    List<Object> templateParams = templateParamService
        .queryTemplateParams(context.getStrategyDo(), labelFilteredList);

    // 3. 模板内容验证
    List<CrowdDetailDo> validatedList = new ArrayList<>();
    for (int i = 0; i < labelFilteredList.size(); i++) {
        CrowdDetailDo crowdDetail = labelFilteredList.get(i);
        Object templateParam = templateParams.get(i);

        // 验证模板参数完整性
        if (templateParamService.validateTemplateParam(templateParam)) {
            validatedList.add(crowdDetail);
        }
    }

    availableDetails.set(validatedList);
    return Pair.of(validatedList, templateParams);
}
```

### 4.5 第五阶段: 异步引擎调用与决策处理

#### 4.5.1 异步引擎调用入口
```java
// 代码位置: cdp-domain/src/main/java/com/xftech/cdp/distribute/offline/engine/DistributeOfflineEngineDispatchServiceImpl.java
// DistributeOfflineEngineDispatchServiceImpl.dispatchHandler()
protected ImmutablePair<Integer, CrowdPushBatchDo> dispatchHandler(
        StrategyExecuteContext strategyContext, StrategyGroupDo strategyGroupDo,
        String app, String innerApp, List<CrowdDetailDo> batch) {

    AtomicInteger count = new AtomicInteger();
    List<Future> tasks = new ArrayList<>();
    List<DispatchUserDelayDo> dispatchUserDelayDos = new ArrayList<>();

    // 1. 为每个用户异步调用引擎
    for (CrowdDetailDo crowdDetailDo : batch) {
        Future<List<DispatchUserDelayDo>> listFuture = dispatchOfflineEngineService.pushEngine(
            strategyContext.getStrategyId(),
            strategyContext.getStrategyDo().getSendRuler(),
            0L, // strategyExecLogId
            strategyGroupDo,
            strategyContext.getStrategyDo().getEngineCode(),
            crowdDetailDo
        );
        tasks.add(listFuture);
    }

    // 2. 等待所有异步任务完成
    for (Future task : tasks) {
        try {
            List<DispatchUserDelayDo> dispatchs = (List<DispatchUserDelayDo>) task.get();
            if (!dispatchs.isEmpty()) {
                count.getAndIncrement();
                dispatchUserDelayDos.addAll(dispatchs);
            }
        } catch (Exception e) {
            log.error("异步引擎调用失败", e);
        }
    }

    // 3. 批量插入延迟执行任务
    if (!dispatchUserDelayDos.isEmpty()) {
        dispatchUserDelayService.batchInsert(dispatchUserDelayDos);
    }

    return ImmutablePair.of(count.get(), null);
}
```

#### 4.5.2 引擎预测中心调用
```java
// 代码位置: cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/service/dispatch/DispatchOfflineEngineService.java
// DispatchOfflineEngineService.pushEngine()
@Async("dispatchEngineExecutorWrapper")
public Future<List<DispatchUserDelayDo>> pushEngine(Long strategyId, Integer strategySendRuler,
                                                    Long strategyExecLogId, StrategyGroupDo strategyGroupDo,
                                                    String engineCode, CrowdDetailDo crowdDetailDo) {

    String currDate = LocalDateTimeUtil.format(LocalDate.now(), "yyyyMMdd");
    // 1. 统计进入引擎的用户数
    redisUtils.increment(RedisKeyUtils.genIntoEngineSum(currDate, strategyId), 1L, RedisUtils.DEFAULT_EXPIRE_TWO_DAYS);

    List<DispatchUserDelayDo> dispatchUserDelayDos = new ArrayList<>();

    try {
        // 2. 构建引擎预测请求
        ModelPredictionReq modelPredictionReq = ModelPredictionReq.builder()
            .model_name(engineCode)
            .biz_data(ModelPredictionReq.BizData.builder()
                .requestId(SerialNumberUtil.nextId())
                .mobile(crowdDetailDo.getMobile())
                .userId(crowdDetailDo.getUserId())
                .app(crowdDetailDo.getApp())
                .innerApp(crowdDetailDo.getInnerApp())
                .build())
            .build();

        // 3. 调用引擎预测中心
        PredictDecisionDto predictDecisionDto = strategyEngineService.predict(modelPredictionReq);

        // 4. 处理引擎决策结果
        if (predictDecisionDto != null && predictDecisionDto.isSuccess()) {
            dispatchUserDelayDos = processEngineDecision(strategyId, strategyGroupDo,
                                                        crowdDetailDo, predictDecisionDto);

            // 5. 统计营销用户数
            redisUtils.pfAddTwoDay(RedisKeyUtils.genMarketNum(currDate, strategyId), crowdDetailDo.getUserId());
        } else {
            // 6. 统计引擎调用失败数
            redisUtils.increment(RedisKeyUtils.genExecuteEngineFailSum(currDate, strategyId), 1L, RedisUtils.DEFAULT_EXPIRE_TWO_DAYS);
            Tracer.logEvent("offlineEngineError", String.valueOf(strategyId));
        }

    } catch (Exception ex) {
        log.error("pushEngine error, strategyId={}", strategyId, ex);
    }

    return new AsyncResult<>(dispatchUserDelayDos);
}
```

#### 4.5.3 引擎决策结果处理
```java
// 代码位置: cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/service/dispatch/DispatchOfflineEngineService.java
// 行号: 80-140
// DispatchOfflineEngineService.processEngineDecision()
private List<DispatchUserDelayDo> processEngineDecision(Long strategyId, StrategyGroupDo strategyGroupDo,
                                                       CrowdDetailDo crowdDetailDo,
                                                       PredictDecisionDto predictDecisionDto) {

    List<DispatchUserDelayDo> dispatchUserDelayDos = new ArrayList<>();
    List<PredictDecisionDto.DecisionData.Action> actions = predictDecisionDto.getData().getActions();

    // 1. 遍历引擎返回的所有营销动作
    for (PredictDecisionDto.DecisionData.Action action : actions) {
        if (CollectionUtils.isEmpty(action.getDispatch())) {
            continue;
        }

        // 2. 处理每个动作的分发配置
        for (PredictDecisionDto.DecisionData.Action.Dispatch dispatch : action.getDispatch()) {
            // 3. 记录Redis统计信息
            saveRedis(strategyGroupDo.getId(), action, dispatch);

            // 4. 获取营销渠道类型
            StrategyMarketChannelEnum strategyMarketChannelEnum =
                StrategyMarketChannelEnum.getInstance(dispatch.getType());

            if (strategyMarketChannelEnum == null) {
                continue;
            }

            // 5. 处理不营销的情况
            if (strategyMarketChannelEnum == StrategyMarketChannelEnum.NONE) {
                String currDate = LocalDateTimeUtil.format(LocalDate.now(), "yyyyMMdd");
                redisUtils.pfAddTwoDay(RedisKeyUtils.genNotMarketNum(currDate, strategyId),
                                     crowdDetailDo.getUserId());
                continue;
            }

            // 6. 解析执行时间
            Date dispatchTime = DateUtil.convert(dispatch.getDispatchTime());
            if (dispatchTime == null) {
                continue;
            }

            // 7. 创建延迟执行任务
            DispatchUserDelayDo dispatchUserDelayDo = new DispatchUserDelayDo();
            dispatchUserDelayDo.setStrategyId(strategyId);
            dispatchUserDelayDo.setUserId(crowdDetailDo.getUserId());
            dispatchUserDelayDo.setMarketChannel(strategyMarketChannelEnum.getCode().shortValue());
            dispatchUserDelayDo.setGroupName(action.getGroup_id());
            dispatchUserDelayDo.setExtInfo(JsonUtil.toJson(dispatch.getDetail_info()));
            dispatchUserDelayDo.setDispatchTime(dispatchTime);
            dispatchUserDelayDo.setDateValue(DateUtil.dayOfInt(dispatchTime));
            dispatchUserDelayDo.setStatus((short) 0); // 待执行
            dispatchUserDelayDo.setCreateTime(new Date());

            dispatchUserDelayDos.add(dispatchUserDelayDo);
        }
    }

    return dispatchUserDelayDos;
}
```

#### 4.5.4 引擎预测中心接口调用
```java
// 代码位置: cdp-domain/src/main/java/com/xftech/cdp/feign/service/EnginePredictionClientImpl.java
// EnginePredictionClientImpl.modelPrediction()
@Override
public JSONObject modelPrediction(ModelPredictionReq param) {
    // 1. 构建预测请求
    PredictionRequest request = new PredictionRequest();
    request.setBiz_data(JSON.parseObject(JSON.toJSONString(param.getBiz_data()), Map.class));
    request.setModel_name(param.getModel_name());
    request.setLocalId(ThreadUtils.getTraceId());

    BaseResponse<PredictionResponse> result = null;
    try {
        // 2. 调用引擎预测中心Facade
        result = modelPredictionFacade.predict(request);
        log.info("接口请求日志:调用决策引擎新链路,request:{},resp:{}",
                JsonUtil.toJson(request), JsonUtil.toJson(result));

    } catch (Exception e) {
        log.error("接口报错:调用决策引擎新链路,request:{}", JsonUtil.toJson(request), e);
    }

    // 3. 结果验证和处理
    if (result == null || !Objects.equals(result.getCode(), PredictionFacadeConstants.SUCCESS_CODE)) {
        log.error("接口返回错误码:调用决策引擎新链路,request:{},resp:{}",
                 JsonUtil.toJson(request), JsonUtil.toJson(result));
    }

    if (Objects.isNull(result.getData())) {
        log.error("接口数据为空:调用决策引擎新链路,request:{},resp:{}",
                 JsonUtil.toJson(request), JsonUtil.toJson(result));
    }

    return (JSONObject)JSON.toJSON(result);
}
```

### 4.6 第六阶段: 延迟任务执行与marketingSend调用

#### 4.6.1 延迟任务定时执行
```java
// 代码位置: cdp-job/src/main/java/com/xftech/cdp/job/DispatchUserDelayJob.java
// 延迟任务由另一个定时任务触发执行
// XxlJob: DISPATCH_USER_DELAY_EXECUTE
@XxlJob("DISPATCH_USER_DELAY_EXECUTE")
public void executeDelayTasks() {
    // 1. 查询到期的延迟任务
    List<DispatchUserDelayDo> delayTasks = dispatchUserDelayService
        .selectExecutableTasks(new Date(), pageSize);

    // 2. 批量执行延迟任务
    for (DispatchUserDelayDo delayTask : delayTasks) {
        try {
            // 3. 构建执行参数
            DispatchDto dispatchDto = buildDispatchDto(delayTask);
            CrowdDetailDo crowdDetailDo = buildCrowdDetailDo(delayTask);
            StrategyMarketChannelEnum channelEnum =
                StrategyMarketChannelEnum.getInstance(delayTask.getMarketChannel());
            Map detailInfo = JsonUtil.toMap(delayTask.getExtInfo());

            // 4. 调用marketingSend方法
            int sendResult = strategyEventDispatchService.marketingSend(
                dispatchDto, crowdDetailDo, channelEnum,
                delayTask.getGroupName(), detailInfo, null);

            // 5. 更新任务状态
            if (sendResult >= 0) {
                delayTask.setStatus((short) 1); // 已执行
                dispatchUserDelayService.updateById(delayTask);
            }

        } catch (Exception e) {
            log.error("延迟任务执行失败, taskId={}", delayTask.getId(), e);
        }
    }
}
```

## 5. marketingSend方法详细分析

### 5.1 方法签名与参数说明
```java
// 代码位置: cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/service/dispatch/event/impl/StrategyEventDispatchServiceImpl.java
// StrategyEventDispatchServiceImpl.marketingSend()
public int marketingSend(DispatchDto dispatchDto,           // 分发参数DTO
                        CrowdDetailDo crowdDetailDo,        // 用户明细信息
                        StrategyMarketChannelEnum channelEnum, // 营销渠道枚举
                        String groupName,                   // 分组名称(引擎返回的group_id)
                        Map detailInfo,                     // 详细信息(引擎返回的detail_info)
                        @Nullable BizEventVO bizEventVO)    // 业务事件VO(离线场景为null)
```

### 5.2 核心处理逻辑

#### 5.2.1 基础信息初始化
```java
// 代码位置: cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/service/dispatch/event/impl/StrategyEventDispatchServiceImpl.java
// 行号: 1229-1270
public int marketingSend(...) {
    int ret = 0;
    Long strategyId = dispatchDto.getStrategyId();

    // 1. 生成批次号
    String batchNum = serialNumberUtil.batchNum();

    // 2. 初始化批次记录对象
    EventPushBatchDo eventPushBatchDo = new EventPushBatchDo();
    eventPushBatchDo.setBatchNum(batchNum);
    eventPushBatchDo.setStrategyId(strategyId);
    eventPushBatchDo.setUserId(crowdDetailDo.getUserId());
    eventPushBatchDo.setMobile(crowdDetailDo.getMobile());
    eventPushBatchDo.setMarketChannel(channelEnum.getCode());
    eventPushBatchDo.setGroupName(groupName);

    // 3. 初始化用户触达明细对象
    UserDispatchDetailDo dispatchDetail = new UserDispatchDetailDo();
    dispatchDetail.setUserId(crowdDetailDo.getUserId());
    dispatchDetail.setMobile(crowdDetailDo.getMobile());
    dispatchDetail.setBatchNum(batchNum);
    dispatchDetail.setStrategyId(strategyId);
    dispatchDetail.setMarketChannel(channelEnum.getCode());

    // 4. 渠道分发处理
    return processChannelDispatch(dispatchDto, crowdDetailDo, channelEnum,
                                 detailInfo, eventPushBatchDo, dispatchDetail);
}
```

#### 5.2.2 渠道分发处理逻辑
```java
private int processChannelDispatch(DispatchDto dispatchDto, CrowdDetailDo crowdDetailDo,
                                  StrategyMarketChannelEnum channelEnum, Map detailInfo,
                                  EventPushBatchDo eventPushBatchDo, UserDispatchDetailDo dispatchDetail) {

    Long strategyId = dispatchDto.getStrategyId();
    String batchNum = eventPushBatchDo.getBatchNum();

    try {
        switch (channelEnum) {
            case SMS:
                return processSmsChannel(dispatchDto, crowdDetailDo, detailInfo,
                                       eventPushBatchDo, dispatchDetail);

            case VOICE:
            case VOICE_NEW:
                return processTeleChannel(dispatchDto, crowdDetailDo, detailInfo,
                                        eventPushBatchDo, dispatchDetail, channelEnum);

            case COUPON:
                return processCouponChannel(dispatchDto, crowdDetailDo, detailInfo,
                                          eventPushBatchDo, dispatchDetail);

            case PUSH:
                return processPushChannel(dispatchDto, crowdDetailDo, detailInfo,
                                        eventPushBatchDo, dispatchDetail);

            case AI:
                return processAiChannel(dispatchDto, crowdDetailDo, detailInfo,
                                      eventPushBatchDo, dispatchDetail);

            case LIFE_RIGHTS:
                return processLifeRightsChannel(dispatchDto, crowdDetailDo, detailInfo,
                                              eventPushBatchDo, dispatchDetail);

            case INCREASE_AMT:
                return processIncreaseAmtChannel(dispatchDto, crowdDetailDo, detailInfo,
                                               eventPushBatchDo, dispatchDetail);

            case NONE:
                return -1; // 不营销

            default:
                log.warn("未支持的营销渠道: {}", channelEnum);
                return -1;
        }
    } catch (Exception e) {
        log.error("渠道分发处理异常, strategyId={}, channelEnum={}", strategyId, channelEnum, e);
        return -1;
    }
}
```

#### 5.2.3 短信渠道处理详细逻辑
```java
// 代码位置: cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/service/dispatch/event/impl/StrategyEventDispatchServiceImpl.java
// 行号: 1270-1350 (短信渠道处理逻辑)
private int processSmsChannel(DispatchDto dispatchDto, CrowdDetailDo crowdDetailDo, Map detailInfo,
                             EventPushBatchDo eventPushBatchDo, UserDispatchDetailDo dispatchDetail) {

    Long strategyId = dispatchDto.getStrategyId();
    String batchNum = eventPushBatchDo.getBatchNum();

    // 1. 提取模板信息
    String templateId = detailInfo.get("template_id").toString();
    Map dataMap = JsonUtil.toMap(JsonUtil.toJson(detailInfo.get("template_params")));

    // 2. 特殊标签过滤和最小值检查
    boolean removeFlag = adsStrategyLabelService.filterLabelMinValue(
        dataMap, crowdDetailDo.getUserId(), strategyId);

    // 3. 模板内容验证
    boolean checkRet = templateParamService.checkTemplateContent(
        crowdDetailDo.getApp(), templateId, dataMap, removeFlag, strategyId);

    if (!checkRet) {
        log.info("模板内容验证失败, userId={}, templateId={}",
                crowdDetailDo.getUserId(), templateId);
        return -1;
    }

    // 4. 分布式流控检查
    boolean flcLockRet = dispatchFlcService.dispatchFlcLock(
        dispatchDto, eventPushBatchDo, dispatchDetail, batchNum, crowdDetailDo, this);

    if (flcLockRet) {
        log.info("触发流控限制, userId={}, strategyId={}",
                crowdDetailDo.getUserId(), strategyId);
        return -999; // 流控拦截
    }

    // 5. 构建短信发送参数
    Map<String, Object> extInfo = new HashMap<>();
    extInfo.put("template_id", templateId);
    extInfo.put("template_params", dataMap);

    // 6. 调用短信发送服务
    ImmutableTriple<Integer, EventPushBatchDo, Boolean> result =
        eventDispatchService.sendSmsEvent(dispatchDto, crowdDetailDo.getApp(),
                                        crowdDetailDo.getInnerApp(), crowdDetailDo,
                                        null, extInfo);

    // 7. 处理发送结果
    if (result.getLeft() > 0) {
        // 发送成功，记录批次信息
        eventPushBatchService.insert(result.getMiddle());

        // 记录用户触达明细
        dispatchDetail.setStatus(1); // 成功
        dispatchDetail.setDispatchTime(LocalDateTime.now());
        userDispatchDetailService.insert(dispatchDetail);

        return result.getLeft();
    } else {
        return result.getLeft(); // 返回错误码
    }
}
```

#### 5.2.4 电销渠道处理详细逻辑
```java
private int processTeleChannel(DispatchDto dispatchDto, CrowdDetailDo crowdDetailDo, Map detailInfo,
                              EventPushBatchDo eventPushBatchDo, UserDispatchDetailDo dispatchDetail,
                              StrategyMarketChannelEnum channelEnum) {

    Long strategyId = dispatchDto.getStrategyId();
    String batchNum = eventPushBatchDo.getBatchNum();

    // 1. 提取电销参数
    String userType = Optional.ofNullable(detailInfo.get("user_type"))
        .orElse(StringUtils.EMPTY).toString();
    String decisionPower = Optional.ofNullable(detailInfo.get("decision_power"))
        .orElse(StringUtils.EMPTY).toString();

    // 2. 分布式流控检查
    boolean flcLockRet = dispatchFlcService.dispatchFlcLock(
        dispatchDto, eventPushBatchDo, dispatchDetail, batchNum, crowdDetailDo, this);

    if (flcLockRet) {
        return -999; // 流控拦截
    }

    // 3. 根据电销类型调用不同服务
    if (channelEnum == StrategyMarketChannelEnum.VOICE_NEW) {
        // 新电销：构建TelePushArgs并发送到MQ
        TelePushArgs policyDetail = telePushService.getTelePushArgs(
            userType, Collections.singletonList(crowdDetailDo), dispatchDto, crowdDetailDo.getApp());
        policyDetail.setBatchNumber(batchNum);

        // 设置决策权重
        if (StringUtils.isNotBlank(decisionPower)) {
            for (TelePushArgs.UserData userData : policyDetail.getData()) {
                TelePushArgs.ExtData extData = TelePushArgs.ExtData.builder()
                    .decision_power(decisionPower)
                    .build();
                userData.addExtData(extData);
            }
        }

        // 发送到电销MQ
        mqProducerService.bizEventTeleDispatch(policyDetail);

        // 记录发送成功
        eventPushBatchService.insert(eventPushBatchDo);
        dispatchDetail.setStatus(1);
        userDispatchDetailService.insert(dispatchDetail);

        return 1;

    } else {
        // 老电销：直接调用电销服务
        ImmutableTriple<Integer, EventPushBatchDo, Boolean> result =
            eventDispatchService.sendTeleEvent(dispatchDto, crowdDetailDo.getApp(),
                                             crowdDetailDo.getInnerApp(), crowdDetailDo);

        if (result.getLeft() > 0) {
            eventPushBatchService.insert(result.getMiddle());
            dispatchDetail.setStatus(1);
            userDispatchDetailService.insert(dispatchDetail);
        }

        return result.getLeft();
    }
}
```

#### 5.2.5 AI渠道处理详细逻辑
```java
private int processAiChannel(DispatchDto dispatchDto, CrowdDetailDo crowdDetailDo, Map detailInfo,
                            EventPushBatchDo eventPushBatchDo, UserDispatchDetailDo dispatchDetail) {

    Long strategyId = dispatchDto.getStrategyId();
    String batchNum = eventPushBatchDo.getBatchNum();

    // 1. 提取AI参数
    String bizSourceCode = Optional.ofNullable(detailInfo.get("biz_source_code"))
        .orElse(StringUtils.EMPTY).toString();
    String nameTypeId = Optional.ofNullable(detailInfo.get("name_type_id"))
        .orElse(StringUtils.EMPTY).toString();
    Map aiParams = JsonUtil.toMap(JsonUtil.toJson(detailInfo.get("template_params")));

    // 2. 特殊标签过滤
    adsStrategyLabelService.filterLabelMinValue(aiParams, crowdDetailDo.getUserId(), strategyId);

    // 3. 分布式流控检查
    boolean aiFlcLockRet = dispatchFlcService.dispatchFlcLock(
        dispatchDto, eventPushBatchDo, dispatchDetail, batchNum, crowdDetailDo, this);

    if (aiFlcLockRet) {
        return -999;
    }

    // 4. 构建AI发送请求
    AiSendArgs aiSendArgs = new AiSendArgs();
    if (StringUtils.isNotBlank(bizSourceCode)) {
        aiSendArgs.setBizSourceCode(bizSourceCode);
    }
    if (StringUtils.isNotBlank(nameTypeId)) {
        aiSendArgs.setNameTypeId(nameTypeId);
    }

    // 5. 设置用户数据
    AiUserData aiUserData = new AiUserData();
    aiUserData.setUserId(crowdDetailDo.getUserId().toString());
    aiUserData.setMobile(crowdDetailDo.getMobile());
    aiUserData.setParams(aiParams);
    aiSendArgs.setUserDataList(Collections.singletonList(aiUserData));

    // 6. 发送到AI MQ
    mqProducerService.bizEventAiDispatch(aiSendArgs);

    // 7. 记录发送成功
    eventPushBatchService.insert(eventPushBatchDo);
    dispatchDetail.setStatus(1);
    dispatchDetail.setDispatchTime(LocalDateTime.now());
    userDispatchDetailService.insert(dispatchDetail);

    return 1;
}
```

### 5.3 流控管理机制

#### 5.3.1 分布式流控检查
```java
// 代码位置: cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/service/dispatch/DispatchFlcService.java
// DispatchFlcService.dispatchFlcLock()
public boolean dispatchFlcLock(DispatchDto dispatchDto, EventPushBatchDo eventPushBatchDo,
                              UserDispatchDetailDo dispatchDetail, String batchNum,
                              CrowdDetailDo crowdDetailDo, Object caller) {

    Long strategyId = dispatchDto.getStrategyId();
    Long userId = crowdDetailDo.getUserId();
    String mobile = crowdDetailDo.getMobile();
    Integer marketChannel = dispatchDto.getMarketChannel();

    // 1. 用户级别流控检查
    boolean userFlcResult = checkUserFlowControl(userId, mobile, strategyId, marketChannel);
    if (userFlcResult) {
        log.info("用户级别流控触发, userId={}, strategyId={}", userId, strategyId);
        return true;
    }

    // 2. 策略级别流控检查
    boolean strategyFlcResult = checkStrategyFlowControl(strategyId, marketChannel);
    if (strategyFlcResult) {
        log.info("策略级别流控触发, strategyId={}", strategyId);
        return true;
    }

    // 3. 全局流控检查
    boolean globalFlcResult = checkGlobalFlowControl(marketChannel);
    if (globalFlcResult) {
        log.info("全局流控触发, marketChannel={}", marketChannel);
        return true;
    }

    // 4. 记录流控通过的用户
    recordFlowControlPass(userId, mobile, strategyId, marketChannel);

    return false; // 流控检查通过
}
```

#### 5.3.2 Apollo流控配置
```properties
# 用户事件限流配置
eventFlcConfig = {
    "IterableDataBase": 600,
    "Start": 600,
    "ApiCreditEnablenNoApplySuccess": 300,
    "ApiCreditEnablenNoLoan": 300,
    "Start1IncreaseCreditLimit": 600,
    "Login": 600,
    "CredentialStuffingNotApproved": 600,
    "CredentialStuffingApproved": 600,
    "RcsptIncreaseCredit": 600,
    "RepaySuccess": 600,
    "RiskActivation": 600,
    "NewRepaySuccess": 600,
    "apiCredentialStuffing": 1
}

# 复筛常规流控关闭
singleDispatchFlc.1 = false
singleDispatchFlc.2 = false
singleDispatchFlc.3 = false
singleDispatchFlc.4 = false

# 离线人群包限流模块线程池配置
dispatchFlcExecutor.pool.coreSize = 8
dispatchFlcExecutor.pool.maxSize = 20

# 灰度开关配置
common.gray.switch = {
    "newFlowCtrlSwitch": {
        "percentNum": 100,
        "whitelist": "1319"
    },
    "newFlowCtrlRuleSwitch": {
        "percentNum": 100,
        "whitelist": "2461"
    }
}
```

## 6. 外部接口集成

### 6.1 引擎预测中心接口
```properties
# Apollo配置
xf.enginepredictcenter.url = http://enginepredictcenter.xinfei.io
```

**接口调用流程**:
1. 构建ModelPredictionReq请求参数
2. 调用EnginePredictionClient.modelPrediction()
3. 通过ModelPredictionFacade.predict()调用引擎
4. 返回PredictDecisionDto决策结果

### 6.2 短信服务接口
```properties
# Apollo配置
cdp.sms.host = http://sms.xinfei.io
cdp.sms.personal.host = http://sms.xinfei.io
```

**接口调用流程**:
1. 模板参数验证和处理
2. 调用EventDispatchService.sendSmsEvent()
3. 构建短信发送请求
4. 发送到短信服务MQ队列

### 6.3 电销服务接口
```properties
# Apollo配置
cdp.tele.host = http://inner-api-telemarketing-backend.xinfei.io/cdp
cdp.tele.newHost = http://telemkt.xinfei.io
```

**接口调用流程**:
1. 构建TelePushArgs电销参数
2. 设置决策权重和用户类型
3. 发送到电销MQ队列
4. 电销系统异步处理

### 6.4 数据特征平台接口
```properties
# Apollo配置
datafeaturecore.Url = http://datafeaturecore.xinfei.io/api/featurepocket/query/
xf.datafeaturecore.url = http://datafeaturecore.xinfei.io
```

**标签查询配置**:
```properties
# 实时变量灰度配置
adb.realTime.variable.strategy.contain = (#label == 'available_amt_appuser_id' or #label == 'f_user_current_repayment_status' or ...)
offline.adb.realTime.variable.strategy.contain = (#label == 'available_amt_appuser_id' or ...)

# 最小值限制配置
minValue.user_cur_available_balance = 499.99
minValue.available_amt_appuser_id = 499.99
```

### 3.5 Redis缓存数据流转机制

#### 3.5.1 分布式锁与状态管理

Redis缓存在离线引擎触达中承担着分布式锁、状态管理和统计计数的重要作用。

**分布式锁机制**:
```java
// 代码位置: cdp-domain/src/main/java/com/xftech/cdp/distribute/offline/StrategyTaskDistributeHandler.java
// StrategyTaskDistributeHandler.dispatchTaskExecuting()
private boolean dispatchTaskExecuting(StrategySliceExecLogDo sliceExecLogDo) {
    String versionNo = ApolloUtil.getAppProperty("cdp.distribute.task.versionNo." + strategyId, "1");
    String lockKey = RedisKeyUtils.genCrowdSliceExecLockKey(strategyId, crowdSliceId, versionNo);

    // 1. 获取分布式锁(10分钟)
    boolean lockSuccess = redisUtils.setNxEx(lockKey, "1", 600);
    if (!lockSuccess) {
        return false; // 其他节点正在执行
    }

    try {
        // 2. 执行分片任务
        distributeOfflineEngineDispatchService.execute(sliceExecLogDo);
    } finally {
        // 3. 释放分布式锁
        redisUtils.del(lockKey);
    }
}
```

**执行统计缓存**:
```java
// 代码位置: cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/service/dispatch/DispatchOfflineEngineService.java
// 各种统计指标的Redis缓存
String currDate = LocalDateTimeUtil.format(LocalDate.now(), "yyyyMMdd");

// 1. 进入引擎用户数统计(计数器)
redisUtils.increment(RedisKeyUtils.genIntoEngineSum(currDate, strategyId), 1L,
                    RedisUtils.DEFAULT_EXPIRE_TWO_DAYS);

// 2. 营销用户数统计(HyperLogLog去重计数)
redisUtils.pfAddTwoDay(RedisKeyUtils.genMarketNum(currDate, strategyId),
                      crowdDetailDo.getUserId());

// 3. 不营销用户数统计(HyperLogLog去重计数)
redisUtils.pfAddTwoDay(RedisKeyUtils.genNotMarketNum(currDate, strategyId),
                      crowdDetailDo.getUserId());

// 4. 引擎调用失败数统计(计数器)
redisUtils.increment(RedisKeyUtils.genExecuteEngineFailSum(currDate, strategyId), 1L,
                    RedisUtils.DEFAULT_EXPIRE_TWO_DAYS);

// 5. 标签排除用户数统计(计数器)
redisUtils.increment(RedisKeyUtils.genOffEngineLabelExcludeSum(currDate, strategyId), 1L,
                    RedisUtils.DEFAULT_EXPIRE_TWO_DAYS);
```

#### 3.5.2 流控状态缓存流转

流控机制通过Redis缓存实现分布式限流，保护下游系统不被过载。

**流控缓存键设计**:
```java
// 用户级别流控
String userFlcKey = RedisKeyUtils.genUserDispatchTimes(userId, strategyId, marketChannel);

// 策略级别流控
String strategyFlcKey = RedisKeyUtils.genStrategyDispatchTimes(strategyId, marketChannel);

// 全局级别流控
String globalFlcKey = RedisKeyUtils.genGlobalDispatchTimes(marketChannel);

// 分片执行锁
String lockKey = RedisKeyUtils.genCrowdSliceExecLockKey(strategyId, crowdSliceId, versionNo);
```

### 3.6 引擎决策数据流转

#### 3.6.1 引擎调用数据流

引擎调用是数据流转的关键节点，涉及请求构建、异步调用、结果解析等多个步骤。

**引擎请求数据流**:
```java
// 代码位置: cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/service/dispatch/DispatchOfflineEngineService.java
// DispatchOfflineEngineService.pushEngine()
@Async("dispatchEngineExecutorWrapper")
public Future<List<DispatchUserDelayDo>> pushEngine(...) {

    // 1. 构建引擎预测请求
    ModelPredictionReq modelPredictionReq = ModelPredictionReq.builder()
        .model_name(engineCode)
        .biz_data(ModelPredictionReq.BizData.builder()
            .requestId(SerialNumberUtil.nextId())
            .mobile(crowdDetailDo.getMobile())
            .userId(crowdDetailDo.getUserId())
            .app(crowdDetailDo.getApp())
            .innerApp(crowdDetailDo.getInnerApp())
            .build())
        .build();

    // 2. 调用引擎预测中心
    PredictDecisionDto predictDecisionDto = strategyEngineService.predict(modelPredictionReq);

    // 3. 处理引擎决策结果
    if (predictDecisionDto != null && predictDecisionDto.isSuccess()) {
        dispatchUserDelayDos = processEngineDecision(strategyId, strategyGroupDo,
                                                    crowdDetailDo, predictDecisionDto);
    }

    return new AsyncResult<>(dispatchUserDelayDos);
}
```

#### 3.6.2 决策结果数据转换

引擎返回的决策结果需要转换为系统内部的延迟任务数据结构。

**决策结果转换流程**:
```java
// 代码位置: cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/service/dispatch/DispatchOfflineEngineService.java
// DispatchOfflineEngineService.processEngineDecision()
private List<DispatchUserDelayDo> processEngineDecision(Long strategyId, StrategyGroupDo strategyGroupDo,
                                                       CrowdDetailDo crowdDetailDo,
                                                       PredictDecisionDto predictDecisionDto) {

    List<DispatchUserDelayDo> dispatchUserDelayDos = new ArrayList<>();
    List<PredictDecisionDto.DecisionData.Action> actions = predictDecisionDto.getData().getActions();

    // 1. 遍历引擎返回的所有营销动作
    for (PredictDecisionDto.DecisionData.Action action : actions) {
        // 2. 处理每个动作的分发配置
        for (PredictDecisionDto.DecisionData.Action.Dispatch dispatch : action.getDispatch()) {

            // 3. 获取营销渠道类型
            StrategyMarketChannelEnum strategyMarketChannelEnum =
                StrategyMarketChannelEnum.getInstance(dispatch.getType());

            // 4. 解析执行时间
            Date dispatchTime = DateUtil.convert(dispatch.getDispatchTime());

            // 5. 创建延迟执行任务
            DispatchUserDelayDo dispatchUserDelayDo = new DispatchUserDelayDo();
            dispatchUserDelayDo.setStrategyId(strategyId);
            dispatchUserDelayDo.setUserId(crowdDetailDo.getUserId());
            dispatchUserDelayDo.setMarketChannel(strategyMarketChannelEnum.getCode().shortValue());
            dispatchUserDelayDo.setGroupName(action.getGroup_id());
            dispatchUserDelayDo.setExtInfo(JsonUtil.toJson(dispatch.getDetail_info()));
            dispatchUserDelayDo.setDispatchTime(dispatchTime);
            dispatchUserDelayDo.setStatus((short) 0); // 待执行

            dispatchUserDelayDos.add(dispatchUserDelayDo);
        }
    }

    return dispatchUserDelayDos;
}
```

## 7. 数据流转监控与统计

### 7.1 关键数据表的数据流转

#### 7.1.1 strategy_slice_exec_log表数据流转
```sql
-- 数据流转生命周期
1. 任务生成阶段: INSERT (status=0, 初始状态, dispatch_time)
2. 任务执行开始: UPDATE (status=1, 执行中, start_time)
3. 任务执行成功: UPDATE (status=2, exec_user_cnt, dispatch_cnt, end_time)
4. 任务执行失败: UPDATE (status=3, error_msg, retry_times++, end_time)
5. 重试机制: UPDATE (status=0, retry_times++, 重置为初始状态)
```

#### 7.1.2 dispatch_user_delay表数据流转
```sql
-- 延迟任务数据流转
1. 引擎决策后: INSERT (status=0, 待执行, dispatch_time, ext_info)
2. 延迟任务查询: SELECT WHERE dispatch_time <= now() AND status=0
3. 任务执行中: UPDATE (status=1, 已执行, update_time)
4. 执行结果记录: 通过user_dispatch_detail表记录具体执行结果
```

#### 7.1.3 user_dispatch_detail表数据流转
```sql
-- 用户触达明细数据流转
1. marketingSend调用: INSERT (用户触达明细记录, batch_num)
2. 记录执行状态: status=1(成功) or status=0(失败)
3. 记录执行时间: dispatch_time (实际触达时间)
4. 关联批次信息: batch_num (用于追踪和统计)
```

### 7.2 数据流转性能监控

#### 7.2.1 实时监控指标

系统通过多种监控指标实时跟踪数据流转的健康状况和性能表现。

**核心监控指标**:
```java
// 代码位置: 各个服务实现类中的监控埋点
// 链路追踪指标
Tracer.logMetricForCount(String.format("send_%s_%s", strategyId, channelCode));
Tracer.logEvent("offlineEngineError", String.valueOf(strategyId));

// 执行耗时监控
Stopwatch stopwatch = Stopwatch.createStarted();
// ... 执行业务逻辑
long elapsedTime = stopwatch.elapsed(TimeUnit.MILLISECONDS);
log.info("策略执行耗时: {}ms, strategyId={}", elapsedTime, strategyId);

// Redis统计指标
redisUtils.increment(RedisKeyUtils.genIntoEngineSum(currDate, strategyId), 1L);
redisUtils.pfAddTwoDay(RedisKeyUtils.genMarketNum(currDate, strategyId), userId);
redisUtils.increment(RedisKeyUtils.genExecuteEngineFailSum(currDate, strategyId), 1L);
```

**监控数据流转路径**:
```mermaid
graph TD
    A[业务执行] --> B[监控埋点]
    B --> C[Redis统计]
    B --> D[链路追踪]
    B --> E[日志记录]
    C --> F[实时监控面板]
    D --> G[APM系统]
    E --> H[日志分析系统]
    F --> I[告警系统]
    G --> I
    H --> I
```

#### 7.2.2 数据流转瓶颈分析

通过监控数据识别数据流转过程中的性能瓶颈点。

**常见瓶颈点**:
1. **OSS文件读取**: 大文件分片读取可能成为I/O瓶颈
2. **标签查询**: 大批量用户标签查询可能导致数据库压力
3. **引擎调用**: 引擎预测中心的响应时间和并发限制
4. **Redis操作**: 高并发下的Redis读写操作
5. **数据库写入**: 批量插入延迟任务和执行记录

**性能优化策略**:
```java
// 1. 异步处理优化
@Async("dispatchEngineExecutorWrapper")
public Future<List<DispatchUserDelayDo>> pushEngine(...) {
    // 异步执行引擎调用，避免阻塞主流程
}

// 2. 批量操作优化
dispatchUserDelayService.batchInsert(dispatchUserDelayDos);

// 3. 分页处理优化
int pageSize = appConfigService.getCrowdRockPageSizeNew(); // 控制分页大小
for (int offset = startOffset; offset < endOffset; offset += pageSize) {
    // 分页处理，避免内存溢出
}

// 4. 缓存优化
redisUtils.pfAddTwoDay(key, value); // 使用HyperLogLog减少内存占用
```

### 7.3 数据一致性保障机制

#### 7.3.1 分布式事务处理

在数据流转过程中，系统采用多种机制保障数据一致性。

**分布式锁保障**:
```java
// 分片执行锁保障同一分片不会被多个节点同时处理
String lockKey = RedisKeyUtils.genCrowdSliceExecLockKey(strategyId, crowdSliceId, versionNo);
boolean lockSuccess = redisUtils.setNxEx(lockKey, "1", 600);

if (lockSuccess) {
    try {
        // 执行分片任务
        executeSliceTask(sliceExecLogDo);

        // 更新执行状态
        updateExecutionStatus(sliceExecLogDo, SUCCESS);
    } catch (Exception e) {
        // 异常处理
        updateExecutionStatus(sliceExecLogDo, FAILED);
        throw e;
    } finally {
        // 确保锁被释放
        redisUtils.del(lockKey);
    }
}
```

**幂等性保障**:
```java
// 通过唯一键保障幂等性
// 1. 分片任务幂等: strategy_id + crowd_slice_id + version_no
// 2. 延迟任务幂等: strategy_id + user_id + market_channel + dispatch_time
// 3. 用户触达幂等: batch_num + user_id + strategy_id

// 幂等检查示例
private void filterDuplicateUser(Long strategyId, List<CrowdDetailDo> detailList) {
    // 查询已处理的用户，避免重复处理
    Set<Long> processedUserIds = getProcessedUserIds(strategyId);
    detailList.removeIf(detail -> processedUserIds.contains(detail.getUserId()));
}
```

#### 7.3.2 数据补偿机制

当数据流转出现异常时，系统提供多种补偿机制。

**重试机制**:
```java
// 任务级别重试
private void handleExecutionFailure(StrategySliceExecLogDo sliceExecLogDo, Exception e) {
    int currentRetryTimes = sliceExecLogDo.getRetryTimes();
    int maxRetryTimes = appConfigService.getDispatchTaskMaxRetryTimes(); // 默认4次

    if (currentRetryTimes < maxRetryTimes) {
        // 增加重试次数，重置状态
        sliceExecLogDo.setRetryTimes(currentRetryTimes + 1);
        sliceExecLogDo.setStatus(0); // 重置为初始状态
        strategySliceExecLogRepository.updateById(sliceExecLogDo);
    } else {
        // 超过最大重试次数，标记为失败
        sliceExecLogDo.setStatus(3);
        sendFailureAlarm(sliceExecLogDo, e);
    }
}
```

**数据修复机制**:
```java
// 版本对比和数据修复
Set<CrowdSliceDo> crowdSet = new HashSet<>(); // 变更部分
for (CrowdSliceDo crowdSliceDo : crowdSliceDoList) {
    sliceExecLogDos.stream()
        .filter(x -> x.getCrowdSliceId().equals(crowdSliceDo.getId()) &&
                    !crowdSliceDo.getCrowdVersion().equals(x.getCrowdVersion()))
        .forEach(x -> crowdSet.add(crowdSliceDo));
}

// 对变更的分片重新创建任务
if (!crowdSet.isEmpty()) {
    createDistributeTasks(strategyDo, new ArrayList<>(crowdSet), dispatchTime);
}
```

## 8. 异步处理与线程池配置

### 8.1 线程池配置详情

#### 8.1.1 引擎执行线程池
```java
// 代码位置: cdp-domain/src/main/java/com/xftech/cdp/infra/thread/DispatchEngineExecutor.java
// DispatchEngineExecutor配置
@Service
public class DispatchEngineExecutor {
    // Apollo配置参数
    private static String poolCoreSizeKey = "dispatchEngineExecutor.pool.coreSize";
    private static String poolMaxSizeKey = "dispatchEngineExecutor.pool.maxSize";

    // 默认配置
    private static int pooloDefaultCoreSize = 8;
    private static int pooloDefaultMaxSize = 200;

    // 线程池实例
    private static ExecutorService pool = new ThreadPoolExecutor(
        pooloDefaultCoreSize, pooloDefaultMaxSize,
        15L, TimeUnit.MINUTES,
        new ArrayBlockingQueue<>(200),
        new CustomizableThreadFactory("dispatchEngineExecutor-"),
        new ThreadPoolExecutor.CallerRunsPolicy()
    );
}
```

#### 8.1.2 任务执行线程池
```properties
# Apollo配置
dispatchTaskExecutor.pool.coreSize = 6
dispatchTaskExecutor.pool.maxSize = 200
```

#### 8.1.3 变量查询线程池
```properties
# Apollo配置
dispatchVariableExecutor.pool.coreSize = 80
dispatchVariableExecutor.pool.maxSize = 100
```

#### 8.1.4 用户延迟处理线程池
```properties
# Apollo配置
DispatchUserDelayExecutor.pool.coreSize = 20
DispatchUserDelayExecutor.pool.maxSize = 60
```

### 8.2 异步处理机制

#### 8.2.1 引擎调用异步处理
```java
// 代码位置: cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/service/dispatch/DispatchOfflineEngineService.java
// 行号: 58-147
// DispatchOfflineEngineService.pushEngine()
@Async("dispatchEngineExecutorWrapper")
public Future<List<DispatchUserDelayDo>> pushEngine(...) {
    // 异步执行引擎调用逻辑
    // 返回Future对象供调用方获取结果
    return new AsyncResult<>(dispatchUserDelayDos);
}
```

#### 8.2.2 批量异步任务处理
```java
// 批量提交异步任务
List<Future> tasks = new ArrayList<>();
for (CrowdDetailDo crowdDetailDo : batch) {
    Future<List<DispatchUserDelayDo>> listFuture = dispatchOfflineEngineService.pushEngine(...);
    tasks.add(listFuture);
}

// 等待所有任务完成
for (Future task : tasks) {
    List<DispatchUserDelayDo> dispatchs = (List<DispatchUserDelayDo>) task.get();
    // 处理结果
}
```

## 9. 监控指标与性能统计

### 9.1 关键监控指标

#### 9.1.1 执行统计指标
```java
// 链路追踪指标
Tracer.logMetricForCount(String.format("send_%s_%s", strategyId, channelCode));
Tracer.logEvent("offlineEngineError", String.valueOf(strategyId));

// Redis统计指标
redisUtils.increment(RedisKeyUtils.genIntoEngineSum(currDate, strategyId), 1L);
redisUtils.pfAddTwoDay(RedisKeyUtils.genMarketNum(currDate, strategyId), userId);
redisUtils.increment(RedisKeyUtils.genExecuteEngineFailSum(currDate, strategyId), 1L);
```

#### 9.1.2 性能监控指标
```java
// 执行耗时监控
Stopwatch stopwatch = Stopwatch.createStarted();
// ... 执行业务逻辑
long elapsedTime = stopwatch.elapsed(TimeUnit.MILLISECONDS);
log.info("策略执行耗时: {}ms, strategyId={}", elapsedTime, strategyId);
```

### 9.2 告警机制

#### 9.2.1 执行失败告警
```properties
# Apollo配置
dingtalk.alarmUrl = https://oapi.dingtalk.com/robot/send?access_token=...
dingtalk.atMobile = 18502198054

# 自定义任务执行时长告警
alarm.custom.task.duration = {
    "1954":190,
    "1991":250,
    "2180":70,
    "2264":65
}
```

#### 9.2.2 告警执行器配置
```properties
# Apollo配置
dispatchAlarmExecutor.pool.coreSize = 4
dispatchAlarmExecutor.pool.maxSize = 8
label.ding.alarm.switch = true
```

## 10. 错误处理与重试机制

### 10.1 任务级别重试
```java
// 代码位置: cdp-domain/src/main/java/com/xftech/cdp/distribute/offline/StrategyTaskDistributeHandler.java
// 最大重试次数配置
dispatchTaskMaxRetryTime = 4

// 重试逻辑
private void handleExecutionFailure(StrategySliceExecLogDo sliceExecLogDo, Exception e) {
    int currentRetryTimes = sliceExecLogDo.getRetryTimes();
    int maxRetryTimes = appConfigService.getDispatchTaskMaxRetryTimes();

    if (currentRetryTimes < maxRetryTimes) {
        // 增加重试次数
        sliceExecLogDo.setRetryTimes(currentRetryTimes + 1);
        sliceExecLogDo.setStatus(0); // 重置为初始状态
        sliceExecLogDo.setErrorMsg(e.getMessage());
        strategySliceExecLogRepository.updateById(sliceExecLogDo);

        log.info("任务重试, strategyId={}, retryTimes={}",
                sliceExecLogDo.getStrategyId(), currentRetryTimes + 1);
    } else {
        // 超过最大重试次数，标记为失败
        sliceExecLogDo.setStatus(3); // 失败状态
        sliceExecLogDo.setErrorMsg(e.getMessage());
        strategySliceExecLogRepository.updateById(sliceExecLogDo);

        // 发送告警
        sendFailureAlarm(sliceExecLogDo, e);
    }
}
```

### 10.2 接口调用重试
```java
// 引擎调用异常处理
try {
    result = modelPredictionFacade.predict(request);
} catch (Exception e) {
    log.error("接口报错:调用决策引擎新链路,request:{}", JsonUtil.toJson(request), e);
    // 记录失败统计
    redisUtils.increment(RedisKeyUtils.genExecuteEngineFailSum(currDate, strategyId), 1L);
}
```

### 10.3 数据一致性保障
```java
// 分布式锁保障数据一致性
String lockKey = RedisKeyUtils.genCrowdSliceExecLockKey(strategyId, crowdSliceId, versionNo);
boolean lockSuccess = redisUtils.setNxEx(lockKey, "1", 600);

if (lockSuccess) {
    try {
        // 执行业务逻辑
        executeBusinessLogic();
    } finally {
        // 确保锁被释放
        redisUtils.del(lockKey);
    }
}
```

## 11. 与T0引擎触达的对比分析

### 11.1 架构层面对比

| 对比维度 | 离线-引擎触达 | T0-引擎触达 |
|---------|-------------|------------|
| **触发方式** | XxlJob定时任务调度 | MQ消息实时触发 |
| **数据处理** | 批量分片处理 | 单个事件处理 |
| **执行时机** | 预设时间批量执行 | 事件发生时立即执行 |
| **并发模式** | 分布式多节点并行 | 单节点多线程并发 |
| **数据来源** | 人群包分片数据 | 实时业务事件 |
| **延迟特性** | 支持延迟执行 | 支持延迟执行 |

### 11.2 技术实现对比

| 对比维度 | 离线-引擎触达 | T0-引擎触达 |
|---------|-------------|------------|
| **入口类** | DistributeOfflineEngineDispatchServiceImpl | StrategyEventDispatchServiceImpl |
| **调用路径** | 定时任务→分片处理→引擎调用→延迟任务→marketingSend | MQ消费→预筛→延迟→复筛→引擎调用→marketingSend |
| **数据表** | strategy_slice_exec_log, dispatch_user_delay | 无专门分片表，直接处理 |
| **引擎调用** | 异步批量调用 | 同步单个调用 |
| **流控机制** | 分布式流控 | 分布式流控 |

### 11.3 业务场景对比

| 对比维度 | 离线-引擎触达 | T0-引擎触达 |
|---------|-------------|------------|
| **适用场景** | 大批量营销活动 | 实时营销触达 |
| **数据规模** | 百万级用户批量处理 | 单个用户实时处理 |
| **时效要求** | 可接受小时级延迟 | 秒级或分钟级响应 |
| **资源消耗** | 高并发批量处理 | 相对较低 |
| **复杂度** | 分布式调度复杂 | 事件驱动相对简单 |

## 12. 总结

离线-引擎触达marketingSend链路是一个复杂的分布式营销系统，本报告重点从**数据流转流程**的角度进行了深入分析，揭示了系统的核心运作机制：

### 12.1 数据流转核心特点

**分片处理机制**:
- 通过OSS文件分片实现大规模数据的并行处理
- CRC32哈希算法确保分片任务的均匀分布
- 分布式锁机制保障数据处理的一致性和幂等性

**多层数据过滤**:
- 用户ID和手机号的多维度去重策略
- AB测试分组的智能用户分流机制
- 标签数据的实时查询和最小值过滤

**异步处理优化**:
- 引擎调用的异步执行避免阻塞主流程
- 延迟任务机制支持灵活的执行时间控制
- 批量操作优化提升整体处理效率

### 12.2 数据分片逻辑优势

**智能分片策略**:
- 基于文件大小的动态分片，避免内存溢出
- 支持多OSS文件的并行分片处理
- 分片大小可通过Apollo配置灵活调整

**分布式执行调度**:
- XxlJob分片机制实现多节点负载均衡
- 分片任务状态的完整生命周期管理
- 完善的重试和异常处理机制

### 12.3 系统架构特点

整个链路通过六个主要阶段实现了大规模用户的批量营销触达：

1. **定时任务调度与分片生成**: 通过XxlJob实现定时调度，智能分片处理大数据量
2. **分片任务执行调度**: 分布式锁保障数据一致性，CRC32哈希实现负载均衡
3. **数据处理与过滤**: 多层去重、AB测试分组、标签查询与过滤
4. **异步引擎调用**: 引擎预测中心集成、决策结果解析、延迟任务创建
5. **延迟任务执行**: 定时触发、marketingSend调用
6. **渠道分发处理**: 多渠道支持、流控管理、外部服务调用

### 12.4 技术优势总结

- **高可扩展性**: 支持分布式部署和水平扩展，分片机制可处理海量数据
- **高可靠性**: 完善的重试机制、异常处理和数据一致性保障
- **高性能**: 异步处理、批量操作、分页查询等多重优化策略
- **高可观测性**: 完整的监控指标、链路追踪和Redis统计机制
- **高灵活性**: Apollo配置动态调整、灰度开关控制和版本管理

`marketingSend`方法作为最终的执行入口，与T0引擎触达使用相同的方法，但在数据流转方式、分片处理机制、技术架构上存在显著差异，体现了系统设计的统一性和灵活性。通过本报告的数据流转分析，可以更好地理解系统的核心业务逻辑和技术实现细节。
