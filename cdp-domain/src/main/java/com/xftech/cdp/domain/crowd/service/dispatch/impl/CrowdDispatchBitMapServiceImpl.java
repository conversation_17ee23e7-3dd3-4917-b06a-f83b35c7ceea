package com.xftech.cdp.domain.crowd.service.dispatch.impl;

import com.xftech.cdp.domain.crowd.model.dispatch.BatchConsumer;
import com.xftech.cdp.domain.crowd.model.dispatch.CrowdContext;
import com.xftech.cdp.domain.crowd.model.dispatch.CrowdWereHouse;
import com.xftech.cdp.domain.crowd.model.dispatch.InvokePage;
import com.xftech.cdp.domain.crowd.repository.AdsLabelMonitorDfRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdDetailRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdExecLogRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdExecSnapshotRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdLabelPrimaryRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdLabelRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdPackRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdWereHouseSnapshotRepository;
import com.xftech.cdp.domain.crowd.repository.LabelRepository;
import com.xftech.cdp.domain.crowd.service.CrowdDetailSubService;
import com.xftech.cdp.domain.crowd.service.dispatch.CrowdDispatchService;
import com.xftech.cdp.infra.client.datacenter.DataCenterClient;
import com.xftech.cdp.infra.client.usercenter.UserCenterClient;
import com.xftech.cdp.infra.config.CrowdConfig;
import com.xftech.xxljob.XxlJobAdminClient;
import org.apache.commons.lang3.tuple.Pair;
import org.roaringbitmap.longlong.Roaring64Bitmap;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @since 2023/2/16
 */
//@Service("crowdDispatchBitMapService")
public class CrowdDispatchBitMapServiceImpl extends AbstractCrowdDispatchService implements CrowdDispatchService {

    public CrowdDispatchBitMapServiceImpl(CrowdPackRepository crowdPackRepository, CrowdLabelRepository crowdLabelRepository, CrowdLabelPrimaryRepository crowdLabelPrimaryRepository, LabelRepository labelRepository, CrowdDetailRepository crowdDetailRepository, CrowdExecSnapshotRepository crowdExecSnapshotRepository, AdsLabelMonitorDfRepository adsLabelMonitorDfRepository, UserCenterClient userCenterClient, DataCenterClient dataCenterClient, CrowdExecLogRepository crowdExecLogRepository, XxlJobAdminClient xxlJobAdminClient, CrowdWereHouseSnapshotRepository crowdWereHouseSnapshotRepository, CrowdConfig crowdConfig, CrowdDetailSubService crowdDetailSubService) {
        super(crowdPackRepository, crowdLabelRepository, crowdLabelPrimaryRepository, labelRepository, crowdDetailRepository, crowdExecSnapshotRepository, adsLabelMonitorDfRepository, userCenterClient, dataCenterClient, crowdExecLogRepository, xxlJobAdminClient, crowdWereHouseSnapshotRepository, crowdConfig, crowdDetailSubService);
    }

    /**
     * 未验证
     */
    @Override
    public void filterAllUserIds(CrowdContext crowdContext, InvokePage invokePage) {
        long beginId = invokePage.getAppUserId();
        Roaring64Bitmap bitMap = new Roaring64Bitmap();
        Pair<StringBuilder, StringBuilder> labelSqlPair = crowdContext.getLabelSqlPair();
        invokePageById(invokePage, labelSqlPair.getRight(), excludeWereHouses -> {
            excludeWereHouses.forEach(excludeWereHouse -> {
                bitMap.add(excludeWereHouse.getAppUserId());
            });
        }, crowdContext);

        BatchConsumer<CrowdWereHouse> consumer = new BatchConsumer<>(invokePage.getPageSize(), crowdWereHouses -> {
            batchSave(crowdWereHouses, crowdContext, invokePage.getBatchConsumer());
        });

        Consumer<List<CrowdWereHouse>> appUserIdsConsumer = includeWereHouses -> {
            includeWereHouses.forEach(wereHouse -> {
                if (!bitMap.contains(wereHouse.getAppUserId())) {
                    consumer.invoke(wereHouse);
                }
            });
        };

        invokePage.setAppUserId(beginId);
        invokePageById(invokePage, labelSqlPair.getLeft(), appUserIdsConsumer, crowdContext);

    }


}
