/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.service.flow;

import com.alibaba.fastjson.JSON;
import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.req.DispatchConfig;
import com.xftech.cdp.api.dto.req.InstantStrategyCreateReq;
import com.xftech.cdp.api.dto.req.StrategyCreateReq;
import com.xftech.cdp.api.dto.req.StrategyUpdateReq;
import com.xftech.cdp.api.dto.req.flow.strategy.*;
import com.xftech.cdp.api.dto.resp.StrategyDetailResp;
import com.xftech.cdp.api.dto.resp.auth.LoginUser;
import com.xftech.cdp.api.dto.resp.flow.strategy.DetailStragegyConfig;
import com.xftech.cdp.api.dto.resp.flow.strategy.Flow;
import com.xftech.cdp.api.dto.resp.flow.strategy.FlowDetailResp;
import com.xftech.cdp.api.dto.resp.flow.strategy.FlowMonitorResp;
import com.xftech.cdp.domain.cache.CacheStrategyGroupService;
import com.xftech.cdp.domain.cache.CacheStrategyMarketChannelService;
import com.xftech.cdp.domain.cache.CacheStrategyMarketEventConditionService;
import com.xftech.cdp.domain.cache.CacheStrategyService;
import com.xftech.cdp.domain.flowctrl.model.enums.StrategyTypeEnum;
import com.xftech.cdp.domain.stat.service.impl.StatStrategyFlowDataServiceImpl;
import com.xftech.cdp.domain.strategy.exception.StrategyException;
import com.xftech.cdp.domain.strategy.model.enums.*;
import com.xftech.cdp.domain.strategy.model.strategy.FlowListQueryBO;
import com.xftech.cdp.domain.strategy.repository.StrategyCrowdPackRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyGroupRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyMarketChannelRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyMarketEventConditionRepository;
import com.xftech.cdp.domain.strategy.service.AminAuditLogService;
import com.xftech.cdp.domain.strategy.service.Constants;
import com.xftech.cdp.domain.strategy.service.StrategyService;
import com.xftech.cdp.domain.strategy.service.TelePushService;
import com.xftech.cdp.domain.strategy.service.impl.StrategyCommonService;
import com.xftech.cdp.infra.repository.cdp.strategy.po.*;
import com.xftech.cdp.infra.utils.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * @version $ FlowService, v 0.1 2023/12/15 13:53 yye.xu Exp $
 */
@Slf4j
@Service
@AllArgsConstructor
public class FlowService {
    private final StrategyService strategyService;
    private final StrategyFlowService strategyFlowService;
    private final StrategyFlowNodeService strategyFlowNodeService;
    private final StrategyCommonService strategyCommonService;
    private final CacheStrategyService cacheStrategyService;
    private final StrategyCrowdPackRepository strategyCrowdPackRepository;
    private final CacheStrategyGroupService cacheStrategyGroupService;
    private final TelePushService telePushService;
    private final CacheStrategyMarketChannelService cacheStrategyMarketChannelService;
    private final StrategyGroupRepository strategyGroupRepository;
    private final StrategyMarketChannelRepository strategyMarketChannelRepository;
    private final StrategyMarketEventConditionRepository strategyMarketEventConditionRepository;
    private final CacheStrategyMarketEventConditionService cacheStrategyMarketEventConditionService;
    private final AminAuditLogService aminAuditLogService;
    private final StatStrategyFlowDataServiceImpl statStrategyFlowDataService;

    private StrategyFlowDo buildStrategyFlow(String name, String businessType, Integer flowType,
                                             LocalDateTime validityBegin, LocalDateTime validityEnd, Integer status) {
        StrategyFlowDo strategyFlowDo = new StrategyFlowDo();
        strategyFlowDo.setFlowNo(SerialNumberUtil.nextId());
        strategyFlowDo.setName(name);
        strategyFlowDo.setBusinessType(businessType);
        strategyFlowDo.setFlowType(flowType.shortValue());
        strategyFlowDo.setValidityBegin(DateUtil.convert(validityBegin));
        strategyFlowDo.setValidityEnd(DateUtil.convert(validityEnd));
        strategyFlowDo.setStatus(status.shortValue());
        return strategyFlowDo;
    }

    private StrategyDo buildStrategyDo(StrategyFlowDo strategyFlowDo, CreateStrategyConfig stragegyConfig) {
        StrategyDo strategyDo = new StrategyDo();
        strategyDo.setFlowNo(strategyFlowDo.getFlowNo());

        strategyDo.setName(stragegyConfig.getName());
        strategyDo.setDetailDescription(Optional.ofNullable(stragegyConfig.getDetailDescription())
                .orElse(strategyDo.getName()));
        strategyDo.setAbTest(stragegyConfig.getAbTest());
        strategyDo.setAbType(stragegyConfig.getAbType());
        strategyDo.setSendRuler(stragegyConfig.getSendRuler());
        strategyDo.setValidityBegin(DateUtil.convert(strategyFlowDo.getValidityBegin()));
        strategyDo.setValidityEnd(DateUtil.convert(strategyFlowDo.getValidityEnd()));
        strategyDo.setBusinessType(strategyFlowDo.getBusinessType());
        strategyDo.setSendFrequency(JsonUtil.toJson(stragegyConfig.getSendFrequency()));
        // 初始状态
        strategyDo.setStatus(stragegyConfig.getStatus());
        strategyDo.setUpdatedOp(strategyFlowDo.getUpdatedOp());
        strategyDo.setUpdatedOpMobile(strategyFlowDo.getUpdatedOpMobile());

        strategyDo.setCrowdPackId(stragegyConfig.getCrowdPackIds());
        strategyDo.setMarketChannel(strategyCommonService.getMarketChannelStr(stragegyConfig.getStrategyGroups()));
        strategyDo.setBizKey(strategyCommonService.getBizKey(stragegyConfig.getAbType(), stragegyConfig.getBizKey()));
        strategyDo.setDispatchConfig(JsonUtil.toJson(stragegyConfig.getDispatchConfig()));
        return strategyDo;
    }

    private void saveMarketCondition(StrategyDo strategyDo, List<InstantStrategyCreateReq.MarketCondition> marketConditions,
                                     List<Integer> excludeTypes, StrategyTypeEnum strategyTypeEnum) {
        strategyCommonService.saveMarketConditionInfo(strategyDo, marketConditions,
                excludeTypes, strategyDo.getBusinessType(), strategyTypeEnum);
    }

    private void saveFlowCtrlRule(StrategyDo strategyDo, String BusinessTypeName, Integer limitDays, Integer limitTimes,
                                  StrategyTypeEnum strategyTypeEnum) {
        strategyCommonService.createFlowCtrlRule(BusinessTypeName,
                strategyDo, limitDays, limitTimes, strategyTypeEnum);
    }

    private void saveStrategyGroups(StrategyDo strategyDo, List<StrategyCreateReq.StrategyGroup> strategyGroups) {
        strategyGroups.forEach(strategyGroup -> {
            StrategyGroupDo strategyGroupDo = new StrategyGroupDo();
            strategyGroupDo.setStrategyId(strategyDo.getId());
            strategyGroupDo.setGroupConfig(JsonUtil.toJson(strategyGroup.getGroupConfig()));
            strategyGroupDo.setName(strategyGroup.getName());
            cacheStrategyGroupService.insert(strategyGroupDo);

            strategyGroup.getStrategyMarketChannels().forEach(strategyMarketChannel -> {
                strategyService.verifyChannel(strategyMarketChannel);
                // 推送新电销
                if (strategyMarketChannel.getMarketChannel() == StrategyMarketChannelEnum.VOICE_NEW.getCode()) {
//                    telePushService.pushTeleAndSetTemplateId(strategyMarketChannel);
                }
                StrategyMarketChannelDo strategyMarketChannelDo = new StrategyMarketChannelDo();
                strategyMarketChannelDo.setStrategyId(strategyDo.getId());
                strategyMarketChannelDo.setStrategyGroupId(strategyGroupDo.getId());
                strategyMarketChannelDo.setSendTime(strategyMarketChannel.getSendTime());
                strategyMarketChannelDo.setMarketChannel(strategyMarketChannel.getMarketChannel());
                strategyMarketChannelDo.setTemplateId(strategyMarketChannel.getTemplateId());
                strategyMarketChannelDo.setApp(strategyMarketChannel.getApp());
                if (!StringUtils.isEmpty(strategyMarketChannel.getExtInfo())){
                    strategyMarketChannelDo.setExtInfo(strategyMarketChannel.getExtInfo());
                }
                cacheStrategyMarketChannelService.insert(strategyMarketChannelDo);
            });
        });
    }

    private StrategyDo createOfflineStrategy(StrategyFlowDo strategyFlowDo, CreateStrategyConfig stragegy) {
        // 校验分组配置
        strategyCommonService.verifyGroups(stragegy.getStrategyGroups(), stragegy.getAbTest(), stragegy.getAbType(), stragegy.getBizKey());
        // 保存策略
        StrategyDo strategyDo = buildStrategyDo(strategyFlowDo, stragegy);
        cacheStrategyService.insert(strategyDo);
        // 校验触达流控
        if (StrategyRulerEnum.getCycleCodes()
                .contains(stragegy.getSendRuler())) {
            FlowCtrlLimit flowCtrlLimit = stragegy.getFlowCtrlLimit();
            strategyCommonService.verifyRuleConfig(flowCtrlLimit.getLimitDays(), flowCtrlLimit.getLimitTimes());
            // 流控入库
            saveFlowCtrlRule(strategyDo, BusinessTypeEnum.getByCode(strategyDo.getBusinessType()).getDescription(),
                    flowCtrlLimit.getLimitDays(), flowCtrlLimit.getLimitTimes(), StrategyTypeEnum.OFFLINE_STRATEGY);
        }
        // 保存人群
        List<StrategyCrowdPackDo> strategyCrowdPackDoList = new ArrayList<>();
        strategyService.updateStrategyCrowdPack(stragegy.getCrowdPackIds(), strategyCrowdPackDoList, strategyDo.getId());
        if (strategyCrowdPackDoList.size() > 0) {
            strategyCrowdPackRepository.saveBatch(strategyCrowdPackDoList);
        }
        // 保存规则条件
        saveMarketCondition(strategyDo, stragegy.getMarketCondition(), stragegy.getExcludeType(), StrategyTypeEnum.OFFLINE_STRATEGY);
        // 保存分组
        saveStrategyGroups(strategyDo, stragegy.getStrategyGroups());
        return strategyDo;
    }

    private List<StrategyFlowNodeDo> saveOfflineStrategy(StrategyFlowDo strategyFlowDo, List<CreateStrategyConfig> stragegyConfigs) {
        List<StrategyFlowNodeDo> strategyFlowNodes = new ArrayList<>();
        if (CollectionUtils.isEmpty(stragegyConfigs)) {
            return strategyFlowNodes;
        }
        // 构建策略节点
        List<Integer> marketChannels = new ArrayList<>();
        for (int i = 0; i < stragegyConfigs.size(); i++) {
            StrategyFlowNodeDo strategyFlowNodeDo = new StrategyFlowNodeDo();
            CreateStrategyConfig stragegy = stragegyConfigs.get(i);
            strategyFlowNodeDo.setFlowNo(strategyFlowDo.getFlowNo());
            strategyFlowNodeDo.setLevelNum(i);
            strategyFlowNodeDo.setNodeId(stragegy.getNode().getNodeId());
            // 获取上一个节点编号
            if (strategyFlowNodes.size() > 0) {
                strategyFlowNodeDo.setParentId(strategyFlowNodes.get(strategyFlowNodes.size() - 1).getNodeId());
            }
            strategyFlowNodes.add(strategyFlowNodeDo);
            // 离线首个策略， 校验人群包
            if (i == 0) {
                strategyFlowDo.setSendRuler(stragegy.getSendRuler().shortValue());
                strategyCommonService.verifyCrowIds(stragegy.getCrowdPackIds(), strategyFlowDo.getBusinessType());
            }
            StrategyDo strategyDo = createOfflineStrategy(strategyFlowDo, stragegy);
            strategyFlowNodeDo.setStrategyId(strategyDo.getId());

            List<Integer> channels = Arrays.stream(Optional.ofNullable(strategyDo.getMarketChannel()).orElse("").split(","))
                    .map(Integer::parseInt).collect(Collectors.toList());
            marketChannels.addAll(channels);
        }
        strategyFlowDo.setMarketChannels(JsonUtil.toJson(marketChannels.stream()
                .distinct().collect(Collectors.toList())));
        return strategyFlowNodes;
    }

    private void updateOfflineStrategy(StrategyFlowDo strategyFlowDo, StrategyDo strategyDo, UpdateStrategyConfig updateStrategyConfig) {
        if (!StringUtils.equalsIgnoreCase(strategyDo.getFlowNo(),
                strategyFlowDo.getFlowNo())) {
            throw new StrategyException("策略信息有误");
        }
        strategyDo.setName(updateStrategyConfig.getName());
        strategyDo.setStatus(updateStrategyConfig.getStatus());
        strategyDo.setAbTest(updateStrategyConfig.getAbTest());
        strategyDo.setAbType(updateStrategyConfig.getAbType());
        strategyDo.setSendRuler(updateStrategyConfig.getSendRuler());
        strategyDo.setValidityBegin(DateUtil.convert(strategyFlowDo.getValidityBegin()));
        strategyDo.setValidityEnd(DateUtil.convert(strategyFlowDo.getValidityEnd()));
        strategyDo.setBusinessType(strategyFlowDo.getBusinessType());
        strategyDo.setSendFrequency(JsonUtil.toJson(updateStrategyConfig.getSendFrequency()));
        // 初始状态
        strategyDo.setUpdatedTime(null);
        strategyDo.setUpdatedOp(strategyFlowDo.getUpdatedOp());
        strategyDo.setUpdatedOpMobile(strategyFlowDo.getUpdatedOpMobile());
        strategyDo.setCrowdPackId(updateStrategyConfig.getCrowdPackIds());
        strategyDo.setMarketChannel(strategyCommonService.getMarketChannelStr(updateStrategyConfig.getStrategyGroups()));
        strategyDo.setBizKey(strategyCommonService.getBizKey(updateStrategyConfig.getAbType(), updateStrategyConfig.getBizKey()));
        strategyDo.setDispatchConfig(JsonUtil.toJson(updateStrategyConfig.getDispatchConfig()));
        cacheStrategyService.updateById(strategyDo);

        List<StrategyCrowdPackDo> strategyCrowdPackDoList = new ArrayList<>();
        strategyService.updateStrategyCrowdPack(updateStrategyConfig.getCrowdPackIds(), strategyCrowdPackDoList, strategyDo.getId());

        List<Long> strategyGroupIdList = new ArrayList<>();
        List<Long> strategyChannelIdList = new ArrayList<>();
        List<Long> strategyMarketConditionIdList = new ArrayList<>();
        // 更新策略组
        updateStrategyConfig.getStrategyGroups().forEach(strategyGroup -> {
            //更新策略组
            StrategyGroupDo strategyGroupDo = strategyCommonService.updateStrategyGroup(strategyGroup, strategyDo.getId());
            strategyGroupIdList.add(strategyGroupDo.getId());
            // 更新触达渠道
            strategyGroup.getStrategyMarketChannels().forEach(strategyMarketChannel -> {
                strategyService.verifyChannel(strategyMarketChannel);
                StrategyMarketChannelDo strategyMarketChannelDo = strategyCommonService.updateStrategyMarketChannels(strategyMarketChannel, strategyDo, strategyGroupDo,
                        updateStrategyConfig.getSendFrequency(), strategyDo.getName());
                strategyChannelIdList.add(strategyMarketChannelDo.getId());
            });
        });
        // 更新策略人群包
        strategyCrowdPackRepository.deleteByStrategyId(updateStrategyConfig.getStrategyId());
        strategyCrowdPackRepository.saveBatch(strategyCrowdPackDoList);
        // 更新策略营销规则
        // 更新触达渠道
        updateStrategyConfig.getMarketCondition().forEach(marketCondition -> {
            StrategyMarketEventConditionDo strategyMarketEventConditionDo = strategyCommonService.updateMarketConditionInfo(marketCondition, strategyDo.getId(), StrategyTypeEnum.OFFLINE_STRATEGY);
            strategyMarketConditionIdList.add(strategyMarketEventConditionDo.getId());
        });
        // 更新营销排除项
        strategyCommonService.updateExcludeLabel(strategyDo.getId(), updateStrategyConfig.getExcludeType(), strategyDo.getBusinessType(), StrategyTypeEnum.OFFLINE_STRATEGY);
        // 删除 被删除组
        List<StrategyGroupDo> strategyGroupDoList = strategyGroupRepository.selectListByStrategyId(strategyDo.getId());
        List<Long> delGroupIdList = strategyGroupDoList.stream().map(StrategyGroupDo::getId).filter(id -> !strategyGroupIdList.contains(id)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(delGroupIdList)) {
            cacheStrategyGroupService.deleteBatch(delGroupIdList);
        }
        // 删除 被删除渠道
        List<StrategyMarketChannelDo> strategyMarketChannelDos = strategyMarketChannelRepository.selectByStrategyId(strategyDo.getId());
        List<StrategyMarketChannelDo> delChannelIdList = strategyMarketChannelDos.stream().filter(item -> !strategyChannelIdList.contains(item.getId())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(delChannelIdList)) {
            List<Long> delIdList = delChannelIdList.stream().map(StrategyMarketChannelDo::getId).collect(Collectors.toList());
            cacheStrategyMarketChannelService.deleteByIdBatch(delIdList);
        }
        // 删除 被删除营销条件(默认排除项除外)
        List<StrategyMarketEventConditionDo> strategyMarketEventConditionDoList = strategyMarketEventConditionRepository.getByStrategyIdAndOption(strategyDo.getId());
        List<Long> delEventConditionIdList = strategyMarketEventConditionDoList.stream().filter(t -> StrategyInstantLabelOptionEnum.INPUT_OPTIONAL.getCode() == t.getOptional())
                .map(StrategyMarketEventConditionDo::getId).filter(id -> !strategyMarketConditionIdList.contains(id)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(delEventConditionIdList)) {
            cacheStrategyMarketEventConditionService.deleteBatch(delEventConditionIdList);
        }
        //更新流控配置
        StrategyUpdateReq strategyUpdateReq = new StrategyUpdateReq();
        strategyUpdateReq.setLimitDays(updateStrategyConfig.getFlowCtrlLimit().getLimitDays());
        strategyUpdateReq.setLimitTimes(updateStrategyConfig.getFlowCtrlLimit().getLimitTimes());
        strategyUpdateReq.setSendRuler(updateStrategyConfig.getSendRuler());
        strategyUpdateReq.setBusinessTypeName(BusinessTypeEnum.getByCode(strategyFlowDo.getBusinessType()).getDescription());
        strategyService.updateRuleConfig(strategyUpdateReq, strategyDo);
    }


    private List<StrategyFlowNodeDo> updateOfflineStrategy(StrategyFlowDo strategyFlowDo, List<UpdateStrategyConfig> stragegyConfigs) {
        List<StrategyFlowNodeDo> strategyFlowNodes = new ArrayList<>();
        List<Integer> marketChannels = new ArrayList<>();
        for (int i = 0; i < stragegyConfigs.size(); i++) {
            StrategyFlowNodeDo strategyFlowNodeDo = new StrategyFlowNodeDo();
            UpdateStrategyConfig strategy = stragegyConfigs.get(i);
            strategyFlowNodeDo.setFlowNo(strategyFlowDo.getFlowNo());
            strategyFlowNodeDo.setLevelNum(i);
            strategyFlowNodeDo.setNodeId(strategy.getNode().getNodeId());
            if (i == 0) {
                strategyCommonService.verifyCrowIds(strategy.getCrowdPackIds(), strategyFlowDo.getBusinessType());
            }
            // 获取上一个节点编号
            if (strategyFlowNodes.size() > 0) {
                strategyFlowNodeDo.setParentId(strategyFlowNodes.get(strategyFlowNodes.size() - 1).getNodeId());
            }
            strategyFlowNodes.add(strategyFlowNodeDo);
            StrategyDo strategyDo = null;
            strategyCommonService.verifyGroups(strategy.getStrategyGroups(), strategy.getAbTest(), strategy.getAbType(), strategy.getBizKey());
            if (strategy.getStrategyId() != null && strategy.getStrategyId() > 0) {
                strategyDo = cacheStrategyService.selectByIdNoCache(strategy.getStrategyId());
                updateOfflineStrategy(strategyFlowDo, strategyDo, strategy);
            } else {
                // 新增策略
                CreateStrategyConfig createStrategy = JsonUtil.parse(JsonUtil.toJson(strategy), CreateStrategyConfig.class);
                strategyDo = createOfflineStrategy(strategyFlowDo, createStrategy);
            }

            strategyFlowNodeDo.setStrategyId(strategyDo.getId());
            List<Integer> channels = Arrays.stream(Optional.ofNullable(strategyDo.getMarketChannel()).orElse("").split(","))
                    .map(Integer::parseInt).collect(Collectors.toList());
            marketChannels.addAll(channels);
        }
        strategyFlowDo.setMarketChannels(JsonUtil.toJson(marketChannels.stream()
                .distinct().collect(Collectors.toList())));
        // 删除无效的策略
        List<StrategyFlowNodeDo> strategyFlowNodeDos = strategyFlowNodeService.select(strategyFlowDo.getFlowNo());
        Set<Long> strategyIds = strategyFlowNodeDos.stream().map(StrategyFlowNodeDo::getStrategyId)
                .collect(Collectors.toSet());
        Set<Long> currentStrategyIds = strategyFlowNodes.stream().map(StrategyFlowNodeDo::getStrategyId)
                .collect(Collectors.toSet());
        strategyIds.removeAll(currentStrategyIds);
        strategyIds.forEach(strategyService::deleteById);
        return strategyFlowNodes;
    }

    @Transactional(transactionManager = "transactionManager", rollbackFor = Exception.class)
    public void create(FlowCreateReq request) {
        LoginUser loginUser = SsoUtil.get();
        Pair<Boolean, String> validPair = request.isValid();
        if (!Objects.equals(true, validPair.getKey())) {
            throw new StrategyException(validPair.getValue());
        }
        // 构建画布对象
        StrategyFlowDo strategyFlowDo = buildStrategyFlow(request.getName(), request.getBusinessType(), request.getFlowType(),
                request.getValidityBegin(), request.getValidityEnd(), request.getStatus());
        strategyFlowDo.setCreatedOp(loginUser.getName());
        strategyFlowDo.setCreatedOpMobile(loginUser.getMobile());
        strategyFlowDo.setUpdatedOp(strategyFlowDo.getCreatedOp());
        strategyFlowDo.setCreatedOpMobile(strategyFlowDo.getCreatedOpMobile());

        if (FlowTypeEnum.isOffline(strategyFlowDo.getFlowType())) {
            List<StrategyFlowNodeDo> flowNodeDos = saveOfflineStrategy(strategyFlowDo, request.getStragegyList());
            strategyFlowNodeService.batchInsert(flowNodeDos);
        }
        strategyFlowService.insert(strategyFlowDo);
        //收集操作对象id
        OperateLogObjectIdUtils.set(Collections.singletonList(strategyFlowDo.getId()));
        // 日志行为
        aminAuditLogService.insert(Constants.FLOW_CREATE, JsonUtil.toJson(request), loginUser.getName(), loginUser.getMobile());
    }

    @Transactional(transactionManager = "transactionManager", rollbackFor = Exception.class)
    public void update(FlowUpdateReq request) {
        StrategyFlowDo strategyFlowDo = strategyFlowService.select(request.getFlowNo());
        if (StrategyFlowStatusEnum.getDisabledStatus().contains(strategyFlowDo.getStatus()
                .intValue())) {
            throw new StrategyException("画布已结束,不可以更新");
        }

        if (!request.isValid()) {
            throw new StrategyException("参数错误");
        }

        if (!Objects.equals(request.getFlowType(),
                strategyFlowDo.getFlowType().intValue())) {
            throw new StrategyException("画布类型不可以被修改");
        }

        UpdateStrategyConfig updateStrategy = request.getStragegyList().get(0);
        if (!Objects.equals(updateStrategy.getSendRuler(),
                strategyFlowDo.getSendRuler().intValue())) {
            throw new StrategyException("发送类型不可以修改");
        }
        // 画布状态更新
        LoginUser loginUser = SsoUtil.get();
        strategyFlowDo.setUpdatedTime(null);
        strategyFlowDo.setUpdatedOp(loginUser.getName());
        strategyFlowDo.setUpdatedOpMobile(loginUser.getMobile());
        strategyFlowDo.setName(request.getName());
        strategyFlowDo.setStatus(request.getStatus().shortValue());
        strategyFlowDo.setValidityBegin(DateUtil.convert(request.getValidityBegin()));
        strategyFlowDo.setValidityEnd(DateUtil.convert(request.getValidityEnd()));

        if (FlowTypeEnum.isOffline(strategyFlowDo.getFlowType())) {
            List<StrategyFlowNodeDo> flowNodeDos = updateOfflineStrategy(strategyFlowDo, request.getStragegyList());
            strategyFlowNodeService.deleteByFlowNo(request.getFlowNo());
            strategyFlowNodeService.batchInsert(flowNodeDos);
        }
        strategyFlowService.updateSelective(strategyFlowDo);

        //收集操作对象id
        OperateLogObjectIdUtils.set(Collections.singletonList(strategyFlowDo.getId()));
        // 日志行为
        aminAuditLogService.insert(Constants.FLOW_EDIT, JsonUtil.toJson(request), loginUser.getName(), loginUser.getMobile());
    }

    public FlowDetailResp detail(String flowNo) {
        StrategyFlowDo strategyFlowDo = strategyFlowService.select(flowNo);
        if (strategyFlowDo == null) {
            return null;
        }
        FlowDetailResp flowDetailResp = FlowDetailResp.builder()
                .id(strategyFlowDo.getId())
                .flowNo(strategyFlowDo.getFlowNo())
                .name(strategyFlowDo.getName())
                .flowType(strategyFlowDo.getFlowType().intValue())
                .businessType(strategyFlowDo.getBusinessType())
                .validityBegin(DateUtil.convertStr(strategyFlowDo.getValidityBegin()))
                .validityEnd(DateUtil.convertStr(strategyFlowDo.getValidityEnd()))
                .sendRuler(strategyFlowDo.getSendRuler().intValue())
                .status(strategyFlowDo.getStatus().intValue())
                .marketChannels(StringUtils.isEmpty(strategyFlowDo.getMarketChannels()) ? new ArrayList<>()
                        : JSON.parseArray(strategyFlowDo.getMarketChannels(), Integer.class))
                .createdOp(strategyFlowDo.getCreatedOp())
                .updatedOp(strategyFlowDo.getUpdatedOp())
                .createdTime(DateUtil.convertStr(strategyFlowDo.getCreatedTime()))
                .updatedTime(DateUtil.convertStr(strategyFlowDo.getUpdatedTime()))
                .build();

        List<StrategyFlowNodeDo> strategyFlowNodeDos = strategyFlowNodeService.select(flowDetailResp.getFlowNo());
        List<DetailStragegyConfig> stragegyList = new ArrayList<>(strategyFlowNodeDos.size());
        for (StrategyFlowNodeDo node : strategyFlowNodeDos) {
            StrategyDetailResp strategyDetailResp = strategyService.getDetail(node.getStrategyId());
            if (strategyDetailResp != null) {
                DetailStragegyConfig stragegyConfig = new DetailStragegyConfig();
                Node curNode = Node.builder()
                        .nodeId(node.getNodeId()).build();
                stragegyConfig.setNode(curNode);
                StrategyFlowNodeDo nextNode = strategyFlowNodeDos.stream().filter(x -> StringUtils.equalsIgnoreCase(x.getParentId(), curNode.getNodeId()))
                        .findFirst().orElse(null);
                if (nextNode != null) {
                    Node next = Node.builder()
                            .nodeId(nextNode.getNodeId()).build();
                    stragegyConfig.setNextNode(next);
                }
                stragegyConfig.setFlowNo(strategyFlowDo.getFlowNo());
                stragegyConfig.setStrategyId(node.getStrategyId());
                stragegyConfig.setName(strategyDetailResp.getName());
                stragegyConfig.setSendFrequency(JsonUtil.parse(JsonUtil.toJson(strategyDetailResp.getSendFrequency()),
                        StrategyCreateReq.SendFrequency.class));
                stragegyConfig.setAbTest(strategyDetailResp.getAbTest());
                stragegyConfig.setAbType(strategyDetailResp.getAbType());
                stragegyConfig.setRandomItem(strategyDetailResp.getRandomItem());
                stragegyConfig.setSendRuler(strategyDetailResp.getSendRuler());
                stragegyConfig.setStrategyGroups(strategyDetailResp.getStrategyGroups());
                stragegyConfig.setMarketCondition(strategyDetailResp.getMarketCondition());
                stragegyConfig.setCrowdPackIds(strategyDetailResp.getCrowdPackIds());
                stragegyConfig.setStatus(strategyDetailResp.getStatus());
                stragegyConfig.setMarketChannels(strategyDetailResp.getMarketChannels());
                stragegyConfig.setExcludeType(strategyDetailResp.getExcludeType());
                if (strategyDetailResp.getLimitDays() != null) {
                    FlowCtrlLimit flowCtrlLimit = new FlowCtrlLimit();
                    flowCtrlLimit.setLimitDays(strategyDetailResp.getLimitDays());
                    flowCtrlLimit.setLimitTimes(strategyDetailResp.getLimitTimes());
                    stragegyConfig.setFlowCtrlLimit(flowCtrlLimit);
                }
                stragegyConfig.setExcludeType(strategyDetailResp.getExcludeType());
                stragegyConfig.setDispatchConfig(JsonUtil.parse(strategyDetailResp.getDispatchConfig(), DispatchConfig.class));
                stragegyConfig.setDispatchType(strategyDetailResp.getDispatchType());
                stragegyList.add(stragegyConfig);
            }
        }
        flowDetailResp.setStragegyList(stragegyList);
        return flowDetailResp;
    }

    public boolean operate(FlowOperateReq request) {
        LoginUser loginUser = SsoUtil.get();
        StrategyOperateEnum operateEnum = StrategyOperateEnum.getInstance(request.getRunType());
        if (operateEnum == null) {
            throw new StrategyException("状态未知");
        }
        StrategyFlowDo strategyFlowDo = strategyFlowService.select(request.getFlowNo());
        StrategyFlowStatusEnum strategyFlowStatusEnum = StrategyFlowStatusEnum.getInstance(strategyFlowDo.getStatus().intValue());
        if (StrategyFlowStatusEnum.getDisabledStatus().contains(strategyFlowStatusEnum.getCode())) {
            throw new StrategyException("画布已经结束, 不可以再更新");
        }
        switch (operateEnum) {
            case PAUSE:
                strategyFlowDo.setStatus((short) StrategyFlowStatusEnum
                        .PAUSING.getCode());
                break;
            case ENABLE:
            case PUBLISH:
                strategyFlowDo.setStatus((short) StrategyFlowStatusEnum
                        .INIT.getCode());
                break;
        }
        strategyFlowService.updateSelective(strategyFlowDo);
        //收集操作对象id
        OperateLogObjectIdUtils.set(Collections.singletonList(strategyFlowDo.getId()));
        aminAuditLogService.insert(Constants.FLOW_OPERATE, JsonUtil.toJson(request), loginUser.getName(), loginUser.getMobile());
        return true;
    }

    public PageResultResponse<Flow> list(FlowListReq request) {
        PageResultResponse<Flow> flowPageResultResponse = new PageResultResponse<>();
        FlowListQueryBO flowListQueryBO = JsonUtil.parse(JsonUtil.toJson(request),
                FlowListQueryBO.class);
        List<StrategyFlowDo> strategyFlowDos = strategyFlowService.selectList(flowListQueryBO);
        flowPageResultResponse.setTotal(strategyFlowDos.size());
        flowPageResultResponse.setPages((int) Math.ceil(strategyFlowDos.size() /
                (double) request.getSize()));
        List<StrategyFlowDo> currentList = strategyFlowDos.stream()
                .skip(request.getBeginNum())
                .limit(request.getSize())
                .collect(Collectors.toList());
        flowPageResultResponse.setCurrent(currentList.size());
        List<Flow> flows = new ArrayList<>();
        for (StrategyFlowDo item : currentList) {
            Flow flow = Flow.builder()
                    .id(item.getId())
                    .flowNo(item.getFlowNo())
                    .name(item.getName())
                    .flowType((int) item.getFlowType())
                    .businessType(item.getBusinessType())
                    .validityBegin(DateUtil.convertStr(item.getValidityBegin()))
                    .validityEnd(DateUtil.convertStr(item.getValidityEnd()))
                    .sendRuler((int) item.getSendRuler())
                    .status((int) item.getStatus())
                    .marketChannels(StringUtils.isEmpty(item.getMarketChannels()) ? new ArrayList<>()
                            : JSON.parseArray(item.getMarketChannels(), Integer.class))
                    .createdOp(item.getCreatedOp())
                    .updatedOp(item.getUpdatedOp())
                    .updatedTime(DateUtil.convertStr(item.getUpdatedTime()))
                    .createdTime(DateUtil.convertStr(item.getCreatedTime()))
                    .build();
            List<StrategyFlowNodeDo> nodes = strategyFlowNodeService.select(flow.getFlowNo());
            flow.setStrategyIds(nodes.stream().map(StrategyFlowNodeDo::getStrategyId)
                    .collect(Collectors.toList()));
            flows.add(flow);
        }
        flowPageResultResponse.setRecords(flows);
        return flowPageResultResponse;
    }

    public PageResultResponse<FlowMonitorResp> monitor(FlowMonitorReq request) {
        return statStrategyFlowDataService.monitor(request);
    }
}