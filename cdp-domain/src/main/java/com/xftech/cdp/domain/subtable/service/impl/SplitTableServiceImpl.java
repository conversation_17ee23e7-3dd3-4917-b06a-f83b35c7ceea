package com.xftech.cdp.domain.subtable.service.impl;

import com.xftech.cdp.domain.crowd.repository.CrowdDetailRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdDetailSubRepository;
import com.xftech.cdp.domain.subtable.repository.SplitTableCurRepository;
import com.xftech.cdp.domain.subtable.repository.SplitTableRouteRepository;
import com.xftech.cdp.domain.subtable.service.SplitTableService;
import com.xftech.cdp.infra.config.CrowdConfig;
import com.xftech.cdp.infra.constant.Constants;
import com.xftech.cdp.infra.constant.RedisKeyConstants;
import com.xftech.cdp.infra.constant.TransConstants;
import com.xftech.cdp.infra.repository.subtable.po.SplitTableCur;
import com.xftech.cdp.infra.repository.subtable.po.SplitTableRoute;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
public class SplitTableServiceImpl implements SplitTableService {
    @Autowired
    public CrowdDetailRepository crowdDetailRepository;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private CrowdConfig crowdConfig;
    @Autowired
    private SplitTableCurRepository splitTableCurRepository;
    @Autowired
    private SplitTableRouteRepository splitTableRouteRepository;
    @Autowired
    private CrowdDetailSubRepository crowdDetailSubRepository;

    @Override
    public List<String> getTableNameByLogId(Long crowdExceLogId) {
        List<SplitTableRoute> splitTableRouteList = splitTableRouteRepository.selectTableNos(crowdExceLogId);
        List<String> tableNames = new ArrayList<>();
        if (CollectionUtils.isEmpty(splitTableRouteList)) {
            tableNames.add(Constants.CROWD_DETAIL_STR);
            return tableNames;
        }
        splitTableRouteList.forEach(splitTableRoute -> {
            tableNames.add(TransConstants.crowdDetailTableName(splitTableRoute.getTableNo()));
        });
        return tableNames;
    }

    @Override
    // 获取批量写入分表序号
    public int getBatchSaveTableNo(String tableName, int batchSize) {
        // 获取当前分表序号
        int curTableNo = this.getCurTableNo(tableName);
        // 获取当前分表记录数
        int curTableSize = this.getCurTableSize(curTableNo);

        // 分表记录数 > 配置分表数 ？ 分表序号 + 1 : 分表序号不变
        if (curTableSize > crowdConfig.getCrowdDetailTableSize()) {
            // 当前分表记录数 > 分表大小，当前分表序号 + 1 取模
            curTableNo = (curTableNo + 1) % 100;
            // 更新分表记录当前分表序号
            splitTableCurRepository.updateSplitTableCurNo(curTableNo, tableName);
            // 当前分表序号自增
            redisTemplate.opsForValue().set(RedisKeyConstants.CROWD_DETAIL_CUR_TABLE_NO, curTableNo);
            // 该分表记录数
            curTableSize = 0;
        }

        // 计算该分表记录数，更新 Redis 对应分表大小
        redisTemplate.opsForValue().set(TransConstants.crowdDetailTableKey(curTableNo), curTableSize + batchSize);

        return curTableNo;
    }

    // 获取当前分表序号
    public int getCurTableNo(String tableName) {
        Boolean hasKey = redisTemplate.hasKey(RedisKeyConstants.CROWD_DETAIL_CUR_TABLE_NO);
        if (hasKey) {
            Object object = redisTemplate.opsForValue().get(RedisKeyConstants.CROWD_DETAIL_CUR_TABLE_NO);
            if (Objects.nonNull(object)) {
                return (int) object;
            }
        }

        // 当前分表记录分表序号
        SplitTableCur splitTableCur = splitTableCurRepository.selectTableCurNo(tableName);
        int curTableNo = splitTableCur.getCurTableNo();
        // 写入 Redis
        redisTemplate.opsForValue().set(RedisKeyConstants.CROWD_DETAIL_CUR_TABLE_NO, curTableNo);
        return curTableNo;
    }

    // 获取当前分表记录数
    public int getCurTableSize(int curTableNo) {
        String tableKey = TransConstants.crowdDetailTableKey(curTableNo);
        Boolean hasKey = redisTemplate.hasKey(tableKey);
        if (hasKey) {
            // Redis 存在 key，存在返回key对应记录数
            Object object = redisTemplate.opsForValue().get(tableKey);
            if (Objects.nonNull(object)) {
                return (int) object;
            }
        }
        // Redis 不存在 key，统计分表记录数
        int tableSize = crowdDetailSubRepository.countCrowdDetail(curTableNo);
        // 写入 Redis
        redisTemplate.opsForValue().set(tableKey, tableSize);
        return tableSize;
    }
}
