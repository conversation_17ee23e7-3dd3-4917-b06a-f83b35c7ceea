/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.rocketmq.advanceOrder;

import com.xftech.cdp.infra.rocketmq.MessageHandlerSelector;
import com.xftech.cdp.infra.utils.WhitelistSwitchUtil;
import com.xinfei.xfframework.common.starter.mq.MqConsumerListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


@Component
@Slf4j
@RocketMQMessageListener(consumerGroup = "cg_xyf_cdp_tp_psenginecore_advance_order_create", topic = "tp_psenginecore_advance_order_create", consumeMode = ConsumeMode.CONCURRENTLY)
public class AdvanceOrderRocketMsgConsumer extends MqConsumerListener<String> {

    @Resource
    private MessageHandlerSelector handlerSelector;

    @Override
    public void doMessage(String topic, String bodyMessage, MessageExt messageExt) {
        if (WhitelistSwitchUtil.boolSwitchByApollo("AdvanceOrderRocketMsgConsumerEnable")) {
            log.info("AdvanceOrderRocketMsgConsumer doMessage topic={} messageId={} body={}", topic, messageExt.getMsgId(), bodyMessage);
            try {
                handlerSelector.doMessage(topic, null, messageExt.getMsgId(), bodyMessage);
            } catch (Exception e) {
                log.error("AdvanceOrderRocketMsgConsumer doMessage error topic={} messageId={} body={}", topic, messageExt.getMsgId(), bodyMessage, e);
            }
        }
    }

}