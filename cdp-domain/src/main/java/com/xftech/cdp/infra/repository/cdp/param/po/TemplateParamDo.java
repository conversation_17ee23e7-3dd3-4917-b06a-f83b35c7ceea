package com.xftech.cdp.infra.repository.cdp.param.po;

import com.xftech.cdp.infra.repository.Do;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 模板参数管理表
 *
 * @TableName template_param
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TemplateParamDo extends Do {

    /**
     * 参数名称
     */
    private String name;

    /**
     * 参数备注
     */
    private String description;

    /**
     * 参数key值
     */
    private String paramKey;

    /**
     * 需求方
     */
    private String demandSide;

    private static final long serialVersionUID = 1L;
}