package com.xinfei.touch.domain.model.unified;

import com.xinfei.touch.domain.model.TouchStatus;
import lombok.Data;
import java.util.List;

/**
 * 触达响应统一模型
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-23
 */
@Data
public class TouchResponse {
    
    /**
     * 请求唯一标识
     */
    private String requestId;
    
    /**
     * 触达状态
     */
    private TouchStatus status;
    
    /**
     * 批次号
     */
    private String batchNo;
    
    /**
     * 错误码
     */
    private String errorCode;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 响应时间戳
     */
    private Long timestamp;
    
    /**
     * 单用户响应结果（单用户模式）
     */
    private TouchUserResult userResult;
    
    /**
     * 用户结果列表（批量模式）
     */
    private List<TouchUserResult> userResults;
    
    /**
     * 批量处理结果
     */
    private BatchResult batchResult;
    
    /**
     * 触达统计信息
     */
    private TouchStatistics statistics;
    
    /**
     * 创建成功响应
     */
    public static TouchResponse success(String requestId) {
        TouchResponse response = new TouchResponse();
        response.setRequestId(requestId);
        response.setStatus(TouchStatus.SUCCESS);
        response.setTimestamp(System.currentTimeMillis());
        return response;
    }
    
    /**
     * 创建成功响应（单用户）
     */
    public static TouchResponse success(String requestId, String batchNo, TouchUserResult userResult) {
        TouchResponse response = success(requestId);
        response.setBatchNo(batchNo);
        response.setUserResult(userResult);
        return response;
    }
    
    /**
     * 创建成功响应（批量）
     */
    public static TouchResponse success(String requestId, String batchNo, List<TouchUserResult> userResults, BatchResult batchResult) {
        TouchResponse response = success(requestId);
        response.setBatchNo(batchNo);
        response.setUserResults(userResults);
        response.setBatchResult(batchResult);
        return response;
    }
    
    /**
     * 创建失败响应
     */
    public static TouchResponse failed(String requestId, String errorCode, String errorMessage) {
        TouchResponse response = new TouchResponse();
        response.setRequestId(requestId);
        response.setStatus(TouchStatus.FAILED);
        response.setErrorCode(errorCode);
        response.setErrorMessage(errorMessage);
        response.setTimestamp(System.currentTimeMillis());
        return response;
    }
    
    /**
     * 创建流控响应
     */
    public static TouchResponse flowControlled(String requestId, String reason) {
        TouchResponse response = new TouchResponse();
        response.setRequestId(requestId);
        response.setStatus(TouchStatus.FLOW_CONTROLLED);
        response.setErrorMessage(reason);
        response.setTimestamp(System.currentTimeMillis());
        return response;
    }
    
    /**
     * 创建处理中响应
     */
    public static TouchResponse pending(String requestId, String batchNo) {
        TouchResponse response = new TouchResponse();
        response.setRequestId(requestId);
        response.setStatus(TouchStatus.PENDING);
        response.setBatchNo(batchNo);
        response.setTimestamp(System.currentTimeMillis());
        return response;
    }
    
    /**
     * 创建部分成功响应
     */
    public static TouchResponse partialSuccess(String requestId, String batchNo, List<TouchUserResult> userResults, BatchResult batchResult) {
        TouchResponse response = new TouchResponse();
        response.setRequestId(requestId);
        response.setStatus(TouchStatus.PARTIAL_SUCCESS);
        response.setBatchNo(batchNo);
        response.setUserResults(userResults);
        response.setBatchResult(batchResult);
        response.setTimestamp(System.currentTimeMillis());
        return response;
    }
    
    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return TouchStatus.SUCCESS.equals(status);
    }
    
    /**
     * 判断是否失败
     */
    public boolean isFailed() {
        return TouchStatus.FAILED.equals(status);
    }
    
    /**
     * 判断是否被流控
     */
    public boolean isFlowControlled() {
        return TouchStatus.FLOW_CONTROLLED.equals(status);
    }
    
    /**
     * 判断是否处理中
     */
    public boolean isPending() {
        return TouchStatus.PENDING.equals(status);
    }
    
    /**
     * 判断是否部分成功
     */
    public boolean isPartialSuccess() {
        return TouchStatus.PARTIAL_SUCCESS.equals(status);
    }
    
    /**
     * 获取成功用户数
     */
    public int getSuccessCount() {
        if (userResult != null) {
            return userResult.isSuccess() ? 1 : 0;
        }
        if (userResults != null) {
            return (int) userResults.stream().filter(TouchUserResult::isSuccess).count();
        }
        if (batchResult != null) {
            return batchResult.getSuccessCount() != null ? batchResult.getSuccessCount() : 0;
        }
        return 0;
    }
    
    /**
     * 获取失败用户数
     */
    public int getFailedCount() {
        if (userResult != null) {
            return userResult.isFailed() ? 1 : 0;
        }
        if (userResults != null) {
            return (int) userResults.stream().filter(TouchUserResult::isFailed).count();
        }
        if (batchResult != null) {
            return batchResult.getFailedCount() != null ? batchResult.getFailedCount() : 0;
        }
        return 0;
    }
    
    /**
     * 获取流控用户数
     */
    public int getFlowControlledCount() {
        if (userResult != null) {
            return userResult.isFlowControlled() ? 1 : 0;
        }
        if (userResults != null) {
            return (int) userResults.stream().filter(TouchUserResult::isFlowControlled).count();
        }
        if (batchResult != null) {
            return batchResult.getFlowControlledCount() != null ? batchResult.getFlowControlledCount() : 0;
        }
        return 0;
    }
    
    /**
     * 获取总处理用户数
     */
    public int getTotalCount() {
        if (userResult != null) {
            return 1;
        }
        if (userResults != null) {
            return userResults.size();
        }
        if (batchResult != null) {
            return batchResult.getTotalCount() != null ? batchResult.getTotalCount() : 0;
        }
        return 0;
    }
    
    /**
     * 设置统计信息
     */
    public void setStatistics(Long startTime, Long endTime) {
        TouchStatistics stats = new TouchStatistics();
        stats.setStartTime(startTime);
        stats.setEndTime(endTime);
        stats.setDuration(endTime - startTime);
        stats.setTotalUsers(getTotalCount());
        stats.setProcessedUsers(getTotalCount());
        
        // 设置状态统计
        java.util.Map<TouchStatus, Integer> statusCount = new java.util.HashMap<>();
        statusCount.put(TouchStatus.SUCCESS, getSuccessCount());
        statusCount.put(TouchStatus.FAILED, getFailedCount());
        statusCount.put(TouchStatus.FLOW_CONTROLLED, getFlowControlledCount());
        stats.setStatusCount(statusCount);
        
        this.statistics = stats;
    }
}
