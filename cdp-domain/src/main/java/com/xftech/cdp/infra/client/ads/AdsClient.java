package com.xftech.cdp.infra.client.ads;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.xftech.cdp.infra.client.ads.config.AdsConfig;
import com.xftech.cdp.infra.client.ads.model.req.*;
import com.xftech.cdp.infra.client.ads.model.req.label.AdsLabelReq;
import com.xftech.cdp.infra.client.ads.model.req.label.BaseLabelRequest;
import com.xftech.cdp.infra.client.ads.model.resp.AdsLabelResp;
import com.xftech.cdp.infra.client.ads.model.resp.BaseAdsResponse;
import com.xftech.cdp.infra.client.ads.model.resp.UserDispatchResp;
import com.xftech.cdp.infra.client.ads.model.resp.UserIndexResp;
import com.xftech.cdp.infra.client.ads.model.resp.*;
import com.xftech.cdp.infra.client.ads.model.resp.DTRiskUserListResp;
import com.xftech.cdp.infra.client.ads.util.Signer;
import com.xftech.cdp.infra.exception.BizException;
import com.xftech.cdp.infra.exception.InfraException;
import com.xftech.cdp.infra.utils.HttpClientUtil;
import com.xftech.cdp.infra.utils.HttpCustomUtil;
import org.apache.poi.ss.formula.functions.T;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.net.URISyntaxException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @<NAME_EMAIL>
 * @date 2023/5/4 11:26
 */
@Component
public class AdsClient {
    private static final Logger LOGGER = LoggerFactory.getLogger(AdsClient.class);

    @Autowired
    private Signer adsSigner;
    @Autowired
    private AdsConfig adsConfig;
    @Autowired
    private HttpClientUtil httpClientUtil;
    @Autowired
    private HttpCustomUtil httpCustomUtil;

    public CrowdBaseResp<List<CrowdPushResp>> pushCrowd(AdsCrowdReq req) {
        String resp = requestCrowd(req, adsConfig.getRunCrowdJob());
        return JSON.parseObject(resp, new TypeReference<CrowdBaseResp<List<CrowdPushResp>>>() {
        });
    }

    public CrowdBaseResp<List<CrowdTotalResp>> getCrowdTotal(AdsCrowdTotalReq req) {
        String resp = requestCrowd(req, adsConfig.getGetCrowdTotal());
        return JSON.parseObject(resp, new TypeReference<CrowdBaseResp<List<CrowdTotalResp>>>() {
        });
    }

    public BaseAdsResponse<List<UserIndexResp>> queryUserDispatchIndex(BaseAdsRequest<?> request) {
        String resp = request(request, getUserDispatchIndexUri(request));
        return JSON.parseObject(resp, new TypeReference<BaseAdsResponse<List<UserIndexResp>>>() {
        });
    }

    public BaseAdsResponse<List<UserDispatchResp>> queryUserDispatchCnt(BaseAdsRequest<?> request) {
        String resp = requestOffline(this.getDispatchReq(request), adsConfig.getBatchOfflineConfig());
        return JSON.parseObject(resp, new TypeReference<BaseAdsResponse<List<UserDispatchResp>>>() {
        });
    }

    /**
     * 获取模型分
     * @link <a href="https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001016260">...</a>
     */
    public BaseAdsResponse<DTRiskUserListResp> dtRiskUserList(BaseAdsRequest<?> request) {
        String resp = request(request, adsConfig.getGetModelParam());
        return JSON.parseObject(resp, new TypeReference<BaseAdsResponse<DTRiskUserListResp>>(){});
    }

    /**
     * 获取手机号和身份证号解密数据
     * @link <a href="https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001016260">...</a>
     */
    public BaseAdsResponse<ArrayList<AesDecodeResp>> aesDecode(BaseAdsRequest<?> request) {
        String resp = request(request, adsConfig.getAesDecode());
        return JSON.parseObject(resp, new TypeReference<BaseAdsResponse<ArrayList<AesDecodeResp>>>(){});
    }

    private String getUserDispatchIndexUri(BaseAdsRequest<?> request) {
        return request.getArgs() instanceof ChannelIndexReq ? adsConfig.getQueryMarketChannelRoute() : adsConfig.getQueryMarketStrategyRoute();
    }

    private BaseOfflineRequest<?> getDispatchReq(BaseAdsRequest<?> request) {
        AdsOfflineReq adsOfflineReq = new AdsOfflineReq();
        if (request.getArgs() instanceof ChannelIndexReq) {
            adsOfflineReq.setLabel("ads_user_dispatch_channel_info_di");
            ChannelIndexReq channelIndexReq = (ChannelIndexReq) request.getArgs();
            Map<String, Object> param = JSONObject.parseObject(JSONObject.toJSONString(channelIndexReq), Map.class);
            adsOfflineReq.setParams(param);
        }
        if (request.getArgs() instanceof StrategyIndexReq) {
            adsOfflineReq.setLabel("ads_user_dispatch_strategy_info_di");
            StrategyIndexReq channelIndexReq = (StrategyIndexReq) request.getArgs();
            Map<String, Object> param = JSONObject.parseObject(JSONObject.toJSONString(channelIndexReq), Map.class);
            adsOfflineReq.setParams(param);
        }
        List<AdsOfflineReq> adsOfflineReqList = new ArrayList<>();
        adsOfflineReqList.add(adsOfflineReq);
        return new BaseOfflineRequest<>(adsOfflineReqList);
    }

    private <T extends BaseAdsRequest<?>> String request(T requester, String uri) {
        String url = adsConfig.getHost() + "/" + uri;
        try {
            this.setSignParam(requester, createUri(url).getPath());
            long startTime = Instant.now().toEpochMilli();
            String result = httpClientUtil.postJson(url, requester);
            LOGGER.info("ads send, url:{}, requester:{}, response:{}, time:{}ms", url, JSON.toJSONString(requester, SerializerFeature.SortField),
                    result, Instant.now().toEpochMilli() - startTime);
            return result;
        } catch (InfraException e) {
            LOGGER.error("ads send error, url:{}, params:{}", url, JSON.toJSONString(requester, SerializerFeature.SortField), e);
            throw e;
        }
    }

    private <T> String requestCrowd(T requester, String uri) {
        String url = adsConfig.getCrowdHost() + "/" + uri;
        try {
            long startTime = Instant.now().toEpochMilli();
            String result = httpClientUtil.postJson(url, requester);
            LOGGER.info("requestCrowd send, url:{} requester:{}, response:{}, time:{}ms", url, JSON.toJSONString(requester),
                    result, Instant.now().toEpochMilli() - startTime);
            return result;
        } catch (InfraException e) {
            LOGGER.error("requestCrowd send error, url:{}, params:{}", url, JSON.toJSONString(requester, SerializerFeature.SortField), e);
            throw e;
        }
    }

    private <T extends BaseAdsRequest<?>> void setSignParam(T requester, String uri) {
        requester.setApiName(uri);
        requester.setUa(adsSigner.getUa());
        requester.setKey(adsSigner.getKey());
        requester.setSign(adsSigner.sign(uri));
        requester.setRequestId(LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMMddHHmmssSSS"));
    }

    private <T extends BaseOfflineRequest<?>> String requestOffline(T requester, String uri) {
        String url = adsConfig.getHost() + "/" + uri;
        try {
            this.setOfflineSignParam(requester, createUri(url).getPath());
            long startTime = Instant.now().toEpochMilli();
            String result = httpClientUtil.postJson(url, requester);
            LOGGER.info("ads requestOffline, request:{}, response:{}, time:{}ms",JSON.toJSONString(requester, SerializerFeature.SortField),
                    result, Instant.now().toEpochMilli() - startTime);
            return result;
        } catch (InfraException e) {
            LOGGER.error("ads requestOffline error, url:{}, params:{}", url, JSON.toJSONString(requester, SerializerFeature.SortField), e);
            throw e;
        }
    }

    private <T extends BaseLabelRequest<?>> String requestLabel(T requester, String uri) {
        String url = adsConfig.getHost() + "/" + uri;
        try {
            this.setLabelSignParam(requester, createUri(url).getPath());
            long startTime = Instant.now().toEpochMilli();
            String result = httpClientUtil.postJson(url, requester);
            LOGGER.info("ads requestLabel send, request:{}, response:{}, time:{}ms",JSON.toJSONString(requester, SerializerFeature.SortField),
                    result, Instant.now().toEpochMilli() - startTime);
            return result;
        } catch (BizException e) {
            LOGGER.error("ads requestLabel send error, url:{}, params:{}", url, JSON.toJSONString(requester, SerializerFeature.SortField), e);
            throw e;
        }
    }

    private <T extends BaseLabelRequest<?>> String requestBatchLabel(T requester, String uri) {
        String url = adsConfig.getHost() + "/" + uri;
        try {
            this.setLabelSignParam(requester, createUri(url).getPath());
            long startTime = Instant.now().toEpochMilli();
            String result = httpCustomUtil.postJson(url, requester);
            LOGGER.info("ads requestBatchLabel send,  request:{}, resp:{}, time:{}ms",JSON.toJSONString(requester, SerializerFeature.SortField),
                    result, Instant.now().toEpochMilli() - startTime);
            return result;
        } catch (InfraException e) {
            LOGGER.error("ads requestBatchLabel send error, url:{}, params:{}", url, JSON.toJSONString(requester, SerializerFeature.SortField), e);
            throw e;
        }
    }

    private <T extends BaseLabelRequest<?>> void setLabelSignParam(T requester, String uri) {
        requester.setApiName(uri);
        requester.setUa(adsSigner.getUa());
        requester.setRequestId(LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMMddHHmmssSSS"));
    }

    private <T extends BaseOfflineRequest<?>> void setOfflineSignParam(T requester, String uri) {
        requester.setApiName(uri);
        requester.setUa(adsSigner.getUa());
        requester.setRequestId(LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMMddHHmmssSSS"));
    }

    private static URI createUri(String str) {
        try {
            return new URI(str);
        } catch (URISyntaxException x) {
            throw new IllegalArgumentException(x.getMessage(), x);
        }
    }
}
