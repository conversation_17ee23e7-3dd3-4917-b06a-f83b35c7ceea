package com.xftech.cdp.infra.client;

import java.util.Objects;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class BaseResponse<T> {

    /**
     * 1-成功
     * -1-失败
     * 500001-活动过期
     * 500002-库存不足
     */
    @JsonProperty("status")
    @JSONField(name = "status")
    private Integer status;

    @JsonProperty("message")
    @JSONField(name = "message")
    private String message;

    @JsonProperty("response")
    @JSONField(name = "response")
    private T response;

    @JsonProperty("time")
    @JSONField(name = "time")
    private Long time;

    public boolean isSuccess() {
        return Objects.equals(status, 1);
    }

}
