/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.stat.service.impl;

import com.xftech.base.database.Page;
import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.req.flow.strategy.FlowMonitorReq;
import com.xftech.cdp.api.dto.resp.flow.strategy.FlowMonitorResp;
import com.xftech.cdp.domain.stat.entity.StatStrategyFlowDataEntity;
import com.xftech.cdp.domain.stat.repository.StatStrategyFlowDataRepository;
import com.xftech.cdp.domain.strategy.model.enums.StrategyFlowStatusEnum;
import com.xftech.cdp.domain.strategy.repository.StrategyFlowBatchRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyFlowNodeRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyFlowRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyRepository;
import com.xftech.cdp.domain.strategy.service.flow.DispatchCrowdService;
import com.xftech.cdp.domain.strategy.vo.FlowReportViewVo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyFlowBatchDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyFlowDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyFlowNodeDo;
import com.xftech.cdp.infra.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $ StatStrategyFlowDataServiceImpl, v 0.1 2024/3/28 11:09 lingang.han Exp $
 */

@Service
@Slf4j
public class StatStrategyFlowDataServiceImpl {

    @Autowired
    private StrategyFlowRepository strategyFlowRepository;
    @Autowired
    private StrategyFlowNodeRepository strategyFlowNodeRepository;
    @Autowired
    private StrategyFlowBatchRepository strategyFlowBatchRepository;
    @Autowired
    private DispatchCrowdService dispatchCrowdService;
    @Autowired
    private StatStrategyFlowDataRepository statStrategyFlowDataRepository;
    @Autowired
    private StrategyRepository strategyRepository;

    public void statStrategyGroupDataProcess() {
        List<FlowReportViewVo> flowReportViewVoList = buildReportView();
        for (FlowReportViewVo flowReportViewVo : flowReportViewVoList) {
            try {
                Integer dispatchNum = dispatchCrowdService.selectDispatchCount(flowReportViewVo.getStrategyId(), flowReportViewVo.getBatchNo(), flowReportViewVo.getDateValue());
                if (dispatchNum != 0) {
                    StatStrategyFlowDataEntity statStrategyFlowDataEntity = new StatStrategyFlowDataEntity();
                    statStrategyFlowDataEntity.setFlowNo(flowReportViewVo.getFlowNo());
                    statStrategyFlowDataEntity.setBatchNo(flowReportViewVo.getBatchNo());
                    statStrategyFlowDataEntity.setStrategyId(flowReportViewVo.getStrategyId());
                    statStrategyFlowDataEntity.setStrategyName(StringUtils.isBlank(flowReportViewVo.getStrategyName()) ? "" : flowReportViewVo.getStrategyName());
                    statStrategyFlowDataEntity.setDispatchNum(dispatchNum);
                    statStrategyFlowDataEntity.setLevelNum(flowReportViewVo.getLevelNum());
                    statStrategyFlowDataEntity.setDateValue(flowReportViewVo.getDateValue());
                    if (statStrategyFlowDataRepository.exitsRecord(statStrategyFlowDataEntity)) {
                        statStrategyFlowDataRepository.updateByRecord(statStrategyFlowDataEntity);
                    } else {
                        statStrategyFlowDataRepository.insert(statStrategyFlowDataEntity);
                    }
                }
            } catch (Exception e) {
                log.error("flow data save fail", e);
            }


        }
    }

    //查询维度
    private List<FlowReportViewVo> buildReportView() {
        List<FlowReportViewVo> flowReportViewVoList = new ArrayList<>();

        int dateValue = DateUtil.dayOfInt(new Date());
        List<StrategyFlowDo> strategyFlowDoList = strategyFlowRepository.selectList(Collections.singletonList(StrategyFlowStatusEnum.EXECUTING.getCode()));
        for (StrategyFlowDo strategyFlowDo : strategyFlowDoList) {
            String flowNo = strategyFlowDo.getFlowNo();
            try {
                List<StrategyFlowNodeDo> strategyFlowNodeList = strategyFlowNodeRepository.select(flowNo);
                List<StrategyFlowBatchDo> strategyFlowBatchDoList = strategyFlowBatchRepository.selectByFlowNo(flowNo);
                //遍历画布周期
                for (StrategyFlowBatchDo flowBatchDo : strategyFlowBatchDoList) {
                    String batchNo = flowBatchDo.getBatchNo();
                    //遍历画布节点
                    for (StrategyFlowNodeDo strategyFlowNodeDo : strategyFlowNodeList) {
                        FlowReportViewVo flowReportViewVo = new FlowReportViewVo();
                        flowReportViewVo.setStrategyId(strategyFlowNodeDo.getStrategyId());
                        flowReportViewVo.setFlowNo(flowNo);
                        flowReportViewVo.setBatchNo(batchNo);
                        flowReportViewVo.setLevelNum(strategyFlowNodeDo.getLevelNum());
                        flowReportViewVo.setDateValue(dateValue);
                        flowReportViewVoList.add(flowReportViewVo);
                    }
                }
            } catch (Exception e) {
                log.error("flowNo:{} report fail", flowNo, e);
            }
        }

        List<Long> strategyId = flowReportViewVoList.stream().map(FlowReportViewVo::getStrategyId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(strategyId)) {
            return flowReportViewVoList;
        }
        List<StrategyDo> strategyList = strategyRepository.getByIds(strategyId);
        Map<Long, String> strategyIdToName = strategyList.stream().collect(Collectors.toMap(StrategyDo::getId, StrategyDo::getName, (value1, value2) -> {
            return value2;
        }));
        flowReportViewVoList.forEach(item -> item.setStrategyName(strategyIdToName.get(item.getStrategyId())));

        return flowReportViewVoList;
    }

    public PageResultResponse<FlowMonitorResp> monitor(FlowMonitorReq request) {

        Page<StatStrategyFlowDataEntity> strategyFlowDataPage = statStrategyFlowDataRepository.selectPage(request.getFlowNo(), request.getBeginNum(), request.getSize());
        List<StatStrategyFlowDataEntity> record = strategyFlowDataPage.getList();
        List<FlowMonitorResp> flowMonitorRespList = record.stream().map(item -> transformResp(item, null)).collect(Collectors.toList());
        for (FlowMonitorResp flowMonitorResp : flowMonitorRespList) {
            String batchNo = flowMonitorResp.getBatchNo();
            List<StatStrategyFlowDataEntity> strategyFlowDataList = statStrategyFlowDataRepository.selectByBatchNo(batchNo);
            List<StatStrategyFlowDataEntity> subStrategyFlowData = strategyFlowDataList.stream().filter(item -> !Objects.equals(item.getStrategyId(), flowMonitorResp.getStrategyId())).collect(Collectors.toList());
            List<FlowMonitorResp> subFlowMonitorRespList = subStrategyFlowData.stream().map(item -> transformResp(item, flowMonitorResp.getDispatchNum())).sorted(Comparator.comparingInt(FlowMonitorResp::getLevelNum)).collect(Collectors.toList());
            flowMonitorResp.setSubFlowMonitor(subFlowMonitorRespList);
        }

        return new PageResultResponse<>(flowMonitorRespList, strategyFlowDataPage.getBeginNum(), strategyFlowDataPage.getFetchNum(), strategyFlowDataPage.getTotalNum());
    }

    public FlowMonitorResp transformResp(StatStrategyFlowDataEntity statStrategyFlowData, Integer rootDispatch) {
        FlowMonitorResp flowMonitorResp = new FlowMonitorResp();
        flowMonitorResp.setStrategyId(statStrategyFlowData.getStrategyId());
        flowMonitorResp.setStrategyName(statStrategyFlowData.getStrategyName());
        flowMonitorResp.setDateValue(statStrategyFlowData.getDateValue());
        flowMonitorResp.setDispatchNum(statStrategyFlowData.getDispatchNum());
        flowMonitorResp.setLevelNum(statStrategyFlowData.getLevelNum());
        flowMonitorResp.setBatchNo(statStrategyFlowData.getBatchNo());
        if (rootDispatch != null) {
            flowMonitorResp.setRate(new BigDecimal(statStrategyFlowData.getDispatchNum()).multiply(new BigDecimal(100)).divide(new BigDecimal(rootDispatch), 2, RoundingMode.HALF_UP) + "%");
        }
        return flowMonitorResp;
    }
}