<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="adminAuditLogMapper">
  <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.strategy.po.AdminAuditLogDo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="op_type" jdbcType="VARCHAR" property="opType" />
    <result column="op_name" jdbcType="VARCHAR" property="opName" />
    <result column="op_mobile" jdbcType="VARCHAR" property="opMobile" />
    <result column="d_flag" jdbcType="TINYINT" property="dFlag" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.xftech.cdp.infra.repository.cdp.strategy.po.AdminAuditLogDo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="op_content" jdbcType="LONGVARCHAR" property="opContent" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, op_type, op_name, op_mobile, d_flag, created_time, updated_time
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    op_content
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from admin_audit_log
    where id = #{id,jdbcType=BIGINT}
  </select>

  <insert id="insert" parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.AdminAuditLogDo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into admin_audit_log (id, op_type, op_name, 
      op_mobile, d_flag, created_time, 
      updated_time, op_content)
    values (#{id,jdbcType=BIGINT}, #{opType,jdbcType=VARCHAR}, #{opName,jdbcType=VARCHAR}, 
      #{opMobile,jdbcType=VARCHAR}, #{dFlag,jdbcType=TINYINT}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{updatedTime,jdbcType=TIMESTAMP}, #{opContent,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" keyProperty="id" useGeneratedKeys="true" parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.AdminAuditLogDo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into admin_audit_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="opType != null">
        op_type,
      </if>
      <if test="opName != null">
        op_name,
      </if>
      <if test="opMobile != null">
        op_mobile,
      </if>
      <if test="dFlag != null">
        d_flag,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="updatedTime != null">
        updated_time,
      </if>
      <if test="opContent != null">
        op_content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="opType != null">
        #{opType,jdbcType=VARCHAR},
      </if>
      <if test="opName != null">
        #{opName,jdbcType=VARCHAR},
      </if>
      <if test="opMobile != null">
        #{opMobile,jdbcType=VARCHAR},
      </if>
      <if test="dFlag != null">
        #{dFlag,jdbcType=TINYINT},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="opContent != null">
        #{opContent,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.AdminAuditLogDo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update admin_audit_log
    <set>
      <if test="opType != null">
        op_type = #{opType,jdbcType=VARCHAR},
      </if>
      <if test="opName != null">
        op_name = #{opName,jdbcType=VARCHAR},
      </if>
      <if test="opMobile != null">
        op_mobile = #{opMobile,jdbcType=VARCHAR},
      </if>
      <if test="dFlag != null">
        d_flag = #{dFlag,jdbcType=TINYINT},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="opContent != null">
        op_content = #{opContent,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.AdminAuditLogDo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update admin_audit_log
    set op_type = #{opType,jdbcType=VARCHAR},
      op_name = #{opName,jdbcType=VARCHAR},
      op_mobile = #{opMobile,jdbcType=VARCHAR},
      d_flag = #{dFlag,jdbcType=TINYINT},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      op_content = #{opContent,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.AdminAuditLogDo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update admin_audit_log
    set op_type = #{opType,jdbcType=VARCHAR},
      op_name = #{opName,jdbcType=VARCHAR},
      op_mobile = #{opMobile,jdbcType=VARCHAR},
      d_flag = #{dFlag,jdbcType=TINYINT},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      updated_time = #{updatedTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>