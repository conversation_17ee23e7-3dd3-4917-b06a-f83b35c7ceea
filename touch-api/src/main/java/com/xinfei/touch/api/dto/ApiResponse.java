package com.xinfei.touch.api.dto;

import lombok.Data;

/**
 * API统一响应格式
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class ApiResponse<T> {
    
    /**
     * 响应码
     */
    private String code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 响应时间戳
     */
    private Long timestamp;
    
    public ApiResponse() {
        this.timestamp = System.currentTimeMillis();
    }
    
    public ApiResponse(String code, String message, T data) {
        this();
        this.code = code;
        this.message = message;
        this.data = data;
    }
    
    /**
     * 成功响应
     */
    public static <T> ApiResponse<T> success() {
        return new ApiResponse<>("SUCCESS", "操作成功", null);
    }
    
    /**
     * 成功响应（带数据）
     */
    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>("SUCCESS", "操作成功", data);
    }
    
    /**
     * 成功响应（自定义消息）
     */
    public static <T> ApiResponse<T> success(String message, T data) {
        return new ApiResponse<>("SUCCESS", message, data);
    }
    
    /**
     * 失败响应
     */
    public static <T> ApiResponse<T> error(String code, String message) {
        return new ApiResponse<>(code, message, null);
    }
    
    /**
     * 失败响应（带数据）
     */
    public static <T> ApiResponse<T> error(String code, String message, T data) {
        return new ApiResponse<>(code, message, data);
    }
    
    /**
     * 系统错误响应
     */
    public static <T> ApiResponse<T> systemError() {
        return new ApiResponse<>("SYSTEM_ERROR", "系统错误", null);
    }
    
    /**
     * 参数错误响应
     */
    public static <T> ApiResponse<T> paramError(String message) {
        return new ApiResponse<>("PARAM_ERROR", message, null);
    }
    
    /**
     * 是否成功
     */
    public boolean isSuccess() {
        return "SUCCESS".equals(this.code);
    }
}
