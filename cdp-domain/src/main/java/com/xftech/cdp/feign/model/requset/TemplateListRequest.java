package com.xftech.cdp.feign.model.requset;

import com.xftech.cdp.api.dto.req.PushTemplateReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * push模板列表查询请求参数
 *
 * <AUTHOR>
 * @version $ TemplateListRequest, v 0.1 2024/1/16 17:49 qu.lu Exp $
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TemplateListRequest {
    /** 模板id */
    private String templateId;
    /** 标题 */
    private String title;
    /** 模板类型 */
    private String tplType;
    /** 模板业务类型 */
    private String bizType;
    /** 创建部门 */
    private String createdDept;
    /** 页数，默认值1 */
    private Integer pageNum;
    /** 每页条目，默认值10 */
    private Integer pageSize;

    public TemplateListRequest(PushTemplateReq req){
        this.templateId = req.getTemplateId();
        this.title = req.getTitle();
        this.tplType = req.getTplType();
        this.bizType = req.getBizType();
        this.createdDept = req.getCreatedDept();
        this.pageNum = req.getCurrent();
        this.pageSize = req.getSize();
    }
}
