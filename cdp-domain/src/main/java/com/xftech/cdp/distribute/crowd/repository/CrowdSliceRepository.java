/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2025. All Rights Reserved.
 */

package com.xftech.cdp.distribute.crowd.repository;

import java.util.List;
import java.util.Map;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.distribute.crowd.repository.model.CrowdSliceDo;

import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;


@Component
public class CrowdSliceRepository {

    public CrowdSliceDo selectByPrimaryKey(Long id) {
        return DBUtil.selectOne("crowdSliceMapper.selectByPrimaryKey", id);
    }

    public void deleteByPrimaryKey(Long id) {
        DBUtil.delete("crowdSliceMapper.deleteByPrimaryKey", id);
    }

    public int insert(CrowdSliceDo sliceDo) {
        return DBUtil.insert("crowdSliceMapper.insert", sliceDo);
    }

    public int insertSelective(CrowdSliceDo sliceDo) {
        return DBUtil.insert("crowdSliceMapper.insertSelective", sliceDo);
    }

    public void insertBatch(List<CrowdSliceDo> crowdSliceDoList) {
        DBUtil.insertBatch("crowdSliceMapper.insertSelective", crowdSliceDoList);
    }

    public int updateByPrimaryKeySelective(CrowdSliceDo sliceDo) {
        return DBUtil.update("crowdSliceMapper.updateByPrimaryKeySelective", sliceDo);
    }

    public int updateByPrimaryKey(CrowdSliceDo sliceDo) {
        if (sliceDo == null || sliceDo.getId() == null) {
            throw new IllegalArgumentException("id cannot be null");
        }
        return DBUtil.update("crowdSliceMapper.updateByPrimaryKey", sliceDo);
    }

    public List<CrowdSliceDo> selectByCrowdVersion(Long crowdId, Long crowdVersion) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("crowdId", crowdId);
        params.put("crowdVersion", crowdVersion);
        return DBUtil.selectList("crowdSliceMapper.selectByCrowdVersion", params);
    }

    public CrowdSliceDo existTodaySliceCrowdVersion(Long crowdId, Long crowdVersion) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("crowdId", crowdId);
        params.put("crowdVersion", crowdVersion);
        return DBUtil.selectOne("crowdSliceMapper.existTodaySliceCrowdVersion", params);
    }

}
