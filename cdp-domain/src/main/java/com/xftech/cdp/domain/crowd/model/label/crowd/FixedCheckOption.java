package com.xftech.cdp.domain.crowd.model.label.crowd;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xftech.cdp.domain.crowd.exception.CrowdException;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/2/15
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FixedCheckOption extends CrowdLabelOption {
    private List<String> items;

    /**
     * column in (a,b,c)
     */
    @Override
    public StringBuilder condition(String column, String configOptionReflect) {
        if (StringUtils.isBlank(configOptionReflect)) {
            StringBuilder sql = super.condition(column, configOptionReflect);
            return sql.append(" in (").append(items.stream().map(a -> "'" + a + "'").collect(Collectors.joining(","))).append(") ");
        }
        if (configOptionReflect.contains("array_contains_all")) {
           // return new StringBuilder(configOptionReflect.replace("${list}", "[" + items.stream().map(a -> "\"\\\"" + a + "\\\"\"").collect(Collectors.joining(",")) + "]"));
              return getArrayContain(column);
        } else {
            JSONObject keyValue = JSON.parseObject(configOptionReflect);
            if (items.size() == 1) {
                StringBuilder sql = new StringBuilder();
                if ("其他".equals(items.get(0))) {
                    return sql.append(column).append(" not in (").append(keyValue.getString("其他")).append(")");
                }
                if ("NULL".equals(keyValue.getString(items.get(0)))) {
                    return sql.append(column).append(" is NULL");
                }
                return sql.append(column).append(" in (").append(items.stream().map(
                        a -> keyValue.getString(a) == null ? ("'" + a + "'") : Arrays.stream(keyValue.getString(a).split(";")).map(
                                b -> "'" + b + "'").collect(Collectors.joining(",")))
                        .collect(Collectors.joining(","))).append(") ");
            }
            StringBuilder sql = super.condition(column, configOptionReflect);
            sql.append(" in (").append(items.stream().filter(item -> !"其他".equals(item) && !"NULL".equals(keyValue.getString(item))).map(
                    a -> keyValue.getString(a) == null ? ("'" + a + "'") : Arrays.stream(keyValue.getString(a).split(";")).map(
                            b -> "'" + b + "'").collect(Collectors.joining(",")))
                    .collect(Collectors.joining(","))).append(") ");
            StringBuilder newSql = new StringBuilder();
            items.forEach(item -> {
                if ("其他".equals(item)) {
                    sql.append(" or (").append(column).append(" not in (").append(keyValue.getString("其他")).append("))");
                }
                if ("NULL".equals(keyValue.getString(item))) {
                    sql.append(" or (").append(column).append(" is NULL").append(")");
                }
            });
            if (StringUtils.isNotBlank(newSql.toString())) {
                newSql.append("(").append(sql).append(")");
                return newSql;
            }
            return sql;
        }

    }

    private StringBuilder getArrayContain(String column) {
        return new StringBuilder(items.stream()
                .map(item -> "ARRAY_CONTAINS(" + column + ", '\"" + item + "\"') = true")
                .collect(Collectors.joining(" OR ")));
    }

    @Override
    public void verify() {
        if (CollectionUtils.isEmpty(items)) {
            throw new CrowdException("多选项数据错误！");
        }
    }

}
