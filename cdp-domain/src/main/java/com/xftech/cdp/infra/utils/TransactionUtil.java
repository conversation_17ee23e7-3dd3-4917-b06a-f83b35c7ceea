package com.xftech.cdp.infra.utils;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.infra.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.TransactionStatus;

import java.util.Optional;
import java.util.function.Consumer;

/**
 * 编程式事务工具类
 *
 * @<NAME_EMAIL>
 * @date 2023/3/2 17:01
 */
@Slf4j
public class TransactionUtil {

    private TransactionUtil() {
        throw new BizException("No com.xftech.cdp.infra.utils.TransactionUtil instances for you!");
    }

    /**
     * 编程式事务
     *
     * @param tryLogic 主要业务逻辑
     * @return boolean true-事务处理成功 false-事务处理失败
     */
    public static boolean transactional(Runnable tryLogic) {
        return transactional(tryLogic, null);
    }

    /**
     * 编程式事务
     *
     * @param tryLogic   主要业务逻辑
     * @param catchLogic 异常处理逻辑
     * @return boolean true-事务处理成功 false-事务处理失败
     */
    public static boolean transactional(Runnable tryLogic, Consumer<Exception> catchLogic) {
        return transactional(tryLogic, catchLogic, null);
    }

    /**
     * 编程式事务
     *
     * @param tryLogic     主要业务逻辑
     * @param catchLogic   异常处理逻辑
     * @param finallyLogic 必须执行的逻辑
     * @return boolean true-事务处理成功 false-事务处理失败
     */
    public static boolean transactional(Runnable tryLogic, Consumer<Exception> catchLogic, Runnable finallyLogic) {
        TransactionStatus transactionStatus = DBUtil.beginTx(true);
        try {

            tryLogic.run();

            DBUtil.commit(transactionStatus);
            return true;
        } catch (Exception e) {
            log.warn("事务异常回滚", e);
            DBUtil.rollback(transactionStatus);
            Optional.ofNullable(catchLogic).ifPresent(consumer -> consumer.accept(e));
            return false;
        } finally {
            Optional.ofNullable(finallyLogic).ifPresent(Runnable::run);
        }
    }
}
