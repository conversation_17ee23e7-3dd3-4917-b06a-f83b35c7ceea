package com.xftech.cdp.infra.pulsar.service.impl;

import com.xftech.cdp.domain.strategy.service.dispatch.event.StrategyEventDispatchService;
import com.xftech.cdp.infra.pulsar.service.CdpPulsarService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @<NAME_EMAIL>
 * @date 2023-08-18 15:19
 */
@Slf4j
@Service("riskSingleAdjCarService")
public class RiskSingleAdjCarServiceImpl implements CdpPulsarService {

    @Autowired
    private StrategyEventDispatchService strategyEventDispatchService;

    @Override
    public void consumer(String messageId, String eventMessage) {
        toBizEventMessageVO(eventMessage).ifPresent(eventMsg -> {
            log.info("RiskSingleAdjCar eventMessage toBizEventMessageVO={}", eventMsg);
            strategyEventDispatchService.prescreen(messageId, eventMsg);
        });
    }
}
