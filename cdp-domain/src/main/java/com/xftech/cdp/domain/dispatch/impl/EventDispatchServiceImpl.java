package com.xftech.cdp.domain.dispatch.impl;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xftech.cdp.domain.dispatch.EventDispatchService;
import com.xftech.cdp.domain.dispatch.dto.DispatchDto;
import com.xftech.cdp.domain.param.service.TemplateParamService;
import com.xftech.cdp.domain.strategy.model.dto.AiProntoChannelDto;
import com.xftech.cdp.domain.strategy.model.dto.DelayedPopupChannelDto;
import com.xftech.cdp.domain.strategy.model.enums.BalanceInvokeStatusEnum;
import com.xftech.cdp.domain.strategy.repository.UserBalanceIncreaseRecordRepository;
import com.xftech.cdp.domain.strategy.service.TelePushService;
import com.xftech.cdp.domain.strategy.service.impl.GoodsServiceImpl;
import com.xftech.cdp.feign.model.GoodsDetail;
import com.xftech.cdp.feign.model.PushUserData;
import com.xftech.cdp.feign.model.SendPushInfo;
import com.xftech.cdp.feign.model.requset.PushBaseRequest;
import com.xftech.cdp.feign.model.requset.SendPushRequest;
import com.xftech.cdp.feign.model.response.PushResponse;
import com.xftech.cdp.infra.client.ai.AiClient;
import com.xftech.cdp.infra.client.appcore.UserCoreClient;
import com.xftech.cdp.infra.client.appcore.request.BalanceOptReq;
import com.xftech.cdp.infra.client.appcore.response.BalanceOptResp;
import com.xftech.cdp.infra.client.coupon.CouponClient;
import com.xftech.cdp.infra.client.coupon.model.req.BaseCouponRequester;
import com.xftech.cdp.infra.client.coupon.model.req.CouponSendBatchReq;
import com.xftech.cdp.infra.client.coupon.model.resp.BaseCouponResponse;
import com.xftech.cdp.infra.client.increaseamt.IncreaseAmtClient;
import com.xftech.cdp.infra.client.increaseamt.IncreaseAmtUtils;
import com.xftech.cdp.infra.client.increaseamt.model.dto.UserInfoDto;
import com.xftech.cdp.infra.client.increaseamt.model.req.Request;
import com.xftech.cdp.infra.client.increaseamt.model.req.RequestHeader;
import com.xftech.cdp.infra.client.increaseamt.model.resp.Resp;
import com.xftech.cdp.infra.client.push.PushClient;
import com.xftech.cdp.infra.client.sms.SmsClient;
import com.xftech.cdp.infra.client.sms.model.SmsSingleSendArgs;
import com.xftech.cdp.infra.client.sms.model.SmsSingleSendRequester;
import com.xftech.cdp.infra.client.sms.model.resp.SmsSingleSendResp;
import com.xftech.cdp.infra.client.telemarketing.TelemarketingClient;
import com.xftech.cdp.infra.client.telemarketing.model.TelePushArgs;
import com.xftech.cdp.infra.client.telemarketing.model.TeleSaveBatchArgs;
import com.xftech.cdp.infra.client.telemarketing.model.TeleSaveBatchRequest;
import com.xftech.cdp.infra.client.telemarketing.model.resp.AiSendResp;
import com.xftech.cdp.infra.client.telemarketing.model.resp.TelePushResp;
import com.xftech.cdp.infra.client.telemarketing.model.resp.TeleSaveBatchResp;
import com.xftech.cdp.infra.client.usercenter.CisService;
import com.xftech.cdp.infra.config.LogUtil;
import com.xftech.cdp.infra.constant.RedisKeyConstants;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventVO;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.marketing.po.UserBalanceIncreaseRecordDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.EventPushBatchDo;
import com.xftech.cdp.infra.rocketmq.dto.AiSendArgs;
import com.xftech.cdp.infra.rocketmq.dto.AiUserData;
import com.xftech.cdp.infra.rocketmq.popup.DelayedPopupMsg;
import com.xftech.cdp.infra.rocketmq.popup.DelayedPopupSendMqProducer;
import com.xftech.cdp.infra.utils.JsonUtil;
import com.xftech.cdp.infra.utils.RedisUtils;
import com.xftech.cdp.infra.utils.SerialNumberUtil;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutableTriple;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;

/**
 * @<NAME_EMAIL>
 * @date 2023/5/23 14:29
 */
@Slf4j
@Service("eventDispatchService")
public class EventDispatchServiceImpl extends AbstractDispatchService implements EventDispatchService {

    @Autowired
    private SmsClient smsClient;
    @Autowired
    private CouponClient couponClient;
    @Autowired
    private TelemarketingClient telemarketingClient;
    @Autowired
    private TemplateParamService templateParamService;
    @Autowired
    private TelePushService telePushService;
    @Autowired
    private PushClient pushClient;
    @Autowired
    private IncreaseAmtClient increaseAmtClient;
    @Autowired
    private AiClient aiClient;
    @Autowired
    private CisService cisService;
    @Autowired
    private GoodsServiceImpl goodsService;
    @Autowired
    private IncreaseAmtUtils increaseAmtUtils;
    @Resource
    private UserCoreClient userCoreClient;
    @Resource
    private UserBalanceIncreaseRecordRepository balanceRepository;
    @Autowired
    private RedisUtils redisUtils;
    @Resource
    private DelayedPopupSendMqProducer delayedPopupSendMqProducer;
    @Resource
    private SerialNumberUtil serialNumberUtil;

    /**
     * 单条短信下发 （带参）
     *
     * @param reach       批次参数
     * @param app         app
     * @param innerApp    innerApp
     * @param crowdDetail 用户信息
     * @return 下发数量
     */
    @Override
    public ImmutableTriple<Integer, EventPushBatchDo, Boolean> sendSmsEvent(DispatchDto reach, String app, String innerApp, CrowdDetailDo crowdDetail, BizEventVO bizEvent, String extInfo) {
        // 判断模板是否存在参数，并获取参数对应的值
        Map<String, Object> tempParam = templateParamService.getSmsTempParam(crowdDetail, reach.getStrategyId(), reach.getStrategyMarketChannelTemplateId(), bizEvent);
        return requestEvent(reach, crowdDetail, this::getMobileList, batchNum -> {
            SmsSingleSendArgs args = new SmsSingleSendArgs();
            args.setMobile(crowdDetail.getMobile());
            args.setTemplateId(reach.getStrategyMarketChannelTemplateId());
            args.setData(CollectionUtils.isEmpty(tempParam) ? null : tempParam);
            args.setApp(app);
            args.setInnerApp(innerApp);
            args.setUserNo(crowdDetail.getUserId());
            JSONObject jsonObject = JSON.parseObject(extInfo);
            if (jsonObject != null && jsonObject.containsKey("signatureKey")) {
                args.setSignatureKey(jsonObject.getString("signatureKey"));
            }
            SmsSingleSendRequester requester = new SmsSingleSendRequester();
            requester.setArgs(args);
            SmsSingleSendResp resp = smsClient.sendSingleSms(requester);
            return Pair.of(resp.isSuccess(), Triple.of(String.valueOf(resp.getStatus()), resp.getMessage(), resp.isSuccess() ? resp.getResponse().getBatchNum() : null));
        });
    }

    private List<String> getMobileList(CrowdDetailDo crowdDetail) {
        return Collections.singletonList(crowdDetail.getMobile());
    }

    /**
     * 调用电销保存接口
     *
     * @param reach       策略执行初始化内容
     * @param app         app
     * @param innerApp    innerApp
     * @param crowdDetail 需要下发的用户ID集合
     * @return ImmutablePair Left-下发成功数 Right-下发批次记录
     */
    @Override
    public ImmutableTriple<Integer, EventPushBatchDo, Boolean> sendTeleEvent(DispatchDto reach, String app, String innerApp, CrowdDetailDo crowdDetail) {
        List<Long> userIdList = getUserIdList(crowdDetail);
        return requestEvent(reach, crowdDetail, this::getUserIdList, batchNum -> {
            TeleSaveBatchRequest requester = new TeleSaveBatchRequest();
            TeleSaveBatchArgs args = new TeleSaveBatchArgs();
            args.setCreditIdArr(userIdList);
            args.setUserType(Integer.valueOf(reach.getStrategyMarketChannelTemplateId()));
            args.setFlowNo(batchNum);
            args.setBatchCount(1);
            args.setCurrentBatch(1);
            requester.setArgs(args);
            TeleSaveBatchResp teleSaveBatchResp = telemarketingClient.saveBatch(requester);
            return Pair.of(teleSaveBatchResp.isSuccess(), Triple.of(teleSaveBatchResp.getCode(), teleSaveBatchResp.getMessage(), batchNum));
        });
    }

    private List<Long> getUserIdList(CrowdDetailDo batch) {
        return Collections.singletonList(batch.getUserId());
    }

    /**
     * 调用优惠券批量下发接口
     *
     * @param reach       策略执行初始化内容
     * @param app         app
     * @param innerApp    innerApp
     * @param crowdDetail 需要下发的用户集合
     * @return ImmutablePair Left-下发成功数 Right-下发批次记录
     */
    @Override
    public ImmutableTriple<Integer, EventPushBatchDo, Boolean> sendCouponEvent(DispatchDto reach, String app, String innerApp, CrowdDetailDo crowdDetail) {
        List<CouponSendBatchReq.User> list = this.getUserList(crowdDetail);
        return requestEvent(reach, crowdDetail, this::getUserList, batchNum -> {
            CouponSendBatchReq req = new CouponSendBatchReq();
            req.setActivityId(Convert.toLong(reach.getStrategyMarketChannelTemplateId()));
            req.setBatchNum(batchNum);
            req.setApp(app);
            req.setInnerApp(innerApp);
            req.setAllBatchCount(list.size());
            req.setUserList(list);
            BaseCouponResponse<Object> response = couponClient.sendBatch(new BaseCouponRequester<>(req));
            return Pair.of(response.isSuccess(), Triple.of(String.valueOf(response.getStatus()), response.getMessage(), batchNum));
        });
    }

    @Override
    public SmsSingleSendResp sendSms(SmsSingleSendRequester request) {
        SmsSingleSendResp resp = null;
        try {
            resp = smsClient.sendSingleSms(request);
            log.info("发送短信, request:{},response:{}", JsonUtil.toJson(request), JsonUtil.toJson(resp));
            boolean ret = (resp != null && resp.isSuccess());
            if (!ret) {
                log.warn("发送短信失败, request:{},response:{}", JsonUtil.toJson(request), JsonUtil.toJson(resp));
            }
        } catch (Exception ex) {
            log.error("发送短信异常, request:{}", JsonUtil.toJson(request), ex);
        }
        return resp;
    }

    @Override
    public TeleSaveBatchResp sendTele(TeleSaveBatchRequest request) {
        TeleSaveBatchResp resp = null;
        try {
            resp = telemarketingClient.saveBatch(request);
            log.info("发送电销, request:{},response:{}", JsonUtil.toJson(request), JsonUtil.toJson(resp));
            boolean ret = (resp != null && resp.isSuccess());
            if (!ret) {
                log.warn("发送电销失败, request:{},response:{}", JsonUtil.toJson(request), JsonUtil.toJson(resp));
            }
            return resp;
        } catch (Exception ex) {
            log.error("发送电销异常, request:{}", JsonUtil.toJson(request), ex);
        }
        return resp;
    }

    @Override
    public TelePushResp sendTeleNew(TelePushArgs request) {
        TelePushResp teleSp = null;
        try {
            teleSp = telemarketingClient.pushTeleData(request);
            log.info("发送新电销, request:{},response:{}", JsonUtil.toJson(request), JsonUtil.toJson(teleSp));
            boolean ret = (teleSp != null && teleSp.isSuccess());
            if (!ret) {
                log.warn("发送新电销失败, request:{},response:{}", JsonUtil.toJson(request), JsonUtil.toJson(teleSp));
            }
        } catch (Exception ex) {
            log.error("发送新电销异常, request:{}", JsonUtil.toJson(request), ex);
        }
        return teleSp;
    }

    @Override
    public boolean increaseBalance(BalanceOptReq req, String orderNo) {
        String key = String.format(RedisKeyConstants.BALANCE_LOCK_KEY, req.getUserNo(), orderNo);
        UserBalanceIncreaseRecordDo.UserBalanceIncreaseRecordDoBuilder builder = UserBalanceIncreaseRecordDo
                .builder()
                .status(BalanceInvokeStatusEnum.SUCCESS.getStatus())
                .appUserId(req.getUserNo())
                .amount(req.getAmount())
                .orderNumber(orderNo);
        try{
            boolean lock = redisUtils.lock(key, 1, 5);
            // 幂等防重
            if (!lock) {
                builder.status(BalanceInvokeStatusEnum.DISCARD.getStatus());
                builder.reason(BalanceInvokeStatusEnum.DISCARD.getDesc());
            }
            // 数额 <= 0 不返现
            else if (BigDecimal.ZERO.compareTo(req.getAmount()) >= 0) {
                builder.status(BalanceInvokeStatusEnum.FILTER.getStatus());
                builder.reason("amount" + BalanceInvokeStatusEnum.FILTER.getDesc());
            }
            // 用户信息为空 不返现
            else if (Objects.isNull(req.getUserNo())) {
                builder.status(BalanceInvokeStatusEnum.FILTER.getStatus());
                builder.reason("app_user_id" + BalanceInvokeStatusEnum.FILTER.getDesc());
            }
            UserBalanceIncreaseRecordDo record = builder.build();
            if (Objects.nonNull(record.getStatus()) && record.getStatus() != 0) {
                balanceRepository.insertSelective(record);
                log.info("increaseBalance1 : {}", record);
                return false;
            }
            // 单据纬度唯一性校验
            List<UserBalanceIncreaseRecordDo> records =
                    balanceRepository.selectByOderAndUserId(UserBalanceIncreaseRecordDo.builder().appUserId(req.getUserNo()).build());
            boolean match = records.stream().anyMatch(e -> Objects.equals(e.getOrderNumber(), orderNo));
            if (match) {
                record.setStatus(BalanceInvokeStatusEnum.FILTER.getStatus());
                record.setReason("order_no" + BalanceInvokeStatusEnum.FILTER.getDesc());
                balanceRepository.insertSelective(record);
                log.info("increaseBalance2 : {}", record);
                return false;
            }
            BalanceOptResp resp = userCoreClient.increaseBalance(req);
            record.setStatus(resp.getStatus());
            balanceRepository.insertSelective(record);
            log.info("increaseBalance3 : {}", record);
            return Objects.equals(resp.getStatus(), BalanceInvokeStatusEnum.SUCCESS.getStatus());
        } catch (Exception e){
            log.error("麻雀余额操作异常",e);
            builder.status(BalanceInvokeStatusEnum.INNER_FAIL.getStatus());
            builder.reason(BalanceInvokeStatusEnum.INNER_FAIL.getDesc());
            balanceRepository.insertSelective(builder.build());
        } finally {
            redisUtils.unlock(key);
        }
        log.info("increaseBalance4");
        return false;
    }

    @Override
    public AiSendResp sendAiPronto(AiSendArgs aiSendArgs) {
        AiSendResp aiSendResp = null;
        try {
            aiSendResp = aiClient.doBatchSendAi(aiSendArgs);
            log.info("发送ai, request:{},response:{}", JsonUtil.toJson(aiSendArgs), JsonUtil.toJson(aiSendResp));
            boolean ret = (aiSendResp != null && aiSendResp.isSuccess());
            if (!ret) {
                log.warn("发送ai失败, request:{},response:{}", JsonUtil.toJson(aiSendArgs), JsonUtil.toJson(aiSendResp));
            }
        } catch (Exception ex) {
            log.error("发送ai异常, request:{}", JsonUtil.toJson(aiSendArgs), ex);
        }
        return aiSendResp;
    }

    @Override
    public BaseCouponResponse<Object> sendCoupon(CouponSendBatchReq request) {
        BaseCouponResponse<Object> resp = null;
        try {
            resp = couponClient.sendBatch(new BaseCouponRequester<>(request));
            log.info("发送优惠券, request:{},response:{}", JsonUtil.toJson(request), JsonUtil.toJson(resp));
            boolean ret = (resp != null && resp.isSuccess());
            if (!ret) {
                log.warn("发送优惠券失败, request:{},response:{}", JsonUtil.toJson(request), JsonUtil.toJson(resp));
            }
            return resp;
        } catch (Exception ex) {
            log.error("发送优惠券异常, request:{}", JsonUtil.toJson(request), ex);
        }
        return resp;
    }

    @Override
    public ImmutableTriple<Integer, EventPushBatchDo, Boolean> sendNewTeleEvent(DispatchDto reach, String app, String innerApp, CrowdDetailDo batch) {
        String tempId = reach.getStrategyMarketChannelTemplateId();
        TelePushArgs policyDetail = telePushService.getTelePushArgs(Integer.valueOf(tempId), Collections.singletonList(batch), reach, app);
        return requestEvent(reach, batch, this::getUserIdList, batchNum -> {
            TelePushArgs requester = TelePushArgs.builder()
                    .batchNumber(batchNum)
                    .traceId(UUID.randomUUID().toString())
                    .policyId(policyDetail.getPolicyId())
                    .policyType(policyDetail.getPolicyType())
                    .strategyType(policyDetail.getStrategyType())
                    .data(policyDetail.getData())
                    .ua("xyf-cdp")
                    .ts(1L)
                    .nameTypeId(policyDetail.getNameTypeId())
                    .cdpStrategyId(policyDetail.getCdpStrategyId())
                    .build();
            TelePushResp teleSp = telemarketingClient.pushTeleData(requester);
            return Pair.of(teleSp.isSuccess(), Triple.of(String.valueOf(teleSp.getStatus()), teleSp.getMessage(), batchNum));
        });
    }

    @Override
    public PushResponse<SendPushInfo> sendPush(PushBaseRequest<SendPushRequest> request) {
        PushResponse<SendPushInfo> resp = null;
        try {
            resp = pushClient.doBatchSendPush(request);
            log.info("发送push, request:{},response:{}", JsonUtil.toJson(request), JsonUtil.toJson(resp));
            boolean ret = (resp != null && resp.isSuccess());
            if (!ret) {
                log.warn("发送push失败, request:{},response:{}", JsonUtil.toJson(request), JsonUtil.toJson(resp));
            }
        } catch (Exception ex) {
            log.error("发送push异常, request:{}", JsonUtil.toJson(request), ex);
        }
        return resp;
    }

    @Override
    public ImmutableTriple<Integer, EventPushBatchDo, Boolean> sendPushEvent(DispatchDto reach, String app, String innerApp, CrowdDetailDo crowdDetail, BizEventVO bizEvent) {
        Map<String, Object> tempParam = templateParamService.getPushTempParam(crowdDetail, reach.getStrategyId(), reach.getStrategyMarketChannelTemplateId(), bizEvent);
        return requestEvent(reach, crowdDetail, this::getUserIdList, batchNum -> {
            PushBaseRequest<SendPushRequest> request = new PushBaseRequest<>();
            SendPushRequest sendPushRequest = new SendPushRequest();
            sendPushRequest.setApp(crowdDetail.getApp());
            sendPushRequest.setInnerApp(crowdDetail.getInnerApp());
            sendPushRequest.setTemplateId(reach.getStrategyMarketChannelTemplateId());
            sendPushRequest.setBatchNum(batchNum);
            PushUserData data = new PushUserData(crowdDetail.getUserId().toString());
            if (!tempParam.isEmpty()) data.setDataMap(tempParam);
            sendPushRequest.setPushDataList(Collections.singletonList(data));
            request.setTimestamp(String.valueOf(Instant.now().getEpochSecond()));
            request.setArgs(sendPushRequest);
            PushResponse<SendPushInfo> resp = pushClient.doBatchSendPush(request);
            return Pair.of(resp.isSuccess(), Triple.of(String.valueOf(resp.getStatus()), resp.getMessage(), resp.isSuccess() ? resp.getResponse().getBatchNum() : null));
        });
    }

    @Override
    public ImmutableTriple<Integer, EventPushBatchDo, Boolean> sendIncreaseAmtEvent(DispatchDto reach, String app, String innerApp, CrowdDetailDo crowdDetail,
                                                                                    Long creditUserId, String deviceId, String ip) {
        List<UserInfoDto> userInfoDtos = cisService.getUserInfoDtos(Arrays.asList(crowdDetail));
        boolean valid = reach.getIncreaseAmtParamDto().isValid();
        if (CollectionUtils.isEmpty(userInfoDtos) || !valid || StringUtils.isAnyBlank(app, innerApp)) {
            reach.setFailReason("用户信息不全");
            LogUtil.logDebug("EventDispatchServiceImpl sendIncreaseAmtEvent userInfoDtos={} valid={} app={} innerApp={}", JSONObject.toJSONString(userInfoDtos), valid, app, innerApp);
            log.info("提额用户信息不全, 无法完成调用, 策略id:{}, 用户id:{}", reach.getStrategyId(), crowdDetail.getUserId());
            return ImmutableTriple.of(0, null, false);
        }

        // 当日已调用提额接口，不在发起提额请求，引擎不下发默认新客
        String increaseType = increaseAmtUtils.getIncreaseType(reach.getIncreaseAmtParamDto());
        boolean hasRecordToday = increaseAmtUtils.hasRecordToday(crowdDetail.getUserId(), increaseType);
        if (hasRecordToday) {
            reach.setFailReason("当日该用户已调用过风险提额接口,不再发起提额请求.");
            log.info("当日该用户已调用过风险提额接口,不再发起提额请求. 提额类型={}, 策略id={}, 用户id={}", increaseType, reach.getStrategyId(), crowdDetail.getUserId());
            return ImmutableTriple.of(0, null, false);
        }

        UserInfoDto userInfoDto = userInfoDtos.get(0);
        return requestEvent(reach, crowdDetail, this::getUserIdList, batchNum -> {
            Long detailId = Long.parseLong(batchNum.split("_")[0]);
            String orderNo = batchNum;
            RequestHeader header = buildIncreaseAmtHeader(crowdDetail.getUserId(), orderNo, app, innerApp, userInfoDto.getOldUserNo(), deviceId, ip);
            // 自定义业务参数
            String newBatchNum = batchNum.split("_")[1];
            Request request = buildIncreaseAmtBody(reach.getIncreaseAmtParamDto(), reach.getDetailTableNo(), newBatchNum, detailId, reach.getStrategyId(), "real", crowdDetail.getApp(),
                    crowdDetail.getInnerApp(), userInfoDto, orderNo);
            Resp resp = increaseAmtClient.increaseAmt(header, request);
            if (resp == null) {
                resp = new Resp();
                resp.setCode("");
                resp.setMessage("none");
            }
            return Pair.of(resp.isSucceed(), Triple.of(String.valueOf(resp.getCode()), resp.getMessage(), newBatchNum));
        });
    }

    @Override
    public ImmutableTriple<Integer, EventPushBatchDo, Boolean> sendXDayInterestFreeEvent(DispatchDto reach, String app, String innerApp, CrowdDetailDo crowdDetail) {
        List<CouponSendBatchReq.User> list = this.getUserList(crowdDetail);
        return requestEvent(reach, crowdDetail, this::getUserList, batchNum -> {
            CouponSendBatchReq req = new CouponSendBatchReq();
            req.setActivityId(Convert.toLong(reach.getStrategyMarketChannelTemplateId()));
            req.setBatchNum(batchNum);
            req.setApp(app);
            req.setInnerApp(innerApp);
            req.setAllBatchCount(list.size());
            req.setUserList(list);
            BaseCouponResponse<Object> response = couponClient.sendBatch(new BaseCouponRequester<>(req));
            return Pair.of(response.isSuccess(), Triple.of(String.valueOf(response.getStatus()), response.getMessage(), batchNum));
        });
    }

    @Override
    public ImmutableTriple<Integer, EventPushBatchDo, Boolean> sendAiProntoEvent(DispatchDto reach, String app, String innerApp, CrowdDetailDo crowdDetail) {
        Map<String, Object> tempParam = templateParamService.getAiTempParam(crowdDetail, reach.getStrategyId(), reach.getAiProntoChannelDto());
        return requestEvent(reach, crowdDetail, this::getUserList, batchNum -> {
            AiSendArgs aiSendArgs = new AiSendArgs();
            aiSendArgs.setBatchNumber(batchNum);

            AiProntoChannelDto aiProntoChannelDto = JsonUtil.parse(reach.getStrategyMarketChannelDo().getExtInfo(), AiProntoChannelDto.class);
            if (aiProntoChannelDto != null && StringUtils.isNotBlank(aiProntoChannelDto.getNameTypeId())) {
                aiSendArgs.setAiChannelType(aiProntoChannelDto.getNameTypeId());
            }

            aiSendArgs.setStrategyId(reach.getStrategyId());
            aiSendArgs.setStrategyType(reach.getStrategyRulerEnum().getStrategyType());
            AiUserData aiUserData = new AiUserData(crowdDetail.getUserId(), crowdDetail.getApp());
            if (!CollectionUtils.isEmpty(tempParam)) {
                aiUserData.setParams(tempParam);
            }
            aiSendArgs.setUserDataList(Collections.singletonList(aiUserData));
            AiSendResp response = aiClient.doBatchSendAi(aiSendArgs);
            return Pair.of(response.isSuccess(), Triple.of(String.valueOf(response.getStatus()), response.getMessage(), batchNum));
        });
    }

    @Override
    public ImmutableTriple<Integer, EventPushBatchDo, Boolean> sendLifeRightsEvent(DispatchDto reach, String app, String innerApp, CrowdDetailDo crowdDetail) {
        String goodsId = reach.getStrategyMarketChannelTemplateId();
        String activityId = reach.getActivityId();
        GoodsDetail goodsDetail = goodsService.getGoodsDetailByGoodsId(Long.parseLong(goodsId));
        List<CouponSendBatchReq.User> list = this.getUserList(crowdDetail);
        return requestEvent(reach, crowdDetail, this::getUserList, batchNum -> {
            CouponSendBatchReq req = new CouponSendBatchReq();
            req.setActivityId(Convert.toLong(activityId));
            req.setBatchNum(batchNum);
            req.setApp(app);
            req.setInnerApp(innerApp);
            req.setAllBatchCount(list.size());
            req.setGoodsId(goodsDetail.getGoodsId().toString());
            req.setGoodsName(goodsDetail.getGoodsName());
            req.setType(2);
            req.setJumpType(goodsDetail.getJumpType());
            req.setJumpUrl(goodsDetail.getJumpUrl());
            req.setUserList(list);
            BaseCouponResponse<Object> response = couponClient.sendBatch(new BaseCouponRequester<>(req));
            return Pair.of(response.isSuccess(), Triple.of(String.valueOf(response.getStatus()), response.getMessage(), batchNum));
        });
    }


    private List<CouponSendBatchReq.User> getUserList(CrowdDetailDo crowdDetail) {
        CouponSendBatchReq.User user = new CouponSendBatchReq.User();
        user.setUserId(crowdDetail.getUserId());
        user.setMobile(crowdDetail.getMobile());
        user.setName(crowdDetail.getMobile());
        return Collections.singletonList(user);
    }

    private List<AiUserData> getAiUserDataList(CrowdDetailDo crowdDetail, Map<String, Object> paramMap) {
        AiUserData aiUserData = new AiUserData();
        aiUserData.setUserNo(crowdDetail.getUserId());
        aiUserData.setApp(crowdDetail.getApp());
        aiUserData.setParams(paramMap);
        return Collections.singletonList(aiUserData);
    }

    public boolean sendDelayedPopupEvent(DelayedPopupChannelDto delayedPopupChannelDto, Long userId) {
        if (delayedPopupChannelDto == null || StringUtils.isBlank(delayedPopupChannelDto.getPopupId()) || delayedPopupChannelDto.getExpiredHours() == null || userId == null) {
            log.info("EventDispatchServiceImpl sendDelayedPopupEvent error, param is null or empty，delayedPopupChannelDto:{}, userId:{}", delayedPopupChannelDto, userId);
            return false;
        }

        try {
            DelayedPopupMsg delayedPopupMsg = new DelayedPopupMsg();
            delayedPopupMsg.setBatchNo(serialNumberUtil.batchNum());
            delayedPopupMsg.setUserNo(String.valueOf(userId));
            delayedPopupMsg.setPopupId(delayedPopupChannelDto.getPopupId());
            delayedPopupMsg.setExpiredHours(delayedPopupChannelDto.getExpiredHours());
            delayedPopupMsg.setPopup_params(delayedPopupChannelDto.getPopup_params());
            log.info("EventDispatchServiceImpl sendDelayedPopupEvent, batchNo:{}, userNo:{}, popupId:{}, expiredHours:{}, params:{}", delayedPopupMsg.getBatchNo(), userId, delayedPopupMsg.getPopupId(), delayedPopupMsg.getExpiredHours(), JsonUtil.toJson(delayedPopupMsg.getPopup_params()));
            return delayedPopupSendMqProducer.asyncSend(delayedPopupMsg);
        } catch (Exception e) {
            log.error("EventDispatchServiceImpl sendDelayedPopupEvent error", e);
        }

        return false;
    }

}
