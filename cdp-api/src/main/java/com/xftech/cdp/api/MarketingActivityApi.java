package com.xftech.cdp.api;

import com.xftech.cdp.api.dto.base.Response;
import com.xftech.cdp.api.dto.marketing.request.ActivityBaseRequest;
import com.xftech.cdp.api.dto.marketing.request.CheckUserEligibilityRequest;
import com.xftech.cdp.api.dto.marketing.request.RegisterRequest;
import com.xftech.cdp.api.dto.marketing.request.WinningRequest;
import com.xftech.cdp.api.dto.marketing.response.*;
import com.xftech.cdp.api.dto.req.external.ExternalBaseRequest;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/11
 */
@Api(tags = "营销活动")
@RequestMapping("/marketing")
public interface MarketingActivityApi {

    @ApiOperation("查询用户活动资格")
    @PostMapping("/checkUserEligibility")
    Response<EligibilityResponse> checkUserEligibility(@Validated @RequestBody ExternalBaseRequest<CheckUserEligibilityRequest> request);

    @ApiOperation("翻卡抽奖")
    @PostMapping("/participateLottery")
    Response<WinningResponse> participateLottery(@Validated @RequestBody ExternalBaseRequest<WinningRequest> request);

    @ApiOperation("已参与用户优惠券膨胀")
    @PostMapping("/couponInflation")
    Response<InflationResponse> couponInflation(@Validated @RequestBody ExternalBaseRequest<ActivityBaseRequest> request);

    @ApiOperation("活动报名")
    @PostMapping("/registerLottery")
    Response<RegisterResponse> registerLottery(@Validated @RequestBody ExternalBaseRequest<RegisterRequest> request);

    @ApiOperation("活动详情页")
    @PostMapping("/activityDetail")
    Response<ActivityDetailResponse> activityDetail(@Validated @RequestBody ExternalBaseRequest<ActivityBaseRequest> request);

    @ApiOperation("活动奖励记录")
    @PostMapping("/recordsBroadcast")
    Response<RecordsBroadcastResponse> recordsBroadcast(@Validated @RequestBody ExternalBaseRequest<ActivityBaseRequest> request);

}