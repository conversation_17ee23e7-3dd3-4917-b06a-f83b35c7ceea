/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.resp.flow.strategy;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ FlowMonitorResp, v 0.1 2024/3/29 09:58 lingang.han Exp $
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class FlowMonitorResp implements Serializable {
    private static final long serialVersionUID = 6824555745216952527L;

    @ApiModelProperty(value = "下发日期")
    private Integer dateValue;

    @ApiModelProperty(value = "策略id")
    private Long strategyId;

    @ApiModelProperty(value = "策略名称")
    private String strategyName;

    @ApiModelProperty(value = "层级")
    private Integer levelNum;

    @ApiModelProperty(value = "批次号")
    private String batchNo;

    @ApiModelProperty(value = "下发人数")
    private Integer dispatchNum;

    @ApiModelProperty(value = "漏斗百分比")
    private String rate;

    @ApiModelProperty(value = "画布下策略")
    private List<FlowMonitorResp>  subFlowMonitor;

}