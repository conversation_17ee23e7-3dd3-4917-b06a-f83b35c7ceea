package com.xftech.cdp.infra.utils;

import cn.hutool.core.util.ObjectUtil;
import com.xinfei.xfframework.context.XFRpcUtil;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;

import java.util.UUID;

/**
 * 线程相关工具类
 *
 * <AUTHOR>
 * @version $ ThreadUtils, v 0.1 2023/9/22 12:49 PM longjie.yuan Exp $
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ThreadUtils {

    static String TID = "customTraceId";

    /**
     * 获取链路id
     *
     * @return
     */
    public static String getTraceId() {
        String tid = MDC.get(TID);
        if (tid != null) {
            return tid;
        }
        tid = getRandomTraceId();
        if (MDC.get(TID) == null) {
            MDC.put(TID, tid);
        }
        return tid;
    }

    /**
     * 生成随机的traceId
     *
     * @return
     */
    public static String getRandomTraceId() {
        String catTraceId = XFRpcUtil.getTraceId();
        if (ObjectUtil.isNotNull(catTraceId) && StringUtils.isNotEmpty(catTraceId)) {
            return catTraceId;
        }
        return UUID.randomUUID().toString().replace("-", "").toUpperCase();
    }


}
