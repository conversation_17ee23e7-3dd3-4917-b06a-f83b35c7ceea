# 重构触达频控设计方案

## 1. 现状分析

### 1.1 当前频控实现分散问题

当前频控逻辑分散在多个服务和方法中，缺乏统一管理：

```
频控实现分散架构：

├── MqConsumeServiceImpl.isReject()                 # 事件级流控
│   └── 使用场景: ✅ T0触达（普通+引擎） ❌ 离线触达不使用
│   └── 特点: MQ消息入口，防止事件重复处理
│
├── DispatchFlcService.dispatchFlc()                # 触达级流控
│   └── 使用场景: ✅ T0触达（普通+引擎） ❌ 离线触达不使用
│   └── 特点: dispatch()前预检查，失败直接终止
│
├── DispatchFlcService.dispatchFlcLock()            # 分布式流控
│   └── 使用场景: ✅ T0触达（普通+引擎） ❌ 离线触达不使用
│   └── 特点: Redis分布式锁，防止并发触达
│
├── FlowCtrlCoreServiceImpl.flowCtrl()              # 离线批量流控
│   └── 使用场景: ❌ T0触达不使用 ✅ 离线普通触达
│   └── 特点: 批量处理，支持新旧流控切换
│
└── StrategyEventDispatchServiceImpl.marketingSend() # 引擎内置流控
    └── 使用场景: ✅ T0引擎触达 ✅ 离线引擎触达 ❌ 普通触达不使用
    └── 特点: 引擎策略专用，渠道级控制
```

### 1.2 按触达类型的频控差异

| 触达类型 | 使用的频控方法 | 执行时机 | 特点 |
|---------|---------------|---------|------|
| **T0-普通触达** | `isReject()` + `dispatchFlc()` + `dispatchFlcLock()` | MQ入口 + dispatch()前 + 分布式锁 | 三重保护，实时性强 |
| **T0-引擎触达** | `isReject()` + `dispatchFlc()` + `dispatchFlcLock()` + `marketingSend()内置` | MQ入口 + dispatch()前 + 分布式锁 + 引擎内部 | 四重保护，最严格 |
| **离线-普通触达** | `flowCtrl()` | 批处理过程中 | 批量流控，支持部分失败 |
| **离线-引擎触达** | `marketingSend()内置` | marketingSend()内部 | 引擎内置，与T0引擎相同 |

### 1.3 主要问题

1. **逻辑分散**：频控逻辑散布在5个不同的类和方法中
2. **重复实现**：T0和离线触达有重复的频控逻辑
3. **维护困难**：修改频控规则需要在多处修改
4. **配置复杂**：不同触达类型使用不同的配置方式
5. **测试困难**：需要分别测试多套频控逻辑

## 2. 重构设计目标

### 2.1 统一频控架构

设计统一的频控服务架构，支持不同触达类型的频控需求：

```mermaid
graph TD
    A[TouchApplicationService] --> B[UnifiedFlowControlService]
    B --> C[FlowControlStrategy]
    C --> D[RealtimeFlowControl]
    C --> E[BatchFlowControl]
    C --> F[EngineFlowControl]
    
    D --> G[EventLevelControl]
    D --> H[DispatchLevelControl]
    D --> I[DistributedLockControl]
    
    E --> J[BatchUserControl]
    E --> K[NewOldVersionControl]
    
    F --> L[ChannelLevelControl]
    F --> M[EngineInternalControl]
```

### 2.2 设计原则

1. **统一入口**：所有频控逻辑通过统一的服务入口
2. **策略模式**：不同触达类型使用不同的频控策略
3. **配置统一**：统一的频控配置管理
4. **扩展性强**：支持新增频控类型和规则
5. **向下兼容**：保持现有频控逻辑的兼容性

## 3. 重构方案设计

### 3.1 统一频控服务接口

```java
/**
 * 统一频控服务
 */
public interface UnifiedFlowControlService {
    
    /**
     * 实时触达频控检查
     * @param request 实时触达频控请求
     * @return 频控结果
     */
    FlowControlResult checkRealtimeFlow(RealtimeFlowControlRequest request);
    
    /**
     * 批量触达频控检查
     * @param request 批量触达频控请求
     * @return 频控结果（包含通过的用户列表）
     */
    BatchFlowControlResult checkBatchFlow(BatchFlowControlRequest request);
    
    /**
     * 引擎触达频控检查
     * @param request 引擎触达频控请求
     * @return 频控结果
     */
    FlowControlResult checkEngineFlow(EngineFlowControlRequest request);
}
```

### 3.2 频控策略接口

```java
/**
 * 频控策略接口
 */
public interface FlowControlStrategy {
    
    /**
     * 执行频控检查
     * @param context 频控上下文
     * @return 频控结果
     */
    FlowControlResult execute(FlowControlContext context);
    
    /**
     * 获取策略类型
     * @return 策略类型
     */
    FlowControlType getType();
    
    /**
     * 是否支持该触达类型
     * @param touchType 触达类型
     * @return 是否支持
     */
    boolean supports(TouchType touchType);
}
```

### 3.3 实时触达频控策略

```java
/**
 * 实时触达频控策略
 */
@Component
public class RealtimeFlowControlStrategy implements FlowControlStrategy {
    
    @Override
    public FlowControlResult execute(FlowControlContext context) {
        RealtimeFlowControlRequest request = (RealtimeFlowControlRequest) context.getRequest();
        
        // 1. 事件级流控检查
        if (isEventLevelBlocked(request)) {
            return FlowControlResult.blocked("事件级流控拦截");
        }
        
        // 2. 触达级流控检查
        if (isDispatchLevelBlocked(request)) {
            return FlowControlResult.blocked("触达级流控拦截");
        }
        
        // 3. 分布式锁流控检查
        if (isDistributedLockBlocked(request)) {
            return FlowControlResult.blocked("分布式锁流控拦截");
        }
        
        // 4. 引擎内置流控检查（如果是引擎触达）
        if (request.isEngineTouch() && isEngineInternalBlocked(request)) {
            return FlowControlResult.blocked("引擎内置流控拦截");
        }
        
        return FlowControlResult.passed();
    }
    
    @Override
    public FlowControlType getType() {
        return FlowControlType.REALTIME;
    }
    
    @Override
    public boolean supports(TouchType touchType) {
        return touchType == TouchType.T0_NORMAL || touchType == TouchType.T0_ENGINE;
    }
}
```

### 3.4 批量触达频控策略

```java
/**
 * 批量触达频控策略
 */
@Component
public class BatchFlowControlStrategy implements FlowControlStrategy {
    
    @Override
    public FlowControlResult execute(FlowControlContext context) {
        BatchFlowControlRequest request = (BatchFlowControlRequest) context.getRequest();
        
        // 1. 批量用户流控检查
        List<CrowdDetailDo> passedUsers = executeBatchFlowControl(request);
        
        // 2. 引擎内置流控检查（如果是离线引擎触达）
        if (request.isEngineTouch()) {
            passedUsers = executeEngineFlowControl(passedUsers, request);
        }
        
        return BatchFlowControlResult.create(passedUsers, 
            request.getOriginalUsers().size() - passedUsers.size());
    }
    
    @Override
    public FlowControlType getType() {
        return FlowControlType.BATCH;
    }
    
    @Override
    public boolean supports(TouchType touchType) {
        return touchType == TouchType.OFFLINE_NORMAL || touchType == TouchType.OFFLINE_ENGINE;
    }
}
```

## 4. 重构实施计划

### 4.1 第一阶段：基础架构搭建

1. **创建统一频控服务接口和实现类**
2. **定义频控策略接口和基础实现**
3. **创建频控配置统一管理**
4. **建立频控结果统一返回格式**

### 4.2 第二阶段：实时触达频控重构

1. **重构MqConsumeServiceImpl.isReject()逻辑**
2. **重构DispatchFlcService.dispatchFlc()逻辑**
3. **重构DispatchFlcService.dispatchFlcLock()逻辑**
4. **集成到RealtimeFlowControlStrategy**

### 4.3 第三阶段：批量触达频控重构

1. **重构FlowCtrlCoreServiceImpl.flowCtrl()逻辑**
2. **保持新旧流控切换兼容性**
3. **集成到BatchFlowControlStrategy**

### 4.4 第四阶段：引擎频控重构

1. **提取StrategyEventDispatchServiceImpl.marketingSend()中的频控逻辑**
2. **创建EngineFlowControlStrategy**
3. **统一T0引擎和离线引擎的频控逻辑**

### 4.5 第五阶段：TouchApplicationService集成

1. **在TouchApplicationService中集成统一频控服务**
2. **替换原有的分散频控调用**
3. **保持向下兼容性**

## 5. 配置统一管理

### 5.1 统一频控配置

```java
/**
 * 统一频控配置
 */
@ConfigurationProperties(prefix = "touch.flow-control")
@Data
public class UnifiedFlowControlConfig {
    
    /**
     * 事件级流控配置
     */
    private EventFlowControlConfig eventLevel;
    
    /**
     * 触达级流控配置
     */
    private DispatchFlowControlConfig dispatchLevel;
    
    /**
     * 分布式锁流控配置
     */
    private DistributedLockConfig distributedLock;
    
    /**
     * 批量流控配置
     */
    private BatchFlowControlConfig batchLevel;
    
    /**
     * 引擎流控配置
     */
    private EngineFlowControlConfig engineLevel;
}
```

### 5.2 Apollo配置迁移

将现有的分散Apollo配置迁移到统一配置：

```properties
# 统一频控配置
touch.flow-control.event-level.enabled=true
touch.flow-control.dispatch-level.sms.enabled=false
touch.flow-control.dispatch-level.voice.enabled=false
touch.flow-control.distributed-lock.enabled=true
touch.flow-control.distributed-lock.timeout=360
touch.flow-control.batch-level.new-version-enabled=true
touch.flow-control.engine-level.enabled=true
```

## 6. 重构收益

### 6.1 架构收益

1. **统一管理**：所有频控逻辑集中在统一服务中
2. **代码复用**：消除重复的频控实现
3. **扩展性强**：新增频控类型只需实现策略接口
4. **测试简化**：统一的测试入口和测试用例

### 6.2 维护收益

1. **配置统一**：所有频控配置在一处管理
2. **逻辑清晰**：每种触达类型的频控逻辑独立且清晰
3. **问题定位**：频控问题可以快速定位到具体策略
4. **监控统一**：统一的频控监控和告警

### 6.3 性能收益

1. **减少重复检查**：避免多次相同的频控检查
2. **批量优化**：批量触达的频控性能优化
3. **缓存共享**：频控结果可以在策略间共享缓存

## 7. 风险控制

### 7.1 兼容性风险

1. **保持现有接口**：重构期间保持现有频控接口不变
2. **灰度切换**：通过开关控制新旧频控逻辑的切换
3. **回滚机制**：支持快速回滚到原有频控逻辑

### 7.2 性能风险

1. **性能测试**：重构前后进行详细的性能对比测试
2. **监控告警**：增加频控性能监控和告警
3. **降级机制**：频控服务异常时的降级处理

### 7.3 数据一致性风险

1. **配置迁移**：确保配置迁移的准确性
2. **数据校验**：频控结果的数据校验机制
3. **日志记录**：详细的频控执行日志记录

## 8. 总结

通过统一频控服务的重构，可以解决当前频控逻辑分散、维护困难的问题，提供更加清晰、可维护、可扩展的频控架构。重构将分阶段进行，确保系统稳定性和向下兼容性。
