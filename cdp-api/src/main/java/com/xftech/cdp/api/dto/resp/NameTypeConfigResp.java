/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ NameTypeConfigResp, v 0.1 2023/10/16 11:30 wancheng.qu Exp $
 */
@Data
public class NameTypeConfigResp implements Serializable {
    @ApiModelProperty(value = "策略类型")
    private List<Config> policyType;
    @ApiModelProperty(value = "撞库方式")
    private List<Config> judgeMethod;
    @ApiModelProperty(value = "结案事件")
    private List<Config> closeCaseEvent;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Config {
        @ApiModelProperty(value = "配置项code")
        private String code;
        @ApiModelProperty(value = "配置项")
        private String name;

    }



}