package com.xftech.cdp.infra.client.randomnum;

import cn.xinfei.usergroup.random.client.AbClient;
import cn.xinfei.usergroup.random.client.bo.AbBO;
import com.alibaba.fastjson.JSON;
import com.xftech.cdp.domain.randomnum.model.RandomListReq;
import com.xftech.cdp.infra.client.randomnum.config.RandomConfig;
import com.xftech.cdp.infra.client.randomnum.model.RandomResp;
import com.xftech.cdp.infra.utils.HttpClientUtil;
import com.xftech.cdp.infra.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @<NAME_EMAIL>
 */
@Slf4j
@Component
public class RandomNumClient {

    @Resource
    RandomConfig randomConfig;
    @Resource
    private HttpClientUtil httpClientUtil;

    public RandomResp getRandomList(RandomListReq requester) {
        String result = request(requester, randomConfig.getRandomListRoute());
        return JSON.parseObject(result, RandomResp.class);
    }

    /**
     * 单个用户获取随机数
     *
     * @param bizKey 业务编号
     * @param userId 用户Id
     * @return 随机数
     */
    public Map<Long, String> randomNumber(String bizKey, Long userId) {
        return this.randomNumber(bizKey, Collections.singletonList(userId), () -> {
        });
    }

    /**
     * 批量用户获取随机数
     *
     * @param bizKey     业务编号
     * @param userIdList 用户Id集合
     * @return 随机数
     */
    public Map<Long, String> randomNumber(String bizKey, List<Long> userIdList, Runnable alarmFun) {
        Map<Long, String> resultMap = userIdList.stream().collect(Collectors.toMap(Function.identity(), item -> ""));
        userIdList.forEach(userId -> resultMap.computeIfPresent(userId, (k, v) -> getRandomNumber(bizKey, k, alarmFun)));
        return resultMap;
    }

    private String getRandomNumber(String bizKey, Long userId, Runnable alarmFun) {
        try {
            AbBO abBO = AbClient.ab(bizKey, String.valueOf(userId),true);
            String randomNumber = String.valueOf(abBO.getRandomNum());
            log.info("获取随机数成功,方法名:ab,场景值:{},用户ID:{},随机数:{}", bizKey, userId, randomNumber);
            return randomNumber;
        } catch (IllegalArgumentException e) {
            log.error("获取随机数异常,参数异常,场景值:{}", bizKey, e);
            if (alarmFun != null) {
                alarmFun.run();
            }
            return "";
        } catch (Exception e) {
            log.error("获取随机数异常,场景值:{},用户ID:{}", bizKey, userId, e);
            return "";
        }
    }

    private <T> String request(T requester, String uri) {
        String url = randomConfig.getHost() + "/" + uri;
        try {
            log.info("random send, url：{}", url);
            long startTime = Instant.now().toEpochMilli();
            String result = httpClientUtil.postJson(url, requester);
            log.info("random send,url:{},request:{},resp:{}, time：{}ms", url, JsonUtil.toJson(requester),
                    result, Instant.now().toEpochMilli() - startTime);
            return result;
        } catch (Exception e) {
            log.error("random send error，url:{}，params:{}", url, requester, e);
            throw e;
        }
    }

}
