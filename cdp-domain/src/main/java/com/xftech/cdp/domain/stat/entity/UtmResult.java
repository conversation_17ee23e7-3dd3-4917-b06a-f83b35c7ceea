package com.xftech.cdp.domain.stat.entity;

import lombok.Data;

/**
 * 渠道结果
 */
@Data
public class UtmResult {
    /**
     * 渠道类型
     */
    private String utmType;
    /**
     * 渠道
     */
    private String utmSource;
    /**
     * 首贷运营规则（可营销——0；进件不可营销——1；注册不可营销——2；放款不可营销——3
     */
    private String newMarketingType;
    private String oldMarketingType;

    /**
     * 首贷注册N天后可营销，-1 默认值（类型为1时，注册一直处于不可营销）
     */
    private Integer newNoMarketingDays;
    private Integer oldNoMarketingDays;
    /**
     * true 可营销 false不可营销
     */
    private Boolean hit;

}
