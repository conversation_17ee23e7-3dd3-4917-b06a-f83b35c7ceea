package com.xftech.cdp.domain.strategy.service.dispatch.offline.impl;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.xftech.cdp.domain.strategy.model.dispatch.StrategyContext;
import com.xftech.cdp.domain.strategy.model.dto.DelayedPopupChannelDto;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.rocketmq.popup.DelayedPopupMsg;
import com.xftech.cdp.infra.rocketmq.popup.DelayedPopupSendMqProducer;
import com.xftech.cdp.infra.utils.SerialNumberUtil;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version v1.0 2025/6/5
 * @description StrategyDispatchForDelayedPopup
 */
@Slf4j
@Service
public class StrategyDispatchForDelayedPopup {

    @Resource
    private SerialNumberUtil serialNumberUtil;
    @Resource
    private DelayedPopupSendMqProducer delayedPopupSendMqProducer;

    public void dispatch(StrategyContext strategyContext, List<CrowdDetailDo> userList) {
        if (strategyContext == null || CollectionUtils.isEmpty(userList) || strategyContext.getStrategyMarketChannelDo() == null || StringUtils.isBlank(strategyContext.getStrategyMarketChannelDo().getExtInfo())) {
            return;
        }

        String extInfo = strategyContext.getStrategyMarketChannelDo().getExtInfo();
        try {
            Map<String, Object> extInfoMap = JSONObject.parseObject(extInfo, Map.class);
            DelayedPopupChannelDto delayedPopupChannelDto = DelayedPopupChannelDto.convertToDelayedPopupChannelDto(extInfoMap);
            if (StringUtils.isBlank(delayedPopupChannelDto.getPopupId()) || delayedPopupChannelDto.getExpiredHours() == null) {
                return;
            }

            for (CrowdDetailDo crowdDetailDo : userList) {
                DelayedPopupMsg delayedPopupMsg = new DelayedPopupMsg();
                delayedPopupMsg.setBatchNo(serialNumberUtil.batchNum());
                delayedPopupMsg.setUserNo(String.valueOf(crowdDetailDo.getUserId()));
                delayedPopupMsg.setPopupId(delayedPopupChannelDto.getPopupId());
                delayedPopupMsg.setExpiredHours(delayedPopupChannelDto.getExpiredHours());
                delayedPopupMsg.setPopup_params(delayedPopupChannelDto.getPopup_params());

                delayedPopupSendMqProducer.asyncSend(delayedPopupMsg);
            }
        } catch (Exception e) {
            log.error("StrategyDispatchForDelayedPopup dispatch error", e);
        }
    }

}
