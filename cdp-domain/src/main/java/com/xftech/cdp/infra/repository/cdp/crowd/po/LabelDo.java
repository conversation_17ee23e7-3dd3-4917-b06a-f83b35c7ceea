package com.xftech.cdp.infra.repository.cdp.crowd.po;

import com.xftech.cdp.api.dto.req.label.LabelPublishReq;
import com.xftech.cdp.infra.repository.Do;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class LabelDo extends Do {

    /**
     * 一级分类,0:用户属性, 1:用户行为, 2: 黑名单
     */
    private Integer primaryLabel;

    /**
     * 二级分类,0:app信息,1:分组标识,2:渠道管理,3:脱落节点,4:注册行为,5:登录行为,6:进件行为,7:授信行为,8:提现行为,9:调额行为,10:还款行为,11:营销行为,12:营销相关,13:风险相关,14渠道相关
     */
    private Integer secondaryLabel;

    /**
     * 标签名称
     */
    private String labelName;
    /**
     * 标签类型,0:sql字段标签,1:自定义加工标签
     */
    private Integer labelType;

    /**
     * 对应数仓字段
     */
    private String dataWarehouseField;

    /**
     * 可选配置类型, 0:固定选项,1:输入项,3:时间范围,4:数值范围
     */
    private Integer configurationOptionType;

    /**
     * 前端配置交互类型
     *   com.xftech.cdp.domain.label.dto.LabelExchangeEnum
     *  1, 固定单选-null 2, 固定单选-time 3, 固定单选-boolean 4, 固定多选 5, 文本输入 6, 未来时间范围 7, 过去时间范围 8, 数值范围-整数 9, 数值范围-小数'
     */
    private Integer exchangeType;

    /**
     * 可选配置项
     */
    private String configurationOption;

    /**
     * 配置sql反射
     */
    private String configurationReflect;

    /**
     * 描述
     */
    private String description;


    /**
     * 业务线
     */
    private String businessType;

    public LabelDo(LabelPublishReq req) {
        super.setId(req.getLabelId());
        this.dataWarehouseField = req.getLabelCode();
        this.labelName = req.getLabelName();
        this.primaryLabel = req.getFirstLevel();
        this.secondaryLabel = req.getSecondLevel();
        this.businessType = String.join(",", req.getBusinessTypes());
    }
}
