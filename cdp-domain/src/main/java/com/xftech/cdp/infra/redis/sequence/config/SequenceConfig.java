package com.xftech.cdp.infra.redis.sequence.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * @<NAME_EMAIL>
 * @date 2023/3/24 15:32
 */
@Getter
@RefreshScope
@Component
public class SequenceConfig {

    @Value("${sole.sequence.segment.max.value:1000}")
    private Long segmentMaxValue;
}
