/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.label.biz;

import com.alibaba.fastjson.JSONObject;
import com.xftech.base.database.Page;
import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.req.label.*;
import com.xftech.cdp.api.dto.resp.label.AssociatedCrowdsResp;
import com.xftech.cdp.api.dto.resp.label.LabelClassificationResp;
import com.xftech.cdp.api.dto.resp.label.LabelConfigListResp;
import com.xftech.cdp.api.dto.resp.label.LabelExchangeResp;
import com.xftech.cdp.domain.crowd.exception.CrowdException;
import com.xftech.cdp.domain.crowd.model.enums.LabelEnum;
import com.xftech.cdp.domain.crowd.service.impl.CrowdPackServiceImpl;
import com.xftech.cdp.domain.crowd.service.impl.LabelServiceImpl;
import com.xftech.cdp.domain.label.dto.LabelCheckBoxConfigurationOption;
import com.xftech.cdp.domain.label.dto.LabelExchangeEnum;
import com.xftech.cdp.domain.label.dto.MetaLabelJoinLabelDto;
import com.xftech.cdp.domain.label.service.MetaLabelService;
import com.xftech.cdp.infra.exception.BizException;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPackDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.LabelDo;
import com.xftech.cdp.infra.repository.cdp.label.po.MetaLabelDo;
import com.xftech.cdp.infra.utils.JsonUtil;
import com.xftech.cdp.infra.utils.SsoUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $ LabelBiz, v 0.1 2024/6/20 16:22 lingang.han Exp $
 */
@Service
@Slf4j
@AllArgsConstructor
public class LabelBiz {
    private final LabelServiceImpl labelService;
    private final CrowdPackServiceImpl crowdPackService;
    private final MetaLabelService metaLabelService;

    public List<LabelClassificationResp> classification() {
        List<LabelClassificationResp> labelClassificationRespList = new ArrayList<>();
        for (LabelEnum.PrimaryLabelEnum primaryLabelEnum : LabelEnum.PrimaryLabelEnum.values()) {
            LabelClassificationResp labelClassificationResp = new LabelClassificationResp();
            labelClassificationResp.setLabelId(primaryLabelEnum.getCode());
            labelClassificationResp.setLabelName(primaryLabelEnum.getDescription());
            labelClassificationResp.setItems(Arrays.stream(LabelEnum.SecondaryLabelEnum.values())
                    .filter(item -> item.getPrimaryLabelEnum().getCode() == primaryLabelEnum.getCode()).map(item -> new LabelClassificationResp(item.getCode(), item.getDescription())).collect(Collectors.toList()));
            labelClassificationRespList.add(labelClassificationResp);
        }
        return labelClassificationRespList;
    }

    public List<LabelExchangeResp> configList() {
        return Arrays.stream(LabelExchangeEnum.values()).map(item -> new LabelExchangeResp(item.getCode(), item.getDesc())).collect(Collectors.toList());
    }

    public PageResultResponse<LabelConfigListResp> list(LabelConfigListReq labelConfigListReq) {
        Page<MetaLabelJoinLabelDto> records = metaLabelService.queryJoinLabelPage(labelConfigListReq);
        List<LabelConfigListResp> result = new ArrayList<>();
        records.getList().forEach(item -> {
            LabelConfigListResp labelConfigVo = new LabelConfigListResp();
            BeanUtils.copyProperties(item, labelConfigVo);
            if (StringUtils.isNotBlank(item.getLabelEnumValue())) {
                labelConfigVo.setLabelEnumValues(Arrays.asList(item.getLabelEnumValue().split(",")));
            }
            labelConfigVo.setOnLineLabelEnum(getOnlineLabelEnum(item));
            result.add(labelConfigVo);
        });
        return new PageResultResponse<>(result, records.getBeginNum(), records.getFetchNum(), records.getTotalNum());
    }

    private Map<String, String> getOnlineLabelEnum(MetaLabelJoinLabelDto metaLabelJoinLabelDto) {
        try {
            Integer exchangeType = metaLabelJoinLabelDto.getExchangeType();
            String configurationReflect = metaLabelJoinLabelDto.getConfigurationReflect();
            String configurationOption = metaLabelJoinLabelDto.getConfigurationOption();
            Map<String, String> result = new HashMap<>();
            if (LabelExchangeEnum.CHECKBOX.getCode().equals(exchangeType)) {
                if (StringUtils.isBlank(configurationReflect)) {
                    LabelCheckBoxConfigurationOption labelEnumConfig = JSONObject.parseObject(configurationOption, LabelCheckBoxConfigurationOption.class);
                    labelEnumConfig.getItems().forEach(item -> result.put(item, item));
                    return result;
                }
                return JsonUtil.toMap(configurationReflect);
            }
            return null;
        } catch (Exception e) {
            log.error("分页查询枚举配置失败", e);
            return null;
        }
    }

    public PageResultResponse<AssociatedCrowdsResp> associatedCrowds(AssociatedCrowdsReq associatedCrowdsReq) {
        Page<CrowdPackDo> records = crowdPackService.queryPageByLabelId(associatedCrowdsReq);
        List<AssociatedCrowdsResp> result = new ArrayList<>();
        records.getList().forEach(item -> {
                    AssociatedCrowdsResp associatedCrowdsResp = new AssociatedCrowdsResp();
                    associatedCrowdsResp.setCrowdId(item.getId());
                    associatedCrowdsResp.setCrowdName(item.getCrowdName());
                    associatedCrowdsResp.setStatus(item.getStatus());
                    associatedCrowdsResp.setBusinessType(item.getBusinessType());
                    result.add(associatedCrowdsResp);
                }
        );
        return new PageResultResponse<>(result, records.getBeginNum(), records.getFetchNum(), records.getTotalNum());
    }

    @Transactional(transactionManager = "transactionManager", rollbackFor = Exception.class)
    public Boolean labelPublish(LabelPublishReq labelPublishReq) {
        //check
        if (Objects.equals(labelPublishReq.getIfAdd(), 0) && labelPublishReq.getLabelId() == null) {
            throw new BizException("标签修改配置,参数有误,缺少主键");
        }
        LabelDo labelDo = new LabelDo(labelPublishReq);

        if (Objects.equals(labelPublishReq.getIfAdd(), 1)) {
            labelDo.setCreatedOp(SsoUtil.get().getName());
        }
        labelDo.setUpdatedOp(SsoUtil.get().getName());

        LabelExchangeEnum exchangeType = LabelExchangeEnum.getInstance(labelPublishReq.getLabelExchangeCode());
        labelDo.setExchangeType(exchangeType.getCode());
        switch (exchangeType) {
            case RADIO_BUTTON_NULL:
                //固定单选
                labelDo.setConfigurationOptionType(LabelEnum.LabelOptionTypeEnum.FIXED_RADIO.getCode());
                labelDo.setConfigurationReflect("{\"yes\":\" is not null \",\"no\":\" is null \"}");
                break;
            case RADIO_BUTTON_TIME:
                //固定单选
                labelDo.setConfigurationOptionType(LabelEnum.LabelOptionTypeEnum.FIXED_RADIO.getCode());
                labelDo.setConfigurationReflect("{\"yes\":\" >= getdate()\",\"no\":\" < getdate()\"}");
                break;
            case RADIO_BUTTON_BOOLEAN:
                //固定单选
                labelDo.setConfigurationOptionType(LabelEnum.LabelOptionTypeEnum.FIXED_RADIO.getCode());
                labelDo.setConfigurationReflect("{\"yes\":\" = 1 \",\"no\":\" = 0\"}");
                break;
            case CHECKBOX:
                //固定多选
                labelDo.setConfigurationOptionType(LabelEnum.LabelOptionTypeEnum.FIXED_CHECK.getCode());
                setLabelEnumInfo(labelDo, labelPublishReq);
                break;
            case INPUT:
                //文本输入
                labelDo.setConfigurationOptionType(LabelEnum.LabelOptionTypeEnum.ENTRY.getCode());
                break;
            case TIMEFRAME_NEXT:
                //未来时间范围
                labelDo.setConfigurationOptionType(LabelEnum.LabelOptionTypeEnum.TIME_LIMIT.getCode());
                labelDo.setConfigurationOption("{\"min\":0, \"max\":9999, \"eval\":\"0<=xxx;xxx<=9999\",\"integer\":true, \"type\":1}");
                break;
            case TIMEFRAME_PASS:
                //过去时间范围
                labelDo.setConfigurationOptionType(LabelEnum.LabelOptionTypeEnum.TIME_LIMIT.getCode());
                labelDo.setConfigurationOption("{\"min\":0, \"max\":9999, \"eval\":\"0<=xxx;xxx<=9999\",\"integer\":true, \"type\":0}");
                break;
            case NUMERIC_RANGE_INTEGER:
                //数值范围
                labelDo.setConfigurationOptionType(LabelEnum.LabelOptionTypeEnum.VALUE_RANGE.getCode());
                labelDo.setConfigurationOption("{\"min\":1, \"max\":999999, \"eval\":\"1<=xxx;xxx<=999999\",\"integer\":true}");
                break;
            case NUMERIC_RANGE_DOUBLE:
                //数值范围
                labelDo.setConfigurationOptionType(LabelEnum.LabelOptionTypeEnum.VALUE_RANGE.getCode());
                labelDo.setConfigurationOption("{\"min\":0, \"max\":999999, \"eval\":\"0<=xxx;xxx<=999999\",\"integer\":false}");
                break;
            default:
                throw new BizException("不识别该配置,请检查交互方式配置");
        }
        MetaLabelDo byLabelCode = metaLabelService.getByLabelCode(labelPublishReq.getLabelCode());
        if (byLabelCode == null) {
            throw new BizException("查询不到元数据标签");
        }
        MetaLabelDo metaLabelDo = new MetaLabelDo();
        metaLabelDo.setId(byLabelCode.getId());
        metaLabelDo.setOnline(1);
        Boolean updateMetaLabelResult = metaLabelService.updateById(metaLabelDo);
        Boolean updateLabelResult = labelService.saveOrUpdate(labelDo);
        Boolean clearCheckResult = metaLabelService.clearCheckResult(byLabelCode.getId());

        return updateMetaLabelResult && updateLabelResult && clearCheckResult;
    }

    private void setLabelEnumInfo(LabelDo labelDo, LabelPublishReq labelPublishReq) {
        Map<String, String> enumResult = labelPublishReq.getEnumResult();
        if (CollectionUtils.isEmpty(enumResult)) {
            throw new BizException("标签枚举配置不能为空");
        }
        List<String> labelEnumKey = new ArrayList<>(enumResult.keySet());
        LabelCheckBoxConfigurationOption labelCheckBoxConfigurationOption = new LabelCheckBoxConfigurationOption(labelEnumKey);

        //存在枚举映射
        AtomicBoolean exitsReflect = new AtomicBoolean(false);
        enumResult.forEach((k, v) -> {
            if (!Objects.equals(k, v)) {
                exitsReflect.set(true);
            }
        });
        String enumResultStr = JsonUtil.toJson(enumResult);
        String configurationOption = JsonUtil.toJson(labelCheckBoxConfigurationOption);

        //todo 待标签枚举重构后处理
        if (enumResultStr.length() > 4096 || configurationOption.length() > 4096) {
            throw new BizException("枚举长度过长,请重新配置 或者 换其他交互类型");
        }

        if (exitsReflect.get()) {
            labelDo.setConfigurationReflect(JsonUtil.toJson(enumResultStr));
        }
        labelDo.setConfigurationOption(configurationOption);
    }

    public Boolean labelRemark(LabelRemarkReq labelRemarkReq) {
        return labelService.updateDescription(labelRemarkReq);
    }

    @Transactional(transactionManager = "transactionManager", rollbackFor = Exception.class)
    public Boolean labelDiscard(LabelDiscardReq labelDiscardReq) {
        //check 未删除
        Integer size = crowdPackService.querySizeByLabelId(labelDiscardReq.getLabelId());
        if (size > 0) {
            throw new CrowdException("该标签存在未删除的人群包中,不能删除");
        }
        LabelDo labelDo = labelService.getById(labelDiscardReq.getLabelId());
        if (labelDo == null) {
            throw new CrowdException("未查询到该标签");
        }
        MetaLabelDo metaLabelDo = new MetaLabelDo();
        metaLabelDo.setLabelCode(labelDo.getDataWarehouseField());
        metaLabelDo.setOnline(0);
        Boolean labelDiscard = labelService.labelDiscard(labelDiscardReq.getLabelId());
        Boolean existByLabelCode = labelService.existByLabelCode(labelDo.getDataWarehouseField());
        if (existByLabelCode) {
            return true;
        }
        return metaLabelService.updateByLabelCode(metaLabelDo);
    }

    public Boolean clearCheckResult(ClearCheckResultReq checkResultReq) {
        return metaLabelService.clearCheckResult(checkResultReq.getMetaLabelId());
    }
}