# AbstractDispatchService流控方法分析报告

## 1. 概述

您的想法是**正确的**！AbstractDispatchService中的两个方法确实分别用于不同的触达场景：

- **`request()`方法**: 离线普通触达调用，用于处理**批量流控**
- **`requestEvent()`方法**: T0普通触达调用，用于处理**单个流控**

## 2. 方法详细分析

### 2.1 request()方法 - 批量流控处理

#### 2.1.1 方法签名
```java
// 位置: AbstractDispatchService.java:91-94
protected <T> ImmutablePair<Integer, CrowdPushBatchDo> request(
    MarketChannelTypeEnum channelTypeEnum, 
    DispatchDto reach, 
    String app, String innerApp, 
    List<CrowdDetailDo> crowdDetailList,
    Function<List<CrowdDetailDo>, List<T>> convertListFun,
    FourParamsFunction<MarketChannelTypeEnum, List<CrowdDetailDo>, List<CrowdDetailDo>, List<T>, List<T>> filterListFun,
    BiFunction<String, List<T>, Pair<Boolean, Pair<String, String>>> request)
```

#### 2.1.2 核心流控逻辑
```java
// 位置: AbstractDispatchService.java:107-108
batchDispatchFlcResultDto = dispatchFlcService
    .batchDispatchFlcLockThenReturnExcludeUsers(reach, crowdPushBatchDo, batchNum, app, innerApp, list, crowdDetailList, respPair);
```

#### 2.1.3 调用场景
- **离线普通触达**: 通过XxlJob定时任务触发
- **调用路径**: 
  ```
  XxlJob → AbstractStrategyDispatchService.coreLogicExecute() 
  → batchDispatch() → dispatchHandler() 
  → BatchDispatchServiceImpl.sendXxx() → AbstractDispatchService.request()
  ```

### 2.2 requestEvent()方法 - 单个流控处理

#### 2.2.1 方法签名
```java
// 位置: AbstractDispatchService.java:283
protected <T> ImmutableTriple<Integer, EventPushBatchDo, Boolean> requestEvent(
    DispatchDto reach, 
    CrowdDetailDo crowdDetail, 
    Function<CrowdDetailDo, List<T>> convertListFun, 
    Function<String, Pair<Boolean, Triple<String, String, String>>> request)
```

#### 2.2.2 核心流控逻辑
```java
// 位置: AbstractDispatchService.java:296-297
boolean flcLockRet = dispatchFlcService.dispatchFlcLock(reach, eventPushBatchDo,
    dispatchDetail, batchNum, crowdDetail, abstractStrategyEventDispatchService);
```

#### 2.2.3 调用场景
- **T0普通触达**: 通过MQ消息实时触发
- **调用路径**: 
  ```
  MQ消息 → StrategyEventDispatchServiceImpl.execSend() 
  → dispatch() → EventDispatchServiceImpl.sendXxxEvent() 
  → AbstractDispatchService.requestEvent()
  ```

## 3. 流控机制对比分析

### 3.1 批量流控 vs 单个流控

| 对比维度 | 批量流控(request) | 单个流控(requestEvent) |
|---------|------------------|----------------------|
| **处理对象** | List<CrowdDetailDo> | 单个CrowdDetailDo |
| **流控方法** | batchDispatchFlcLockThenReturnExcludeUsers | dispatchFlcLock |
| **并发处理** | 线程池并发处理多用户 | 单用户串行处理 |
| **锁粒度** | 用户级分布式锁 | 用户级分布式锁 |
| **返回类型** | CrowdPushBatchDo | EventPushBatchDo |
| **适用场景** | 离线批量触达 | 实时单用户触达 |

### 3.2 流控开关配置

#### 3.2.1 批量流控开关
```properties
# Apollo配置: 麻雀apollo配置.txt
# 批量流控开关通过AppConfigService.getBatchDispatchFlcLockSwitch()控制
```

#### 3.2.2 单个流控开关
```properties
# Apollo配置: 麻雀apollo配置.txt:328-331
singleDispatchFlc.1 = false  # 短信渠道流控开关
singleDispatchFlc.2 = false  # 电销渠道流控开关  
singleDispatchFlc.3 = false  # 优惠券渠道流控开关
singleDispatchFlc.4 = false  # 其他渠道流控开关
```

## 4. 流程图分析

### 4.1 离线普通触达-批量流控流程

```mermaid
graph TD
    A[XxlJob定时任务] --> B[AbstractStrategyDispatchService.coreLogicExecute]
    B --> C[batchDispatch 分页查询]
    C --> D[dispatchHandler 批量下发]
    D --> E[BatchDispatchServiceImpl.sendXxx]
    E --> F[AbstractDispatchService.request]
    F --> G[DispatchFlcService.batchDispatchFlcLockThenReturnExcludeUsers]
    G --> H[线程池并发处理]
    H --> I[tryLockSingle 单用户流控]
    I --> J[FlowCtrlCoreServiceImpl.flowCtrl]
    J --> K[查询用户触达指标]
    K --> L[流控规则判断]
    L --> M[返回通过流控的用户列表]
    M --> N[批量下发到外部系统]
    
    style F fill:#e1f5fe
    style G fill:#fff3e0
    style I fill:#f3e5f5
```

### 4.2 T0普通触达-单个流控流程

```mermaid
graph TD
    A[MQ业务事件消息] --> B[StrategyEventDispatchServiceImpl.execSend]
    B --> C[预筛选处理]
    C --> D[dispatch 触达处理]
    D --> E[dispatchFlc 触达级流控]
    E --> F[EventDispatchServiceImpl.sendXxxEvent]
    F --> G[AbstractDispatchService.requestEvent]
    G --> H[DispatchFlcService.dispatchFlcLock]
    H --> I[tryLock 用户级分布式锁]
    I --> J[AbstractStrategyEventDispatchService.flowCtrl]
    J --> K[FlowCtrlCoreServiceImpl.flowCtrl]
    K --> L[查询用户触达指标]
    L --> M[流控规则判断]
    M --> N[单用户下发到外部系统]
    
    style G fill:#e1f5fe
    style H fill:#fff3e0
    style I fill:#f3e5f5
```

## 5. 数据流向分析

### 5.1 批量流控数据流

```mermaid
graph LR
    A[人群包数据] --> B[分页查询用户列表]
    B --> C[List&lt;CrowdDetailDo&gt;]
    C --> D[批量流控处理]
    D --> E[并发单用户流控]
    E --> F[过滤被流控用户]
    F --> G[剩余用户批量下发]
    G --> H[CrowdPushBatchDo记录]
```

### 5.2 单个流控数据流

```mermaid
graph LR
    A[MQ事件消息] --> B[BizEventVO]
    B --> C[CrowdDetailDo转换]
    C --> D[单用户流控]
    D --> E[流控通过/拒绝]
    E --> F[单用户下发]
    F --> G[EventPushBatchDo记录]
```

## 6. 核心差异总结

### 6.1 设计理念差异
- **批量流控**: 面向**吞吐量优化**，适合离线大批量处理
- **单个流控**: 面向**实时性优化**，适合实时事件触发

### 6.2 性能特点差异
- **批量流控**: 高吞吐量，但延迟较高
- **单个流控**: 低延迟，但吞吐量有限

### 6.3 应用场景差异
- **批量流控**: 营销活动、定期推送等离线场景
- **单个流控**: 用户行为触发、实时营销等在线场景

## 7. Apollo配置使用情况

### 7.1 流控相关配置
```properties
# 事件流控配置
eventFlcConfig = {
    "Start": 600,
    "Login": 600, 
    "Start1IncreaseCreditLimit": 600,
    "RepaySuccess": 600
}

# 流控开关配置
singleDispatchFlc.1 = false
singleDispatchFlc.2 = false
singleDispatchFlc.3 = false
singleDispatchFlc.4 = false

# 流控线程池配置
dispatchFlcExecutor.pool.coreSize = 8
dispatchFlcExecutor.pool.maxSize = 20
```

### 7.2 渠道配置
```properties
# 短信渠道配置
cdp.sms.host = http://sms.xinfei.io
strategy.dispatch.channel.sms.pagesize = 1000

# 电销渠道配置  
cdp.tele.host = http://inner-api-telemarketing-backend.xinfei.io/cdp
strategy.dispatch.channel.tele.pagesize = 1500

# 优惠券渠道配置
cdp.coupon.host = http://inner-coupon-api.xinyongfei.io
strategy.dispatch.channel.coupon.pagesize = 1000
```

## 8. 结论

您的分析完全正确：
1. **`request()`方法确实用于离线普通触达的批量流控处理**
2. **`requestEvent()`方法确实用于T0普通触达的单个流控处理**
3. **两个方法在流控机制、数据处理方式、应用场景上都有明确的分工**

这种设计很好地体现了**单一职责原则**，针对不同的业务场景采用了不同的流控策略，既保证了离线场景的高吞吐量，又满足了实时场景的低延迟需求。
