package com.xftech.cdp.api.dto.resp;


import lombok.Data;

import java.time.LocalDateTime;


/**
 * <AUTHOR>
 */
@Data
public class AbsMonitorListResp {
    // 周期格式 当前/总数
    private String cycleFormat;

    public AbsMonitorListResp() {
        cycleFormat = "-/-";
    }

    public<T> T getBizDate() {
        return null;
    }
    
    public LocalDateTime getDateTime() {
        return null;
    }
}
