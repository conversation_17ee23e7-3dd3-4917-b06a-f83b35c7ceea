package com.xftech.cdp.api.dto.resp;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/2/16
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CrowdLabelsResp extends BaseResp {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "一级分类,0:用户属性, 1:用户行为, 2: 黑名单")
    private Integer primaryLabel;

    @ApiModelProperty(value = "二级分类,0:app信息,1:分组标识,2:渠道管理,3:脱落节点,4:注册行为,5:登录行为,6:进件行为,7:授信行为,8:提现行为,9:调额行为,10:还款行为,11:营销行为,12:营销相关,13:风险相关,14渠道相关")
    private Integer secondaryLabel;

    @ApiModelProperty(value = "标签名称")
    private String labelName;

    @ApiModelProperty(value = "对应数仓字段")
    private String dataWarehouseField;

    @ApiModelProperty(value = "可选配置类型, 0:固定单项,1:输入项,3:时间范围,4:数值范围")
    private Integer configurationOptionType;

    @ApiModelProperty(value = "可选配置项")
    private JSONObject configurationOption;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "下级列表")
    private List<CrowdLabelsResp> list;
}
