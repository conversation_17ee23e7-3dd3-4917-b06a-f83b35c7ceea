<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="label">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.crowd.po.LabelDo">
        <id column="id" property="id"/>
        <result column="primary_label" property="primaryLabel"/>
        <result column="secondary_label" property="secondaryLabel"/>
        <result column="label_name" property="labelName"/>
        <result column="label_type" property="labelType"/>
        <result column="data_warehouse_field" property="dataWarehouseField"/>
        <result column="configuration_option_type" property="configurationOptionType"/>
        <result column="exchange_type" property="exchangeType"/>
        <result column="configuration_option" property="configurationOption"/>
        <result column="configuration_reflect" property="configurationReflect"/>
        <result column="description" property="description"/>
        <result column="business_type" property="businessType"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="d_flag" property="dFlag"/>
        <result column="created_op" property="createdOp"/>
        <result column="updated_op" property="updatedOp"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, primary_label, secondary_label, label_name, label_type, data_warehouse_field, configuration_option_type, exchange_type, configuration_option, configuration_reflect, description,business_type, created_time, updated_time, d_flag, created_op, updated_op
    </sql>

    <select id="getAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from label
        where d_flag = 0 and find_in_set(#{businessType}, business_type)
    </select>

    <select id="getAllWithoutLimit" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from label
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from label
        where id = #{id,jdbcType=BIGINT}
        and d_flag = 0
    </select>

    <select id="selectByLabelCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from label
        where data_warehouse_field = #{labelCode,jdbcType=VARCHAR}
        and d_flag = 0
    </select>

    <select id="selectBatchIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from label
        where id in
        <foreach collection="list" item="id" index="index" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from label
        where id = #{id}
        limit 1;
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        update label
        set d_flag = 1
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.xftech.cdp.infra.repository.cdp.crowd.po.LabelDo" useGeneratedKeys="true">
        insert into label
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="primaryLabel != null">primary_label,</if>
            <if test="secondaryLabel != null">secondary_label,</if>
            <if test="labelName != null">label_name,</if>
            <if test="dataWarehouseField != null">data_warehouse_field,</if>
            <if test="configurationOptionType != null">configuration_option_type,</if>
            <if test="exchangeType != null">exchange_type,</if>
            <if test="configurationOption != null">configuration_option,</if>
            <if test="configurationReflect != null">configuration_reflect,</if>
            <if test="description != null">description,</if>
            <if test="businessType != null">business_type,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updatedTime != null">updated_time,</if>
            <if test="dFlag != null">d_flag,</if>
            <if test="createdOp != null">created_op,</if>
            <if test="updatedOp != null">updated_op,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="primaryLabel != null">#{primaryLabel,jdbcType=SMALLINT},</if>
            <if test="secondaryLabel != null">#{secondaryLabel,jdbcType=SMALLINT},</if>
            <if test="labelName != null">#{labelName,jdbcType=VARCHAR},</if>
            <if test="dataWarehouseField != null">#{dataWarehouseField,jdbcType=VARCHAR},</if>
            <if test="configurationOptionType != null">#{configurationOptionType,jdbcType=SMALLINT},</if>
            <if test="exchangeType != null">#{exchangeType,jdbcType=SMALLINT},</if>
            <if test="configurationOption != null">#{configurationOption,jdbcType=VARCHAR},</if>
            <if test="configurationReflect != null">#{configurationReflect,jdbcType=VARCHAR},</if>
            <if test="description != null">#{description,jdbcType=VARCHAR},</if>
            <if test="businessType != null">#{businessType,jdbcType=VARCHAR},</if>
            <if test="createdTime != null">#{createdTime,jdbcType=TIMESTAMP},</if>
            <if test="updatedTime != null">#{updatedTime,jdbcType=TIMESTAMP},</if>
            <if test="dFlag != null">#{dFlag,jdbcType=SMALLINT},</if>
            <if test="createdOp != null">#{createdOp,jdbcType=VARCHAR},</if>
            <if test="updatedOp != null">#{updatedOp,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.xftech.cdp.infra.repository.cdp.crowd.po.LabelDo">
        update label
        <set>
            <if test="primaryLabel != null">
                primary_label = #{primaryLabel,jdbcType=SMALLINT},
            </if>
            <if test="secondaryLabel != null">
                secondary_label = #{secondaryLabel,jdbcType=SMALLINT},
            </if>
            <if test="labelName != null">
                label_name = #{labelName,jdbcType=VARCHAR},
            </if>
            <if test="dataWarehouseField != null">
                data_warehouse_field = #{dataWarehouseField,jdbcType=VARCHAR},
            </if>
            <if test="configurationOptionType != null">
                configuration_option_type = #{configurationOptionType,jdbcType=SMALLINT},
            </if>
            <if test="exchangeType != null">
                exchange_type = #{exchangeType,jdbcType=SMALLINT},
            </if>
            <if test="configurationOption != null">
                configuration_option = #{configurationOption,jdbcType=VARCHAR},
            </if>
            <if test="configurationReflect != null">
                configuration_reflect = #{configurationReflect,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
            </if>
            <if test="businessType != null">
                business_type = #{businessType,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedTime != null">
                updated_time = #{updatedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="dFlag != null">
                d_flag = #{dFlag,jdbcType=SMALLINT},
            </if>
            <if test="createdOp != null">
                created_op = #{createdOp,jdbcType=VARCHAR},
            </if>
            <if test="updatedOp != null">
                updated_op = #{updatedOp,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

</mapper>
