package com.xinfei.touch.domain.model.unified;

import com.xinfei.touch.domain.model.TouchStatus;
import lombok.Data;
import java.util.Map;

/**
 * 触达统计信息模型
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-23
 */
@Data
public class TouchStatistics {
    
    /**
     * 开始时间
     */
    private Long startTime;
    
    /**
     * 结束时间
     */
    private Long endTime;
    
    /**
     * 处理耗时（毫秒）
     */
    private Long duration;
    
    /**
     * 总用户数
     */
    private Integer totalUsers;
    
    /**
     * 已处理用户数
     */
    private Integer processedUsers;
    
    /**
     * 各状态统计
     */
    private Map<TouchStatus, Integer> statusCount;
    
    /**
     * 平均处理时间（毫秒/用户）
     */
    private Double avgProcessingTime;
    
    /**
     * 吞吐量（用户/秒）
     */
    private Double throughput;
    
    /**
     * 创建统计信息
     */
    public static TouchStatistics create(Long startTime) {
        TouchStatistics statistics = new TouchStatistics();
        statistics.setStartTime(startTime);
        statistics.setStatusCount(new java.util.HashMap<>());
        return statistics;
    }
    
    /**
     * 完成统计
     */
    public void complete(Long endTime, Integer totalUsers, Integer processedUsers) {
        this.endTime = endTime;
        this.totalUsers = totalUsers;
        this.processedUsers = processedUsers;
        
        if (startTime != null && endTime != null) {
            this.duration = endTime - startTime;
            
            // 计算平均处理时间
            if (processedUsers != null && processedUsers > 0) {
                this.avgProcessingTime = (double) duration / processedUsers;
            }
            
            // 计算吞吐量（用户/秒）
            if (duration != null && duration > 0 && processedUsers != null) {
                this.throughput = (double) processedUsers / (duration / 1000.0);
            }
        }
    }
    
    /**
     * 增加状态统计
     */
    public void incrementStatus(TouchStatus status) {
        if (statusCount == null) {
            statusCount = new java.util.HashMap<>();
        }
        statusCount.put(status, statusCount.getOrDefault(status, 0) + 1);
    }
    
    /**
     * 设置状态统计
     */
    public void setStatusCount(TouchStatus status, Integer count) {
        if (statusCount == null) {
            statusCount = new java.util.HashMap<>();
        }
        statusCount.put(status, count);
    }
    
    /**
     * 获取状态统计
     */
    public Integer getStatusCount(TouchStatus status) {
        return statusCount != null ? statusCount.getOrDefault(status, 0) : 0;
    }
    
    /**
     * 获取成功数
     */
    public Integer getSuccessCount() {
        return getStatusCount(TouchStatus.SUCCESS);
    }
    
    /**
     * 获取失败数
     */
    public Integer getFailedCount() {
        return getStatusCount(TouchStatus.FAILED);
    }
    
    /**
     * 获取流控数
     */
    public Integer getFlowControlledCount() {
        return getStatusCount(TouchStatus.FLOW_CONTROLLED);
    }
    
    /**
     * 获取处理中数
     */
    public Integer getPendingCount() {
        return getStatusCount(TouchStatus.PENDING);
    }
    
    /**
     * 获取部分成功数
     */
    public Integer getPartialSuccessCount() {
        return getStatusCount(TouchStatus.PARTIAL_SUCCESS);
    }
    
    /**
     * 获取成功率
     */
    public Double getSuccessRate() {
        if (processedUsers == null || processedUsers == 0) {
            return 0.0;
        }
        return (double) getSuccessCount() / processedUsers * 100;
    }
    
    /**
     * 获取失败率
     */
    public Double getFailedRate() {
        if (processedUsers == null || processedUsers == 0) {
            return 0.0;
        }
        return (double) getFailedCount() / processedUsers * 100;
    }
    
    /**
     * 获取流控率
     */
    public Double getFlowControlledRate() {
        if (processedUsers == null || processedUsers == 0) {
            return 0.0;
        }
        return (double) getFlowControlledCount() / processedUsers * 100;
    }
    
    /**
     * 获取处理进度（0-100）
     */
    public Double getProgress() {
        if (totalUsers == null || totalUsers == 0) {
            return 100.0;
        }
        if (processedUsers == null) {
            return 0.0;
        }
        return (double) processedUsers / totalUsers * 100;
    }
    
    /**
     * 判断是否处理完成
     */
    public boolean isCompleted() {
        return totalUsers != null && processedUsers != null && 
               processedUsers.equals(totalUsers);
    }
    
    /**
     * 获取剩余用户数
     */
    public Integer getRemainingUsers() {
        if (totalUsers == null || processedUsers == null) {
            return null;
        }
        return totalUsers - processedUsers;
    }
    
    /**
     * 估算剩余时间（毫秒）
     */
    public Long getEstimatedRemainingTime() {
        if (avgProcessingTime == null || getRemainingUsers() == null) {
            return null;
        }
        return (long) (avgProcessingTime * getRemainingUsers());
    }
    
    /**
     * 转换为字符串（用于日志）
     */
    @Override
    public String toString() {
        return String.format("TouchStatistics{totalUsers=%d, processedUsers=%d, duration=%dms, " +
                           "successRate=%.2f%%, throughput=%.2f users/sec}", 
                           totalUsers, processedUsers, duration, 
                           getSuccessRate(), throughput);
    }
}
