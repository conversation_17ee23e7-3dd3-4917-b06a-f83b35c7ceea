package com.xftech.cdp.domain.stat.repository;

import com.xftech.cdp.BaseTest;
import com.xftech.cdp.domain.stat.entity.OfflineDecisionRecordEntity;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

public class OfflineDecisionRecordDaoTest extends BaseTest {


    @Autowired
    private OfflineDecisionRecordDao offlineDecisionRecordDao;

    @Test
    public void saveOfflineDecisionRecord() {
        OfflineDecisionRecordEntity entity = new OfflineDecisionRecordEntity();
        entity.setStrategyId(1L);
        entity.setMarketChannel(1);
        entity.setMarketChannelId(2L);
        entity.setApp("xyf");
        entity.setInnerApp("xyf");
        entity.setMobile("18924835789");
        entity.setAppUserId(1234566L);
        entity.setTraceId("111");
        entity.setDecisionResult(1);
        entity.setFailCode(200);
        entity.setFailReason("11111");
        entity.setDecisionDetail("xxx");
        entity.setTableNameNo("202306");
        List<OfflineDecisionRecordEntity> list = new ArrayList<>();
        list.add(entity);
        offlineDecisionRecordDao.saveOfflineDecisionRecord(list);

    }
}