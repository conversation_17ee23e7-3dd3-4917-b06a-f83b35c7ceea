package com.xftech.cdp.infra.rocketmq.rcspt.handler;

import com.alibaba.fastjson.JSONObject;
import com.xftech.cdp.domain.mq.MqConsumeService;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventMessageVO;
import com.xftech.cdp.infra.rocketmq.EventEnum;
import com.xftech.cdp.infra.rocketmq.MessageHandler;
import com.xftech.cdp.infra.rocketmq.RocketMQEnum;
import com.xftech.cdp.infra.rocketmq.dto.AccessControlDiversionVO;
import com.xftech.cdp.infra.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version $ AccessControlDiversionHandler, v 0.1 2024/11/11 10:22 tianshuo.qiu Exp $
 */
@Slf4j
@Component
public class AccessControlDiversionHandler implements MessageHandler {
    @Autowired
    private MqConsumeService mqConsumeService;
    @Override
    public String getTopic() {
        return RocketMQEnum.TP_ACCESS_CONTROL_DIVERSION.getTopic();
    }

    @Override
    public boolean execute(String messageId, Object message) {
        AccessControlDiversionVO messageVO = JSONObject.parseObject(message.toString(), AccessControlDiversionVO.class);
        if (StringUtils.isAnyBlank(messageVO.getApp(), messageVO.getInnerApp(), messageVO.getAccessAction(), messageVO.getLoanType(),messageVO.getBizType(),messageVO.getDiversionLabel()) || Objects.isNull(messageVO.getForbiddenApplyDay())) {
            log.info("AccessControlDiversionHandler 不符合[风控禁申]事件条件 message={}", JSONObject.toJSONString(messageVO));
        }
        BizEventMessageVO bizEventMessageVO = transform(messageVO);
        bizEventMessageVO.setBizEventType(EventEnum.ACCESS_CONTROL_DIVERSION.getEventType());
        mqConsumeService.bizEventProcess(messageId, bizEventMessageVO);
        return true;
    }

    private BizEventMessageVO transform(AccessControlDiversionVO accessControlDiversionVO) {
        BizEventMessageVO bizEventMessageVO = new BizEventMessageVO();
        bizEventMessageVO.setApp(accessControlDiversionVO.getApp());
        bizEventMessageVO.setInnerApp(accessControlDiversionVO.getInnerApp());
        bizEventMessageVO.setAppUserId(Long.parseLong(accessControlDiversionVO.getUserId()));
        BizEventMessageVO.ExtrData extrData = new BizEventMessageVO.ExtrData();
        extrData.setBizType(accessControlDiversionVO.getBizType());
        extrData.setLoanType(accessControlDiversionVO.getLoanType());
        extrData.setForbiddenApplyDay(accessControlDiversionVO.getForbiddenApplyDay());
        extrData.setDiversionLabel(accessControlDiversionVO.getDiversionLabel());
        extrData.setAccessAction(accessControlDiversionVO.getAccessAction());
        bizEventMessageVO.setExtrData(extrData);
        bizEventMessageVO.setTriggerDatetime(DateUtil.convertStr(new Date()));
        return bizEventMessageVO;
    }
}
