/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.interceptor;

import cn.dev33.satoken.SaManager;
import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.stp.StpUtil;
import com.xftech.cdp.api.dto.resp.auth.LoginUser;
import com.xftech.cdp.infra.annotation.ApiNoSigner;
import com.xftech.cdp.infra.annotation.ApiSigner;
import com.xftech.cdp.infra.exception.BizException;
import com.xftech.cdp.infra.exception.InfraException;
import com.xftech.cdp.infra.utils.SsoUtil;
import com.xinfei.ssocore.client.component.SsoUserDataUtil;
import com.xinfei.ssocore.client.component.SsoUserInfoDTO;
import com.xinfei.ssocore.client.config.SsoClientConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.CollectionUtils;
import org.springframework.util.PathMatcher;
import org.springframework.web.method.HandlerMethod;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version $ NewTokenInterceptor, v 0.1 2024/7/18 16:25 lingang.han Exp $
 */

@Slf4j
@Component
public class NewTokenInterceptor {
    @Resource
    private SsoUtil ssoUtil;

    @Resource
    private SsoClientConfig ssoClientConfig;

    @Resource
    private SsoUserDataUtil ssoUserDataUtil;

    void preHandle(HttpServletRequest request, HandlerMethod handlerMethod) {
        //对外接口加签的注解
        ApiSigner methodAnnotation = handlerMethod.getMethodAnnotation(ApiSigner.class);
        ApiNoSigner noSignMethodAnnotation = handlerMethod.getMethodAnnotation(ApiNoSigner.class);
        String uri = request.getRequestURI();
        try {
            if (methodAnnotation != null || noSignMethodAnnotation != null) {
                return;
            }
            if (ssoClientConfig.getAuthInterceptorFlag()) {
                SaRouter.match(this.ssoClientConfig.getAuthList()).notMatch(this.ssoClientConfig.getWhiteList())
                        .check((r) -> {
                            StpUtil.checkLogin();
                        });
                String clientId = ssoClientConfig.getClientId();
                PathMatcher pathMatcher = new AntPathMatcher();
                List<String> userApi = ssoUserDataUtil.getUserApi(clientId);
                if (CollectionUtils.isEmpty(userApi)) {
                    throw new InfraException(HttpStatus.FORBIDDEN.value(), "没有权限访问当前资源.");
                }
                Optional<String> optionalMatcher =
                        userApi.stream().filter(apiUrl -> pathMatcher.match(apiUrl, uri)).findAny();
                if (!optionalMatcher.isPresent()) {
                    throw new InfraException(HttpStatus.FORBIDDEN.value(), "没有权限访问当前资源.");
                }
                // 获取用户信息
                LoginUser loginUser = buildUser(ssoUserDataUtil.getUserInfo());
                if (ssoUserDataUtil.getUserRole(clientId).contains("admin")) {
                    loginUser.setIfAdmin(true);
                }
                ssoUtil.set(loginUser, true);
                Object tokenTimeOut = SaManager.getSaTokenDao().getObject("sso:token_time_out");
                if (StpUtil.getTokenTimeout() < Long.parseLong(tokenTimeOut.toString()) / 2L) {
                    StpUtil.renewTimeout(Long.parseLong(tokenTimeOut.toString()));
                }
            }
        } catch (NotLoginException notLoginException) {
            log.error(notLoginException.getMessage(), notLoginException);
            throw new BizException(notLoginException.getMessage());
        } catch (Exception e) {
            throw e;
        }
    }


    void afterCompletion() {
        ssoUtil.remove();
    }


    private LoginUser buildUser(SsoUserInfoDTO ssoUserInfoDTO) {
        LoginUser loginUser = new LoginUser();
        loginUser.setId(ssoUserInfoDTO.getUserId());
        loginUser.setName(ssoUserInfoDTO.getFullName());
        loginUser.setMobile(ssoUserInfoDTO.getMobile());
        loginUser.setEmail(ssoUserInfoDTO.getEmail());
        return loginUser;
    }


}