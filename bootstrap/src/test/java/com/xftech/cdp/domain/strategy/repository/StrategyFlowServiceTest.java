/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.repository;

import com.xftech.cdp.BaseTest;
import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.req.flow.strategy.FlowMonitorReq;
import com.xftech.cdp.api.dto.resp.flow.strategy.FlowMonitorResp;
import com.xftech.cdp.domain.stat.service.impl.StatStrategyFlowDataServiceImpl;
import com.xftech.cdp.domain.strategy.model.strategy.FlowListQueryBO;
import com.xftech.cdp.domain.strategy.service.flow.StrategyFlowService;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyFlowDo;
import com.xftech.cdp.infra.utils.JsonUtil;
import org.apache.logging.log4j.core.util.JsonUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ StrategyFlowServiceTest, v 0.1 2023/12/21 11:27 yye.xu Exp $
 */

public class StrategyFlowServiceTest extends BaseTest {

    @Autowired
    private StrategyFlowService strategyFlowService;
    @Autowired
    private StatStrategyFlowDataServiceImpl statStrategyFlowDataService;

    @Test
    public void selectList() {
        FlowListQueryBO flowListQueryBO = new FlowListQueryBO();
        //flowListQueryBO.setBusinessType("123");
        flowListQueryBO.setMarketChannel(Arrays.asList());
        List<StrategyFlowDo> strategyFlowDos = strategyFlowService
                .selectList(flowListQueryBO);
        System.out.println(JsonUtil.toJson(strategyFlowDos));
    }

    @Test
    public void selectFlowDataList() {
        FlowMonitorReq flowMonitorReq = new FlowMonitorReq();
        flowMonitorReq.setFlowNo("716121971481391104");
        PageResultResponse<FlowMonitorResp> monitor = statStrategyFlowDataService.monitor(flowMonitorReq);
        System.out.println("查询结果" + JsonUtil.toJson(monitor));
    }

}