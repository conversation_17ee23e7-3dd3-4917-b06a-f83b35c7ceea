package com.xftech.cdp.api.dto.marketing.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version v1.0 2024/10/11
 * @description ParticipateRequest
 */
@ApiModel("翻卡抽奖 响应对象")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WinningResponse {

    @ApiModelProperty(value = "优惠券ID")
    private String couponId;

    @ApiModelProperty(value = "优惠券名称")
    private String couponName;

    @ApiModelProperty(value = "优惠券活动名称")
    private String couponActivityName;

    @ApiModelProperty(value = "优惠券有效期倒计时-时间戳")
    private Long expirationCountdown;

}
