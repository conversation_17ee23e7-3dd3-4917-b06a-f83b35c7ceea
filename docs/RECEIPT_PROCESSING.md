# 回执统一处理功能设计

## 概述

根据《麻雀重构专项一期-触达》设计方案和《T0实时和离线触达回执动作清单》，回执处理是触达服务的三大核心功能之一（触达、频控、回执）。本文档基于现有MQ消息队列集成方式，设计统一的回执处理功能。

## 现状分析

### 当前回执集成方式
- **短信回执**: RabbitMQ队列 `sms_supplier_report_callback`
- **优惠券回执**: RocketMQ消费者监听
- **AI外呼回执**: Topic `CALL_CENTER_AI_CALLBACK`
- **Push回执**: 直接方法调用
- **语音外呼**: 发送成功后直接设置状态

### 设计原则
- **零改造成本**: 保持现有外部系统集成方式不变
- **统一处理逻辑**: 内部统一回执处理流程和数据更新
- **向后兼容**: 支持现有回执处理逻辑平滑迁移

## 功能特性

### 1. 统一回执处理核心
- 保持现有MQ消费者和方法调用入口
- 统一的回执数据解析和状态更新逻辑
- 插件化的回执处理器，易于扩展新渠道

### 2. 回执处理流程
1. **回执接收** - 保持现有MQ消费者和方法调用方式
2. **消息解析** - 解析原始回执数据为标准格式
3. **状态更新** - 统一更新触达记录状态
4. **业务回调** - 根据配置执行下游业务回调
5. **监控上报** - 上报回执处理监控指标

### 3. 回执状态管理
- 成功（SUCCESS）
- 失败（FAILED）
- 处理中（PROCESSING）
- 流控拦截（FLOW_CONTROLLED）
- 已取消（CANCELLED）

## 架构设计

### 分层架构

```
API层 (touch-api)
├── ReceiptController - 回执接收控制器
└── ApiResponse - 统一响应格式

应用层 (touch-application)
├── ReceiptApplicationService - 回执应用服务
├── ReceiptHandler - 回执处理器接口
├── BusinessCallbackService - 业务回调服务
└── TouchMonitorService - 监控服务

领域层 (touch-domain)
├── ReceiptMessage - 回执消息领域模型
├── TouchRecord - 触达记录领域模型
├── ReceiptDomainService - 回执领域服务
└── TouchRecordRepository - 触达记录仓储接口

基础设施层 (touch-infrastructure)
├── SmsReceiptHandler - 短信回执处理器
├── PushReceiptHandler - Push回执处理器
├── VoiceReceiptHandler - 电销回执处理器
├── CouponReceiptHandler - 优惠券回执处理器
└── TouchRecordRepositoryImpl - 触达记录仓储实现
```

## 核心组件

### 1. 回执消息模型 (ReceiptMessage)

```java
public class ReceiptMessage {
    private String requestId;        // 请求ID
    private String batchNo;          // 批次号
    private TouchChannel channel;    // 触达渠道
    private Long userId;             // 用户ID
    private TouchStatus status;      // 回执状态
    private String errorCode;        // 错误码
    private String errorMessage;     // 错误信息
    private LocalDateTime receiptTime; // 回执时间
    private Boolean needCallback;    // 是否需要业务回调
    private String callbackUrl;      // 回调地址
    private Map<String, Object> extParams; // 扩展参数
}
```

### 2. 回执处理器接口 (ReceiptHandler)

```java
public interface ReceiptHandler {
    TouchChannel getChannel();                    // 获取支持的渠道
    ReceiptMessage handle(String rawMessage);     // 处理回执消息
    ReceiptMessage parseMessage(String rawMessage); // 解析回执消息
    boolean needCallback(ReceiptMessage receipt); // 是否需要业务回调
}
```

### 3. 回执应用服务 (ReceiptApplicationService)

```java
@Service
public class ReceiptApplicationService {
    // 处理回执消息
    public void processReceipt(TouchChannel channel, String rawMessage);
    
    // 各渠道回执处理方法
    public void processSmsReceipt(String rawMessage);
    public void processPushReceipt(String rawMessage);
    public void processTeleReceipt(String rawMessage);
    public void processCouponReceipt(String rawMessage);
}
```

## MQ消息队列集成

### 回执接收方式

#### 1. 短信回执 - RabbitMQ
```yaml
# 队列配置
queue: sms_supplier_report_callback
exchange: exchange_report_callback_topic
routing-key: sms_center_callback_app_xyf-cdp

# 消息格式
[
  {
    "batchNo": "batch_001",
    "userId": 123456,
    "strategyId": 789,
    "status": "delivered",
    "errorCode": null,
    "errorMessage": null,
    "receiptTime": "2025-06-21T15:30:00",
    "mobile": "***********",
    "content": "短信内容",
    "supplierId": "supplier_001"
  }
]
```

#### 2. 优惠券回执 - RocketMQ
```yaml
# Topic配置
topic: tp_xyf_cdp_coupon_callback
consumer-group: cg_xyf_cdp_coupon_callback

# 消息格式
[
  {
    "batchNo": "batch_002",
    "userId": 123457,
    "strategyId": 790,
    "status": "successed",
    "usedStatus": 0,
    "couponType": 4,
    "bizType": "credit_limit",
    "couponId": "coupon_001",
    "couponAmount": 10000
  }
]
```

#### 3. AI外呼回执 - RocketMQ
```yaml
# Topic配置
topic: CALL_CENTER_AI_CALLBACK
consumer-group: cg_xyf_cdp_ai_callback

# 消息格式
{
  "batchNo": "batch_003",
  "userId": 123458,
  "strategyId": 791,
  "status": "CALLED",
  "callResult": "answered",
  "callDuration": 120,
  "recordUrl": "http://example.com/record.mp3",
  "aiResult": "用户接听并完成对话"
}
```

#### 4. Push回执 - 直接方法调用
```java
// 直接调用应用服务方法
receiptApplicationService.processPushReceipt(pushReportReq);
```

## 渠道特性

### 1. 短信回执 (SmsReceiptHandler)
- 状态映射：delivered/success → SUCCESS，failed/error → FAILED
- 失败时需要业务回调
- 支持错误码和错误信息

### 2. Push回执 (PushReceiptHandler)
- 状态映射：delivered/success → SUCCESS，failed/error → FAILED
- 失败时需要业务回调
- 支持推送到达和点击统计

### 3. 电销回执 (VoiceReceiptHandler)
- 状态映射：answered/success → SUCCESS，failed/no_answer/busy → FAILED
- 失败或有录音时需要业务回调
- 扩展信息：通话时长、挂断原因、录音地址

### 4. 优惠券回执 (CouponReceiptHandler)
- 状态映射：successed → SUCCESS，failed → FAILED
- 通常都需要业务回调
- 扩展信息：使用状态、业务类型

## 监控指标

### 回执处理监控
- `touch.receipt.success` - 回执成功计数
- `touch.receipt.failure` - 回执失败计数
- `touch.receipt.latency` - 回执处理延迟

### 业务回调监控
- `touch.callback.success` - 回调成功计数
- `touch.callback.failure` - 回调失败计数
- `touch.callback.latency` - 回调响应延迟

## 配置说明

### 应用配置
```yaml
# 业务回调配置
touch:
  callback:
    timeout: 5000ms      # 回调超时时间
    retry-times: 3       # 重试次数
    async: true          # 是否异步回调
```

## 使用示例

### 1. 接收短信回执
```bash
curl -X POST http://localhost:8080/api/receipt/sms \
  -H "Content-Type: application/json" \
  -d '{
    "requestId": "req_001",
    "batchNo": "batch_001",
    "userId": 123456,
    "status": "delivered"
  }'
```

### 2. 接收优惠券回执
```bash
curl -X POST http://localhost:8080/api/receipt/coupon \
  -H "Content-Type: application/json" \
  -d '{
    "requestId": "req_004",
    "batchNo": "batch_004",
    "userId": 123459,
    "status": "successed",
    "usedStatus": "unused",
    "bizType": "credit_limit"
  }'
```

## 扩展指南

### 添加新渠道回执处理器

1. **实现ReceiptHandler接口**
```java
@Component
public class NewChannelReceiptHandler implements ReceiptHandler {
    @Override
    public TouchChannel getChannel() {
        return TouchChannel.NEW_CHANNEL;
    }
    
    @Override
    public ReceiptMessage handle(String rawMessage) {
        // 实现回执处理逻辑
    }
}
```

2. **添加API接口**
```java
@PostMapping("/new-channel")
public ApiResponse<Void> newChannelReceipt(@RequestBody String rawMessage) {
    receiptApplicationService.processNewChannelReceipt(rawMessage);
    return ApiResponse.success();
}
```

3. **Spring会自动注册处理器**
- 无需手动配置，ReceiptApplicationService会自动发现并注册新的处理器

## 总结

回执统一处理功能完善了触达服务的闭环处理能力，实现了：

1. **统一性** - 所有渠道回执统一处理流程
2. **可扩展性** - 插件化设计，易于添加新渠道
3. **可观测性** - 完整的监控指标和日志
4. **可靠性** - 异常处理和重试机制
5. **业务集成** - 支持业务回调和状态同步

这为触达服务提供了完整的"触达-频控-回执"闭环处理能力，符合设计方案的核心要求。
