package com.xftech.cdp.api.dto.strategy;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * @<NAME_EMAIL>
 */
@Data
public class StrategyEventParam {
    /**
     * 事件
     */
    private String bizEventType;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * APP
     */
    private String app;
    /**
     * 操作系统
     */
    private String os;
    /**
     * innerApp
     */
    private String innerApp;
    /**
     * utm来源
     */
    private String utmSource;
    /**
     * 发送时间
     */
    private LocalDateTime sendDatetime;
    /**
     * 额外数据
     */
    private Map<String, Object> extraData;
    /**
     * 触发时间
     */
    private LocalDateTime triggerDatetime;
    /**
     * 用户ID
     */
    @JsonProperty("creditUserId")
    @JSONField(name = "creditUserId")
    private Long appUserId;

    /**
     * 随机数
     */
    private String abNum;

    /**
     * UID后两位
     */
    private Integer appUserIdLast2;

    /**
     * 策略ID
     */
    private Long strategyId;
}
