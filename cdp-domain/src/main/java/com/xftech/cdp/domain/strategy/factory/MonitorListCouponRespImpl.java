package com.xftech.cdp.domain.strategy.factory;

import cn.hutool.core.convert.Convert;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xftech.cdp.domain.strategy.model.enums.StrategyExecStatusEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyMarketChannelEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyRulerEnum;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyExecLogDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/2/23
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MonitorListCouponRespImpl extends AbsMonitorListRespFinal {

    @ApiModelProperty(value = "活动ID")
    @ExcelProperty(index = 3, value = "活动ID")
    protected String templateId;


    @ApiModelProperty(value = "应发优惠券人数")
    @ExcelProperty(index = 5, value = "应发优惠券人数")
    protected String triggerCount;

    @ApiModelProperty(value = "麻雀推送进度")
    @ExcelProperty(index = 6, value = "麻雀推送进度")
    protected String selfProcess;

    @ApiModelProperty(value = "优惠券发送进度")
    @ExcelProperty(index = 7, value = "优惠券发送进度")
    protected String sendProcess;

    @ApiModelProperty(value = "发送成功数")
    @ExcelProperty(index = 8, value = "发送成功数")
    protected String succCount;

    @ApiModelProperty(value = "发送成功率")
    @ExcelProperty(index = 9, value = "发送成功率")
    protected String succRate;

    @ApiModelProperty(value = "使用数")
    @ExcelProperty(index = 10, value = "使用数")
    protected String usageCount;

    @ApiModelProperty(value = "使用成功率")
    @ExcelProperty(index = 11, value = "使用成功率")
    protected String usageRate;

    @ApiModelProperty(value = "营销触达成功率")
    @ExcelProperty(index = 12, value = "营销触达成功率")
    protected String reachSuccRate;

    @Override
    public MonitorListCouponRespImpl convertRes(StrategyDo strategyDo,StrategyExecLogDo item) {
        boolean executingFlag = strategyDo.getSendRuler() != StrategyRulerEnum.EVENT.getCode() && StrategyExecStatusEnum.EXECUTING.getCode() == item.getExecStatus();
        boolean sendCountZero = item.getSendCount() == 0;
        boolean actualCountZero = item.getActualCount() == 0;
        boolean succCountZero = item.getSuccCount() == 0;
        MonitorListCouponRespImpl monitorListCouponResp = new MonitorListCouponRespImpl();
        StrategyMarketChannelEnum strategyMarketChannelEnum = StrategyMarketChannelEnum.getInstance(item.getStrategyMarketChannel());
        StrategyExecStatusEnum strategyExecStatusEnum = StrategyExecStatusEnum.getInstance(item.getExecStatus());
        monitorListCouponResp.setDateTime(item.getExecTime());
        monitorListCouponResp.setGroupName(item.getStrategyGroupName());
        monitorListCouponResp.setMarketChannel(strategyMarketChannelEnum.getDescription());
        monitorListCouponResp.setExecStatus(Objects.nonNull(strategyExecStatusEnum) ? strategyExecStatusEnum.getDescription() : "");
        monitorListCouponResp.setTemplateId(item.getTemplateId());
        monitorListCouponResp.setTriggerCount(String.valueOf(item.getExecCount()));
        monitorListCouponResp.setSelfProcess(item.getExecCount() + "/" + item.getSendCount());
        monitorListCouponResp.setSendProcess(executingFlag || sendCountZero ? "-" : item.getSendCount() + "/" + item.getActualCount());
        monitorListCouponResp.setSuccCount(executingFlag || sendCountZero || actualCountZero ? "-" : String.valueOf(item.getSuccCount()));
        monitorListCouponResp.setSuccRate(executingFlag || sendCountZero || actualCountZero ? "-" : BigDecimal.valueOf(((float) item.getSuccCount() / item.getActualCount()) * 100).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
        monitorListCouponResp.setUsageCount(executingFlag || sendCountZero || actualCountZero || succCountZero ? "-" : String.valueOf(item.getUsedCount()));
        monitorListCouponResp.setUsageRate(executingFlag || sendCountZero || actualCountZero || succCountZero ? "-" : BigDecimal.valueOf(((float) item.getUsedCount() / item.getSuccCount()) * 100).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
        monitorListCouponResp.setReachSuccRate(item.getExecCount() == 0 || sendCountZero || actualCountZero ? "-" : BigDecimal.valueOf(((float) item.getSuccCount() / item.getExecCount()) * 100).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
        return monitorListCouponResp;
    }
}
