<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="statOfflineStrategyFlowDataMapper">

    <resultMap id="BaseResultMap" type="com.xftech.cdp.domain.stat.entity.StatOfflineStrategyFlowDataEntity">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="bizDate" column="biz_date" jdbcType="TIMESTAMP"/>
        <result property="strategyId" column="strategy_id" jdbcType="BIGINT"/>
        <result property="crowdUserNum" column="crowd_user_num" jdbcType="INTEGER"/>
        <result property="crowdRefreshTime" column="crowd_refresh_time" jdbcType="TIMESTAMP"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,biz_date,strategy_id,crowd_user_num,crowd_refresh_time,
        created_time,updated_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from stat_offline_strategy_flow_data
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByStrategyId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from stat_offline_strategy_flow_data
        where strategy_id = #{strategyId,jdbcType=BIGINT} and d_flag = 0 order by biz_date desc
    </select>

    <select id="getByStrategyIdAndBizDate" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from stat_offline_strategy_flow_data
        where strategy_id = #{strategyId,jdbcType=BIGINT} and biz_date = #{bizDate}
    </select>

    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.xftech.cdp.domain.stat.entity.StatOfflineStrategyFlowDataEntity"
            useGeneratedKeys="true">
        insert into stat_offline_strategy_flow_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="bizDate != null">biz_date,</if>
            <if test="strategyId != null">strategy_id,</if>
            <if test="crowdUserNum != null">crowd_user_num,</if>
            <if test="crowdRefreshTime != null">crowd_refresh_time,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updatedTime != null">updated_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="bizDate != null">#{bizDate,jdbcType=TIMESTAMP},</if>
            <if test="strategyId != null">#{strategyId,jdbcType=BIGINT},</if>
            <if test="crowdUserNum != null">#{crowdUserNum,jdbcType=INTEGER},</if>
            <if test="crowdRefreshTime != null">#{crowdRefreshTime,jdbcType=TIMESTAMP},</if>
            <if test="createdTime != null">#{createdTime,jdbcType=TIMESTAMP},</if>
            <if test="updatedTime != null">#{updatedTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.xftech.cdp.domain.stat.entity.StatOfflineStrategyFlowDataEntity">
        update stat_offline_strategy_flow_data
        <set>
            <if test="bizDate != null">
                biz_date = #{bizDate,jdbcType=TIMESTAMP},
            </if>
            <if test="strategyId != null">
                strategy_id = #{strategyId,jdbcType=BIGINT},
            </if>
            <if test="crowdUserNum != null">
                crowd_user_num = #{crowdUserNum,jdbcType=INTEGER},
            </if>
            <if test="crowdRefreshTime != null">
                crowd_refresh_time = #{crowdRefreshTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedTime != null">
                updated_time = #{updatedTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

</mapper>
