package com.xftech.cdp.domain.event.processor;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import com.xftech.cdp.domain.event.model.annotation.ProcessorRule;
import com.xftech.cdp.domain.event.model.config.CalculateConfig;
import com.xftech.cdp.domain.event.model.config.FieldConfig;
import com.xftech.cdp.domain.event.model.dto.Field;
import com.xftech.cdp.domain.event.model.dto.FieldDetail;
import com.xftech.cdp.domain.event.model.enums.DataProcessEnum;
import com.xftech.cdp.domain.event.model.enums.DataTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * 科学计算数据处理器
 * <AUTHOR>
 * @version $ CalculateProcessor, v 0.1 2024/11/19 14:26 snail Exp $
 */
@Slf4j
@Component
@ProcessorRule(processor = DataProcessEnum.CALCULATE)
public class CalculateProcessor extends AbstractDataProcessor{
    @Override
    public Field process(FieldDetail detail, FieldConfig fieldConfig, Map<String, Object> values) {
        CalculateConfig calculateConfig = (CalculateConfig) fieldConfig;
        Object resultVal = doCalculate(detail,calculateConfig,values);
        return new Field(detail.getTargetField(),resultVal);
    }

    /**
     * 执行科学计算
     * @param detail 字段映射配置信息
     * @param calculateConfig 科学计算配置信息
     * @param values 消息体信息
     * @return 计算后的结果
     */
    public Object doCalculate(FieldDetail detail,CalculateConfig calculateConfig,Map<String, Object> values){
        Object originVal = values.get(detail.getTargetField());
        if(Objects.isNull(originVal)){
            return null;
        }
        DataTypeEnum dataType = DataTypeEnum.getDataType(calculateConfig.getDataType());
        if(Objects.isNull(dataType)){
            log.error("can't find dataType,pls check this config {}",calculateConfig);
            return null;
        }

        Double convertedVal = Convert.convert(Double.class,originVal), joinVal = Convert.convert(Double.class,calculateConfig.getJoinValue());

        Double result = null;
        switch (calculateConfig.getOperate()){
            case "+":
                result = NumberUtil.add(convertedVal,joinVal);
                break;
            case "-":
                result = NumberUtil.sub(convertedVal,joinVal);
                break;
            case "*":
                result = NumberUtil.mul(convertedVal,joinVal);
                break;
            case "/":
                result = NumberUtil.div(convertedVal,joinVal);
                break;
        }

        return Convert.convert(dataType.getClazz(),result);
    }
}
