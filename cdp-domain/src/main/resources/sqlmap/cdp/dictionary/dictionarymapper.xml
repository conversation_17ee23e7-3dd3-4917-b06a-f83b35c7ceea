<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="dictionaryMapper">

    <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.dictionary.po.DictionaryDo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="dictType" column="dict_type" jdbcType="VARCHAR"/>
        <result property="dictTypeName" column="dict_type_name" jdbcType="VARCHAR"/>
        <result property="dictCode" column="dict_code" jdbcType="VARCHAR"/>
        <result property="dictValue" column="dict_value" jdbcType="VARCHAR"/>
        <result property="parentId" column="parent_id" jdbcType="VARCHAR"/>
        <result property="orderSeq" column="order_seq" jdbcType="INTEGER"/>
        <result property="dFlag" column="d_flag" jdbcType="SMALLINT"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,dict_type,dict_type_name,
        dict_code,dict_value,parent_id,
        order_seq,d_flag,created_time,
        updated_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from dictionary
        where id = #{id,jdbcType=BIGINT} and d_flag = 0
    </select>

    <select id="selectAllByBusinessType" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from dictionary
        where d_flag = 0 and find_in_set(#{businessType}, business_type)
    </select>

    <select id="selectAllBusinessType" parameterType="java.lang.String" resultMap="BaseResultMap">
        select GROUP_CONCAT(distinct business_type)
        from dictionary
    </select>

    <select id="selectByDictTypeListAndBusinessType" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from dictionary
        where d_flag = 0
        <if test="dictTypeList != null and dictTypeList.size>0">
            and dict_type in
            <foreach collection="dictTypeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and find_in_set(#{businessType}, business_type)
    </select>

    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.xftech.cdp.infra.repository.cdp.dictionary.po.DictionaryDo" useGeneratedKeys="true">
        insert into dictionary
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="dictType != null">dict_type,</if>
            <if test="dictTypeName != null">dict_type_name,</if>
            <if test="dictCode != null">dict_code,</if>
            <if test="dictValue != null">dict_value,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="orderSeq != null">order_seq,</if>
            <if test="dFlag != null">d_flag,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updatedTime != null">updated_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="dictType != null">#{dictType,jdbcType=INTEGER},</if>
            <if test="dictTypeName != null">#{dictTypeName,jdbcType=VARCHAR},</if>
            <if test="dictCode != null">#{dictCode,jdbcType=VARCHAR},</if>
            <if test="dictValue != null">#{dictValue,jdbcType=VARCHAR},</if>
            <if test="parentId != null">#{parentId,jdbcType=VARCHAR},</if>
            <if test="orderSeq != null">#{orderSeq,jdbcType=INTEGER},</if>
            <if test="dFlag != null">#{dFlag,jdbcType=SMALLINT},</if>
            <if test="createdTime != null">#{createdTime,jdbcType=TIMESTAMP},</if>
            <if test="updatedTime != null">#{updatedTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.xftech.cdp.infra.repository.cdp.dictionary.po.DictionaryDo">
        update dictionary
        <set>
            <if test="dictType != null">
                dict_type = #{dictType,jdbcType=INTEGER},
            </if>
            <if test="dictTypeName != null">
                dict_type_name = #{dictTypeName,jdbcType=VARCHAR},
            </if>
            <if test="dictCode != null">
                dict_code = #{dictCode,jdbcType=VARCHAR},
            </if>
            <if test="dictValue != null">
                dict_value = #{dictValue,jdbcType=VARCHAR},
            </if>
            <if test="parentId != null">
                parent_id = #{parentId,jdbcType=VARCHAR},
            </if>
            <if test="orderSeq != null">
                order_seq = #{orderSeq,jdbcType=INTEGER},
            </if>
            <if test="dFlag != null">
                d_flag = #{dFlag,jdbcType=SMALLINT},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedTime != null">
                updated_time = #{updatedTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

</mapper>
