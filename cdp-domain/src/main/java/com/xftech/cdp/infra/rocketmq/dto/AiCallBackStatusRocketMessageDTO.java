package com.xftech.cdp.infra.rocketmq.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ AiCallStatusRocketMessageDTO, v 0.1 2025/4/16 17:28 tianshuo.qiu Exp $
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AiCallBackStatusRocketMessageDTO {
    /**
     * 请求结果
     */
    private BatchResultInfo content;

    @Data
    public static class BatchResultInfo {
        /**
         * 请求批次编号
         */
        private String batchId;
        /**
         * 请求批次状态
         */
        private Integer batchStatus;
        /**
         * 批次结果
         */
        private List<JobResultInfo> batchResult;
        /**
         * 主要批次编号
         */
        private String mainBatchId;
        /**
         * 厂商编码
         */
        private String channelCode;
        /**
         * 厂商名称
         */
        private String channelName;
        /**
         * 策略编号
         */
        private String strategyId;
    }

    @Data
    public static class JobResultInfo {
        /**
         * 供应商拨打任务的唯一标识
         */
        private String operationId;
        /**
         * 请求批次编号
         */
        private String batchId;
        /**
         * 工单编号
         */
        private String jobId;
        /**
         * 用户号
         */
        private String userNo;
        /**
         * 联系人电话
         */
        private String phone;
        /**
         * 通话id
         */
        private String callId;
        /**
         * 产品类型
         */
        private String productType;
        /**
         * 外呼状态
         */
        private String callStatus;
        /**
         * 外呼结果码
         */
        private String callResult;
        /**
         * 外呼结果描述
         */
        private String callResultDesc;
        /**
         * 通话内容
         */
        private String callContent;
        /**
         * 通话时长
         */
        private int duration;
        /**
         * 振铃时长（单位：秒）
         */
        private int ringDuration;

        /**
         * 录音下载地址
         */
        private String recordingUrl;

        /**
         * 开始呼叫时间（格式：yyyy-MM-dd HH:mm:ss）
         */
        private String beginTime;

        /**
         * 振铃时间（格式：yyyy-MM-dd HH:mm:ss）
         */
        private String ringTime;

        /**
         * 接听时间（格式：yyyy-MM-dd HH:mm:ss）
         */
        private String answerTime;

        /**
         * 挂机时间（格式：yyyy-MM-dd HH:mm:ss）
         */
        private String hangupTime;

        /**
         * 结果类型
         */
        private int resultType;
        /**
         * 发送状态
         */
        private int sendStatus;
        /**
         * 角色
         */
        private int role;
        /**
         * 短信计费条数
         */
        private int smsCount;
        /**
         * 业务来源Code
         */
        private String bizSourceCode;
        /**
         * 线路组编码
         */
        private String lineGroupNo;
        /**
         * 呼叫结果明细
         */
        private CallResultDetail callResultDetail;

    }

    @Data
    public static class CallResultDetail {
        /**
         * 厂商呼叫结果码
         */
        private String thirdCallResult;

        /**
         * 厂商呼叫结果描述
         */
        private String thirdCallResultDesc;

        /**
         * 大模型呼叫结果码
         */
        private String aiCallResult;

        /**
         * 大模型呼叫结果描述
         */
        private String aiCallResultDesc;
    }
}