package com.xftech.cdp.domain.crowd.service;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.xftech.cdp.domain.crowd.model.dispatch.CrowdContext;
import com.xftech.cdp.domain.crowd.repository.CrowdPackRepository;
import com.xftech.cdp.infra.config.LogUtil;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPackDo;
import com.xftech.cdp.infra.utils.RedisUtils;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version v1.0 2024/10/14
 * @description CrowdCommonRepository
 */
@Slf4j
@Service
public class CrowdCommonService {

    private static final int HOUR = 3600;

    @Value("${cdp.marketing.crowd.user.cache.expire:4}")
    private Integer cacheExpire;

    @Resource
    private CrowdPackService crowdPackService;

    @Resource
    private CrowdPackRepository crowdPackRepository;

    @Resource
    private RedisUtils redisUtils;

    public Set<Long> crowdPackVerify(Long userNo, Set<Long> crowdPackIds) {
        Set<Long> hitCrowdPackIds = Sets.newHashSet();
        if (Objects.isNull(userNo) || CollectionUtils.isEmpty(crowdPackIds)) {
            return hitCrowdPackIds;
        }

        try {
            // 查询人群包详情
            Map<Long, CrowdContext> crowdContent = getCrowdContent(crowdPackIds);
            if (MapUtils.isEmpty(crowdContent)) {
                log.warn("CrowdCommonService 人群包不存在, userNo={} 人群包={}", userNo, crowdPackIds);
                return hitCrowdPackIds;
            }
            // 校验人群随机数
            List<CrowdPackDo> crowdPackDoList = crowdContent.values().stream().map(CrowdContext::getCrowdPack).collect(Collectors.toList());
            if (crowdPackDoList.stream().anyMatch(x -> Objects.equals(x.getFilterMethod(), 1)) && !crowdPackService.verifyCrowdPackByNewRandom(userNo, crowdPackDoList)) {
                log.info("CrowdCommonService 人群随机数校验不通过, userNo={} 人群包={}", userNo, crowdPackIds);
                return hitCrowdPackIds;
            }
            // 查询人群包所在的表
            List<Triple<Long, Long, List<String>>> triples = crowdPackService.getExecLogIdAndTablePairList(Lists.newArrayList(crowdPackIds), crowdContent);
            if (CollectionUtils.isEmpty(triples)) {
                log.warn("CrowdCommonService 未查询到人群包执行记录, userNo={} 人群包={}", userNo, crowdPackIds);
                return hitCrowdPackIds;
            }
            // 检查是否存在
            for (Triple<Long, Long, List<String>> triple : triples) {
                for (String tableName : triple.getRight()) {
                    if (hasUserRecord(triple.getLeft(), userNo, triple.getMiddle(), tableName)) {
                        hitCrowdPackIds.add(triple.getLeft());
                    }
                }
            }
        } catch (Exception e) {
            log.error("CrowdCommonService crowdPackVerify error", e);
        }

        log.info("CrowdCommonService crowdPackVerify userNo={} 人群包={} 命中人群包={}", userNo, crowdPackIds, hitCrowdPackIds);
        return hitCrowdPackIds;
    }


    private Map<Long, CrowdContext> getCrowdContent(Set<Long> crowdPackIds) {
        Map<Long, CrowdContext> crowdContextMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(crowdPackIds)) {
            return crowdContextMap;
        }
        try {
            crowdPackIds.forEach(crowdPackId -> {
                CrowdPackDo crowdPack = crowdPackRepository.selectById(crowdPackId);
                if (Objects.nonNull(crowdPack)) {
                    CrowdContext context = new CrowdContext();
                    context.setCrowdPack(crowdPack);
                    crowdContextMap.put(crowdPackId, context);
                }
            });
        } catch (Exception e) {
            log.error("CrowdCommonService getCrowdContent error", e);
        }
        return crowdContextMap;
    }

    private boolean hasUserRecord(Long crowdId, Long userNo, Long crowdExecLogId, String tableName) {
        // 从缓存查询
        String cacheKey = String.format("xyf-cdp:marketing:crowd:%d-%d", crowdId, userNo);
        String cacheValue = redisUtils.get(cacheKey);
        LogUtil.logDebug("CrowdCommonService hasUserRecord crowdId={} userNo={} cacheKey={} cacheValue={}", crowdId, userNo, cacheKey, cacheValue);
        if (StringUtils.equals(cacheValue, Boolean.TRUE.toString())) {
            return true;
        }
        if (StringUtils.equals(cacheValue, Boolean.FALSE.toString())) {
            return false;
        }

        // 从数据库查询
        Optional<CrowdDetailDo> crowdDetailDo = crowdPackService.hasUserRecord(crowdId, userNo, crowdExecLogId, tableName);
        boolean hasUserRecord = crowdDetailDo.isPresent();
        if (hasUserRecord) {
            redisUtils.set(cacheKey, Boolean.TRUE.toString(), cacheExpire * HOUR);
        } else {
            redisUtils.set(cacheKey, Boolean.FALSE.toString(), cacheExpire * HOUR);
        }
        LogUtil.logDebug("CrowdCommonService hasUserRecord crowdId={} userNo={} hasUserRecord={}}", crowdId, userNo, hasUserRecord);
        return hasUserRecord;
    }

}
