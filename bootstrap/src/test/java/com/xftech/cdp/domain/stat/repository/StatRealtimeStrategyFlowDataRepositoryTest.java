package com.xftech.cdp.domain.stat.repository;

import com.xftech.base.database.Page;
import com.xftech.cdp.BaseTest;
import com.xftech.cdp.domain.stat.entity.StatRealtimeStrategyFlowDataEntity;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;
import java.time.LocalDateTime;

public class StatRealtimeStrategyFlowDataRepositoryTest extends BaseTest {

    @Autowired
    private StatRealtimeStrategyFlowDataRepository strategyFlowDataRepository;

    @Test
    public void saveStatRealtimeStrategyFlowData() {
        StatRealtimeStrategyFlowDataEntity entity = new StatRealtimeStrategyFlowDataEntity();
        entity.setStrategyId(2L);
        entity.setBizDate(LocalDate.now());
        entity.setEventSum(100);
        entity.setUserSum(90);
        entity.setFilterEventNum(10);
        entity.setFilterRegTimNum(10);
        entity.setFilterCrowdNum(4);
        entity.setFilterLabelNum(5);
        entity.setFilterExcludeNum(1);
        entity.setPassNum(20);
        entity.setCreatedTime(LocalDateTime.now());
        entity.setUpdatedTime(LocalDateTime.now());
        strategyFlowDataRepository.saveStatRealtimeStrategyFlowData(entity);
    }

    @Test
    public void listByStrategyId() {
        Page<StatRealtimeStrategyFlowDataEntity> page = strategyFlowDataRepository.listByStrategyId(1L, 0, 10);
        logger.info("page={}", page);
    }
}