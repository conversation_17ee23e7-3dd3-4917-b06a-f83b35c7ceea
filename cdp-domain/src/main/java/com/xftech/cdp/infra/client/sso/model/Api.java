package com.xftech.cdp.infra.client.sso.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR> wanghu
 * @since : 2022/8/2 14:51:50
 */
@Setter
@Getter
public class Api {


    @JsonProperty("user")
    @JSONField(name = "user")
    private List<String> user;

    @JsonProperty("role")
    @JSONField(name = "role")
    private List<String> role;

    @JsonProperty("project")
    @JSONField(name = "project")
    private List<String> project;

    @JsonProperty("menu")
    @JSONField(name = "menu")
    private List<String> menu;

}
