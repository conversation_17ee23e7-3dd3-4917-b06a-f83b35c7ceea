package com.xftech.cdp.crowd;

import com.xftech.cdp.BaseTest;
import com.xftech.cdp.domain.crowd.model.dispatch.MinMaxId;
import com.xftech.cdp.domain.crowd.repository.CrowdDetailRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdDetailSubRepository;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * @<NAME_EMAIL>
 */
public class CrowdDetailRepositoryTest extends BaseTest {
    @Autowired
    private CrowdDetailRepository crowdDetailRepository;
    @Autowired
    private CrowdDetailSubRepository crowdDetailSubRepository;

    @Test
    public void saveBatch() {
        List<CrowdDetailDo> crowdDetailDoList = new ArrayList<>();
        CrowdDetailDo crowdDetailDo = new CrowdDetailDo();
        crowdDetailDo.setCrowdId(1L);
        crowdDetailDo.setApp("cxh");
        crowdDetailDo.setInnerApp("cxh");
        crowdDetailDoList.add(crowdDetailDo);
        crowdDetailRepository.saveBatch(crowdDetailDoList);
    }

    @Test
    public void clearData() {
        crowdDetailRepository.clearData(LocalDateTime.now(), null);
    }

    @Test
    public void testClearData() {
//        crowdDetailRepository.clearData(null, null, null);
    }

    @Test
    public void deleteByCrowdId() {
        crowdDetailRepository.deleteByCrowdId(1L);
    }

    @Test
    public void selectByIdPage() {
        List<CrowdDetailDo> crowdDetailDoList = crowdDetailRepository.selectByIdPage(1L, 10, 1L, 1L);
        System.out.println(crowdDetailDoList);
    }

    @Test
    public void selectBatchByCrowdMaxExecLogId() {
        List<CrowdDetailDo> crowdDetailDoList = crowdDetailRepository.selectBatchByCrowdMaxExecLogId("", 1L, 10L, 1L);
        System.out.println(crowdDetailDoList);

    }

    @Test
    public void batchSaveDo() {
        List<CrowdDetailDo> crowdDetailDoList = new ArrayList<>();
        CrowdDetailDo crowdDetailDo = new CrowdDetailDo();
        crowdDetailDo.setCrowdId(1L);
        crowdDetailDo.setApp("cxh");
        crowdDetailDo.setInnerApp("cxh");
        crowdDetailDoList.add(crowdDetailDo);
        crowdDetailSubRepository.batchSaveCrowdDetailSub(0, crowdDetailDoList);
    }

    @Test
    public void selectMinMaxIdByCrowdLogId() {
//        MinMaxId minMaxId = crowdDetailRepository.selectMinMaxIdByCrowdLogId(Constants.CROWD_DETAIL_STR);
//        System.out.println(minMaxId);
    }

    @Test
    public void deleteById() {
        crowdDetailRepository.deleteById(1L);
    }

    @Test
    public void updateBatchById() {
        List<CrowdDetailDo> crowdDetailDoList = new ArrayList<>();
        CrowdDetailDo crowdDetailDo = new CrowdDetailDo();
        crowdDetailDo.setCrowdId(1L);
        crowdDetailDo.setApp("cxh");
        crowdDetailDo.setInnerApp("cxh");
        crowdDetailDoList.add(crowdDetailDo);

        CrowdDetailDo crowdDetailDo2 = new CrowdDetailDo();
        crowdDetailDo2.setCrowdId(1L);
        crowdDetailDo2.setApp("cxh");
        crowdDetailDo2.setInnerApp("cxh");
        crowdDetailDoList.add(crowdDetailDo2);
        crowdDetailRepository.updateBatchById(crowdDetailDoList);
    }
}