package com.xftech.cdp.infra.pulsar.service;

import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSON;
import com.xftech.cdp.infra.pulsar.dto.RegisterEventDTO;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventMessageVO;

import java.util.Optional;

/**
 * @<NAME_EMAIL>
 * @date 2023-08-14 10:33
 */
public interface CdpPulsarService {

    /**
     * 新事件消息体转为BizEventMessageVO对象
     *
     * @param registerEvent 新注册消息体
     * @return BizEventMessageVO
     */
    default Optional<BizEventMessageVO> toBizEventMessageVO(RegisterEventDTO registerEvent) {
        return Optional.ofNullable(registerEvent).map(event -> {
            BizEventMessageVO bizEventMessageVO = new BizEventMessageVO();
            bizEventMessageVO.setBizEventType(CharSequenceUtil.format("{}-useractionlog", event.getType()));
            bizEventMessageVO.setCreditUserId(event.getAppUserId());
            bizEventMessageVO.setMobile(event.getMobile());
            bizEventMessageVO.setApp(event.getApp());
            bizEventMessageVO.setOs(event.getOs());
            bizEventMessageVO.setInnerApp(event.getInnerApp());
            bizEventMessageVO.setUtmSource(event.getUtmSource());
            bizEventMessageVO.setTriggerDatetime(event.getCreatedTime());
            bizEventMessageVO.setExtrData(new BizEventMessageVO.ExtrData(event.getCreatedTime(), event.getCurrentUtmSource(), event.getSourceType()));
            return bizEventMessageVO;
        });
    }

    default Optional<BizEventMessageVO> toBizEventMessageVO(String eventMessage) {
        return Optional.ofNullable(JSON.parseObject(eventMessage, BizEventMessageVO.class));
    }

    void consumer(String messageId, String eventMessage);
}
