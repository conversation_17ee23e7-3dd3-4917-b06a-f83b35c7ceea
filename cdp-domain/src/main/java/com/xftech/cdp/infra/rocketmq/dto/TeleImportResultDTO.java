package com.xftech.cdp.infra.rocketmq.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @date 2023/4/3 15:39
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TeleImportResultDTO {


    private String flowNo;


    private Long userType;


    private List<TeleUser> userList;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TeleUser {
        /**
         * 用户ID
         */
        private Long creditId;
        /**
         * 1-入库用户，2-排除用户
         */
        private Integer status;
        /**
         * 入库时间
         */
        private String execTime;
    }
}
