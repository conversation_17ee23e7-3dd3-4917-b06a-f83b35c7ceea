package com.xftech.cdp.infra.client.coupon.model.req;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class CouponActivityListReq {
    /**
     * 活动id
     */
    @JsonProperty("activity_id")
    @JSONField(name = "activity_id")
    private Long activityId;
    /**
     * 活动名称
     */
    @JsonProperty("activity_name")
    @JSONField(name = "activity_name")
    private String activityName;
    /**
     * 券类型 1:借款免息券 2:还款立减金，3:限时提额券，4.拉卡拉聚合支付 ,5:x天免息券
     */
    @JsonProperty("coupon_type")
    @JSONField(name = "coupon_type")
    private List<Integer> couponType;
    /**
     * 券模版id
     */
    @JsonProperty("coupon_id")
    @JSONField(name = "coupon_id")
    private Long couponId;
    /**
     * 券模版名称
     */
    @JsonProperty("coupon_name")
    @JSONField(name = "coupon_name")
    private String couponName;

    /**
     * 类型： 1：金融券（默认）， 2:消费券（新增）
     */
    @JsonProperty("type")
    @JSONField(name = "type")
    private Integer type;

    /**
     * 当前页
     */
    @JsonProperty("page")
    @JSONField(name = "page")
    private Integer page;
    /**
     * 每页数量
     */
    @JsonProperty("page_size")
    @JSONField(name = "page_size")
    private Integer pageSize;
}
