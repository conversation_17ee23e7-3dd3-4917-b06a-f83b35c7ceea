package com.xftech.cdp.adapter.risk;

import com.xftech.cdp.application.RiskHandle;
import com.xftech.cdp.infra.constant.XxlJobConstants;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/5/24
 */
@Slf4j
@Component
public class RiskDispatch {

    private final RiskHandle riskHandle;

    public RiskDispatch(RiskHandle riskHandle) {
        this.riskHandle = riskHandle;
    }


    @XxlJob(XxlJobConstants.RISK_FORECAST_NEW)
    public ReturnT<String> RiskDispatchNew(String refreshDate) {
        log.info("RiskDispatch begin refreshDate:{}", refreshDate);
        XxlJobLogger.log("RiskDispatch begin refreshDate:{}", refreshDate);

        LocalDate now = LocalDate.now();
        List<String> splits = Arrays.asList(refreshDate.split(","));
        int startDay = Integer.parseInt(splits.get(0));
        int endDay = Integer.parseInt(splits.get(1));
        try {
            LocalDate firstDay = now.with(TemporalAdjusters.firstDayOfMonth());
            for (int i = startDay; i <= endDay; i++) {
                LocalDate date = firstDay.plusDays(i - 1);
                log.info("风险模型分开始调用日期:{},", date);
                riskHandle.execute(date);
                log.info("风险模型分结束调用日期:{},", date);
            }
        } catch (Exception e) {
            log.warn("RiskDispatch execute error param:{}, e: ", refreshDate, e);
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    // 删除不用
    @Deprecated
    @XxlJob(XxlJobConstants.RISK_FORECAST)
    public ReturnT<String> RiskDispatch(String refreshDate) {

        log.info("RiskDispatch begin refreshDate:{}", refreshDate);
        XxlJobLogger.log("RiskDispatch begin refreshDate:{}", refreshDate);

        LocalDate date = refreshDate.length() > 0 ? LocalDate.parse(refreshDate) : null;

        try {
            riskHandle.execute(date);
        } catch (Exception e) {
            log.warn("RiskDispatch execute error param:{}, e: ", refreshDate, e);
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }

        return ReturnT.SUCCESS;
    }
}
