/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.feign.model.response;

import com.xftech.cdp.feign.contants.FeignConstants;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.poi.ss.formula.functions.T;

/**
 *
 * <AUTHOR>
 * @version $ GoodResponse, v 0.1 2024/5/7 10:19 lingang.han Exp $
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GoodsResponse<T> {

    private Boolean suc;

    private String code;

    private String message;

    private T data;

    public boolean isSuccess(){
        return suc;
    }
}