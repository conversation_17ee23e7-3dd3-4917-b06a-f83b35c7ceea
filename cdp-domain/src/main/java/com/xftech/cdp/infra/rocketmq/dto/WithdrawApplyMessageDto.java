package com.xftech.cdp.infra.rocketmq.dto;

/**
 * 冻额审批策略结果通知：
 * <AUTHOR>
 * @version $ WithdrawApplyMessageDto author:guangchun.wang, v 0.1 2025/6/11 15:02 tianshuo.qiu Exp $
 */

import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class WithdrawApplyMessageDto {
    /**
     * 结果,pass,reject,
     */
    @JSONField(name = "check_result")
    @JsonProperty("check_result")
    private String checkResult;
    /**
     * 进件状态    String 是     ACW,ACING,ERROR,EXPIRED,PS，RJ,NONE
     */
    @JSONField(name = "status")
    @JsonProperty("status")
    private String status;

    /**
     * app
     */
    @JSONField(name = "app")
    @JsonProperty("app")
    private String app;
    /**
     * inner_app 目前线上还没有改字段，待开发加入
     */
    @JSONField(name = "inner_app")
    @JsonProperty("inner_app")
    private String innerApp;
    /**
     * 客户号
     */
    @JSONField(name = "cust_no")
    @JsonProperty("cust_no")
    private String custNo;
    /**
     * orderNumber
     */
    @JSONField(name = "order_number")
    @JsonProperty("order_number")
    private String orderNumber;
    /**
     * orderNumber
     */
    @JSONField(name = "biz_flow_number")
    @JsonProperty("biz_flow_number")
    private String bizFlowNumber;
    /**
     * 用户号
     */
    @JSONField(name = "user_no")
    @JsonProperty("user_no")
    private String userNo;

    /**
     * 端码
     */
    @JsonProperty("cnl_pd_code")
    @JSONField(name = "cnl_pd_code")
    private String cnlPdCode;
    /**
     * 端到端流水号
     */
    @JsonProperty("cnl_no")
    @JSONField(name = "cnl_no")
    private String cnlNo;
    /**
     * 端到端事件码
     */
    @JsonProperty("cnl_ev_code")
    @JSONField(name = "cnl_ev_code")
    private String cnlEvCode;
    /**
     * 业务产品码
     */
    @JsonProperty("biz_pd_code")
    @JSONField(name = "biz_pd_code")
    private String bizPdCode;
    /**
     * 业务事件码
     */
    @JsonProperty("biz_ev_code")
    @JSONField(name = "biz_ev_code")
    private String bizEvCode;



    /**
     * 决策结果字段
     */
    @JsonProperty("decision_result")
    @JSONField(name = "decision_result")
    JSONObject decisionResult;

    /**
     * 事件触发时间 如：2025-06-05 14:15:59
     */
    @JsonProperty("event_datetime")
    @JSONField(name = "event_datetime")
    String eventDatetime;
    /**
     * 事件触发时间搓
     */
    @JsonProperty("event_timestamp")
    @JSONField(name = "event_timestamp")
    Long eventTimestamp;
    /**
     * 消息发送时间 如：2025-06-05 14:15:59
     */

    @JsonProperty("trigger_datetime")
    @JSONField(name = "trigger_datetime")
    String  triggerdatetime;
    /**
     * 消息发送时间搓
     */
    @JsonProperty("trigger_timestamp")
    @JSONField(name = "trigger_timestamp")
    Long triggerTimestamp;

}
