package com.xftech.cdp.domain.touch.model;

import lombok.Data;
import java.util.List;
import java.util.Map;

/**
 * 触达请求统一模型
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-23
 */
@Data
public class TouchRequest {
    
    // ========== 基础信息 ==========
    /**
     * 请求唯一标识
     */
    private String requestId;
    
    /**
     * 触达类型：标识触达方式
     */
    private TouchType touchType;
    
    /**
     * 触达模式：SINGLE(单用户), BATCH(批量)
     */
    private TouchMode touchMode;
    
    /**
     * 请求时间戳
     */
    private Long timestamp;
    
    /**
     * 链路追踪ID
     */
    private String traceId;
    
    // ========== 策略信息 ==========
    /**
     * 策略ID
     */
    private Long strategyId;
    
    /**
     * 策略执行ID
     */
    private String strategyExecId;
    
    /**
     * 策略分组ID
     */
    private Long strategyGroupId;
    
    /**
     * 策略分组名称
     */
    private String strategyGroupName;
    
    /**
     * 策略渠道ID
     */
    private Long strategyChannelId;
    
    /**
     * 触达渠道：SMS, VOICE, PUSH, COUPON等
     */
    private TouchChannel channel;
    
    /**
     * 模板ID
     */
    private String templateId;
    
    // ========== 用户信息 ==========
    /**
     * 单用户信息（单用户模式）
     */
    private TouchUserInfo userInfo;
    
    /**
     * 用户列表（批量模式）
     */
    private List<TouchUserInfo> userList;
    
    // ========== 业务信息 ==========
    /**
     * 业务事件类型（T0触达必填）
     */
    private String bizEventType;
    
    /**
     * 业务事件数据（T0触达）
     */
    private Map<String, Object> bizEventData;
    
    /**
     * 模板参数
     */
    private Map<String, Object> templateParams;
    
    /**
     * 扩展数据
     */
    private Map<String, Object> extData;
    
    // ========== 引擎信息 ==========
    /**
     * 引擎编码（引擎触达）
     */
    private String engineCode;
    
    /**
     * 引擎分组名称（引擎触达）
     */
    private String engineGroupName;
    
    /**
     * 引擎详细信息（引擎触达）
     */
    private Map<String, Object> engineDetail;
    
    // ========== 触达配置 ==========
    /**
     * 触达配置
     */
    private TouchConfig touchConfig;
    
    /**
     * 流控配置
     */
    private FlowControlConfig flowControlConfig;
    
    // ========== 批量处理信息 ==========
    /**
     * 批量处理信息（批量模式）
     */
    private BatchInfo batchInfo;
    
    /**
     * 判断是否为单用户模式
     */
    public boolean isSingleMode() {
        return TouchMode.SINGLE.equals(touchMode);
    }
    
    /**
     * 判断是否为批量模式
     */
    public boolean isBatchMode() {
        return TouchMode.BATCH.equals(touchMode);
    }
    
    /**
     * 判断是否为T0触达
     */
    public boolean isT0Touch() {
        return TouchType.T0_NORMAL.equals(touchType) || TouchType.T0_ENGINE.equals(touchType);
    }
    
    /**
     * 判断是否为离线触达
     */
    public boolean isOfflineTouch() {
        return TouchType.OFFLINE_NORMAL.equals(touchType) || TouchType.OFFLINE_ENGINE.equals(touchType);
    }
    
    /**
     * 判断是否为引擎触达
     */
    public boolean isEngineTouch() {
        return TouchType.T0_ENGINE.equals(touchType) || TouchType.OFFLINE_ENGINE.equals(touchType);
    }
    
    /**
     * 判断是否为普通触达
     */
    public boolean isNormalTouch() {
        return TouchType.T0_NORMAL.equals(touchType) || TouchType.OFFLINE_NORMAL.equals(touchType);
    }
    
    /**
     * 获取用户数量
     */
    public int getUserCount() {
        if (isSingleMode()) {
            return userInfo != null ? 1 : 0;
        } else {
            return userList != null ? userList.size() : 0;
        }
    }
    
    /**
     * 验证请求参数
     */
    public void validate() {
        if (requestId == null || requestId.trim().isEmpty()) {
            throw new IllegalArgumentException("requestId不能为空");
        }
        if (touchType == null) {
            throw new IllegalArgumentException("touchType不能为空");
        }
        if (touchMode == null) {
            throw new IllegalArgumentException("touchMode不能为空");
        }
        if (strategyId == null) {
            throw new IllegalArgumentException("strategyId不能为空");
        }
        if (channel == null) {
            throw new IllegalArgumentException("channel不能为空");
        }
        
        // 验证用户信息
        if (isSingleMode()) {
            if (userInfo == null) {
                throw new IllegalArgumentException("单用户模式下userInfo不能为空");
            }
            userInfo.validate();
        } else {
            if (userList == null || userList.isEmpty()) {
                throw new IllegalArgumentException("批量模式下userList不能为空");
            }
            for (TouchUserInfo user : userList) {
                user.validate();
            }
        }
        
        // T0触达必须有业务事件类型
        if (isT0Touch() && (bizEventType == null || bizEventType.trim().isEmpty())) {
            throw new IllegalArgumentException("T0触达必须指定bizEventType");
        }
    }
}
