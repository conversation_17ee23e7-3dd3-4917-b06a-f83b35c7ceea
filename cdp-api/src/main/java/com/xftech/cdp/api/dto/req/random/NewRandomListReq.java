package com.xftech.cdp.api.dto.req.random;

import com.xftech.cdp.api.dto.base.PageRequestDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * @<NAME_EMAIL>
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class NewRandomListReq extends PageRequestDto {
    @ApiModelProperty(value = "归属业务线")
    private String bizNames;
    @ApiModelProperty(value = "场景key")
    private String bizKey;
    @ApiModelProperty(value = "场景名称")
    private String testName;
    // bizKey 和 bizKeyAccurate 2选一
    @ApiModelProperty(value = "精确匹配场景key")
    private String bizKeyAccurate;

}
