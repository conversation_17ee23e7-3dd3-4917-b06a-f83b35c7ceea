/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.client.telemarketing.model.resp;

import lombok.Data;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ TelePolicyConfigListResp, v 0.1 2024/1/12 15:13 ****.**** Exp $
 */
@Data
public class TelePolicyConfigListResp extends BaseNewTeleResp<TelePolicyConfigListResp.ResponseData> {
    @Data
    public static class ResponseData {
        private Integer currentPage;
        private List<TelePolicyConfigListResp.ResponseData.Item> list;
        private Integer pageSize;
        private Integer total;
        private Integer totalPage;
        
        @Data
        public static class Item {
            private String policyName;
            private String createBy;
            private String createdTime;
            private Integer id;
            private Integer status;     //状态1:启用,0:禁用
            private String type;
            private String updateBy;
            private String updatedTime;
            private Integer priority;
        }
    }
}