package com.xinfei.touch.api.controller;

import com.xinfei.touch.domain.model.unified.TouchRequest;
import com.xinfei.touch.domain.model.unified.TouchResponse;
import com.xinfei.touch.domain.service.UnifiedTouchService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 统一触达API控制器
 * 提供统一的触达服务REST接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-23
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/touch")
@RequiredArgsConstructor
public class UnifiedTouchController {
    
    private final UnifiedTouchService unifiedTouchService;
    
    /**
     * 同步触达处理
     * 
     * @param request 触达请求
     * @return 触达响应
     */
    @PostMapping("/process")
    public TouchResponse processTouch(@RequestBody TouchRequest request) {
        log.info("收到同步触达请求: requestId={}, touchType={}, touchMode={}", 
                request.getRequestId(), request.getTouchType(), request.getTouchMode());
        
        try {
            return unifiedTouchService.processTouch(request);
        } catch (Exception e) {
            log.error("同步触达处理失败: requestId={}", request.getRequestId(), e);
            return TouchResponse.failed(request.getRequestId(), "API_ERROR", e.getMessage());
        }
    }
    
    /**
     * 异步触达处理
     * 
     * @param request 触达请求
     * @return 触达响应（包含批次号）
     */
    @PostMapping("/process-async")
    public TouchResponse processTouchAsync(@RequestBody TouchRequest request) {
        log.info("收到异步触达请求: requestId={}, touchType={}, touchMode={}", 
                request.getRequestId(), request.getTouchType(), request.getTouchMode());
        
        try {
            return unifiedTouchService.processTouchAsync(request);
        } catch (Exception e) {
            log.error("异步触达启动失败: requestId={}", request.getRequestId(), e);
            return TouchResponse.failed(request.getRequestId(), "ASYNC_API_ERROR", e.getMessage());
        }
    }
    
    /**
     * 查询触达进度
     * 
     * @param requestId 请求ID
     * @return 触达响应（包含进度信息）
     */
    @GetMapping("/progress/{requestId}")
    public TouchResponse queryProgress(@PathVariable String requestId) {
        log.debug("查询触达进度: requestId={}", requestId);
        
        try {
            return unifiedTouchService.queryTouchProgress(requestId);
        } catch (Exception e) {
            log.error("查询触达进度失败: requestId={}", requestId, e);
            return TouchResponse.failed(requestId, "QUERY_ERROR", e.getMessage());
        }
    }
    
    /**
     * 取消触达处理
     * 
     * @param requestId 请求ID
     * @return 取消结果
     */
    @PostMapping("/cancel/{requestId}")
    public ApiResult<Boolean> cancelTouch(@PathVariable String requestId) {
        log.info("收到取消触达请求: requestId={}", requestId);
        
        try {
            boolean result = unifiedTouchService.cancelTouch(requestId);
            return ApiResult.success(result);
        } catch (Exception e) {
            log.error("取消触达失败: requestId={}", requestId, e);
            return ApiResult.error("CANCEL_ERROR", e.getMessage());
        }
    }
    
    /**
     * API响应结果封装
     */
    public static class ApiResult<T> {
        private boolean success;
        private String errorCode;
        private String errorMessage;
        private T data;
        
        public static <T> ApiResult<T> success(T data) {
            ApiResult<T> result = new ApiResult<>();
            result.success = true;
            result.data = data;
            return result;
        }
        
        public static <T> ApiResult<T> error(String errorCode, String errorMessage) {
            ApiResult<T> result = new ApiResult<>();
            result.success = false;
            result.errorCode = errorCode;
            result.errorMessage = errorMessage;
            return result;
        }
        
        // Getters and Setters
        public boolean isSuccess() {
            return success;
        }
        
        public void setSuccess(boolean success) {
            this.success = success;
        }
        
        public String getErrorCode() {
            return errorCode;
        }
        
        public void setErrorCode(String errorCode) {
            this.errorCode = errorCode;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
        
        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }
        
        public T getData() {
            return data;
        }
        
        public void setData(T data) {
            this.data = data;
        }
    }
}
