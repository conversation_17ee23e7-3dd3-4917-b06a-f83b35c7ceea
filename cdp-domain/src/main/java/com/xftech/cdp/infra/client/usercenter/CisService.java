/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.infra.client.usercenter;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.cat.proxy.Tracer;
import com.google.common.collect.Lists;
import com.xftech.cdp.infra.client.increaseamt.model.dto.UserInfoDto;
import com.xftech.cdp.infra.client.usercenter.config.CisConfig;
import com.xftech.cdp.infra.client.usercenter.model.*;
import com.xftech.cdp.infra.config.AppConfigService;
import com.xftech.cdp.infra.config.LogUtil;
import com.xftech.cdp.infra.constant.Constants;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.utils.DateUtil;
import com.xftech.cdp.infra.utils.HashUtils;
import com.xftech.cdp.infra.utils.JsonUtil;

import com.xyf.cis.query.facade.AccountQueryFacade;
import com.xyf.cis.query.facade.BatchQueryFacade;
import com.xyf.cis.query.facade.dto.request.QueryAccountNoRequest;
import com.xyf.cis.query.facade.dto.standard.request.CustNoListRequest;
import com.xyf.cis.query.facade.dto.standard.request.QueryMobilesByUserNosRequest;
import com.xyf.cis.query.facade.dto.standard.response.AccountNoDTO;
import com.xyf.cis.query.facade.dto.standard.response.IdNoBaseDTO;
import com.xyf.cis.query.facade.dto.standard.response.MobileDTO;
import com.xyf.cis.query.facade.dto.standard.response.RegisterInfoDTO;
import com.xyf.user.facade.common.model.BaseResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.*;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * @version $ CisService, v 0.1 2023/11/14 16:38 yye.xu Exp $
 */

@Slf4j
@Service
@AllArgsConstructor
public class CisService {
    private CisConfig cisConfig;
    private RestTemplate restTemplate;
    private AppConfigService appConfigService;
    private BatchQueryFacade batchQueryFacade;
    private AccountQueryFacade accountQueryFacade;

    public boolean isQueryUserInfoByCis() {
        return cisConfig.isQueryCisUserSwitch();
    }

    public static Map<String, Object> getHeader() {
        Map<String, Object> header = new HashMap<>();
        header.put("TRACE_ID", IdUtil.fastSimpleUUID());
        header.put("upstreamService", Constants.APP_NAME);
        header.put("requestTime", DateFormatUtils.format(new Date(),
                DateUtil.NOMAL_DATE_FORMAT));
        return header;
    }

    public BaseCisResp<UserNoByMobileAndApp.RespDto> queryUserNoByMobileAndApp(UserNoByMobileAndApp.Req req) {
        HttpEntity httpEntity = new HttpEntity(getHeader());

        if (req == null || StringUtils.isEmpty(req.getApp())
                || StringUtils.isEmpty(req.getMobile())) {
            log.warn("queryUserNoByMobileAndApp params error, request:{}", JsonUtil.toJson(req));
            return null;
        }
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(cisConfig.getQueryUserNoByMobileAndAppUrl());
        builder.queryParam("mobile", req.getMobile());
        builder.queryParam("app", req.getApp());
        String url = builder.toUriString();
        int retryTimes = 3;
        do {
            try {
                ResponseEntity<String> responseEntity = restTemplate.exchange(
                        url,
                        HttpMethod.GET,
                        httpEntity,
                        String.class);
                log.info("cis service named queryUserNoByMobileAndApp, url:{}, request:{}, resp:{}", cisConfig.getQueryUserNoByMobileAndAppUrl(), JsonUtil.toJson(req), responseEntity.getBody());
                BaseCisResp<UserNoByMobileAndApp.RespDto> resp = JSON.parseObject(responseEntity.getBody(),
                        new TypeReference<BaseCisResp<UserNoByMobileAndApp.RespDto>>() {
                        });
                if (resp == null || !resp.isCodeSucceed()) {
                    log.error("cis service named queryUserNoByMobileAndApp error, url:{}, request:{}, resp:{}", cisConfig.getQueryUserNoByMobileAndAppUrl(), JsonUtil.toJson(req), responseEntity.getBody());
                }
                return resp;
            } catch (Exception ex) {
                retryTimes--;
                Tracer.logEvent("CisError", "queryUserNoByMobileAndApp");
                log.error("cis service named queryUserNoByMobileAndApp exception, url={}, request={}, 剩余重试次数:{}", cisConfig.getQueryUserNoByMobileAndAppUrl(), JsonUtil.toJson(req), retryTimes, ex);
            }
        } while (retryTimes > 0);
        return null;
    }

    public BaseCisResp<RegisterInfoByUserNo.RespDto> queryRegisterInfoByUserNo(Long userNo) {
        if (userNo == null || userNo < 1L) {
            return null;
        }
        HttpEntity httpEntity = new HttpEntity(getHeader());

        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(cisConfig.getQueryRegisterInfoByUserNo());
        builder.queryParam("userNo", userNo);
        String url = builder.toUriString();

        int retryTimes = 3;
        do {
            try {
                ResponseEntity<String> responseEntity = restTemplate.exchange(
                        url,
                        HttpMethod.GET,
                        httpEntity,
                        String.class);
                BaseCisResp<RegisterInfoByUserNo.RespDto> resp = JSON.parseObject(responseEntity.getBody(),
                        new TypeReference<BaseCisResp<RegisterInfoByUserNo.RespDto>>() {
                        });
                log.info("cis service [queryRegisterInfoByUserNo], url:{}, userNo:{}, final-url:{}, request:{}, resp:{}", cisConfig.getQueryRegisterInfoByUserNo(), userNo, url, JsonUtil.toJson(httpEntity), responseEntity.getBody());
                if (resp == null || !resp.isCodeSucceed()) {
                    log.error("cis service [queryRegisterInfoByUserNo] error, url:{}, userNo:{}, final-url:{}, resp:{}", cisConfig.getQueryRegisterInfoByUserNo(), userNo, url, responseEntity.getBody());
                }
                return resp;
            } catch (Exception ex) {
                retryTimes--;
                Tracer.logEvent("CisError", "queryRegisterInfoByUserNo");
                log.error("cis service [queryRegisterInfoByUserNo] exception, url:{}, userNo:{},  final-url:{}, 剩余重试次数:{}", cisConfig.getQueryRegisterInfoByUserNo(), userNo, url, retryTimes, ex);
            }
        } while (retryTimes > 0);
        return null;
    }

    public BaseCisResp<MobileByUserNo.RespDto> queryMobileByUserNo(Long userNo) {
        if (userNo == null) {
            return null;
        }
        HttpEntity httpEntity = new HttpEntity(getHeader());
        try {
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(cisConfig.getQueryMobileByUserNo());
            builder.queryParam("userNo", userNo);
            String url = builder.toUriString();

            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    httpEntity,
                    String.class);
            BaseCisResp<MobileByUserNo.RespDto> resp = JSON.parseObject(responseEntity.getBody(),
                    new TypeReference<BaseCisResp<MobileByUserNo.RespDto>>() {
                    });
            LogUtil.logDebug("CisService queryMobileByUserNo url={} request={} response={}", url, JSONObject.toJSONString(httpEntity), JSONObject.toJSONString(resp));
            if (resp == null || !resp.isCodeSucceed()) {
                log.error("cis service named queryMobileByUserNo error, url:{}, resp:{}", url, responseEntity.getBody());
            }
            return resp;
        } catch (Exception ex) {
            log.error("cis service named queryMobileByUserNo exception, url:{}, userNo:{}", cisConfig.getQueryMobileByUserNo(), userNo, ex);
        }
        return null;
    }

    public BaseCisResp<List<RegisterInfoByUserNo.RespDto>> queryBatchUserByUserNo(List<Long> userNos){
        if (CollectionUtils.isEmpty(userNos)) {
            return null;
        }
        HttpEntity httpEntity = new HttpEntity(getHeader());
        String userNosStr = userNos.stream()
                .map(String::valueOf)
                .collect(Collectors.joining(","));
        try {
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(cisConfig.getQueryBatchUserByUserNo());
            builder.queryParam("userNos", userNosStr);
            String url = builder.toUriString();

            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    httpEntity,
                    String.class);
            BaseCisResp<List<RegisterInfoByUserNo.RespDto>> resp = JSON.parseObject(responseEntity.getBody(),
                    new TypeReference<BaseCisResp<List<RegisterInfoByUserNo.RespDto>>>() {
                    });
            if (resp == null || !resp.isCodeSucceed()) {
                log.error("cis service named queryBatchUserByUserNo error, url:{}, resp:{}", url, responseEntity.getBody());
            }
            return resp;
        } catch (Exception ex) {
            log.error("cis service named queryBatchUserByUserNo exception, url:{}, userNo:{}", cisConfig.getQueryBatchUserByUserNo(), userNosStr, ex);
        }
        return null;
    }

    public List<RegisterInfoDTO> queryBatchUserByUserNos(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return new ArrayList<>(0);
        }
        List<RegisterInfoDTO> result = new ArrayList<>();
        QueryMobilesByUserNosRequest request = new QueryMobilesByUserNosRequest();
        request.setUserNos(userIds);
        request.setUpstreamService(Constants.APP_NAME);
        try {
            BaseResponse<List<RegisterInfoDTO>> response = batchQueryFacade.queryBatchUserByUserNoV2(request);
            log.info("batchQueryFacade.queryBatchUserByUserNo, request:{}, response:{}", JsonUtil.toJson(request), JsonUtil.toJson(response));
            if (response.isSuccess() && !CollectionUtils.isEmpty(response.getData())) {
                result.addAll(response.getData());
            } else {
                log.warn("batchQueryFacade.queryBatchUserByUserNo,查询信息有误,userIds:{}, resp:{}", JsonUtil.toJson(userIds), JsonUtil.toJson(response));
            }
        } catch (Exception ex) {
            log.error("batchQueryFacade.queryBatchUserByUserNo, userIds:{}, error:{}", JsonUtil.toJson(userIds), ex, ex);
        }
        return result;
    }

    private boolean checkIncreaseAmtParams(RegisterInfoDTO registerInfoDTO) {
        if (StringUtils.isEmpty(registerInfoDTO.getCustNo())) {
            return false;
        }
        if (Objects.isNull(registerInfoDTO.getUserNo())) {
            return false;
        }
        if (StringUtils.isEmpty(registerInfoDTO.getMobile())) {
            return false;
        }
        if (StringUtils.isEmpty(registerInfoDTO.getCustName())) {
            return false;
        }
        return true;
    }

    /**
     * 先根据userNo查询custNo，再根据custNo查询IdCardNo
     */
    public List<UserInfoDto>  getUserInfoDtos(List<CrowdDetailDo> batch) {
        if (org.springframework.util.CollectionUtils.isEmpty(batch)) {
            return new ArrayList<>(0);
        }
        List<UserInfoDto> userInfoDtos = new ArrayList<>(batch.size());
        int pageSize = appConfigService.getBatchQueryUserPageSize();
        Lists.partition(batch, pageSize).forEach(items -> {
            List<Long> userIds = items.stream().map(CrowdDetailDo::getUserId)
                    .filter(Objects::nonNull).collect(Collectors.toList());
            List<RegisterInfoDTO> registerInfos = this.queryBatchUserByUserNos(userIds);
            List<RegisterInfoDTO> validRegisterInfos = registerInfos.stream().filter(this::checkIncreaseAmtParams)
                    .collect(Collectors.toList());
            List<String> custNos = validRegisterInfos.stream().map(RegisterInfoDTO::getCustNo).collect(Collectors.toList());
            List<IdNoBaseDTO> idNoBaseDTOs = this.queryBatchIdNoByCustNo(custNos);
            if (!org.springframework.util.CollectionUtils.isEmpty(idNoBaseDTOs)) {
                for (IdNoBaseDTO item : idNoBaseDTOs) {
                    if (StringUtils.isNotEmpty(item.getIdNo())) {
                        RegisterInfoDTO registerInfoDTO = validRegisterInfos.stream()
                                .filter(x -> x.getCustNo().equals(item.getCustNo())).findFirst().orElse(null);
                        if (registerInfoDTO == null) {
                            continue;
                        }
                        UserInfoDto userInfoDto = new UserInfoDto();
                        userInfoDto.setUserId(registerInfoDTO.getUserNo());
                        userInfoDto.setName(registerInfoDTO.getCustName());
                        userInfoDto.setId_card_number(item.getIdNo());
                        userInfoDto.setCust_no(item.getCustNo());
                        userInfoDto.setUser_no(registerInfoDTO.getUserNo().toString());
                        userInfoDto.setMobile(registerInfoDTO.getMobile());
                        userInfoDto.setOldUserNo(registerInfoDTO.getOldUserNo());
                        userInfoDtos.add(userInfoDto);
                    }
                }
            }
        });
        return userInfoDtos;
    }

    public List<IdNoBaseDTO> queryBatchIdNoByCustNo(List<String> custNos) {
        if (CollectionUtils.isEmpty(custNos)) {
            return new ArrayList<>(0);
        }
        List<IdNoBaseDTO> result = new ArrayList<>();
        CustNoListRequest request = new CustNoListRequest();
        request.setCustNoList(custNos);
        request.setUpstreamService(Constants.APP_NAME);
        try {
            BaseResponse<List<IdNoBaseDTO>> response = batchQueryFacade.queryBatchIdNoByCustNoV2(request);
            log.info("batchQueryFacade.queryBatchIdNoByCustNo, request:{}, response:{}", JsonUtil.toJson(request), JsonUtil.toJson(response));
            if (response.isSuccess() && !CollectionUtils.isEmpty(response.getData())) {
                result.addAll(response.getData());
            } else {
                log.warn("batchQueryFacade.queryBatchIdNoByCustNo,查询信息有误,custNos:{}, resp:{}", JsonUtil.toJson(custNos), JsonUtil.toJson(response));
            }
        } catch (Exception ex) {
            log.error("batchQueryFacade.queryBatchIdNoByCustNo, userIds:{}, custNos:{}", JsonUtil.toJson(custNos), ex, ex);
        }
        return result;
    }

    // 查询用户手机号， 接口限制最大不超过200
    public List<MobileDTO> batchQueryMobileByUserNos(List<Long> userNos) {
        if (CollectionUtils.isEmpty(userNos)) {
            return new ArrayList<>(0);
        }
        log.info("查询用户手机号, 数量:{}", userNos.size());
        List<MobileDTO> result = new ArrayList<>();
        try {
            QueryMobilesByUserNosRequest queryMobilesByUserNosRequest = new QueryMobilesByUserNosRequest();
            queryMobilesByUserNosRequest.setUserNos(userNos);
            BaseResponse<List<MobileDTO>> response = batchQueryFacade.batchQueryMobileByUserNos(queryMobilesByUserNosRequest);
            log.info("batchQueryFacade.batchQueryMobileByUserNos, request:{}, response:{}", JsonUtil.toJson(queryMobilesByUserNosRequest), JsonUtil.toJson(response));
            if (response.isSuccess() && !CollectionUtils.isEmpty(response.getData())) {
                result.addAll(response.getData());
            } else {
                log.warn("batchQueryFacade.batchQueryMobileByUserNos,查询信息有误,userNos:{}, resp:{}", JsonUtil.toJson(userNos), JsonUtil.toJson(response));
            }
        } catch (Exception ex) {
            log.error("batchQueryFacade.batchQueryMobileByUserNos, userIds:{}, custNos:{}", JsonUtil.toJson(userNos), ex, ex);
        }
        return result;
    }

    /**
     * 查询账户信息
     * @param app
     * @param custNo
     * @return
     */
    private String queryAccountNo(String app, String custNo) {
        if (StringUtils.isAnyBlank(app, custNo)) {
            return null;
        }
        try {
            QueryAccountNoRequest request = new QueryAccountNoRequest();
            request.setApp(app);
            request.setCustNo(custNo);
            BaseResponse<AccountNoDTO> response = accountQueryFacade.queryAccountNo(request);
            log.info("CisService queryAccountNo request={} response={}", JSONObject.toJSONString(request), JSONObject.toJSONString(response));
            if (response.isSuccess() && response.getData() != null) {
                return response.getData().getAccountNo();
            }
        } catch (Exception e) {
            log.error("CisService queryAccountNo error, app={}, custNo={}", app, custNo, e);
        }
        return null;
    }

    /**
     * 先根据userNo查询custNo，再根据custNo查询acctNo
     */
    public AcctInfoModel getAcctNoByUserNo(Long userNo) {
        if (userNo == null) {
            return null;
        }
        try {
            List<RegisterInfoDTO> registerInfos = this.queryBatchUserByUserNos(Lists.newArrayList(userNo));
            List<RegisterInfoDTO> validRegisterInfos = registerInfos.stream()
                    .filter(x -> StringUtils.isNoneBlank(x.getCustNo(), x.getApp()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(validRegisterInfos)) {
                return null;
            }

            String app = validRegisterInfos.get(0).getApp();
            String custNo = validRegisterInfos.get(0).getCustNo();
            String accountNo = queryAccountNo(app, custNo);
            log.info("CisService getAcctNoByUserNo userNo={}; app={} custNo={}; accountNo={}", userNo, app, custNo, accountNo);
            if (StringUtils.isNotBlank(accountNo)) {
                return new AcctInfoModel(app, custNo, accountNo);
            }
        } catch (Exception e) {
            log.error("CisService getAcctNoByUserNo error userNo={}", userNo, e);
        }
        return null;
    }

}