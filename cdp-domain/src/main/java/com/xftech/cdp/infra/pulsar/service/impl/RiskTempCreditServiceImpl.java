/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.infra.pulsar.service.impl;

import com.xftech.cdp.domain.strategy.service.dispatch.event.StrategyEventDispatchService;
import com.xftech.cdp.infra.pulsar.service.CdpPulsarService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 * @version $ RiskTempCreditServiceImpl, v 0.1 2023/11/22 11:09 lingang.han Exp $
 */

@Slf4j
@Service("riskTempCreditService")
@AllArgsConstructor
public class RiskTempCreditServiceImpl implements CdpPulsarService {

    private StrategyEventDispatchService strategyEventDispatchService;

    @Override
    public void consumer(String messageId, String eventMessage) {
        toBizEventMessageVO(eventMessage).ifPresent(eventMsg -> {
            log.info("RiskTempCredit eventMessage toBizEventMessageVO={}", eventMsg);
            strategyEventDispatchService.prescreen(messageId, eventMsg);
        });
    }
}