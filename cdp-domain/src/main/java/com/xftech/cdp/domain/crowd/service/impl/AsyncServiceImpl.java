package com.xftech.cdp.domain.crowd.service.impl;

import com.alibaba.excel.util.ListUtils;
import com.xftech.cdp.domain.cache.CacheCrowdPackService;
import com.xftech.cdp.domain.crowd.model.dispatch.CrowdWereHouse;
import com.xftech.cdp.domain.crowd.model.enums.CrowdExecResultEnum;
import com.xftech.cdp.domain.crowd.model.enums.CrowdStatusEnum;
import com.xftech.cdp.domain.crowd.repository.AdsUserLabelDetailInfoDfRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdDetailRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdExecLogRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdPackRepository;
import com.xftech.cdp.domain.crowd.service.AsyncService;
import com.xftech.cdp.domain.cache.CacheCrowdExecLogService;
import com.xftech.cdp.infra.client.usercenter.DataFeatureCoreService;
import com.xftech.cdp.infra.client.usercenter.UserCenterClient;
import com.xftech.cdp.infra.config.Config;
import com.xftech.cdp.infra.config.CrowdConfig;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdExecLogDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPackDo;
import com.xftech.cdp.infra.utils.AesUtil;
import com.xftech.cdp.infra.utils.JsonUtil;
import com.xftech.cdp.infra.utils.MapUtils;
import com.xftech.cdp.infra.utils.SsoUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/2/20
 */
@Slf4j
@Service
public class AsyncServiceImpl implements AsyncService {

    @Autowired
    AdsUserLabelDetailInfoDfRepository adsUserLabelDetailInfoDfRepository;
    @Autowired
    private CrowdDetailRepository crowdDetailRepository;
    @Autowired
    private CrowdExecLogRepository crowdExecLogRepository;
    @Autowired
    private CacheCrowdExecLogService cacheCrowdExecLogService;
    @Autowired
    private CacheCrowdPackService cacheCrowdPackService;
    @Autowired
    protected Config config;
    @Autowired
    private DataFeatureCoreService dataFeatureCoreService;
    @Autowired
    private CrowdConfig crowdConfig;

    @Override
    public Integer parseCrowdUser(CrowdPackDo crowdPackDo) {
        int pageSize = 50;
        int success = 0;
        AtomicReference<Long> detailId = new AtomicReference<>(0L);

        log.info("start async...");

        // 添加人群包执行日志记录
        CrowdExecLogDo execLog = new CrowdExecLogDo();
        execLog.setCrowdId(crowdPackDo.getId());
        execLog.setExecTime(LocalDateTime.now());
        execLog.setExecResult(CrowdExecResultEnum.EXECUTING.getCode());
        execLog.setExecMan(SsoUtil.get().getName());
        execLog.setCrowdPersonNum(success);
        crowdExecLogRepository.insert(execLog);

        while (true) {

            List<CrowdDetailDo> records = crowdDetailRepository.selectByIdPage(detailId.get(), pageSize, crowdPackDo.getId(), null);

            if (CollectionUtils.isEmpty(records)) {
                break;
            }

            List<CrowdDetailDo> crowdDetailDoList = new ArrayList<>(pageSize);
            List<Long> userIds = records.stream().map(CrowdDetailDo::getUserId).collect(Collectors.toList());
            Map<Long, CrowdDetailDo> crowdDetailDoMap = MapUtils.listToMap(records, CrowdDetailDo::getUserId);
            StringBuilder sql = new StringBuilder("select app_user_id,app,inner_app,ab_num,app_user_id_last2,mobile,register_time from ads_user_label_detail_info_df where user_status = \"正常\" and app_user_id in ( ");
            String userIdString = userIds.stream().map(String::valueOf).collect(Collectors.joining(","));
            sql.append(userIdString).append(")");
            //  List<CrowdWereHouse> crowdWereHouses = adsJdbcTemplate.query(  sql.toString(), new BeanPropertyRowMapper<>( CrowdWereHouse.class ) );
            List<CrowdWereHouse> crowdWereHouses = crowdConfig.isNotQueryUserLabelDetail() ? dataFeatureCoreService.queryUserList(userIds) :
                    adsUserLabelDetailInfoDfRepository.queryWareHouseUserInfo(sql.toString());
            crowdWereHouses.forEach(userInfo -> {
                CrowdDetailDo currentDetailDo = crowdDetailDoMap.get(userInfo.getAppUserId());
                if (currentDetailDo != null) {
                    currentDetailDo.setApp(userInfo.getApp());
                    currentDetailDo.setInnerApp(userInfo.getInnerApp());
                    //currentDetailDo.setMobile(AesUtil.mobileDecrypt(userInfo.getMobile(), config.getAdsMobileAesKey()));
                    currentDetailDo.setAbNum(userInfo.getAbNum());
                    currentDetailDo.setAppUserIdLast2(userInfo.getAppUserIdLast2());
                    currentDetailDo.setCrowdExecLogId(execLog.getId());
                    currentDetailDo.setRegisterTime(userInfo.getRegisterTime());
                    crowdDetailDoList.add(currentDetailDo);
                }
            });

            List<Long> delIds = new ArrayList<>();
            // 结果集中不存在的删除
            Map<Long, CrowdWereHouse> crowdWereHouseMap = MapUtils.listToMap(crowdWereHouses, CrowdWereHouse::getAppUserId);
            records.forEach(crowdDetailBo -> {
                if (crowdWereHouseMap.get(crowdDetailBo.getUserId()) == null) {
                    delIds.add(crowdDetailBo.getId());
                    if (delIds.size() >= 100) {
                        crowdDetailRepository.delDirtyData(delIds);
                        delIds.clear();
                    }
                }
                detailId.set(crowdDetailBo.getId());
            });

            if (!CollectionUtils.isEmpty(delIds)) {
                crowdDetailRepository.delDirtyData(delIds);
            }

            if (!crowdDetailDoList.isEmpty()) {
                if (crowdDetailRepository.updateBatchById(crowdDetailDoList)) {
                    success += crowdDetailDoList.size();
                }
            }

            if (records.size() < pageSize) {
                break;
            }
        }

        if (success > 0) {
            crowdPackDo.setCrowdPersonNum(success);
            crowdPackDo.setStatus(CrowdStatusEnum.ENDED.getCode());
        } else {
            crowdPackDo.setCrowdPersonNum(0);
            crowdPackDo.setStatus(CrowdStatusEnum.FAILED.getCode());
        }

        cacheCrowdPackService.updateById(crowdPackDo);

        // 更新执行日志状态，人数
        execLog.setExecResult(success > 0 ? CrowdExecResultEnum.SUCCESS.getCode() : CrowdExecResultEnum.FAIL.getCode());
        execLog.setCrowdPersonNum(success);
        cacheCrowdExecLogService.updateById(execLog);
        return success;
    }


    @Override
    @Async("threadPoolTaskExecutor")
    public Future<Long> getValidUserNum(List<Long> userIdList, Long execLogId, Long crowdId) {
        try {
            log.info("开始处理异步线程解析文件批次:{}", userIdList);
            StringBuilder sql = new StringBuilder("select app_user_id,app,inner_app,ab_num,app_user_id_last2,mobile,register_time from ads_user_label_detail_info_df where user_status = \"正常\" and app_user_id in ( ");
            String userIdString = userIdList.stream().map(String::valueOf).collect(Collectors.joining(","));
            sql.append(userIdString).append(")");
            List<CrowdWereHouse> result = crowdConfig.isNotQueryUserLabelDetail() ? dataFeatureCoreService.queryUserList(userIdList) : adsUserLabelDetailInfoDfRepository.queryWareHouseUserInfo(sql.toString());
            if (CollectionUtils.isEmpty(result)) {
                return new AsyncResult<>(0L);
            }
            List<CrowdDetailDo> crowdDetailDoList = ListUtils.newArrayListWithExpectedSize(userIdList.size());
            result.forEach(userInfo -> {
                CrowdDetailDo crowdDetailDo = new CrowdDetailDo();
                crowdDetailDo.setUserId(userInfo.getAppUserId());
                crowdDetailDo.setCrowdId(crowdId);
                crowdDetailDo.setApp(userInfo.getApp());
                crowdDetailDo.setInnerApp(userInfo.getInnerApp());
                //crowdDetailDo.setMobile(AesUtil.mobileDecrypt(userInfo.getMobile(), config.getAdsMobileAesKey()));
                crowdDetailDo.setAbNum(userInfo.getAbNum());
                crowdDetailDo.setAppUserIdLast2(userInfo.getAppUserIdLast2());
                crowdDetailDo.setCrowdExecLogId(execLogId);
                crowdDetailDo.setDFlag(1);
                crowdDetailDo.setRegisterTime(userInfo.getRegisterTime());
                crowdDetailDoList.add(crowdDetailDo);
            });
            crowdDetailRepository.saveBatch(crowdDetailDoList);
            return new AsyncResult<>((long) crowdDetailDoList.size());
        } catch (Exception e) {
            log.error("异步线程解析文件批次失败,批次用户号{}", userIdList, e);
        }
        return new AsyncResult<>(0L);
    }
}
