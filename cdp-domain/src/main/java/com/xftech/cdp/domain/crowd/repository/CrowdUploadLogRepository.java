package com.xftech.cdp.domain.crowd.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdUploadLogDo;
import org.springframework.stereotype.Component;

/**
 * 人群上传日志表操作
 *
 * <AUTHOR>
 * @since 2023/2/17
 */

@Component
public class CrowdUploadLogRepository {

    /**
     * 根据人群包id查询上传记录
     *
     * @param crowdId 人群包id
     * @return 该人群包id对应的上传记录
     */
    public CrowdUploadLogDo selectByCrowdId(Long crowdId) {
        return DBUtil.selectOne("crowdUploadLog.selectByCrowdId", crowdId);
    }

    /**
     * 根据人群包id删除人群上传记录
     *
     * @param crowdId 人群包id
     */
    public void deleteByCrowdId(Long crowdId) {
        DBUtil.delete("crowdUploadLog.deleteByCrowdId", crowdId);
    }

    /**
     * 插入一条人群上传记录
     *
     * @param crowdUploadLogDo 人群上传记录对象
     */
    public void insert(CrowdUploadLogDo crowdUploadLogDo) {
        DBUtil.insert("crowdUploadLog.insertSelective", crowdUploadLogDo);
    }

    public CrowdUploadLogDo selectById(Long uploadLogId) {
        return DBUtil.selectOne("crowdUploadLog.selectByPrimaryKey", uploadLogId);
    }

    /**
     * 根据id更新人群上传记录
     *
     * @param crowdUploadLogDo 对象
     */
    public void updateById(CrowdUploadLogDo crowdUploadLogDo) {
        DBUtil.delete("crowdUploadLog.updateByPrimaryKeySelective", crowdUploadLogDo);
    }

    /**
     * 根据id删除人群上传记录
     *
     * @param uploadLogId id
     */
    public void deleteById(Long uploadLogId) {
        DBUtil.delete("crowdUploadLog.deleteByPrimaryKey", uploadLogId);
    }
}
