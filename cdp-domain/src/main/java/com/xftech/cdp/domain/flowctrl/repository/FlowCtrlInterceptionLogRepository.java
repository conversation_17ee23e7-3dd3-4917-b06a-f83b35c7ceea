package com.xftech.cdp.domain.flowctrl.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.infra.repository.cdp.flowctrl.po.FlowCtrlGroupNum;
import com.xftech.cdp.infra.repository.cdp.flowctrl.po.FlowCtrlInterceptionLogDo;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @<NAME_EMAIL>
 * @date 2023/3/27 9:49
 */
@Component
public class FlowCtrlInterceptionLogRepository {

    /**
     * 根据主键id查询
     *
     * @param id id
     * @return id对应的记录
     */
    public FlowCtrlInterceptionLogDo selectById(Long id) {
        return DBUtil.selectOne("flowCtrlInterceptionLogMapper.selectByPrimaryKey", id);
    }

//    /**
//     * 插入
//     *
//     * @param flowCtrlInterceptionLogDo 对象
//     * @return 是否插入成功标识
//     */
//    public boolean insert(FlowCtrlInterceptionLogDo flowCtrlInterceptionLogDo) {
//        return DBUtil.insert("flowCtrlInterceptionLogMapper.insertSelective", flowCtrlInterceptionLogDo) > 0;
//    }

    /**
     * 根据主键id更新
     *
     * @param flowCtrlInterceptionLogDo 对象
     * @return 是否更新成功标识
     */
    public boolean updateById(FlowCtrlInterceptionLogDo flowCtrlInterceptionLogDo) {
        return DBUtil.update("flowCtrlInterceptionLogMapper.updateByPrimaryKeySelective", flowCtrlInterceptionLogDo) > 0;
    }

    /**
     * 批量保存
     */
    public void saveBatch(List<FlowCtrlInterceptionLogDo> list) {
        DBUtil.insertBatch("flowCtrlInterceptionLogMapper.insertSelective", list);
    }

    /**
     * 根据策略id统计分组流控拦截的用户数
     * @param strategyId
     * @param startDate
     * @param endDate
     * @return
     */
    public Integer countStrategyGroupFlowCtrl(Long strategyId, List<Long> strategyChannelIds, String startDate, String endDate) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("strategyId", strategyId);
        paramMap.put("strategyChannelIds", strategyChannelIds);
        paramMap.put("startDate", startDate);
        paramMap.put("endDate", endDate);
        return DBUtil.selectOne("flowCtrlInterceptionLogMapper.countStrategyGroupFlowCtrl", paramMap);
    }
}
