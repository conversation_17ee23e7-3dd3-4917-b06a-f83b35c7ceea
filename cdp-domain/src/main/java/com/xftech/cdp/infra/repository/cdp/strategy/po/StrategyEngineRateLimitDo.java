package com.xftech.cdp.infra.repository.cdp.strategy.po;

import java.util.Date;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * T0引擎策略-推入引擎频次限制表
 * strategy_engine_rate_limit
 */
@Data
@NoArgsConstructor
public class StrategyEngineRateLimitDo {

    public StrategyEngineRateLimitDo (Long strategyId, Integer days, Integer quota, Integer coolDownPeriod) {
        this.strategyId = strategyId;
        this.days = days;
        this.quota = quota;
        this.coolDownPeriod = coolDownPeriod;
    }
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 策略ID
     */
    private Long strategyId;

    /**
     * 每x天
     */
    private Integer days;

    /**
     * 推入y次
     */
    private Integer quota;

    /**
     * 间隔时间:分钟
     */
    private Integer coolDownPeriod;

    /**
     * 是否删除 0:未删除;1:已删除
     */
    private Integer dFlag;

    /**
     * 创建人
     */
    private String createdOp;

    /**
     * 修改人
     */
    private String updatedOp;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新时间
     */
    private Date updatedTime;
}