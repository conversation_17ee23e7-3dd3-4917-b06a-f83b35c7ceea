/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.domain.stat.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 *
 * <AUTHOR>
 * @version $ StatStrategyGroupData, v 0.1 2023/12/22 15:14 lingang.han Exp $
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class StatStrategyGroupDataEntity implements Serializable {
    private static final long serialVersionUID = -6324467788651564642L;

    /**
     * pk
     */
    private Long id;

    /**
     *
     * 统计日期
     */
    private LocalDateTime bizDate;

    /**
     *
     * 策略id
     */
    private Long strategyId;

    /**
     *
     * 策略类型: 1.离线-引擎, 2.T0-引擎,3.离线策略,4.T0策略
     */
    private Integer type;

    /**
     *
     * 策略分组id
     */
    private Long strategyGroupId;

    /**
     *
     * 策略分组名称
     */
    private String strategyGroupName;

    /**
     *
     * 是否进入引擎,0非引擎组，1引擎组
     */
    private Integer ifIntoEngine;

    /**
     *
     * 引擎返回groupId
     */
    private String engineGroupId;

    /**
     * 引擎分组标识
     */
    private String groupSource;

    /**
     *
     * 渠道
     */
    private Integer marketChannel;

    /**
     *
     * 模板Id
     */
    private String templateId;

    /**
     *
     * 状态 0：进行中，1：已结束
     */
    private Integer status;

    /**
     *
     * 分组人数
     */
    private Integer groupCount;

    /**
     *
     * 应发人数
     */
    private Integer execCount;

    /**
     *
     * 麻雀发送人数
     */
    private Integer sendCount;

    /**
     *
     * 渠道接收人数
     */
    private Integer receiveCount;

    /**
     *
     * 回执成功人数
     */
    private Integer succCount;

    /**
     *
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     *
     * 更新时间
     */
    private LocalDateTime updatedTime;

}