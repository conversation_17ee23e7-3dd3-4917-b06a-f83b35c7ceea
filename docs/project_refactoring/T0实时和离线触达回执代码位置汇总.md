# T0实时和离线触达回执代码位置汇总

## 概述

本文档汇总了T0实时和离线触达回执动作清单.md中涉及的所有代码位置，便于开发人员快速定位相关代码。

## 1. 核心处理类

### 1.1 主要回执处理服务
- **MqConsumeServiceImpl**: `cdp-domain/src/main/java/com/xftech/cdp/domain/mq/impl/MqConsumeServiceImpl.java`
  - 短信回执处理: `smsReportProcess()` (行82-168)
  - 推送回执处理: `pushReportProcess()` (行169-244)
  - 优惠券回执处理: `couponCallbackProcess()` (行246-505)
  - AI外呼回执处理: `aiCallbackProcess()` (行569-629)

### 1.2 新架构MQ消费者类
- **短信回执消费者**: `touch-infrastructure/src/main/java/com/xinfei/touch/infrastructure/mq/SmsReceiptConsumer.java`
- **推送回执消费者**: (待实现)
- **优惠券回执消费者**: `touch-infrastructure/src/main/java/com/xinfei/touch/infrastructure/mq/CouponReceiptConsumer.java`
- **AI外呼回执消费者**: `touch-infrastructure/src/main/java/com/xinfei/touch/infrastructure/mq/AiCallbackConsumer.java`

### 1.3 RocketMQ消费者
- **优惠券回执Handler**: `cdp-domain/src/main/java/com/xftech/cdp/infra/rocketmq/couponCallback/ReceivedCouponHandler.java`

## 2. 数据模型和枚举

### 2.1 回执VO类
- **SmsReportVO**: `touch-infrastructure/src/main/java/com/xinfei/touch/infrastructure/mq/dto/SmsReportVO.java`
- **PushReportReq**: `cdp-api/src/main/java/com/xftech/cdp/api/dto/req/external/PushReportReq.java`
- **CouponCallbackVO**: `touch-infrastructure/src/main/java/com/xinfei/touch/infrastructure/mq/dto/CouponCallbackVO.java`
- **AiCallBackMessageVO**: `cdp-domain/src/main/java/com/xftech/cdp/infra/rocketmq/dto/AiCallBackMessageVO.java`

### 2.2 状态枚举类
- **SmsStatusEnum**: `cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/model/enums/SmsStatusEnum.java`
- **PushCallbackStatusEnum**: `cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/model/enums/PushCallbackStatusEnum.java`
- **CouponStatusEnum**: `cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/model/enums/CouponStatusEnum.java`
- **AiProntoCallbackStatusEnum**: `cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/model/enums/AiProntoCallbackStatusEnum.java`
- **StrategyMarketChannelEnum**: `cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/model/enums/StrategyMarketChannelEnum.java`

## 3. 数据库操作Repository

### 3.1 核心Repository类
- **CrowdPushBatchRepository**: `cdp-domain/src/main/java/com/xftech/cdp/domain/crowd/repository/CrowdPushBatchRepository.java`
  - 查询表序号: `selectTableNoByBatchNum()`
  - 查询未完成批次: `selectUnFinishQuery()`
- **EventPushBatchRepository**: `cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/repository/EventPushBatchRepository.java`
  - 根据渠道和批次号查询: `getByChannelAndBatchNum()`
- **UserDispatchDetailRepository**: `cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/repository/UserDispatchDetailRepository.java`
- **StrategyExecLogRepository**: `cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/repository/StrategyExecLogRepository.java`

### 3.2 数据库实体类
- **CrowdPushBatchDo**: `cdp-domain/src/main/java/com/xftech/cdp/infra/repository/cdp/crowd/po/CrowdPushBatchDo.java`
  - 语音渠道特殊处理: `initQueryStatus()` (行148-161)

## 4. 业务服务类

### 4.1 计数器服务
- **UserSendCounterService**: `cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/service/UserSendCounterService.java`
- **UserSendCounterServiceImpl**: `cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/service/impl/UserSendCounterServiceImpl.java`
  - 成功计数: `counterIncrementSum()` (行28-62)
  - 失败计数: `counterIncrementFailed()` (行64-70)

### 4.2 触达服务类
- **StrategyEventDispatchServiceImpl**: `cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/service/dispatch/event/impl/StrategyEventDispatchServiceImpl.java`
- **EventDispatchServiceImpl**: `cdp-domain/src/main/java/com/xftech/cdp/domain/dispatch/impl/EventDispatchServiceImpl.java`
- **BatchDispatchServiceImpl**: `cdp-domain/src/main/java/com/xftech/cdp/domain/dispatch/impl/BatchDispatchServiceImpl.java`

## 5. 关键代码位置详解

### 5.1 短信回执处理流程
1. **消息接收**: `SmsReceiptConsumer.smsReportCallback()` (行36-56)
2. **状态验证**: `MqConsumeServiceImpl.smsReportProcess()` (行93-97)
3. **表序号查询**: `crowdPushBatchRepository.selectTableNoByBatchNum()` (行99)
4. **失败计数**: `Tracer.logMetricForCount("failed_sms_" + strategyId)` (行113)
5. **状态更新**: UserDispatchDetailDo对象构建 (行123-133)

### 5.2 语音渠道特殊处理
1. **渠道定义**: `StrategyMarketChannelEnum.VOICE(2, "电销")` (行25)
2. **状态设置**: `CrowdPushBatchDo.initQueryStatus()` (行152-154)
   ```java
   if (channelEnum == StrategyMarketChannelEnum.VOICE) {
       this.setQueryStatus(CrowdPushQueryStatusEnum.COMPLETED.getCode());
       return;
   }
   ```

### 5.3 优惠券特殊业务回执
1. **生活权益**: couponType=4 处理逻辑 (行268-270)
2. **X天免息**: couponType=5 处理逻辑 (行270-272)
3. **特殊回执发送**: 生活权益和X天免息回执处理 (行328-370)

## 6. 监控和日志

### 6.1 Cat监控指标
- `failed_sms_{strategyId}`: MqConsumeServiceImpl.java:113
- `failed_push_{strategyId}`: MqConsumeServiceImpl.java:198
- `failed_coupon_{strategyId}`: MqConsumeServiceImpl.java:283
- `failed_ai_{strategyId}`: MqConsumeServiceImpl.java:621

### 6.2 Apollo配置
- 短信回执队列配置在Apollo中的配置项:
  - `sms.report.exchange = exchange_report_callback_topic`
  - `sms.report.queue.name = sms_supplier_report_callback`
  - `sms.report.routingKey = sms_center_callback_app_xyf-cdp`

## 7. 数据流关系

### 7.1 回执处理数据流
```
MQ消息 → 消费者类 → MqConsumeServiceImpl → Repository → 数据库更新
       ↓
   状态枚举验证 → 计数器更新 → 监控上报 → 特殊业务回执
```

### 7.2 表关系
- `crowd_push_batch` ← 批次号查询 ← 回执消息
- `event_push_batch` ← T0实时触达批次查询
- `user_dispatch_detail_{tableNo}` ← 状态更新
- `strategy_exec_log` ← 统计数据更新

---

**文档版本**: v1.0  
**创建时间**: 2025-06-25  
**维护人员**: CDP开发团队
