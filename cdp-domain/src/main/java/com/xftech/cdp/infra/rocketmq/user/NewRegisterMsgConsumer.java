package com.xftech.cdp.infra.rocketmq.user;

import com.xftech.cdp.infra.rocketmq.EventMessageBaseProcessor;
import com.xftech.cdp.infra.rocketmq.MessageHandlerSelector;
import com.xftech.cdp.infra.utils.WhitelistSwitchUtil;
import com.xinfei.xfframework.common.starter.mq.MqConsumerListener;
import groovy.util.logging.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version $ RegisterMsgConsumer, v 0.1 2025/3/13 17:00 tianshuo.qiu Exp $
 */
@Slf4j
@Component
@RocketMQMessageListener(consumerGroup = "cg_xyf_cdp_tp_user_register", topic = "tp_user_register", consumeMode = ConsumeMode.CONCURRENTLY)

public class NewRegisterMsgConsumer extends MqConsumerListener<String> {
    private static final Logger log = LoggerFactory.getLogger(NewRegisterMsgConsumer.class);

    @Autowired
    private MessageHandlerSelector handlerSelector;

    @Override
    protected void doMessage(String topic, String bodyMessage, MessageExt messageExt) {
        if (WhitelistSwitchUtil.boolSwitchByApollo("NewRegisterMsgConsumerEnable")) {
            log.info("NewRegisterMsgConsumer receive message topic={},messageId={},body={}", topic, messageExt.getMsgId(), bodyMessage);
            try {
                handlerSelector.doMessage(topic, null, messageExt.getMsgId(), bodyMessage);
            } catch (Exception e) {
                log.error("NewRegisterMsgConsumer consumer error,topic={} messageId={},body={}", topic, messageExt.getMsgId(), bodyMessage, e);
            }
        }
    }
}
