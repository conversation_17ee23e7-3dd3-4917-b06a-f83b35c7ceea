/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.service;

import com.xftech.cdp.BaseTest;
import com.xftech.cdp.domain.crowd.model.dispatch.CrowdWereHouse;
import com.xftech.cdp.infra.client.usercenter.DataFeatureCoreService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ DataFeatureCoreServiceTest, v 0.1 2024/4/7 21:18 yye.xu Exp $
 */

public class DataFeatureCoreServiceTest  extends BaseTest {

    @Autowired
    DataFeatureCoreService dataFeatureCoreService;

    @Test
    public void test_query() {
        List<CrowdWereHouse> crowdWereHouses = dataFeatureCoreService.queryUserList(Arrays.asList(2024041901L));
        System.out.println(crowdWereHouses.size());
    }
}