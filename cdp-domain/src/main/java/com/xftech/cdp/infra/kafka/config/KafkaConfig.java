package com.xftech.cdp.infra.kafka.config;

import java.util.Properties;

import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * 阿里云文档地址: https://help.aliyun.com/zh/apsaramq-for-kafka/best-practices-for-producers?spm=a2c4g.11186623.0.i4
 */
@Configuration
public class KafkaConfig {
    private static final String kafkaProperties = "kafkaProperties";
    private static final String keySerializer = "org.apache.kafka.common.serialization.StringSerializer";
    private static final String valueSerializer = "org.apache.kafka.common.serialization.StringSerializer";

    @Bean(kafkaProperties)
    public Properties properties(@Value("${kafka.bootstrap.servers}") String bootstrapServers,
                                 @Value("${kafka.max.block.ms:1000}") int maxBlockMs,
                                 @Value("${kafka.batch.size:16384}") int batchSize,
                                 @Value("${kafka.linger.ms:1000}") int lingerMs,
                                 @Value("${kafka.acks:1}") String acks,
                                 @Value("${kafka.retries:1}") int retries,
                                 @Value("${kafka.compressionType:none}") String compressionType) {
        Properties props = new Properties();
        //设置接入点，请通过控制台获取对应Topic的接入点。
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, keySerializer);
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, valueSerializer);
        props.put(ProducerConfig.ACKS_CONFIG, acks);
        // 其它使用默认值
        // acks=0：无需服务端的Response、性能较高、丢数据风险较大。
        //props.put(ProducerConfig.ACKS_CONFIG, 0);
        // batch.size : 发往每个分区（Partition）的消息缓存量（消息内容的字节数之和，不是条数）。达到设置的数值时，就会触发一次网络请求，然后Producer客户端把消息批量发往服务器。如果batch.size设置过小，有可能影响发送性能和稳定性。建议保持默认值16384。单位：字节。
        //linger.ms : 每条消息在缓存中的最长时间。若超过这个时间，Producer客户端就会忽略batch.size的限制，立即把消息发往服务器。建议根据业务场景， 设置linger.ms在100~1000之间。单位：毫秒。
        //因此，云消息队列 Kafka 版Producer客户端什么时候把消息批量发送至服务器是由batch.size和linger.ms共同决定的。您可以根据具体业务需求进行调整。为了提升发送的性能，保障服务的稳定性， 建议您设置batch.size=16384和linger.ms=1000
        // 必须配置
        props.put(ProducerConfig.BATCH_SIZE_CONFIG, batchSize);
        // 可选配置
        props.put(ProducerConfig.LINGER_MS_CONFIG, lingerMs);
        //请求的最长等待时间。
        props.put(ProducerConfig.MAX_BLOCK_MS_CONFIG, maxBlockMs);
        //设置客户端内部重试次数。
        props.put(ProducerConfig.RETRIES_CONFIG, retries);

        // CompressionType.LZ4.name
        if (StringUtils.isNotBlank(compressionType)) {
            props.put(ProducerConfig.COMPRESSION_TYPE_CONFIG, compressionType);
        }
        return props;
    }

    @Bean
    @Primary
    public KafkaProducer<String, String> kafkaProducer(@Qualifier(kafkaProperties) Properties properties) {
        return new KafkaProducer<>(properties);
    }
}