package com.xftech.cdp.distribute.crowd.repository.model;

import java.util.Date;

import lombok.Data;

/**
 * 人群元数据版本表
 * crowd_info_version
 */
@Data
public class CrowdInfoVersionDo {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 人群包id
     */
    private Long crowdId;

    /**
     * 版本:初始版本为YYYYMMDD01,oss路径发生变更时版本+1
     */
    private Long crowdVersion;

    /**
     * oss目录
     */
    private String crowdOssFolder;

    /**
     * oss文件列表
     */
    private String crowdOssFile;

    /**
     * 人群数量
     */
    private Long crowdSize;

    /**
     * 洞察平台人群运行版本,YYYYMMDD_xx
     */
    private String rawVersion;

    /**
     * 分片状态,0:初始化;1:分片中;2:分片完成;3:分片失败
     */
    private Integer sliceStatus;

    /**
     * 是否删除:0未删除;1已删除
     */
    private Integer dFlag;

    /**
     * 创建人
     */
    private String createdOp;

    /**
     * 修改人
     */
    private String updatedOp;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 修改时间
     */
    private Date updatedTime;

}