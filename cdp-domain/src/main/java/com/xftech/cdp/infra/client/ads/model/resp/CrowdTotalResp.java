/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.infra.client.ads.model.resp;

import lombok.Data;

import java.io.Serializable;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 * @version $ CrowdTotalResp, v 0.1 2023/11/22 18:05 wancheng.qu Exp $
 */
@Data
public class CrowdTotalResp implements Serializable {

    private Long crowdId;//	人群包ID	Long
    private Integer status;//	状态，0：未完成, 1：完成
    private Integer total;//人群包条数

    public boolean isSuccess(){
        return Objects.equals(1,status);
    }

    //单个人群包状态是否失败
    public boolean isFail(){
        return status == null || (status != 0 && status != 1);
    }
}