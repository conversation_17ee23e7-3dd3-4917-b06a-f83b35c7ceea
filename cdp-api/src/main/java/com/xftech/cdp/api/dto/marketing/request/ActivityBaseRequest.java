/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2025. All Rights Reserved.
 */

package com.xftech.cdp.api.dto.marketing.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version $ ActivityCommonRequest, v 0.1 2025/1/4 15:06 xu.fan Exp $
 */
@Data
@ApiModel("活动通用请求体")
public class ActivityBaseRequest {

    @ApiModelProperty(value = "活动ID")
    @NotNull(message = "活动信息不能为空")
    private Long activityId;

    @ApiModelProperty(value = "用户ID")
    @NotNull(message = "用户信息不能为空")
    private Long userId;
}
