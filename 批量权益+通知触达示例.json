{"touchRequest": {"requestId": "REQ_BATCH_BENEFIT_NOTIFY_1735804800000", "businessScene": "BATCH_COUPON_WITH_SMS_NOTIFICATION", "requestTime": "2025-01-02T10:00:00", "userTouches": [{"target": {"userId": 30001, "mobile": "***********", "appId": "xinfei-app", "userTags": {"vip_level": "gold", "campaign": "spring_festival", "region": "beijing"}}, "message": {"templateId": "COUPON_SPRING_001", "title": "春节专享优惠券", "content": "恭喜您获得春节专享优惠券", "templateParams": {"userName": "张先生", "couponAmount": "100", "validDays": "7"}, "attachments": {"coupon_type": "DISCOUNT", "coupon_amount": 100, "validity_days": 7, "applicable_products": ["LOAN", "CREDIT_CARD"]}}}, {"target": {"userId": 30002, "mobile": "***********", "appId": "xinfei-app", "userTags": {"vip_level": "silver", "campaign": "spring_festival", "region": "shanghai"}}, "message": {"templateId": "COUPON_SPRING_001", "title": "春节专享优惠券", "content": "恭喜您获得春节专享优惠券", "templateParams": {"userName": "李女士", "couponAmount": "80", "validDays": "7"}, "attachments": {"coupon_type": "DISCOUNT", "coupon_amount": 80, "validity_days": 7, "applicable_products": ["LOAN", "CREDIT_CARD"]}}}, {"target": {"userId": 30003, "mobile": "13800138003", "appId": "xinfei-app", "userTags": {"vip_level": "bronze", "campaign": "spring_festival", "region": "guangzhou"}}, "message": {"templateId": "COUPON_SPRING_001", "title": "春节专享优惠券", "content": "恭喜您获得春节专享优惠券", "templateParams": {"userName": "王先生", "couponAmount": "50", "validDays": "7"}, "attachments": {"coupon_type": "DISCOUNT", "coupon_amount": 50, "validity_days": 7, "applicable_products": ["LOAN"]}}}, {"target": {"userId": 30004, "mobile": "13800138004", "appId": "xinfei-app", "userTags": {"vip_level": "gold", "campaign": "spring_festival", "region": "<PERSON><PERSON><PERSON>"}}, "message": {"templateId": "COUPON_SPRING_001", "title": "春节专享优惠券", "content": "恭喜您获得春节专享优惠券", "templateParams": {"userName": "陈女士", "couponAmount": "120", "validDays": "7"}, "attachments": {"coupon_type": "DISCOUNT", "coupon_amount": 120, "validity_days": 7, "applicable_products": ["LOAN", "CREDIT_CARD", "INSURANCE"]}}}, {"target": {"userId": 30005, "mobile": "13800138005", "appId": "xinfei-app", "userTags": {"vip_level": "platinum", "campaign": "spring_festival", "region": "hangzhou"}}, "message": {"templateId": "COUPON_SPRING_001", "title": "春节专享优惠券", "content": "恭喜您获得春节专享优惠券", "templateParams": {"userName": "刘先生", "couponAmount": "200", "validDays": "7"}, "attachments": {"coupon_type": "DISCOUNT", "coupon_amount": 200, "validity_days": 7, "applicable_products": ["LOAN", "CREDIT_CARD", "INSURANCE", "WEALTH"]}}}], "touchMethod": {"primaryMethod": {"methodType": "BENEFIT", "benefit": "COUPON"}, "notificationMethod": {"enabled": true, "condition": "SUCCESS", "delaySeconds": 300, "notificationChannel": "SMS", "notificationMessage": {"templateId": "SMS_COUPON_NOTIFY_001", "title": "优惠券到账通知", "content": "您的${couponAmount}元优惠券已到账，有效期${validDays}天，快去使用吧！", "templateParams": {"couponAmount": "动态参数", "validDays": "7"}}}}, "timing": {"timingType": "IMMEDIATE", "batchIntervalSeconds": 0, "batchTimeoutSeconds": 300}, "config": {"priority": "HIGH", "allowDuplicate": false, "validityMinutes": 1440, "requireReceipt": true}, "businessContext": {"strategyId": 6000, "activityId": "SPRING_FESTIVAL_2025", "eventType": "MARKETING_CAMPAIGN", "businessTags": {"touch_type": "benefit_with_notification", "season": "spring", "batch_size": "5"}}}, "touchResponse": {"requestId": "REQ_BATCH_BENEFIT_NOTIFY_1735804800000", "executionStatus": "PARTIAL_SUCCESS", "responseTime": "2025-01-02T10:00:15", "userResponses": [{"userId": 30001, "result": "SUCCESS", "executeTime": "2025-01-02T10:00:02", "channelResponse": {"channelCode": "COUPON_SUCCESS", "channelMessage": "优惠券发放成功", "channelData": {"couponId": "CPN_20250102_001", "couponAmount": 100, "expiryDate": "2025-01-09"}}, "businessFeedback": {"businessResult": "SUCCESS", "businessMessage": "权益发放成功，通知已发送", "businessData": {"notificationSent": true, "notificationTime": "2025-01-02T10:05:02"}}}, {"userId": 30002, "result": "SUCCESS", "executeTime": "2025-01-02T10:00:03", "channelResponse": {"channelCode": "COUPON_SUCCESS", "channelMessage": "优惠券发放成功", "channelData": {"couponId": "CPN_20250102_002", "couponAmount": 80, "expiryDate": "2025-01-09"}}, "businessFeedback": {"businessResult": "SUCCESS", "businessMessage": "权益发放成功，通知已发送", "businessData": {"notificationSent": true, "notificationTime": "2025-01-02T10:05:03"}}}, {"userId": 30003, "result": "FAILED", "executeTime": "2025-01-02T10:00:04", "errorMessage": "用户风险等级过高，权益发放被拒绝", "channelResponse": {"channelCode": "COUPON_REJECTED", "channelMessage": "权益发放被风控拒绝", "channelData": {"rejectReason": "RISK_CONTROL", "riskScore": 85}}, "businessFeedback": {"businessResult": "FAILED", "businessMessage": "权益发放失败，未发送通知", "businessData": {"notificationSent": false}}}, {"userId": 30004, "result": "SUCCESS", "executeTime": "2025-01-02T10:00:05", "channelResponse": {"channelCode": "COUPON_SUCCESS", "channelMessage": "优惠券发放成功", "channelData": {"couponId": "CPN_20250102_004", "couponAmount": 120, "expiryDate": "2025-01-09"}}, "businessFeedback": {"businessResult": "SUCCESS", "businessMessage": "权益发放成功，通知已发送", "businessData": {"notificationSent": true, "notificationTime": "2025-01-02T10:05:05"}}}, {"userId": 30005, "result": "SUCCESS", "executeTime": "2025-01-02T10:00:06", "channelResponse": {"channelCode": "COUPON_SUCCESS", "channelMessage": "优惠券发放成功", "channelData": {"couponId": "CPN_20250102_005", "couponAmount": 200, "expiryDate": "2025-01-09"}}, "businessFeedback": {"businessResult": "SUCCESS", "businessMessage": "权益发放成功，通知已发送", "businessData": {"notificationSent": true, "notificationTime": "2025-01-02T10:05:06"}}}], "executionSummary": {"totalUsers": 5, "successUsers": 4, "failedUsers": 1, "rejectedUsers": 0, "successRate": 0.8, "executionTimeMs": 15000}}}