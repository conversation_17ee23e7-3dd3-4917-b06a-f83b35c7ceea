package com.xftech.cdp.infra.rocketmq;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version v1.0 2024/9/24
 * @description RocketMQEnum
 */
@Getter
@AllArgsConstructor
public enum RocketMQEnum {
    TP_APIOPFCORE_API_JUDGE_ALL("tp_apiopfcore_api_judge_all", "cg_xyf_cdp_tp_apiopfcore_api_judge_all", "撞库MQ"),

    DISTRIBUTION_GRANTED_SUCCESS("tp_distributecore_approval_success", "cg_xyf_cdp_tp_distributecore_approval_success", "分发授信成功"),

    TP_ACCESS_CONTROL_DIVERSION("tp_rcspt_risk_access_control_message", "cg_xyf_cdp_tp_rcspt_risk_access_control_message", "风控禁申"),

    TP_UGCHANPROD_API_JUDGE_ENGINE("tp_ugchanprod_api_judge_engine","cg_xyf_cdp_tp_ugchanprod_api_judge_engine","API撞库事件上报"),

    TP_REPAY_TRADE_REPAY_RESULT_SETTLE_SUCCESS("tp_repaytrade_repayResult", "cg_xyf_cdp_tp_repaytrade_repayResult_settleSuccess", "(新)还款成功"),
    TP_REPAY_TRADE_REPAY_RESULT_REPAY_SUCCESS("tp_repaytrade_repayResult", "cg_xyf_cdp_tp_repaytrade_repayResult_repaySuccess", "(新)结清(借据级)"),

    TP_PSENGINECORE_ADVANCE_ORDER_CREATE("tp_psenginecore_advance_order_create", "cg_xyf_cdp_tp_psenginecore_advance_order_create", "预借款提交成功"),

    TP_APIOPFCORE_CREDIT_RESULT("tp_apiopfcore_credit_result", "cg_xyf_cdp_tp_apiopfcore_credit_result", "API进件"),
    ;

    private final String topic;
    private final String consumerGroup;
    private final String desc;

}
