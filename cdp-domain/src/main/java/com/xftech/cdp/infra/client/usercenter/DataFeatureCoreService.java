/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.client.usercenter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.xftech.cdp.domain.crowd.model.dispatch.CrowdWereHouse;
import com.xftech.cdp.infra.client.usercenter.config.DataFeatureConfig;
import com.xftech.cdp.infra.utils.DateUtil;
import com.xftech.cdp.infra.utils.JsonUtil;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @version $ DataFeatureCoreService, v 0.1 2024/4/7 19:34 yye.xu Exp $
 */

@Slf4j
@Service
@AllArgsConstructor
public class DataFeatureCoreService {
    private RestTemplate restTemplate;
    private DataFeatureConfig dataFeatureConfig;

    public List<CrowdWereHouse> queryUserList(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return new ArrayList<>(0);
        }
        String featureUrl = dataFeatureConfig.getDatafeaturecoreUrl();
        userIdList = userIdList.stream().distinct().collect(Collectors.toList());
        List<CrowdWereHouse> crowdWereHouses = new ArrayList<>(0);
        Lists.partition(userIdList, 50).parallelStream()
                .forEach(items -> {
                    HashMap<String, Object> params = new HashMap<>();
                    params.put("pocketId", dataFeatureConfig.getPocketId());
                    params.put("rowkeys", items.stream().map(RowkeysRequest::new)
                            .collect(Collectors.toList()));
                    try {
                        Map postForObject = restTemplate.postForObject(featureUrl, params, Map.class);
                        log.info("接口调用, url:{}, 请求参数:{}, 请求返回:{}", featureUrl, JsonUtil.toJson(params), JsonUtil.toJson(postForObject));
                        if (postForObject != null && postForObject.containsKey("code")) {
                            if (StringUtils.equalsIgnoreCase("200",
                                    postForObject.get("code").toString())) {
                                Map<String, Object> data = JsonUtil.toMap(JsonUtil.toJson(postForObject.get("valuesWithRowkeys")));
                                if (!CollectionUtils.isEmpty(data)) {
                                    data.forEach((key, value) -> {
                                        CrowdWereHouse crowdWereHouse = new CrowdWereHouse();
                                        try {
                                            Field field = JsonUtil.parse(JsonUtil.toJson(value), Field.class);
                                            if (field != null && field.isValid()) {
                                                crowdWereHouse.setAppUserId(Long.parseLong(key));
                                                crowdWereHouse.setApp(field.getApp().getObj());
                                                crowdWereHouse.setInnerApp(field.getInner_app() != null ? field.getInner_app().getObj() : null);
                                                crowdWereHouse.setAbNum(field.getAb_num().getObj());
                                                crowdWereHouse.setMobile(field.getMobile().getObj());
                                                crowdWereHouse.setRegisterTime(DateUtil.convert(DateUtil.convert(field.getRegister_time().getObj())));
                                                crowdWereHouse.setAppUserIdLast2(Integer.valueOf(key.substring(key.length() - 2)));
                                            } else {
                                                log.warn("DataFeatureCoreService queryUserList user invalid, userId={}", key);
                                                return;
                                            }
                                        } catch (Exception e) {
                                            log.warn("DataFeatureCoreService queryUserList user error, userId={}", key, e);
                                            return;
                                        }
                                        crowdWereHouses.add(crowdWereHouse);
                                    });
                                }
                            }
                        }
                    } catch (Exception ex) {
                        log.error("queryUserList, url:{}, request:{}", featureUrl, JsonUtil.toJson(params));
                    }
                });
        return crowdWereHouses;
    }

    @Data
    public static class RowkeysRequest {
        private Long app_user_id;

        public RowkeysRequest() {
        }

        public RowkeysRequest(Long app_user_id) {
            this.app_user_id = app_user_id;
        }
    }

    @Data
    public static class Field {
        private ObjType app;
        private ObjType inner_app;
        private ObjType ab_num;
        private ObjType mobile;
        private ObjType register_time;

        public boolean isValid() {
            if (app == null || StringUtils.isBlank(app.getObj())) {
                return false;
            }
            if (inner_app == null || StringUtils.isBlank(inner_app.getObj())) {
                return false;
            }
            if (mobile == null || StringUtils.isBlank(mobile.getObj())) {
                return false;
            }
            if (register_time == null || StringUtils.isBlank(register_time.getObj())) {
                return false;
            }
            return true;
        }
    }

    @Data
    public static class ObjType {
        private String obj;
        private String type;
    }
}