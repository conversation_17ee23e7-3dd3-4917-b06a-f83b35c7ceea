package com.xftech.cdp.domain.crowd.repository;


import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.domain.crowd.model.dispatch.CrowdWereHouse;
import com.xftech.cdp.infra.config.LogUtil;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;

import com.alibaba.fastjson.JSON;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

/**
 * <AUTHOR>
 */
@Component
public class AdsUserLabelDetailInfoDfRepository {

    /**
     * 根据组装sql查询数仓用户标签表用户信息
     *
     * @param sql sql
     * @return 数仓用户标签用户信息列表
     */
    @Deprecated
    public List<CrowdWereHouse> queryWareHouseUserInfo(String sql) {
        return DBUtil.selectList("ads", "adsUserLabelDetailInfo.queryWareHouseUserInfo", sql);
    }

    /**
     * 查询用户标签表最大的appUserId
     *
     * @return
     */
    public Long selectMaxAppUserId() {
        return DBUtil.selectOne("ads", "adsUserLabelDetailInfo.selectMaxAppUserId", null);
    }

    public Long selectMinAppUserId() {
        return DBUtil.selectOne("ads", "adsUserLabelDetailInfo.selectMinAppUserId", null);
    }

    @Deprecated
    public CrowdWereHouse queryUtmByMobileAndApp(String mobile, String app) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("mobile", mobile);
        paramMap.put("app", app);
        return DBUtil.selectOne("ads", "adsUserLabelDetailInfo.queryUtmByMobileAndApp", paramMap);
    }

    /**
     * 新人群包查询
     */
    public List<CrowdWereHouse> selectBatchStarrocks(String tableName, LocalDateTime time,Long cnost,Long pageSize,Long crowdId) {
        Map<String, Object> param = new HashMap<>();
        param.put("tableName", tableName);
        param.put("crowdId", crowdId);
        param.put("pt", time.toLocalDate());
        param.put("cnost", cnost);
        param.put("cnoed", cnost+pageSize+1);
        param.put("pageSize", pageSize);
        return DBUtil.selectList("ads","adsUserLabelDetailInfo.selectBatchStarrocks",param);
    }

    public Optional<CrowdWereHouse> hasUserRecord(String tableName,Long userId, Long crowdExecLogId, LocalDateTime time) {
        Map<String, Object> param = new HashMap<>();
        param.put("userId", userId);
        param.put("crowdId", crowdExecLogId);
        param.put("pt", time.toLocalDate());
        param.put("tableName", tableName);
        return Optional.ofNullable(DBUtil.selectOne("ads","adsUserLabelDetailInfo.hasUserRecord", param));
    }

    public List<CrowdWereHouse> hasUserRecordByIn(String tableName, Long userId, Set<Long> crowdIds, LocalDateTime time) {
        Map<String, Object> param = new HashMap<>();
        param.put("userId", userId);
        param.put("crowdIds", crowdIds.stream().map(String::valueOf).toArray(String[]::new));
        param.put("pt", time.toLocalDate());
        param.put("tableName", tableName);
        LogUtil.logDebug("AdsUserLabelDetailInfoDfRepository hasUserRecordByIn param={}", JSON.toJSONString(param));
        return DBUtil.selectList("ads","adsUserLabelDetailInfo.hasUserRecordByIn", param);
    }

}
