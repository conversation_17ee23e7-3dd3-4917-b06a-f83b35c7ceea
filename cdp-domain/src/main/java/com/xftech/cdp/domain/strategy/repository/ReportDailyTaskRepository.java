/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.infra.repository.cdp.strategy.po.ReportDailyTaskDo;
import org.springframework.stereotype.Component;

import java.util.Date;


/**
 * <AUTHOR>
 */
@Component
public class ReportDailyTaskRepository {

    public ReportDailyTaskDo selectById(Long id) {
        return DBUtil.selectOne("reportDailyTaskMapper.selectByPrimaryKey", id);
    }

    public boolean insert(ReportDailyTaskDo reportDailyTaskDo) {
        return DBUtil.insert("reportDailyTaskMapper.insertSelective", reportDailyTaskDo) > 0;
    }

    public boolean updateById(ReportDailyTaskDo reportDailyTaskDo) {
        reportDailyTaskDo.setUpdatedTime(new Date());
        return DBUtil.update("reportDailyTaskMapper.updateByPrimaryKeySelective", reportDailyTaskDo) > 0;
    }

    public boolean existReportDailyTaskRecord(ReportDailyTaskDo reportDailyTaskDo) {
        Integer num = DBUtil.selectOne("reportDailyTaskMapper.existReportDailyTaskRecord", reportDailyTaskDo);
        return num > 0;
    }

    public void updateByDateAndType(ReportDailyTaskDo reportDailyTaskDo) {
        DBUtil.update("reportDailyTaskMapper.updateByDateAndType", reportDailyTaskDo);
    }

    public ReportDailyTaskDo selectTodayByType(Integer type) {
        return DBUtil.selectOne("reportDailyTaskMapper.selectTodayByType", type);
    }
}