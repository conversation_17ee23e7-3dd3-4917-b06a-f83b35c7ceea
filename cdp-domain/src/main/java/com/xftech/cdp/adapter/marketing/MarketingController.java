package com.xftech.cdp.adapter.marketing;

import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;

import com.xftech.cdp.api.MarketingActivityApi;
import com.xftech.cdp.api.dto.base.Response;
import com.xftech.cdp.api.dto.marketing.request.ActivityBaseRequest;
import com.xftech.cdp.api.dto.marketing.request.CheckUserEligibilityRequest;
import com.xftech.cdp.api.dto.marketing.request.RegisterRequest;
import com.xftech.cdp.api.dto.marketing.request.WinningRequest;
import com.xftech.cdp.api.dto.marketing.response.*;
import com.xftech.cdp.api.dto.req.external.ExternalBaseRequest;
import com.xftech.cdp.domain.marketing.service.MarketingService;
import com.xftech.cdp.infra.annotation.ApiNoSigner;
import com.xftech.cdp.infra.annotation.ApiSigner;
import com.xftech.cdp.infra.annotation.SysLog;
import com.xftech.cdp.infra.constant.SysConstants;

import com.xftech.cdp.infra.utils.WhitelistSwitchUtil;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2023/2/13
 */
@RestController
public class MarketingController implements MarketingActivityApi {

    @Resource
    private MarketingService marketingService;

    @ApiNoSigner
    @SysLog(value = "查询用户活动资格", extApi = true)
    @Override
    public Response<EligibilityResponse> checkUserEligibility(@Validated @RequestBody ExternalBaseRequest<CheckUserEligibilityRequest> request) {
        CheckUserEligibilityRequest checkUserEligibilityRequest = request.getArgs();
        if (checkUserEligibilityRequest == null) {
            return Response.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), "请求参数非法");
        }
        if (checkUserEligibilityRequest.getUserId() == null) {
            return Response.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), "userId can't be null");
        }
        if (checkUserEligibilityRequest.getActivityId() == null) {
            return Response.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), "activityId can't be null");
        }
        if(!WhitelistSwitchUtil.graySwitchByApollo("" +checkUserEligibilityRequest.getUserId(), "checkUserEligibility", null)) {
            return Response.fail(HttpStatus.METHOD_NOT_ALLOWED.value(), "开关拦截");
        }
        return Response.success(marketingService.checkUserEligibility(checkUserEligibilityRequest.getActivityId(), checkUserEligibilityRequest.getUserId()));
    }

    @ApiNoSigner
    @SysLog(value = "翻卡抽奖", extApi = true)
    @Override
    public Response<WinningResponse> participateLottery(@Validated @RequestBody ExternalBaseRequest<WinningRequest> request) {
        WinningRequest winningRequest = request.getArgs();
        if (winningRequest == null) {
            return Response.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), "请求参数非法");
        }
        if (winningRequest.getUserId() == null) {
            return Response.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), "userId can't be null");
        }
        if (winningRequest.getActivityId() == null) {
            return Response.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), "activityId can't be null");
        }
        if(!WhitelistSwitchUtil.graySwitchByApollo("" +winningRequest.getUserId(), "participateLottery", null)) {
            return Response.fail(HttpStatus.METHOD_NOT_ALLOWED.value(), "开关拦截");
        }
        Object winningResponse = marketingService.participateLottery(winningRequest);
        if (winningResponse instanceof WinningResponse) {
            return Response.success((WinningResponse) winningResponse);
        } else if (winningResponse instanceof String) {
            return Response.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), (String) winningResponse);
        } else {
            return Response.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), "活动太火爆啦，请稍后再试");
        }
    }

    @ApiNoSigner
    @SysLog(value = "已参与用户优惠券膨胀", extApi = true)
    @Override
    public  Response<InflationResponse> couponInflation(@Validated @RequestBody ExternalBaseRequest<ActivityBaseRequest> request) {

        ActivityBaseRequest commonRequest = request.getArgs();
        if (commonRequest == null) {
            return Response.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), "请求参数非法");
        }
        if (commonRequest.getUserId() == null) {
            return Response.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), "userId can't be null");
        }
        if (commonRequest.getActivityId() == null) {
            return Response.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), "activityId can't be null");
        }
        if(!WhitelistSwitchUtil.graySwitchByApollo("" +commonRequest.getUserId(), "couponInflation", null)) {
            return Response.fail(HttpStatus.METHOD_NOT_ALLOWED.value(), "开关拦截");
        }
        Object inflationResponse = marketingService.couponInflation(commonRequest);
        if (inflationResponse instanceof InflationResponse) {
            return Response.success((InflationResponse) inflationResponse);
        } else {
            return Response.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), "活动太火爆啦，请稍后再试");
        }
    }

    @ApiNoSigner
    @SysLog(value = "活动报名", extApi = true)
    @Override
    public Response<RegisterResponse> registerLottery(@Validated @RequestBody ExternalBaseRequest<RegisterRequest> request) {
        RegisterRequest registerRequest = request.getArgs();
        if (registerRequest == null) {
            return Response.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), "请求参数非法");
        }
        if (registerRequest.getUserId() == null) {
            return Response.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), "userId can't be null");
        }
        if (registerRequest.getActivityId() == null) {
            return Response.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), "activityId can't be null");
        }
        if (registerRequest.getQueryStatus() == null) {
            return Response.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), "queryStatus can't be null");
        }
        if (!Objects.equals(registerRequest.getQueryStatus(), 1) && !Objects.equals(registerRequest.getQueryStatus(), 2)) {
            return Response.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), "queryStatus illegal");
        }
        if(!WhitelistSwitchUtil.graySwitchByApollo("" +registerRequest.getUserId(), "registerLottery", null)) {
            return Response.fail(HttpStatus.METHOD_NOT_ALLOWED.value(), "开关拦截");
        }
        return Response.success(marketingService.registerLottery(registerRequest));
    }

    @ApiNoSigner
    @SysLog(value = "活动详情", extApi = true)
    @Override
    public Response<ActivityDetailResponse> activityDetail(@Validated @RequestBody ExternalBaseRequest<ActivityBaseRequest> request) {

        ActivityBaseRequest commonRequest = request.getArgs();
        if (commonRequest == null) {
            return Response.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), "请求参数非法");
        }
        if (commonRequest.getUserId() == null) {
            return Response.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), "userId can't be null");
        }
        if (commonRequest.getActivityId() == null) {
            return Response.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), "activityId can't be null");
        }
        if(!WhitelistSwitchUtil.graySwitchByApollo("" +commonRequest.getUserId(), "activityDetail", null)) {
            return Response.fail(HttpStatus.METHOD_NOT_ALLOWED.value(), "开关拦截");
        }

        return Response.success(marketingService.activityDetail(commonRequest));
    }

    @ApiNoSigner
    @SysLog(value = "活动记录广播", extApi = true)
    @Override
    public Response<RecordsBroadcastResponse> recordsBroadcast(@Validated @RequestBody ExternalBaseRequest<ActivityBaseRequest> request) {

        ActivityBaseRequest commonRequest = request.getArgs();
        if (commonRequest == null) {
            return Response.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), "请求参数非法");
        }
        if (commonRequest.getUserId() == null) {
            return Response.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), "userId can't be null");
        }
        if (commonRequest.getActivityId() == null) {
            return Response.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), "activityId can't be null");
        }
        if(!WhitelistSwitchUtil.graySwitchByApollo("" +commonRequest.getUserId(), "recordsBroadcast", null)) {
            return Response.fail(HttpStatus.METHOD_NOT_ALLOWED.value(), "开关拦截");
        }

        return Response.success(marketingService.recordsBroadcast(commonRequest));
    }

}