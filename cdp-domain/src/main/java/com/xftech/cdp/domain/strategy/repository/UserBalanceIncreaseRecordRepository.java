package com.xftech.cdp.domain.strategy.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.infra.repository.cdp.marketing.po.UserBalanceIncreaseRecordDo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 用户返现记录实体操作
 */
@Repository
public class UserBalanceIncreaseRecordRepository {

    /**
     * 新增用户返现记录
     */
    public int insertSelective(UserBalanceIncreaseRecordDo record) {
        return DBUtil.insert("userbalanceIncreaserecordmapper.insertSelective", record);
    }

    /**
     * 根据用户id与单号（非必填）查询用户返现记录
     */
    public List<UserBalanceIncreaseRecordDo> selectByOderAndUserId(UserBalanceIncreaseRecordDo record) {
        return DBUtil.selectList("userbalanceIncreaserecordmapper.selectByOderAndUserId", record);
    }
}