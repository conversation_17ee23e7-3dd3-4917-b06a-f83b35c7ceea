/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.req;

import com.xftech.cdp.api.dto.base.PageRequestDto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version $ MonitorEngineListReq, v 0.1 2023/12/14 14:26 lingang.han Exp $
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MonitorEngineReInputListReq extends PageRequestDto {

    @ApiModelProperty(value = "策略id")
    private Long strategyId;

    @ApiModelProperty(value = "日期,如2024-07-08")
    private String date;

}