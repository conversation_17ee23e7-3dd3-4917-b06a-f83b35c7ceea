# T0-普通触达execSend链路详细分析报告

## 1. 概述

本文档深入分析T0普通触达的完整链路，从MQ消息消费到最终营销触达执行的全过程。重点分析`StrategyEventDispatchServiceImpl.execSend`方法的调用链路、业务逻辑、代码分层、外部接口调用、数据流转等核心内容。

## 2. 系统架构与分层设计

### 2.1 整体架构图

```mermaid
graph TD
    A[业务事件MQ] --> B[消息消费层]
    B --> C[消息路由层MessageHandlerSelector]
    C --> D[策略预筛层prescreen]
    D --> E[策略复筛层rescreen]
    E --> F[触达分发层dispatch]
    F --> G[execSend执行层]
    G --> H[渠道适配层EventDispatchService]
    H --> I[外部服务调用层]
    I --> J[短信服务SMS]
    I --> K[电销服务TELE]
    I --> L[优惠券服务COUPON]
    I --> M[生活权益服务LIFE_RIGHTS]
    I --> N[推送服务PUSH]
    I --> O[AI服务AI_PRONTO]
```

### 2.2 代码分层逻辑

#### 2.2.1 消息消费层 (MQ Consumer Layer)
- **位置**: `cdp-infrastructure/src/main/java/com/xftech/cdp/infrastructure/mq/consumer/`
- **职责**: 接收业务事件MQ消息，进行基础校验和格式转换
- **核心类**:
  - `LifeRightsRocketMqConsumer`: 生活权益事件消费
  - `NewRegisterMsgConsumer`: 新注册用户事件消费
  - `Start1RocketMsgConsumer`: Start1事件消费

#### 2.2.2 消息路由层 (Message Router Layer)
- **位置**: `cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/service/dispatch/`
- **职责**: 根据事件类型路由到对应的策略处理器
- **核心类**: `MessageHandlerSelector`

#### 2.2.3 策略处理层 (Strategy Processing Layer)
- **位置**: `cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/service/dispatch/event/impl/`
- **职责**: 执行策略预筛、复筛、触达分发逻辑
- **核心类**: `StrategyEventDispatchServiceImpl`

#### 2.2.4 渠道适配层 (Channel Adapter Layer)
- **位置**: `cdp-domain/src/main/java/com/xftech/cdp/domain/dispatch/impl/`
- **职责**: 适配不同营销渠道的发送逻辑
- **核心类**: `EventDispatchServiceImpl`

#### 2.2.5 外部服务调用层 (External Service Layer)
- **位置**: `cdp-infrastructure/src/main/java/com/xftech/cdp/infrastructure/client/`
- **职责**: 封装对外部服务的调用
- **核心类**: `SmsClient`, `TelemarketingClient`, `CouponClient`等

## 3. MQ消息消费入口分析

### 3.1 消息消费配置 (基于Apollo配置)

#### 3.1.1 RabbitMQ配置
```properties
# 业务事件队列配置 (来源: /Users/<USER>/IdeaProjects/xyf-cdp/docs/麻雀apollo配置.txt)
biz.event.hl.exchange = exchange_biz_event_high_level_topic
biz.event.hl.exchangeType = topic
biz.event.hl.routingKey = key_biz_event_high_level_xyf_cdp
biz.event.hl.queue.name = queue_biz_event_high_level_xyf_cdp

biz.event.ml.exchange = exchange_biz_event_middle_level_topic
biz.event.ml.exchangeType = topic
biz.event.ml.routingKey = key_biz_event_middle_level_xyf_cdp
biz.event.ml.queue.name = queue_biz_event_middle_level_xyf_cdp
```

#### 3.1.2 消费者配置
```properties
# 批量消费配置
spring.rabbitmq.biz.prefetchSize = 3
spring.rabbitmq.biz.batchSize = 95
spring.rabbitmq.biz.timeout = 5000
```

### 3.2 消息消费流程

#### 3.2.1 消息接收与解析
- **入口**: 各个MQ消费者类的`@RabbitListener`方法
- **消息格式**: JSON格式的业务事件消息
- **关键字段**:
  - `bizEventType`: 业务事件类型
  - `creditUserId`: 用户ID
  - `mobile`: 手机号
  - `app`: 应用标识

## 4. 预筛阶段详细分析 (prescreen)

### 4.1 预筛入口与数据初始化

#### 4.1.1 方法签名与位置
```java
// 位置: StrategyEventDispatchServiceImpl.java:193
@Override
public void prescreen(String messageId, BizEventMessageVO bizEventMessageVO)
```

#### 4.1.2 策略匹配逻辑
```java
// 位置: StrategyEventDispatchServiceImpl.java:199
List<StrategyMarketEventDo> marketEventList = cacheStrategyMarketEventService.getByEventName(bizEventMessageVO.getBizEventType());
```
- **数据表**: `strategy_market_event`
- **缓存策略**: 使用Caffeine缓存，提高查询性能
- **查询条件**: 根据`bizEventType`查询对应的策略配置

#### 4.1.3 策略预筛处理
```java
// 位置: StrategyEventDispatchServiceImpl.java:228
this.strategyPrescreenHandler(eventContext);
```
**核心逻辑**:
1. 检查策略状态是否为运行中
2. 验证策略有效期
3. 检查策略执行时间窗口
4. 验证用户是否在策略目标用户范围内

#### 4.1.4 引擎策略灰度判定
```java
// 位置: StrategyEventDispatchServiceImpl.java:230-243
if (eventContext.getStrategyDo().isEngineStrategy()) {
    String engineCode = eventContext.getBizEventVO().getEngineCode();
    if (!isInEngineGrayGroup(eventContext, group)) {
        eventContext.getBizEventVO().setIfIntoEngine(false);
    }
}
```
- **灰度配置**: 基于用户ID和策略ID进行哈希计算
- **灰度比例**: 通过Apollo配置动态调整

### 4.2 事件预筛处理

#### 4.2.1 事件预筛入口
```java
// 位置: StrategyEventDispatchServiceImpl.java:245
this.eventPrescreenHandler(eventContext);
```

#### 4.2.2 子事件类型判断
```java
// 位置: StrategyEventDispatchServiceImpl.java:331
Map<Integer, List<StrategyMarketSubEventDo>> marketSubEventMap = strategyMarketSubEventService.getByEventId(marketEventDo.getId());
```
- **数据表**: `strategy_market_sub_event`
- **业务逻辑**: 支持一个主事件下配置多个子事件类型
- **筛选条件**: 根据子事件条件进一步筛选用户

### 4.3 人群预筛处理

#### 4.3.1 人群预筛入口
```java
// 位置: StrategyEventDispatchServiceImpl.java:247
this.crowdPrescreenHandler(eventContext, bizEventMessageVO);
```

#### 4.3.2 人群包查询与验证
```java
// 位置: AbstractStrategyEventDispatchService.java:497
if (!crowdPackService.verifyCrowdPackByNewRandom(appUserId, crowdContent.values().stream().map(CrowdContext::getCrowdPack).collect(Collectors.toList()))) {
    return Pair.of(Boolean.FALSE, null);
}
```
- **数据表**: `crowd_pack`, `strategy_crowd_pack`, `crowd_detail_*`
- **验证逻辑**:
  1. 查询策略关联的人群包
  2. 验证用户是否在人群包中
  3. 支持洞察平台人群包接口查询

#### 4.3.3 洞察平台人群包支持
```java
// 位置: AbstractStrategyEventDispatchService.java:503
if (crowdInfoService.isCheckByInsightPlatform(crowdPackIds)) {
    return crowdInfoService.checkByInsightPlatform(crowdPackIds, appUserId);
}
```
- **外部接口**: 洞察平台人群包查询接口
- **配置**: `InsightCrowdPack.WhiteList.CrowdPack`白名单控制

### 4.4 延迟处理逻辑

#### 4.4.1 延迟处理入口
```java
// 位置: StrategyEventDispatchServiceImpl.java:251
this.delayHandler(eventContext);
```

#### 4.4.2 延迟队列投递
- **延迟时间**: 根据策略配置的延迟时间
- **MQ配置**: 使用RabbitMQ的延迟队列功能
- **队列配置**:
  ```properties
  biz.event.delay.exchange = exchange_biz_event_delay_topic
  biz.event.delay.exchangeType = x-delayed-message
  ```

## 5. 复筛阶段详细分析 (rescreen)

### 5.1 复筛入口与状态检查

#### 5.1.1 方法签名与位置
```java
// 位置: StrategyEventDispatchServiceImpl.java:483
@Override
public void rescreen(BizEventVO event)
```

#### 5.1.2 策略状态验证
```java
// 位置: StrategyEventDispatchServiceImpl.java:489
StrategyDo strategyDo = strategyRepository.selectById(event.getStrategyId());
if (StrategyStatusEnum.getPausedCodes().contains(strategyDo.getStatus())) {
    log.info("策略状态:{},不执行复筛流程", StrategyStatusEnum.getInstance(strategyDo.getStatus()).getDescription());
    return;
}
```

### 5.2 引擎决策处理

#### 5.2.1 引擎预测调用
```java
// 位置: StrategyEventDispatchServiceImpl.java:602
event.setIfMarket(predictDecisionDto.ifMarket());
```
- **外部接口**: 决策引擎预测接口
- **返回结果**: 是否进行营销触达的决策结果

#### 5.2.2 实时标签查询
```java
// 位置: StrategyEventDispatchServiceImpl.java:606
this.queryLabelHandler(eventContext);
```
- **外部接口**: 数仓实时标签查询接口
- **配置**: `cdp.ads.host = http://api-dps.xinfei.io`
- **标签类型**: 用户属性标签、行为标签、风险标签等

#### 5.2.3 策略复筛逻辑
```java
// 位置: StrategyEventDispatchServiceImpl.java:608
this.rescreeningHandler(eventContext);
```
- **数据表**: `strategy_market_event_condition`
- **筛选条件**: 基于实时标签的条件表达式
- **执行引擎**: 使用规则引擎进行条件匹配

## 6. 触达阶段详细分析 (dispatch)

### 6.1 触达入口与时间校验

#### 6.1.1 方法签名与位置
```java
// 位置: StrategyEventDispatchServiceImpl.java:1040
@Override
public void dispatch(BizEventVO event)
```

#### 6.1.2 触达时间校验
```java
// 位置: StrategyEventDispatchServiceImpl.java:1042-1051
StrategyDo strategyDo = cacheStrategyService.selectById(event.getStrategyId());
if (strategyDo != null && strategyDo.getDispatchConfig() != null) {
    DispatchConfig dispatchConfig = JsonUtil.parse(strategyDo.getDispatchConfig(), DispatchConfig.class);
    if (dispatchConfig != null && !dispatchConfig.isInTime(LocalDateTime.now())) {
        log.info("不在有效的触达时间内, 丢弃不进行触达");
        return;
    }
}
```
- **配置字段**: `strategy.dispatch_config`
- **时间窗口**: 支持按小时、星期配置触达时间窗口

#### 6.1.3 用户信息转换
```java
// 位置: StrategyEventDispatchServiceImpl.java:1054-1056
CrowdDetailDo crowdDetail = convertToCrowdDetail(event);
CrowdDetailDo newCrowd = abstractAdsStrategyLabelService.convertCrowdOne(crowdDetail, event.getUserConvert());
```
- **转换逻辑**: 支持fxk老客转xyf01的用户转换
- **方法位置**: `AbstractAdsStrategyLabelService.convertCrowdOne()`

### 6.2 流控检查机制

#### 6.2.1 流控入口
```java
// 位置: StrategyEventDispatchServiceImpl.java:1060
boolean flcRet = dispatchFlcService.dispatchFlc(event, newCrowd, this);
```

#### 6.2.2 流控开关控制
```java
// 位置: DispatchFlcService.java:70
boolean switchFlag = appConfigService.getSingleDispatchFlcSwitch(marketChannelEnum);
if (!Objects.equals(true, switchFlag)) {
    return false;
}
```
- **Apollo配置**: `singleDispatchFlc.1 = false` (短信渠道流控开关)

#### 6.2.3 流控规则执行
```java
// 位置: DispatchFlcService.java:83
List<CrowdDetailDo> crowdDetailList = abstractStrategyEventDispatchService.flowCtrl(messageId, triggerDatetime, channelDo, crowdDetail, sucStatus, bizEventType);
```
- **流控表**: `flow_ctrl`
- **流控维度**: 用户维度、策略维度、渠道维度、时间维度
- **流控算法**: 滑动窗口、令牌桶等

### 6.3 策略配置查询

#### 6.3.1 配置查询
```java
// 位置: StrategyEventDispatchServiceImpl.java:1066
Triple<StrategyGroupDo, StrategyMarketChannelDo, StrategyExecLogDo> triple = this.getStrategyConfig(event.getStrategyGroupId(), marketChannelEnum);
```
- **数据表**: `strategy_group`, `strategy_market_channel`, `strategy_exec_log`

#### 6.3.2 分发参数封装
```java
// 位置: StrategyEventDispatchServiceImpl.java:1068
DispatchDto dispatchDto = this.convertToDispatchDto(event, triple);
```

## 7. execSend方法核心实现分析

### 7.1 方法签名与返回值

#### 7.1.1 方法签名
```java
// 位置: StrategyEventDispatchServiceImpl.java:1148
private ImmutableTriple<Integer, EventPushBatchDo, Boolean> execSend(
    DispatchDto dispatchDto,
    CrowdDetailDo crowdDetail,
    StrategyMarketChannelEnum channelEnum,
    StrategyMarketChannelDo channelDo,
    BizEventVO bizEvent)
```

#### 7.1.2 返回值说明
- **Integer**: 发送数量 (成功发送的用户数量)
- **EventPushBatchDo**: 批次记录对象 (记录本次发送的批次信息)
- **Boolean**: 是否发生限流 (true表示触发了流控限制)

### 7.2 用户信息转换预处理

#### 7.2.1 用户信息转换
```java
// 位置: StrategyEventDispatchServiceImpl.java:1150
if (Objects.equals(Boolean.TRUE, this.convertUserInfo(crowdDetail, channelDo))) {
```

#### 7.2.2 convertUserInfo方法详细实现
```java
// 位置: StrategyEventDispatchServiceImpl.java:1742-1767
private Boolean convertUserInfo(CrowdDetailDo crowdDetail, StrategyMarketChannelDo marketChannel) {
    String dispatchApp = marketChannel.getDispatchApp();
    if (StringUtils.isBlank(dispatchApp) || StringUtils.equalsIgnoreCase(dispatchApp, "default")) {
        return Boolean.TRUE;
    }

    // 切换触达app逻辑
    crowdDetail.setApp(dispatchApp);
    if (StringUtils.isBlank(crowdDetail.getMobile())) {
        UserIdResp users = userCenterClient.getUsersById(crowdDetail.getUserId());
        users.getUserList().stream().findFirst().map(UserIdDetailResp::getMobile).ifPresent(crowdDetail::setMobile);
    }
    UserInfoResp resp = userCenterClient.getUserByMobile(crowdDetail.getMobile(), crowdDetail.getApp());
    if (Objects.isNull(resp)) {
        return Boolean.FALSE;
    }
    crowdDetail.setUserId(resp.getCreditUserId());
    return Boolean.TRUE;
}
```
- **外部接口**: 用户中心接口 `userCenterClient`
- **配置**: `php.user.host = http://api.xinfei.io/user`
- **业务逻辑**: 支持跨app用户转换，如fxk用户转换为xyf01用户

### 7.3 渠道分发Switch逻辑

#### 7.3.1 渠道分发核心代码
```java
// 位置: StrategyEventDispatchServiceImpl.java:1151-1155
switch (channelEnum) {
    case SMS:
        dispatchResult = eventDispatchService.sendSmsEvent(dispatchDto,
            crowdDetail.getApp(), crowdDetail.getInnerApp(), crowdDetail,
            bizEvent, channelDo.getExtInfo());
        break;
    case VOICE:
        // 电销渠道处理
    case COUPON:
        // 优惠券渠道处理
    // ... 其他渠道
}
```

## 8. 各渠道详细实现分析

### 8.1 短信渠道 (SMS) 实现

#### 8.1.1 短信发送入口
```java
// 位置: EventDispatchServiceImpl.java:118
@Override
public ImmutableTriple<Integer, EventPushBatchDo, Boolean> sendSmsEvent(
    DispatchDto reach, String app, String innerApp, CrowdDetailDo crowdDetail,
    BizEventVO bizEvent, String extInfo)
```

#### 8.1.2 模板参数处理
```java
// 位置: EventDispatchServiceImpl.java:120
Map<String, Object> tempParam = templateParamService.getSmsTempParam(
    crowdDetail, reach.getStrategyId(), reach.getStrategyMarketChannelTemplateId(), bizEvent);
```
- **服务类**: `TemplateParamService`
- **参数来源**: 用户标签、策略配置、事件参数
- **模板表**: `strategy_market_channel_template`

#### 8.1.3 短信发送参数构建
```java
// 位置: EventDispatchServiceImpl.java:122-136
SmsSingleSendArgs args = new SmsSingleSendArgs();
args.setMobile(crowdDetail.getMobile());
args.setTemplateId(reach.getStrategyMarketChannelTemplateId());
args.setData(CollectionUtils.isEmpty(tempParam) ? null : tempParam);
args.setApp(app);
args.setInnerApp(innerApp);
args.setUserNo(crowdDetail.getUserId());
```

#### 8.1.4 外部短信服务调用
```java
// 位置: EventDispatchServiceImpl.java:135
SmsSingleSendResp resp = smsClient.sendSingleSms(requester);
```
- **外部接口**: 短信服务接口
- **配置**: `cdp.sms.host = http://sms.xinfei.io`
- **返回结果**: 包含发送状态、批次号等信息

### 8.2 电销渠道 (VOICE) 实现

#### 8.2.1 电销发送入口
```java
// 位置: EventDispatchServiceImpl.java:154
@Override
public ImmutableTriple<Integer, EventPushBatchDo, Boolean> sendTeleEvent(
    DispatchDto reach, String app, String innerApp, CrowdDetailDo crowdDetail)
```

#### 8.2.2 电销参数构建
```java
// 位置: EventDispatchServiceImpl.java:157-162
TeleSaveBatchArgs args = new TeleSaveBatchArgs();
args.setCreditIdArr(userIdList);
args.setUserType(Integer.valueOf(reach.getStrategyMarketChannelTemplateId()));
args.setFlowNo(batchNum);
args.setBatchCount(1);
```

#### 8.2.3 外部电销服务调用
- **外部接口**: 电销服务接口
- **配置**: `cdp.tele.host = http://inner-api-telemarketing-backend.xinfei.io/cdp`
- **新版配置**: `cdp.tele.newHost = http://telemkt.xinfei.io`

### 8.3 优惠券渠道 (COUPON) 实现

#### 8.3.1 优惠券发送逻辑
- **外部接口**: 优惠券服务接口
- **配置**: `cdp.coupon.host = http://inner-coupon-api.xinyongfei.io`
- **新版配置**: `cdp.coupon.newHost = http://userassetcore.xinfei.io`

#### 8.3.2 优惠券回调处理
```properties
# 优惠券明细回传队列配置
coupon.callback.exchange = exchange_batch_coupon_callback_send_topic
coupon.callback.routingKey = coupon_center_callback_app_xyf_cdp
coupon.callback.queue.name = coupon_center_cash_coupon_cdp_process
```

### 8.4 推送渠道 (PUSH) 实现

#### 8.4.1 推送发送入口
```java
// 位置: EventDispatchServiceImpl.java:380
@Override
public ImmutableTriple<Integer, EventPushBatchDo, Boolean> sendPushEvent(
    DispatchDto reach, String app, String innerApp, CrowdDetailDo crowdDetail, BizEventVO bizEvent)
```

#### 8.4.2 推送参数处理
```java
// 位置: EventDispatchServiceImpl.java:381
Map<String, Object> tempParam = templateParamService.getPushTempParam(
    crowdDetail, reach.getStrategyId(), reach.getStrategyMarketChannelTemplateId(), bizEvent);
```

#### 8.4.3 推送服务配置
```properties
# 推送服务配置
xf.push-service.url = http://sms.xinfei.io
strategy.dispatch.channel.push.pagesize = 1000
```

### 8.5 AI智能外呼渠道 (AI_PRONTO) 实现

#### 8.5.1 AI外呼入口
```java
// 位置: EventDispatchServiceImpl.java:456
@Override
public ImmutableTriple<Integer, EventPushBatchDo, Boolean> sendAiProntoEvent(
    DispatchDto reach, String app, String innerApp, CrowdDetailDo crowdDetail)
```

#### 8.5.2 AI参数处理
```java
// 位置: EventDispatchServiceImpl.java:457
Map<String, Object> tempParam = templateParamService.getAiTempParam(
    crowdDetail, reach.getStrategyId(), reach.getAiProntoChannelDto());
```

#### 8.5.3 AI服务配置
```properties
# AI外呼服务配置
xf.call-service.url = http://call.xinfei.io
strategy.dispatch.channel.ai.pronto.pagesize = 200
```

## 9. 流控机制详细分析

### 9.1 流控核心实现

#### 9.1.1 流控入口方法
```java
// 位置: AbstractStrategyEventDispatchService.java:622
public List<CrowdDetailDo> flowCtrl(String messageId, LocalDateTime triggerDatetime,
    StrategyMarketChannelDo channelDo, CrowdDetailDo crowdDetail, List<Integer> statusList, String bizEventType)
```

#### 9.1.2 流控规则查询
```java
// 位置: AbstractStrategyEventDispatchService.java:625
List<FlowCtrlDo> flowCtrlRule = flowCtrlCoreService.getFlowCtrlRule(
    channelDo.getStrategyId(), channelDo.getMarketChannel(), bizType);
```
- **数据表**: `flow_ctrl`
- **规则维度**: 策略ID、渠道类型、业务线类型

#### 9.1.3 流控参数构建
```java
// 位置: AbstractStrategyEventDispatchService.java:626-635
FlowCtrlDto flowCtrlDto = new FlowCtrlDto();
flowCtrlDto.setTableNo(getTableNo(triggerDatetime));
flowCtrlDto.setMarketChannelDo(channelDo);
flowCtrlDto.setList(Collections.singletonList(crowdDetail));
flowCtrlDto.setFlowCtrlRuleList(flowCtrlRule);
flowCtrlDto.setMessageId(messageId);
flowCtrlDto.setTriggerDatetime(triggerDatetime);
flowCtrlDto.setStrategyRulerEnum(StrategyRulerEnum.EVENT);
flowCtrlDto.setBizEventType(bizEventType);
```

### 9.2 分布式流控实现

#### 9.2.1 分布式锁流控
```java
// 位置: DispatchFlcService.java:100
public boolean dispatchFlcLock(DispatchDto reach, EventPushBatchDo eventPushBatch,
    UserDispatchDetailDo dispatchDetail, String batchNum, CrowdDetailDo crowdDetail,
    AbstractStrategyEventDispatchService abstractStrategyEventDispatchService)
```

#### 9.2.2 Redis分布式锁
```java
// 位置: DispatchFlcService.java:138
String lockValue = String.valueOf(System.currentTimeMillis());
tryLock(userId, lockValue);
```
- **锁粒度**: 用户级别
- **锁超时**: 防止死锁
- **锁释放**: 自动释放机制

### 9.3 流控配置管理

#### 9.3.1 流控开关配置
```properties
# 复筛常规流控关闭配置
singleDispatchFlc.1 = false  # 短信渠道
singleDispatchFlc.2 = false  # 电销渠道
singleDispatchFlc.3 = false  # 优惠券渠道
singleDispatchFlc.4 = false  # 其他渠道
```

#### 9.3.2 事件流控配置
```properties
# 用户事件限流配置
eventFlcConfig = {
    "Start": 600,
    "Login": 600,
    "Start1IncreaseCreditLimit": 600,
    "RepaySuccess": 600
}
```
## 10. 数据流转与存储分析

### 10.1 核心数据表结构

#### 10.1.1 事件推送批次表 (event_push_batch)
- **表作用**: 记录每次推送的批次信息
- **关键字段**:
  - `batch_num`: 批次号 (唯一标识)
  - `strategy_id`: 策略ID
  - `strategy_group_id`: 策略分组ID
  - `market_channel`: 营销渠道
  - `send_count`: 发送数量
  - `success_count`: 成功数量
  - `fail_count`: 失败数量
  - `create_time`: 创建时间

#### 10.1.2 用户触达明细表 (user_dispatch_detail)
- **表作用**: 记录具体的用户触达详情
- **分表策略**: 按时间分表 `user_dispatch_detail_YYYYMM`
- **关键字段**:
  - `user_id`: 用户ID
  - `mobile`: 手机号
  - `strategy_id`: 策略ID
  - `batch_num`: 批次号
  - `market_channel`: 营销渠道
  - `send_status`: 发送状态
  - `response_code`: 响应码
  - `response_msg`: 响应消息
  - `biz_event_type`: 业务事件类型

#### 10.1.3 策略执行日志表 (strategy_exec_log)
- **表作用**: 记录策略执行状态和统计信息
- **关键字段**:
  - `strategy_id`: 策略ID
  - `exec_date`: 执行日期
  - `total_count`: 总数量
  - `success_count`: 成功数量
  - `fail_count`: 失败数量
  - `exec_status`: 执行状态

### 10.2 数据写入流程

#### 10.2.1 批次记录写入
```java
// 位置: UserDispatchDetailService.saveEventDispatchDetail()
EventPushBatchDo eventPushBatch = new EventPushBatchDo();
eventPushBatch.setBatchNum(batchNum);
eventPushBatch.setStrategyId(reach.getStrategyId());
eventPushBatch.setMarketChannel(reach.getStrategyChannel());
// ... 其他字段设置
eventPushBatchRepository.save(eventPushBatch);
```

#### 10.2.2 用户明细记录写入
```java
// 位置: StrategyEventDispatchServiceImpl.java:1726-1729
UserDispatchDetailDo dispatchDetail = new UserDispatchDetailDo();
dispatchDetail.setBizEventType(bizEventVO.getBizEventType());
dispatchDetail.setBizType(getBizType(bizEventVO.getStrategyId()));
getUserDispatchDetailRepository().saveBatchWithoutTx(detailTableNo, Collections.singletonList(dispatchDetail));
```

### 10.3 外部接口调用详细分析

#### 10.3.1 短信服务接口
- **接口地址**: `http://sms.xinfei.io`
- **调用方式**: HTTP POST
- **请求参数**:
  ```json
  {
    "mobile": "手机号",
    "templateId": "模板ID",
    "data": "模板参数",
    "app": "应用标识",
    "innerApp": "内部应用标识",
    "userNo": "用户ID"
  }
  ```
- **响应格式**:
  ```json
  {
    "success": true,
    "status": "200",
    "message": "发送成功",
    "response": {
      "batchNum": "批次号"
    }
  }
  ```

#### 10.3.2 电销服务接口
- **接口地址**: `http://inner-api-telemarketing-backend.xinfei.io/cdp`
- **新版地址**: `http://telemkt.xinfei.io`
- **调用方式**: HTTP POST
- **请求参数**:
  ```json
  {
    "creditIdArr": ["用户ID列表"],
    "userType": "用户类型",
    "flowNo": "流水号",
    "batchCount": "批次数量"
  }
  ```

#### 10.3.3 用户中心接口
- **接口地址**: `http://api.xinfei.io/user`
- **主要方法**:
  - `getUsersById()`: 根据用户ID查询用户信息
  - `getUserByMobile()`: 根据手机号查询用户信息

#### 10.3.4 数仓接口
- **接口地址**: `http://api-dps.xinfei.io`
- **主要功能**: 实时标签查询
- **调用方法**: `adsStrategyLabelService.query()`

## 11. 异常处理与错误码分析

### 11.1 异常处理机制

#### 11.1.1 策略异常处理
```java
// 位置: StrategyEventDispatchServiceImpl.java:1076-1084
try {
    dispatchResult = this.execSend(dispatchDto, newCrowd, marketChannelEnum, triple.getMiddle(), event);
} catch (StrategyException e) {
    errorMesg = e.getMessage();
    List<String> ignoreErrorMsg = getAppConfigService().getIgnoreDispatchErrorMsgAlarm(marketChannelEnum);
    if (!CollectionUtils.isEmpty(ignoreErrorMsg) && ignoreErrorMsg.contains(e.getMessage())) {
        isIgnoreStrategyException = true;
    }
}
```

#### 11.1.2 忽略错误配置
```properties
# 告警忽略配置
dispatch.reponse.ignore.failedCodes.1 = ["499110", "499104"]
dispatch.ignore.errmsg.1 = ["模板参数不全，终止发送"]
dispatch.reponse.ignore.failedCodes.5 = [-1]
dispatch.ignore.errmsg.5 = ["查询push模板详情接口失败"]
```

### 11.2 重试机制

#### 11.2.1 MQ消费重试
- **重试次数**: 根据MQ配置
- **重试间隔**: 指数退避策略
- **死信队列**: 超过重试次数后进入死信队列

#### 11.2.2 外部接口重试
- **HTTP超时配置**:
  ```properties
  xyf.http.connectionRequestTimeout = 20000
  xyf.http.socketTimeout = 20000
  ```

### 11.3 降级策略

#### 11.3.1 外部服务降级
- **熔断机制**: 当外部服务不可用时触发熔断
- **降级处理**: 记录失败日志，不影响主流程

#### 11.3.2 流控降级
- **流控触发**: 当触发流控时，直接返回不进行后续处理
- **流控记录**: 记录流控日志用于监控分析

## 12. 监控与链路追踪

### 12.1 链路追踪配置

#### 12.1.1 链路追踪开关
```properties
# 链路追踪配置
xyf.trace.report.enable = true
xyf.brave.zipkin.endpoint = https://gz-trace.cn-beijing.log.aliyuncs.com/zipkin/api/v2/spans
xyf.trace.sls.otel.project = prod-gz-java-cdp
xyf.trace.sls.otel.instance.id = prod-gz-java-cdp
```

#### 12.1.2 关键埋点
```java
// 位置: StrategyEventDispatchServiceImpl.java:195
Tracer.logEvent("T0Events", bizEventMessageVO.getBizEventType());

// 位置: StrategyEventDispatchServiceImpl.java:1047
Tracer.logEvent("dispatchReject", String.valueOf(event.getStrategyId()));
```

### 12.2 性能监控指标

#### 12.2.1 关键监控点
- **消息消费延迟**: MQ消息从发送到消费的时间差
- **预筛通过率**: 通过预筛的用户比例
- **复筛通过率**: 通过复筛的用户比例
- **触达成功率**: 成功触达的用户比例
- **外部接口调用成功率**: 各外部接口的调用成功率
- **流控拦截率**: 被流控拦截的请求比例

#### 12.2.2 告警配置
```properties
# 钉钉告警配置
dingtalk.alarmUrl = https://oapi.dingtalk.com/robot/send?access_token=xxx
dingtalk.atMobile = 18502198054

# 自定义任务执行时长告警
alarm.custom.task.duration = {
    "1954":190,
    "1991":250,
    "2180":70,
    "2264":65
}
```
## 13. 性能优化与缓存策略

### 13.1 缓存策略分析

#### 13.1.1 策略配置缓存
```java
// 位置: CacheStrategyService
// 缓存策略配置，减少数据库查询
@Cacheable(value = "strategy", key = "#strategyId")
public StrategyDo selectById(Long strategyId)
```
- **缓存框架**: Caffeine
- **缓存时间**: 根据配置动态调整
- **缓存更新**: 策略配置变更时自动刷新

#### 13.1.2 人群包缓存
```java
// 位置: CacheCrowdPackService
// 缓存人群包配置，提高查询性能
@Cacheable(value = "crowdPack", key = "#crowdPackId")
public CrowdPackDo selectById(Long crowdPackId)
```

#### 13.1.3 标签数据缓存
- **Redis缓存**: 用户实时标签数据
- **缓存键**: `label:{userId}:{labelCode}`
- **过期时间**: 根据标签类型设置不同过期时间

### 13.2 线程池配置优化

#### 13.2.1 人群处理线程池
```properties
# 人群处理线程池配置
crowd.pool.poolName = corwdwerehouse
crowd.pool.corePoolSize = 8
crowd.pool.maximumPoolSize = 20
crowd.pool.keepAliveTime = 300
crowd.pool.queueSize = 2000
```

#### 13.2.2 流控处理线程池
```properties
# 离线人群包限流模块线程池配置
dispatchFlcExecutor.pool.coreSize = 8
dispatchFlcExecutor.pool.maxSize = 20
```

#### 13.2.3 变量查询线程池
```properties
# 变量查询线程池配置
dispatchVariableExecutor.pool.coreSize = 80
dispatchVariableExecutor.pool.maxSize = 100
```

#### 13.2.4 任务执行线程池
```properties
# 任务执行线程池配置
dispatchTaskExecutor.pool.coreSize = 6
dispatchTaskExecutor.pool.maxSize = 200
```

### 13.3 批处理优化

#### 13.3.1 批量查询优化
- **标签批量查询**: 一次查询多个用户的标签数据
- **人群包批量验证**: 批量验证用户是否在人群包中
- **数据库批量操作**: 减少数据库连接开销

#### 13.3.2 分页处理配置
```properties
# 各渠道分页大小配置
strategy.dispatch.channel.sms.pagesize = 1000
strategy.dispatch.channel.tele.pagesize = 1500
strategy.dispatch.channel.coupon.pagesize = 1000
strategy.dispatch.channel.push.pagesize = 1000
strategy.dispatch.channel.ai.pronto.pagesize = 200
```

## 14. 灰度发布与AB测试

### 14.1 灰度配置管理

#### 14.1.1 通用灰度开关
```properties
# 通用灰度开关配置
common.gray.switch = {
    "newFlowCtrlSwitch": {
        "percentNum": 100,
        "whitelist": "1319"
    },
    "offlineEngineFilterSwitch": {
        "percentNum": 0,
        "whitelist": "0"
    }
}
```

#### 14.1.2 特征平台灰度
```properties
# 特征平台变量灰度配置
adb.realTime.variable.gray = {
    "allHits": false,
    "variableList": ["user_cur_available_balance"]
}
```

### 14.2 AB测试机制

#### 14.2.1 随机数服务
- **服务作用**: 为AB测试提供一致性随机数
- **算法**: 基于用户ID和业务键的哈希算法
- **一致性**: 同一用户在同一业务场景下始终返回相同随机数

#### 14.2.2 分组策略
```java
// 位置: StrategyGroupService.matchGroupRule()
// 根据随机数和分组规则进行用户分组
BiPredicate<String, Integer> matchFun = strategyGroupDo.match(StrategyGroupTypeEnum.getInstance(strategyDo.getAbType()));
```

## 15. 业务规则与决策引擎

### 15.1 决策引擎集成

#### 15.1.1 引擎预测接口
- **接口地址**: `http://open-business-engine.xinfei.io/marketing_model/prediction`
- **认证方式**: `authorization = 9a0f420b2c324cfcb7b23a49d5932c28`
- **功能**: 基于用户特征进行营销决策

#### 15.1.2 模型列表接口
- **接口地址**: `http://business-engine-manage.xinfei.io/model/list`
- **功能**: 获取可用的决策模型列表

#### 15.1.3 引擎预测中心
- **接口地址**: `http://enginepredictcenter.xinfei.io`
- **功能**: 统一的引擎预测服务

### 15.2 业务规则配置

#### 15.2.1 最小值限制配置
```properties
# 标签最小值限制
minValue.user_cur_available_balance = 499.99
minValue.available_amt_appuser_id = 499.99
minValue.label = {
    "available_amt_appuser_id": 99.99,
    "last_increase_temporary_amount": 0,
    "user_cur_available_balance": 499.99
}
```

#### 15.2.2 特殊时间标签配置
```properties
# 特殊开始时间标签
flowSpecialStartTimeLabels = ["is_last_order_time_equal_or_after_starttime", "not_login"]
```

## 16. 总结与架构优势

### 16.1 架构设计优势

#### 16.1.1 分层清晰
- **消息消费层**: 负责MQ消息接收和基础校验
- **业务处理层**: 负责策略预筛、复筛、触达逻辑
- **渠道适配层**: 负责不同营销渠道的适配
- **外部服务层**: 负责外部接口调用封装

#### 16.1.2 高可扩展性
- **渠道扩展**: 新增营销渠道只需实现对应的适配器
- **策略扩展**: 支持灵活的策略配置和规则引擎
- **标签扩展**: 支持动态标签配置和实时查询

#### 16.1.3 高可用性
- **流控保护**: 多维度流控保护系统稳定性
- **异常处理**: 完善的异常处理和降级机制
- **监控告警**: 全链路监控和实时告警

### 16.2 关键技术特点

#### 16.2.1 实时性
- **实时标签**: 支持实时标签查询和决策
- **实时流控**: 实时流控保护下游服务
- **实时监控**: 实时监控和告警机制

#### 16.2.2 一致性
- **分布式锁**: 保证并发场景下的数据一致性
- **事务管理**: 合理的事务边界设计
- **幂等性**: 支持消息重复消费的幂等处理

#### 16.2.3 性能优化
- **缓存策略**: 多层缓存提高查询性能
- **批处理**: 批量处理提高吞吐量
- **线程池**: 合理的线程池配置提高并发能力

### 16.3 业务价值

#### 16.3.1 营销效率提升
- **精准触达**: 基于用户标签和行为的精准营销
- **实时响应**: T0实时触达提高营销时效性
- **多渠道支持**: 支持短信、电销、推送等多种渠道

#### 16.3.2 风险控制
- **流控保护**: 防止营销过度骚扰用户
- **规则引擎**: 灵活的业务规则配置
- **监控告警**: 及时发现和处理异常情况

#### 16.3.3 数据驱动
- **全链路追踪**: 完整的营销链路数据记录
- **效果分析**: 支持营销效果的数据分析
- **持续优化**: 基于数据反馈持续优化策略

T0-普通触达execSend链路作为整个营销系统的核心执行引擎，通过精心设计的分层架构、完善的异常处理机制、高效的性能优化策略，实现了高可用、高性能、高扩展性的营销触达能力，为业务的快速发展提供了强有力的技术支撑。
