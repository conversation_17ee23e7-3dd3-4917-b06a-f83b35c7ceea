package com.xinfei.touch.domain.model;

import lombok.Getter;

/**
 * 触达状态枚举
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
public enum TouchStatus {
    
    /**
     * 待处理
     */
    PENDING(0, "待处理"),
    
    /**
     * 成功
     */
    SUCCESS(1, "成功"),
    
    /**
     * 失败
     */
    FAILED(2, "失败"),
    
    /**
     * 流控拦截
     */
    FLOW_CONTROLLED(3, "流控拦截"),
    
    /**
     * 处理中
     */
    PROCESSING(4, "处理中"),
    
    /**
     * 已取消
     */
    CANCELLED(5, "已取消"),

    /**
     * 部分成功
     */
    PARTIAL_SUCCESS(6, "部分成功");
    
    /**
     * 状态编码
     */
    private final Integer code;
    
    /**
     * 状态名称
     */
    private final String name;
    
    TouchStatus(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
    
    /**
     * 根据编码获取状态
     */
    public static TouchStatus getByCode(Integer code) {
        for (TouchStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的触达状态编码: " + code);
    }

    /**
     * 根据编码获取状态（兼容方法）
     */
    public static TouchStatus fromCode(Integer code) {
        return getByCode(code);
    }
    
    /**
     * 是否为终态
     */
    public boolean isFinalStatus() {
        return this == SUCCESS || this == FAILED || this == FLOW_CONTROLLED || this == CANCELLED;
    }
    
    /**
     * 是否为成功状态
     */
    public boolean isSuccess() {
        return this == SUCCESS;
    }
    
    /**
     * 是否为失败状态
     */
    public boolean isFailed() {
        return this == FAILED;
    }
    
    /**
     * 是否为流控状态
     */
    public boolean isFlowControlled() {
        return this == FLOW_CONTROLLED;
    }
}
