package com.xftech.cdp.infra.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 人群相关配置
 *
 * <AUTHOR>
 * @since 2023/2/14
 */
@Getter
@RefreshScope
@Component
public class CrowdConfig {

    @Value("${cdp.crowd.alarmUrl:https://oapi.dingtalk.com/robot/send?access_token=e3060e687917359a9d0570468665121de4f1a591b928269ae21c5455e0b3e502}")
    private String alarmUrl;

    @Value("${cdp.crowd.rockPageSize:1000}")
    private Integer rockPageSize;

    @Value("${cdp.crowd.rockPageSizeNew:1000}")
    private Integer rockPageSizeNew;

    @Value("${cdp.crowd.batchSaveSize:500}")
    private Integer batchSaveSize;

    @Value("${cdp.crowd.taskBatchCount:100}")
    private Integer taskBatchCount;

    @Value("${crowd.alarm.job}")
    private Integer alarmJobId;

    @Value("${crowd.Job.stop:true}")
    private boolean crowdJobStop;

    @Value("${notQueryUserLabelDetail:true}")
    private boolean notQueryUserLabelDetail;

    /**
     * 人群用户分表大小
     */
    @Value("${crowd.detail.table.size:5000000}")
    private Integer crowdDetailTableSize;

    /**
     * 人群包刷新超时时间，默认10分钟，单位：分钟
     */
    @Value("${crowd.refresh.timeout.limit:10}")
    private Integer crowdRefreshTimeoutLimit;

    /**
     * 人群包刷新超时At
     */
    @Value("#{'${crowd.refresh.timeout.atMobile:13535548025,18178422265,17520254145}'.split(',')}")
    private List<String> crowdRefreshTimeoutAtMobiles;

    /**
     * 人群包报警手机号--报警到产品
     */
    @Value("#{'${crowd.alarm.cp.atMobile:13046631879,13621921420,18321308699}'.split(',')}")
    private List<String> crowdalarmMobiles;

    /**
     * 人群包生产报警手机号
     */
    @Value("#{'${crowd.alarm.prod.atMobile:13046631879,13681832724,18321308699}'.split(',')}")
    private List<String> crowdalarmProdMobiles;

    @Value("${crowd.execution.time:100}")
    private Integer executionTime;

    @Value("${crowd.batcQueryUserMobile.size:100}")
    private Integer batcQueryUserMobileSize;
}
