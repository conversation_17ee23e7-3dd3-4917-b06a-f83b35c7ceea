package com.xftech.cdp.domain.strategy.service.dispatch.event.impl;

import brave.internal.Nullable;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.proxy.Tracer;

import com.xftech.cdp.api.dto.req.DispatchConfig;
import com.xftech.cdp.api.dto.req.StrategyCreateReq;
import com.xftech.cdp.distribute.crowd.repository.CrowdInfoRepository;
import com.xftech.cdp.distribute.crowd.repository.model.CrowdInfoDo;
import com.xftech.cdp.domain.ads.model.PredictDecisionDto;
import com.xftech.cdp.domain.ads.service.AdsStrategyLabelService;
import com.xftech.cdp.domain.ads.service.ModelPlatformService;
import com.xftech.cdp.domain.ads.service.impl.AbstractAdsStrategyLabelService;
import com.xftech.cdp.domain.cache.CacheStrategyGroupService;
import com.xftech.cdp.domain.cache.CacheStrategyMarketChannelService;
import com.xftech.cdp.domain.cache.CacheStrategyMarketEventService;
import com.xftech.cdp.domain.cache.CacheStrategyService;
import com.xftech.cdp.domain.crowd.model.dispatch.CrowdContext;
import com.xftech.cdp.domain.crowd.model.enums.CrowdFilterMethodEnum;
import com.xftech.cdp.domain.crowd.repository.CrowdPackRepository;
import com.xftech.cdp.domain.dispatch.DispatchFlcService;
import com.xftech.cdp.domain.dispatch.EventDispatchService;
import com.xftech.cdp.domain.dispatch.dto.DispatchDto;
import com.xftech.cdp.domain.dispatch.impl.EventDispatchServiceImpl;
import com.xftech.cdp.domain.mq.MqProducerService;
import com.xftech.cdp.domain.param.service.TemplateParamService;
import com.xftech.cdp.domain.randomnum.RandomNumService;
import com.xftech.cdp.domain.redecision.enums.ReDecisionResultStatus;
import com.xftech.cdp.domain.redecision.service.OfflineReDecisionService;
import com.xftech.cdp.domain.redecision.service.ReDecisionService;
import com.xftech.cdp.domain.strategy.exception.StrategyException;
import com.xftech.cdp.domain.strategy.model.dispatch.StrategyEventCheckContext;
import com.xftech.cdp.domain.strategy.model.dispatch.StrategyLabelCheckContext;
import com.xftech.cdp.domain.strategy.model.dto.DelayedPopupChannelDto;
import com.xftech.cdp.domain.strategy.model.dto.EngineStrategyGroupIdInfoDto;
import com.xftech.cdp.domain.strategy.model.dto.IncreaseAmtParamDto;
import com.xftech.cdp.domain.strategy.model.enums.*;
import com.xftech.cdp.domain.strategy.repository.StrategyExecLogRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyRepository;
import com.xftech.cdp.domain.strategy.service.*;
import com.xftech.cdp.domain.strategy.service.dispatch.event.StrategyEventCatchService;
import com.xftech.cdp.domain.strategy.service.dispatch.event.StrategyEventDispatchService;
import com.xftech.cdp.domain.strategy.service.impl.GoodsServiceImpl;
import com.xftech.cdp.feign.model.GoodsDetail;
import com.xftech.cdp.feign.model.PushUserData;
import com.xftech.cdp.feign.model.SendPushInfo;
import com.xftech.cdp.feign.model.requset.PushBaseRequest;
import com.xftech.cdp.feign.model.requset.SendPushRequest;
import com.xftech.cdp.feign.model.response.PushResponse;
import com.xftech.cdp.infra.client.ads.model.req.ModelPredictionReq;
import com.xftech.cdp.infra.client.appcore.request.BalanceOptReq;
import com.xftech.cdp.infra.client.coupon.model.req.CouponSendBatchReq;
import com.xftech.cdp.infra.client.coupon.model.resp.BaseCouponResponse;
import com.xftech.cdp.infra.client.dingtalk.DingTalkUtil;
import com.xftech.cdp.infra.client.dingtalk.config.DingTalkConfig;
import com.xftech.cdp.infra.client.sms.model.SmsSingleSendArgs;
import com.xftech.cdp.infra.client.sms.model.SmsSingleSendRequester;
import com.xftech.cdp.infra.client.sms.model.resp.SmsSingleSendResp;
import com.xftech.cdp.infra.client.telemarketing.model.TelePushArgs;
import com.xftech.cdp.infra.client.telemarketing.model.TeleSaveBatchArgs;
import com.xftech.cdp.infra.client.telemarketing.model.TeleSaveBatchRequest;
import com.xftech.cdp.infra.client.telemarketing.model.resp.AiSendResp;
import com.xftech.cdp.infra.client.telemarketing.model.resp.TelePushResp;
import com.xftech.cdp.infra.client.telemarketing.model.resp.TeleSaveBatchResp;
import com.xftech.cdp.infra.client.usercenter.UserCenterClient;
import com.xftech.cdp.infra.client.usercenter.model.UserIdDetailResp;
import com.xftech.cdp.infra.client.usercenter.model.UserIdResp;
import com.xftech.cdp.infra.client.usercenter.model.UserInfoResp;
import com.xftech.cdp.infra.config.ApolloUtil;
import com.xftech.cdp.infra.config.AppConfigService;
import com.xftech.cdp.infra.config.LogUtil;
import com.xftech.cdp.infra.constant.RedisKeyConstants;
import com.xftech.cdp.infra.exception.NoneException;
import com.xftech.cdp.infra.rabbitmq.enums.DecisionResultEnum;
import com.xftech.cdp.infra.rabbitmq.factory.impl.BizEventMqService;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventMessageVO;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventVO;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPackDo;
import com.xftech.cdp.infra.repository.cdp.flowctrl.po.FlowCtrlDo;
import com.xftech.cdp.infra.repository.cdp.flowctrl.po.FlowCtrlInterceptionLogDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.*;
import com.xftech.cdp.infra.rocketmq.dto.AiSendArgs;
import com.xftech.cdp.infra.rocketmq.dto.AiUserData;
import com.xftech.cdp.infra.thread.DispatchAlarmExecutor;
import com.xftech.cdp.infra.utils.*;

import com.google.common.base.Stopwatch;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutableTriple;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import static com.xftech.cdp.domain.strategy.model.enums.BusinessTypeEnum.NEW_CUST;
import static com.xftech.cdp.domain.strategy.model.enums.BusinessTypeEnum.OLD_CUST;
import static com.xftech.cdp.domain.strategy.model.enums.IncreaseTypeEnum.PERSONAL_MARKETING_RELOAN_TEMP;
import static com.xftech.cdp.domain.strategy.model.enums.IncreaseTypeEnum.TEMP_CREDIT;
import static com.xftech.cdp.infra.config.ApolloKeyConstants.INSIGHT_CROWD_PACK_WHITELIST_CROWD_PACK;

/**
 * 实时事件策略执行流程
 *
 * @<NAME_EMAIL>
 */
@Slf4j
@Service
@DependsOn("serialNumberUtil")
public class StrategyEventDispatchServiceImpl extends AbstractStrategyEventDispatchService implements StrategyEventDispatchService {

    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private SerialNumberUtil serialNumberUtil;
    @Autowired
    private DingTalkConfig dingTalkConfig;
    @Autowired
    private UserCenterClient userCenterClient;
    @Autowired
    private BizEventMqService bizEventMqService;
    @Autowired
    private MqProducerService mqProducerService;
    @Autowired
    private RandomNumService randomNumService;
    @Autowired
    private EventDispatchService eventDispatchService;
    @Autowired
    private CacheStrategyService cacheStrategyService;
    @Autowired
    private StrategyExecLogService strategyExecLogService;
    @Autowired
    private AdsStrategyLabelService adsStrategyLabelService;
    @Autowired
    private CacheStrategyGroupService cacheStrategyGroupService;
    @Autowired
    private StrategyEventCatchService strategyEventCatchService;
    @Autowired
    private UserBlankGroupDetailService userBlankGroupDetailService;
    @Autowired
    private StrategyMarketSubEventService strategyMarketSubEventService;
    @Autowired
    private CacheStrategyMarketEventService cacheStrategyMarketEventService;
    @Autowired
    private CacheStrategyMarketChannelService cacheStrategyMarketChannelService;
    @Autowired
    private StrategyMarketEventConditionService strategyMarketEventConditionService;
    @Autowired
    private ModelPlatformService modelPlatformService;
    @Autowired
    private UserSendCounterService userSendCounterService;
    @Autowired
    private TemplateParamService templateParamService;
    @Autowired
    private DispatchFlcService dispatchFlcService;
    @Autowired
    private StrategyRepository strategyRepository;
    @Autowired
    private AbstractAdsStrategyLabelService abstractAdsStrategyLabelService;
    @Autowired
    private CrowdPackRepository crowdPackRepository;
    @Autowired
    private StrategyExecLogRepository strategyExecLogRepository;
    @Autowired
    private TelePushService telePushService;
    @Autowired
    private AppConfigService configService;
    @Autowired
    private ReDecisionService reDecisionService;
    @Autowired
    private StrategyEngineService strategyEngineService;
    @Resource
    private CrowdInfoRepository crowdInfoRepository;
    @Autowired
    private GoodsServiceImpl goodsService;
    @Resource
    private OfflineReDecisionService offlineReDecisionService;

    /**
     * 1.预筛
     */
    @Override
    public void prescreen(String messageId, BizEventMessageVO bizEventMessageVO) {
        long startTime = Instant.now().toEpochMilli();
        Tracer.logEvent("T0Events", bizEventMessageVO.getBizEventType());
        log.info("实时策略-预筛开始,事件:{},用户ID:{},消息ID:{}", bizEventMessageVO.getBizEventType(), bizEventMessageVO.getCreditUserId(), messageId);

        // 是否有对应策略配置, 新老客提额区分
        List<StrategyMarketEventDo> marketEventListOrigin = cacheStrategyMarketEventService.getByEventName(bizEventMessageVO.getBizEventType());
        // 提额类型新老客过滤
        List<StrategyMarketEventDo> marketEventList = filterIncreaseAmtByBizType(bizEventMessageVO, marketEventListOrigin);
        if (CollectionUtils.isEmpty(marketEventList)) {
            log.warn("不存在[{}]事件所对应的策略", bizEventMessageVO.getBizEventType());
            log.warn("bizEventMessageVO={}", bizEventMessageVO);
            return;
        }
        log.info("消息ID={}, 事件:{}, 用户ID:{}, 需要处理的策略IDs:{}", messageId, bizEventMessageVO.getBizEventType(), bizEventMessageVO.getCreditUserId(), JsonUtil.toJson(marketEventList.stream()
                .map(StrategyMarketEventDo::getStrategyId).collect(Collectors.toList())));
        for (StrategyMarketEventDo marketEvent : marketEventList) {
            long startTimeStrategy = Instant.now().toEpochMilli();
            BizEventVO event = toBizEventVO(messageId, bizEventMessageVO);
            StrategyEventCheckContext eventContext = new StrategyEventCheckContext(event);
            try {
                log.info("开始单个策略处理, 消息ID={}, 事件:{}, 用户ID:{}, 需要处理的策略ID:{}", messageId, bizEventMessageVO.getBizEventType(), bizEventMessageVO.getCreditUserId(), marketEvent.getStrategyId());
                // 用户信息补全
                strategyMarketEventConditionService.userInfoComplete(eventContext.getBizEventVO(), bizEventMessageVO);
                // 初始化-预筛
                this.initHandler(eventContext, marketEvent, messageId);
                // 分组统计
                StrategyGroupDo group = getAbGroup(eventContext.getBizEventVO().getStrategyId(), eventContext);
                if (group != null) {
                    if (group.getId() != null) {
                        event.setStrategyGroupId(group.getId());
                    }
                    if (group.getName() != null) {
                        event.setGroupName(group.getName().replace("组", ""));
                    }
                }
                // 策略-预筛
                this.strategyPrescreenHandler(eventContext);
                // 如果是决策引擎策略，则进行灰度判定
                if (eventContext.getStrategyDo()
                        .isEngineStrategy()) {
                    String engineCode = eventContext.getBizEventVO().getEngineCode();
                    Tracer.logEvent("engineCode", engineCode);
                    Tracer.logMetricForCount(String.format("count_%s_%s", marketEvent.getStrategyId(), engineCode));
                    log.info("引擎策略版本灰度流程判定, messageId:{}, 策略ID:{}, 用户id:{}", messageId, marketEvent.getStrategyId(), bizEventMessageVO.getCreditUserId());
                    if (!isInEngineGrayGroup(eventContext, group)) {
                        log.info("策略引擎版本:灰度流量未命中,消息ID={},事件:{},用户ID:{}, 需要处理的策略ID:{}", messageId,
                                bizEventMessageVO.getBizEventType(),
                                bizEventMessageVO.getCreditUserId(),
                                marketEvent.getStrategyId());
                        eventContext.getBizEventVO().setIfIntoEngine(false);
                    }
                }
                // 事件-预筛
                this.eventPrescreenHandler(eventContext);
                // 人群-预筛
                this.crowdPrescreenHandler(eventContext, bizEventMessageVO);
                // 推入引擎次数限制
                strategyEngineService.checkEngineRateLimitThrowException(event.getAppUserId(), event.getStrategyId());
                // 延迟处理
                this.delayHandler(eventContext);
            } catch (Exception e) {
                log.warn("未通过预筛, 用户ID:{}, 手机号:{}", event.getAppUserId(), event.getMobile(), e);
                if (eventContext.getBizEventVO().getFailCode() == null) {
                    eventContext.getBizEventVO().addDecisionFailResult(DecisionResultEnum.START_FILTER_FAIL);
                }
                if (event.getAppUserId() == null && bizEventMessageVO.getUser_id() != null) {
                    Tracer.logEvent("NoneEventUserId", Optional.ofNullable(bizEventMessageVO.getBizEventType()).orElse("none"));
                    event.setAppUserId(bizEventMessageVO.getUser_id());
                }
                bizEventMqService.sendDecisionResultMessage(eventContext.getBizEventVO());
                strategyEventCatchService.hasEventButNoMatchFlag(marketEvent.getEventName(), marketEvent.getStrategyId());
            } finally {
                Optional.ofNullable(eventContext.getStrategyDo()).ifPresent(strategyDo -> {
                    String bizEventType = eventContext.getBizEventVO().getBizEventType();
                    strategyEventCatchService.resetEventTriggerTime(bizEventType, strategyDo);
                });
                log.info("实时策略-策略{}-预审结束, 用户ID:{}, 耗时:{}ms", marketEvent.getStrategyId(), event.getAppUserId(), duration(startTimeStrategy));
            }
        }
        log.info("实时策略-预筛结束, 用户ID:{}, 耗时:{}ms", bizEventMessageVO.getCreditUserId(), duration(startTime));
    }

    private List<StrategyMarketEventDo> filterIncreaseAmtByBizType(BizEventMessageVO bizEventMessageVO, List<StrategyMarketEventDo> marketEventDoList) {
        if (CollectionUtils.isEmpty(marketEventDoList)) {
            return marketEventDoList;
        }
        String bizEventType = bizEventMessageVO.getBizEventType();

        if("Notify_IncreaseAmt".equals(bizEventType)) {
            String increaseType = bizEventMessageVO.getExtrData().getIncreaseCode();
            if(PERSONAL_MARKETING_RELOAN_TEMP.getDesc().equals(increaseType)) {// 老客
                List<StrategyMarketEventDo> oldEventList = marketEventDoList.stream().filter(item -> OLD_CUST.getCode().equals(item.getBizType())).collect(Collectors.toList());
                log.info("filterIncreaseAmtByBizType 老客提额, 过滤策略 bizEventType:{}  increaseType:{} originalSize:{} newSize:{}",
                        bizEventType,increaseType, marketEventDoList.size(), oldEventList.size());
                return oldEventList;
            } else if(TEMP_CREDIT.getDesc().equals(increaseType)) {
                List<StrategyMarketEventDo> newEventList = marketEventDoList.stream().filter(item -> NEW_CUST.getCode().equals(item.getBizType())).collect(Collectors.toList());
                log.info("filterIncreaseAmtByBizType 新客提额, 过滤策略 bizEventType:{} increaseType:{} originalSize:{} newSize:{}",
                        bizEventType,increaseType, marketEventDoList.size(), newEventList.size());
                return newEventList;
            }
        }
        return marketEventDoList;
    }

    /**
     * 初始化事件实体、消息ID、策略实体
     *
     * @param eventContext  事件消息
     * @param marketEventDo 事件实体
     */
    private void initHandler(StrategyEventCheckContext eventContext, StrategyMarketEventDo marketEventDo, String eventMessageId) {
        eventContext.setStrategyMarketEventDo(marketEventDo);
        eventContext.setStrategyDo(cacheStrategyService.selectById(marketEventDo.getStrategyId()));
        eventContext.getBizEventVO().setMessageId(eventMessageId + "_" + String.format("%08d", eventContext.getStrategyDo().getId()));
        eventContext.getBizEventVO().setStrategyId(eventContext.getStrategyDo().getId());
        eventContext.getBizEventVO().setStrategyType(eventContext.getStrategyDo().getType());
        eventContext.getBizEventVO().setEngineCode(eventContext.getStrategyDo().getEngineCode());
        eventContext.getBizEventVO().setUserConvert(eventContext.getStrategyDo().getUserConvert());
        if (StringUtils.isNotEmpty(eventContext.getBizEventVO().getEngineCode())) {
            eventContext.getBizEventVO().addEngineDetail("engineCode", eventContext.getBizEventVO().getEngineCode());
        }
        eventContext.getBizEventVO().setStrategyExecId(serialNumberUtil.batchNum());
        log.info("实时策略-策略{}-数据初始化完成,用户ID:{},消息ID:{}",
                eventContext.getStrategyDo().getId(), eventContext.getBizEventVO().getAppUserId(), eventContext.getBizEventVO().getMessageId());
    }

    /**
     * 预筛-策略
     *
     * @param eventContext 事件
     */
    private void strategyPrescreenHandler(StrategyEventCheckContext eventContext) {
        LocalDateTime currTime = LocalDateTime.now();
        StrategyDo strategyDo = eventContext.getStrategyDo();
        if (currTime.isBefore(strategyDo.getValidityBegin()) || currTime.isAfter(strategyDo.getValidityEnd())) {
            Map<String, Object> param = new HashMap<>(6);
            param.put("validityBegin", strategyDo.getValidityBegin());
            param.put("validityEnd", strategyDo.getValidityEnd());
            param.put("currTime", currTime);
            eventContext.getBizEventVO().addHitResult("time.compare(currTime,between,validityBegin,validityEnd)", param, false);
            eventContext.getBizEventVO().addDecisionFailResult(DecisionResultEnum.STRATEGY_EXPIRE);
            throw new StrategyException(String.format("当前策略不在有效期内,策略ID:%s", strategyDo.getId()));
        }

        if (StrategyStatusEnum.getInstance(strategyDo.getStatus()) != StrategyStatusEnum.EXECUTING) {
            Map<String, Object> param = new HashMap<>(6);
            param.put("status", strategyDo.getStatus());
            eventContext.getBizEventVO().addHitResult("status == 1", param, false);
            eventContext.getBizEventVO().addDecisionFailResult(DecisionResultEnum.STRATEGY_STATUS_ERR);
            throw new StrategyException(String.format("当前策略非执行中状态,策略ID:%s", strategyDo.getId()));
        }
    }

    /**
     * 预筛-事件
     *
     * @param eventContext 事件消息
     */
    private void eventPrescreenHandler(StrategyEventCheckContext eventContext) {
        StrategyMarketEventDo marketEventDo = eventContext.getStrategyMarketEventDo();
        // 子类型判断
        Map<Integer, List<StrategyMarketSubEventDo>> marketSubEventMap = strategyMarketSubEventService.getByEventId(marketEventDo.getId());
        eventContext.setMarketSubEventMap(marketSubEventMap);

        List<StrategyMarketSubEventDo> marketSubEventList = marketSubEventMap.get(1);
        if (CollectionUtils.isEmpty(marketSubEventList)) {
            return;
        }

        String expression = this.getExpression(marketSubEventList);
        Map<String, Object> param = this.getExpressionParam(eventContext.getBizEventVO(), marketSubEventList);
        Boolean result = AviatorUtil.compute(expression, param);
        eventContext.getBizEventVO().addHitResult(expression, param, result);
        if (Boolean.FALSE.equals(result)) {
            eventContext.getBizEventVO().addDecisionFailResult(DecisionResultEnum.MARKET_SUB_FAIL);
            throw new StrategyException(String.format("营销节点限制条件验证不通过,用户ID:%s", eventContext.getBizEventVO().getAppUserId()));
        }
    }

    /**
     * 预筛-人群
     *
     * @param eventContext 事件消息
     */
    private void crowdPrescreenHandler(StrategyEventCheckContext eventContext, BizEventMessageVO bizEventMessageVO) {
        StrategyDo strategyDo = eventContext.getStrategyDo();
        switch (MarketCrowdTypeEnum.getInstance(strategyDo.getMarketCrowdType())) {
            case T0_REGISTER:
                // 注册时间、用户ID补全
                strategyMarketEventConditionService.paramComplete(eventContext.getBizEventVO(), bizEventMessageVO);

                List<StrategyMarketSubEventDo> marketSubEventList = eventContext.getMarketSubEventMap().get(2);
                if (CollectionUtils.isEmpty(marketSubEventList)) {
                    eventContext.getBizEventVO().addHitResult("", new HashMap<>(), false);
                    eventContext.getBizEventVO().addDecisionFailResult(DecisionResultEnum.REGISTER_TIME_CONFIG_ERR);
                    throw new StrategyException(String.format("实时人群筛选不通过,用户ID:%s", eventContext.getBizEventVO().getAppUserId()));
                }

                String expression = this.getExpression(marketSubEventList);
                Map<String, Object> param = this.getExpressionParam(eventContext.getBizEventVO(), marketSubEventList);
                Boolean result = AviatorUtil.compute(expression, param);
                eventContext.getBizEventVO().addHitResult(expression, param, result);
                if (Boolean.FALSE.equals(result)) {
                    eventContext.getBizEventVO().addDecisionFailResult(DecisionResultEnum.INSTANT_CROWD_FILTER_FAIL);
                    throw new StrategyException(String.format("实时人群筛选不通过,用户ID:%s", eventContext.getBizEventVO().getAppUserId()));
                }
                break;

            case CROWD_PACK:
                if (Objects.isNull(eventContext.getBizEventVO().getAppUserId())) {
                    // 注册时间、用户ID补全
                    strategyMarketEventConditionService.paramComplete(eventContext.getBizEventVO(), bizEventMessageVO);
                }
                Map<Long, CrowdContext> crowdContent = getCrowdContent(eventContext.getStrategyDo().getCrowdPackId());
                Pair<Boolean, CrowdDetailDo> crowdPackVerify = this.crowdPackVerify(eventContext, crowdContent);
                if (!Boolean.TRUE.equals(crowdPackVerify.getLeft())) {
                    eventContext.getBizEventVO().addDecisionFailResult(DecisionResultEnum.OFFLINE_CROWD_FILTER_FAIL);
                    throw new StrategyException(String.format("离线人群包筛选不通过,用户ID:%s", eventContext.getBizEventVO().getAppUserId()));
                }

                Optional.ofNullable(crowdPackVerify.getRight()).ifPresent(crowdDetailDo -> {
                    if (StringUtils.isNotBlank(crowdDetailDo.getAbNum())) {
                        eventContext.getBizEventVO().setAbNum(crowdDetailDo.getAbNum());
                    }
                    if (crowdDetailDo.getAppUserIdLast2() != null) {
                        eventContext.getBizEventVO().setAppUserIdLast2(crowdDetailDo.getAppUserIdLast2());
                    }
                    if (crowdDetailDo.getCrowdId() != null) {
                        eventContext.getBizEventVO().setCrowdPackId(crowdDetailDo.getCrowdId());
                    }
                });
                break;

            default:
        }
    }


    private Map<Long, CrowdContext> getCrowdContent(String crowdids) {
        List<Long> crowdPackIds = Arrays.stream(crowdids.split(";")).map(Convert::toLong).collect(Collectors.toList());
        // 查询人群包上传类型
        if (!CollectionUtils.isEmpty(crowdPackIds)) {
            Map<Long, CrowdContext> res = new HashMap<>(crowdPackIds.size());
            try {
                for (Long cid : crowdPackIds) {
                    CrowdPackDo crowdPack = crowdPackRepository.selectById(cid);
                    if (Objects.nonNull(crowdPack)) {
                        CrowdContext c = new CrowdContext();
                        c.setCrowdPack(crowdPack);
                        res.put(cid, c);
                    }

                    // 新增洞察平台人群包
                    if (crowdPack == null && ApolloUtil.isInTheWhitelist(INSIGHT_CROWD_PACK_WHITELIST_CROWD_PACK, cid)) {
                        CrowdInfoDo crowdInfoDo = crowdInfoRepository.selectByCrowdId(cid);
                        if (crowdInfoDo != null) {
                            CrowdContext c = new CrowdContext();
                            c.setCrowdPack(convertCrowInfoDoToCrowdPackDo(crowdInfoDo));
                            res.putIfAbsent(cid, c);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("getCrowdContent error", e);
            }
            return res;
        }
        return new HashMap<>();
    }

    private CrowdPackDo convertCrowInfoDoToCrowdPackDo(CrowdInfoDo crowdInfoDo) {
        CrowdPackDo crowdPackDo = new CrowdPackDo();
        crowdPackDo.setId(crowdInfoDo.getCrowdId());
        crowdPackDo.setFilterMethod(CrowdFilterMethodEnum.LABEL.getCode());
        return crowdPackDo;
    }

    /**
     * 延迟处理
     *
     * @param eventContext 事件
     */
    private void delayHandler(StrategyEventCheckContext eventContext) {
        StrategyMarketEventDo marketEventDo = eventContext.getStrategyMarketEventDo();
        // 获取延迟时间，单位：秒
        long delayTime = 0;
        if (marketEventDo.getTimeType() == 2) {
            Integer timeValue = marketEventDo.getTimeValue();
            delayTime = marketEventDo.getTimeUnit() == 1 ? timeValue * 60 : timeValue * 60 * 60;

            LocalDateTime triggerDatetime = eventContext.getBizEventVO().getTriggerDatetime();
            long seconds = 0;
            if (triggerDatetime != null) {
                seconds = Duration.between(triggerDatetime, LocalDateTime.now()).getSeconds();
                if (seconds < 0) {
                    seconds = 0;
                }
            }
            // 确保延迟时间不能超过一天
            if (delayTime > 24 * 60 * 60) {
                delayTime = 24 * 60 * 60;
            }
            delayTime = delayTime - seconds;
        }
        mqProducerService.bizEventDelay(eventContext.getBizEventVO(), Math.max(delayTime, 0), StrategyTypeEnum.getInstance(eventContext.getStrategyDo().getType()));
    }

    /**
     * 2.复筛
     *
     * @param event 事件消息
     */
    @Override
    public void rescreen(BizEventVO event) {
        long startTime = Instant.now().toEpochMilli();
        log.info("实时策略-复筛开始,事件:{},用户ID:{},消息ID:{},引擎名称:{}", event.getBizEventType(),
                event.getAppUserId(), event.getMessageId(), event.getEngineCode());
        StrategyLabelCheckContext eventContext = new StrategyLabelCheckContext(event);
        try {
            StrategyDo strategyDo = strategyRepository.selectById(event.getStrategyId());
            if (StrategyStatusEnum.getPausedCodes().contains(strategyDo.getStatus())) {
                log.info("实时策略-复筛,策略状态:{},不执行复筛流程,strategyId:{}", StrategyStatusEnum.getInstance(strategyDo.getStatus()).getDescription(), event.getStrategyId());
                return;
            }
            eventContext.getBizEventVO().setDispatchType(strategyDo.getDispatchType());
            if (StrategyTypeEnum.getInstance(event.getStrategyType())
                    == StrategyTypeEnum.EVENT_ENGINE && !Objects.equals(event.getIfIntoEngine(), false)) {
                log.info("策略引擎实时策略-复筛开始,事件:{},用户ID：{},消息ID:{},引擎名称:{}", event.getBizEventType(),
                        event.getAppUserId(), event.getMessageId(), event.getEngineCode());
                event.setIfIntoEngine(true);
                rescreenWithEngine(event);
                return;
            }
            // 实时标签查询
            this.queryLabelHandler(eventContext);
            // 策略-复筛
            this.rescreeningHandler(eventContext);
            // 触达
            if (StrategyTypeEnum.getInstance(event.getStrategyType()) == StrategyTypeEnum.EVENT) {
                this.dispatchHandler(eventContext);
            }
            // 清除redis标识
            strategyEventCatchService.removeHasEventButNoMatchFlag(event.getBizEventType(), event.getStrategyId());
            eventContext.getBizEventVO().addDecisionSucResult();
            bizEventMqService.sendDecisionResultMessage(eventContext.getBizEventVO());
        } catch (Exception be) {
            log.warn("未通过复筛,用户:{}, e:", event.getAppUserId(), be);
            DecisionResultEnum decisionResultEnum = DecisionResultEnum.REPEAT_FILTER_FAIL;
            if (StringUtils.isNotBlank(be.getMessage())) {
                decisionResultEnum.setFailReason(CharSequenceUtil.sub(be.getMessage(), 0, 150));
            }
            if (eventContext.getBizEventVO().getFailCode() == null) {
                eventContext.getBizEventVO().addDecisionFailResult(decisionResultEnum);
            }
            rescreenFailProcess(eventContext);
        }
        log.info("实时策略-复筛结束,用户ID:{},耗时:{}ms", event.getAppUserId(), duration(startTime));
    }

    private boolean isInEngineGrayGroup(StrategyEventCheckContext eventContext, StrategyGroupDo grayGroup) {
        if (grayGroup != null) {
            Tracer.logMetricForCount(String.format("engineGray_%s_%s", eventContext.getBizEventVO().getStrategyId(),
                    grayGroup.getName()));
            eventContext.getBizEventVO().addEngineDetail("grayGroup", grayGroup.getName());
            StrategyCreateReq.GroupConfig groupConfig = JSON.parseObject(grayGroup.getGroupConfig(),
                    StrategyCreateReq.GroupConfig.class);
            if (groupConfig == null || !Objects.equals(1, groupConfig.getSelected())) {
                return false;
            }
        }
        return true;
    }

    private void addStrategyIdParam(ModelPredictionReq modelPredictionReq, Long strategyId) {

        if (!WhitelistSwitchUtil.boolSwitchByApollo("addStrategyIdParamEnable") || modelPredictionReq == null
                || strategyId == null) {
            return;
        }

        ModelPredictionReq.BizData bizData = modelPredictionReq.getBiz_data();
        if (bizData != null) {
            bizData.setStrategy_id(strategyId);
        }
    }

    /**
     * 调用引擎决策
     *
     * @param event
     */
    public void rescreenWithEngine(BizEventVO event) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        if (event.getEngineCallerCount() == null || event.getEngineCallerCount() < 0) {
            event.setEngineCallerCount(1L);
        }

        /* 业务引擎延迟决策 */
        BizEventVO eventCopy = BeanCopyUtils.deepCopy(event, BizEventVO.class);
        boolean isReDecision = event.getEngineReDecisionId() != null;
        boolean isDispatch = false;

        StrategyLabelCheckContext eventContext = new StrategyLabelCheckContext(event);
        ModelPredictionReq modelPredictionReq = ModelPredictionReq.builder()
                .model_name(event.getEngineCode())
                .biz_data(ModelPredictionReq.BizData.builder()
                        .requestId(event.getMessageId())
                        .app(event.getApp())
                        .biz_type(event.getBizEventType())
                        .mobile(event.getMobile())
                        .app_user_id(event.getAppUserId())
                        .user_no(event.getAppUserId())
                        .trigger_datetime(DateUtil.getMills(event.getTriggerDatetime()))
                        .timestamp(new Date().getTime())
                        .callerCount(event.getEngineCallerCount())
                        .device_id(event.getDeviceId())
                        .extMap(MapUtils.isNotEmpty(event.getExt()) ? event.getExt() : Maps.newHashMap())
                        .requestType("ONLINE")
                        .build())
                .build();
        addStrategyIdParam(modelPredictionReq, event.getStrategyId()); // 2025-02-10 新增策略ID参数
        event.setEngineCallerCount(event.getEngineCallerCount() + 1);
        JSONObject resp = modelPlatformService.prediction(modelPredictionReq);
        event.addEngineDetail("enginePredictionResp", resp);
        Object decisionVal = resp.getJSONObject("data").getJSONObject("output_data").get(event.getEngineCode());
        PredictDecisionDto predictDecisionDto = JsonUtil.parse(JsonUtil.toJson(decisionVal), PredictDecisionDto.class);
        if (predictDecisionDto != null && predictDecisionDto.isSucced()) {
            if (predictDecisionDto.isDelay()) {
                log.info("引擎决策结果:延迟,引擎名称:{},用户ID:{},策略ID:{},延迟时间:{}s", event.getEngineCode(), event.getAppUserId(), event.getStrategyId(), predictDecisionDto.getDelaySeconds());
                mqProducerService.bizEventDelay(event, predictDecisionDto.getDelaySeconds(), StrategyTypeEnum.getInstance(event.getStrategyType()));
                return;
            }
            log.info("策略引擎版本,开始进行实时标签筛选,事件id:{},策略id:{},用户id:{},引擎code:{}", event.getMessageId(), event.getStrategyId(), event.getAppUserId(), event.getEngineCode());
            event.setIfMarket(predictDecisionDto.ifMarket());
            try {
                // 实时标签筛选
                this.queryLabelHandler(eventContext);
                // 策略-复筛
                this.rescreeningHandler(eventContext);
            } catch (Exception ex) {
                // todo 复筛错误埋点
                log.info("策略引擎版本复筛失败,开始营销触达动作,事件id:{},策略id:{},用户id:{},引擎code:{},错误内容:{}", event.getMessageId(), event.getStrategyId(), event.getAppUserId(), event.getEngineCode(), ex.toString());
                if (isReDecision) {
                    reDecisionService.updateReDecisionResult(event.getEngineReDecisionId(), ReDecisionResultStatus.TAG_NOT_HIT.getStatus());
                } else {
                    if (eventContext.getBizEventVO().getFailCode() == null) {
                        eventContext.getBizEventVO().addDecisionFailResult(DecisionResultEnum.REPEAT_FILTER_FAIL.setFailReason(CharSequenceUtil.sub(ex.getMessage(), 0, 150)));
                    }
                    rescreenFailProcess(eventContext);
                }
                return;
            }
            StrategyDo strategyDo = cacheStrategyService.selectById(event.getStrategyId());
            List<PredictDecisionDto.DecisionData.Action> actions = predictDecisionDto.getActions();
            // 短信/PUSH 下发成功后，同时下发延迟弹窗
            boolean smsPushSendResult = false;

            for (PredictDecisionDto.DecisionData.Action action : actions) {
                log.info("策略引擎版本, 开始营销触达动作,事件id:{},策略id:{},用户id:{},营销组:{}", event.getMessageId(), event.getStrategyId(), event.getAppUserId(), action.getGroup_id());
                if (strategyDo != null && strategyDo.getDispatchConfig() != null) {
                    log.info("策略引擎版本, 进行有效的触达时间判定, 用户id:{}, 策略id:{}", event.getAppUserId(), event.getStrategyId());
                    DispatchConfig dispatchConfig = JsonUtil.parse(strategyDo.getDispatchConfig(), DispatchConfig.class);
                    if (dispatchConfig != null && !dispatchConfig.isInTime(LocalDateTime.now())) {
                        Tracer.logEvent("dispatchReject", String.valueOf(event.getStrategyId()));
                        log.info("策略引擎版本, 不在有效的触达时间内, 丢弃不进行触达, event={}", JsonUtil.toJson(event));
                        break;
                    }
                }
                List<PredictDecisionDto.DecisionData.Action.Dispatch> dispatches = action.getOrderedDispatch();
                for (PredictDecisionDto.DecisionData.Action.Dispatch dispatch : dispatches) {
                    //redis记录groupId+渠道+template
                    saveRedis(event.getStrategyGroupId(), action, dispatch);
                    try {
                        StrategyMarketChannelEnum strategyMarketChannelEnum = StrategyMarketChannelEnum.getInstance(dispatch.getType());
                        Tracer.logMetricForCount(String.format("send_%s_%s", event.getStrategyId(), strategyMarketChannelEnum.getCode()));
                        if (strategyMarketChannelEnum != StrategyMarketChannelEnum.NONE && strategyMarketChannelEnum != StrategyMarketChannelEnum.RE_DECISION) {
                            isDispatch = true;
                        }
                        if (strategyMarketChannelEnum == StrategyMarketChannelEnum.DELAYED_POPUP && !smsPushSendResult) {
                            continue;
                        }

                        BizEventVO bizEventVO = eventContext.getBizEventVO();
                        DispatchDto dispatchDto = convertDispatchDto(bizEventVO, 0L, strategyMarketChannelEnum, StrategyRulerEnum.EVENT, null);
                        CrowdDetailDo crowdDetailDo = new CrowdDetailDo();
                        crowdDetailDo.setUserId(bizEventVO.getAppUserId());
                        crowdDetailDo.setMobile(bizEventVO.getMobile());
                        crowdDetailDo.setApp(bizEventVO.getApp());
                        crowdDetailDo.setInnerApp(bizEventVO.getInnerApp());
                        crowdDetailDo.setUnionId(bizEventVO.getUnionId()); // 统一ID


                        int sendRet = marketingSend(dispatchDto, crowdDetailDo, strategyMarketChannelEnum, action.getGroup_id(), dispatch.getDetail_info(), eventCopy, MarketingSendTypeEnum.EVENT_STRATEGY);

                        if (sendRet == 0) {
                            log.warn("策略引擎营销触达失败,marketChannel:{},detailInfo:{},event:{}", strategyMarketChannelEnum.getDescription(), JsonUtil.toJson(dispatch.getDetail_info()), JsonUtil.toJson(eventContext));
                            Tracer.logMetricForCount(String.format("send_failed_%s_%s", event.getStrategyId(), strategyMarketChannelEnum.getCode()));
                            CrowdDetailDo crowdDetail = convertToCrowdDetail(event);
                            DispatchDto detailDispatchDto = new DispatchDto();
                            detailDispatchDto.setStrategyId(event.getStrategyId());
                            detailDispatchDto.setStrategyChannel(strategyMarketChannelEnum.getCode());
                            detailDispatchDto.setFailReason(dispatchDto.getFailReason());
                            saveFailRecord(crowdDetail, detailDispatchDto, detailDispatchDto.getFailReason(), action.getGroup_id());
                            break;
                        }

                        if (sendRet < 0) {
                            continue;
                        }
                        // todo 计数器
                        if (strategyMarketChannelEnum != StrategyMarketChannelEnum.NONE) {
                            userSendCounterService.counterIncrementSum(event.getAppUserId(), event.getMobile(), event.getStrategyId(),
                                    strategyMarketChannelEnum.getCode(), DateUtil.dayOfInt(new Date()), sendRet);
                        }

                        if (strategyMarketChannelEnum == StrategyMarketChannelEnum.SMS || strategyMarketChannelEnum == StrategyMarketChannelEnum.PUSH) {
                            smsPushSendResult = true;
                        }
                    } catch (Exception exception) {
                        log.error("引擎派发营销出现失败:引擎名称:{},用户ID:{},策略ID:{},内容:{}", event.getEngineCode(), event.getAppUserId(), event.getStrategyId(), JsonUtil.toJson(dispatch), NoneException.catError());
                        LogUtil.logDebug("引擎派发营销出现失败,e={}", exception.toString(), exception);
                        break;
                    }
                }
            }
            // 清除redis标识
            strategyEventCatchService.removeHasEventButNoMatchFlag(event.getBizEventType(), event.getStrategyId());
            eventContext.getBizEventVO().addDecisionSucResult();
            bizEventMqService.sendDecisionResultMessage(eventContext.getBizEventVO());
        } else {
            if (eventContext.getBizEventVO().getFailCode() == null) {
                eventContext.getBizEventVO().addDecisionFailResult(DecisionResultEnum.EVENT_ENGINE_FAIL);
            }
            rescreenFailProcess(eventContext);
            log.warn("引擎决策结果失败:详细信息,用户ID:{}, 结果:{}", event.getAppUserId(), JsonUtil.toJson(resp));
        }

        if (isReDecision) {
            reDecisionService.updateReDecisionResult(event.getEngineReDecisionId(), isDispatch ? ReDecisionResultStatus.ENGINE_MARKETING.getStatus() : ReDecisionResultStatus.ENGINE_NOT_MARKETING.getStatus());
        }
        log.info("rescreenWithEngine cost={}, strategyId={}, userNo={}", stopwatch.elapsed(TimeUnit.MILLISECONDS), event.getStrategyId(), event.getAppUserId());
    }

    private void saveRedis(Long strategyGroupId, PredictDecisionDto.DecisionData.Action action, PredictDecisionDto.DecisionData.Action.Dispatch dispatch) {
        try {
            String groupId = action.getGroup_id();
            EngineStrategyGroupIdInfoDto engineStrategyGroupIdInfoDto = new EngineStrategyGroupIdInfoDto();
            engineStrategyGroupIdInfoDto.setGroupId(groupId);
            engineStrategyGroupIdInfoDto.setGroupSource(action.getGroup_source());
            List<EngineStrategyGroupIdInfoDto.ChannelInfo> channelInfoList = new ArrayList<>();

            StrategyMarketChannelEnum strategyMarketChannelEnum = StrategyMarketChannelEnum.getInstance(dispatch.getType());
            switch (strategyMarketChannelEnum) {
                case SMS:
                case PUSH:
                    if (dispatch.getDetail_info().get("template_id") == null) {
                        return;
                    }
                    EngineStrategyGroupIdInfoDto.ChannelInfo smsOrPushChannelInfo = new EngineStrategyGroupIdInfoDto.ChannelInfo();
                    smsOrPushChannelInfo.setMarketChannel(strategyMarketChannelEnum.getCode());
                    smsOrPushChannelInfo.setTemplate(dispatch.getDetail_info().get("template_id").toString());
                    channelInfoList.add(smsOrPushChannelInfo);
                    break;
                case VOICE:
                    if (dispatch.getDetail_info().get("user_type") == null) {
                        return;
                    }
                    EngineStrategyGroupIdInfoDto.ChannelInfo voiceChannelInfo = new EngineStrategyGroupIdInfoDto.ChannelInfo();
                    voiceChannelInfo.setMarketChannel(strategyMarketChannelEnum.getCode());
                    voiceChannelInfo.setTemplate(dispatch.getDetail_info().get("user_type").toString());
                    channelInfoList.add(voiceChannelInfo);
                    break;
                case SALE_TICKET:
                case X_DAY_INTEREST_FREE:
                    List<PredictDecisionDto.DetailCouponDto> couponDtos = JsonUtil.toList(JsonUtil.toJson(dispatch.getDetail_info().get("batch")),
                            PredictDecisionDto.DetailCouponDto.class);
                    if (CollectionUtils.isEmpty(couponDtos)) {
                        return;
                    }
                    for (PredictDecisionDto.DetailCouponDto couponDto : couponDtos) {
                        EngineStrategyGroupIdInfoDto.ChannelInfo couponChannelInfo = new EngineStrategyGroupIdInfoDto.ChannelInfo();
                        couponChannelInfo.setMarketChannel(strategyMarketChannelEnum.getCode());
                        couponChannelInfo.setTemplate(couponDto.getActivity_id().toString());
                        channelInfoList.add(couponChannelInfo);
                    }
                    break;
                case LIFE_RIGHTS:
                    List<PredictDecisionDto.DetailLifeRightsDto> detailLifeRightsDtos = JsonUtil.toList(JsonUtil.toJson(dispatch.getDetail_info().get("batch")),
                            PredictDecisionDto.DetailLifeRightsDto.class);
                    if (CollectionUtils.isEmpty(detailLifeRightsDtos)) {
                        return;
                    }
                    for (PredictDecisionDto.DetailLifeRightsDto detailLifeRightsDto : detailLifeRightsDtos) {
                        EngineStrategyGroupIdInfoDto.ChannelInfo couponChannelInfo = new EngineStrategyGroupIdInfoDto.ChannelInfo();
                        couponChannelInfo.setMarketChannel(strategyMarketChannelEnum.getCode());
                        couponChannelInfo.setTemplate(detailLifeRightsDto.getActivity_id().toString());
                        channelInfoList.add(couponChannelInfo);
                    }
                    break;
                case VOICE_NEW:
                    if (dispatch.getDetail_info().get("name_type_id") == null) {
                        return;
                    }
                    EngineStrategyGroupIdInfoDto.ChannelInfo newVoiceChannelInfo = new EngineStrategyGroupIdInfoDto.ChannelInfo();
                    newVoiceChannelInfo.setMarketChannel(strategyMarketChannelEnum.getCode());
                    newVoiceChannelInfo.setTemplate(dispatch.getDetail_info().get("name_type_id").toString());
                    channelInfoList.add(newVoiceChannelInfo);
                    break;
                case INCREASE_AMOUNT:
                    EngineStrategyGroupIdInfoDto.ChannelInfo increaseAmountChannelInfo = new EngineStrategyGroupIdInfoDto.ChannelInfo();
                    increaseAmountChannelInfo.setMarketChannel(strategyMarketChannelEnum.getCode());
                    //提额类型：默认TEMP_CREDIT
                    IncreaseTypeEnum increaseType = IncreaseTypeEnum.getEnum(dispatch.getDetail_info().get("increase_type").toString());
                    String increaseTemplateId = StringUtils.isBlank(dispatch.getDetail_info().get("increase_type").toString()) ?
                            TEMP_CREDIT.getCode() : increaseType == null ?
                            TEMP_CREDIT.getCode() : increaseType.getDesc();
                    increaseAmountChannelInfo.setTemplate(increaseTemplateId);
                    channelInfoList.add(increaseAmountChannelInfo);
                    break;
                case AI_PRONTO:
                    if (dispatch.getDetail_info().get("biz_source_code") == null
                            && dispatch.getDetail_info().get("name_type_id") == null) {
                        return;
                    }
                    EngineStrategyGroupIdInfoDto.ChannelInfo aiChannelInfo = new EngineStrategyGroupIdInfoDto.ChannelInfo();
                    aiChannelInfo.setMarketChannel(strategyMarketChannelEnum.getCode());
                    Object templateId = Objects.nonNull(dispatch.getDetail_info().get("biz_source_code")) ? dispatch.getDetail_info().get("biz_source_code") : dispatch.getDetail_info().get("name_type_id");
                    aiChannelInfo.setTemplate(String.valueOf(templateId));
                    channelInfoList.add(aiChannelInfo);
                    break;
                case NONE:
                    EngineStrategyGroupIdInfoDto.ChannelInfo noneChannelInfo = new EngineStrategyGroupIdInfoDto.ChannelInfo();
                    noneChannelInfo.setMarketChannel(strategyMarketChannelEnum.getCode());
                    channelInfoList.add(noneChannelInfo);
                    break;
                default:
                    break;
            }
            engineStrategyGroupIdInfoDto.setChannelInfoList(channelInfoList);

            String curDate = LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMMdd");
            String redisKey = String.format(RedisKeyConstants.ENGINE_STRATEGY_GROUP_ID_DATA, curDate, strategyGroupId);
            String redisHashValue = redisUtils.hGet(redisKey, groupId);
            if (StringUtils.isNotBlank(redisHashValue)) {
                EngineStrategyGroupIdInfoDto redisGroupIdInfo = JSON.parseObject(redisHashValue, EngineStrategyGroupIdInfoDto.class);
                List<EngineStrategyGroupIdInfoDto.ChannelInfo> redisChannelInfo = redisGroupIdInfo.getChannelInfoList();
                //比较当前渠道redis中是否存在
                for (EngineStrategyGroupIdInfoDto.ChannelInfo channelInfo : channelInfoList) {
                    if (!redisChannelInfo.contains(channelInfo)) {
                        redisChannelInfo.add(channelInfo);
                    }
                }
                redisGroupIdInfo.setChannelInfoList(redisChannelInfo);
                redisUtils.hPut(redisKey, groupId, JSON.toJSONString(redisGroupIdInfo), RedisUtils.DEFAULT_EXPIRE_DAYS);
            } else {
                redisUtils.hPut(redisKey, groupId, JSON.toJSONString(engineStrategyGroupIdInfoDto), RedisUtils.DEFAULT_EXPIRE_DAYS);
            }
        } catch (Exception e) {
            log.info("realtime engine save redis engine groupId info fail", e);
        }
    }

    /**
     * 2.复筛-失败处理
     *
     * @param eventContext 事件
     */
    private void rescreenFailProcess(StrategyLabelCheckContext eventContext) {
        bizEventMqService.sendDecisionResultMessage(eventContext.getBizEventVO());
        strategyEventCatchService.hasEventButNoMatchFlag(eventContext.getBizEventVO().getBizEventType(), eventContext.getBizEventVO().getStrategyId());
    }

    /**
     * 实时标签查询，一次查询多个实时标签
     *
     * @param eventContext 事件
     */
    private void queryLabelHandler(StrategyLabelCheckContext eventContext) {
        BizEventVO bizEventVO = eventContext.getBizEventVO();
        // 查询策略配置的实时标签相关配置
        Map<String, List<StrategyMarketEventConditionDo>> labelNameToList = strategyMarketEventConditionService.getStringToListByStrategyId(bizEventVO.getStrategyId());
        if (CollectionUtils.isEmpty(labelNameToList)) {
            log.warn("复筛,不存在需要查询的标签.策略ID:{}", bizEventVO.getStrategyId());
            return;
        }
        // 截止本次营销前
        List<StrategyMarketEventConditionDo> list = labelNameToList.values().stream().flatMap(List::stream).filter(item -> Objects.nonNull(item.getTimeType())).collect(Collectors.toList());
        bizEventVO.setStartTime(getStartTime(CollectionUtils.isEmpty(list) ? null : list.get(0)));
        // 请求数仓
        Map<Long, Map<String, Object>> labelValueMap = adsStrategyLabelService.query(bizEventVO, labelNameToList.keySet(), StrategyInstantLabelTypeEnum.LABEL);
        // 设置事件策略标签对象
        eventContext.setLabelValueMap(labelValueMap);
        eventContext.setMarketEventConditionMap(labelNameToList);
        //异步 钉钉告警
        labelAlarm(eventContext);

        // 设置 distributeCurrentAvailableTotalQuota
        setDistributeCurrentAvailableTotalQuota(bizEventVO, labelValueMap);
    }

    private void setDistributeCurrentAvailableTotalQuota(BizEventVO bizEventVO, Map<Long, Map<String, Object>> labelValueMap) {
        try {
            if (labelValueMap != null && labelValueMap.containsKey(bizEventVO.getAppUserId())) {
                Map<String, Object> labelValues = labelValueMap.get(bizEventVO.getAppUserId());
                if (labelValues != null && labelValues.containsKey("distribute_current_available_total_quota")) {
                    Object quotaValue = labelValues.get("distribute_current_available_total_quota");
                    if (quotaValue == null || StringUtils.isBlank(quotaValue.toString())) {
                        bizEventVO.setDistributeCurrentAvailableTotalQuota(BigDecimal.ZERO);
                    } else {
                        bizEventVO.setDistributeCurrentAvailableTotalQuota(new BigDecimal(quotaValue.toString()));
                    }
                }
            }
        } catch (Exception e) {
            log.info("Setting distributeCurrentAvailableTotalQuota for user: {}", bizEventVO.getAppUserId(), e);
        }
    }

    /**
     * 实时标签查询,特定告警
     */
    private void labelAlarm(StrategyLabelCheckContext eventContext) {
        Long strategyId = eventContext.getBizEventVO().getStrategyId();
        Long appUserId = eventContext.getBizEventVO().getAppUserId();
        Map<Long, Map<String, Object>> labelValueMap = eventContext.getLabelValueMap();
        labelValueMap.forEach((k, v) -> {
            //api首贷额度使用率>1告警
            String labelName = "api_down_loan_usage_rate";
            Object apiDownLoanUsageRate = v.get(labelName);
            if (configService.getLabelDingAlarmSwitch() && Objects.nonNull(apiDownLoanUsageRate) && Double.compare(Double.parseDouble(apiDownLoanUsageRate.toString()), 1) > 0) {
                BigDecimal firstLoanSucAmt = eventContext.getBizEventVO().getFirst_loan_suc_amt();
                String content = String.format("事件上报放款金额:%s,标签返回值api_down_loan_usage_rate:%s", firstLoanSucAmt, apiDownLoanUsageRate);
                //异步发送钉钉告警消息
                DispatchAlarmExecutor.getPool().submit(() -> DingTalkUtil.labelDingTalk(dingTalkConfig, labelName, strategyId, appUserId, content));
            }
        });
    }

    /**
     * 复筛
     *
     * @param eventContext 事件消息
     */
    private void rescreeningHandler(StrategyLabelCheckContext eventContext) {
        if (CollectionUtils.isEmpty(eventContext.getMarketEventConditionMap())) {
            log.warn("复筛,不存在标签配置.策略ID:{}", eventContext.getBizEventVO().getStrategyId());
            return;
        }
        // 获取用户的标签，进行表达式比较
        Map<String, Object> labelValueMap = eventContext.getLabelValueMap().get(eventContext.getBizEventVO().getAppUserId());
        // 根据标签类型分组
        Map<Integer, List<StrategyMarketEventConditionDo>> optionalMap = eventContext.getMarketEventConditionMap().values()
                .stream()
                .flatMap(List::stream)
                .collect(Collectors.groupingBy(item -> item.getOptional() == 1 ? 1 : 2));
        // 依次检查 实时标签、排序项标签
        new TreeMap<>(optionalMap).forEach((optional, list) -> labelCheck(eventContext.getBizEventVO(), list, labelValueMap, optional));
    }

    /**
     * 标签判断
     *
     * @param bizEventVO    事件
     * @param list          标签集合
     * @param labelValueMap 标签值
     * @param type          标签类型
     */
    private void labelCheck(BizEventVO bizEventVO, List<StrategyMarketEventConditionDo> list, Map<String, Object> labelValueMap, Integer type) {
        if (CollectionUtils.isEmpty(list)) {
            log.warn("标签判断,不存在该类型标签.标签类型:{}", type == 1 ? "实时" : "排除");
            return;
        }

        boolean isInstant = type == 1;
        String expression = list.stream().map(StrategyMarketEventConditionDo::getExpression).collect(Collectors.joining(" && "));
        Boolean result = AviatorUtil.compute(expression, labelValueMap);
        bizEventVO.addHitResult(expression, labelValueMap, result);
        if (Objects.equals(result, Boolean.FALSE)) {
            bizEventVO.addDecisionFailResult(isInstant ? DecisionResultEnum.INSTANT_LABEL_FILTER_FAIL : DecisionResultEnum.EXCLUDE_LABEL_FILTER_FAIL);
            throw new StrategyException(String.format("不满足%s标签条件,用户:%s", isInstant ? "实时" : "排除", bizEventVO.getAppUserId()));
        }
    }

    /**
     * 分组/渠道匹配
     *
     * @param eventContext 事件消息
     */
    private void dispatchHandler(StrategyLabelCheckContext eventContext) {
        BizEventVO bizEventVO = eventContext.getBizEventVO();
        CrowdDetailDo crowdDetail = convertToCrowdDetail(bizEventVO);
        boolean notFlow = StringUtils.equals("NOTIFY", bizEventVO.getDispatchType());
        // 获取该策略下所有分组
        List<StrategyGroupDo> strategyGroupDoList = cacheStrategyGroupService.selectListByStrategyId(bizEventVO.getStrategyId());
        // 用户分组匹配
        strategyGroupDoList = userGroupMatch(bizEventVO, crowdDetail, strategyGroupDoList);
        // 循环分组渠道调用下发
        strategyGroupDoList.forEach(strategyGroupDo -> {
            List<StrategyMarketChannelDo> marketChannelList = cacheStrategyMarketChannelService.selectByStrategyGroupId(strategyGroupDo.getId());
            for (StrategyMarketChannelDo channelDo : marketChannelList) {
                log.info("渠道-流控开始,策略组:{},渠道:{}", strategyGroupDo.getId(), channelDo.getId());
                StrategyMarketChannelEnum marketChannelEnum = StrategyMarketChannelEnum.getInstance(channelDo.getMarketChannel());
                // 成功状态
                List<Integer> dispatchSucStatus = Arrays.asList(-1, 1);
                // 非不营销渠道，执行流控
                if (marketChannelEnum != StrategyMarketChannelEnum.NONE) {
                    if (notFlow) {
                        // todo 空动作
                        log.info("渠道-复筛忽略流控, 策略:{},", bizEventVO.getStrategyId());
                    } else {
                        log.info("渠道-复筛开始流控, 策略:{},", bizEventVO.getStrategyId());
                        List<CrowdDetailDo> crowdDetailList = flowCtrl(bizEventVO.getMessageId(), bizEventVO.getTriggerDatetime(), channelDo, crowdDetail, dispatchSucStatus, bizEventVO.getBizEventType());
                        if (CollectionUtils.isEmpty(crowdDetailList)) {
                            log.warn("分组/渠道匹配,用户已被流控拦截,终止下发.用户ID:{}", bizEventVO.getAppUserId());
                            continue;
                        }
                    }
                }
                bizEventVO.setMarketChannelId(channelDo.getId());
                bizEventVO.setStrategyGroupId(strategyGroupDo.getId());
                bizEventVO.setMarketChannel(channelDo.getMarketChannel());
                log.info("策略分组-满足所有条件,调用触达队列...");
                mqProducerService.channelDelivery(bizEventVO);
            }
        });
    }

    /**
     * 用户分组匹配
     *
     * @param bizEventVO          事件
     * @param crowdDetail         用户明细
     * @param strategyGroupDoList 策略所有分组
     * @return 最终需要使用的分组
     */
    private List<StrategyGroupDo> userGroupMatch(BizEventVO bizEventVO, CrowdDetailDo crowdDetail, List<StrategyGroupDo> strategyGroupDoList) {
        StrategyDo strategy = cacheStrategyService.selectById(bizEventVO.getStrategyId());
        // 未分组，返回当天分组记录
        if (StrategyAbTestEnum.getInstance(strategy.getAbTest()) == StrategyAbTestEnum.NO) {
            return strategyGroupDoList;
        }

        StrategyGroupTypeEnum abType = StrategyGroupTypeEnum.getInstance(strategy.getAbType());
        // 随机数组件
        if (abType == StrategyGroupTypeEnum.NEW_RANDOM) {
            List<CrowdDetailDo> list = Collections.singletonList(crowdDetail);
            randomNumService.randomNum(strategy, bizEventVO.getMarketChannel(), null, list).stream().findFirst().ifPresent(crowdDetailDo -> {
                crowdDetail.setAbNum(crowdDetailDo.getAbNum());
                crowdDetail.setAppUserIdLast2(crowdDetailDo.getAppUserIdLast2());
            });
        }
        // 原随机数
        else {
            if (StringUtils.isBlank(bizEventVO.getAbNum())) {
                strategyMarketEventConditionService.abNumComplete(bizEventVO);
                crowdDetail.setAbNum(bizEventVO.getAbNum());
                crowdDetail.setAppUserIdLast2(bizEventVO.getAppUserIdLast2());
                log.info("策略分组-参数补全,用户ID:{},随机数:{},UID后两位:{}", bizEventVO.getAppUserId(), bizEventVO.getAbNum(), bizEventVO.getAppUserIdLast2());
            }
        }

        // 根据分组配置，匹配用户所属的组
        return strategyGroupDoList.stream().filter(item -> matchGroup(strategy, crowdDetail, item)).collect(Collectors.toList());
    }

    /**
     * 获取人群的灰度分组
     *
     * @param strategyId
     * @param eventContext
     * @return
     */
    private StrategyGroupDo getAbGroup(Long strategyId, StrategyEventCheckContext eventContext) {
        // 获取该策略下所有分组
        List<StrategyGroupDo> strategyGroupDos = cacheStrategyGroupService
                .selectListByStrategyId(strategyId);
        if (!CollectionUtils.isEmpty(strategyGroupDos)) {
            BizEventVO bizEventVO = eventContext.getBizEventVO();
            CrowdDetailDo crowdDetail = convertToCrowdDetail(bizEventVO);
            // 用户分组匹配
            List<StrategyGroupDo> groups = userGroupMatch(bizEventVO, crowdDetail, strategyGroupDos);
            if (!CollectionUtils.isEmpty(groups)) {
                return groups.get(0);
            }
        }
        return null;
    }

    /**
     * 3.触达
     *
     * @param event 事件通知
     */
    @Override
    public void dispatch(BizEventVO event) {
        StrategyDo strategyDo = cacheStrategyService.selectById(event.getStrategyId());
        if (strategyDo != null && strategyDo.getDispatchConfig() != null) {
            log.info("进行有效的触达时间判定, 用户id:{}, 策略id:{}", event.getAppUserId(), event.getStrategyId());
            DispatchConfig dispatchConfig = JsonUtil.parse(strategyDo.getDispatchConfig(),
                    DispatchConfig.class);
            if (dispatchConfig != null && !dispatchConfig.isInTime(LocalDateTime.now())) {
                Tracer.logEvent("dispatchReject", String.valueOf(event.getStrategyId()));
                log.info("不在有效的触达时间内, 丢弃不进行触达, event={}", JsonUtil.toJson(event));
                return;
            }
        }
        long startTime = Instant.now().toEpochMilli();
        log.info("实时策略-触达开始 事件:{} 用户ID:{} 消息ID:{} event:{}", event.getBizEventType(), event.getAppUserId(), event.getMessageId(), event);
        CrowdDetailDo crowdDetail = convertToCrowdDetail(event);
        // 麻雀-fxk老客转xyf01下发 ,AB分组之后，触达下发前，流控判断前
        CrowdDetailDo newCrowd = abstractAdsStrategyLabelService.convertCrowdOne(crowdDetail, event.getUserConvert());
        log.info("实时策略-触达开始 转换后用户ID:{}", newCrowd);
        StrategyMarketChannelEnum marketChannelEnum = StrategyMarketChannelEnum.getInstance(event.getMarketChannel());
        log.info("实时策略-触达开始流控 策略id:{} 事件:{} 用户ID:{} 消息ID:{} 渠道:{}", event.getStrategyId(), event.getBizEventType(), event.getAppUserId(), event.getMessageId(), event.getMarketChannel());
        // todo 再次流控 !!!
        boolean flcRet = dispatchFlcService.dispatchFlc(event, newCrowd, this);
        if (flcRet) {
            return;
        }
        log.info("dispatchFlc, 触达流控通过, 策略id:{}, marketChannel = {}, 用户ID：{}, 消息ID:{}", event.getStrategyId(), marketChannelEnum.getCode(), event.getAppUserId(), event.getMessageId());
        // 查询策略分组、渠道、执行日志
        Triple<StrategyGroupDo, StrategyMarketChannelDo, StrategyExecLogDo> triple = this.getStrategyConfig(event.getStrategyGroupId(), marketChannelEnum);
        // 封装下发请求参数
        DispatchDto dispatchDto = this.convertToDispatchDto(event, triple);
        dispatchDto.setDispatchType(strategyDo.getDispatchType());
        // 执行下发
        ImmutableTriple<Integer, EventPushBatchDo, Boolean> dispatchResult = ImmutableTriple.of(0, null, false);
        boolean isIgnoreStrategyException = false;
        String errorMesg = null;
        try {
            dispatchResult = this.execSend(dispatchDto, newCrowd, marketChannelEnum, triple.getMiddle(), event);
        } catch (StrategyException e) {
            errorMesg = e.getMessage();
            log.warn("下发异常,用户ID:{},策略:{},分组:{},渠道:{},异常信息:{}", newCrowd.getUserId(), dispatchDto.getStrategyId(), dispatchDto.getStrategyGroupId(), dispatchDto.getStrategyChannel(), e.getMessage(), e);
            List<String> ignoreErrorMsg = getAppConfigService().getIgnoreDispatchErrorMsgAlarm(marketChannelEnum);
            if (!CollectionUtils.isEmpty(ignoreErrorMsg)
                    && ignoreErrorMsg.contains(e.getMessage())) {
                Tracer.logEvent("DispatchIgnoreErrorMsg", Optional.ofNullable(e.getMessage()).orElse("NULL"));
                isIgnoreStrategyException = true;
            }
        } finally {
            log.info("下发结果：{}", dispatchResult);
            if (marketChannelEnum != StrategyMarketChannelEnum.NONE && dispatchResult.getLeft() <= 0) {
                // 保存失败记录
                if (!Objects.equals(true, dispatchResult.getRight())) {
                    this.saveFailRecord(newCrowd, dispatchDto);
                    // 触达失败，根据返回code，忽略已知告警
                    List<String> ignoreCodes = getAppConfigService().getIgnoreDispatchReponseFailedCodesAlarm(marketChannelEnum);
                    if (dispatchResult.getMiddle() != null && !CollectionUtils.isEmpty(ignoreCodes) && ignoreCodes
                            .contains(dispatchResult.getMiddle().getSendCode())) {
                        log.warn("销触达失败告警忽略, 策略id:{}, marketChannel:{},用户ID:{}, 触达code:{}", event.getStrategyId(),
                                marketChannelEnum.getCode(), event.getAppUserId(), dispatchResult.getMiddle().getSendCode());
                    } else {
                        if (isIgnoreStrategyException) {
                            log.warn("未触达告警忽略, 策略id:{},marketChannel:{},用户ID:{}", event.getStrategyId(), marketChannelEnum.getCode(), event.getAppUserId());
                        } else {
                            if (marketChannelEnum == StrategyMarketChannelEnum.INCREASE_AMOUNT && dispatchResult.getMiddle() == null) {
                                log.warn("未触达告警忽略, 策略id:{},marketChannel:{},用户ID:{}", event.getStrategyId(), marketChannelEnum.getCode(), event.getAppUserId());
                            } else {
                                strategyDo = cacheStrategyService.selectById(triple.getLeft().getStrategyId());
                                List<String> atMobileList = new ArrayList<>(dingTalkConfig.atMobileList());
                                atMobileList.add(strategyDo.getUpdatedOpMobile());
                                log.warn("触达失败, 进行告警, 策略id:{}, 触达渠道:{}, 用户id:{}", event.getStrategyId(), marketChannelEnum.getDescription(), event.getAppUserId());
                                strategyDo.alarmDingTalk(dingTalkConfig.getAlarmUrl(), atMobileList, String.format("今日推送【%s】接口失败, 请立即检查, 错误信息:%s", marketChannelEnum.getDescription(), errorMesg), null);
                            }
                        }
                    }
                }
            }
            // 统计执行日志
            this.setChannelExecLogCount(triple.getLeft().getId(), marketChannelEnum.getCode(), dispatchResult.getLeft());
            log.info("实时策略-触达结束,用户ID:{},耗时:{}ms", event.getAppUserId(), duration(startTime));
        }
    }

    /**
     * 查询策略分组、渠道、执行日志
     *
     * @param strategyGroupId   分组ID
     * @param marketChannelEnum 渠道
     * @return 策略分组、渠道、执行日志
     */
    private Triple<StrategyGroupDo, StrategyMarketChannelDo, StrategyExecLogDo> getStrategyConfig(Long strategyGroupId, StrategyMarketChannelEnum marketChannelEnum) {
        StrategyGroupDo strategyGroup = cacheStrategyGroupService.selectById(strategyGroupId);
        StrategyMarketChannelDo strategyMarketChannel = cacheStrategyMarketChannelService.selectByStrategyGroupIdAndChannel(strategyGroupId, marketChannelEnum.getCode());
        StrategyExecLogDo execLogDo = strategyExecLogService.channelExecLog(strategyGroup, strategyMarketChannel);
        LogUtil.logDebug("StrategyEventDispatchServiceImpl getStrategyConfig strategyGroupId={} marketChannelEnum={} strategyGroup={} strategyMarketChannel={} execLogDo={}", strategyGroupId, marketChannelEnum, JSONObject.toJSONString(strategyGroup), JSONObject.toJSONString(strategyMarketChannel), JSONObject.toJSONString(execLogDo));
        if (Objects.isNull(execLogDo)) {
            String date = LocalDateTimeUtil.format(LocalDate.now(), "yyyyMMdd");
            String logKey = String.format(RedisKeyConstants.EVENT_CHANNEL_EXEC_LOG, date, strategyGroup.getId(), marketChannelEnum.getCode());
            execLogDo = this.queryRedisStrategyExecLog(logKey);
        }
        return Triple.of(strategyGroup, strategyMarketChannel, execLogDo);
    }

    /**
     * 执行发送
     *
     * @param dispatchDto 下发相关参数
     * @param crowdDetail 用户明细
     * @param channelEnum 渠道
     * @return 下发结果, 返回值，最后1个代表的是否发生限流， true 代表发生限流
     */
    private ImmutableTriple<Integer, EventPushBatchDo, Boolean> execSend(DispatchDto dispatchDto, CrowdDetailDo crowdDetail, StrategyMarketChannelEnum channelEnum, StrategyMarketChannelDo channelDo, BizEventVO bizEvent) {
        ImmutableTriple<Integer, EventPushBatchDo, Boolean> dispatchResult = ImmutableTriple.of(0, null, false);
        if (Objects.equals(Boolean.TRUE, this.convertUserInfo(crowdDetail, channelDo))) {
            switch (channelEnum) {
                case SMS:
                    dispatchResult = eventDispatchService.sendSmsEvent(dispatchDto, crowdDetail.getApp(), crowdDetail.getInnerApp(), crowdDetail, bizEvent, channelDo.getExtInfo());
                    // 短信/PUSH 同时下发延迟弹窗
                    if (dispatchResult.getLeft() > 0) {
                        eventDispatchService.sendDelayedPopupEvent(Optional.ofNullable(dispatchDto).map(DispatchDto::getDelayedPopupChannelDto).orElse(null), Optional.ofNullable(crowdDetail).map(CrowdDetailDo::getUserId).orElse(null));
                    }
                    break;
                case VOICE:
                    dispatchResult = eventDispatchService.sendTeleEvent(dispatchDto, crowdDetail.getApp(), crowdDetail.getInnerApp(), crowdDetail);
                    break;
                case VOICE_NEW:
                    dispatchResult = eventDispatchService.sendNewTeleEvent(dispatchDto, crowdDetail.getApp(), crowdDetail.getInnerApp(), crowdDetail);
                    break;
                case SALE_TICKET:
                    dispatchResult = eventDispatchService.sendCouponEvent(dispatchDto, crowdDetail.getApp(), crowdDetail.getInnerApp(), crowdDetail);
                    break;
                case PUSH:
                    dispatchResult = eventDispatchService.sendPushEvent(dispatchDto, crowdDetail.getApp(), crowdDetail.getInnerApp(), crowdDetail, bizEvent);
                    // 短信/PUSH 同时下发延迟弹窗
                    if (dispatchResult.getLeft() > 0) {
                        eventDispatchService.sendDelayedPopupEvent(Optional.ofNullable(dispatchDto).map(DispatchDto::getDelayedPopupChannelDto).orElse(null), Optional.ofNullable(crowdDetail).map(CrowdDetailDo::getUserId).orElse(null));
                    }
                    break;
                case INCREASE_AMOUNT:
                    dispatchResult = eventDispatchService.sendIncreaseAmtEvent(dispatchDto, crowdDetail.getApp(), crowdDetail.getInnerApp(), crowdDetail, bizEvent.getCreditUserId(), bizEvent.getDeviceId(), bizEvent.getIp());
                    break;
                case LIFE_RIGHTS:
                    dispatchResult = eventDispatchService.sendLifeRightsEvent(dispatchDto, crowdDetail.getApp(), crowdDetail.getInnerApp(), crowdDetail);
                    break;
                case X_DAY_INTEREST_FREE:
                    dispatchResult = eventDispatchService.sendXDayInterestFreeEvent(dispatchDto, crowdDetail.getApp(), crowdDetail.getInnerApp(), crowdDetail);
                    break;
                case AI_PRONTO:
                    dispatchResult = eventDispatchService.sendAiProntoEvent(dispatchDto, crowdDetail.getApp(), crowdDetail.getInnerApp(), crowdDetail);
                    break;
                case NONE:
                    userBlankGroupDetailService.add(convertToBlankDetail(crowdDetail, dispatchDto), StrategyRulerEnum.EVENT);
                    break;
                default:
                    log.warn("该渠道暂不支持下发,渠道:{}", channelEnum);
            }
        } else {
            // user_dispatch_fail_detail app存要转换成的app，appuid没查到就存空或者定义一个默认值；
            crowdDetail.setUserId(-1L);
            dispatchDto.setFailReason("用户信息转换失败");
        }
        return dispatchResult;
    }

    /**
     * 策略纬度的流控，被流控返回true
     *
     * @param channelEnum
     * @param userId
     * @param strategyId
     * @return
     */
    @Deprecated
    public boolean filterUserByFlowCtrl(StrategyMarketChannelEnum channelEnum, long userId, Long strategyId,
                                        FlowCtrlInterceptionLogDo interceptionLogDo) {
        if (StrategyMarketChannelEnum.getIgnoreCodes()
                .contains(channelEnum.getCode())) {
            return false;
        }
        // 获取流控配置
        FlowCtrlDo flowCtrlDo = getFlowCtrlCoreService().getFlowCtrlRulesOnline(2, strategyId)
                .stream()
                .filter(x -> Objects.equals(1, x.getType()))
                .findFirst().orElse(null);
        if (flowCtrlDo == null || flowCtrlDo.getLimitDays() == null) {
            return false;
        }

        interceptionLogDo.setFlowCtrlId(flowCtrlDo.getId());

        Date now = new Date();
        Date start = DateUtil.addDays(now, flowCtrlDo.getLimitDays() - 1);
        int startVal = DateUtil.dayOfInt(start);
        int endVal = DateUtil.dayOfInt(now);
        long ct = getUserSendSucceedCounterService().countSum(userId, strategyId,
                channelEnum.getCode(), startVal, endVal);
        return (ct >= flowCtrlDo.getLimitTimes());
    }

    // -999:发生流控错误， -1:无需发送， 其他值：表示发送的数量
    public int marketingSend(DispatchDto dispatchDto, CrowdDetailDo crowdDetailDo, StrategyMarketChannelEnum channelEnum,
                             String groupName, Map detailInfo,
                             @Nullable BizEventVO bizEventVO,
                             MarketingSendTypeEnum sendTypeEnum) {
        int ret = 0;
        Long strategyId = dispatchDto.getStrategyId();
        String batchNum = serialNumberUtil.batchNum();

        EventPushBatchDo eventPushBatchDo = new EventPushBatchDo();
        UserDispatchDetailDo dispatchDetail = new UserDispatchDetailDo();
        dispatchDetail.setUnionId(crowdDetailDo.getUnionId());
        log.info("marketingSend userDispatchDetail:{} crowdDetailDo:{}", dispatchDetail, crowdDetailDo);

        switch (channelEnum) {
            case REMAINING_SUM_CHARGING:
                BigDecimal amount = BigDecimalUtils.toBigDecimal(detailInfo.get("balanceIncreaseAmount"), 0);
                BalanceOptReq balanceOptReq = BalanceOptReq.builder()
                        .userNo(crowdDetailDo.getUserId())
                        .amount(amount)
                        .description((String) detailInfo.get("balanceDesc"))
                        .build();
                return eventDispatchService.increaseBalance(balanceOptReq, (String) dispatchDto.getEventParamMap().get("orderNumber")) ? 1 : 0;
            case SMS:
                if (StringUtils.isEmpty(crowdDetailDo.getMobile())) {
                    log.error("引擎策略发送营销短信无手机号, 策略id:{}, 用户id:{}",
                            strategyId, crowdDetailDo.getUserId(), NoneException.catError());
                    return 0;
                }
                if (null == detailInfo.get("template_id")) {
                    log.error("引擎策略发送营销短信无短信模板, 策略id:{}, 用户id:{}",
                            strategyId, crowdDetailDo.getUserId(), NoneException.catError());
                    return 0;
                }
                String templateId = detailInfo.get("template_id").toString();
                if (StringUtils.isEmpty(templateId)) {
                    log.error("引擎策略发送营销短信无短信模板, 策略id:{}, 用户id:{}",
                            strategyId, crowdDetailDo.getUserId(), NoneException.catError());
                    return 0;
                }
                Map<String, Object> dataMap = Maps.newHashMap();
                if (detailInfo.containsKey("template_params")) {
                    dataMap= JsonUtil.toMap(JsonUtil.toJson(detailInfo.get("template_params")));
                    // 特殊标签移除
                    boolean removeFlag = adsStrategyLabelService.filterLabelMinValue(dataMap, crowdDetailDo.getUserId(), strategyId);
                    boolean checkRet = templateParamService.checkTemplateContent(crowdDetailDo.getApp(), templateId, dataMap, removeFlag, strategyId);
                    if (!checkRet) {
                        log.error("引擎策略发短信内容参数验证不通过, 模板id:{}, 策略id:{}, 用户id:{}", templateId, strategyId, crowdDetailDo.getUserId());
                        return 0;
                    }
                }else {
                    if (bizEventVO != null) {
                        dataMap = templateParamService.getSmsTempParam(crowdDetailDo, strategyId, templateId, bizEventVO);
                    }
                }

                dispatchDetail.setGroupName(groupName);
                dispatchDto.setStrategyChannel(channelEnum.getCode());
                dispatchDto.setStrategyMarketChannelTemplateId(templateId);

                boolean flcLockRet = dispatchFlcService.dispatchFlcLock(dispatchDto, eventPushBatchDo, dispatchDetail, batchNum, crowdDetailDo, this);
                if (flcLockRet) {
                    return -999;
                }
                SmsSingleSendArgs smsSingleSendArgs = new SmsSingleSendArgs();
                smsSingleSendArgs.setMobile(crowdDetailDo.getMobile());
                smsSingleSendArgs.setUserNo(crowdDetailDo.getUserId());
                smsSingleSendArgs.setTemplateId(templateId);
                smsSingleSendArgs.setData(dataMap);
                smsSingleSendArgs.setApp(crowdDetailDo.getApp());
                smsSingleSendArgs.setInnerApp(crowdDetailDo.getInnerApp());
                SmsSingleSendRequester smsSingleSendRequester = new SmsSingleSendRequester();
                smsSingleSendRequester.setArgs(smsSingleSendArgs);

                if (detailInfo.containsKey("signatureKey")) {
                    smsSingleSendArgs.setSignatureKey((String) detailInfo.get("signatureKey"));
                }
                SmsSingleSendResp resp = eventDispatchService.sendSms(smsSingleSendRequester);

                String batchNumUpdate = null;
                if (resp != null && resp.getResponse() != null) {
                    batchNumUpdate = resp.getResponse().getBatchNum();
                }
                eventPushBatchDo.setBatchNum(batchNumUpdate);
                eventPushBatchDo.setSendCode(String.valueOf(resp.getStatus()));
                eventPushBatchDo.setSendMsg(resp.getMessage());
                eventPushBatchDo.setStatus(((EventDispatchServiceImpl) eventDispatchService).sendStatus(channelEnum.getCode(),
                        String.valueOf(resp.getStatus())));

                if (eventPushBatchDo.getId() != null && eventPushBatchDo.getId() > 0) {
                    getEventPushBatchRepository().updateById(eventPushBatchDo.getDetailTableNo(), eventPushBatchDo);
                }
                if (!Objects.equals(resp.isSuccess(), true)) {
                    log.info("触达失败, 更新dispatchDetail表状态, userDispatchDetailId:{}, userid:{}, marketChannel:{}", dispatchDetail.getId(),
                            dispatchDetail.getUserId(), channelEnum.getCode());
                    if (dispatchDetail.getId() != null && dispatchDetail.getId() > 0) {
                        dispatchDetail.setStatus(0);
                        getUserDispatchDetailRepository().updateById(dispatchDto.getDetailTableNo(), dispatchDetail);
                    }
                }
                return resp.isSuccess() ? 1 : 0;
            case AI_PRONTO:
                if (StringUtils.isBlank((String) detailInfo.get("biz_source_code"))
                        && StringUtils.isBlank((String) detailInfo.get("name_type_id"))) {
                    log.error("引擎策略发送ai无业务来源&名单类型, 策略id:{}, 用户id:{}", strategyId, crowdDetailDo.getUserId(), NoneException.catError());
                    return 0;
                }
                String bizSourceCode = Optional.ofNullable(detailInfo.get("biz_source_code")).orElse(StringUtils.EMPTY).toString();
                String nameTypeId = Optional.ofNullable(detailInfo.get("name_type_id")).orElse(StringUtils.EMPTY).toString();
                Map aiParams = JsonUtil.toMap(JsonUtil.toJson(detailInfo.get("template_params")));
                // 特殊标签移除
                adsStrategyLabelService.filterLabelMinValue(aiParams, crowdDetailDo.getUserId(), strategyId);
                dispatchDetail.setGroupName(groupName);
                dispatchDto.setStrategyChannel(channelEnum.getCode());
                dispatchDto.setStrategyMarketChannelTemplateId(StringUtils.isNotBlank(nameTypeId) ? nameTypeId : bizSourceCode);

                boolean aiFlcLockRet = dispatchFlcService.dispatchFlcLock(dispatchDto, eventPushBatchDo, dispatchDetail, batchNum, crowdDetailDo, this);
                if (aiFlcLockRet) {
                    return -999;
                }
                boolean aiIsRespSucced = false;
                String aiRespMsg = null;
                String aiRespCode = null;

                try {
                    //封装参数，发送ai
                    AiSendArgs aiSendArgs = new AiSendArgs();
                    if (StringUtils.isNotBlank(bizSourceCode)) {
                        aiSendArgs.setBizSourceCode(bizSourceCode);
                    }
                    if (StringUtils.isNotBlank(nameTypeId)) {
                        aiSendArgs.setAiChannelType(nameTypeId);
                    }

                    aiSendArgs.setBatchNumber(batchNum);
                    aiSendArgs.setStrategyId(strategyId);
                    aiSendArgs.setStrategyType(dispatchDto.getStrategyRulerEnum().getStrategyType());
                    aiSendArgs.setUserDataList(Collections.singletonList(new AiUserData(crowdDetailDo.getUserId(), crowdDetailDo.getApp(), aiParams)));
                    AiSendResp aiSendResp = eventDispatchService.sendAiPronto(aiSendArgs);
                    aiIsRespSucced = aiSendResp.isSuccess();
                    aiRespMsg = aiSendResp.getMessage();
                    aiRespCode = String.valueOf(aiSendResp.getStatus());
                } finally {
                    eventPushBatchDo.setSendCode(aiRespCode);
                    eventPushBatchDo.setSendMsg(aiRespMsg);

                    eventPushBatchDo.setStatus(((EventDispatchServiceImpl) eventDispatchService).sendStatus(channelEnum.getCode(),
                            String.valueOf(aiRespCode)));
                    if (eventPushBatchDo.getId() != null && eventPushBatchDo.getId() > 0) {
                        getEventPushBatchRepository().updateById(eventPushBatchDo.getDetailTableNo(), eventPushBatchDo);
                    }
                    if (!Objects.equals(aiIsRespSucced, true)) {
                        log.info("触达失败, 更新dispatchDetail表状态, userDispatchDetailId:{}, userid:{}, marketChannel:{}", dispatchDetail.getId(),
                                dispatchDetail.getUserId(), channelEnum.getCode());
                        if (dispatchDetail.getId() != null && dispatchDetail.getId() > 0) {
                            dispatchDetail.setStatus(0);
                            getUserDispatchDetailRepository().updateById(dispatchDto.getDetailTableNo(), dispatchDetail);
                        }
                    }
                }
                return aiIsRespSucced ? 1 : 0;
            case PUSH:
                if (StringUtils.isEmpty((String) detailInfo.get("template_id"))) {
                    log.error("引擎策略发送营销push无push模板, 策略id:{}, 用户id:{}",
                            strategyId, crowdDetailDo.getUserId(), NoneException.catError());
                    return 0;
                }
                String pushTemplateId = detailInfo.get("template_id").toString();
                Map<String, Object> tempParam = Maps.newHashMap();
                if (detailInfo.containsKey("template_params")) {
                    tempParam = JsonUtil.toMap(JsonUtil.toJson(detailInfo.get("template_params")));
                    if (!MapUtil.isEmpty(tempParam)) {
                        // 特殊标签移除
                        boolean removePushFlag = adsStrategyLabelService.filterLabelMinValue(tempParam, crowdDetailDo.getUserId(), strategyId);
                        boolean checkPushRet = templateParamService.checkPushTemplateContent(crowdDetailDo.getApp(), pushTemplateId, tempParam, removePushFlag, strategyId);
                        if (!checkPushRet) {
                            log.error("引擎策略发PUSH内容参数验证不通过, 模板id:{}, 策略id:{}, 用户id:{}", pushTemplateId, strategyId, crowdDetailDo.getUserId());
                            return 0;
                        }
                    } else {
                        log.warn("tempParam map is empty or null");
                    }
                }else {
                    if (bizEventVO != null) {
                        tempParam = templateParamService.getPushTempParam(crowdDetailDo, strategyId, pushTemplateId, bizEventVO);
                    }
                }
                dispatchDetail.setGroupName(groupName);
                dispatchDto.setStrategyChannel(channelEnum.getCode());
                dispatchDto.setStrategyMarketChannelTemplateId(pushTemplateId);

                boolean pushFlcLockRet = dispatchFlcService.dispatchFlcLock(dispatchDto, eventPushBatchDo, dispatchDetail, batchNum, crowdDetailDo, this);
                if (pushFlcLockRet) {
                    return -999;
                }

                PushBaseRequest<SendPushRequest> request = new PushBaseRequest<>();
                SendPushRequest sendPushRequest = new SendPushRequest();
                sendPushRequest.setApp(crowdDetailDo.getApp());
                sendPushRequest.setInnerApp(crowdDetailDo.getInnerApp());
                sendPushRequest.setTemplateId(pushTemplateId);
                sendPushRequest.setBatchNum(batchNum);
                if (tempParam != null) {
                    sendPushRequest.setPushDataList(Collections.singletonList(new PushUserData(crowdDetailDo.getUserId().toString(), tempParam)));
                } else {
                    sendPushRequest.setPushDataList(Collections.singletonList(new PushUserData(crowdDetailDo.getUserId().toString())));
                }

                request.setTimestamp(String.valueOf(Instant.now().getEpochSecond()));
                request.setArgs(sendPushRequest);
                PushResponse<SendPushInfo> pushResponse = eventDispatchService.sendPush(request);

                String pushBatchNumUpdate = null;
                if (pushResponse != null && pushResponse.getResponse() != null) {
                    pushBatchNumUpdate = pushResponse.getResponse().getBatchNum();
                }
                eventPushBatchDo.setBatchNum(pushBatchNumUpdate);
                eventPushBatchDo.setSendCode(String.valueOf(pushResponse.getStatus()));
                eventPushBatchDo.setSendMsg(pushResponse.getMessage());
                eventPushBatchDo.setStatus(((EventDispatchServiceImpl) eventDispatchService).sendStatus(channelEnum.getCode(),
                        String.valueOf(pushResponse.getStatus())));

                if (eventPushBatchDo.getId() != null && eventPushBatchDo.getId() > 0) {
                    getEventPushBatchRepository().updateById(eventPushBatchDo.getDetailTableNo(), eventPushBatchDo);
                }
                if (!Objects.equals(pushResponse.isSuccess(), true)) {
                    log.info("触达失败, 更新dispatchDetail表状态, userDispatchDetailId:{}, userid:{}, marketChannel:{}", dispatchDetail.getId(),
                            dispatchDetail.getUserId(), channelEnum.getCode());
                    if (dispatchDetail.getId() != null && dispatchDetail.getId() > 0) {
                        dispatchDetail.setStatus(0);
                        getUserDispatchDetailRepository().updateById(dispatchDto.getDetailTableNo(), dispatchDetail);
                    }
                }
                return pushResponse.isSuccess() ? 1 : 0;
            case INCREASE_AMOUNT:
                if (!detailInfo.containsKey("increase_type")
                        || !detailInfo.containsKey("increase_amt")
                        || !detailInfo.containsKey("amt_start_time")
                        || !detailInfo.containsKey("amt_end_time")) {
                    log.info("提额参数错误, userId:{}", dispatchDetail.getUserId());
                    return 0;
                }
                BigDecimal increaseAmt = new BigDecimal(detailInfo.get("increase_amt").toString());
                String startTime = detailInfo.get("amt_start_time").toString();
                String endTime = detailInfo.get("amt_end_time").toString();
                String increaseType = detailInfo.get("increase_type").toString();

                IncreaseAmtParamDto increaseAmtParamDto = new IncreaseAmtParamDto();
                increaseAmtParamDto.setIncreaseType(increaseType);
                increaseAmtParamDto.setAmount(increaseAmt.multiply(new BigDecimal("100")).intValue());
                increaseAmtParamDto.setStartTime(startTime);
                increaseAmtParamDto.setEndTime(endTime);
                dispatchDto.setIncreaseAmtParamDto(increaseAmtParamDto);
                if (!increaseAmtParamDto.isValid()) {
                    log.info("提额参数错误, userId:{}, params:{}", dispatchDetail.getUserId(), JsonUtil.toJson(detailInfo));
                    return 0;
                }

                dispatchDetail.setGroupName(groupName);
                dispatchDto.setStrategyChannel(channelEnum.getCode());
                dispatchDto.setStrategyMarketChannelTemplateId(null);

                try {
                    ImmutableTriple<Integer, EventPushBatchDo, Boolean> dispatchResult = eventDispatchService
                            .sendIncreaseAmtEvent(dispatchDto, crowdDetailDo.getApp(), crowdDetailDo.getInnerApp(), crowdDetailDo, null, null, null);
                    if (Objects.equals(0, dispatchResult.getLeft()) && Objects.equals(true, dispatchResult.getRight())) {
                        return -999;
                    }
                    return dispatchResult.getLeft();
                } catch (Exception ex) {
                    log.warn("提额触达失败, userid:{}", detailInfo, ex);
                }
                return 0;
            case VOICE:
            case VOICE_NEW:
                // 不同的取值key
                String paramKey = (channelEnum == StrategyMarketChannelEnum.VOICE) ? "user_type" : "policy_id";
                if (null == detailInfo.get(paramKey)) {
                    log.error("发送电销缺少必须参数, channelEnum = {}, 策略id:{}, 用户id:{}", channelEnum.getCode(),
                            strategyId, crowdDetailDo.getUserId(), NoneException.catError());
                    return 0;
                }
                //新电销新增类型ID
                Object typeId = null;
                Integer decisionPower = null;
                if (channelEnum == StrategyMarketChannelEnum.VOICE_NEW) {
                    if (!detailInfo.containsKey("name_type_id") || null == detailInfo.get("name_type_id")) {
                        log.error("发送电销缺少必须参数, channelEnum = {}, 策略id:{}, 用户id:{}", channelEnum.getCode(),
                                strategyId, crowdDetailDo.getUserId(), NoneException.catError());
                        return 0;
                    }
                    typeId = detailInfo.get("name_type_id");
                    dispatchDto.setNameTypeId(typeId.toString());

                    Object dpValue = detailInfo.get("decision_power");
                    try {
                        if (dpValue != null && !dpValue.toString().isEmpty()) {
                            decisionPower = Integer.parseInt(dpValue.toString());
                        }
                    } catch (NumberFormatException e) {
                        log.error("decision_power参数格式错误 value={}", dpValue, e);
                    }
                }
                Integer userType = Integer.valueOf(detailInfo.get(paramKey).toString());
                dispatchDetail.setGroupName(groupName);
                dispatchDto.setStrategyChannel(channelEnum.getCode());
                dispatchDto.setStrategyMarketChannelTemplateId(userType.toString());

                flcLockRet = dispatchFlcService.dispatchFlcLock(dispatchDto, eventPushBatchDo, dispatchDetail,
                        batchNum, crowdDetailDo, this);
                if (flcLockRet) {
                    return -999;
                }
                boolean isRespSucced = false;
                String respMsg = null;
                String respCode = null;
                try {
                    if (channelEnum == StrategyMarketChannelEnum.VOICE_NEW) {
                        TelePushArgs policyDetail = telePushService.getTelePushArgs(userType, Collections.singletonList(crowdDetailDo), dispatchDto, crowdDetailDo.getApp());
                        policyDetail.setBatchNumber(batchNum);
                        // 设置到每个UserData中
                        List<TelePushArgs.UserData> userDataList = policyDetail.getData();
                        if (userDataList != null) {
                            if (decisionPower != null) {
                                for (TelePushArgs.UserData userData : userDataList) {
                                    // 创建一个新的 ExtData 对象并设置 decision_power
                                    TelePushArgs.ExtData extData = TelePushArgs.ExtData.builder()
                                            .decision_power(decisionPower)
                                            .build();
                                    // 将 ExtData 对象设置到 userData 中
                                    userData.addExtData(extData);
                                }
                            }
                        }
                        TelePushResp telePushResp = eventDispatchService.sendTeleNew(policyDetail);
                        isRespSucced = telePushResp.isSuccess();
                        respMsg = telePushResp.getMessage();
                        respCode = String.valueOf(telePushResp.getStatus());
                    } else {
                        TeleSaveBatchRequest teleSaveBatchRequest = new TeleSaveBatchRequest();
                        TeleSaveBatchArgs teleSaveBatchArgs = new TeleSaveBatchArgs();
                        teleSaveBatchArgs.setCreditIdArr(Collections.singletonList(crowdDetailDo.getUserId()));
                        teleSaveBatchArgs.setUserType(userType);
                        teleSaveBatchArgs.setFlowNo(batchNum);
                        teleSaveBatchArgs.setBatchCount(1);
                        teleSaveBatchArgs.setCurrentBatch(1);
                        teleSaveBatchRequest.setArgs(teleSaveBatchArgs);

                        TeleSaveBatchResp teleSaveBatchResp = eventDispatchService
                                .sendTele(teleSaveBatchRequest);
                        isRespSucced = teleSaveBatchResp.isSuccess();
                        respMsg = teleSaveBatchResp.getMessage();
                        respCode = teleSaveBatchResp.getCode();
                    }
                } finally {
                    eventPushBatchDo.setSendCode(respCode);
                    eventPushBatchDo.setSendMsg(respMsg);

                    eventPushBatchDo.setStatus(((EventDispatchServiceImpl) eventDispatchService).sendStatus(channelEnum.getCode(),
                            String.valueOf(respCode)));
                    if (eventPushBatchDo.getId() != null && eventPushBatchDo.getId() > 0) {
                        getEventPushBatchRepository().updateById(eventPushBatchDo.getDetailTableNo(), eventPushBatchDo);
                    }
                    if (!Objects.equals(isRespSucced, true)) {
                        log.info("触达失败, 更新dispatchDetail表状态, userDispatchDetailId:{}, userid:{}, marketChannel:{}", dispatchDetail.getId(),
                                dispatchDetail.getUserId(), channelEnum.getCode());
                        if (dispatchDetail.getId() != null && dispatchDetail.getId() > 0) {
                            dispatchDetail.setStatus(0);
                            getUserDispatchDetailRepository().updateById(dispatchDto.getDetailTableNo(), dispatchDetail);
                        }
                    }
                }
                return isRespSucced ? 1 : 0;
            case SALE_TICKET:
            case X_DAY_INTEREST_FREE:   //当前“X天免息”也是一种优惠券（2024-07-30）
                if (detailInfo.get("batch") == null) {
                    log.error("无优惠券的信息, 策略id:{}, 用户id:{}",
                            strategyId, crowdDetailDo.getUserId(), NoneException.catError());
                    return 0;
                }
                CouponSendBatchReq.User user = new CouponSendBatchReq.User(crowdDetailDo.getUserId(), crowdDetailDo.getMobile(), crowdDetailDo.getMobile());
                List<PredictDecisionDto.DetailCouponDto> couponDtos = JsonUtil.toList(JsonUtil.toJson(detailInfo.get("batch")),
                        PredictDecisionDto.DetailCouponDto.class);
                if (CollectionUtils.isEmpty(couponDtos)) {
                    return -1;
                }
                for (PredictDecisionDto.DetailCouponDto couponDto : couponDtos) {
                    dispatchDetail = new UserDispatchDetailDo();
                    eventPushBatchDo = new EventPushBatchDo();

                    batchNum = serialNumberUtil.batchNum();

                    dispatchDetail.setGroupName(groupName);
                    dispatchDto.setStrategyChannel(channelEnum.getCode());
                    dispatchDto.setStrategyMarketChannelTemplateId(String.valueOf(couponDto.getActivity_id()));

                    flcLockRet = dispatchFlcService
                            .dispatchFlcLock(dispatchDto, eventPushBatchDo, dispatchDetail, batchNum, crowdDetailDo, this);
                    if (flcLockRet) {
                        continue;
                    }

                    CouponSendBatchReq couponSendBatchReq = new CouponSendBatchReq();
                    couponSendBatchReq.setActivityId(couponDto.getActivity_id());
                    couponSendBatchReq.setBatchNum(batchNum);
                    couponSendBatchReq.setApp(crowdDetailDo.getApp());
                    couponSendBatchReq.setInnerApp(crowdDetailDo.getInnerApp());
                    couponSendBatchReq.setAllBatchCount(1);
                    couponSendBatchReq.setUserList(Collections.singletonList(user));
                    BaseCouponResponse<Object> couponRet = eventDispatchService.sendCoupon(couponSendBatchReq);
                    if (couponRet.isSuccess()) {
                        ret = ret + 1;
                    }
                    eventPushBatchDo.setSendCode(String.valueOf(couponRet.getStatus()));
                    eventPushBatchDo.setSendMsg(couponRet.getMessage());

                    eventPushBatchDo.setStatus(((EventDispatchServiceImpl) eventDispatchService).sendStatus(channelEnum.getCode(),
                            String.valueOf(couponRet.getStatus())));

                    if (eventPushBatchDo.getId() != null && eventPushBatchDo.getId() > 0) {
                        getEventPushBatchRepository().updateById(eventPushBatchDo.getDetailTableNo(), eventPushBatchDo);
                    }
                    if (!Objects.equals(couponRet.isSuccess(), true)) {
                        log.info("触达失败, 更新dispatchDetail表状态, userDispatchDetailId:{}, userid:{}, marketChannel:{}", dispatchDetail.getId(),
                                dispatchDetail.getUserId(), channelEnum.getCode());
                        if (dispatchDetail.getId() != null && dispatchDetail.getId() > 0) {
                            dispatchDetail.setStatus(0);
                            getUserDispatchDetailRepository().updateById(dispatchDto.getDetailTableNo(), dispatchDetail);
                        }
                    }
                }
                return ret;
            case NONE:
                UserBlankGroupDetailDo userBlankGroupDetailDo = new UserBlankGroupDetailDo();
                userBlankGroupDetailDo.setUserId(crowdDetailDo.getUserId());
                userBlankGroupDetailDo.setMobile(crowdDetailDo.getMobile());
                userBlankGroupDetailDo.setBatchNum(serialNumberUtil.batchNum());
                userBlankGroupDetailDo.setCrowdPackId(crowdDetailDo.getCrowdId() != null ? crowdDetailDo.getCrowdId() : -1L);
                userBlankGroupDetailDo.setStrategyId(strategyId);
                userBlankGroupDetailDo.setStrategyChannelId(0L);
                userBlankGroupDetailDo.setMarketChannel(channelEnum.getCode());
                userBlankGroupDetailDo.setStrategyExecId(LocalDateTimeUtil.format(LocalDate.now(), "yyMMdd")
                        + String.format("%010d", strategyId));
                userBlankGroupDetailDo.setStatus(UserDispatchDetailStatusEnum.SUCCESS.getStatus());
                userBlankGroupDetailDo.setDispatchTime(LocalDateTime.now());

                userBlankGroupDetailDo.setMessageId(dispatchDto.getMessageId());
                userBlankGroupDetailDo.setTriggerDatetime(dispatchDto.getTriggerDatetime());
                userBlankGroupDetailDo.setGroupName(groupName);
                userBlankGroupDetailDo.setBizEventType(dispatchDto.getBizEventType());
                userBlankGroupDetailDo.setStrategyGroupId(dispatchDto.getStrategyGroupId());
                userBlankGroupDetailDo.setStrategyGroupName(dispatchDto.getStrategyGroupName());
                userBlankGroupDetailDo.setUnionId(crowdDetailDo.getUnionId());
                userBlankGroupDetailService.add(userBlankGroupDetailDo, dispatchDto.getStrategyRulerEnum());
                return -1;
            case RE_DECISION:
                try {
                    if (MapUtils.isEmpty(detailInfo)) {
                        return 0;
                    }
                    if (Objects.isNull(detailInfo.get("reinput_delay_second"))) {
                        log.warn("业务引擎-延迟决策,下发异常. 策略id={},用户id={}", strategyId, crowdDetailDo.getUserId());
                        return 0;
                    }
                    int reInputDelaySecond = Integer.parseInt(detailInfo.get("reinput_delay_second").toString());
                    if (reInputDelaySecond <= 0 || reInputDelaySecond > 60 * 60 * 24) {
                        log.warn("业务引擎-延迟决策,下发异常. 策略id={},用户id={},延迟时间={}", strategyId, crowdDetailDo.getUserId(), reInputDelaySecond);
                        return 0;
                    }

                    if (sendTypeEnum == MarketingSendTypeEnum.EVENT_STRATEGY) {
                        reDecisionService.reDecision(groupName, reInputDelaySecond, bizEventVO);
                    } else if (sendTypeEnum == MarketingSendTypeEnum.OFFLINE_STRATEGY) {
                        Object reInputCount = detailInfo.get("reInputCount");
                        if (reInputCount == null) {
                            return 0;
                        }
                        offlineReDecisionService.reDecision(dispatchDto, crowdDetailDo, Integer.parseInt(reInputCount.toString()));
                    }
                } catch (Exception e) {
                    log.error("业务引擎-延迟决策,处理异常.", e);
                }
                return -1;
            case DELAYED_POPUP:
                try {
                    if (MapUtils.isEmpty(detailInfo)) {
                        return -1;
                    }
                    if (Objects.isNull(detailInfo.get("popupId")) || Objects.isNull(detailInfo.get("expiredHours"))) {
                        log.warn("短信/PUSH 下发延迟弹窗, 引擎下发异常. 策略id={},用户id={}", strategyId, crowdDetailDo.getUserId());
                        return -1;
                    }

                    Map<String, Object> delayed_popup = Maps.newHashMap();
                    delayed_popup.put(DelayedPopupChannelDto.POPUP_ID, detailInfo.get(DelayedPopupChannelDto.POPUP_ID));
                    delayed_popup.put(DelayedPopupChannelDto.EXPIRED_HOURS, detailInfo.get(DelayedPopupChannelDto.EXPIRED_HOURS));

                    Map<String, Object> popup_params = Maps.newHashMap();
                    popup_params.put(DelayedPopupChannelDto.POPUP_PARAMS, detailInfo.get(DelayedPopupChannelDto.POPUP_PARAMS));

                    Map<String, Object> popupChannelMap = Maps.newHashMap();
                    popupChannelMap.put(DelayedPopupChannelDto.DELAYED_POPUP, delayed_popup);
                    popupChannelMap.put(DelayedPopupChannelDto.POPUP_PARAMS, popup_params);

                    DelayedPopupChannelDto delayedPopupChannelDto = DelayedPopupChannelDto.convertToDelayedPopupChannelDto(popupChannelMap);
                    boolean sendResult = eventDispatchService.sendDelayedPopupEvent(delayedPopupChannelDto, crowdDetailDo.getUserId());
                    return sendResult ? 1 : 0;
                } catch (Exception e) {
                    log.error("短信/PUSH 下发延迟弹窗, 引擎下发处理异常.", e);
                }
                return -1;
            case LIFE_RIGHTS:
                try {
                    if (detailInfo.get("batch") == null) {
                        log.error("无生活权益的信息, 策略id:{}, 用户id:{}",
                                strategyId, crowdDetailDo.getUserId(), NoneException.catError());
                        return -1;
                    }
                    CouponSendBatchReq.User userLifeRights = new CouponSendBatchReq.User(crowdDetailDo.getUserId(), crowdDetailDo.getMobile(), crowdDetailDo.getMobile());
                    List<PredictDecisionDto.DetailLifeRightsDto> detailLifeRightsDtoList = JsonUtil.toList(JsonUtil.toJson(detailInfo.get("batch")),
                            PredictDecisionDto.DetailLifeRightsDto.class);
                    if (CollectionUtils.isEmpty(detailLifeRightsDtoList)) {
                        return -1;
                    }
                    for (PredictDecisionDto.DetailLifeRightsDto detailLifeRightsDto : detailLifeRightsDtoList) {
                        dispatchDetail = new UserDispatchDetailDo();
                        eventPushBatchDo = new EventPushBatchDo();

                        batchNum = serialNumberUtil.batchNum();

                        dispatchDetail.setGroupName(groupName);
                        dispatchDto.setStrategyChannel(channelEnum.getCode());
                        dispatchDto.setStrategyMarketChannelTemplateId(String.valueOf(detailLifeRightsDto.getGoods_id()));

                        GoodsDetail goodsDetail = goodsService.getGoodsDetailByGoodsId(detailLifeRightsDto.getGoods_id());
                        flcLockRet = dispatchFlcService
                                .dispatchFlcLock(dispatchDto, eventPushBatchDo, dispatchDetail, batchNum, crowdDetailDo, this);
                        if (flcLockRet) {
                            continue;
                        }

                        CouponSendBatchReq couponSendBatchReq = new CouponSendBatchReq();
                        couponSendBatchReq.setActivityId(detailLifeRightsDto.getActivity_id());
                        couponSendBatchReq.setBatchNum(batchNum);
                        couponSendBatchReq.setApp(crowdDetailDo.getApp());
                        couponSendBatchReq.setInnerApp(crowdDetailDo.getInnerApp());
                        couponSendBatchReq.setAllBatchCount(1);
                        couponSendBatchReq.setGoodsId(String.valueOf(detailLifeRightsDto.getGoods_id()));
                        couponSendBatchReq.setGoodsName(goodsDetail.getGoodsName());
                        couponSendBatchReq.setType(2);
                        couponSendBatchReq.setJumpType(goodsDetail.getJumpType());
                        couponSendBatchReq.setJumpUrl(goodsDetail.getJumpUrl());
                        couponSendBatchReq.setUserList(Collections.singletonList(userLifeRights));
                        BaseCouponResponse<Object> couponRet = eventDispatchService.sendCoupon(couponSendBatchReq);
                        if (couponRet.isSuccess()) {
                            ret = ret + 1;
                        }
                        eventPushBatchDo.setSendCode(String.valueOf(couponRet.getStatus()));
                        eventPushBatchDo.setSendMsg(couponRet.getMessage());

                        eventPushBatchDo.setStatus(((EventDispatchServiceImpl) eventDispatchService).sendStatus(channelEnum.getCode(),
                                String.valueOf(couponRet.getStatus())));

                        if (eventPushBatchDo.getId() != null && eventPushBatchDo.getId() > 0) {
                            getEventPushBatchRepository().updateById(eventPushBatchDo.getDetailTableNo(), eventPushBatchDo);
                        }
                        if (!Objects.equals(couponRet.isSuccess(), true)) {
                            log.info("触达失败, 更新dispatchDetail表状态, userDispatchDetailId:{}, userid:{}, marketChannel:{}", dispatchDetail.getId(),
                                    dispatchDetail.getUserId(), channelEnum.getCode());
                            if (dispatchDetail.getId() != null && dispatchDetail.getId() > 0) {
                                dispatchDetail.setStatus(0);
                                getUserDispatchDetailRepository().updateById(dispatchDto.getDetailTableNo(), dispatchDetail);
                            }
                        }
                    }
                    return ret;
                } catch (Exception e) {
                    log.error("业务引擎-生活权益,处理异常.", e);
                }
                return -1;
            default:
                break;
        }
        return -1;
    }

    private EventPushBatchDo saveEventPushBatchAndDispatchDetail(StrategyMarketChannelEnum channelEnum, String groupName,
                                                                 BizEventVO bizEventVO, String templateId, String batchNum) {
        EventPushBatchDo eventPushBatch = new EventPushBatchDo();
        eventPushBatch.setStrategyId(bizEventVO.getStrategyId());
        eventPushBatch.setMarketChannelId(0L);
        eventPushBatch.setExecLogId(0L);
        eventPushBatch.setMarketChannel(channelEnum.getCode());
        eventPushBatch.setUserId(bizEventVO.getAppUserId());
        eventPushBatch.setMobile(bizEventVO.getMobile());
        eventPushBatch.setApp(bizEventVO.getApp());
        eventPushBatch.setTemplateId(Optional.ofNullable(templateId).orElse(""));
        eventPushBatch.setBatchNum(batchNum);
        eventPushBatch.setInnerBatchNum(serialNumberUtil.batchNum());
        eventPushBatch.setSendCode("-1");
        eventPushBatch.setSendMsg("failed");
        eventPushBatch.setStatus(2);
        eventPushBatch.setGroupName(groupName);
        eventPushBatch.setBizEventType(bizEventVO.getBizEventType());
        String detailTableNo = getTableNo(bizEventVO.getTriggerDatetime());
        eventPushBatch.setDetailTableNo(detailTableNo);

        getEventPushBatchRepository().insert(detailTableNo, eventPushBatch);

        UserDispatchDetailDo dispatchDetail = new UserDispatchDetailDo();
        dispatchDetail.setUserId(bizEventVO.getAppUserId());
        dispatchDetail.setBatchNum(eventPushBatch.getInnerBatchNum());
        dispatchDetail.setCrowdPackId(null);
        dispatchDetail.setStrategyId(bizEventVO.getStrategyId());
        dispatchDetail.setStrategyChannelId(0L);
        dispatchDetail.setMarketChannel(channelEnum.getCode());
        dispatchDetail.setStrategyExecId("0");
        dispatchDetail.setExecLogId(eventPushBatch.getExecLogId());
        dispatchDetail.setStatus(UserDispatchDetailStatusEnum.INIT.getStatus());
        dispatchDetail.setDispatchTime(LocalDateTime.now());
        dispatchDetail.setMessageId(bizEventVO.getMessageId());
        dispatchDetail.setTriggerDatetime(bizEventVO.getTriggerDatetime());
        dispatchDetail.setGroupName(groupName);
        dispatchDetail.setBizEventType(bizEventVO.getBizEventType());
        dispatchDetail.setBizType(getBizType(bizEventVO.getStrategyId())); // 添加业务线类型
        dispatchDetail.setUnionId(bizEventVO.getUnionId()); // 添加统一ID
        getUserDispatchDetailRepository()
                .saveBatchWithoutTx(detailTableNo, Collections.singletonList(dispatchDetail));
        return eventPushBatch;
    }

    /*
     * 切换触达app
     * <p>
     * 【麻雀-T0策略-xyf01提额的下发fxk的短信+标签+事件】
     * https://www.tapd.cn/60211538/prong/stories/view/1160211538001058941
     *
     * @param crowdDetail   人群明细
     * @param marketChannel 渠道
     */
    private Boolean convertUserInfo(CrowdDetailDo crowdDetail, StrategyMarketChannelDo marketChannel) {
        try {
            String dispatchApp = marketChannel.getDispatchApp();
            if (StringUtils.isBlank(dispatchApp) || StringUtils.equalsIgnoreCase(dispatchApp, "default")) {
                return Boolean.TRUE;
            }

            log.info("convertUserInfo,原app={},原AppUserId={}", crowdDetail.getApp(), crowdDetail.getUserId());
            crowdDetail.setApp(dispatchApp);
            if (StringUtils.isBlank(crowdDetail.getMobile())) {
                UserIdResp users = userCenterClient.getUsersById(crowdDetail.getUserId());
                users.getUserList().stream().findFirst().map(UserIdDetailResp::getMobile).ifPresent(crowdDetail::setMobile);
            }
            UserInfoResp resp = userCenterClient.getUserByMobile(crowdDetail.getMobile(), crowdDetail.getApp());
            if (Objects.isNull(resp)) {
                log.warn("convertUserInfo,用户记录不存在,mobile={},app={}", crowdDetail.getMobile(), crowdDetail.getApp());
                return Boolean.FALSE;
            }
            crowdDetail.setUserId(resp.getCreditUserId());
            log.info("convertUserInfo,新app={},新AppUserId={}", crowdDetail.getApp(), crowdDetail.getUserId());
            return Boolean.TRUE;
        } catch (Exception e) {
            log.warn("convertUserInfo error!", e);
            return Boolean.FALSE;
        }
    }

    public DispatchDto convertDispatchDto(BizEventVO bizEventVO, Long strategyExecLog, StrategyMarketChannelEnum channelEnum,
                                          StrategyRulerEnum strategyRulerEnum, String templateId) {
        DispatchDto dispatch = new DispatchDto();
        dispatch.setMessageId(bizEventVO.getMessageId());

        dispatch.setStrategyExecId("0");
        dispatch.setDetailTableNo(getTableNo(bizEventVO.getTriggerDatetime()));
        dispatch.setStrategyId(bizEventVO.getStrategyId());
        dispatch.setBizType(getBizType(bizEventVO.getStrategyId())); // 添加业务线类型
        dispatch.setStrategyGroupId(bizEventVO.getStrategyGroupId());
        dispatch.setStrategyGroupName(bizEventVO.getGroupName());
        dispatch.setStrategyChannelId(0L);
        dispatch.setStrategyChannelXxlJobId(0);
        dispatch.setStrategyChannel(Optional.ofNullable(channelEnum).orElse(StrategyMarketChannelEnum.FILTER).getCode());
        dispatch.setStrategyMarketChannelTemplateId(templateId);
        dispatch.setStrategyExecLogId(strategyExecLog);
        dispatch.setStrategyExecLogRetryId(0L);
        dispatch.setMessageId(bizEventVO.getMessageId());
        dispatch.setTriggerDatetime(bizEventVO.getTriggerDatetime());
        dispatch.setStrategyRulerEnum(strategyRulerEnum);
        dispatch.setBizEventType(bizEventVO.getBizEventType());

        StrategyMarketChannelDo strategyMarketChannelDo = new StrategyMarketChannelDo();
        strategyMarketChannelDo.setId(0L);
        // 设置营销渠道
        strategyMarketChannelDo.setMarketChannel(Optional.ofNullable(channelEnum).orElse(StrategyMarketChannelEnum.FILTER).getCode());
        strategyMarketChannelDo.setStrategyId(bizEventVO.getStrategyId());

        dispatch.setStrategyMarketChannelDo(strategyMarketChannelDo);

        dispatch.setEventParamMap(Optional.ofNullable(bizEventVO.getExt()).orElse(Maps.newHashMap()));
        return dispatch;
    }

    public String getBizType(Long strategyId) {
        StrategyDo strategyDo = cacheStrategyService.selectById(strategyId);
        if (strategyDo != null) {
            return strategyDo.getBusinessType();
        }
        return "";
    }
}
