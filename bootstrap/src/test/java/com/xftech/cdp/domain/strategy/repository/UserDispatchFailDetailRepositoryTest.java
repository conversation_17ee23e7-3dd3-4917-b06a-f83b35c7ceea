/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.repository;

import com.xftech.cdp.BaseTest;
import com.xftech.cdp.infra.repository.cdp.param.po.UserDispatchFailDetailDo;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.Arrays;

/**
 *
 * <AUTHOR>
 * @version $ UserDispatchFailDetailRepositoryTest, v 0.1 2024/3/21 14:40 yye.xu Exp $
 */

public class UserDispatchFailDetailRepositoryTest extends BaseTest {

    @Autowired
    UserDispatchFailDetailRepository userDispatchFailDetailRepository;

    @Test
    public void test(){
        UserDispatchFailDetailDo userDispatchFailDetailDo = new UserDispatchFailDetailDo();
        userDispatchFailDetailDo.setStatus(0);
        userDispatchFailDetailDo.setStrategyId(0L);
        userDispatchFailDetailDo.setAppUserId(0L);
        userDispatchFailDetailDo.setFailReason("");
        userDispatchFailDetailDo.setMobile("17621372484");
        userDispatchFailDetailDo.setGroupName("");
        userDispatchFailDetailDo.setMarketChannel(0);
        userDispatchFailDetailDo.setApp("1");
        userDispatchFailDetailDo.setDispatchTime(LocalDateTime.now());
        userDispatchFailDetailDo.setStatus(0);
        userDispatchFailDetailRepository.batchSave(Arrays.asList(userDispatchFailDetailDo));
    }
}