<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="strategyExecCycleCounterMapper">
  <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyExecCycleCounterDo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="strategy_id" jdbcType="BIGINT" property="strategyId" />
    <result column="sum_val" jdbcType="INTEGER" property="sumVal" />
    <result column="date_value" jdbcType="INTEGER" property="dateValue" />
    <result column="d_flag" jdbcType="TINYINT" property="dFlag" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, strategy_id, sum_val, date_value, d_flag, created_time, updated_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from strategy_exec_cycle_counter
    where id = #{id,jdbcType=BIGINT}
  </select>

  <insert id="insertSelective" keyProperty="id" useGeneratedKeys="true" parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyExecCycleCounterDo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into strategy_exec_cycle_counter
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="strategyId != null">
        strategy_id,
      </if>
      <if test="sumVal != null">
        sum_val,
      </if>
      <if test="dateValue != null">
        date_value,
      </if>
      <if test="dFlag != null">
        d_flag,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="updatedTime != null">
        updated_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="strategyId != null">
        #{strategyId,jdbcType=BIGINT},
      </if>
      <if test="sumVal != null">
        #{sumVal,jdbcType=INTEGER},
      </if>
      <if test="dateValue != null">
        #{dateValue,jdbcType=INTEGER},
      </if>
      <if test="dFlag != null">
        #{dFlag,jdbcType=TINYINT},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        #{updatedTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyExecCycleCounterDo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update strategy_exec_cycle_counter
    <set>
      <if test="strategyId != null">
        strategy_id = #{strategyId,jdbcType=BIGINT},
      </if>
      <if test="sumVal != null">
        sum_val = #{sumVal,jdbcType=INTEGER},
      </if>
      <if test="dateValue != null">
        date_value = #{dateValue,jdbcType=INTEGER},
      </if>
      <if test="dFlag != null">
        d_flag = #{dFlag,jdbcType=TINYINT},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyExecCycleCounterDo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update strategy_exec_cycle_counter
    set strategy_id = #{strategyId,jdbcType=BIGINT},
      sum_val = #{sumVal,jdbcType=INTEGER},
      date_value = #{dateValue,jdbcType=INTEGER},
      d_flag = #{dFlag,jdbcType=TINYINT},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      updated_time = #{updatedTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>


  <select id="selectStrategyCycleCounterByStrategyId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from strategy_exec_cycle_counter
    where strategy_id = #{strategyId} and d_flag = 0
    order by id desc
    limit 1
  </select>

  <select id="selectStrategyCycleCounter" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from strategy_exec_cycle_counter
    where strategy_id = #{strategyId} and date_value = #{dateValue}
    order by id desc
    limit 1
  </select>

  <update id="updateStrategyCycleCounter">
    update strategy_exec_cycle_counter
    set sum_val = sum_val + 1,
     date_value = #{dateValue}
    where strategy_id = #{strategyId} and date_value != #{dateValue}
    order by id desc
    limit 1
  </update>
</mapper>