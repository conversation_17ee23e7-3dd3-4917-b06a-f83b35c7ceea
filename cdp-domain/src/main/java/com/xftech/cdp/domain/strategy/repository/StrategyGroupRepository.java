package com.xftech.cdp.domain.strategy.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyGroupDo;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 策略分组信息表操作
 *
 * <AUTHOR>
 * @since 2023/2/21
 */

@Component
public class StrategyGroupRepository {
    /**
     * 根据策略id删除分组信息记录
     *
     * @param strategyId 策略id
     */
    public void deleteByStrategyId(Long strategyId) {
        DBUtil.delete("strategyGroup.deleteByStrategyId", strategyId);
    }

    /**
     * 根据策略id查询分组信息记录
     *
     * @param strategyId 策略id
     * @return 该策略下的分组信息列表
     */
    public List<StrategyGroupDo> selectListByStrategyId(Long strategyId) {
        return DBUtil.selectList("strategyGroup.selectListByStrategyId", strategyId);
    }

    /**
     * 根据策略id查询分组信息记录
     *
     * @param strategyId 策略id
     * @return 该策略下的分组信息列表
     */
    public List<StrategyGroupDo> selectAllByStrategyId(Long strategyId) {
        return DBUtil.selectList("strategyGroup.selectAllByStrategyId", strategyId);
    }

    /**
     * 根据分组id（主键）查询某条记录
     *
     * @param strategyGroupId 分组id
     * @return 该id对应的一条记录
     */
    public StrategyGroupDo selectById(Long strategyGroupId) {
        return DBUtil.selectOne("strategyGroup.selectByPrimaryKey", strategyGroupId);

    }

    /**
     * 插入一条分组信息记录
     *
     * @param strategyGroupDo 分组信息记录
     */
    public void insert(StrategyGroupDo strategyGroupDo) {
        DBUtil.insert("strategyGroup.insertSelective", strategyGroupDo);
    }

    /**
     * 根据主键id更新分组信息记录
     *
     * @param strategyGroupDo 分组信息记录
     */
    public void update(StrategyGroupDo strategyGroupDo) {
        DBUtil.update("strategyGroup.updateByPrimaryKeySelective", strategyGroupDo);
    }

    /**
     * 根据主键id更新分组信息记录
     *
     * @param ids id列表
     */
    public void deleteBatch(List<Long> ids) {
        DBUtil.deleteBatchWithoutTx("strategyGroup.deleteByPrimaryKey", ids);
    }

    /**
     * 根据策略ID查询所有不营销组配置
     *
     * @param strategyId 策略ID
     * @return 不营销组配置
     */
    public List<StrategyGroupDo> selectBlankGroup(Long strategyId) {
        return DBUtil.selectList("strategyGroup.selectBlankGroup", strategyId);
    }
}
