<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="eventPushBatchMapper">

    <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.strategy.po.EventPushBatchDo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="strategyId" column="strategy_id" jdbcType="BIGINT"/>
        <result property="marketChannelId" column="market_channel_id" jdbcType="BIGINT"/>
        <result property="execLogId" column="exec_log_id" jdbcType="BIGINT"/>
        <result property="marketChannel" column="market_channel" jdbcType="SMALLINT"/>
        <result property="userId" column="user_id" jdbcType="BIGINT"/>
        <result property="app" column="app" jdbcType="VARCHAR"/>
        <result property="templateId" column="template_id" jdbcType="VARCHAR"/>
        <result property="batchNum" column="batch_num" jdbcType="VARCHAR"/>
        <result property="innerBatchNum" column="inner_batch_num" jdbcType="VARCHAR"/>
        <result property="sendCode" column="send_code" jdbcType="VARCHAR"/>
        <result property="sendMsg" column="send_msg" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="SMALLINT"/>
        <result property="queryStatus" column="query_status" jdbcType="SMALLINT"/>
        <result property="detailTableNo" column="detail_table_no" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="groupName" column="group_name" jdbcType="VARCHAR"/>
        <result property="strategyGroupId" column="strategy_group_id" jdbcType="BIGINT"/>
        <result property="strategyGroupName" column="strategy_group_name" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,strategy_id,market_channel_id,
        exec_log_id,market_channel,user_id,
        app,template_id,
        batch_num,inner_batch_num,send_code,send_msg,
        status,query_status,detail_table_no,
        created_time,group_name,strategy_group_id,strategy_group_name
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ${tableName}
        <where>
            and id = #{id,jdbcType=BIGINT}
        </where>
    </select>

    <select id="getByChannelAndBatchNum" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ${tableName}
        <where>
            and market_channel = #{channel}
            and batch_num = #{flowNo}
        </where>
    </select>

    <select id="selectCountByStatusAndExecLogId"  resultType="integer">
        select
        count(*)
        from ${tableName}
        where exec_log_id = #{execLogId}
        <if test="status != null">
            and status = #{status}
        </if>
    </select>

    <select id="countByDetail"  resultType="integer">
        select
        count(distinct user_id)
        from ${tableName}
        where strategy_id = #{strategyDoId}
          and strategy_group_id = #{strategyGroupId}
          and group_name = #{groupId}
          and market_channel =  #{marketChannel}
          and template_id = #{templateId}
        <if test="statusList !=null and statusList.size()>0">
            and status in
            <foreach collection="statusList" item="status" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
        and created_time  &gt;= #{timeStart}
        and created_time  &lt; #{timeEnd}
    </select>

    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.EventPushBatchDo" useGeneratedKeys="true">
        insert into ${tableName}
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="strategyId != null">strategy_id,</if>
            <if test="marketChannelId != null">market_channel_id,</if>
            <if test="execLogId != null">exec_log_id,</if>
            <if test="marketChannel != null">market_channel,</if>
            <if test="userId != null">user_id,</if>
            <if test="app != null">app,</if>
            <if test="templateId != null">template_id,</if>
            <if test="batchNum != null">batch_num,</if>
            <if test="innerBatchNum != null">inner_batch_num,</if>
            <if test="sendCode != null">send_code,</if>
            <if test="sendMsg != null">send_msg,</if>
            <if test="status != null">status,</if>
            <if test="queryStatus != null">query_status,</if>
            <if test="detailTableNo != null">detail_table_no,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="groupName != null">group_name,</if>
            <if test="bizEventType != null">biz_event_type,</if>
            <if test="strategyGroupId != null">strategy_group_id,</if>
            <if test="strategyGroupName != null">strategy_group_name,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="strategyId != null">#{strategyId,jdbcType=BIGINT},</if>
            <if test="marketChannelId != null">#{marketChannelId,jdbcType=BIGINT},</if>
            <if test="execLogId != null">#{execLogId,jdbcType=BIGINT},</if>
            <if test="marketChannel != null">#{marketChannel,jdbcType=SMALLINT},</if>
            <if test="userId != null">#{userId,jdbcType=BIGINT},</if>
            <if test="app != null">#{app,jdbcType=VARCHAR},</if>
            <if test="templateId != null">#{templateId,jdbcType=VARCHAR},</if>
            <if test="batchNum != null">#{batchNum,jdbcType=VARCHAR},</if>
            <if test="innerBatchNum != null">#{innerBatchNum,jdbcType=VARCHAR},</if>
            <if test="sendCode != null">#{sendCode,jdbcType=VARCHAR},</if>
            <if test="sendMsg != null">#{sendMsg,jdbcType=VARCHAR},</if>
            <if test="status != null">#{status,jdbcType=SMALLINT},</if>
            <if test="queryStatus != null">#{queryStatus,jdbcType=SMALLINT},</if>
            <if test="detailTableNo != null">#{detailTableNo,jdbcType=VARCHAR},</if>
            <if test="createdTime != null">#{createdTime,jdbcType=TIMESTAMP},</if>
            <if test="groupName != null">#{groupName,jdbcType=VARCHAR},</if>
            <if test="bizEventType != null">#{bizEventType,jdbcType=VARCHAR},</if>
            <if test="strategyGroupId != null">#{strategyGroupId,jdbcType=BIGINT},</if>
            <if test="strategyGroupName != null">#{strategyGroupName,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.EventPushBatchDo">
        update ${tableName}
        <set>
            <if test="strategyId != null">
                strategy_id = #{strategyId,jdbcType=BIGINT},
            </if>
            <if test="marketChannelId != null">
                market_channel_id = #{marketChannelId,jdbcType=BIGINT},
            </if>
            <if test="execLogId != null">
                exec_log_id = #{execLogId,jdbcType=BIGINT},
            </if>
            <if test="marketChannel != null">
                market_channel = #{marketChannel,jdbcType=SMALLINT},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="app != null">
                app = #{app,jdbcType=VARCHAR},
            </if>
            <if test="templateId != null">
                template_id = #{templateId,jdbcType=VARCHAR},
            </if>
            <if test="batchNum != null">
                batch_num = #{batchNum,jdbcType=VARCHAR},
            </if>
            <if test="innerBatchNum != null">
                inner_batch_num = #{innerBatchNum,jdbcType=VARCHAR},
            </if>
            <if test="sendCode != null">
                send_code = #{sendCode,jdbcType=VARCHAR},
            </if>
            <if test="sendMsg != null">
                send_msg = #{sendMsg,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=SMALLINT},
            </if>
            <if test="queryStatus != null">
                query_status = #{queryStatus,jdbcType=SMALLINT},
            </if>
            <if test="detailTableNo != null">
                detail_table_no = #{detailTableNo,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="groupName != null">
                group_name = #{groupName,jdbcType=VARCHAR},
            </if>
            <if test="strategyGroupId != null">
                strategy_group_id = #{strategyGroupId,jdbcType=BIGINT},
            </if>
            <if test="strategyGroupName != null">
                strategy_group_name = #{strategyGroupName,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

</mapper>
