package com.xftech.cdp.domain.strategy.service.dispatch.offline.impl;

import com.xftech.cdp.domain.dispatch.BatchDispatchService;
import com.xftech.cdp.domain.strategy.model.dispatch.StrategyContext;
import com.xftech.cdp.domain.strategy.service.dispatch.offline.StrategyDispatchForTeleService;
import com.xftech.cdp.domain.strategy.service.dispatch.offline.StrategyDispatchService;
import com.xftech.cdp.infra.config.StrategyConfig;
import com.xftech.cdp.infra.constant.StrategyDispatchConstants;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPushBatchDo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @date 2023/3/24 16:37
 */
@Slf4j
@Service(StrategyDispatchConstants.NEW_TELE_SERVICE)
public class StrategyDispatchForNewTeleServiceImpl extends AbstractStrategyDispatchService implements StrategyDispatchService, StrategyDispatchForTeleService {

    @Autowired
    private StrategyConfig strategyConfig;
    @Autowired
    private BatchDispatchService batchDispatchService;

    /**
     * 设置批次大小
     *
     * @return 批次大小
     */
    @Override
    protected Integer setBatchSize() {
        return strategyConfig.getTeleBatchSize();
    }

    /**
     * 下发
     *
     * @param strategyContext 策略执行初始化内容
     * @param app             app
     * @param innerApp        innerApp
     * @param batch           按分组配置过滤出来的用户集合
     * @return ImmutablePair Left-下发成功数 Right-下发批次记录
     */
    @Override
    protected <T> ImmutablePair<Integer, CrowdPushBatchDo> dispatchHandler(StrategyContext strategyContext, String app, String innerApp, List<CrowdDetailDo> batch, List<T> params) {
        return sendTele(strategyContext, app, innerApp, batch);
    }


    /**
     * 重试下发
     *
     * @param context     策略执行初始化内容
     * @param tableNameNo 下发明细表序号
     * @param app         app
     * @param innerApp    innerApp
     * @param detailList  下发明细
     * @return ImmutablePair Left-下发成功数 Right-下发批次记录
     */
    @Override
    protected <T> ImmutablePair<Integer, CrowdPushBatchDo> retryDispatchHandler(StrategyContext context, String tableNameNo, String app, String innerApp, List<CrowdDetailDo> detailList, List<T> params) {
        return sendTele(context, app, innerApp, detailList);
    }

    /**
     * 调用新电销保存接口
     *
     * @param strategyContext 策略执行初始化内容
     * @param app             app
     * @param innerApp        innerApp
     * @param batch           需要下发的用户ID集合
     * @return ImmutablePair Left-下发成功数 Right-下发批次记录
     */
    private ImmutablePair<Integer, CrowdPushBatchDo> sendTele(StrategyContext strategyContext, String app, String innerApp, List<CrowdDetailDo> batch) {
        return batchDispatchService.sendNewTele(convertToDispatchDto(strategyContext), app, innerApp, batch);
    }
}
