package com.xftech.cdp.domain.touch.converter;

import com.xftech.cdp.domain.dispatch.dto.DispatchDto;
import com.xftech.cdp.domain.strategy.model.dispatch.StrategyContext;
import com.xftech.cdp.domain.strategy.model.enums.StrategyMarketChannelEnum;
import com.xftech.cdp.domain.touch.model.*;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventVO;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketChannelDo;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 触达请求转换器
 * 负责将现有的各种参数结构转换为统一的TouchRequest模型
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-23
 */
@Component
public class TouchRequestConverter {
    
    /**
     * 转换T0普通触达参数（execSend方法）
     */
    public TouchRequest convertFromExecSend(DispatchDto dispatchDto, 
                                          CrowdDetailDo crowdDetail, 
                                          StrategyMarketChannelEnum channelEnum, 
                                          StrategyMarketChannelDo channelDo, 
                                          BizEventVO bizEvent) {
        TouchRequest request = new TouchRequest();
        
        // 基础信息
        request.setRequestId(UUID.randomUUID().toString());
        request.setTouchType(TouchType.T0_NORMAL);
        request.setTouchMode(TouchMode.SINGLE);
        request.setTimestamp(System.currentTimeMillis());
        request.setTraceId(bizEvent.getTraceId());
        
        // 策略信息
        setStrategyInfo(request, dispatchDto, channelEnum);
        
        // 用户信息
        request.setUserInfo(convertToTouchUserInfo(crowdDetail));
        
        // 业务信息
        request.setBizEventType(bizEvent.getBizEventType());
        request.setBizEventData(convertBizEventToMap(bizEvent));
        request.setTemplateParams(extractTemplateParams(bizEvent));
        
        // 触达配置
        request.setTouchConfig(convertTouchConfig(dispatchDto, channelDo));
        
        // 流控配置
        request.setFlowControlConfig(FlowControlConfig.createT0Config());
        
        return request;
    }
    
    /**
     * 转换T0引擎触达参数（marketingSend方法 - T0场景）
     */
    public TouchRequest convertFromT0MarketingSend(DispatchDto dispatchDto,
                                                 CrowdDetailDo crowdDetailDo,
                                                 StrategyMarketChannelEnum channelEnum,
                                                 String groupName,
                                                 Map<String, Object> detailInfo,
                                                 BizEventVO bizEventVO) {
        TouchRequest request = new TouchRequest();
        
        // 基础信息
        request.setRequestId(UUID.randomUUID().toString());
        request.setTouchType(TouchType.T0_ENGINE);
        request.setTouchMode(TouchMode.SINGLE);
        request.setTimestamp(System.currentTimeMillis());
        if (bizEventVO != null) {
            request.setTraceId(bizEventVO.getTraceId());
        }
        
        // 策略信息
        setStrategyInfo(request, dispatchDto, channelEnum);
        
        // 用户信息
        request.setUserInfo(convertToTouchUserInfo(crowdDetailDo));
        
        // 引擎信息
        request.setEngineGroupName(groupName);
        request.setEngineDetail(detailInfo);
        if (bizEventVO != null) {
            request.setEngineCode(bizEventVO.getEngineCode());
            request.setBizEventType(bizEventVO.getBizEventType());
            request.setBizEventData(convertBizEventToMap(bizEventVO));
        }
        
        // 触达配置
        request.setTouchConfig(convertTouchConfig(dispatchDto, null));
        
        // 流控配置
        request.setFlowControlConfig(FlowControlConfig.createT0EngineConfig());
        
        return request;
    }
    
    /**
     * 转换离线引擎触达参数（marketingSend方法 - 离线场景）
     */
    public TouchRequest convertFromOfflineMarketingSend(DispatchDto dispatchDto,
                                                      CrowdDetailDo crowdDetailDo,
                                                      StrategyMarketChannelEnum channelEnum,
                                                      String groupName,
                                                      Map<String, Object> detailInfo) {
        TouchRequest request = new TouchRequest();
        
        // 基础信息
        request.setRequestId(UUID.randomUUID().toString());
        request.setTouchType(TouchType.OFFLINE_ENGINE);
        request.setTouchMode(TouchMode.SINGLE);
        request.setTimestamp(System.currentTimeMillis());
        
        // 策略信息
        setStrategyInfo(request, dispatchDto, channelEnum);
        
        // 用户信息
        request.setUserInfo(convertToTouchUserInfo(crowdDetailDo));
        
        // 引擎信息
        request.setEngineGroupName(groupName);
        request.setEngineDetail(detailInfo);
        
        // 触达配置
        request.setTouchConfig(convertTouchConfig(dispatchDto, null));
        
        // 流控配置
        request.setFlowControlConfig(FlowControlConfig.createOfflineEngineConfig());
        
        return request;
    }
    
    /**
     * 转换离线普通触达参数（dispatchHandler方法）
     */
    public TouchRequest convertFromDispatchHandler(StrategyContext context,
                                                 String app,
                                                 List<CrowdDetailDo> userList,
                                                 List<Object> templateParams) {
        TouchRequest request = new TouchRequest();
        
        // 基础信息
        request.setRequestId(UUID.randomUUID().toString());
        request.setTouchType(TouchType.OFFLINE_NORMAL);
        request.setTouchMode(TouchMode.BATCH);
        request.setTimestamp(System.currentTimeMillis());
        
        // 策略信息
        setStrategyInfoFromContext(request, context);
        
        // 用户信息列表
        List<TouchUserInfo> touchUserList = userList.stream()
                .map(this::convertToTouchUserInfo)
                .collect(Collectors.toList());
        request.setUserList(touchUserList);
        
        // 批量处理信息
        String batchNo = generateBatchNo();
        BatchInfo batchInfo = BatchInfo.create(batchNo, userList.size(), userList.size());
        batchInfo.setDetailTableNo(context.getDetailTableNo());
        request.setBatchInfo(batchInfo);
        
        // 模板参数
        request.setTemplateParams(convertTemplateParams(templateParams));
        
        // 触达配置
        request.setTouchConfig(convertTouchConfigFromContext(context));
        
        // 流控配置
        request.setFlowControlConfig(convertFlowControlConfig(context));
        
        return request;
    }
    
    /**
     * 设置策略信息
     */
    private void setStrategyInfo(TouchRequest request, DispatchDto dispatchDto, StrategyMarketChannelEnum channelEnum) {
        request.setStrategyId(dispatchDto.getStrategyId());
        request.setStrategyExecId(dispatchDto.getStrategyExecId());
        request.setStrategyGroupId(dispatchDto.getStrategyGroupId());
        request.setStrategyGroupName(dispatchDto.getStrategyGroupName());
        request.setStrategyChannelId(dispatchDto.getStrategyChannelId());
        request.setChannel(TouchChannel.fromStrategyMarketChannel(channelEnum));
        request.setTemplateId(dispatchDto.getStrategyMarketChannelTemplateId());
    }
    
    /**
     * 从StrategyContext设置策略信息
     */
    private void setStrategyInfoFromContext(TouchRequest request, StrategyContext context) {
        request.setStrategyId(context.getStrategyDo().getId());
        request.setStrategyGroupId(context.getStrategyGroupDo().getId());
        request.setStrategyGroupName(context.getStrategyGroupDo().getName());
        request.setStrategyChannelId(context.getStrategyMarketChannelDo().getId());
        
        StrategyMarketChannelEnum channelEnum = StrategyMarketChannelEnum.getInstance(
                context.getStrategyMarketChannelDo().getMarketChannel());
        request.setChannel(TouchChannel.fromStrategyMarketChannel(channelEnum));
    }
    
    /**
     * 转换用户信息
     */
    private TouchUserInfo convertToTouchUserInfo(CrowdDetailDo crowdDetail) {
        TouchUserInfo userInfo = new TouchUserInfo();
        userInfo.setUserId(crowdDetail.getUserId());
        userInfo.setMobile(crowdDetail.getMobile());
        userInfo.setApp(crowdDetail.getApp());
        userInfo.setInnerApp(crowdDetail.getInnerApp());
        userInfo.setDeviceId(crowdDetail.getDeviceId());
        userInfo.setAbNum(crowdDetail.getAbNum());
        userInfo.setAppUserIdLast2(crowdDetail.getAppUserIdLast2());
        userInfo.setCrowdId(crowdDetail.getCrowdId());
        return userInfo;
    }
    
    /**
     * 转换业务事件为Map
     */
    private Map<String, Object> convertBizEventToMap(BizEventVO bizEvent) {
        Map<String, Object> eventData = new HashMap<>();
        eventData.put("messageId", bizEvent.getMessageId());
        eventData.put("appUserId", bizEvent.getAppUserId());
        eventData.put("mobile", bizEvent.getMobile());
        eventData.put("app", bizEvent.getApp());
        eventData.put("innerApp", bizEvent.getInnerApp());
        eventData.put("strategyId", bizEvent.getStrategyId());
        eventData.put("marketChannel", bizEvent.getMarketChannel());
        eventData.put("triggerDatetime", bizEvent.getTriggerDatetime());
        
        // 添加扩展数据
        if (bizEvent.getExt() != null) {
            eventData.putAll(bizEvent.getExt());
        }
        
        return eventData;
    }
    
    /**
     * 提取模板参数
     */
    private Map<String, Object> extractTemplateParams(BizEventVO bizEvent) {
        Map<String, Object> templateParams = new HashMap<>();
        // 这里可以根据业务需要提取特定的模板参数
        // 例如：金额、时间等业务字段
        if (bizEvent.getAmount() != null) {
            templateParams.put("amount", bizEvent.getAmount());
        }
        if (bizEvent.getAdjustAmount() != null) {
            templateParams.put("adjustAmount", bizEvent.getAdjustAmount());
        }
        return templateParams;
    }
    
    /**
     * 转换触达配置
     */
    private TouchConfig convertTouchConfig(DispatchDto dispatchDto, StrategyMarketChannelDo channelDo) {
        TouchConfig config = TouchConfig.createDefault();
        
        if (dispatchDto.getDispatchType() != null) {
            config.setDispatchType(dispatchDto.getDispatchType());
        }
        
        if (channelDo != null && channelDo.getExtInfo() != null) {
            config.setChannelConfig(Map.of("extInfo", channelDo.getExtInfo()));
        }
        
        return config;
    }
    
    /**
     * 从StrategyContext转换触达配置
     */
    private TouchConfig convertTouchConfigFromContext(StrategyContext context) {
        TouchConfig config = TouchConfig.createDefault();
        // 可以根据context中的信息设置特定配置
        return config;
    }
    
    /**
     * 转换流控配置
     */
    private FlowControlConfig convertFlowControlConfig(StrategyContext context) {
        FlowControlConfig config = FlowControlConfig.createOfflineBatchConfig();
        
        // 转换流控规则
        if (context.getFlowCtrlList() != null && !context.getFlowCtrlList().isEmpty()) {
            List<FlowControlRule> rules = context.getFlowCtrlList().stream()
                    .map(this::convertFlowCtrlDoToRule)
                    .collect(Collectors.toList());
            config.setRules(rules);
        }
        
        return config;
    }
    
    /**
     * 转换FlowCtrlDo为FlowControlRule
     */
    private FlowControlRule convertFlowCtrlDoToRule(com.xftech.cdp.infra.repository.cdp.flowctrl.po.FlowCtrlDo flowCtrlDo) {
        FlowControlRule rule = new FlowControlRule();
        rule.setRuleId(flowCtrlDo.getId());
        rule.setRuleName(flowCtrlDo.getName());
        // 这里需要根据实际的FlowCtrlDo字段进行映射
        // rule.setType(...);
        // rule.setScope(...);
        // rule.setLimitTimes(...);
        // rule.setLimitSeconds(...);
        return rule;
    }
    
    /**
     * 转换模板参数
     */
    private Map<String, Object> convertTemplateParams(List<Object> templateParams) {
        Map<String, Object> params = new HashMap<>();
        if (templateParams != null) {
            for (int i = 0; i < templateParams.size(); i++) {
                params.put("param" + i, templateParams.get(i));
            }
        }
        return params;
    }
    
    /**
     * 生成批次号
     */
    private String generateBatchNo() {
        return "BATCH_" + System.currentTimeMillis() + "_" + 
               UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }
}
