package com.xftech.cdp.infra.client.sms.model.resp;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2023-02-28
 */
@Setter
@Getter
public class SmsBatchReportItem {
    @JsonProperty("ua")
    @JSONField(name = "ua")
    private String ua;

    // 入参的批次号
    @JsonProperty("batch_num")
    @JSONField(name = "batch_num")
    private String batchNum;

    // 具体报告
    @JsonProperty("reports")
    @JSONField(name = "reports")
    private Reports reports;

    @Override
    public String toString() {
        return "SmsBatchReportItem{" +
                "ua='" + ua + '\'' +
                ", batchNum='" + batchNum + '\'' +
                ", reports=" + reports +
                '}';
    }

    @Data
    public static class Reports {
        // 发送中数量
        @JsonProperty("wait_delivery")
        @JSONField(name = "wait_delivery")
        private Integer waitDelivery;

        // 发送成功数量
        @JsonProperty("delivered")
        @JSONField(name = "delivered")
        private Integer delivered;

        // 发送失败数量
        @JsonProperty("failed")
        @JSONField(name = "failed")
        private Integer failed;

        @Override
        public String toString() {
            return "Reports{" +
                    "waitDelivery=" + waitDelivery +
                    ", delivered=" + delivered +
                    ", failed=" + failed +
                    '}';
        }
    }
}
