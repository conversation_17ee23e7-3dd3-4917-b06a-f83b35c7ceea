package com.xftech.cdp.infra.client.coupon.model.req;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @date 2023/4/14 18:18
 */
@Data
public class CouponSendBatchReq {

    @JsonProperty("activity_id")
    @JSONField(name = "activity_id")
    private Long activityId;

    @JsonProperty("batch_num")
    @JSONField(name = "batch_num")
    private String batchNum;

    @JsonProperty("app")
    @JSONField(name = "app")
    private String app;

    @JsonProperty("inner_app")
    @JSONField(name = "inner_app")
    private String innerApp;

    @JsonProperty("all_batch_count")
    @JSONField(name = "all_batch_count")
    private Integer allBatchCount;

    @JsonProperty("user_list")
    @JSONField(name = "user_list")
    private List<User> userList;

    /**
     * 1:金融券，2:消费券（新增）
     */
    @JsonProperty("type")
    @JSONField(name = "type")
    private Integer type;


    @JsonProperty("goods_id")
    @JSONField(name = "goods_id")
    private String goodsId;

    /**
     * 0:无 1:url 2:app
     */
    @JsonProperty("jump_type")
    @JSONField(name = "jump_type")
    private Integer jumpType;

    @JsonProperty("jump_url")
    @JSONField(name = "jump_url")
    private String jumpUrl;

    @JsonProperty("goods_name")
    @JSONField(name = "goods_name")
    private String goodsName;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class User {
        @JsonProperty("user_id")
        @JSONField(name = "user_id")
        private Long userId;

        @JsonProperty("mobile")
        @JSONField(name = "mobile")
        private String mobile;

        @JsonProperty("name")
        @JSONField(name = "name")
        private String name;
    }

}
