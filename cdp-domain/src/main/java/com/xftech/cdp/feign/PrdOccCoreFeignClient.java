package com.xftech.cdp.feign;

import com.xftech.cdp.feign.model.prdocc.PopupCheckRequest;
import com.xftech.cdp.feign.model.prdocc.PopupCheckResponse;
import com.xftech.cdp.feign.model.prdocc.PrdOccResponse;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "prdocccore-service", contextId = "prdocccore-service" + ".FeignClient")
public interface PrdOccCoreFeignClient {

    /**
     * 检查弹窗合法性
     */
    @PostMapping("/popup/check")
    PrdOccResponse<PopupCheckResponse> popupCheck(@RequestBody PopupCheckRequest request);

}
