/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.req.external;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ ExistDispatchRecordReq, v 0.1 2024/4/1 17:19 lingang.han Exp $
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExistDispatchRecordReq implements Serializable {
    private static final long serialVersionUID = -5917028559080881466L;

    /**
     * 用户ID
     */
    private Long appUserId;

    /**
     * 策略ID
     */
    private List<Long> strategyIdList;
}