/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.infra.client.telemarketing.model;
import brave.Tracing;
import com.xftech.cdp.api.dto.req.NameTypeReq;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 *
 * <AUTHOR>
 * @version $ TeleNameTypeArgs, v 0.1 2023/10/13 14:14 wancheng.qu Exp $
 */
@Data
public class TeleNameTypeArgs {

    private List<String> businessType;
    private Integer currentPage;
    private String isAsc;
    private Integer nameTypeId;
    private String orderByColumn;
    private Integer pageSize;
    private Integer status=1;
    private String traceId;
    private Integer ts;
    private String typeName;
    private String ua="xyf-cdp";
    private String updateBy;
    private String updateTimeEnd;
    private String updateTimeStart;
    @ApiModelProperty(value = "场景筛选 AI=ai语音;AT=人工")
    private List<String> scenes;

    public TeleNameTypeArgs(){}

    public TeleNameTypeArgs(NameTypeReq nameTypeReq) {
        if(CollectionUtils.isNotEmpty(nameTypeReq.getBusinessType()))this.businessType = nameTypeReq.getBusinessType();
        if(Objects.nonNull(nameTypeReq.getNameTypeId()))this.nameTypeId = nameTypeReq.getNameTypeId();
        if(StringUtils.isNoneBlank(nameTypeReq.getTypeName()))this.typeName = nameTypeReq.getTypeName();
        if (CollectionUtils.isNotEmpty(nameTypeReq.getScenes())) {
            this.scenes = nameTypeReq.getScenes();
        } else {
            this.scenes = Lists.newArrayList("AT"); // 默认只查人工
        }
        this.currentPage = nameTypeReq.getCurrent();
        this.pageSize = nameTypeReq.getSize();
        this.traceId = UUID.randomUUID().toString();
    }
}
