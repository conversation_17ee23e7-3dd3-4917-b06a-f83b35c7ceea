package com.xftech.cdp.domain.crowd.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdWereHouseSnapshotDo;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 人群包数仓调度记录表操作
 *
 * <AUTHOR>
 * @since 2023/2/25
 */

@Component
public class CrowdWereHouseSnapshotRepository {

    /**
     * 查询当天 数仓最大app_user_id
     *
     * @return 当天数仓最大app_user_id
     */
    @Deprecated
    public CrowdWereHouseSnapshotDo getTodayCrowd() {
        return DBUtil.selectOne("crowdWereHouseSnapshot.getTodayCrowd", LocalDateTime.now().with(LocalTime.MIN));
    }

    /**
     * 插入一条人群包数仓调度记录
     *
     * @param crowdWereHouseSnapshotDo 人群包数仓调度对象
     */
    @Deprecated
    public void insert(CrowdWereHouseSnapshotDo crowdWereHouseSnapshotDo) {
        DBUtil.insert("crowdWereHouseSnapshot.insertSelective", crowdWereHouseSnapshotDo);
    }
}
