package com.xftech.cdp.domain.strategy.service;

import com.xftech.cdp.domain.strategy.model.enums.StrategyInstantLabelTypeEnum;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyInstantLabelDo;

/**
 * @<NAME_EMAIL>
 */
public interface StrategyInstantLabelService {

    StrategyInstantLabelDo getByLabelNameAndLabelType(String labelName, StrategyInstantLabelTypeEnum queryType,Integer strategyType, Integer optional);

}
