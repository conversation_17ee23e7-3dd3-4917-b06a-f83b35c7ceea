package com.xftech.cdp.domain.strategy.model.enums;

import com.xftech.cdp.domain.strategy.exception.StrategyException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/2/21
 */
@AllArgsConstructor
@Getter
public enum StrategyTimeTypeEnum {
    REALTIME(1, "立即"),


    DELAY(2, "延迟");


    private final int code;

    private final String description;

    public static StrategyTimeTypeEnum getInstance(Integer code) {
        for (StrategyTimeTypeEnum rulerEnum : StrategyTimeTypeEnum.values()) {
            if (Objects.equals(rulerEnum.getCode(), code)) {
                return rulerEnum;
            }
        }
        throw new StrategyException(String.format("不存在该策略执行类型：%s", code));
    }
}
