package com.xftech.cdp.domain.stat.entity;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 实时策略分组数据统计
 */
@Data
public class StatRealtimeStrategyGroupDataEntity {

    /**
     * 自增id
     */
    private Long id;
    /**
     * 统计日期
     */
    private LocalDate bizDate;
    /**
     * 策略ID
     */
    private Long strategyId;
    /**
     * 策略分组ID
     */
    private Long strategyGroupId;
    /**
     * 分组名称
     */
    private String groupName;

    /**
     * 流控用户数
     */
    private Integer flowControlNum;
    /**
     * 应发用户数
     */
    private Integer dispatchNum;
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

}
