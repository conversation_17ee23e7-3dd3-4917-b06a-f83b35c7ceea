/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version $ FlowReportVIewVo, v 0.1 2024/3/28 15:08 lingang.han Exp $
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class FlowReportViewVo implements Serializable {
    private static final long serialVersionUID = 2810422512997299289L;

    /**
     *
     * 批次日期
     */
    private Integer dateValue;

    /**
     *
     * 策略id
     */
    private Long strategyId;

    /**
     *
     * 策略名称
     */
    private String strategyName;

    /**
     *
     * 层级
     */
    private Integer levelNum;

    /**
     *
     * 画布no
     */
    private String flowNo;

    /**
     *
     * 画布批次no
     */
    private String batchNo;
}