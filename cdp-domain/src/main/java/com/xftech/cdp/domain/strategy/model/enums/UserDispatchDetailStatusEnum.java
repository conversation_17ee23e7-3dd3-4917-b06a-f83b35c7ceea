package com.xftech.cdp.domain.strategy.model.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 用户下发明细状态枚举
 *
 * @<NAME_EMAIL>
 * @date 2023/3/28 9:46
 */
public enum UserDispatchDetailStatusEnum {
    /**
     * 初始化 -1
     */
    INIT(-1),
    /**
     * 失败 0
     */
    FAIL(0),
    /**
     * 成功 1
     */
    SUCCESS(1);

    @Getter
    private final Integer status;

    UserDispatchDetailStatusEnum(Integer status) {
        this.status = status;
    }

    public static UserDispatchDetailStatusEnum getInstance(Integer status) {
        for (UserDispatchDetailStatusEnum detailStatusEnum : UserDispatchDetailStatusEnum.values()) {
            if (Objects.equals(detailStatusEnum.getStatus(), status)) {
                return detailStatusEnum;
            }
        }
        return INIT;
    }
}
