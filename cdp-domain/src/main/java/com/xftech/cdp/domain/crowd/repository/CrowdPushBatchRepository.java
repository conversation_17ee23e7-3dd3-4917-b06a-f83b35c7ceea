package com.xftech.cdp.domain.crowd.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.domain.crowd.model.enums.CrowdPushBatchStatusEnum;
import com.xftech.cdp.domain.crowd.model.enums.CrowdPushQueryStatusEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyMarketChannelEnum;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPushBatchDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyExecLogDo;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 人群推送批次表操作
 *
 * <AUTHOR>
 * @since 2023-02-28
 */

@Component
public class CrowdPushBatchRepository {

    /**
     * 分页查询未完成营销渠道为短信的（结果查询状态0:未查询 1:轮询中）的推送批次
     *
     * @param id       主键id
     * @param pageSize 分页大小
     * @return 未完成的人群推送批次列表
     */
    public List<CrowdPushBatchDo> selectUnFinishQuery(Long id, Long pageSize) {
        Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        param.put("pageSize", pageSize);
        return DBUtil.selectList("crowdPushBatch.selectUnFinishQuery", param);
    }

    /**
     * 根据策略渠道id查询推送失败的批次
     *
     * @param strategyExecLogId 策略执行日志id
     * @return 返回该策略渠道id下的人群推送失败批次列表
     */
    public List<CrowdPushBatchDo> selectFailBatchByStrategyExecLogId(Long strategyExecLogId) {
        Map<String, Object> param = new HashMap<>();
        param.put("strategyExecLogId", strategyExecLogId);
        param.put("queryStatus", CrowdPushQueryStatusEnum.DIS_QUERY.getCode());
        param.put("batchStatus", CrowdPushBatchStatusEnum.CAN_RETRY.getCode());
        return DBUtil.selectList("crowdPushBatch.selectFailBatchByStrategyExecLogId", param);
    }

    /**
     * 根据策略渠道id查询是否存在未完成的批次
     *
     * @param strategyExecLogId 策略执行日志id
     * @return 否存在未完成的批次标识
     */
    public boolean selectExistUnFinishBatch(Long strategyExecLogId) {
        Map<String, Object> param = new HashMap<>();
        param.put("strategyExecLogId", strategyExecLogId);
        param.put("queryStatus", Stream.of(CrowdPushQueryStatusEnum.WAIT_QUERIED.getCode(), CrowdPushQueryStatusEnum.EXECUTING.getCode()).collect(Collectors.toList()));
        return DBUtil.selectOne("crowdPushBatch.selectExistUnFinishBatch", param) != null;
    }

    /**
     * 根据策略执行日志id统计推送成功数量
     *
     * @param strategyExecLogId 策略执行日志id
     * @return 该策略执行日志id下推送成功数量
     */
    public Integer sumBatchSuccCount(Long strategyExecLogId) {
        return DBUtil.selectOne("crowdPushBatch.sumBatchSuccCount", strategyExecLogId);
    }

    /**
     * 根据策略执行日志id统计推送成功数量
     *
     * @param strategyExecLogId 策略执行日志id
     * @return 该策略执行日志id下推送成功数量
     */
    public StrategyExecLogDo countCrowdPushCount(Long strategyExecLogId) {
        return DBUtil.selectOne("crowdPushBatch.countCrowdPushCount", strategyExecLogId);
    }

    /**
     * 根据策略执行日志id统计推送总数量
     *
     * @param strategyExecLogId 策略执行日志id
     * @return 该策略执行日志id下推送总数量
     */
    public Integer selectTotalByExecLogId(Long strategyExecLogId) {
        return DBUtil.selectOne("crowdPushBatch.selectTotalByExecLogId", strategyExecLogId);

    }

    /**
     * 根据主键id更新记录
     *
     * @param crowdPushBatchDo 人群推送批次对象
     */
    public void updateById(CrowdPushBatchDo crowdPushBatchDo) {
        DBUtil.update("crowdPushBatch.updateByPrimaryKeySelective", crowdPushBatchDo);
    }

    /**
     * 插入一条人群推送批次记录
     *
     * @param crowdPushBatchDo 人群推送批次对象
     */
    public void insert(CrowdPushBatchDo crowdPushBatchDo) {
        DBUtil.insert("crowdPushBatch.insertSelective", crowdPushBatchDo);
    }

    /**
     * 批量插入人群推送批次记录
     *
     * @param crowdPushBatchDoList 人群推送批次对象列表
     */
    public void saveBatch(List<CrowdPushBatchDo> crowdPushBatchDoList) {
        DBUtil.insertBatch("crowdPushBatch.insertSelective", crowdPushBatchDoList);
    }

    /**
     * 根据渠道和批次号查询下发记录
     *
     * @param channelEnum 渠道
     * @param flowNo      批次号
     * @return 下发记录
     */
    public CrowdPushBatchDo selectByChannelAndBatchNum(StrategyMarketChannelEnum channelEnum, String flowNo) {
        Map<String, Object> param = new HashMap<>();
        param.put("channel", channelEnum.getCode());
        param.put("batchNum", flowNo);
        return DBUtil.selectOne("crowdPushBatch.selectByChannelAndBatchNum", param);
    }

    /**
     * 根据批次号查询明细表序号
     *
     * @param batchNum
     * @return 明细表序号
     */
    public String selectTableNoByBatchNum(String batchNum) {
        return DBUtil.selectOne("crowdPushBatch.selectTableNoByBatchNum", batchNum);
    }

    public CrowdPushBatchDo selectByBatchNum(String batchNum) {
        return DBUtil.selectOne("crowdPushBatch.selectByBatchNum", batchNum);
    }

    public List<CrowdPushBatchDo> selectByStrategyExecLogId(Long execLogId) {
        return DBUtil.selectList("crowdPushBatch.selectByStrategyExecLogId", execLogId);
    }
}
