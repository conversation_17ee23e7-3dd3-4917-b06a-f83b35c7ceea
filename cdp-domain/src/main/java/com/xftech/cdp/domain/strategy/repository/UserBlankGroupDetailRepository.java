package com.xftech.cdp.domain.strategy.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.domain.strategy.model.enums.UserDispatchDetailStatusEnum;
import com.xftech.cdp.infra.repository.cdp.strategy.po.UserBlankGroupDetailDo;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @<NAME_EMAIL>
 * @date 2023/4/19 15:15
 */
@Component
public class UserBlankGroupDetailRepository {

    /**
     * 当天是否执行过
     *
     * @param tableNameNo 明细表序号
     * @param strategyId  策略ID
     * @return 是否执行过
     */
    public boolean todayExecuted(String tableNameNo, Long strategyId) {
        Map<String, Object> params = new HashMap<>();
        params.put("tableName", getTableName(tableNameNo));
        params.put("strategyId", strategyId);
        return Optional.ofNullable(DBUtil.selectOne("userBlankGroupDetailMapper.todayExecuted", params)).isPresent();
    }

    /**
     * 批量保存用户下发明细
     *
     * @param tableNameNo 明细表序号
     * @param list        用户下发明细
     */
    public void saveBatch(String tableNameNo, List<UserBlankGroupDetailDo> list) {
        String tableName = getTableName(tableNameNo);
        list.forEach(userDispatchDetailDo -> userDispatchDetailDo.setTableName(tableName));
        DBUtil.insertBatch("userBlankGroupDetailMapper.insertSelective", list);
    }

    public Integer setStatusByStrategyId(String tableNameNo, Long strategyId, UserDispatchDetailStatusEnum statusEnum) {
        Map<String, Object> params = new HashMap<>();
        params.put("tableName", getTableName(tableNameNo));
        params.put("strategyId", strategyId);
        params.put("status", statusEnum.getStatus());
        return DBUtil.update("userBlankGroupDetailMapper.setFailStatusByStrategyId", params);
    }

    public Integer countUserByChannelIdAndDispatchTime(String tableNameNo, Long channelId, LocalDate dispatchDate) {
        Map<String, Object> params = new HashMap<>();
        params.put("tableName", getTableName(tableNameNo));
        params.put("channelId", channelId);
        params.put("dispatchDate", dispatchDate);
        params.put("endDispatchDate", LocalDateTime.of(dispatchDate, LocalTime.of(23, 59, 59)));
        return DBUtil.selectOne("userBlankGroupDetailMapper.countUserByChannelIdAndDispatchTime", params);
    }

    /**
     * 根据表序号生成表名
     *
     * @param tableNameNo 表序号
     * @return 表名
     */
    private String getTableName(String tableNameNo) {
        return "user_blank_group_detail_" + tableNameNo;
    }


    public Integer countUserBlankGroupData(String tableNameNo, Long strategyId, List<Long> strategyChannelIds, String startDate, String endDate) {
        Map<String, Object> params = new HashMap<>();
        params.put("tableName", getTableName(tableNameNo));
        params.put("strategyId", strategyId);
        params.put("strategyChannelIds", strategyChannelIds);
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        return DBUtil.selectOne("userBlankGroupDetailMapper.countUserBlankGroupData", params);
    }

}
