package com.xftech.cdp.feign.model.prdocc;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0 2025/6/5
 * @description PopupCheckResponse
 */
@Data
public class PopupCheckResponse {

    @ApiModelProperty(value = "弹窗是否存在")
    private Boolean exist;

    @ApiModelProperty(value = "状态是否有效中")
    private Boolean statusEffective;

    @ApiModelProperty(value = "时间是否有效中")
    private Boolean timeEffective;

}
