package com.xftech.cdp.domain.dispatch.impl;

import brave.internal.Nullable;
import com.alibaba.fastjson.JSON;
import com.dianping.cat.proxy.Tracer;
import com.google.common.base.Stopwatch;
import com.xftech.cdp.domain.crowd.service.CrowdPushBatchService;
import com.xftech.cdp.domain.dispatch.DispatchFlcService;
import com.xftech.cdp.domain.dispatch.dto.BatchDispatchFlcResultDto;
import com.xftech.cdp.domain.dispatch.dto.DispatchDto;
import com.xftech.cdp.domain.dispatch.function.FourParamsFunction;
import com.xftech.cdp.domain.strategy.exception.StrategyException;
import com.xftech.cdp.domain.strategy.model.dto.IncreaseAmtDto;
import com.xftech.cdp.domain.strategy.model.dto.IncreaseAmtParamDto;
import com.xftech.cdp.domain.strategy.model.enums.MarketChannelTypeEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyMarketChannelEnum;
import com.xftech.cdp.domain.strategy.repository.EventPushBatchRepository;
import com.xftech.cdp.domain.strategy.service.UserDispatchDetailService;
import com.xftech.cdp.domain.strategy.service.dispatch.event.impl.AbstractStrategyEventDispatchService;
import com.xftech.cdp.feign.contants.FeignConstants;
import com.xftech.cdp.infra.client.increaseamt.IncreaseAmtClient;
import com.xftech.cdp.infra.client.increaseamt.IncreaseAmtUtils;
import com.xftech.cdp.infra.client.increaseamt.model.dto.UserInfoDto;
import com.xftech.cdp.infra.client.increaseamt.model.req.Request;
import com.xftech.cdp.infra.client.increaseamt.model.req.RequestHeader;
import com.xftech.cdp.infra.client.increaseamt.model.resp.Resp;
import com.xftech.cdp.infra.config.AppConfigService;
import com.xftech.cdp.infra.repository.Do;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPushBatchDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.EventPushBatchDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.UserDispatchDetailDo;
import com.xftech.cdp.infra.utils.JsonUtil;
import com.xftech.cdp.infra.utils.SerialNumberUtil;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.ImmutableTriple;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @<NAME_EMAIL>
 */
@Slf4j
@Component
public class AbstractDispatchService {

    @Autowired
    private SerialNumberUtil serialNumberUtil;
    @Autowired
    private CrowdPushBatchService crowdPushBatchService;
    @Autowired
    private EventPushBatchRepository eventPushBatchRepository;
    @Autowired
    private UserDispatchDetailService userDispatchDetailService;
    @Autowired
    private DispatchFlcService dispatchFlcService;
    @Autowired
    private AbstractStrategyEventDispatchService abstractStrategyEventDispatchService;
    @Autowired
    private IncreaseAmtClient increaseAmtClient;
    @Autowired
    private AppConfigService appConfigService;
    @Autowired
    private IncreaseAmtUtils increaseAmtUtils;

    /**
     * 发送请求
     *
     * @param reach           策略执行初始化参数
     * @param app             app
     * @param innerApp        innerApp
     * @param crowdDetailList 需下发的集合
     * @param convertListFun  洗发批次参数转换
     * @param request         具体请求方法
     * @return 请求结果
     */
    protected <T> ImmutablePair<Integer, CrowdPushBatchDo> request(MarketChannelTypeEnum channelTypeEnum, DispatchDto reach, String app, String innerApp, List<CrowdDetailDo> crowdDetailList,
                                                                   Function<List<CrowdDetailDo>, List<T>> convertListFun,
                                                                   FourParamsFunction<MarketChannelTypeEnum, List<CrowdDetailDo>, List<CrowdDetailDo>, List<T>, List<T>> filterListFun,
                                                                   BiFunction<String, List<T>, Pair<Boolean, Pair<String, String>>> request) {
        Boolean sendRet = false;
        long epochMilli = Instant.now().toEpochMilli();
        CrowdPushBatchDo crowdPushBatchDo = new CrowdPushBatchDo();
        BatchDispatchFlcResultDto batchDispatchFlcResultDto = null;
        String batchNum = serialNumberUtil.batchNum();
        Pair<String, String> respPair = Pair.of("-1", "failed");
        try {
            List<T> list = convertListFun.apply(crowdDetailList);
            if (CollectionUtils.isEmpty(list)) {
                return ImmutablePair.of(0, null);
            }

            batchDispatchFlcResultDto = dispatchFlcService
                    .batchDispatchFlcLockThenReturnExcludeUsers(reach, crowdPushBatchDo, batchNum, app, innerApp, list, crowdDetailList, respPair);

            List<T> params = filterListFun.call(channelTypeEnum, crowdDetailList, batchDispatchFlcResultDto.getExcludeDetailsList(), list);
            int diff = list.size() - params.size();
            log.info("批量触达流控, 策略id:{}, 渠道:{}, 排除数量:{}, 流控前信息:{}, 流控后信息:{}", reach.getStrategyId(), channelTypeEnum.getDescription(), diff,
                    JsonUtil.toJson(list), JsonUtil.toJson(params));
            if (diff > 0) {
                log.info("批量触达流控产生拦截数据, 策略id:{}, 渠道:{}, 排除数量:{}", reach.getStrategyId(), channelTypeEnum.getDescription(), diff);
                Tracer.logEvent("BatchFlcLock", String.valueOf(reach.getStrategyChannel()));
            }
            crowdPushBatchDo.setBatchTotal(params.size());
            crowdPushBatchDo.setMobileBatch(JSON.toJSONString(params));
            if (CollectionUtils.isEmpty(params)) {
                return ImmutablePair.of(0, null);
            }
            // 1.保存明细
//            userDispatchDetailService.saveDispatchDetail(reach, crowdPushBatchDo, batchNum, app, innerApp, list, crowdDetailList, respPair);
            // 2.发送请求
            Pair<Boolean, Pair<String, String>> resp = request.apply(batchNum, params);
            log.info("策略id:{}, 批次号:{}, 数量:{}, 接口耗时:{} ms, 发送响应:{}", reach.getStrategyId(), batchNum, params.size(), Instant.now().toEpochMilli() - epochMilli, JsonUtil.toJson(resp));
            sendRet = resp.getLeft();
            // 3.修改响应
            respPair = resp.getRight();
            // 4.校验结果
            this.verifyResp(StrategyMarketChannelEnum.getInstance(reach.getStrategyChannel()), resp);
            // 5.返回数据
            return new ImmutablePair<>(Boolean.TRUE.equals(resp.getLeft()) ? params.size() : 0, crowdPushBatchDo);
        } catch (StrategyException e) {
            throw new StrategyException(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.warn("批量下发异常，批次号：{}，数量：{}", batchNum, crowdPushBatchDo.getBatchTotal() == null ? 0 : crowdPushBatchDo.getBatchTotal(), e);
            return new ImmutablePair<>(0, crowdPushBatchDo);
        } finally {
            if (!Objects.equals(sendRet, true)) {
                if (batchDispatchFlcResultDto != null
                        && !CollectionUtils.isEmpty(batchDispatchFlcResultDto.getUserDispatchDetailList())) {
                    List<Long> userDetailIdList = batchDispatchFlcResultDto.getUserDispatchDetailList().stream().map(Do::getId).filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(userDetailIdList)) {
                        userDispatchDetailService.batchUpdateDispatchFail(reach.getDetailTableNo(), userDetailIdList);
                    }
                }
            }
            if (crowdPushBatchDo.getId() != null && crowdPushBatchDo.getId() > 0) {
                // 6.更新批次
                crowdPushBatchDo.setSendCode(respPair.getLeft());
                crowdPushBatchDo.setSendMsg(respPair.getRight());
                crowdPushBatchDo.initQueryStatus(respPair.getLeft());
                crowdPushBatchService.updateById(crowdPushBatchDo);
            }
        }
    }

    protected ImmutablePair<Integer, CrowdPushBatchDo> batchRequestIncreaseAmt(DispatchDto reach, String app, String innerApp, List<CrowdDetailDo> batch, List<UserInfoDto> userInfoDtos) {
        CrowdPushBatchDo crowdPushBatchDo = new CrowdPushBatchDo();
        String batchNum = serialNumberUtil.batchNum();
        if (CollectionUtils.isEmpty(batch)) {
            return new ImmutablePair<>(0, null);
        }
        int succSum = 0;
        List<Long> userDetailFailList = new ArrayList<>();
        List<Long> userIds = batch.stream().map(CrowdDetailDo::getUserId).collect(Collectors.toList());
        crowdPushBatchService.insert(reach, crowdPushBatchDo, app, innerApp, userIds, "1", "Success", batchNum);
        for (CrowdDetailDo crowdDetailDo : batch) {
            try {
                UserInfoDto userInfoDto = userInfoDtos.stream()
                        .filter(x -> x.getUserId().equals(crowdDetailDo.getUserId()))
                        .findFirst().orElse(null);
                if (userInfoDto == null) {
                    continue;
                }
                BatchDispatchFlcResultDto dispatchFlcResultDto = dispatchFlcService.singleDispatchFlcLockThenReturnExcludeUsers(reach, crowdPushBatchDo, crowdDetailDo);
                if (!CollectionUtils.isEmpty(dispatchFlcResultDto.getUserDispatchDetailList())) {
                    UserDispatchDetailDo userDispatchDetailDo = dispatchFlcResultDto.getUserDispatchDetailList().get(0);
                    Long detailId = userDispatchDetailDo.getId();

                    // 当日已调用提额接口，不在发起提额请求
                    String increaseType = increaseAmtUtils.getIncreaseType(reach.getIncreaseAmtParamDto());
                    boolean hasRecordToday = increaseAmtUtils.hasRecordToday(crowdDetailDo.getUserId(), increaseType);
                    if (hasRecordToday) {
                        userDetailFailList.add(detailId);
                        log.info("当日该用户已调用过风险提额接口,不再发起提额请求. 提额类型={}, 策略id={}, 用户id={}", increaseType, reach.getStrategyId(), crowdDetailDo.getUserId());
                    } else {
                        String orderNo = String.format("%s_%s", detailId, batchNum);
                        RequestHeader header = buildIncreaseAmtHeader(crowdDetailDo.getUserId(), orderNo, app, innerApp, userInfoDto.getOldUserNo(), null, null);
                        // 自定义业务参数
                        Request request = buildIncreaseAmtBody(reach.getIncreaseAmtParamDto(), reach.getDetailTableNo(), batchNum, detailId, reach.getStrategyId(), "offline", crowdDetailDo.getApp(),
                                crowdDetailDo.getInnerApp(), userInfoDto, orderNo);
                        Resp resp = increaseAmtClient.increaseAmt(header, request);
                        if (resp.isSucceed()) {
                            succSum++;
                        } else {
                            userDetailFailList.add(detailId);
                        }
                    }
                } else {
                    log.info("用户提额, 已被流控, 策略id:{}, 用户id:{}", reach.getStrategyId(), crowdDetailDo.getUserId());
                }
            } catch (Exception ex) {
                log.error("batchRequestIncreaseAmt 异常, 策略id:{}, crowdDetailDo:{}", reach.getStrategyId(), JsonUtil.toJson(crowdDetailDo));
            }
        }
        // 批量更新失败的数量
        if (!CollectionUtils.isEmpty(userDetailFailList)) {
            log.info("用户提额,调用接口失败, 更新失败的数量:{}, 策略id:{}, 渠道id:{}", userDetailFailList.size(), reach.getStrategyId(), reach.getStrategyChannelId());
            userDispatchDetailService.batchUpdateDispatchFail(reach.getDetailTableNo(), userDetailFailList);
        }
        log.info("提额本批次调用, 成功数量:{}, 失败数量:{}, 策略id:{}, 渠道id:{}", succSum, userDetailFailList.size(), reach.getStrategyId(), reach.getStrategyChannelId());
        return new ImmutablePair<>(succSum, crowdPushBatchDo);
    }

    public RequestHeader buildIncreaseAmtHeader(Long userId, String orderNo, String app, String innerApp,
                                                @Nullable Long creditUserId, @Nullable String deviceId, @Nullable String ip) {
        RequestHeader header = new RequestHeader();
        header.setRequester("xyf-cdp");
        header.setBiz_type("xyf-cdp");
        header.setBiz_flow_number(orderNo);
        header.setSeq_id(orderNo);
        header.setSource_type("wap");
        header.setProduct_name("xinyongfei");
        header.setApp(app);
        header.setInner_app(innerApp);
        header.setUser_id(Objects.nonNull(creditUserId) ? creditUserId.toString() : userId.toString());
        header.setDevice_id(deviceId);
        header.setIp(ip);
        return header;
    }

    public Request buildIncreaseAmtBody(IncreaseAmtParamDto increaseAmtParamDto, String  tableNo, String batchNum, Long detailId, Long strategyId, String strategyType,
                                           String app, String innerApp,  UserInfoDto userInfoDto, String orderNo) {
        Request request = new Request();
        String increaseAmtCallbackUrl = appConfigService.getIncreaseAmtCallbackUrl();
        String increaseType = increaseAmtParamDto.getIncreaseType();
        if (IncreaseAmtDto.IncreaseType.PERSONAL_API_FST_LOGIN_TEMP.equals(increaseType)) {
            request.setModify_stage_type("personal_api_fst_login_temp");
        } else if (IncreaseAmtDto.IncreaseType.LOAN_UPTO_FULLAMT.equals(increaseType)) {
            request.setModify_stage_type("loan_upto_fullamt");
        } else if (IncreaseAmtDto.IncreaseType.PERSONAL_MARKETING_RELOAN_TEMP.equals(increaseType)) {
            request.setModify_stage_type("personal_marketing_reloan_temp");
            request.setModify_cash_line(increaseAmtParamDto.getAmount());
        } else {
            request.setModify_stage_type("increase_tmp_amount_marketing");
            request.setModify_cash_line(increaseAmtParamDto.getAmount());
        }

        request.setId_card_number(userInfoDto.getId_card_number());
        request.setCust_no(userInfoDto.getCust_no());
        request.setUser_no(userInfoDto.getUser_no());
        request.setMobile(userInfoDto.getMobile());
        request.setName(userInfoDto.getName());
        request.setUnique_flow_number(orderNo);
        request.set_async(true);
        request.setCall_back_url(increaseAmtCallbackUrl);


        Map<String, Object> extendData = request.buildExtData(tableNo, batchNum, detailId, strategyType,
                increaseAmtParamDto.getAmount(), userInfoDto.getUserId(), app, innerApp, userInfoDto.getMobile(),
                strategyId, request.getModify_stage_type());
        extendData.put("temporary_amt_start_time_input", increaseAmtParamDto.getStartTime());
        extendData.put("temporary_amt_end_time_input", increaseAmtParamDto.getEndTime());

        request.setExtend_data(extendData);
        return request;
    }


    /**
     * 发送请求
     *
     * @param reach          策略执行初始化参数
     * @param crowdDetail    需下发的集合
     * @param convertListFun 洗发批次参数转换
     * @param request        具体请求方法
     * @return 请求结果
     */
    protected <T> ImmutableTriple<Integer, EventPushBatchDo, Boolean> requestEvent(DispatchDto reach, CrowdDetailDo crowdDetail, Function<CrowdDetailDo, List<T>> convertListFun, Function<String, Pair<Boolean, Triple<String, String, String>>> request) {
        Boolean sendRet = false;
        long epochMilli = Instant.now().toEpochMilli();
        EventPushBatchDo eventPushBatchDo = new EventPushBatchDo();
        String batchNum = serialNumberUtil.batchNum();
        Triple<String, String, String> respPair = Triple.of("-1", "failed", null);
        StrategyMarketChannelEnum marketChannelEnum = StrategyMarketChannelEnum.getInstance(reach.getStrategyChannel());
        UserDispatchDetailDo dispatchDetail = new UserDispatchDetailDo();
        try {
            List<T> list = convertListFun.apply(crowdDetail);
            if (CollectionUtils.isEmpty(list)) {
                return ImmutableTriple.of(0, null, false);
            }
            boolean flcLockRet = dispatchFlcService.dispatchFlcLock(reach, eventPushBatchDo,
                    dispatchDetail, batchNum, crowdDetail, abstractStrategyEventDispatchService);
            if (flcLockRet) {
                return ImmutableTriple.of(0, null, true);
            }
            // 1.保存明细  -- 频控后写入
            // userDispatchDetailService.saveEventDispatchDetail(reach, eventPushBatchDo, dispatchDetail, batchNum, crowdDetail, respPair);
            // 2.发送请求
            String tempBatNum = batchNum;
            if (marketChannelEnum == StrategyMarketChannelEnum.INCREASE_AMOUNT) {
                tempBatNum = String.format("%s_%s", dispatchDetail.getId(), batchNum);
            }
            Pair<Boolean, Triple<String, String, String>> resp = request.apply(tempBatNum);
            log.info("批次号:{}, 数量:{}, 接口耗时:{}ms", batchNum, list.size(), Instant.now().toEpochMilli() - epochMilli);
            sendRet = resp.getLeft();
            // 3.修改响应
            respPair = resp.getRight();
            // 4.校验结果
            Pair<Boolean, Pair<String, String>> pair = Pair.of(resp.getLeft(), Pair.of(resp.getRight().getLeft(), resp.getRight().getMiddle()));
            this.verifyResp(marketChannelEnum, pair);
            // 5.返回数据
            return new ImmutableTriple<>(Boolean.TRUE.equals(resp.getLeft()) ? list.size() : 0, eventPushBatchDo, false);
        } catch (StrategyException e) {
            throw new StrategyException(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.warn("批量下发异常，批次号：{}，数量：{}", batchNum, 1, e);
            return new ImmutableTriple<>(0, eventPushBatchDo, false);
        } finally {
            // 更新为失败状态
            if (!Objects.equals(sendRet, true)) {
                log.info("触达失败, 更新dispatchDetail表状态, userDispatchDetailId:{}, userid:{}, marketChannel:{}", dispatchDetail.getId(),
                        dispatchDetail.getUserId(), reach.getStrategyChannel());
                if (dispatchDetail.getId() != null && dispatchDetail.getId() > 0) {
                    dispatchDetail.setStatus(0);
                    userDispatchDetailService.updateById(reach.getDetailTableNo(), dispatchDetail);
                }
            }
            if (eventPushBatchDo.getId() != null && eventPushBatchDo.getId() > 0) {
                // 6.更新批次
                if (marketChannelEnum == StrategyMarketChannelEnum.SMS) {
                    eventPushBatchDo.setBatchNum(respPair.getRight());
                }
                eventPushBatchDo.setSendCode(respPair.getLeft());
                eventPushBatchDo.setSendMsg(respPair.getMiddle());
                eventPushBatchDo.setStatus(sendStatus(reach.getStrategyChannel(), respPair.getLeft()));
                eventPushBatchRepository.updateById(eventPushBatchDo.getDetailTableNo(), eventPushBatchDo);
            }
        }
    }

    /**
     * 最终请求状态
     *
     * @param channel 渠道
     * @param code    响应码
     * @return 状态 1-成功 2-失败
     */
    public Integer sendStatus(Integer channel, String code) {
        StrategyMarketChannelEnum channelEnum = StrategyMarketChannelEnum.getInstance(channel);
        if (channelEnum == StrategyMarketChannelEnum.SMS && StringUtils.equals("1", code)) {
            return 1;
        }
        if (channelEnum == StrategyMarketChannelEnum.VOICE && StringUtils.equals("000000", code)) {
            return 1;
        }
        if (channelEnum == StrategyMarketChannelEnum.SALE_TICKET && StringUtils.equals("1", code)) {
            return 1;
        }
        if (channelEnum == StrategyMarketChannelEnum.VOICE_NEW && StringUtils.equals("1", code)) {
            return 1;
        }
        if (channelEnum == StrategyMarketChannelEnum.PUSH && StringUtils.equals(FeignConstants.PUSH_SUCCESS_CODE.toString(), code)) {
            return 1;
        }
        if (channelEnum == StrategyMarketChannelEnum.AI_PRONTO && StringUtils.equals("1", code)) {
            return 1;
        }
        return 2;
    }

    /**
     * 校验接口响应
     *
     * @param channelEnum 渠道
     * @param resp        响应结果
     */
    private void verifyResp(StrategyMarketChannelEnum channelEnum, Pair<Boolean, Pair<String, String>> resp) {
        switch (channelEnum) {
            case SMS:

                break;
            case VOICE:

                break;
            case VOICE_NEW:

                break;
            case SALE_TICKET:
                if (StringUtils.equals(resp.getRight().getLeft(), "500001")) {
                    throw new StrategyException(500001, "优惠券活动失效，策略执行失败，请尽快排查并操作重试！");
                }
                if (StringUtils.equals(resp.getRight().getLeft(), "500002")) {
                    throw new StrategyException(500002, "优惠券库存不足，策略执行失败，请尽快排查并操作重试！");
                }
                break;
            default:
        }
    }
}
