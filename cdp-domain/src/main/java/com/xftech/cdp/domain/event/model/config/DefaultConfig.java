package com.xftech.cdp.domain.event.model.config;

import com.xftech.cdp.domain.event.model.annotation.ProcessConfig;
import com.xftech.cdp.domain.event.model.enums.DataProcessEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 默认值数据处理器配置信息
 * <AUTHOR>
 * @version $ DefaultConfig, v 0.1 2024/11/12 17:56 snail Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ProcessConfig(processor = DataProcessEnum.DEFAULT)
public class DefaultConfig extends FieldConfig {
    /** 数据类型 */
    private String defaultType;
    /** 默认值 */
    private String defaultValue;
}
