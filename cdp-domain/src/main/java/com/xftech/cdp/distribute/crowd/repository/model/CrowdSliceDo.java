package com.xftech.cdp.distribute.crowd.repository.model;

import java.util.Date;

import lombok.Data;

/**
 * 人群包分片记录表
 * crowd_slice
 */
@Data
public class CrowdSliceDo {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 人群包id
     */
    private Long crowdId;

    /**
     * 人群包版本
     */
    private Long crowdVersion;

    /**
     * 人群包路径:目录/文件名
     */
    private String ossUri;

    /**
     * 分片开始字节数
     */
    private Long startPos;

    /**
     * 分片字节数
     */
    private Long endPos;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新时间
     */
    private Date updatedTime;

    /**
     * 是否删除:1删除0未删除
     */
    private Integer dFlag;

    /**
     * 创建人
     */
    private String createdOp;

    /**
     * 修改人
     */
    private String updatedOp;

}