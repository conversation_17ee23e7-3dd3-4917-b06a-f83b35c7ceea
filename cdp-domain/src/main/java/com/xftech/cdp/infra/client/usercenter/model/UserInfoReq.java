package com.xftech.cdp.infra.client.usercenter.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2023-05-12
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class UserInfoReq {
    private static final long serialVersionUID = 1L;

    @JsonProperty("mobile")
    @SerializedName("mobile")
    private String mobile;

    @JsonProperty("app")
    @SerializedName("app")
    private String app;
}
