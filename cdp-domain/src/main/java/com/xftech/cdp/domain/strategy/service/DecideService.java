/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.service;

import com.xftech.cdp.api.dto.req.DecideReq;
import com.xftech.cdp.api.dto.resp.DecideResp;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ AppBannerService, v 0.1 2024/4/18 17:51 benlin.wang Exp $
 */

public interface DecideService {
    List<DecideResp> decide(DecideReq decideReq);
}