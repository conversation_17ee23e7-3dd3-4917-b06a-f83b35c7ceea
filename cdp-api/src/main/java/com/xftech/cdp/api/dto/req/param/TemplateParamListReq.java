package com.xftech.cdp.api.dto.req.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xftech.cdp.api.dto.base.PageRequestDto;
import com.xftech.cdp.api.dto.base.TimeFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Size;
import java.time.LocalDateTime;


/**
 * @<NAME_EMAIL>
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class TemplateParamListReq extends PageRequestDto {

    @ApiModelProperty(value = "参数名称", required = true)
    @Size(max = 20,message = "参数名称不能超过20个字符")
    private String name;

    @ApiModelProperty(value = "参数id")
    private String id;

    @ApiModelProperty(value = "参数备注")
    @Size(max = 100,message = "参数备注不能超过100个字符")
    private String description;

    @ApiModelProperty(value = "key值", required = true)
    @Size(max = 100,message = "key值不能超过100个字符")
    private String paramKey;

    @ApiModelProperty(value = "需求方", required = true)
    @Size(max = 20,message = "参数名称不能超过20个字符")
    private String demandSide;

    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    @ApiModelProperty(value = "上线开始时间")
    private LocalDateTime startTime;

    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    @ApiModelProperty(value = "上线结束时间")
    private LocalDateTime endTime;


}
