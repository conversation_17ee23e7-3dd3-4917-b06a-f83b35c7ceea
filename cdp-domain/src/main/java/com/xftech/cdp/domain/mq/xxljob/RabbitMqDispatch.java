package com.xftech.cdp.domain.mq.xxljob;

import com.alibaba.fastjson.JSON;
import com.xftech.cdp.infra.constant.XxlJobConstants;
import com.xftech.cdp.domain.mq.RabbitMqService;
import com.xftech.cdp.infra.rabbitmq.vo.MqSwitchXxlJobParam;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2023-04-23
 */
@Slf4j
@Component
public class RabbitMqDispatch {

    @Autowired
    private RabbitMqService rabbitMqService;

    @XxlJob(XxlJobConstants.RABBIT_MQ_DISPATCH)
    public ReturnT<String> rabbitMqDispatch(String param) {
        log.info("rabbitMqDispatch begin param:{}", param);
        XxlJobLogger.log("rabbitMqDispatch begin param:{}", param);
        if (StringUtils.isEmpty(param)) {
            log.warn("rabbitMqDispatch param error param:{}", param);
            XxlJobLogger.log("rabbitMqDispatch param error param:{}", param);
            return ReturnT.FAIL;
        }
        try {
            MqSwitchXxlJobParam mqSwitchXxlJobParam = JSON.parseObject(param, MqSwitchXxlJobParam.class);
            rabbitMqService.switchRabbitMq(mqSwitchXxlJobParam);
        } catch (Exception e) {
            log.warn("rabbitMqDispatch execute error param:{}, e: ", param, e);
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }

        return ReturnT.SUCCESS;
    }
}
