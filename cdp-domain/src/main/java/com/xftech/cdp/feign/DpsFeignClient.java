package com.xftech.cdp.feign;

import com.xftech.cdp.feign.contants.FeignConstants;
import com.xftech.cdp.feign.interceptor.DpsFeignClientInterceptor;
import com.xftech.cdp.feign.model.requset.DpsPageRequest;
import com.xftech.cdp.feign.model.response.DpsResponse;
import com.xftech.cdp.feign.model.response.MetaLabelDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 *
 * <AUTHOR>
 */
@FeignClient(name = FeignConstants.DPS_SERVICE, contextId = FeignConstants.DPS_SERVICE + ".FeignClient", path = "/", configuration = DpsFeignClientInterceptor.class)
public interface DpsFeignClient {

    /**
     * 查询标签元数据
     */
    @PostMapping("/api/crowd-label/by-page")
    DpsResponse<List<MetaLabelDto>> queryLabelList(@RequestBody DpsPageRequest request);
}
