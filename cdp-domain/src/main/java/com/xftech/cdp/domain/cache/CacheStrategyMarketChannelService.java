package com.xftech.cdp.domain.cache;

import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketChannelDo;

import java.util.List;

/**
 * 策略营销渠道关系表操作
 */
public interface CacheStrategyMarketChannelService {

    /**
     * 根据策略id删除营销渠道记录
     *
     * @param strategyId 策略id
     */
    void deleteByStrategyId(Long strategyId);

    /**
     * 根据策略id查询营销渠道记录
     *
     * @param strategyId 策略id
     * @return 该策略下的所有渠道列表
     */
    List<StrategyMarketChannelDo> selectByStrategyId(Long strategyId);

    /**
     * 根据策略分组id查询营销渠道记录
     *
     * @param strategyGroupId 策略分组id
     * @return 该策略某个分组下的所有营销渠道列表
     */
    List<StrategyMarketChannelDo> selectByStrategyGroupId(Long strategyGroupId);

    /**
     * 插入一条营销渠道记录
     *
     * @param strategyMarketChannelDo 营销渠道对象
     */
    void insert(StrategyMarketChannelDo strategyMarketChannelDo);

    /**
     * 根据渠道id(主键)更新某一条记录
     *
     * @param strategyMarketChannelDo 营销渠道对象
     */
    void updateById(StrategyMarketChannelDo strategyMarketChannelDo);

    /**
     * 根据id批量删除营销渠道记录
     *
     * @param channelIdList 渠道id列表
     */
    void deleteByIdBatch(List<Long> channelIdList);

    StrategyMarketChannelDo selectByStrategyGroupIdAndChannel(Long strategyGroupId, Integer marketChannel);

    StrategyMarketChannelDo cacheSelectById(Long channelId);
}
