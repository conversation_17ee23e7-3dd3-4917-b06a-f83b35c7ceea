/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.adapter.strategy.label;

import com.xftech.cdp.infra.client.ads.model.resp.AdsLabelResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ LabelHandlerSelector, v 0.1 2024/6/13 16:39 lingang.han Exp $
 */

@Slf4j
@Component
public class LabelHandlerSelector {

    @Autowired
    private List<LabelHandler> labelHandlerList;

    public AdsLabelResp doQueryLabel(String label, QueryLabelRequest param) {
        try {
            LabelHandler labelHandler = labelHandlerList.stream().filter(item -> item.topicTagCompare(label)).findFirst().orElse(null);
            if (labelHandler != null) {
                return labelHandler.execute(label,param);
            }
            return null;
        } catch (Exception e) {
            log.error("查询接口类实时标签执行失败", e);
            return null;
        }
    }
}