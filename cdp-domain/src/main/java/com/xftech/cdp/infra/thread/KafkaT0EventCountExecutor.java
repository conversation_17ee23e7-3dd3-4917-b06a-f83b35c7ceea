/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.infra.thread;

import java.util.Arrays;
import java.util.HashSet;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import com.xftech.cdp.infra.config.AppConfigService;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigChangeListener;
import com.ctrip.framework.apollo.ConfigService;
import com.ctrip.framework.apollo.model.ConfigChange;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Service;

/**
 * kafka-T0事件策略频次统计-线程池
 */
@Slf4j
@Service
public class KafkaT0EventCountExecutor {

    private static int poolDefaultCoreSize = 8;

    private static int poolDefaultMaxSize = 20;

    private static String poolCoreSizeKey = "KafkaT0EventCountExecutor.pool.coreSize";

    private static String poolMaxSizeKey = "KafkaT0EventCountExecutor.pool.maxSize";

    private static ExecutorService pool = new ThreadPoolExecutor(poolDefaultCoreSize, poolDefaultMaxSize,
            1L, TimeUnit.MINUTES,
            new ArrayBlockingQueue<>(200),
            new CustomizableThreadFactory("KafkaT0EventCountExecutor-"),
            new ThreadPoolExecutor.DiscardOldestPolicy());

    public static ExecutorService getPool() {
        return pool;
    }

    @Resource
    private AppConfigService appConfigService;

    @PostConstruct
    public void init() throws Exception {
        int coreSize = Integer.parseInt(appConfigService.getPropertyValue(poolCoreSizeKey, String.valueOf(poolDefaultCoreSize)));
        int maxSize = Integer.parseInt(appConfigService.getPropertyValue(poolMaxSizeKey, String.valueOf(poolDefaultMaxSize)));
        setPoolCoreSize(coreSize);
        setPoolMaxSize(maxSize);
        Config config = ConfigService.getAppConfig();
        config.addChangeListener(new ConfigChangeListener() {
            @Override
            public void onChange(ConfigChangeEvent configChangeEvent) {
                for (String key : configChangeEvent.changedKeys()) {
                    ConfigChange change = configChangeEvent.getChange(key);
                    if (change != null) {
                        if (poolCoreSizeKey.equals(key)) {
                            setPoolCoreSize(Integer.parseInt(change.getNewValue()));
                        } else if (poolMaxSizeKey.equals(key)) {
                            setPoolMaxSize(Integer.parseInt(change.getNewValue()));
                        }
                    }
                }
            }
        }, new HashSet<>(Arrays.asList(poolCoreSizeKey, poolMaxSizeKey)));
    }

    public void setPoolCoreSize(int size) {
        if (size <= 0) {
            return;
        }
        if (((ThreadPoolExecutor) pool).getCorePoolSize() != size) {
            log.info("KafkaT0EventCountExecutor, 线程池核心线程数重新设置, coreSize={}", size);
            ((ThreadPoolExecutor) pool).setCorePoolSize(size);
        }
    }

    public void setPoolMaxSize(int size) {
        if (size <= 0) {
            return;
        }
        if (((ThreadPoolExecutor) pool).getMaximumPoolSize() != size) {
            log.info("KafkaT0EventCountExecutor, 线程池最大线程数重新设置, maxSize={}", size);
            ((ThreadPoolExecutor) pool).setMaximumPoolSize(size);
        }
    }

}