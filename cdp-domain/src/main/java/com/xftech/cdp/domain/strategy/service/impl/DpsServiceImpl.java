/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.xftech.cdp.domain.strategy.exception.StrategyException;
import com.xftech.cdp.feign.DpsFeignClient;
import com.xftech.cdp.feign.GoodsFeignClient;
import com.xftech.cdp.feign.model.GoodsDetail;
import com.xftech.cdp.feign.model.GoodsList;
import com.xftech.cdp.feign.model.requset.DpsPageRequest;
import com.xftech.cdp.feign.model.requset.GoodsListRequest;
import com.xftech.cdp.feign.model.response.DpsResponse;
import com.xftech.cdp.feign.model.response.GoodsResponse;
import com.xftech.cdp.feign.model.response.MetaLabelDto;
import com.xftech.cdp.infra.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ DpsServiceImpl, v 0.1 2024/6/19 14:31 lingang.han Exp $
 */

@Service
@Slf4j
public class DpsServiceImpl {

    @Autowired
    private DpsFeignClient dpsFeignClient;

    private static final String ua = "xyf-cdp";

    public List<MetaLabelDto> getMetaLabel(DpsPageRequest request) {
        if (request == null) {
            throw new StrategyException("request do not null");
        }
        DpsResponse<List<MetaLabelDto>> labelListResponse = dpsFeignClient.queryLabelList(request);

        log.info("dps url:/api/crowd-label/by-page req:{},resp:{}", request, labelListResponse);

        if (labelListResponse == null || !labelListResponse.isSuccess()) {
            String errorMessage = labelListResponse != null ? "dps url:/api/crowd-label/by-page return error=" + labelListResponse.getMsg() : "dps url:/api/crowd-label/by-page return null";
            throw new StrategyException(errorMessage);
        }

        if (!CollectionUtils.isEmpty(labelListResponse.getData())) {
            return labelListResponse.getData();
        } else {
            return null;
        }
    }
}