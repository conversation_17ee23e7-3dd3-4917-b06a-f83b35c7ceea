package com.xftech.cdp.domain.event.model.config;

import com.xftech.cdp.domain.event.model.annotation.ProcessConfig;
import com.xftech.cdp.domain.event.model.enums.DataProcessEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据映射处理器配置信息
 * <AUTHOR>
 * @version $ MappingConfig, v 0.1 2024/11/13 14:18 snail Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ProcessConfig(processor = DataProcessEnum.MAPPING)
public class MappingConfig extends FieldConfig {
    /** 数据类型：int,float,long,short等 */
    private String dataType;
    /** 数据映射,格式如下：1_2#4_5 */
    private String mapping;
}
