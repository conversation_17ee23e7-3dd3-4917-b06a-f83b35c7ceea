/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.rocketmq.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventMessageVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version $ BizEventRocketMessageVO, v 0.1 2024/3/18 14:01 lingang.han Exp $
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RcsptRocketMessageVO {

    private String app;

    /**
     * 调额结果 S:成功,F:失败
     */
    @JsonProperty("adjust_result")
    @JSONField(name = "adjust_result")
    private String adjustResult;

    @JsonProperty("event_datetime")
    @JSONField(name = "event_datetime")
    private String eventDatetime;

    @JsonProperty("trigger_datetime")
    @JSONField(name = "trigger_datetime")
    private String triggerDatetime;

    @JsonProperty("manage_type")
    @JSONField(name = "manage_type")
    private String manageType;

    @JsonProperty("inner_app")
    @JSONField(name = "inner_app")
    private String innerApp;

    @JsonProperty("cust_no")
    @JSONField(name = "cust_no")
    private String custNo;

    @JsonProperty("user_no")
    @JSONField(name = "user_no")
    private String userNo;

    @JsonProperty("extra_data")
    @JSONField(name = "extra_data")
    private ExtrData extrData;


    @Data
    @NoArgsConstructor
    public static class ExtrData {

        @JsonProperty("available_amount")
        @JSONField(name = "available_amount")
        private BigDecimal availableAmount;

        @JsonProperty("adjust_amount")
        @JSONField(name = "adjust_amount")
        private BigDecimal adjustAmount;

        @JsonProperty("end_time")
        @JSONField(name = "end_time")
        String endTime;
    }
}