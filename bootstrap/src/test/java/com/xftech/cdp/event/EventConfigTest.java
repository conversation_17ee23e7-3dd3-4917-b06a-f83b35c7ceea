package com.xftech.cdp.event;

import com.xftech.cdp.BaseTest;
import com.xftech.cdp.domain.event.EventMessageProcessor;
import com.xftech.cdp.domain.event.model.dto.MqEventFieldMappingConfig;
import com.xftech.cdp.domain.event.parse.DataParser;
import com.xftech.cdp.domain.event.parse.DataParserStrategy;
import com.xftech.cdp.domain.event.service.MqEventFieldConfigService;
import com.xftech.cdp.domain.mqevent.MqEventFieldMappingConfigRepository;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventMessageVO;
import com.xftech.cdp.infra.repository.cdp.mqevent.MqEventFieldMappingConfigDo;
import com.xftech.cdp.infra.rocketmq.credit.CreditApplyMsgConsumer;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

/**
 * <AUTHOR>
 * @version $ EventConfigTest, v 0.1 2024/11/13 15:16 snail Exp $
 */
@Slf4j
public class EventConfigTest extends BaseTest {
    @Autowired
    private MqEventFieldMappingConfigRepository mappingConfigRepository;

    @Autowired
    private MqEventFieldConfigService mqEventFieldConfigService;

    @Autowired
    private DataParserStrategy dataParserStrategy;

    @Autowired
    private EventMessageProcessor eventMessageProcessor;

    @Autowired
    private CreditApplyMsgConsumer creditApplyMsgConsumer;

    @Test
    public void creditApplyMsgConsumer(){
        String body = "{\"account_no\":\"0823245969165848576\",\"agreement_no\":\"AGN202412311548300823245969165848576\",\"app\":\"xyf01\",\"apply_id\":\"3e089080a8de498a96fa12237579cf73\",\"apply_time\":\"2024-12-31 15:48:13\",\"cnl_ev_code\":\"WC00100023\",\"cnl_no\":\"a6cd731b-cfc0-4a69-bf0e-50d4693f4355\",\"cnl_pd_code\":\"APP001000007\",\"credit_details\":{\"credit_amt\":2600000,\"expire_date\":\"2025-03-01\",\"first_success_apply\":true,\"renew_time\":\"2024-12-31 15:48:30\",\"success_time\":\"2024-12-31 15:48:30\"},\"credit_message_type\":\"success\",\"credit_status\":\"success\",\"cust_no\":\"CTL053ede05f31fd2741a5c34dcf359abd5f\",\"event_datetime\":\"2024-12-31 15:48:30\",\"event_timestamp\":*************,\"first_apply_input\":false,\"inner_app\":\"xyf01\",\"user_no\":\"**********\",\"utm_source\":\"\",\"trigger_datetime\":\"2024-12-31 15:48:30\",\"trigger_timestamp\":*************}";
        MessageExt messageExt = new MessageExt();
        messageExt.setMsgId("sadkfjalsdjflaskdjflsakdfj");
        messageExt.setTags("*");

        creditApplyMsgConsumer.doMessage("tp_rcspt_credit_apply_message",body,new MessageExt());
    }

    @Test
    public void increaseCreditMessageProcess(){
        String topic = "tp_rcspt_risk_amount_change_message", consumer = "cg_xyf_cdp_tp_rcspt_risk_amount_change_message", message = getIncreaseCreditMessage();

        BizEventMessageVO bizEventMessage = eventMessageProcessor.doMessageProcessor(topic,consumer,null,message);
        log.info("message={}",bizEventMessage);
    }

    @Test
    public void messageProcess(){
        String topic = "tp_biz_report_Login1", consumer = "cg_xyf_cdp_tp_biz_report_Login1", message = getMessage();

        BizEventMessageVO bizEventMessage = eventMessageProcessor.doMessageProcessor(topic,consumer,null,message);
        log.info("message={}",bizEventMessage);
    }

    private String getIncreaseCreditMessage(){
        return "{\"app\":\"xyf01\",\"inner_app\":\"xyf01\",\"extra_data\":{\"adjust_amount\":\"30000\",\"decision_result\":{\"result\":\"pass\",\"limit_change\":30000,\"check_result\":\"pass\",\"ruleHitNum\":1,\"report_id\":\"c5c24eb60d9d4badbcd6517d59aaf440\",\"rate_level_output\":\"0\",\"interGroup\":\"jcl_20231120000001_17\",\"ruleHitScore\":1000,\"cash_credit_amount_output\":418045,\"ruleHitResult\":\"hit\",\"ruleTotalScore\":1000,\"is_accompany_flag\":\"N\"},\"ori_amount\":\"418045\",\"final_amount\":\"448045\",\"available_amount\":\"300000\"},\"cust_no\":\"CTL026f40c2007ab51082feab7e2112c6be8\",\"adjust_result\":\"S\",\"manage_type\":\"micro_businesses_identification_increase_amount\",\"user_no\":\"1049012128\",\"event_datetime\":\"2024-11-19 19:05:44\",\"event_timestamp\":1732014344871,\"trigger_datetime\":\"2024-11-19 19:05:44\",\"trigger_timestamp\":1732014344925}";
    }

    private String getMessage(){
        String content = "{\n" +
                "  \"appUserId\": 1131236027,\n" +
                "  \"app\": \"xyf01\",\n" +
                "  \"innerApp\": \"xyf01\",\n" +
                "  \"eventTime\": 1730270767594,\n" +
                "  \"sendTime\": 1730270767594,\n" +
                "  \"extraData\": {\n" +
                "    \"isRenewActivity\": 0,\n" +
                "    \"isVipFund\": 1,\n" +
                "    \"deductType\": 2,\n" +
                "    \"contractPrice\": 13900,\n" +
                "    \"cardType\": 2,\n" +
                "    \"orderPrice\": 13900,\n" +
                "    \"type\": 3,\n" +
                "    \"isRenew\": 2\n" +
                "  }\n" +
                "}";

        return content;
    }

    @Test
    public void parserContent(){
        String content = "{\n" +
                "  \"appUserId\": 1131236027,\n" +
                "  \"app\": \"xyf01\",\n" +
                "  \"innerApp\": \"xyf01\",\n" +
                "  \"eventTime\": 1730270767594,\n" +
                "  \"sendTime\": 1730270767594,\n" +
                "  \"extraData\": {\n" +
                "    \"isRenewActivity\": 0,\n" +
                "    \"isVipFund\": 1,\n" +
                "    \"deductType\": 2,\n" +
                "    \"contractPrice\": 13900,\n" +
                "    \"cardType\": 2,\n" +
                "    \"orderPrice\": 13900,\n" +
                "    \"type\": 3,\n" +
                "    \"isRenew\": 2\n" +
                "  }\n" +
                "}";

        MqEventFieldMappingConfig config = mqEventFieldConfigService.getMqEventFieldMappingConfig("tp_biz_report_Login1","cg_xyf_cdp_tp_biz_report_Login1","");
        DataParser parser = dataParserStrategy.getParser(config.getParse());
        Map<String,Object> map = parser.doParse(content,config);
        log.info("data parse content,map={}",map);
    }

    @Test
    public void getEventFieldConfig(){
        MqEventFieldMappingConfig config = mqEventFieldConfigService.getMqEventFieldMappingConfig("tp_biz_report_Login1","cg_xyf_cdp_tp_biz_report_Login1","");
        log.info("config={}",config);
    }

    @Test
    public void addMqEventConfig(){
        MqEventFieldMappingConfigDo config = new MqEventFieldMappingConfigDo();
        config.setTopic("tp_biz_report_Login1");
        config.setConsumer("cg_xyf_cdp_tp_biz_report_Login1");
        config.setFieldMapping("map");
        mappingConfigRepository.insertSelective(config);
    }

    @Test
    public void loadMqEventConfig(){
        MqEventFieldMappingConfigDo mappingConfig = mappingConfigRepository.selectByTopicAndConsumer("tp_biz_report_Login1","cg_xyf_cdp_tp_biz_report_Login1","");
        log.info("mappingConfig={}",mappingConfig);
    }
}
