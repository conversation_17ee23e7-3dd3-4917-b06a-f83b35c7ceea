package com.xftech.cdp.api.dto.req.sms;

import com.xftech.cdp.api.dto.base.PageRequestDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2023/2/23
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SmsTemplateListReq extends PageRequestDto {

    @ApiModelProperty(value = "single 单条类型； batch 批量类型；all 所有类型")
    private String type;

    @ApiModelProperty(value = "模板id模糊查询")
    private String templateId;
}
