package com.xftech.cdp.domain.strategy.model.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version $ BalanceInvokeStatusEnum, v 0.1 2025/1/10 14:05 mingwen.zang
 */
@Getter
@AllArgsConstructor
public enum BalanceInvokeStatusEnum {
    SUCCESS(0, "成功"),
    BUSINESS_FAIL(1, "逻辑-失败"),
    REMOTE_FAIL(2, "异常-失败"),
    INNER_FAIL(3, "内部-失败"),
    DISCARD(4, "幂等丢弃"),
    FILTER(5, "过滤"),
    ;

    private final int status;
    private final String desc;
}
