package com.xftech.cdp;

import cn.hutool.core.thread.ThreadUtil;
import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.domain.crowd.model.dispatch.CrowdWereHouse;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;

import java.util.List;

public class TestAds extends BaseTest {

    private static final Logger logger = LoggerFactory.getLogger(TestAds.class);

//    @Resource
//    protected JdbcTemplate adsJdbcTemplate;

    protected static BeanPropertyRowMapper<CrowdWereHouse> crowdWereHouseBeanPropertyRowMapper = new BeanPropertyRowMapper<>(CrowdWereHouse.class);

    private String newSql = "select app_user_id,app,inner_app,ab_num,app_user_id_last2,mobile from ads_user_label_detail_info_df limit 10";

    @Test
    public void testAds() {
//        List<CrowdWereHouse> list = adsJdbcTemplate.query(newSql.toString(), crowdWereHouseBeanPropertyRowMapper);
//        logger.info("list={}", list);
    }

    @Test
    public void testAds2() {
        List<CrowdWereHouse> list = DBUtil.selectList("myads", "ads.listAds", null);
        logger.info("list={}", list);
    }

    @Test
    public void testConfig(){

    }


}
