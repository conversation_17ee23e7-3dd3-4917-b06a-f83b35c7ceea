/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.param.service.impl;

import com.xftech.cdp.domain.param.enums.TempParamToBizEventFieldEnum;
import com.xftech.cdp.domain.param.service.ReplaceStrategy;
import com.xftech.cdp.infra.client.usercenter.model.UserInfoResp;
import com.xftech.cdp.infra.config.LogUtil;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventVO;
import com.xftech.cdp.infra.utils.ReflectGetFieldUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * @version $ TempParamReplacer, v 0.1 2024/3/1 15:59 wancheng.qu Exp $
 */
@Slf4j
@Component
public class TempParamReplacer {

    private final Map<String, Supplier<ReplaceStrategy>> replacementMap;

    private final List<String> bizEventParamList;

    @Autowired
    public TempParamReplacer(Map<String, Supplier<ReplaceStrategy>> strategyMap) {
        this.replacementMap = strategyMap;
        this.bizEventParamList = TempParamToBizEventFieldEnum.getAllTempParam();
    }

    public Map<String, Object> replaceElements(String input, UserInfoResp userInfoResp) {
        return replacementMap.entrySet().stream()
                .filter(entry -> input.contains(entry.getKey()))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().get().replace(userInfoResp)
                ));
    }

    public Map<String, Object> bizEventReplaceElements(String input, BizEventVO bizEventVO) {
        try {
            LogUtil.logDebug("bizEventReplaceElements input:{} bizEventVO:{}", input,bizEventVO);
            return bizEventParamList.stream().filter(input::contains)
                    .collect(Collectors.toMap(
                            item -> item,
                            item -> ReflectGetFieldUtil.getFieldValue(bizEventVO, Objects.requireNonNull(TempParamToBizEventFieldEnum.getInstance(item)).getBizEventField())
                    ));
        } catch (Exception e) {
            log.error("消息体中取字段作为短信参数获取失败",e);
            return null;
        }
    }

    public Map<String, Supplier<ReplaceStrategy>> getReplacementMap() {
        return replacementMap;
    }

    public List<String> getBizEventParamList(){
        return bizEventParamList;
    }

}