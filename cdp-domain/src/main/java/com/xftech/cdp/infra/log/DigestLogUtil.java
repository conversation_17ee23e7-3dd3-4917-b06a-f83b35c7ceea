package com.xftech.cdp.infra.log;

import java.util.Optional;

import com.xftech.cdp.infra.client.telemarketing.model.TelePushArgs;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;


/**
 * <AUTHOR>
 * @version v1.0 2025/2/25
 * @description DigestLogUtil
 */
@Slf4j
public class DigestLogUtil {

    private static final String SUCCESS = "SUCCESS";
    private static final String FAIL = "FAIL";

    public static void telDigestLog(TelePushArgs telePushArgs, SendResult sendResult, long elapseTime) {
        try {
            // 请求信息
            String traceId = Optional.ofNullable(telePushArgs).map(TelePushArgs::getBatchNumber).orElse("-");
            String clientIp = "-";
            String applicationName = "telemkt";
            // 响应信息
            boolean invokeResult = true;
            String invokeCode = SUCCESS;
            if (sendResult == null || sendResult.getSendStatus() != SendStatus.SEND_OK) {
                invokeResult = false;
                invokeCode = FAIL;
            }
            info(traceId, clientIp, applicationName, invokeResult, invokeCode, elapseTime, JSON.parseObject(JSON.toJSONString(telePushArgs)));
        } catch (Exception e) {
            log.warn("DigestLogUtil telDigestLog error", e);
        }
    }

    /**
     * 打印摘要日志
     *
     * @param traceId         链路跟踪ID
     * @param clientIp        客户端IP
     * @param applicationName 服务端应用名称
     * @param invokeResult    调用结果
     * @param invokeCode      调用结果码
     * @param elapseTime      接口耗时
     * @param bizInfo         摘要信息
     */
    public static void info(String traceId, String clientIp, String applicationName, boolean invokeResult, String invokeCode, long elapseTime, JSON bizInfo) {
        try {
            BaseDigestLog baseDigestLog = new BaseDigestLog();
            baseDigestLog.setTraceId(traceId);
            baseDigestLog.setClientIp(clientIp);
            baseDigestLog.setApplicationName(applicationName);
            baseDigestLog.setInvokeResult(invokeResult);
            baseDigestLog.setInvokeCode(invokeCode);
            baseDigestLog.setElapseTime(elapseTime);
            baseDigestLog.setBizInfo(bizInfo);
            LoggerConstant.TEL_REACH_LOGGER.info(JSON.toJSONString(baseDigestLog));
        } catch (Exception e) {
            log.warn("DigestLogUtil info error", e);
        }
    }

}
