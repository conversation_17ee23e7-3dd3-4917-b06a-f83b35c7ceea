package com.xftech.cdp.adapter.strategy.label.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ QueryCardSignInfoResponse, v 0.1 2024/11/25 14:09 tianshuo.qiu Exp $
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryCardSignInfoResponse {
    private String code;
    private String message;
    private List<QueryCardSignInfoResponse.Data> data;
    // getter 和 setter

    static class Data {
        private String channelCode;
        private String member;
        private String protocolNo;
        // getter 和 setter
    }
}
