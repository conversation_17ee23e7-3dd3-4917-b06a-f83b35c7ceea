package com.xftech.cdp.api;

import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.base.Response;
import com.xftech.cdp.api.dto.req.CrowdCreateReq;
import com.xftech.cdp.api.dto.req.label.*;
import com.xftech.cdp.api.dto.resp.label.AssociatedCrowdsResp;
import com.xftech.cdp.api.dto.resp.label.LabelClassificationResp;
import com.xftech.cdp.api.dto.resp.label.LabelConfigListResp;
import com.xftech.cdp.api.dto.resp.label.LabelExchangeResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 *
 * <AUTHOR>
 */
@Api(tags = "标签配置")
@RequestMapping("/label")
public interface LabelApi {

    @ApiOperation("标签分类")
    @PostMapping("/classification")
    Response<List<LabelClassificationResp>> classification();

    @ApiOperation("可选配置列表")
    @PostMapping("/config-List")
    Response<List<LabelExchangeResp>> configList();


    @ApiOperation("分页查询")
    @PostMapping("/list")
    Response<PageResultResponse<LabelConfigListResp>> list(@Validated @RequestBody LabelConfigListReq labelConfigListReq);


    @ApiOperation("详情")
    @PostMapping("/detail")
    Response<Boolean> detail(@Validated @RequestBody CrowdCreateReq crowdCreateReq);

    @ApiOperation("关联人群包")
    @PostMapping("/associated-crowds")
    Response<PageResultResponse<AssociatedCrowdsResp>> associatedCrowds(@Validated @RequestBody AssociatedCrowdsReq associatedCrowdsReq);

    /**
     * 修改配置，添加配置，一个接口，通过入参区分
     */
    @ApiOperation("配置")
    @PostMapping("/publish")
    Response<Boolean> labelPublish(@Validated @RequestBody LabelPublishReq labelPublishReq);

    @ApiOperation("编辑注释")
    @PostMapping("/remark")
    Response<Boolean> labelRemark(@Validated @RequestBody LabelRemarkReq labelRemarkReq);

    @ApiOperation("下线")
    @PostMapping("/discard")
    Response<Boolean> labelDiscard(@Validated @RequestBody LabelDiscardReq labelDiscardReq);

    @ApiOperation("清除检查状态")
    @PostMapping("/clear-check")
    Response<Boolean> clearCheckResult(@Validated @RequestBody ClearCheckResultReq checkResultReq);
}
