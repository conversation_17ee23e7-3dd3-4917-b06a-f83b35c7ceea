package com.xftech.cdp.infra.repository.cdp.strategy.po;

import java.util.Date;
import java.util.Map;

import lombok.Data;

/**
 * 引擎延迟重新决策记录表
 * engine_redecision_delay
 */
@Data
public class EngineReDecisionDelayDo {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 策略ID
     */
    private Long strategyId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 营销组名称
     */
    private String groupName;

    /**
     * 延迟重新决策-重入次数
     */
    private Integer reInputCount;

    /**
     * 延迟重新决策时间(单位:s)
     */
    private Integer reInputDelaySecond;

    /**
     * 延迟重新决策时间
     */
    private Date reInputTime;

    /**
     * 事件初始触发时间
     */
    private Date eventFirstTime;

    /**
     * 0-待处理 1-处理成功 2-处理失败
     */
    private Integer status;

    /**
     * 0-实时标签未命中 1-引擎下发再延迟 2-引擎下发营销 3-引擎下发不营销
     */
    private Integer reInputResult;

    /**
     * 初始事件ID
     */
    private String messageId;

    /**
     * 上次记录主键ID
     */
    private Long lastReDecisionId;

    /**
     * 扩展信息
     */
    private String extInfo;

    /**
     * 逻辑删除标记
     */
    private Integer dFlag;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updatedTime;

    /**
     * 日期分区，格式yyyyMMdd
     */
    private Integer dateValue;

}