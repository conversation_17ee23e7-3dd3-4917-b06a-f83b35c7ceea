package com.xftech.cdp.api.dto.req.flowctrl;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * @<NAME_EMAIL>
 */

@Data
public class FlowCtrlCreateReq {
    @Size(max = 30, message = "规则名称不能超过30个字符")
    @NotBlank(message = "规则名称不能为空")
    @ApiModelProperty(value = "规则名称", required = true)
    private String name;

    @ApiModelProperty(value = "规则描述")
    private String description;

    @NotNull(message = "规则类型不能为空")
    @ApiModelProperty(value = "规则类型 1-策略 2-渠道 3-多策略共享 4-业务线", required = true)
    private Integer type;

    @NotEmpty(message = "生效渠道不能为空")
    @ApiModelProperty(value = "生效渠道 如 [0,1]", required = true)
    private List<String> effectiveContent;

    @ApiModelProperty(value = "触达限制天数")
    @NotNull(message = "触达限制天数不能为空")
    private Integer limitDays;

    @ApiModelProperty(value = "触达限制次数")
    @NotNull(message = "触达限制次数不能为空")
    private Integer limitTimes;

    /**
     * 业务线在type为4时生效
     */
    @ApiModelProperty(value = "业务线")
    private String bizType;

}
