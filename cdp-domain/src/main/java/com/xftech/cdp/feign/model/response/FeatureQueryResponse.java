package com.xftech.cdp.feign.model.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.MapUtils;

import java.util.Map;

/**
 * <AUTHOR>
 * @version v1.0 2025/1/3
 * @description RealCollectResponse
 */
@Data
public class FeatureQueryResponse {

    private boolean suc;

    private boolean hasFailed;

    private Map<String, FeatureValueModel> featureValues;

    public boolean isFeatureValuesOk() {
        return suc && MapUtils.isNotEmpty(featureValues);
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FeatureValueModel {
        /**
         * 特征结果值
         */
        private Object obj;

        /**
         * 特征结果值类型
         */
        private String type;
    }

}
