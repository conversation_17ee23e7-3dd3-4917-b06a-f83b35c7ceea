package com.xftech.cdp.api.dto.marketing.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version v1.0 2024/10/11
 * @description ParticipateRequest
 */
@ApiModel("优惠券膨胀 response")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class InflationResponse {

    @ApiModelProperty(value = "业务执行结果Code")
    private String bizCode;

    @ApiModelProperty(value = "业务执行结果message")
    private String bizMessage;

    @ApiModelProperty(value = "优惠券ID")
    private String couponId;

    @ApiModelProperty(value = "优惠券名称")
    private String couponName;

    @ApiModelProperty(value = "优惠券活动名称")
    private String couponActivityName;

    @ApiModelProperty(value = "优惠券有效期倒计时-时间戳")
    private Long expirationCountdown;

    @ApiModelProperty(value = "优惠券膨胀进度, status=2时返回")
    private Integer expansionStatus;

    public InflationResponse(String bizCode, String bizMessage) {
        this.bizCode = bizCode;
        this.bizMessage = bizMessage;
    }

}
