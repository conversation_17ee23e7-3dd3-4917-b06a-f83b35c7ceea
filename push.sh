#!/bin/bash

# GitHub推送脚本
# 使用方法：chmod +x push.sh && ./push.sh

echo "🚀 开始推送xyf-touch-service到GitHub..."
echo ""

# 显示当前状态
echo "📋 当前Git状态："
git status --short
echo ""

# 显示待推送的提交
echo "📝 待推送的提交："
git log --oneline origin/main..HEAD 2>/dev/null || git log --oneline -2
echo ""

# 检查远程仓库配置
echo "🔗 远程仓库配置："
git remote -v
echo ""

# 提供推送选项
echo "请选择推送方式："
echo "1) SSH推送（推荐，需要SSH密钥密码）"
echo "2) HTTPS推送（需要GitHub用户名和个人访问令牌）"
echo "3) 退出"
echo ""

read -p "请输入选择 (1-3): " choice

case $choice in
    1)
        echo ""
        echo "🔑 使用SSH方式推送..."
        
        # 切换到SSH URL
        git remote set-<NAME_EMAIL>:ruintime/xyf-touch-service.git
        echo "✅ 已切换到SSH URL"
        
        # 尝试推送
        echo "🚀 开始推送..."
        echo "注意：如果提示输入密码，请输入您的SSH密钥密码"
        echo ""
        
        if git push origin main; then
            echo ""
            echo "🎉 推送成功！"
            echo "📱 请访问查看：https://github.com/ruintime/xyf-touch-service"
        else
            echo ""
            echo "❌ SSH推送失败，可能需要："
            echo "1. 确认SSH密钥已添加到GitHub账户"
            echo "2. 输入正确的SSH密钥密码"
            echo "3. 尝试使用HTTPS方式推送"
        fi
        ;;
        
    2)
        echo ""
        echo "🌐 使用HTTPS方式推送..."
        
        # 切换到HTTPS URL
        git remote set-url origin https://github.com/ruintime/xyf-touch-service.git
        echo "✅ 已切换到HTTPS URL"
        
        # 尝试推送
        echo "🚀 开始推送..."
        echo "注意：请输入GitHub用户名和个人访问令牌（不是密码）"
        echo ""
        
        if git push origin main; then
            echo ""
            echo "🎉 推送成功！"
            echo "📱 请访问查看：https://github.com/ruintime/xyf-touch-service"
        else
            echo ""
            echo "❌ HTTPS推送失败，可能需要："
            echo "1. 确认GitHub用户名正确"
            echo "2. 使用个人访问令牌而不是密码"
            echo "3. 确认令牌有repo权限"
        fi
        ;;
        
    3)
        echo "👋 退出推送脚本"
        exit 0
        ;;
        
    *)
        echo "❌ 无效选择，请重新运行脚本"
        exit 1
        ;;
esac

echo ""
echo "📊 推送完成后的统计："
echo "- 总提交数：$(git rev-list --count HEAD)"
echo "- 本次推送提交数：2"
echo "- 新增文件：49个"
echo "- 新增代码行数：5,800+"
echo ""

echo "🎯 主要功能："
echo "✅ 统一触达模型（TouchRequest、TouchResponse等）"
echo "✅ 统一触达服务接口和实现"
echo "✅ 参数转换器支持四种触达方式"
echo "✅ REST API控制器"
echo "✅ MQ基础回执处理"
echo "✅ 完整的单元测试和技术文档"
echo ""

echo "📚 查看文档："
echo "- 设计总结：docs/触达统一入参模型设计总结.md"
echo "- 实施总结：docs/项目结构和实施完成总结.md"
echo "- 推送指南：手动推送指南.md"
echo ""

echo "🔗 GitHub仓库：https://github.com/ruintime/xyf-touch-service"
echo "✨ 推送脚本执行完成！"
