package com.xftech.cdp.feign;

import com.xftech.cdp.BaseTest;
import com.xftech.cdp.api.dto.req.GoodsListReq;
import com.xftech.cdp.domain.strategy.service.impl.DpsServiceImpl;
import com.xftech.cdp.domain.ads.service.impl.AdsStrategyLabelServiceImpl;
import com.xftech.cdp.domain.flowctrl.model.enums.StrategyTypeEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyInstantLabelTypeEnum;
import com.xftech.cdp.domain.strategy.service.impl.GoodsServiceImpl;
import com.xftech.cdp.domain.strategy.vo.BatchAdsLabelVO;
import com.xftech.cdp.feign.model.*;
import com.xftech.cdp.feign.model.requset.*;
import com.xftech.cdp.feign.model.response.DpsResponse;
import com.xftech.cdp.feign.model.response.GoodsResponse;
import com.xftech.cdp.feign.model.response.MetaLabelDto;
import com.xftech.cdp.feign.model.response.PushResponse;
import com.xftech.cdp.feign.model.response.VipCoreResponse;
import com.xftech.cdp.infra.aviator.util.DateTimeUtil;
import com.xftech.cdp.infra.client.increaseamt.RcsProviderClient;
import com.xftech.cdp.infra.client.increaseamt.model.req.AccessControlQueryReq;
import com.xftech.cdp.infra.client.increaseamt.model.req.RequestHeader;
import com.xftech.cdp.infra.client.increaseamt.model.resp.AccessControlResp;
import com.xftech.cdp.infra.client.increaseamt.model.resp.Response;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventVO;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketEventConditionDo;
import com.xftech.cdp.infra.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.*;

/**
 * <AUTHOR>
 * @version $ PushFeignClientTest, v 0.1 2024/1/16 21:11 qu.lu Exp $
 */
@Slf4j
public class PushFeignClientTest extends BaseTest {

    @Autowired
    private PushFeignClient pushFeignClient;
    @Autowired
    private GoodsFeignClient goodsFeignClient;
    @Autowired
    private GoodsServiceImpl goodsService;
    @Autowired
    private DpsFeignClient dpsFeignClient;
    @Autowired
    private DpsServiceImpl dpsService;
    @Autowired
    private VipCoreFeignClient vipCoreFeignClient;
    @Autowired
    private AdsStrategyLabelServiceImpl adsStrategyLabelService;
    @Autowired
    private RcsProviderClient rcsProviderClient;


    @Test
    public void sendPush(){
        PushBaseRequest<SendPushRequest> request = new PushBaseRequest<>();
        SendPushRequest sendPushRequest = new SendPushRequest();
        sendPushRequest.setApp("xyf01");
        sendPushRequest.setTemplateId("pushtpl100006");

        List<PushUserData> userDataList = new ArrayList<>();
        PushUserData userData;
        userData = new PushUserData();
        userData.setUserNo("111111111270286");
        userDataList.add(userData);

        userData = new PushUserData();
        userData.setUserNo("111111111156004");
        userDataList.add(userData);
        sendPushRequest.setPushDataList(userDataList);
        request.setArgs(sendPushRequest);
        request.setUa("xyf-cdp");

        PushResponse<SendPushInfo> response = pushFeignClient.sendPush(request);
        log.info("response={}",response);
    }

    @Test
    public void loadPushTemplateDetail(){
        PushBaseRequest<TemplateDetailRequest> request = new PushBaseRequest<>();
        TemplateDetailRequest detailRequest = new TemplateDetailRequest();
        detailRequest.setTemplateId("pushtpl100006");
        detailRequest.setApp("xyf01");

        request.setArgs(detailRequest);
        request.setUa("xyf-cdp");

        PushResponse<PushTemplateDetail> response = pushFeignClient.loadPushTemplateDetail(request);
        log.info("response={}",response);
    }

    @Test
    public void queryPushTemplate(){
        PushBaseRequest<TemplateListRequest> request = new PushBaseRequest<>();
        TemplateListRequest listRequest = new TemplateListRequest();
        listRequest.setPageNum(1);
        listRequest.setPageSize(10);

        request.setArgs(listRequest);
        request.setUa("xyf-cdp");

        PushResponse<PushTemplateList> response = pushFeignClient.queryPushTemplateList(request);
        log.info("response={}",response);
    }

    @Test
    public void queryGoodsList(){
        GoodsListReq goodsListReq = new GoodsListReq();
        goodsListReq.setGoodsId(1L);
        GoodsResponse<GoodsList> goodsListResp = goodsFeignClient.queryGoodsList(new GoodsListRequest(),"xyf-cdp");
        log.info("response={}", JsonUtil.toJson(goodsListResp));
    }

    @Test
    public void queryVipCoreStatus(){
        VipCoreRenewStatusBatchRequest objectVipCoreBaseRequest = new VipCoreRenewStatusBatchRequest();
        objectVipCoreBaseRequest.setUserNos(Arrays.asList(1639203089095980781L, 111111111181170L, 111111111271540L));
        log.info("request={}", JsonUtil.toJson(objectVipCoreBaseRequest));
        VipCoreResponse<Map<Long, Boolean>> mapVipCoreBaseRequest = vipCoreFeignClient.renewStatusBatch(objectVipCoreBaseRequest);
        log.info("response={}", JsonUtil.toJson(mapVipCoreBaseRequest));
    }

    @Test
    public void queryGoodsDetailList(){
        GoodsDetail goodsDetailByGoodsId = goodsService.getGoodsDetailByGoodsId(1L);
        log.info("response={}", JsonUtil.toJson(goodsDetailByGoodsId));
    }


    @Test
    public void dpsQuery() {
        DpsPageRequest dpsPageRequest = new DpsPageRequest();
        DpsResponse<List<MetaLabelDto>> listDpsResponse = dpsFeignClient.queryLabelList(dpsPageRequest);
        log.info("request={}", JsonUtil.toJson(dpsPageRequest));
        log.info("response={}", JsonUtil.toJson(listDpsResponse));
    }

    @Test
    public void serviceDpsQuery() {
        DpsPageRequest dpsPageRequest = new DpsPageRequest();
        dpsPageRequest.setPageNum(100);
        dpsPageRequest.setPageSize(100);
        List<MetaLabelDto> metaLabel = dpsService.getMetaLabel(dpsPageRequest);
        System.out.println(metaLabel.stream().map(MetaLabelDto::getId).collect(Collectors.toList()));
    }

    @Test
    public void queryLabel(){
        Set<String> labelSet = new HashSet<>();
        labelSet.add("vip_card_renewal_status");
        BatchAdsLabelVO batchAdsLabelVO = new BatchAdsLabelVO();
        batchAdsLabelVO.setApp("xyf01");
        List<BatchAdsLabelVO.UserInfo> userInfoList = new ArrayList<>();
        BatchAdsLabelVO.UserInfo userInfo = new BatchAdsLabelVO.UserInfo();
        userInfo.setAppUserId(111111111271540L);
        userInfoList.add(userInfo);
        batchAdsLabelVO.setUserInfoList(userInfoList);
        Map<Long, Map<String, Object>> longMapMap = adsStrategyLabelService.queryBatch(batchAdsLabelVO, labelSet, StrategyInstantLabelTypeEnum.LABEL, StrategyTypeEnum.OFFLINE_STRATEGY);
        log.info("response={}", JsonUtil.toJson(longMapMap));
    }

    @Test
    public void queryRcs(){
        String orderNo = String.format("%s_%s", DateTimeUtil.formatDateToStr(new Date(), null), 11011);
        RequestHeader requestHeader = rcsProviderClient.buildAccessControlHeader(orderNo, "xyf01", "xyf01");
        AccessControlQueryReq accessControlQueryReq = new AccessControlQueryReq("500230199005252978", null, null);
        Response<AccessControlResp> accessControlRespResponse = rcsProviderClient.accessControlQuery(requestHeader, accessControlQueryReq);
        log.info("response={}", JsonUtil.toJson(accessControlRespResponse));
    }

    @Test
    public void queryT0Label(){
        Set<String> labelSet = new HashSet<>();
        labelSet.add("vip_card_renewal_status");
        BizEventVO bizEventVO = new BizEventVO();
        bizEventVO.setAppUserId(111111111271540L);

        Map<Long, Map<String, Object>> longMapMap = adsStrategyLabelService.query(bizEventVO, labelSet, StrategyInstantLabelTypeEnum.LABEL);
        log.info("response={}", JsonUtil.toJson(longMapMap));
    }

}
