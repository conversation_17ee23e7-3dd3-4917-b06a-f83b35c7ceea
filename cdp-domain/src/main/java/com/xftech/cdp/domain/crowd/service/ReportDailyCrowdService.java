/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.domain.crowd.service;

import com.xftech.base.database.Page;
import com.xftech.cdp.api.dto.req.CrowdRefreshInfoReq;
import com.xftech.cdp.domain.crowd.repository.ReportDailyCrowdRepository;
import com.xftech.cdp.infra.repository.cdp.crowd.po.ReportDailyCrowdDo;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ ReportDailyCrowdService, v 0.1 2023/12/1 16:13 lingang.han Exp $
 */

@Service
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class ReportDailyCrowdService {

    @Autowired
    private ReportDailyCrowdRepository repository;

    public ReportDailyCrowdDo queryById(Long id) {
        return repository.selectById(id);
    }

    public boolean save(ReportDailyCrowdDo reportDailyCrowdDo) {
        return repository.insert(reportDailyCrowdDo);
    }

    public boolean updateById(ReportDailyCrowdDo reportDailyCrowdDo) {
        return repository.updateById(reportDailyCrowdDo);
    }

    public void saveOrUpdateBatch(List<ReportDailyCrowdDo> reportDailyCrowdDos) {
        for (ReportDailyCrowdDo reportDailyCrowdDo : reportDailyCrowdDos) {
            if (repository.existReportDailyCrowd(reportDailyCrowdDo)) {
                repository.updateByDateAndCrowdId(reportDailyCrowdDo);
            } else {
                save(reportDailyCrowdDo);
            }
        }
    }

    public List<ReportDailyCrowdDo> selectTodayFail() {
        return repository.selectTodayFail();
    }

    public List<ReportDailyCrowdDo> selectToday() {
        return repository.selectToday();
    }

    public Page<ReportDailyCrowdDo> queryPageByCrowdId(CrowdRefreshInfoReq crowdRefreshInfo) {
        return repository.queryPageByCrowdId(crowdRefreshInfo);
    }
}