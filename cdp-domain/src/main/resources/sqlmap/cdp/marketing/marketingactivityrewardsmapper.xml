<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="marketingActivityRewards">

    <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.marketing.po.MarketingActivityRewardsDo">
        <id property="id" column="id"/>
        <result property="activityId" column="activity_id"/>
        <result property="rewardName" column="reward_name"/>
        <result property="rewardType" column="reward_type"/>
        <result property="rewardId" column="reward_id"/>
        <result property="rewardPriority" column="reward_priority"/>
        <result property="rewardCrowdPack" column="reward_crowd_pack"/>
        <result property="rewardFallback" column="reward_fallback"/>
        <result property="dFlag" column="d_flag"/>
        <result property="createdOp" column="created_op"/>
        <result property="updatedOp" column="updated_op"/>
        <result property="createdTime" column="created_time"/>
        <result property="updatedTime" column="updated_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , activity_id, reward_name, reward_type, reward_id, reward_priority, reward_crowd_pack, reward_fallback, d_flag, created_op, updated_op, created_time, updated_time
    </sql>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from marketing_activity_rewards where id = #{id} and d_flag = 0
    </select>

    <select id="selectByActivityId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from marketing_activity_rewards where activity_id = #{activityId} and d_flag = 0
    </select>

    <insert id="insertSelective"
            parameterType="com.xftech.cdp.infra.repository.cdp.marketing.po.MarketingActivityRewardsDo"
            keyColumn="id" keyProperty="id" useGeneratedKeys="true">
        insert into marketing_activity_rewards
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="activityId != null">
                activity_id,
            </if>
            <if test="rewardName != null">
                reward_name,
            </if>
            <if test="rewardType != null">
                reward_type,
            </if>
            <if test="rewardId != null">
                reward_id,
            </if>
            <if test="rewardPriority != null">
                reward_priority,
            </if>
            <if test="rewardCrowdPack != null">
                reward_crowd_pack,
            </if>
            <if test="rewardFallback != null">
                reward_fallback,
            </if>
            <if test="createdOp != null">
                created_op,
            </if>
            <if test="updatedOp != null">
                updated_op,
            </if>
            created_time,
            updated_time,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="activityId != null">
                #{activityId},
            </if>
            <if test="rewardName != null">
                #{rewardName},
            </if>
            <if test="rewardType != null">
                #{rewardType},
            </if>
            <if test="rewardId != null">
                #{rewardId},
            </if>
            <if test="rewardPriority != null">
                #{rewardPriority},
            </if>
            <if test="rewardCrowdPack != null">
                #{rewardCrowdPack},
            </if>
            <if test="rewardFallback != null">
                #{rewardFallback},
            </if>
            <if test="createdOp != null">
                #{createdOp},
            </if>
            <if test="updatedOp != null">
                #{updatedOp},
            </if>
            now(),
            now(),
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.xftech.cdp.infra.repository.cdp.marketing.po.MarketingActivityRewardsDo">
        update marketing_activity_rewards
        <set>
            <if test="activityId != null">
                activity_id = #{activityId},
            </if>
            <if test="rewardName != null">
                reward_name = #{rewardName},
            </if>
            <if test="rewardType != null">
                reward_type = #{rewardType},
            </if>
            <if test="rewardId != null">
                reward_id = #{rewardId},
            </if>
            <if test="rewardPriority != null">
                reward_priority = #{rewardPriority},
            </if>
            <if test="rewardCrowdPack != null">
                reward_crowd_pack = #{rewardCrowdPack},
            </if>
            <if test="rewardFallback != null">
                reward_fallback = #{rewardFallback},
            </if>
            <if test="createdOp != null">
                created_op = #{createdOp},
            </if>
            <if test="updatedOp != null">
                updated_op = #{updatedOp},
            </if>
            updated_time = now()
        </set>
        where id = #{id}
    </update>

    <delete id="deleteByActivityId">
        update marketing_activity_rewards
        set d_flag = 1
        where activity_id = #{id}
    </delete>

</mapper>
