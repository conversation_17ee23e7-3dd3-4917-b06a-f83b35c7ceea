package com.xftech.cdp.infra.rabbitmq;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * RabbitMq 生产者
 * <AUTHOR>
 * @since 2023-04-06
 */
@Slf4j
@Component
public class RabbitMqProducer {
    @Resource
    private RabbitTemplate rabbitTemplate;
//    @Resource
//    private QueueConfig queueConfig;


    public void send(JSONObject notifyMsg) {
        log.info("生产消息：{}", notifyMsg);
//        this.rabbitTemplate.convertAndSend(queueConfig.getSmsReportExchange(), queueConfig.getSmsReportRoutingKey(), notifyMsg.toString());
    }
}
