package com.xftech.cdp.domain.event.model.enums;

import lombok.Getter;

/**
 * 科学计算的操作符
 * <AUTHOR>
 * @version $ CalculateOperateEnum, v 0.1 2024/11/19 14:19 snail Exp $
 */
@Getter
public enum CalculateOperateEnum {

    ADD("+","加"),
    MINUS("-","减"),
    MULTIPLY("*","乘"),
    DIVIDE("/","除"),
    ;

    CalculateOperateEnum(String operate,String desc){
        this.operate = operate;
        this.desc = desc;
    }

    /** 操作符 */
    private final String operate;
    /** 操作符描述 */
    private final String desc;
}
