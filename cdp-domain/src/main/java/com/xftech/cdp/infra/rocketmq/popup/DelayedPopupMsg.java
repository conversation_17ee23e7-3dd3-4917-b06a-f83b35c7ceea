package com.xftech.cdp.infra.rocketmq.popup;

import java.util.Map;

import com.google.common.collect.Maps;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0 2025/6/4
 * @description DelayedPopupMsg
 */
@Data
public class DelayedPopupMsg {

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 用户号
     */
    private String userNo;

    /**
     * 弹窗ID
     */
    private String popupId;

    /**
     * 弹窗营销时长，单位:小时
     */
    private Integer expiredHours;

    /**
     * 扩展参数
     */
    private Map<String, Object> popup_params = Maps.newHashMap();

}
