package com.xftech.cdp.infra.client.randomnum.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * @<NAME_EMAIL>
 * @date 2023/3/31 15:48
 */
@Data
public class BaseRandomResp<Response> {

    private static final long serialVersionUID = 1L;

    @JsonProperty("code")
    @JSONField(name = "code")
    private String code;

    @JsonProperty("msg")
    @JSONField(name = "msg")
    private String msg;

    @JsonProperty("data")
    @JSONField(name = "data")
    private Response data;

    /**
     * '000000' => '成功'
     * '400200' => '签名错误',
     */
    public boolean isSuccess() {
        return StringUtils.equals(code, "200");
    }
}