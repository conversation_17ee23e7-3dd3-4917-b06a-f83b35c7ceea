package com.xftech.cdp.infra.utils;

import com.xftech.cdp.BaseTest;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;


public class AviatorUtilTest extends BaseTest {


    @Test
    public void compute01() {

        String script = "not_login == 1 && not_finish_borrow == 0";
        Integer notLogin = 1;
        Integer notBorrow = 0;
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("not_login", notLogin);
        paramMap.put("not_finish_borrow", notBorrow);
        Boolean result = AviatorUtil.compute(script, paramMap);
        logger.info("script:{}, paramMap:{}, result:{}", script, paramMap, result);


    }

    @Test
    public void compute001() {

        String script = "contain(app, 'xyf01,fxk')";
        String app = "fxk";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("app", app);
        Boolean result = AviatorUtil.compute(script, paramMap);
        logger.info("script:{}, paramMap:{}, result:{}", script, paramMap, result);

    }

    @Test
    public void compute02() {
        String script = "notLogin == '1' && notBorrow == '0'";
        String notLogin = "1";
        String notBorrow = "0";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("notLogin", notLogin);
        paramMap.put("notBorrow", notBorrow);
        Boolean result = AviatorUtil.compute(script, paramMap);
        logger.info("script:{}, paramMap:{}, result:{}", script, paramMap, result);

    }

    @Test
    public void compute03() {
        String script = "notLogin == '1' && notBorrow == '0'";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("notLogin", "1");
//        paramMap.put("notBorrow", "0");
        Boolean result = AviatorUtil.compute(script, paramMap);
        logger.info("script:{}, paramMap:{}, result:{}", script, paramMap, result);

    }

    @Test
    public void compute04() {
        String script = "notLogin==1 && amount > 1000";
        BigDecimal amount = new BigDecimal("1000.01");
        Integer notLogin = 1;
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("notLogin", notLogin);
        paramMap.put("amount", amount);
        Boolean result = AviatorUtil.compute(script, paramMap);
        logger.info("script:{}, paramMap:{}, result:{}", script, paramMap, result);

    }

    @Test
    public void compute05() {
        String script = "(amount >= 1 && amount <= 1000)";
        Integer amount = new Integer("1001");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("amount", amount);
        Boolean result = AviatorUtil.compute(script, paramMap);
        logger.info("script:{}, paramMap:{}, result:{}", script, paramMap, result);
    }
}