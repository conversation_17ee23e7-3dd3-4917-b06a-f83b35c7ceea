<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="statStrategyFlowData">
  <resultMap id="BaseResultMap" type="com.xftech.cdp.domain.stat.entity.StatStrategyEngineFlowDataEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_date" jdbcType="TIMESTAMP" property="bizDate" />
    <result column="strategy_id" jdbcType="BIGINT" property="strategyId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="crowd_refresh_time" jdbcType="TIMESTAMP" property="crowdRefreshTime" />
    <result column="crowd_user_num" jdbcType="INTEGER" property="crowdUserNum" />
    <result column="event_sum" jdbcType="INTEGER" property="eventSum" />
    <result column="filter_event_sum" jdbcType="INTEGER" property="filterEventSum" />
    <result column="engine_sum" jdbcType="INTEGER" property="engineSum" />
    <result column="engine_num" jdbcType="INTEGER" property="engineNum" />
    <result column="market_sum" jdbcType="INTEGER" property="marketSum" />
    <result column="market_num" jdbcType="INTEGER" property="marketNum" />
    <result column="exclude_market_sum" jdbcType="INTEGER" property="excludeMarketSum" />
    <result column="flow_control_sum" jdbcType="INTEGER" property="flowControlSum" />
    <result column="dispatch_num" jdbcType="INTEGER" property="dispatchNum" />
    <result column="not_market_sum" jdbcType="INTEGER" property="notMarketSum" />
    <result column="not_market_num" jdbcType="INTEGER" property="notMarketNum" />
    <result column="exclude_not_market_sum" jdbcType="INTEGER" property="excludeNotMarketSum" />
    <result column="decision_fail_sum" jdbcType="INTEGER" property="decisionFailSum" />
    <result column="decision_fail_num" jdbcType="INTEGER" property="decisionFailNum" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
  </resultMap>
  <sql id="Base_Column_List">
    `id`, `biz_date`, `strategy_id`, `type`, `crowd_refresh_time`, `crowd_user_num`, 
    `event_sum`, `filter_event_sum`, `engine_sum`, `engine_num`, `market_sum`, `market_num`, 
    `exclude_market_sum`, `flow_control_sum`, `dispatch_num`, `not_market_sum`, `not_market_num`, 
    `exclude_not_market_sum`, `decision_fail_sum`, `decision_fail_num`, `created_time`, 
    `updated_time`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from stat_strategy_engine_flow_data
    where `id` = #{id,jdbcType=BIGINT}
  </select>

  <insert id="insertSelective" parameterType="com.xftech.cdp.domain.stat.entity.StatStrategyEngineFlowDataEntity">
    insert into stat_strategy_engine_flow_data
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="bizDate != null">
        `biz_date`,
      </if>
      <if test="strategyId != null">
        `strategy_id`,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="crowdRefreshTime != null">
        `crowd_refresh_time`,
      </if>
      <if test="crowdUserNum != null">
        `crowd_user_num`,
      </if>
      <if test="eventSum != null">
        `event_sum`,
      </if>
      <if test="filterEventSum != null">
        `filter_event_sum`,
      </if>
      <if test="engineSum != null">
        `engine_sum`,
      </if>
      <if test="engineNum != null">
        `engine_num`,
      </if>
      <if test="marketSum != null">
        `market_sum`,
      </if>
      <if test="marketNum != null">
        `market_num`,
      </if>
      <if test="excludeMarketSum != null">
        `exclude_market_sum`,
      </if>
      <if test="flowControlSum != null">
        `flow_control_sum`,
      </if>
      <if test="dispatchNum != null">
        `dispatch_num`,
      </if>
      <if test="notMarketSum != null">
        `not_market_sum`,
      </if>
      <if test="notMarketNum != null">
        `not_market_num`,
      </if>
      <if test="excludeNotMarketSum != null">
        `exclude_not_market_sum`,
      </if>
      <if test="decisionFailSum != null">
        `decision_fail_sum`,
      </if>
      <if test="decisionFailNum != null">
        `decision_fail_num`,
      </if>
      <if test="createdTime != null">
        `created_time`,
      </if>
      <if test="updatedTime != null">
        `updated_time`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="bizDate != null">
        #{bizDate,jdbcType=TIMESTAMP},
      </if>
      <if test="strategyId != null">
        #{strategyId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="crowdRefreshTime != null">
        #{crowdRefreshTime,jdbcType=TIMESTAMP},
      </if>
      <if test="crowdUserNum != null">
        #{crowdUserNum,jdbcType=INTEGER},
      </if>
      <if test="eventSum != null">
        #{eventSum,jdbcType=INTEGER},
      </if>
      <if test="filterEventSum != null">
        #{filterEventSum,jdbcType=INTEGER},
      </if>
      <if test="engineSum != null">
        #{engineSum,jdbcType=INTEGER},
      </if>
      <if test="engineNum != null">
        #{engineNum,jdbcType=INTEGER},
      </if>
      <if test="marketSum != null">
        #{marketSum,jdbcType=INTEGER},
      </if>
      <if test="marketNum != null">
        #{marketNum,jdbcType=INTEGER},
      </if>
      <if test="excludeMarketSum != null">
        #{excludeMarketSum,jdbcType=INTEGER},
      </if>
      <if test="flowControlSum != null">
        #{flowControlSum,jdbcType=INTEGER},
      </if>
      <if test="dispatchNum != null">
        #{dispatchNum,jdbcType=INTEGER},
      </if>
      <if test="notMarketSum != null">
        #{notMarketSum,jdbcType=INTEGER},
      </if>
      <if test="notMarketNum != null">
        #{notMarketNum,jdbcType=INTEGER},
      </if>
      <if test="excludeNotMarketSum != null">
        #{excludeNotMarketSum,jdbcType=INTEGER},
      </if>
      <if test="decisionFailSum != null">
        #{decisionFailSum,jdbcType=INTEGER},
      </if>
      <if test="decisionFailNum != null">
        #{decisionFailNum,jdbcType=INTEGER},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        #{updatedTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xftech.cdp.domain.stat.entity.StatStrategyEngineFlowDataEntity">
    update stat_strategy_engine_flow_data
    <set>
      <if test="bizDate != null">
        `biz_date` = #{bizDate,jdbcType=TIMESTAMP},
      </if>
      <if test="strategyId != null">
        `strategy_id` = #{strategyId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="crowdRefreshTime != null">
        `crowd_refresh_time` = #{crowdRefreshTime,jdbcType=TIMESTAMP},
      </if>
      <if test="crowdUserNum != null">
        `crowd_user_num` = #{crowdUserNum,jdbcType=INTEGER},
      </if>
      <if test="eventSum != null">
        `event_sum` = #{eventSum,jdbcType=INTEGER},
      </if>
      <if test="filterEventSum != null">
        `filter_event_sum` = #{filterEventSum,jdbcType=INTEGER},
      </if>
      <if test="engineSum != null">
        `engine_sum` = #{engineSum,jdbcType=INTEGER},
      </if>
      <if test="engineNum != null">
        `engine_num` = #{engineNum,jdbcType=INTEGER},
      </if>
      <if test="marketSum != null">
        `market_sum` = #{marketSum,jdbcType=INTEGER},
      </if>
      <if test="marketNum != null">
        `market_num` = #{marketNum,jdbcType=INTEGER},
      </if>
      <if test="excludeMarketSum != null">
        `exclude_market_sum` = #{excludeMarketSum,jdbcType=INTEGER},
      </if>
      <if test="flowControlSum != null">
        `flow_control_sum` = #{flowControlSum,jdbcType=INTEGER},
      </if>
      <if test="dispatchNum != null">
        `dispatch_num` = #{dispatchNum,jdbcType=INTEGER},
      </if>
      <if test="notMarketSum != null">
        `not_market_sum` = #{notMarketSum,jdbcType=INTEGER},
      </if>
      <if test="notMarketNum != null">
        `not_market_num` = #{notMarketNum,jdbcType=INTEGER},
      </if>
      <if test="excludeNotMarketSum != null">
        `exclude_not_market_sum` = #{excludeNotMarketSum,jdbcType=INTEGER},
      </if>
      <if test="decisionFailSum != null">
        `decision_fail_sum` = #{decisionFailSum,jdbcType=INTEGER},
      </if>
      <if test="decisionFailNum != null">
        `decision_fail_num` = #{decisionFailNum,jdbcType=INTEGER},
      </if>
      <if test="createdTime != null">
        `created_time` = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        `updated_time` = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>

  <select id="existStrategyFlowData" resultType="integer">
    select
      count(*)
    from stat_strategy_engine_flow_data
    where strategy_id = #{strategyId} and biz_date = #{bizDate}
  </select>

  <update id="updateByDateAndStrategyId" parameterType="com.xftech.cdp.domain.stat.entity.StatStrategyEngineFlowDataEntity">
    update stat_strategy_engine_flow_data
    <set>
      <if test="bizDate != null">
        `biz_date` = #{bizDate,jdbcType=TIMESTAMP},
      </if>
      <if test="strategyId != null">
        `strategy_id` = #{strategyId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="crowdRefreshTime != null">
        `crowd_refresh_time` = #{crowdRefreshTime,jdbcType=TIMESTAMP},
      </if>
      <if test="crowdUserNum != null">
        `crowd_user_num` = #{crowdUserNum,jdbcType=INTEGER},
      </if>
      <if test="eventSum != null">
        `event_sum` = #{eventSum,jdbcType=INTEGER},
      </if>
      <if test="filterEventSum != null">
        `filter_event_sum` = #{filterEventSum,jdbcType=INTEGER},
      </if>
      <if test="engineSum != null">
        `engine_sum` = #{engineSum,jdbcType=INTEGER},
      </if>
      <if test="engineNum != null">
        `engine_num` = #{engineNum,jdbcType=INTEGER},
      </if>
      <if test="marketSum != null">
        `market_sum` = #{marketSum,jdbcType=INTEGER},
      </if>
      <if test="marketNum != null">
        `market_num` = #{marketNum,jdbcType=INTEGER},
      </if>
      <if test="excludeMarketSum != null">
        `exclude_market_sum` = #{excludeMarketSum,jdbcType=INTEGER},
      </if>
      <if test="flowControlSum != null">
        `flow_control_sum` = #{flowControlSum,jdbcType=INTEGER},
      </if>
      <if test="dispatchNum != null">
        `dispatch_num` = #{dispatchNum,jdbcType=INTEGER},
      </if>
      <if test="notMarketSum != null">
        `not_market_sum` = #{notMarketSum,jdbcType=INTEGER},
      </if>
      <if test="notMarketNum != null">
        `not_market_num` = #{notMarketNum,jdbcType=INTEGER},
      </if>
      <if test="excludeNotMarketSum != null">
        `exclude_not_market_sum` = #{excludeNotMarketSum,jdbcType=INTEGER},
      </if>
      <if test="decisionFailSum != null">
        `decision_fail_sum` = #{decisionFailSum,jdbcType=INTEGER},
      </if>
      <if test="decisionFailNum != null">
        `decision_fail_num` = #{decisionFailNum,jdbcType=INTEGER},
      </if>
      <if test="createdTime != null">
        `created_time` = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        `updated_time` = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where strategy_id = #{strategyId} and biz_date = #{bizDate}
  </update>


  <select id="listByStrategyIdPage" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from stat_strategy_engine_flow_data
    where strategy_id = #{strategyId}
    order by biz_date desc
  </select>

</mapper>