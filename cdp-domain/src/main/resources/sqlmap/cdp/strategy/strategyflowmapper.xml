<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="strategyFlowDoMapper">
  <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyFlowDo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="flow_no" jdbcType="VARCHAR" property="flowNo" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="business_type" jdbcType="VARCHAR" property="businessType" />
    <result column="validity_begin" jdbcType="TIMESTAMP" property="validityBegin" />
    <result column="validity_end" jdbcType="TIMESTAMP" property="validityEnd" />
    <result column="flow_type" jdbcType="SMALLINT" property="flowType" />
    <result column="send_ruler" jdbcType="SMALLINT" property="sendRuler" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="market_channels" jdbcType="VARCHAR" property="marketChannels" />
    <result column="created_op" jdbcType="VARCHAR" property="createdOp" />
    <result column="created_op_mobile" jdbcType="VARCHAR" property="createdOpMobile" />
    <result column="updated_op" jdbcType="VARCHAR" property="updatedOp" />
    <result column="updated_op_mobile" jdbcType="VARCHAR" property="updatedOpMobile" />
    <result column="d_flag" jdbcType="SMALLINT" property="dFlag" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, flow_no, name, business_type, validity_begin, validity_end, flow_type, send_ruler, 
    status, market_channels, created_op, created_op_mobile, updated_op, updated_op_mobile, 
    d_flag, created_time, updated_time
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from strategy_flow
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectByFlowNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from strategy_flow
    where flow_no = #{flowNo} and d_flag = 0
    order by id desc limit 1;
  </select>

  <select id="selectList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from strategy_flow
    where d_flag = 0
    <if test="businessType != null and businessType != ''">
      and business_type = #{businessType}
    </if>
    <if test="name != null and name != ''">
      and name like concat('%',#{name},'%') escape '/'
    </if>
    <if test="id != null">
      and id = #{id}
    </if>
    <if test="updatedOp != null and updatedOp != ''">
      and updated_op = #{updatedOp}
    </if>
    <if test="status != null">
      and status = #{status}
    </if>
    <if test="sendRuler != null">
      and send_ruler = #{sendRuler}
    </if>
    <if test="marketChannel != null">
      and JSON_OVERLAPS(market_channels, #{marketChannel})
    </if>
    <if test="updateStartTime != null">
      and updated_time &gt;= #{updateStartTime}
    </if>
    <if test="updateEndTime != null">
      and updated_time &lt;= #{updateEndTime}
    </if>
    order by updated_time desc
  </select>

  <select id="selectListByStatus" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from strategy_flow
    where d_flag = 0
    and `status` in
    <foreach collection="statusList" index="index" item="status" open="(" separator="," close=")">
      #{status}
    </foreach>
  </select>

  <insert id="insertSelective" keyProperty="id" useGeneratedKeys="true" parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyFlowDo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into strategy_flow
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="flowNo != null">
        flow_no,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="validityBegin != null">
        validity_begin,
      </if>
      <if test="validityEnd != null">
        validity_end,
      </if>
      <if test="flowType != null">
        flow_type,
      </if>
      <if test="sendRuler != null">
        send_ruler,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="marketChannels != null">
        market_channels,
      </if>
      <if test="createdOp != null">
        created_op,
      </if>
      <if test="createdOpMobile != null">
        created_op_mobile,
      </if>
      <if test="updatedOp != null">
        updated_op,
      </if>
      <if test="updatedOpMobile != null">
        updated_op_mobile,
      </if>
      <if test="dFlag != null">
        d_flag,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="updatedTime != null">
        updated_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="flowNo != null">
        #{flowNo,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="validityBegin != null">
        #{validityBegin,jdbcType=TIMESTAMP},
      </if>
      <if test="validityEnd != null">
        #{validityEnd,jdbcType=TIMESTAMP},
      </if>
      <if test="flowType != null">
        #{flowType,jdbcType=SMALLINT},
      </if>
      <if test="sendRuler != null">
        #{sendRuler,jdbcType=SMALLINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=SMALLINT},
      </if>
      <if test="marketChannels != null">
        #{marketChannels,jdbcType=VARCHAR},
      </if>
      <if test="createdOp != null">
        #{createdOp,jdbcType=VARCHAR},
      </if>
      <if test="createdOpMobile != null">
        #{createdOpMobile,jdbcType=VARCHAR},
      </if>
      <if test="updatedOp != null">
        #{updatedOp,jdbcType=VARCHAR},
      </if>
      <if test="updatedOpMobile != null">
        #{updatedOpMobile,jdbcType=VARCHAR},
      </if>
      <if test="dFlag != null">
        #{dFlag,jdbcType=SMALLINT},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        #{updatedTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <update id="updateStatus">
    update strategy_flow
    set status=#{toStatus}
    where id = #{id} and status= #{fromStatus}
  </update>

  <update id="updateByPrimaryKeySelective" parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyFlowDo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update strategy_flow
    <set>
      <if test="flowNo != null">
        flow_no = #{flowNo,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="validityBegin != null">
        validity_begin = #{validityBegin,jdbcType=TIMESTAMP},
      </if>
      <if test="validityEnd != null">
        validity_end = #{validityEnd,jdbcType=TIMESTAMP},
      </if>
      <if test="flowType != null">
        flow_type = #{flowType,jdbcType=SMALLINT},
      </if>
      <if test="sendRuler != null">
        send_ruler = #{sendRuler,jdbcType=SMALLINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=SMALLINT},
      </if>
      <if test="marketChannels != null">
        market_channels = #{marketChannels,jdbcType=VARCHAR},
      </if>
      <if test="createdOp != null">
        created_op = #{createdOp,jdbcType=VARCHAR},
      </if>
      <if test="createdOpMobile != null">
        created_op_mobile = #{createdOpMobile,jdbcType=VARCHAR},
      </if>
      <if test="updatedOp != null">
        updated_op = #{updatedOp,jdbcType=VARCHAR},
      </if>
      <if test="updatedOpMobile != null">
        updated_op_mobile = #{updatedOpMobile,jdbcType=VARCHAR},
      </if>
      <if test="dFlag != null">
        d_flag = #{dFlag,jdbcType=SMALLINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyFlowDo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update strategy_flow
    set flow_no = #{flowNo,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      business_type = #{businessType,jdbcType=VARCHAR},
      validity_begin = #{validityBegin,jdbcType=TIMESTAMP},
      validity_end = #{validityEnd,jdbcType=TIMESTAMP},
      flow_type = #{flowType,jdbcType=SMALLINT},
      send_ruler = #{sendRuler,jdbcType=SMALLINT},
      status = #{status,jdbcType=SMALLINT},
      market_channels = #{marketChannels,jdbcType=VARCHAR},
      created_op = #{createdOp,jdbcType=VARCHAR},
      created_op_mobile = #{createdOpMobile,jdbcType=VARCHAR},
      updated_op = #{updatedOp,jdbcType=VARCHAR},
      updated_op_mobile = #{updatedOpMobile,jdbcType=VARCHAR},
      d_flag = #{dFlag,jdbcType=SMALLINT},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      updated_time = #{updatedTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>