/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.req;

import com.xftech.cdp.api.dto.base.PageRequestDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @version $ PushTemplateReq, v 0.1 2024/1/18 19:27 lingang.han Exp $
 */

@Data
public class PushTemplateReq extends PageRequestDto implements Serializable {
    private static final long serialVersionUID = -6094997775627544883L;

    @ApiModelProperty(value = "模板编号")
    private String templateId;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "模板类型")
    private String tplType;

    @ApiModelProperty(value = "模板类型")
    private String bizType;

    @ApiModelProperty(value = "创建部门")
    private String createdDept;
}