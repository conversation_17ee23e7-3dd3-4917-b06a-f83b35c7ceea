package com.xftech.cdp.domain.event.model.enums;

import lombok.Getter;

import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import java.util.Objects;

/**
 * 数据类型枚举信息
 * <AUTHOR>
 * @version $ DataTypeEnum, v 0.1 2024/11/19 11:08 snail Exp $
 */
@Getter
public enum DataTypeEnum {
    BYTE("byte", Byte.class,"byte"),
    SHORT("short", Short.class,"short"),
    INT("int", Integer.class,"int"),
    LONG("long", Long.class,"long"),
    FLOAT("float", Float.class,"float"),
    DOUBLE("double", Double.class,"double"),
    BOOLEAN("boolean", Boolean.class,"boolean"),
    TIMESTAMP("timestamp", Timestamp.class,"timestamp"),
    DATE("date", Date.class,"date"),
    STRING("string",String.class,"string"),
    BIG_DECIMAL("bigdecimal", BigDecimal.class,"BigDecimal"),;

    DataTypeEnum(String type, Class<?> clazz, String desc){
        this.type = type;
        this.clazz = clazz;
        this.desc = desc;
    }

    public static DataTypeEnum getDataType(String type){
        for (DataTypeEnum dataType : DataTypeEnum.values()){
            if(Objects.equals(type,dataType.type)){
                return dataType;
            }
        }

        return null;
    }

    /** 数据类型 */
    private final String type;
    /** 数据类型对应的Java类 */
    private final Class<?> clazz;
    /** 数据类型描述信息 */
    private final String desc;
}
