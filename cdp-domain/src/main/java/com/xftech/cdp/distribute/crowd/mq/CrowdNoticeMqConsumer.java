package com.xftech.cdp.distribute.crowd.mq;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import com.xinfei.xfframework.common.starter.mq.MqConsumerListener;

import com.xftech.cdp.distribute.crowd.enums.CrowdEffectiveEnum;
import com.xftech.cdp.distribute.crowd.enums.CrowdSliceStatusEnum;
import com.xftech.cdp.distribute.crowd.enums.CrowdStatusEnum;
import com.xftech.cdp.distribute.crowd.model.CrowdNoticeMsgModel;
import com.xftech.cdp.distribute.crowd.repository.CrowdInfoRepository;
import com.xftech.cdp.distribute.crowd.repository.CrowdInfoVersionRepository;
import com.xftech.cdp.distribute.crowd.repository.model.CrowdInfoDo;
import com.xftech.cdp.distribute.crowd.repository.model.CrowdInfoVersionDo;
import com.xftech.cdp.distribute.crowd.service.CrowdSliceService;
import com.xftech.cdp.infra.config.ApolloUtil;
import com.xftech.cdp.infra.utils.DateUtil;
import com.xftech.cdp.infra.utils.VersionUtil;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;

/**
 * 洞察平台-人群变更消息监听
 *
 * <AUTHOR>
 * @version v1.0 2025/5/7
 * @detail https://alidocs.dingtalk.com/i/nodes/AR4GpnMqJzMv4mYlHp3Pxmp4VKe0xjE3?utm_scene=team_space
 */
@Slf4j
@Component
@RocketMQMessageListener(consumerGroup = "cg_xyf_cdp_tp_datainsight_crowdNotice", topic = "tp_datainsight_crowdNotice", consumeMode = ConsumeMode.ORDERLY)
public class CrowdNoticeMqConsumer extends MqConsumerListener<String> {

    @Resource
    private CrowdInfoRepository crowdInfoRepository;
    @Resource
    private CrowdInfoVersionRepository crowdInfoVersionRepository;
    @Resource
    private CrowdSliceService crowdSliceService;

    @Override
    protected void doMessage(String topic, String bodyMessage, MessageExt messageExt) {
        if (ApolloUtil.switchStatus("CrowdNoticeMqConsumerClose")) {
            return;
        }
        log.info("CrowdNoticeMqConsumer doMessage topic={}, messageId={}, bodyMessage={}", topic, messageExt.getMsgId(), bodyMessage);
        try {
            CrowdNoticeMsgModel crowdNoticeMsgModel = JSONObject.parseObject(bodyMessage, CrowdNoticeMsgModel.class);
            switch (crowdNoticeMsgModel.getMessageType()) { // 1=上线;2=下线;3=删除;4=刷新
                case 1:
                    crowOnline(crowdNoticeMsgModel);
                    break;
                case 2:
                    crowOffline(crowdNoticeMsgModel.getCrowdId());
                    break;
                case 3:
                    crowDeleted(crowdNoticeMsgModel.getCrowdId());
                    break;
                case 4:
                    crowRefresh(crowdNoticeMsgModel);
                    break;
                default:
                    log.info("CrowdNoticeMqConsumer doMessage default crowdNoticeMsgModel={}", JSONObject.toJSONString(crowdNoticeMsgModel));
            }
        } catch (DuplicateKeyException e) {
            log.warn("CrowdNoticeMqConsumer doMessage 人群分片任务异常-版本重复", e);
        } catch (Exception e) {
            log.error("CrowdNoticeMqConsumer doMessage error", e);
        }
    }

    /**
     * 上线人群包
     *
     * @param crowdNoticeMsgModel
     */
    private void crowOnline(CrowdNoticeMsgModel crowdNoticeMsgModel) {
        CrowdInfoDo crowdInfoDo = crowdInfoRepository.selectByCrowdId(crowdNoticeMsgModel.getCrowdId());
        if (crowdInfoDo == null) {
            return;
        }

        log.info("CrowdNoticeMqConsumer crowOnline crowdNoticeMsgModel={}", JSONObject.toJSONString(crowdNoticeMsgModel));
        crowdInfoRepository.updateCrowdStatusByCrowdId(crowdNoticeMsgModel.getCrowdId(), CrowdStatusEnum.ONLINE.getStatus(),
                crowdNoticeMsgModel.getEffectStartTime(), crowdNoticeMsgModel.getEffectEndTime(), crowdNoticeMsgModel.isPermanent() ? CrowdEffectiveEnum.PERMANENT.getStatus() : CrowdEffectiveEnum.NO_PERMANENT.getStatus());
    }

    /**
     * 下线人群包
     *
     * @param crowdId
     */
    private void crowOffline(Long crowdId) {
        CrowdInfoDo crowdInfoDo = crowdInfoRepository.selectByCrowdId(crowdId);
        if (crowdInfoDo == null) {
            return;
        }

        log.info("CrowdNoticeMqConsumer crowOffline crowdId={}", crowdId);
        crowdInfoRepository.updateCrowdStatusByCrowdId(crowdId, CrowdStatusEnum.OFFLINE.getStatus(),
                null, null, null);
    }

    /**
     * 删除人群包
     *
     * @param crowdId
     */
    private void crowDeleted(Long crowdId) {
        CrowdInfoDo crowdInfoDo = crowdInfoRepository.selectByCrowdId(crowdId);
        if (crowdInfoDo == null) {
            return;
        }

        log.info("CrowdNoticeMqConsumer crowDeleted crowdId={}", crowdId);
        crowdInfoRepository.updateCrowdStatusByCrowdId(crowdId, CrowdStatusEnum.DELETED.getStatus(),
                null, null, null);
    }

    /**
     * 刷新人群包
     *
     * @param crowdNoticeMsgModel
     */
    private void crowRefresh(CrowdNoticeMsgModel crowdNoticeMsgModel) {
        Long crowdId = crowdNoticeMsgModel.getCrowdId();
        CrowdInfoDo crowdInfoDo = crowdInfoRepository.selectByCrowdId(crowdId);
        if (crowdInfoDo == null) {
            return;
        }

        CrowdInfoVersionDo crowdInfoVersionDo = convertToCrowdInfoVersionDo(crowdNoticeMsgModel);
        CrowdInfoVersionDo maxHistoryCrowdVersionDo = crowdInfoVersionRepository.selectMaxCrowdVersion(crowdId);
        if (maxHistoryCrowdVersionDo != null && VersionUtil.isTodayVersion(maxHistoryCrowdVersionDo.getCrowdVersion())) { // 有历史&当日版本
            crowdInfoVersionDo.setCrowdVersion(maxHistoryCrowdVersionDo.getCrowdVersion() + 1);
        }
        log.info("CrowdNoticeMqConsumer crowRefresh crowdInfoVersionDo={}, maxHistoryCrowdVersionDo={}", JSONObject.toJSONString(crowdInfoVersionDo), JSONObject.toJSONString(maxHistoryCrowdVersionDo));

        // 分片
        crowdSliceService.crowdSlice(crowdInfoVersionDo, "正常刷新分片");
    }

    private CrowdInfoVersionDo convertToCrowdInfoVersionDo(CrowdNoticeMsgModel crowdNoticeMsgModel) {
        CrowdInfoVersionDo crowdInfoVersionDo = new CrowdInfoVersionDo();
        crowdInfoVersionDo.setCrowdId(crowdNoticeMsgModel.getCrowdId());
        crowdInfoVersionDo.setCrowdVersion(Long.parseLong(DateUtil.dayOfInt(new Date()) + "01"));
        crowdInfoVersionDo.setCrowdOssFolder(crowdNoticeMsgModel.getOssPath());
        List<String> ossFiles = crowdNoticeMsgModel.getOssFiles();
        if (CollectionUtils.isNotEmpty(ossFiles)) {
            crowdInfoVersionDo.setCrowdOssFile(String.join(",", ossFiles));
        }
        crowdInfoVersionDo.setCrowdSize(crowdNoticeMsgModel.getCrowdSize());
        crowdInfoVersionDo.setRawVersion(crowdNoticeMsgModel.getRunVersion());
        crowdInfoVersionDo.setSliceStatus(CrowdSliceStatusEnum.INIT.getStatus());
        return crowdInfoVersionDo;
    }

}
