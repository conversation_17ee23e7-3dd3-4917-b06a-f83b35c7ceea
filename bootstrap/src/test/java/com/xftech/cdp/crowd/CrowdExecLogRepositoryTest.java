package com.xftech.cdp.crowd;

import com.xftech.cdp.BaseTest;
import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.req.CrowdRefreshInfoReq;
import com.xftech.cdp.api.dto.resp.CrowdRefreshInfoResp;
import com.xftech.cdp.api.dto.resp.CrowdReportDailyResp;
import com.xftech.cdp.domain.crowd.repository.CrowdDetailRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdExecLogRepository;
import com.xftech.cdp.domain.crowd.repository.ReportDailyCrowdRepository;
import com.xftech.cdp.domain.crowd.service.CrowdPackService;
import com.xftech.cdp.domain.crowd.service.ReportDailyCrowdService;
import com.xftech.cdp.domain.crowd.service.impl.CrowdPushBatchServiceImpl;
import com.xftech.cdp.domain.strategy.model.enums.ReportDailyTypeEnum;
import com.xftech.cdp.domain.strategy.service.ReportDailyTaskService;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdExecLogDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.ReportDailyCrowdDo;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.Array;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * @<NAME_EMAIL>
 */
public class CrowdExecLogRepositoryTest extends BaseTest {

    @Autowired
    private CrowdExecLogRepository crowdExecLogRepository;
    @Autowired
    private ReportDailyCrowdService reportDailyCrowdService;
    @Autowired
    private ReportDailyTaskService reportDailyTaskService;
    @Autowired
    private CrowdPackService crowdPackService;
    @Autowired
    private CrowdPushBatchServiceImpl crowdPushBatchService;

    @Test
    public void findTodayExecLogsByCrowdIdAndStatus() {
    }

    @Test
    public void selectCrowdMaxExecLogIds() {
    }

    @Test
    public void selectExecLogIdsByCrowdIds() {
    }

    @Test
    public void insert() {
    }

    @Test
    public void updateById() {
    }

    @Test
    public void testSelectTodayExecLogByCrowdIds(){
        List<Long> crowdIds = Arrays.asList(55L, 56L, 57L, 322L, 356L);
        List<CrowdExecLogDo> crowdExecLogDos = crowdExecLogRepository.selectExecLogByCrowdIdsAndTime(crowdIds,
                LocalDateTime.of(LocalDate.now(), LocalTime.MIN),
                LocalDateTime.of(LocalDate.now(), LocalTime.MAX));
        System.out.println(crowdExecLogDos);
    }

    @Test
    public void testSaveOrUpdateBatch(){
        ReportDailyCrowdDo reportDailyCrowdDo = new ReportDailyCrowdDo();
        reportDailyCrowdDo.setDate(new Date());
        reportDailyCrowdDo.setCrowdId(112L);
        reportDailyCrowdDo.setCrowdName("testName");
        reportDailyCrowdDo.setExecStartTime(LocalDateTime.now());
        reportDailyCrowdDo.setExecEndTime(LocalDateTime.now());
        reportDailyCrowdDo.setExecStatus(1);
        reportDailyCrowdDo.setFailReason("testFailReason");
        reportDailyCrowdDo.setCrowdTodaySum(999);
        reportDailyCrowdDo.setCrowdYesterdaySum(999);

        ReportDailyCrowdDo reportDailyCrowdDo1 = new ReportDailyCrowdDo();
        reportDailyCrowdDo1.setDate(new Date());
        reportDailyCrowdDo1.setCrowdId(113L);
        reportDailyCrowdDo1.setCrowdName("testName");
        reportDailyCrowdDo1.setExecStartTime(LocalDateTime.now());
        reportDailyCrowdDo1.setExecEndTime(LocalDateTime.now());
        reportDailyCrowdDo1.setExecStatus(1);
        reportDailyCrowdDo1.setFailReason("testFailReason");
        reportDailyCrowdDo1.setCrowdTodaySum(996);
        reportDailyCrowdDo1.setCrowdYesterdaySum(996);
        List<ReportDailyCrowdDo> reportDailyCrowdDos = new ArrayList<>();
        reportDailyCrowdDos.add(reportDailyCrowdDo);
        reportDailyCrowdDos.add(reportDailyCrowdDo1);
        reportDailyCrowdService.saveOrUpdateBatch(reportDailyCrowdDos);
    }

    @Test
    public void testTaskSaveOrUpdate(){
        reportDailyTaskService.saveOrUpdate(ReportDailyTypeEnum.CROWD.getCode());
    }
    @Test
    public void testCrowdReport(){
        crowdPackService.reportDailyCrowd();
    }

    @Test
    public void testCrowdReportLog(){
        crowdPushBatchService.pushCrowdListLog();
    }
    @Test
    public void testQuery(){
        CrowdReportDailyResp crowdReportDailyResp = crowdPackService.queryReportDailyList();
        System.out.println(crowdReportDailyResp);
    }

    @Test
    public void testQueryCrowdId(){
        PageResultResponse<CrowdRefreshInfoResp> crowdRefreshInfoRespPageResultResponse = crowdPackService.crowdRefreshInfo(new CrowdRefreshInfoReq(356L));
        System.out.println(crowdRefreshInfoRespPageResultResponse);
    }

}