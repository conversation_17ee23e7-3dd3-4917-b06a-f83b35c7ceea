package com.xftech.cdp.api.dto.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xftech.cdp.api.dto.base.TimeFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/2/14
 */
@Setter
@Getter
public class CrowdListResp extends BaseResp {

    @ApiModelProperty("id")
    public Integer id;

    @ApiModelProperty(value = "人群名称")
    public String crowdName;

    @ApiModelProperty(value = "人群数量")
    public Integer crowdPersonNum;

    @Min(0)
    @Max(1)
    @ApiModelProperty(value = "筛选方式,0:文件上传,1:标签圈选")
    public Integer filterMethod;

    @Min(0)
    @Max(1)
    @ApiModelProperty(value = "刷新机制,0:手动刷新，1:例行刷新")
    public Integer refreshType;

    @ApiModelProperty("状态，0: 初始化、1: 刷新中、2: 刷新成功、3: 刷新失败、4: 暂停中、5: 已完成")
    public Integer status;

    @ApiModelProperty("更新人")
    public String updateOp;

    @ApiModelProperty("刷新时间")
    @JsonFormat(pattern = TimeFormat.TIME)
    private LocalTime refreshTime;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    private LocalDateTime createdTime;

    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    private LocalDateTime updatedTime;

    @ApiModelProperty("最近刷新时间")
    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    private LocalDateTime latestRefreshTime;

    @ApiModelProperty("人群包执行sql")
    private CrowdExecSqlResp execSql;

    @ApiModelProperty("关联策略id")
    private List<StrategyResp> strategyIds;

    @ApiModelProperty(value = "人群分组")
    private Integer groupType;


    @Data
    public static class StrategyResp{
        private Long strategyId;

        private Integer status;


    }
}
