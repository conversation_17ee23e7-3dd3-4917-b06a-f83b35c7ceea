package com.xftech.cdp.api.dto.resp.external;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xftech.cdp.api.dto.base.TimeFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> xugong<PERSON>@mzsk.com
 * @date 2023-07-21 14:54
 */
@Data
public class StrategyListResp {
    /**
     * 业务线
     */
    private String businessType;
    /**
     * 策略ID
     */
    private Long strategyId;
    /**
     * 策略类型
     */
    private Integer strategyType;
    /**
     * 策略名称
     */
    private String strategyName;
    /**
     * 策略状态
     */
    private Integer strategyStatus;
    /**
     * 有效期结束时间
     */
    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    private LocalDateTime validityEndTime;
    /**
     * 更新人
     */
    private String updateOp;

    private LocalDateTime updatedTime;


}
