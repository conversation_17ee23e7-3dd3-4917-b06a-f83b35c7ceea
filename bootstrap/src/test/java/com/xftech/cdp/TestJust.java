package com.xftech.cdp;

import cn.xinfei.usergroup.random.client.AbClient;
import com.xftech.cdp.domain.strategy.model.dto.AdbRealTimeVariableGrayConfig;
import com.xftech.cdp.domain.strategy.model.enums.StrategyRulerEnum;
import com.xftech.cdp.infra.utils.DateUtil;
import com.xftech.cdp.infra.utils.JsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;

import java.net.StandardSocketOptions;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Date;

public class TestJust {

    @Test
    public void test(){
        AdbRealTimeVariableGrayConfig.StrategyGrayConfig grayConfig = new AdbRealTimeVariableGrayConfig.StrategyGrayConfig();
        grayConfig.setAllHits(true);

        AdbRealTimeVariableGrayConfig.VariableGrayConfig variableGrayConfig = new AdbRealTimeVariableGrayConfig.VariableGrayConfig();
        variableGrayConfig.setAllHits(false);
        variableGrayConfig.setVariableList(Arrays.asList("user_cur_available_balance"));

        System.out.println( JsonUtil.toJson(grayConfig));
        System.out.println(JsonUtil.toJson(variableGrayConfig));


        System.out.println(StringUtils.equalsIgnoreCase("null", "NULL"));
    }

    @Test
    public void test2() {
        //AbClient.ab("PayoffGrayscale", "233", true);
        System.out.println(String.format("sss:%s", null));
    }

    @Test
    public void compare(){
        // Parses the date
        LocalDateTime dt1
                = LocalDateTime.parse("2018-11-02T12:45:30");

        // Prints the date
        System.out.println(dt1);

        // Parses the date
        LocalDateTime dt2
                = LocalDateTime.parse("2018-11-03T12:45:30");

        // Prints the date
        System.out.println(dt2);

        // Compares both dates
        System.out.println(dt1.isBefore(dt2));
    }

    @Test
    public void t1(){
        System.out.println(DateUtil.dayOfWeek(new Date()));
        System.out.println(DateUtil.dayOfMonth(new Date()));

        String dayNumber = LocalDate.now().format(DateTimeFormatter.ofPattern("d"));
        String reqDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        System.out.println("dayNumber = " + dayNumber);
        System.out.println("reqDate = " + reqDate);
    }
}
