package com.xftech.cdp.domain.touch.model;

import lombok.Data;
import java.util.Map;

/**
 * 用户触达结果模型
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-23
 */
@Data
public class TouchUserResult {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 触达状态
     */
    private TouchStatus status;
    
    /**
     * 批次号
     */
    private String batchNo;
    
    /**
     * 错误码
     */
    private String errorCode;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 处理时间戳
     */
    private Long timestamp;
    
    /**
     * 结果数据（如：第三方返回的消息ID等）
     */
    private Map<String, Object> resultData;
    
    /**
     * 创建成功结果
     */
    public static TouchUserResult success(Long userId, String batchNo) {
        TouchUserResult result = new TouchUserResult();
        result.setUserId(userId);
        result.setStatus(TouchStatus.SUCCESS);
        result.setBatchNo(batchNo);
        result.setTimestamp(System.currentTimeMillis());
        return result;
    }
    
    /**
     * 创建成功结果（带结果数据）
     */
    public static TouchUserResult success(Long userId, String batchNo, Map<String, Object> resultData) {
        TouchUserResult result = success(userId, batchNo);
        result.setResultData(resultData);
        return result;
    }
    
    /**
     * 创建失败结果
     */
    public static TouchUserResult failed(Long userId, String batchNo, String errorCode, String errorMessage) {
        TouchUserResult result = new TouchUserResult();
        result.setUserId(userId);
        result.setStatus(TouchStatus.FAILED);
        result.setBatchNo(batchNo);
        result.setErrorCode(errorCode);
        result.setErrorMessage(errorMessage);
        result.setTimestamp(System.currentTimeMillis());
        return result;
    }
    
    /**
     * 创建流控结果
     */
    public static TouchUserResult flowControlled(Long userId, String batchNo, String reason) {
        TouchUserResult result = new TouchUserResult();
        result.setUserId(userId);
        result.setStatus(TouchStatus.FLOW_CONTROLLED);
        result.setBatchNo(batchNo);
        result.setErrorMessage(reason);
        result.setTimestamp(System.currentTimeMillis());
        return result;
    }
    
    /**
     * 创建处理中结果
     */
    public static TouchUserResult pending(Long userId, String batchNo) {
        TouchUserResult result = new TouchUserResult();
        result.setUserId(userId);
        result.setStatus(TouchStatus.PENDING);
        result.setBatchNo(batchNo);
        result.setTimestamp(System.currentTimeMillis());
        return result;
    }
    
    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return TouchStatus.SUCCESS.equals(status);
    }
    
    /**
     * 判断是否失败
     */
    public boolean isFailed() {
        return TouchStatus.FAILED.equals(status);
    }
    
    /**
     * 判断是否被流控
     */
    public boolean isFlowControlled() {
        return TouchStatus.FLOW_CONTROLLED.equals(status);
    }
    
    /**
     * 判断是否处理中
     */
    public boolean isPending() {
        return TouchStatus.PENDING.equals(status);
    }
    
    /**
     * 获取结果数据
     */
    public Object getResultData(String key) {
        return resultData != null ? resultData.get(key) : null;
    }
    
    /**
     * 设置结果数据
     */
    public void setResultData(String key, Object value) {
        if (resultData == null) {
            resultData = new java.util.HashMap<>();
        }
        resultData.put(key, value);
    }
    
    /**
     * 添加结果数据
     */
    public void addResultData(Map<String, Object> data) {
        if (data != null) {
            if (resultData == null) {
                resultData = new java.util.HashMap<>();
            }
            resultData.putAll(data);
        }
    }
    
    /**
     * 转换为字符串（用于日志）
     */
    @Override
    public String toString() {
        return String.format("TouchUserResult{userId=%d, status=%s, batchNo=%s, errorCode=%s}", 
                           userId, status, batchNo, errorCode);
    }
}
