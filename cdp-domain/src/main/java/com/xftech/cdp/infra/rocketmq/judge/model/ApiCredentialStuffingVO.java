package com.xftech.cdp.infra.rocketmq.judge.model;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version $ apiCredentialStuffingVO, v 0.1 2025/3/14 16:05 tianshuo.qiu Exp $
 */
@Data
public class ApiCredentialStuffingVO {
    /**
     * 撞库时间
     */
    private Date check_time;
    /**
     * 渠道撞库inner-app
     */
    private String inner_app;
    /**
     * userNo credit.id
     */
    private Long user_no;
    /**
     * 撞库类型 1-手机号md5，2-身份证号md5，3-手机号+身份证号md5
     */
    private Integer check_type;
    private String cust_no;
    private String app;
    /**
     * 撞库结果,0：撞库拒绝，1：撞库通过
     */
    private Integer check_result;
    /**
     * 是否有放款成功记录，0-无，1-有；
     */
    private Integer has_loan_succ_record;

    /**
     * 数据来源，1-API渠道实时请求，2-离线撞库
     */
    private Integer data_source;

}
