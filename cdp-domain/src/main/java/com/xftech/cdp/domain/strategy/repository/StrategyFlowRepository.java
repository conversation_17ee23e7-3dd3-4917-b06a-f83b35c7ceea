/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.domain.strategy.model.strategy.FlowListQueryBO;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyFlowDo;
import com.xftech.cdp.infra.utils.JsonUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @version $ StrategyFlowRepository, v 0.1 2023/12/13 13:54 yye.xu Exp $
 */

@Repository
@AllArgsConstructor
public class StrategyFlowRepository {
    public void insert(StrategyFlowDo strategyFlowDo) {
        if (strategyFlowDo == null) {
            return;
        }
        DBUtil.insert("strategyFlowDoMapper.insertSelective", strategyFlowDo);
    }

    public void updateSelective(StrategyFlowDo strategyFlowDo) {
        if (strategyFlowDo == null) {
            return;
        }
        DBUtil.insert("strategyFlowDoMapper.updateByPrimaryKeySelective", strategyFlowDo);
    }

    public StrategyFlowDo select(String flowNo) {
        Map<String, Object> param = new HashMap<>();
        param.put("flowNo", flowNo);
        return DBUtil.selectOne("strategyFlowDoMapper.selectByFlowNo", param);
    }

    public List<StrategyFlowDo> selectList(FlowListQueryBO flowListQueryBO) {
        Map<String, Object> param = JsonUtil.toMap(JsonUtil.toJson(flowListQueryBO));
        if (CollectionUtils.isEmpty(flowListQueryBO.getMarketChannel())) {
            param.put("marketChannel", null);
        }else {
            param.put("marketChannel", JsonUtil.toJson(flowListQueryBO.getMarketChannel()));
        }
        return DBUtil.selectList("strategyFlowDoMapper.selectList", param);
    }

    public List<StrategyFlowDo> selectList(List<Integer> statusList){
        // selectListByStatus
        Map<String, Object> param = new HashMap<>();
        param.put("statusList", statusList);
        return DBUtil.selectList("strategyFlowDoMapper.selectListByStatus", param);
    }

    public int updateStatus(Long id, Integer fromStatus, Integer toStatus) {
        Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        param.put("fromStatus", fromStatus);
        param.put("toStatus", toStatus);
        return DBUtil.update("strategyFlowDoMapper.updateStatus", param);
    }
}