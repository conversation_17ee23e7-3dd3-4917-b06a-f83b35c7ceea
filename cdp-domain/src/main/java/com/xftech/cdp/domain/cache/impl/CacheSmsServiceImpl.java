package com.xftech.cdp.domain.cache.impl;

import com.xftech.cdp.domain.sms.SmsService;
import com.xftech.cdp.domain.cache.CacheSmsService;
import com.xftech.cdp.infra.client.sms.model.resp.SmsItem;
import com.xftech.cdp.infra.constant.RedisKeyConstants;
import com.xftech.cdp.infra.utils.RedisUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CacheSmsServiceImpl implements CacheSmsService {

    @Autowired
    private SmsService smsService;
    @Autowired
    private RedisUtils redisUtils;

    public SmsItem queryTemplateDetail(String templateId, String app) {
        String redisKey = String.format(RedisKeyConstants.SINGLE_TEMPLATE, app, templateId);
        SmsItem smsItem = redisUtils.get(redisKey, SmsItem.class);
        if (smsItem == null) {
            smsItem = smsService.queryTemplateDetail(templateId, app);
            redisUtils.set(redisKey, smsItem, RedisUtils.DEFAULT_EXPIRE_SECONDS * 10);
        }
        return smsItem;
    }
}
