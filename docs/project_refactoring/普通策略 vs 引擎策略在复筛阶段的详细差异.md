# 普通策略 vs 引擎策略在复筛阶段的详细差异分析

## 1. 整体流程差异

### 1.1 修正后的流程图

```mermaid
graph TD
    A[延迟队列消息] --> B[rescreen方法入口]
    B --> C{策略类型判断}
    C -->|引擎策略| D[rescreenWithEngine]
    C -->|普通策略| E[queryLabelHandler]
    D --> F[构建引擎请求参数]
    F --> G[调用决策引擎]
    G --> H[解析引擎决策结果]
    H --> I{引擎决策成功?}
    I -->|成功| J[引擎内部queryLabelHandler]
    I -->|失败| K[引擎决策失败处理]
    J --> L[引擎内部rescreeningHandler]
    L --> M{复筛结果}
    M -->|成功| N[引擎直接调用marketingSend]
    M -->|失败| O[复筛失败但继续营销]
    E --> P[普通策略queryLabelHandler]
    P --> Q[普通策略rescreeningHandler]
    Q --> R{复筛结果}
    R -->|成功| S[dispatchHandler触达分发]
    R -->|失败| T[记录失败原因]
    S --> U[投递到触达队列]
    U --> V[dispatch方法处理]
    V --> W[最终调用execSend]
```

## 2. 核心差异分析

### 2.1 执行时机差异

| 维度 | 普通策略 | 引擎策略 |
|------|----------|----------|
| **queryLabelHandler调用时机** | 复筛阶段开始时立即调用 | 引擎决策成功后才调用 |
| **rescreeningHandler调用时机** | queryLabelHandler之后调用 | 引擎决策成功后才调用 |
| **失败处理方式** | 复筛失败直接终止流程 | 复筛失败仍继续执行营销动作 |
| **触达方式** | 通过MQ队列异步触达，最终调用execSend | 直接同步调用marketingSend |

### 2.2 代码位置对比

**普通策略流程**:
```java
// 代码位置: StrategyEventDispatchServiceImpl.rescreen() 第503-510行
// 实时标签查询
this.queryLabelHandler(eventContext);
// 策略-复筛
this.rescreeningHandler(eventContext);
// 触达
if (StrategyTypeEnum.getInstance(event.getStrategyType()) == StrategyTypeEnum.EVENT) {
    this.dispatchHandler(eventContext);  // 投递到MQ队列
}
```

**引擎策略流程**:
```java
// 代码位置: StrategyEventDispatchServiceImpl.rescreenWithEngine() 第605-608行
try {
    // 实时标签筛选
    this.queryLabelHandler(eventContext);
    // 策略-复筛
    this.rescreeningHandler(eventContext);
} catch (Exception ex) {
    // 复筛失败仍继续执行营销动作
    log.info("策略引擎版本复筛失败,开始营销触达动作...");
}
// 直接执行营销动作
int sendRet = marketingSend(dispatchDto, crowdDetailDo, strategyMarketChannelEnum,
                           action.getGroup_id(), dispatch.getDetail_info(), eventCopy);
```

## 3. 分层逻辑差异

### 3.1 普通策略分层逻辑

```
1. rescreen() 入口
   ├── queryLabelHandler() - 实时标签查询
   ├── rescreeningHandler() - 策略复筛
   └── dispatchHandler() - 触达分发
       ├── userGroupMatch() - 用户分组匹配
       ├── flowCtrl() - 流控检查
       └── mqProducerService.channelDelivery() - MQ投递
           └── 异步处理
               └── dispatch() 方法
                   └── execSend() 方法
                       └── EventDispatchService各渠道方法
                           ├── sendSmsEvent() - 短信发送
                           ├── sendTeleEvent() - 电销发送
                           ├── sendPushEvent() - 推送发送
                           ├── sendCouponEvent() - 优惠券发送
                           └── sendAiProntoEvent() - AI外呼发送
```

### 3.2 引擎策略分层逻辑

```
1. rescreen() 入口
   └── rescreenWithEngine() - 引擎策略处理
       ├── modelPlatformService.prediction() - 调用决策引擎
       ├── 解析引擎决策结果
       ├── queryLabelHandler() - 实时标签查询（引擎决策后）
       ├── rescreeningHandler() - 策略复筛（引擎决策后）
       └── 直接循环调用 marketingSend() - 同步营销触达
```

## 4. 外部接口差异

### 4.1 普通策略外部接口调用

| 接口类型 | 调用位置 | 调用方式 | 作用 |
|----------|----------|----------|------|
| **ADS数仓/特征平台** | queryLabelHandler | 同步调用 | 查询实时标签 |
| **RabbitMQ** | dispatchHandler | 异步投递 | 触达队列投递 |
| **外部营销服务** | dispatch→execSend | 异步调用 | 最终营销触达 |

### 4.2 引擎策略外部接口调用

| 接口类型 | 调用位置 | 调用方式 | 作用 |
|----------|----------|----------|------|
| **决策引擎** | rescreenWithEngine | 同步调用 | 获取营销决策 |
| **ADS数仓/特征平台** | queryLabelHandler | 同步调用 | 查询实时标签 |
| **外部营销服务** | rescreenWithEngine | 同步调用 | 直接营销触达 |

## 5. 请求逻辑差异

### 5.1 普通策略请求逻辑

```java
// 1. 标签查询请求
// 代码位置: StrategyEventDispatchServiceImpl.queryLabelHandler() 第835行
Map<Long, Map<String, Object>> labelValueMap = adsStrategyLabelService.query(
    bizEventVO, labelNameToList.keySet(), StrategyInstantLabelTypeEnum.LABEL);

// 2. 复筛表达式计算
// 代码位置: StrategyEventDispatchServiceImpl.labelCheck() 第920-921行
String expression = list.stream()
    .map(StrategyMarketEventConditionDo::getExpression)
    .collect(Collectors.joining(" && "));
Boolean result = AviatorUtil.compute(expression, labelValueMap);

// 3. 触达队列投递
// 代码位置: StrategyEventDispatchServiceImpl.dispatchHandler() 第968行
mqProducerService.channelDelivery(bizEventVO);

// 4. 异步触达处理
// 代码位置: StrategyEventDispatchServiceImpl.dispatch() 第1040行
// dispatch() 方法异步处理触达逻辑
```

### 5.2 引擎策略请求逻辑

```java
// 1. 引擎决策请求
// 代码位置: StrategyEventDispatchServiceImpl.rescreenWithEngine() 第566-590行
ModelPredictionReq modelPredictionReq = ModelPredictionReq.builder()
    .model_name(event.getEngineCode())
    .biz_data(ModelPredictionReq.BizData.builder()
            .requestId(event.getMessageId())
            .app(event.getApp())
            .biz_type(event.getBizEventType())
            .mobile(event.getMobile())
            .app_user_id(event.getAppUserId())
            .user_no(event.getAppUserId())
            .trigger_datetime(DateUtil.getMills(event.getTriggerDatetime()))
            .timestamp(new Date().getTime())
            .callerCount(event.getEngineCallerCount())
            .device_id(event.getDeviceId())
            .extMap(MapUtils.isNotEmpty(event.getExt()) ? event.getExt() : Maps.newHashMap())
            .requestType("ONLINE")
            .build())
    .build();

// 代码位置: StrategyEventDispatchServiceImpl.rescreenWithEngine() 第595行
JSONObject resp = modelPlatformService.prediction(modelPredictionReq);

// 2. 解析引擎决策结果
// 代码位置: StrategyEventDispatchServiceImpl.rescreenWithEngine() 第598-599行
Object decisionVal = resp.getJSONObject("data").getJSONObject("output_data").get(event.getEngineCode());
PredictDecisionDto predictDecisionDto = JsonUtil.parse(JsonUtil.toJson(decisionVal), PredictDecisionDto.class);

// 3. 标签查询请求（引擎决策后）
// 代码位置: StrategyEventDispatchServiceImpl.rescreenWithEngine() 第606行
this.queryLabelHandler(eventContext);

// 4. 复筛表达式计算（引擎决策后）
// 代码位置: StrategyEventDispatchServiceImpl.rescreenWithEngine() 第608行
this.rescreeningHandler(eventContext);

// 5. 直接营销触达（同步）
// 代码位置: StrategyEventDispatchServiceImpl.rescreenWithEngine() 第623-655行
List<PredictDecisionDto.DecisionData.Action> actions = predictDecisionDto.getDecisionData().getActions();
for (PredictDecisionDto.DecisionData.Action action : actions) {
    List<PredictDecisionDto.DecisionData.Action.Dispatch> dispatches = action.getOrderedDispatch();
    for (PredictDecisionDto.DecisionData.Action.Dispatch dispatch : dispatches) {
        StrategyMarketChannelEnum strategyMarketChannelEnum = StrategyMarketChannelEnum.getInstance(dispatch.getType());
        int sendRet = marketingSend(dispatchDto, crowdDetailDo, strategyMarketChannelEnum,
                                   action.getGroup_id(), dispatch.getDetail_info(), eventCopy);
    }
}
```

## 6. 数据流转差异

### 6.1 普通策略数据流转

```mermaid
graph LR
    A[BizEventVO] --> B[queryLabelHandler]
    B --> C[strategy_market_event_condition表]
    C --> D[ADS数仓/特征平台]
    D --> E[标签数据Map]
    E --> F[rescreeningHandler]
    F --> G[Aviator表达式计算]
    G --> H[复筛结果]
    H --> I[dispatchHandler]
    I --> J[strategy_group表]
    J --> K[strategy_market_channel表]
    K --> L[RabbitMQ队列]
    L --> M[异步dispatch处理]
    M --> N[execSend]
```

### 6.2 引擎策略数据流转

```mermaid
graph LR
    A[BizEventVO] --> B[rescreenWithEngine]
    B --> C[决策引擎接口]
    C --> D[引擎决策结果]
    D --> E[queryLabelHandler]
    E --> F[ADS数仓/特征平台]
    F --> G[标签数据Map]
    G --> H[rescreeningHandler]
    H --> I[Aviator表达式计算]
    I --> J[复筛结果]
    J --> K[引擎Action列表]
    K --> L[直接marketingSend]
```

## 7. 表结构使用差异

### 7.1 普通策略涉及的表

| 表名 | 使用阶段 | 作用 |
|------|----------|------|
| `strategy_market_event_condition` | queryLabelHandler | 标签查询配置 |
| `strategy_instant_label` | queryLabelHandler | 标签元数据 |
| `strategy_group` | dispatchHandler | 用户分组配置 |
| `strategy_market_channel` | dispatchHandler | 渠道配置 |
| `flow_ctrl` | dispatchHandler | 流控规则 |
| `user_dispatch_detail_{YYYYMM}` | dispatchHandler | 触达记录 |

### 7.2 引擎策略涉及的表

| 表名 | 使用阶段 | 作用 |
|------|----------|------|
| `strategy_market_event_condition` | queryLabelHandler | 标签查询配置 |
| `strategy_instant_label` | queryLabelHandler | 标签元数据 |
| `strategy` | rescreenWithEngine | 策略配置（触达时间判定） |
| **不涉及** | - | 不使用strategy_group表（引擎返回分组） |
| **不涉及** | - | 不使用strategy_market_channel表（引擎返回渠道） |

## 8. 异常处理差异

### 8.1 普通策略异常处理

```java
// 代码位置: StrategyEventDispatchServiceImpl.rescreen() 第515-525行
} catch (Exception be) {
    log.warn("未通过复筛,用户:{}, e:", event.getAppUserId(), be);
    DecisionResultEnum decisionResultEnum = DecisionResultEnum.REPEAT_FILTER_FAIL;
    if (StringUtils.isNotBlank(be.getMessage())) {
        decisionResultEnum.setFailReason(CharSequenceUtil.sub(be.getMessage(), 0, 150));
    }
    if (eventContext.getBizEventVO().getFailCode() == null) {
        eventContext.getBizEventVO().addDecisionFailResult(decisionResultEnum);
    }
    rescreenFailProcess(eventContext);  // 发送失败消息，终止流程
}
```

**特点**: 复筛失败直接终止，不会进行营销触达

### 8.2 引擎策略异常处理

```java
// 代码位置: StrategyEventDispatchServiceImpl.rescreenWithEngine() 第609-620行
} catch (Exception ex) {
    log.info("策略引擎版本复筛失败,开始营销触达动作,事件id:{},策略id:{},用户id:{},引擎code:{},错误内容:{}",
             event.getMessageId(), event.getStrategyId(), event.getAppUserId(), event.getEngineCode(), ex.toString());
    if (isReDecision) {
        reDecisionService.updateReDecisionResult(event.getEngineReDecisionId(), ReDecisionResultStatus.TAG_NOT_HIT.getStatus());
    } else {
        if (eventContext.getBizEventVO().getFailCode() == null) {
            eventContext.getBizEventVO().addDecisionFailResult(DecisionResultEnum.REPEAT_FILTER_FAIL.setFailReason(CharSequenceUtil.sub(ex.getMessage(), 0, 150)));
        }
        rescreenFailProcess(eventContext);
    }
    return;  // 仅记录失败，但继续执行营销动作
}
// 继续执行营销触达逻辑...
```

**特点**: 复筛失败仍继续执行营销动作，体现引擎决策的优先级

## 9. 性能和资源消耗差异

### 9.1 普通策略
- **优点**: 异步处理，不阻塞主流程
- **缺点**: 需要额外的MQ资源，处理延迟较高
- **适用场景**: 大批量用户处理，对实时性要求不高

### 9.2 引擎策略
- **优点**: 同步处理，实时性高，引擎决策优先级高
- **缺点**: 阻塞主流程，对引擎服务依赖性强
- **适用场景**: 高价值用户，需要实时决策的场景

## 10. 监控和日志差异

### 10.1 普通策略监控点
```java
// 复筛阶段
log.info("实时策略-复筛开始,事件:{},用户ID:{},消息ID:{}", ...);
// 触达队列投递
log.info("策略分组-满足所有条件,调用触达队列...");
// 异步触达处理
log.info("实时策略-触达开始,事件:{},用户ID:{},消息ID:{}", ...);
```

### 10.2 引擎策略监控点
```java
// 引擎决策调用
log.info("策略引擎实时策略-复筛开始,事件:{},用户ID:{},消息ID:{},引擎名称:{}", ...);
// 引擎决策结果
log.info("策略引擎版本,开始进行实时标签筛选,事件id:{},策略id:{},用户id:{},引擎code:{}", ...);
// 直接营销触达
log.info("策略引擎版本,开始营销触达动作,事件id:{},策略id:{},用户id:{},营销组:{}", ...);
// 性能监控
log.info("rescreenWithEngine cost={}, strategyId={}, userNo={}", stopwatch.elapsed(TimeUnit.MILLISECONDS), ...);
```

## 11. 决策引擎接口详细分析

### 11.1 引擎请求参数构建

**代码位置**: `StrategyEventDispatchServiceImpl.rescreenWithEngine()` 第566-590行

```java
ModelPredictionReq modelPredictionReq = ModelPredictionReq.builder()
        .model_name(event.getEngineCode())
        .biz_data(ModelPredictionReq.BizData.builder()
                .requestId(event.getMessageId())
                .app(event.getApp())
                .biz_type(event.getBizEventType())
                .mobile(event.getMobile())
                .app_user_id(event.getAppUserId())
                .user_no(event.getAppUserId())
                .trigger_datetime(DateUtil.getMills(event.getTriggerDatetime()))
                .timestamp(new Date().getTime())
                .callerCount(event.getEngineCallerCount())
                .device_id(event.getDeviceId())
                .extMap(MapUtils.isNotEmpty(event.getExt()) ? event.getExt() : Maps.newHashMap())
                .requestType("ONLINE")
                .build())
        .build();
```

### 11.2 引擎响应结构

```java
public class PredictDecisionDto {
    private boolean isSucced;           // 决策是否成功
    private boolean ifMarket;           // 是否进行营销
    private boolean isDelay;            // 是否延迟决策
    private long delaySeconds;          // 延迟秒数
    private DecisionData decisionData;  // 决策数据

    public static class DecisionData {
        private List<Action> actions;   // 营销动作列表

        public static class Action {
            private String group_id;        // 分组ID
            private String group_source;    // 分组来源
            private List<Dispatch> dispatch; // 渠道分发列表

            public static class Dispatch {
                private Integer type;           // 渠道类型
                private Map<String, Object> detail_info; // 渠道详细信息
            }
        }
    }
}
```

### 11.3 引擎决策结果处理

```java
// 1. 延迟决策处理
if (predictDecisionDto.isDelay()) {
    log.info("引擎决策结果:延迟,引擎名称:{},用户ID:{},策略ID:{},延迟时间:{}s",
             event.getEngineCode(), event.getAppUserId(), event.getStrategyId(), predictDecisionDto.getDelaySeconds());
    // 重新放入延迟队列
    mqProducerService.bizEventDelay(event, predictDecisionDto.getDelaySeconds(), StrategyTypeEnum.EVENT_ENGINE);
    return;
}

// 2. 营销标识设置
event.setIfMarket(predictDecisionDto.ifMarket());

// 3. 营销动作执行
List<PredictDecisionDto.DecisionData.Action> actions = predictDecisionDto.getDecisionData().getActions();
for (PredictDecisionDto.DecisionData.Action action : actions) {
    List<PredictDecisionDto.DecisionData.Action.Dispatch> dispatches = action.getOrderedDispatch();
    for (PredictDecisionDto.DecisionData.Action.Dispatch dispatch : dispatches) {
        StrategyMarketChannelEnum strategyMarketChannelEnum = StrategyMarketChannelEnum.getInstance(dispatch.getType());
        int sendRet = marketingSend(dispatchDto, crowdDetailDo, strategyMarketChannelEnum,
                                   action.getGroup_id(), dispatch.getDetail_info(), eventCopy);
    }
}
```

## 12. 配置和依赖差异

### 12.1 普通策略配置依赖

**Apollo配置**:
```properties
# 标签查询相关
ads.feature.need.idcardnumber = ["idcard_feature1", "idcard_feature2"]
ads.feature.need.custno = ["cust_feature1", "cust_feature2"]
ads.feature.replace.config = ["last_login_time", "register_time"]

# 流控相关
singleDispatchFlc.1=true    # 短信触达流控开关
singleDispatchFlc.2=true    # 电销触达流控开关

# 特征平台灰度
strategyDataFeatureSwitch.{策略ID} = {"percentNum": 50, "whitelist": "用户ID列表"}
```

**数据库依赖**:
- 主要依赖CDP数据库中的策略配置表
- 需要查询分组和渠道配置

### 12.2 引擎策略配置依赖

**Apollo配置**:
```properties
# 决策引擎地址
xf.enginepredictcenter.url = http://enginepredictcenter.xinfei.io

# 引擎参数开关
addStrategyIdParamEnable = true

# 标签查询相关（与普通策略相同）
ads.feature.need.idcardnumber = ["idcard_feature1", "idcard_feature2"]
strategyDataFeatureSwitch.{策略ID} = {"percentNum": 50, "whitelist": "用户ID列表"}
```

**数据库依赖**:
- 主要依赖业务引擎数据库: `**********************************************************************************`
- 不依赖分组和渠道配置表（由引擎返回）

## 13. 总结

### 13.1 核心差异总结

| 维度 | 普通策略 | 引擎策略 |
|------|----------|----------|
| **架构模式** | 异步队列模式 | 同步直调模式 |
| **决策来源** | 基于配置规则 | 基于AI决策引擎 |
| **容错机制** | 复筛失败即终止 | 复筛失败仍继续营销 |
| **性能特点** | 吞吐量高但延迟大 | 实时性高但资源消耗大 |
| **适用场景** | 批量营销 | 精准实时营销 |
| **分组渠道来源** | 数据库配置 | 引擎决策返回 |
| **流控位置** | dispatch阶段(execSend前) | marketingSend阶段 |
| **监控复杂度** | 相对简单 | 需要监控引擎调用 |

### 13.2 设计理念

这种设计体现了系统的灵活性和扩展性：

1. **传统规则驱动**: 普通策略支持基于业务规则的批量营销
2. **AI智能驱动**: 引擎策略支持基于机器学习的精准营销
3. **混合架构**: 两种模式可以并存，满足不同业务场景需求
4. **渐进式演进**: 可以从普通策略逐步迁移到引擎策略

### 13.3 选择建议

**选择普通策略的场景**:
- 营销规则相对固定
- 需要处理大批量用户
- 对实时性要求不高
- 系统资源有限

**选择引擎策略的场景**:
- 需要个性化营销决策
- 高价值用户精准营销
- 对实时性要求高
- 有充足的计算资源

这种差异化设计使得系统能够在保证稳定性的同时，支持更加智能和灵活的营销策略。

## 14. 重要澄清：execSend与marketingSend的关系

### 14.1 独立性说明
**重要提醒**：根据代码分析，`execSend`方法和`marketingSend`方法是**完全独立**的两个执行路径：

- **普通策略链路**: `rescreen` → `dispatchHandler` → `MQ投递` → `dispatch` → `execSend` → `EventDispatchService各渠道方法`
- **引擎策略链路**: `rescreenWithEngine` → `引擎决策` → `marketingSend` → `直接调用外部服务`

### 14.2 关键差异
| 方法 | execSend | marketingSend |
|------|----------|---------------|
| **调用场景** | T0-普通触达 | T0-引擎触达 |
| **参数来源** | 策略配置表 | 引擎决策结果 |
| **外部调用方式** | 通过EventDispatchService适配层 | 直接构建外部服务参数 |
| **流控方式** | 预先流控检查 | 分布式锁流控 |

**execSend绝不会调用marketingSend**，它们服务于不同的业务场景，体现了良好的职责分离和架构设计原则。