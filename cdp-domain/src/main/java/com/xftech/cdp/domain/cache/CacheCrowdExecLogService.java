package com.xftech.cdp.domain.cache;

import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdExecLogDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPackDo;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

public interface CacheCrowdExecLogService {
    List<Map<String, Object>> selectCrowdMaxExecLogIds(List<Long> crowdIds);

    boolean updateById(CrowdExecLogDo crowdExecLog);

    void saveBatchCrowdExeLog(List<CrowdPackDo> res, LocalDateTime finishExecTime);

    List<Map<String, Object>> selectCrowdMaxExecLogTime(List<Long> crowdIds);
}
