package com.xftech.cdp.domain.marketing.service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.xftech.cdp.api.dto.config.ActBroadcastVo;
import com.xftech.cdp.api.dto.marketing.request.ActivityBaseRequest;
import com.xftech.cdp.api.dto.marketing.request.RegisterRequest;
import com.xftech.cdp.api.dto.marketing.request.WinningRequest;
import com.xftech.cdp.api.dto.marketing.response.*;
import com.xftech.cdp.domain.crowd.service.CrowdCommonService;
import com.xftech.cdp.domain.marketing.repository.MarketingActivityInfoRepository;
import com.xftech.cdp.domain.marketing.repository.MarketingActivityRewardsRepository;
import com.xftech.cdp.domain.marketing.repository.MarketingActivityUserDetailRepository;
import com.xftech.cdp.domain.marketing.repository.MarketingActivityUserRegisterRepository;
import com.xftech.cdp.domain.marketing.vo.CouponRewardConfigVo;
import com.xftech.cdp.feign.model.response.FeatureQueryResponse;
import com.xftech.cdp.feign.service.DataFeatureService;
import com.xftech.cdp.infra.aviator.util.DateTimeUtil;
import com.xftech.cdp.infra.client.coupon.CouponClient;
import com.xftech.cdp.infra.client.coupon.model.req.BaseCouponRequester;
import com.xftech.cdp.infra.client.coupon.model.req.getactivitycoupon.GetActivityCouponDetailRequest;
import com.xftech.cdp.infra.client.coupon.model.req.getusercouponlist.GetUserCouponListReq;
import com.xftech.cdp.infra.client.coupon.model.req.sendusercoupon.SendUserCouponReq;
import com.xftech.cdp.infra.client.coupon.model.resp.BaseCouponResponse;
import com.xftech.cdp.infra.client.coupon.model.resp.getactivitycoupon.GetActivityCouponDetailResponse;
import com.xftech.cdp.infra.client.coupon.model.resp.getusercouponlist.CouponDetail;
import com.xftech.cdp.infra.client.coupon.model.resp.getusercouponlist.GetUserCouponListResp;
import com.xftech.cdp.infra.config.ApolloUtil;
import com.xftech.cdp.infra.repository.cdp.marketing.po.MarketingActivityInfoDo;
import com.xftech.cdp.infra.repository.cdp.marketing.po.MarketingActivityRewardsDo;
import com.xftech.cdp.infra.repository.cdp.marketing.po.MarketingActivityUserDetailDo;
import com.xftech.cdp.infra.repository.cdp.marketing.po.MarketingActivityUserRegisterDo;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.xftech.cdp.infra.utils.ListUtils;
import com.xftech.cdp.infra.utils.MapUtils;
import com.xftech.cdp.infra.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.xftech.cdp.domain.marketing.enums.UserActivityStatus.*;
import static com.xftech.cdp.infra.constant.RedisKeyEnum.ACT_COUPON_INFLATE_LOCK;
import static com.xftech.cdp.infra.constant.RedisKeyEnum.ACT_COUPON_LOTTERY_LOCK;


@Slf4j
@Service
public class MarketingServiceImpl implements MarketingService {

    /**
     * 活动报名模块关联人群包
     */
    private static final String APOLLO_ACTIVITY_REGISTER_MODULE_PACK = "activity_register_module_pack";

    private static final String ACTIVITY_REGISTER_MODULE_FEATURE = "activity.biz.register.feature";

    @Resource
    private MarketingActivityInfoRepository marketingActivityInfoRepository;

    @Resource
    private MarketingActivityRewardsRepository marketingActivityRewardsRepository;

    @Resource
    private MarketingActivityUserDetailRepository marketingActivityUserDetailRepository;

    @Resource
    private MarketingActivityUserRegisterRepository marketingActivityUserRegisterRepository;

    @Resource
    private CrowdCommonService crowdCommonService;

    @Resource
    private CouponClient couponClient;

    @Resource
    private DataFeatureService dataFeatureService;

    @Autowired
    private RedisUtils redisUtils;

    private static final String ACT_COUPON_CONFIG = "activity.coupon.Inflate.config";

    private static final String ACT_USER_APPLY_FEATURE_CODE = "activity.user.apply.feature.code";

    /**
     * 查询用户活动资格
     *
     * @param activityId 活动ID
     * @param userId     用户ID
     * @return
     */
    @Override
    public EligibilityResponse checkUserEligibility(Long activityId, Long userId) {
        try {
            // 1 查询活动详情
            MarketingActivityInfoDo marketingActivityInfoDo = marketingActivityInfoRepository.selectById(activityId);
            // 1.1 查无此活动/活动未生效
            if (marketingActivityInfoDo == null || !Objects.equals(marketingActivityInfoDo.getStatus(), 1)) {
                log.info("MarketingServiceImpl 活动资格检查 activityId={} userId={}. 查无此活动/活动未生效", activityId, userId);
                return new EligibilityResponse(ACTIVITY_HAS_ENDED.getStatus());
            }
            // 1.2 活动已过期
            Date validityBegin = marketingActivityInfoDo.getValidityBegin();
            Date validityEnd = marketingActivityInfoDo.getValidityEnd();
            if (!DateTimeUtil.compareDateWithNow(validityBegin, validityEnd)) {
                log.info("MarketingServiceImpl 活动资格检查 activityId={} userId={}. 活动已过期", activityId, userId);
                return new EligibilityResponse(ACTIVITY_HAS_ENDED.getStatus());
            }
            // 1.3 查询活动奖品明细
            List<MarketingActivityRewardsDo> marketingActivityRewardsDos = marketingActivityRewardsRepository.selectByActivityId(activityId);
            if (CollectionUtils.isEmpty(marketingActivityRewardsDos)) {
                log.info("MarketingServiceImpl 活动资格检查 activityId={} userId={}. 活动奖品为空", activityId, userId);
                return new EligibilityResponse(ACTIVITY_HAS_ENDED.getStatus());
            }

            // 2 是否参加过活动
            List<MarketingActivityUserDetailDo> marketingActivityUserDetailDos = marketingActivityUserDetailRepository.selectByActivityAndUserId(activityId, userId, 1);
            log.info("MarketingServiceImpl DBquery 活动资格检查 activityId={} userId={}. marketingActivityUserDetailDos={}", activityId, userId, marketingActivityUserDetailDos);
            if (CollectionUtils.isEmpty(marketingActivityUserDetailDos)) { // 3 未参加
                // 3.1 查询奖品命中人群包  新需求奖品命中人群包走特征平台
                Set<String> featureCodeSet = Sets.newHashSet();
                marketingActivityRewardsDos.sort(Comparator.comparing(MarketingActivityRewardsDo::getRewardPriority).reversed());
                marketingActivityRewardsDos.forEach(x ->
                        featureCodeSet.addAll(Arrays.stream(x.getRewardCrowdPack().split(","))
                                .filter(y -> !StringUtils.isBlank(y))
                                .collect(Collectors.toSet())));

                List<String> featureList = new ArrayList<>(featureCodeSet);
                List<String> hitFeatureIdList = dataFeatureService.getUserHitFeature(userId,featureList);
                if (CollectionUtils.isNotEmpty(hitFeatureIdList)) {
                    log.info("MarketingServiceImpl 活动资格检查 activityId={} userId={}. 未参加-命中奖品人群包={}", activityId, userId, hitFeatureIdList);
                    return new EligibilityResponse(NOT_PARTICIPATED.getStatus());
                }
                // 3.2 未命中人群包
                log.info("MarketingServiceImpl 活动资格检查 activityId={} userId={}. 未参加-未命中奖品", activityId, userId);
                return new EligibilityResponse(UNABLE_TO_PARTICIPATE.getStatus());
            } else { // 4 已参加
                // 4.1 查询用户优惠券明细
                if(marketingActivityUserDetailDos.size() > 1) {
                    // 取最后一次获得的优惠券
                    marketingActivityUserDetailDos.sort(Comparator.comparing(MarketingActivityUserDetailDo::getCreatedTime).reversed());
                }
                MarketingActivityUserDetailDo marketingActivityUserDetailDo = marketingActivityUserDetailDos.get(0);
                if (Objects.equals(marketingActivityUserDetailDo.getStatus(), 0)) {
                    return new EligibilityResponse(OTHER.getStatus());
                }
                String rewardId = marketingActivityUserDetailDo.getRewardId();
                BaseCouponResponse<GetUserCouponListResp> userCouponListResp = couponClient.getUserCouponList(new BaseCouponRequester<>(new GetUserCouponListReq(userId, Long.parseLong(rewardId))));
                if (userCouponListResp.isSuccess() && Objects.nonNull(userCouponListResp.getResponse())) {
                    Optional<CouponDetail> couponDetailOptional = Optional.ofNullable(userCouponListResp.getResponse().getList())
                            .orElse(Lists.newArrayList()).stream()
                            .filter(x -> StringUtils.equals(x.getActivity_id(), rewardId))
                            .max(Comparator.comparing(CouponDetail::getCreated_time, Comparator.nullsFirst(Date::compareTo)));
                    if (couponDetailOptional.isPresent()) {
                        CouponDetail couponDetail = couponDetailOptional.get();
                        String couponId = couponDetail.getCoupon_id();
                        String couponName = couponDetail.getCoupon_name();
                        String couponActivityName = couponDetail.getActivity_name();
                        String status = couponDetail.getStatus();
                        Integer inflationStatus = marketingActivityUserDetailDos.size() > 1 ? 2 : 1;
                        // 膨胀状态字段
                        if(inflationStatus!= 2 && CollectionUtils.isEmpty( checkCouponInflateConfig(activityId))) {
                            // 判断动支时间是否在活动内
                            inflationStatus = 0;
                        } else if(!checkUserLastApplyTime(userId, validityBegin)) {
                            inflationStatus = 0;
                        }

                        EligibilityResponse eligibilityResponse = new EligibilityResponse();
                        eligibilityResponse.setCouponId(couponId);
                        eligibilityResponse.setCouponName(couponName);
                        eligibilityResponse.setCouponActivityName(couponActivityName);
                        eligibilityResponse.setExpansionStatus(inflationStatus);
                        if (StringUtils.equals(status, "0")) { // 0:可使用
                            eligibilityResponse.setStatus(PARTICIPATED_TO_BE_USED.getStatus());
                            eligibilityResponse.setExpirationCountdown(calculateCountdown(couponDetail.getExpired_time()));
                        }
                        if (StringUtils.equals(status, "1")) { // 1:已使用
                            eligibilityResponse.setStatus(PARTICIPATED_ALREADY_USED.getStatus());
                        }
                        if (StringUtils.equals(status, "2") || StringUtils.equals(status, "3")
                                || StringUtils.equals(status, "4")) { //  0:可使用 1:已使用 2:过期，3:失效，4：冻结
                            eligibilityResponse.setStatus(PARTICIPATED_ALREADY_INVALID.getStatus());
                        }
                        log.info("MarketingServiceImpl 活动资格检查 activityId={} userId={}. 已参加-获得奖品={}, 用户资格={}", activityId, userId, JSONObject.toJSONString(couponDetail), JSONObject.toJSONString(eligibilityResponse));
                        return eligibilityResponse;
                    }
                }
            }
        } catch (Exception e) {
            log.error("MarketingServiceImpl checkUserEligibility error", e);
        }
        return new EligibilityResponse(OTHER.getStatus());
    }

    /**
     * 翻卡抽奖
     *
     * @param participateRequest 抽奖请求信息
     * @return
     */
    @Override
    public Object participateLottery(WinningRequest participateRequest) {
        try {
            Long activityId = participateRequest.getActivityId();
            Long userId = participateRequest.getUserId();

            // 1 查询活动详情
            MarketingActivityInfoDo marketingActivityInfoDo = marketingActivityInfoRepository.selectById(activityId);
            // 1.1 查无此活动/活动未生效
            if (marketingActivityInfoDo == null || !Objects.equals(marketingActivityInfoDo.getStatus(), 1)) {
                log.info("MarketingServiceImpl 翻卡抽奖 activityId={} userId={}. 查无此活动/活动未生效", activityId, userId);
                return "活动未生效";
            }
            // 1.2 活动已过期
            Date validityBegin = marketingActivityInfoDo.getValidityBegin();
            Date validityEnd = marketingActivityInfoDo.getValidityEnd();
            if (!DateTimeUtil.compareDateWithNow(validityBegin, validityEnd)) {
                log.info("MarketingServiceImpl 翻卡抽奖 activityId={} userId={}. 活动已过期", activityId, userId);
                return "活动已过期";
            }
            // 1.3 查询活动奖品明细
            List<MarketingActivityRewardsDo> marketingActivityRewardsDos = marketingActivityRewardsRepository.selectByActivityId(activityId);
            if (CollectionUtils.isEmpty(marketingActivityRewardsDos)) {
                log.info("MarketingServiceImpl 翻卡抽奖 activityId={} userId={}. 活动奖品为空", activityId, userId);
                return "活动奖品为空";
            }

            // 2 是否参加过活动
            List<MarketingActivityUserDetailDo> marketingActivityUserDetailDos = marketingActivityUserDetailRepository.selectByActivityAndUserId(activityId, userId, 1);

            if (CollectionUtils.isEmpty(marketingActivityUserDetailDos)) { // 3 未参加
                boolean lock = redisUtils.lock(ACT_COUPON_LOTTERY_LOCK.getKey(userId),
                        ACT_COUPON_LOTTERY_LOCK.getDefaultValue(),
                        ACT_COUPON_LOTTERY_LOCK.getExpire(), TimeUnit.SECONDS);
                if(!lock) {
                    return "命中并发锁";
                }
                try {
                    marketingActivityRewardsDos.sort(Comparator.comparing(MarketingActivityRewardsDo::getRewardPriority).reversed());
                    // 3.1 是否命中人群包
                    Set<String> featureSet = Sets.newHashSet();
                    marketingActivityRewardsDos.forEach(x ->
                            featureSet.addAll(Arrays.stream(x.getRewardCrowdPack().split(","))
                                    .filter(StringUtils::isNotBlank)
                                    .collect(Collectors.toSet())));
                    List<String> featureList = new ArrayList<>(featureSet);
                    List<String> hitFeatures = dataFeatureService.getUserHitFeature(userId, featureList);
                    if (CollectionUtils.isEmpty(hitFeatures)) {
                        log.info("MarketingServiceImpl 翻卡抽奖 activityId={} userId={}. 未命中奖品人群包={}", activityId, userId, hitFeatures);
                        return "无活动资格";
                    }

                    // 3.2 校验优惠券合法性
                    Set<String> validRewardIds = checkCouponIsAvailable(marketingActivityRewardsDos.stream().map(x -> Long.parseLong(x.getRewardId())).collect(Collectors.toSet()));

                    List<String> hitCrowdRewardIds = marketingActivityRewardsDos.stream()
                            .filter(x -> {
                                Set<String> rewardFeaturePack = Arrays.stream(x.getRewardCrowdPack().split(","))
                                        .collect(Collectors.toSet());
                                return CollectionUtils.containsAny(hitFeatures, rewardFeaturePack);
                            })
                            .sorted(Comparator.comparing(MarketingActivityRewardsDo::getRewardPriority).reversed())
                            .map(MarketingActivityRewardsDo::getRewardId)
                            .collect(Collectors.toList());
                    // 3.3 命中的奖品里优先级最高的一个
                    if (CollectionUtils.isNotEmpty(hitCrowdRewardIds) && validRewardIds.contains(hitCrowdRewardIds.get(0))) {
                        log.info("MarketingServiceImpl 翻卡抽奖 activityId={} userId={}. 发放命中奖品里优先级最高券={}", activityId, userId, hitCrowdRewardIds.get(0));
                        return distributeCoupon(userId, activityId, hitCrowdRewardIds.get(0));
                    } else {
                        // 3.4 兜底奖品
                        Optional<String> fallBackRewardId = marketingActivityRewardsDos.stream()
                                .filter(x -> Objects.equals(x.getRewardFallback(), 1))
                                .map(MarketingActivityRewardsDo::getRewardId)
                                .findAny();
                        if (fallBackRewardId.isPresent() && validRewardIds.contains(fallBackRewardId.get())) {
                            log.info("MarketingServiceImpl 翻卡抽奖 activityId={} userId={}. 发放兜底券={}", activityId, userId, fallBackRewardId.get());
                            return distributeCoupon(userId, activityId, fallBackRewardId.get());
                        }
                        log.info("MarketingServiceImpl 翻卡抽奖 activityId={} userId={}. 无可发放优惠券", activityId, userId);
                    }
                } catch (Exception e) {
                    log.error("MarketingServiceImpl participateLottery sendCoupon error", e);
                } finally {
                    redisUtils.delete(ACT_COUPON_LOTTERY_LOCK.getKey(userId));
                }
            } else {  // 4 已参加
                log.info("MarketingServiceImpl 翻卡抽奖 activityId={} userId={}. 已参与活动", activityId, userId);
                return "已参与活动";
            }
        } catch (Exception e) {
            log.error("MarketingServiceImpl participateLottery error", e);
        }
        return null;
    }

    /**
     * 优惠券膨胀接口
     * DingTalkUtil.dingTalk(dingTalkConfig.getAlarmUrl(), strategyDo);
     * @param couponInflationRequest
     * @return
     */
    @Override
    public InflationResponse couponInflation(ActivityBaseRequest couponInflationRequest) {
        /*
         * 1、Apollo有膨胀的配置，活动有效，奖品有效，用户有参与记录，
         * 2、调用特征平台（未动支），
         * 3、标记已膨胀（Redis），
         * 4、调用发券，返回膨胀后券信息并且是已膨胀状态。
         */
        try {
            Long activityId = couponInflationRequest.getActivityId();
            Long userId = couponInflationRequest.getUserId();

            // 1 查询活动详情
            MarketingActivityInfoDo marketingActivityInfoDo = marketingActivityInfoRepository.selectById(activityId);
            // 1.1 查无此活动/活动未生效
            if (marketingActivityInfoDo == null || !Objects.equals(marketingActivityInfoDo.getStatus(), 1)) {
                log.info("MarketingServiceImpl 优惠券膨胀 activityId={} userId={}. 查无此活动/活动未生效", activityId, userId);
                return new InflationResponse("-1", "活动未生效");
            }
            // 1.2 活动已过期
            Date validityBegin = marketingActivityInfoDo.getValidityBegin();
            Date validityEnd = marketingActivityInfoDo.getValidityEnd();
            if (!DateTimeUtil.compareDateWithNow(validityBegin, validityEnd)) {
                log.info("MarketingServiceImpl 优惠券膨胀 activityId={} userId={}. 活动已过期", activityId, userId);
                return new InflationResponse("-2", "活动已过期");
            }

            // 加锁
            boolean lock = redisUtils.lock(ACT_COUPON_INFLATE_LOCK.getKey(userId),
                    ACT_COUPON_INFLATE_LOCK.getDefaultValue(),
                    ACT_COUPON_INFLATE_LOCK.getExpire(), TimeUnit.SECONDS);
            if(!lock) {
                return new InflationResponse("-6", "命中并发锁");
            }

            try {
                // 1.3 校验用户已经有参与记录，成功获得优惠券
                List<MarketingActivityUserDetailDo> marketingActivityUserDetailDos = marketingActivityUserDetailRepository.selectByActivityAndUserId(activityId, userId, 1);
                if (CollectionUtils.isEmpty(marketingActivityUserDetailDos)) { // 3 未参加
                    return new InflationResponse("-3", "无可膨胀优惠券");
                }

                if(marketingActivityUserDetailDos.size() > 1) {
                    return new InflationResponse("-8", "已参与过其他活动");
                }

                // 用户活动期间未动支
                if(!checkUserLastApplyTime(userId, validityBegin)) {
                    return new InflationResponse("-4", "用户动支时间不符合");
                }

                // 直接查询Apollo配置，不再查数据库
                List<CouponRewardConfigVo> validCouponRewardList = checkCouponInflateConfig(activityId);
                if (CollectionUtils.isEmpty(validCouponRewardList)) {
                    log.info("MarketingServiceImpl 优惠券膨胀 activityId={} userId={}. 膨胀配置为空", activityId, userId);
                    return new InflationResponse("-5", "膨胀配置为空");
                }

                validCouponRewardList.sort(Comparator.comparing(CouponRewardConfigVo::getRewardPriority).reversed());
                // 3.1 是否命中人群包
                Set<String> featureSet = Sets.newHashSet();
                validCouponRewardList.forEach(x ->
                        featureSet.addAll(Arrays.stream(x.getRewardCrowdPack().split(","))
                                .filter(StringUtils::isNotBlank)
                                .collect(Collectors.toSet())));
                List<String> featureList = new ArrayList<>(featureSet);
                List<String> hitFeatures = dataFeatureService.getUserHitFeature(userId, featureList);
                if (CollectionUtils.isEmpty(hitFeatures)) {
                    log.info("MarketingServiceImpl 优惠券膨胀 activityId={} userId={}. 未命中奖品人群包={}", activityId, userId, hitFeatures);
                    return new InflationResponse("-7", "没有活动资格");
                }

                Set<String> validRewardIds = checkCouponIsAvailable(validCouponRewardList.stream().map(x -> Long.parseLong(x.getRewardId())).collect(Collectors.toSet()));

                List<String> hitCrowdRewardIds = validCouponRewardList.stream()
                        .filter(x -> {
                            Set<String> rewardFeaturePack = Arrays.stream(x.getRewardCrowdPack().split(","))
                                    .collect(Collectors.toSet());
                            return CollectionUtils.containsAny(hitFeatures, rewardFeaturePack);
                        })
                        .sorted(Comparator.comparing(CouponRewardConfigVo::getRewardPriority).reversed())
                        .map(CouponRewardConfigVo::getRewardId)
                        .collect(Collectors.toList());

                InflationResponse inflationResponse = new InflationResponse("-8", "未知错误");
                // 3.3 命中的奖品里优先级最高的一个
                if (CollectionUtils.isNotEmpty(hitCrowdRewardIds) && validRewardIds.contains(hitCrowdRewardIds.get(0))) {
                    log.info("MarketingServiceImpl 优惠券膨胀 activityId={} userId={}. 奖品里优先级最高券={}", activityId, userId, hitCrowdRewardIds.get(0));
                    WinningResponse winningResponse =  distributeCoupon(userId, activityId, hitCrowdRewardIds.get(0));
                    inflationResponse = new InflationResponse("0", "success");
                    inflationResponse.setCouponName(winningResponse.getCouponName());
                    inflationResponse.setCouponId(winningResponse.getCouponId());
                    inflationResponse.setExpirationCountdown(winningResponse.getExpirationCountdown());
                    inflationResponse.setCouponActivityName(winningResponse.getCouponActivityName());
                    inflationResponse.setExpansionStatus(2);
                } else {
                    // 3.4 兜底奖品
                    Optional<String> fallBackRewardId = validCouponRewardList.stream()
                            .filter(x -> Objects.equals(x.getRewardFallback(), 1))
                            .map(CouponRewardConfigVo::getRewardId)
                            .findAny();
                    if (fallBackRewardId.isPresent() && validRewardIds.contains(fallBackRewardId.get())) {
                        log.info("MarketingServiceImpl 优惠券膨胀 activityId={} userId={}. 发放兜底券={}", activityId, userId, fallBackRewardId.get());
                        WinningResponse winningResponse =  distributeCoupon(userId, activityId, fallBackRewardId.get());
                        inflationResponse = new InflationResponse("0", "success");
                        inflationResponse.setCouponName(winningResponse.getCouponName());
                        inflationResponse.setCouponId(winningResponse.getCouponId());
                        inflationResponse.setExpirationCountdown(winningResponse.getExpirationCountdown());
                        inflationResponse.setCouponActivityName(winningResponse.getCouponActivityName());
                        inflationResponse.setExpansionStatus(2); // 膨胀状态0=不可膨胀 1=可膨胀 2=已膨胀
                    }
                    log.info("MarketingServiceImpl 优惠券膨胀 activityId={} userId={}. 无可发放优惠券", activityId, userId);
                }

                return inflationResponse;

            } catch (Exception e) {
                log.error("MarketingServiceImpl couponInflation sendCoupon error", e);
            } finally {
                redisUtils.delete(ACT_COUPON_INFLATE_LOCK.getKey(userId));
            }

        } catch (Throwable e) {
            log.error("MarketingServiceImpl couponInflation error", e);
        }

        return new InflationResponse("-99", "活动未开启膨胀功能");
    }

    private List<CouponRewardConfigVo> checkCouponInflateConfig(Long activityId) {
        String inflationConfig = ApolloUtil.getAppProperty(ACT_COUPON_CONFIG);
        List<CouponRewardConfigVo> configRewardList = JSON.parseArray(inflationConfig, CouponRewardConfigVo.class);
        List<CouponRewardConfigVo> validCouponRewardList = Optional.ofNullable(configRewardList).orElse(Lists.newArrayList()).stream()
                .filter(x -> x.getActivityId().equals(activityId))
                .filter(y -> DateTimeUtil.compareDateWithNow(y.getStartTime(),y.getEndTime()))
                .collect(Collectors.toList());
        if (StringUtils.isEmpty(inflationConfig) || CollectionUtils.isEmpty(validCouponRewardList)) {
            log.info("MarketingServiceImpl#checkCouponInflateConfig activityId={} 膨胀配置为空", activityId);
            return Collections.emptyList();
        }
        return validCouponRewardList;
    }

    /**
     * true: 未动支, false: 已动支
     * @param userId
     * @param validityBegin
     * @return
     */
    private boolean checkUserLastApplyTime(Long userId, Date validityBegin) {
        // TODO 开关控制
        String featureName = ApolloUtil.getAppProperty(ACT_USER_APPLY_FEATURE_CODE,"");
        List<String> featureList = Collections.singletonList(featureName);
        FeatureQueryResponse featureQueryResponse = dataFeatureService.getUserLastApplyDate(userId, featureList);
        if(featureQueryResponse == null || MapUtils.isEmpty(featureQueryResponse.getFeatureValues())) {
            // 活动动支时间失败，默认不满足
            return false;
        }
        Map<String, FeatureQueryResponse.FeatureValueModel> featureValues = featureQueryResponse.getFeatureValues();
        FeatureQueryResponse.FeatureValueModel result = featureValues.get(featureName);

        if(result.getObj() != null ) {
            //  判断结果
            if("-9999".equals("" + result.getObj())) {
                return true; // 未动支
            }

            try {
                Date lastApplyTime = DateTimeUtil.formatStrToDate("" + result.getObj(), "yyyy-MM-dd");
                if(lastApplyTime != null) {
                    return lastApplyTime.getTime() < validityBegin.getTime();
                } else {
                    return false;
                }
            } catch (Exception e) {
                log.error("MarketingServiceImpl#checkUserLastApplyTime 动支时间转换异常{}", result, e);
            }
        }
        // 查询失败等情况，默认不满足
        return false;
    }

    /**
     * 检查奖品是否命中人群包
     *
     * @param userId                      用户Id
     * @param marketingActivityRewardsDos 奖品列表
     * @return 命中人群的奖品ID列表
     */
    private Set<String> checkCouponHitCrowd(Long userId, List<MarketingActivityRewardsDo> marketingActivityRewardsDos) {
        Set<String> hitRewardIds = Sets.newHashSet();
        if (Objects.isNull(userId) || CollectionUtils.isEmpty(marketingActivityRewardsDos)) {
            return hitRewardIds;
        }
        try {
            // 非兜底奖品及其人群包
            Map<String, Set<Long>> rewardCrowdSetMap = Maps.newHashMap();
            marketingActivityRewardsDos.sort(Comparator.comparing(MarketingActivityRewardsDo::getRewardPriority).reversed());
            marketingActivityRewardsDos.forEach(x -> {
                Set<Long> crowdSet = Arrays.stream(x.getRewardCrowdPack().split(","))
                        .map(Long::parseLong)
                        .filter(y -> !Objects.equals(y, 0L))
                        .collect(Collectors.toSet());
                rewardCrowdSetMap.put(x.getRewardId(), crowdSet);
            });
            Set<Long> hitCrowdPackIds = crowdCommonService.crowdPackVerify(userId, rewardCrowdSetMap.values().stream().flatMap(Collection::stream).collect(Collectors.toSet()));
            rewardCrowdSetMap.forEach((k, v) -> {
                if (CollectionUtils.containsAny(hitCrowdPackIds, v)) {
                    hitRewardIds.add(k);
                }
            });

            // 兜底奖品
            Set<String> fallBackRewardIds = marketingActivityRewardsDos.stream()
                    .filter(x -> Objects.equals(x.getRewardFallback(), 1))
                    .map(MarketingActivityRewardsDo::getRewardId)
                    .collect(Collectors.toSet());
            hitRewardIds.addAll(fallBackRewardIds);
        } catch (Exception e) {
            log.error("MarketingServiceImpl participateLottery error", e);
        }
        log.info("MarketingServiceImpl 奖品池={} 命中人群包+兜底奖品={}", JSONObject.toJSONString(marketingActivityRewardsDos.stream().map(MarketingActivityRewardsDo::getRewardId).collect(Collectors.toSet())), JSONObject.toJSONString(hitRewardIds));
        return hitRewardIds;
    }

    /**
     * 校验优惠券合法性
     *
     * @param couponActivityIds 优惠券活动ID
     * @return 合法奖品ID列表
     */
    private Set<String> checkCouponIsAvailable(Set<Long> couponActivityIds) {
        Set<String> validRewardIds = Sets.newHashSet();
        if (CollectionUtils.isEmpty(couponActivityIds)) {
            return validRewardIds;
        }
        try {
            BaseCouponResponse<List<GetActivityCouponDetailResponse>> activityListResponse = couponClient.getActivityCouponList(new BaseCouponRequester<>(new GetActivityCouponDetailRequest(couponActivityIds)));
            if (activityListResponse.isSuccess() && CollectionUtils.isNotEmpty(activityListResponse.getResponse())) {
                List<GetActivityCouponDetailResponse> activityInfoList = activityListResponse.getResponse();
                validRewardIds.addAll(activityInfoList.stream()
                        .filter(x -> Objects.nonNull(x) && x.isValid() && couponActivityIds.contains(Long.parseLong(x.getActivity_id())))
                        .map(GetActivityCouponDetailResponse::getActivity_id)
                        .collect(Collectors.toSet()));
            }
        } catch (Exception e) {
            log.error("MarketingServiceImpl checkCouponIsAvailable error", e);
        }
        log.info("MarketingServiceImpl 奖品池={} 校验合法奖品={}", JSONObject.toJSONString(couponActivityIds), JSONObject.toJSONString(validRewardIds));
        return validRewardIds;
    }

    /**
     * 活动页面轮播活动记录，记录从Apollo获取
     * 打乱后一次返回30条记录
     * @param commonRequest
     * @return
     */
    public RecordsBroadcastResponse recordsBroadcast(ActivityBaseRequest commonRequest) {
        Long activityId = commonRequest.getActivityId();
        Long userId = commonRequest.getUserId();
        try {
            // 活动基本信息校验：活动是否存在，是否生效，是否过期
            // 1 查询活动详情
            MarketingActivityInfoDo marketingActivityInfoDo = marketingActivityInfoRepository.selectById(activityId);
            // 1.1 查无此活动/活动未生效
            if (marketingActivityInfoDo == null || !Objects.equals(marketingActivityInfoDo.getStatus(), 1)) {
                log.info("MarketingServiceImpl 奖励轮播 activityId={} userId={}. 查无此活动/活动未生效", activityId, userId);
                return new RecordsBroadcastResponse("-1", "活动未生效");
            }
            // 1.2 活动已过期
            Date validityBegin = marketingActivityInfoDo.getValidityBegin();
            Date validityEnd = marketingActivityInfoDo.getValidityEnd();
            Date now = new Date();
            if (validityBegin != null &&validityBegin.getTime() >now.getTime()) {
                log.info("MarketingServiceImpl 奖励轮播 activityId={} userId={}. 活动已过期", activityId, userId);
                return new RecordsBroadcastResponse("1", "活动未开始");
            }
            // 1.2 活动已过期
            if (!DateTimeUtil.compareDateWithNow(validityBegin, validityEnd)) {
                log.info("MarketingServiceImpl 奖励轮播 activityId={} userId={}. 活动已过期", activityId, userId);
                return new RecordsBroadcastResponse("2", "活动已过期");
            }

            List<ActBroadcastVo> records = JSON.parseArray(ApolloUtil.getAppProperty("activity.biz.records"), ActBroadcastVo.class);
            RecordsBroadcastResponse response = new RecordsBroadcastResponse("0", "success");
            if (records == null || records.isEmpty()) {
                response.setRecords(Collections.emptyList());
                return response;
            }
            if (records.size() > 3) {
                // 随机打乱顺序
                Collections.shuffle(records);
            }
            response.setRecords(records.size() > 30 ? records.subList(0, 30) : records);
            return response;
        } catch (Exception e) {
            log.error(String.format("MarketingServiceImpl recordsBroadcast error activityId:%s userId:%s",
                    activityId, userId), e);
        }
        return new RecordsBroadcastResponse("-99", "活动太火爆!~");
    }
    /**
     * 活动详情页，通过数据库活动配置与Apollo配置获取活动详情，用户活动状态通过"查询用户活动资格"接口获取
     * @param commonRequest
     * @return
     */
    @Override
    public ActivityDetailResponse activityDetail(ActivityBaseRequest commonRequest) {
        Long activityId = commonRequest.getActivityId();
        Long userId = commonRequest.getUserId();
        try {
            // 活动基本信息校验：活动是否存在，是否生效，是否过期
            // 1 查询活动详情
            MarketingActivityInfoDo marketingActivityInfoDo = marketingActivityInfoRepository.selectById(activityId);
            // 1.1 查无此活动/活动未生效
            if (marketingActivityInfoDo == null || !Objects.equals(marketingActivityInfoDo.getStatus(), 1)) {
                log.info("MarketingServiceImpl#activityDetail  activityId={} userId={}. 查无此活动/活动未生效", activityId, userId);
                return new ActivityDetailResponse("-1", "活动未生效");
            }
            Date validityBegin = marketingActivityInfoDo.getValidityBegin();
            Date validityEnd = marketingActivityInfoDo.getValidityEnd();
            Date now = new Date();
            if (validityBegin != null &&validityBegin.getTime() >now.getTime()) {
                log.info("MarketingServiceImpl#activityDetail activityId={} userId={}. 活动已过期", activityId, userId);
                return new ActivityDetailResponse("1", "活动未开始");
            }
            // 1.2 活动已过期
            if (!DateTimeUtil.compareDateWithNow(validityBegin, validityEnd)) {
                log.info("MarketingServiceImpl#activityDetail activityId={} userId={}. 活动已过期", activityId, userId);
                return new ActivityDetailResponse("2", "活动已过期");
            }

            ActivityDetailResponse response = new ActivityDetailResponse("0", "success");
            // 活动信息返回
            response.setActivityName(marketingActivityInfoDo.getName());

            if(validityEnd != null) {
                Long endTimeSecond = validityEnd.getTime();
                Long curTimeSecond = now.getTime();
                response.setCountdown((endTimeSecond - curTimeSecond) / 1000L);
            } else {
                log.info("MarketingServiceImpl#activityDetail activityId={} userId={}. 活动未配置", activityId, userId);
                return new ActivityDetailResponse("-99", "活动结束时间异常");
            }
            // hitCrowd 对接特征平台
            String activityRegisterModulePack = ApolloUtil.getAppProperty(ACTIVITY_REGISTER_MODULE_FEATURE, "{}");
            Map<String, Set<String>> activityRegisterModulePackMap = JSONObject.parseObject(activityRegisterModulePack, new TypeReference<Map<String, Set<String>>>() {
            });
            if (activityRegisterModulePackMap.containsKey(activityId)) {
                List<String> featureList = new ArrayList<>(activityRegisterModulePackMap.get(activityId));
                List<String> result = dataFeatureService.getUserHitFeature(userId,featureList);
                if(CollectionUtils.isNotEmpty(result)) {
                    response.setHitCrowd(1);
                }else {
                    response.setHitCrowd(0);
                }
            } else {
                // TODO 未找到人群配置 配置异常报警
                response.setHitCrowd(0);
            }

            return response;
        } catch (Exception e) {
            log.error(String.format("MarketingServiceImpl activityDetail error activityId:%s userId:%s",
                    activityId, userId), e);
        }
        return new ActivityDetailResponse("-99", "活动太火爆!~");
    }

    /**
     * 发放优惠券
     *
     * @param userId     用户ID
     * @param activityId 活动ID
     * @param rewardId   奖品(优惠券活动)ID
     * @return
     */
    private WinningResponse distributeCoupon(Long userId, Long activityId, String rewardId) {
        log.info("MarketingServiceImpl 发放优惠券-start. userId={} activityId={} rewardId={}", userId, activityId, rewardId);
        int status = -1;
        try {
            BaseCouponResponse sendUserCouponResponse = couponClient.sendUserCoupon(new BaseCouponRequester<>(new SendUserCouponReq(userId, Long.parseLong(rewardId))));
            if (sendUserCouponResponse.isSuccess()) {  // 发券成功
                status = 1;
                // 查询用户优惠券明细
                BaseCouponResponse<GetUserCouponListResp> userCouponListResp = couponClient.getUserCouponList(new BaseCouponRequester<>(new GetUserCouponListReq(userId, Long.parseLong(rewardId))));
                if (userCouponListResp.isSuccess() && Objects.nonNull(userCouponListResp.getResponse())) {
                    Optional<CouponDetail> couponDetailOptional = Optional.ofNullable(userCouponListResp.getResponse().getList()).orElse(Lists.newArrayList()).stream()
                            .filter(x -> StringUtils.equals(x.getActivity_id(), rewardId))
                            .max(Comparator.comparing(CouponDetail::getCreated_time, Comparator.nullsFirst(Date::compareTo)));
                    if (couponDetailOptional.isPresent()) {
                        CouponDetail couponDetail = couponDetailOptional.get();
                        WinningResponse winningResponse = new WinningResponse();
                        winningResponse.setCouponId(couponDetail.getCoupon_id());
                        winningResponse.setCouponName(couponDetail.getCoupon_name());
                        winningResponse.setCouponActivityName(couponDetail.getActivity_name());
                        winningResponse.setExpirationCountdown(calculateCountdown(couponDetail.getExpired_time()));
                        log.info("MarketingServiceImpl 发放优惠券-end. userId={} activityId={} rewardId={} 发放结果={}", userId, activityId, rewardId, JSONObject.toJSONString(winningResponse));
                        return winningResponse;
                    }
                }
            } else {
                status = 0;
            }
        } catch (Exception e) {
            log.error("MarketingServiceImpl distributeCoupon error", e);
        } finally {
            // 记录用户参与明细
            MarketingActivityUserDetailDo marketingActivityUserDetailDo = new MarketingActivityUserDetailDo();
            marketingActivityUserDetailDo.setActivityId(activityId);
            marketingActivityUserDetailDo.setUserId(userId);
            marketingActivityUserDetailDo.setRewardType(1);
            marketingActivityUserDetailDo.setRewardId(rewardId);
            marketingActivityUserDetailDo.setStatus(status);
            marketingActivityUserDetailRepository.insert(marketingActivityUserDetailDo);
        }
        return null;
    }

    /**
     * 计算距离目标时间的倒计时
     *
     * @param targetDate 目标时间
     * @return
     */
    private long calculateCountdown(Date targetDate) {
        if (targetDate == null) {
            return 0;
        }
        // 获取当前时间
        Date currentDate = new Date();
        // 计算时间差（毫秒）
        long differenceInMillis = targetDate.getTime() - currentDate.getTime();
        // 如果目标时间已经过去，返回0
        if (differenceInMillis < 0) {
            return 0;
        }
        // 将毫秒差转换为秒
        return TimeUnit.MILLISECONDS.toSeconds(differenceInMillis);
    }

    /**
     * 活动报名
     *
     * @param request 报名请求
     * @return
     */
    @Override
    public RegisterResponse registerLottery(RegisterRequest request) {
        RegisterResponse registerResponse = new RegisterResponse();
        Long activityId = request.getActivityId();
        Long userId = request.getUserId();

        // 判断是否展示报名模块
        boolean show = showRegisterModule(String.valueOf(activityId), userId);
        registerResponse.setShow(show);
        if (!show) {
            log.info("MarketingServiceImpl 活动报名 activityId={} userId={} 未命中报名模块", request.getActivityId(), request.getUserId());
            return registerResponse;
        }

        // 查询报名信息
        MarketingActivityUserRegisterDo marketingActivityUserRegisterDo = queryUserRegisterStatus(activityId, userId);
        if (Objects.equals(request.getQueryStatus(), 1)) { // 查询
            if (marketingActivityUserRegisterDo == null) {
                registerResponse.setStatus(0);
            } else {
                registerResponse.setStatus(1);
            }
        } else { // 报名
            if (marketingActivityUserRegisterDo == null) {
                marketingActivityUserRegisterDo = new MarketingActivityUserRegisterDo();
                marketingActivityUserRegisterDo.setActivityId(activityId);
                marketingActivityUserRegisterDo.setUserId(userId);
                marketingActivityUserRegisterRepository.insert(marketingActivityUserRegisterDo);
            }
            registerResponse.setStatus(1);
        }
        log.info("MarketingServiceImpl 活动报名 activityId={} userId={}. 报名结果={}", request.getActivityId(), request.getUserId(), JSONObject.toJSONString(registerResponse));
        return registerResponse;
    }

    /**
     * 根据活动ID与用户ID查询报名信息
     *
     * @param activityId 活动ID
     * @param userId     用户ID
     * @return
     */
    private MarketingActivityUserRegisterDo queryUserRegisterStatus(Long activityId, Long userId) {
        try {
            List<MarketingActivityUserRegisterDo> marketingActivityUserRegisterDos = marketingActivityUserRegisterRepository.selectByActivityAndUserId(activityId, userId);
            if (CollectionUtils.isNotEmpty(marketingActivityUserRegisterDos)) {
                return marketingActivityUserRegisterDos.get(0);
            }
        } catch (Exception e) {
            log.error("MarketingServiceImpl queryUserRegisterStatus error={}", e.getMessage(), e);
        }
        return null;
    }

    /**
     * 判断是否展示报名模块
     *
     * @param activityId 活动ID
     * @param userId     用户ID
     * @return
     */
    private boolean showRegisterModule(String activityId, Long userId) {
        boolean show = false;
        try {
            // 1 查询活动详情
            MarketingActivityInfoDo marketingActivityInfoDo = marketingActivityInfoRepository.selectById(Long.parseLong(activityId));
            // 1.1 查无此活动/活动未生效
            if (marketingActivityInfoDo == null || !Objects.equals(marketingActivityInfoDo.getStatus(), 1)) {
                log.info("MarketingServiceImpl 是否展示报名模块=否 activityId={} userId={}. 查无此活动/活动未生效", activityId, userId);
                return false;
            }
            // 1.2 活动已过期
            Date validityBegin = marketingActivityInfoDo.getValidityBegin();
            Date validityEnd = marketingActivityInfoDo.getValidityEnd();
            if (!DateTimeUtil.compareDateWithNow(validityBegin, validityEnd)) {
                log.info("MarketingServiceImpl 是否展示报名模块=否 activityId={} userId={}. 活动已过期", activityId, userId);
                return false;
            }

            // 2 判断用户是否命中 报名模块人群包 20250109 改为特征平台
            String activityRegisterModulePack = ApolloUtil.getAppProperty(ACTIVITY_REGISTER_MODULE_FEATURE, "{}");
            Map<String, Set<String>> activityRegisterModulePackMap = JSONObject.parseObject(activityRegisterModulePack, new TypeReference<Map<String, Set<String>>>() {
            });
            if (activityRegisterModulePackMap.containsKey(activityId)) {
                List<String> featureList = new ArrayList<>(activityRegisterModulePackMap.get(activityId));
                List<String> result = dataFeatureService.getUserHitFeature(userId,featureList);
                show = CollectionUtils.isNotEmpty(result);
            }
        } catch (Exception e) {
            log.error("MarketingServiceImpl showRegisterModule error={}", e.getMessage(), e);
        }
        log.info("MarketingServiceImpl 是否展示报名模块={} activityId={} userId={}", show, activityId, userId);
        return show;
    }

}