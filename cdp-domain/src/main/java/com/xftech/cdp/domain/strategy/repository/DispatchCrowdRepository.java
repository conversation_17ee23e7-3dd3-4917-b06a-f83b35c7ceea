/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.infra.repository.cdp.strategy.po.DispatchCrowdDo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @version $ DispatchCrowdRepository, v 0.1 2023/12/29 14:04 yye.xu Exp $
 */

@Repository
@AllArgsConstructor
public class DispatchCrowdRepository {

    public Integer selectCount(long strategyId, String flowNo,
                               int dateValue) {
        Map<String, Object> params = new HashMap<>();
        params.put("tableNum", dateValue);
        params.put("flowNo", flowNo);
        params.put("strategyId", strategyId);
        return DBUtil.selectOne("dispatchCrowdDoMapper.selectCount", params);
    }

    public Integer selectDispatchCount(long strategyId, String batchNo,
                               int dateValue) {
        Map<String, Object> params = new HashMap<>();
        params.put("tableNum", dateValue);
        params.put("batchNo", batchNo);
        params.put("strategyId", strategyId);
        return DBUtil.selectOne("dispatchCrowdDoMapper.selectDispatchCount", params);
    }

    public List<DispatchCrowdDo> selectList(long startIndex, long size, long strategyId, String flowNo,
                                            int dateValue) {
        Map<String, Object> params = new HashMap<>();
        params.put("tableNum", dateValue);
        params.put("flowNo", flowNo);
        params.put("startIndex", startIndex);
        params.put("size", size);
        params.put("strategyId", strategyId);
        return DBUtil.selectList("dispatchCrowdDoMapper.selectList", params);
    }

    public List<DispatchCrowdDo> selectUserList(String flowNo, int dateValue, List<Long> userIdList,
                                     Long strategyId) {
        Map<String, Object> params = new HashMap<>();
        params.put("tableNum", dateValue);
        params.put("flowNo", flowNo);
        params.put("userIdList", userIdList);
        params.put("strategyId", strategyId);
        return DBUtil.selectList("dispatchCrowdDoMapper.selectUserList", params);
    }

    public void bacthInsert(List<DispatchCrowdDo> dispatchCrowdDoList, int dateValue) {
        if (CollectionUtils.isEmpty(dispatchCrowdDoList)) {
            return;
        }
        dispatchCrowdDoList.forEach(x -> x.setTableNum(dateValue));
        DBUtil.insertBatchWithoutTx("dispatchCrowdDoMapper.insertSelective",
                dispatchCrowdDoList);
    }
}