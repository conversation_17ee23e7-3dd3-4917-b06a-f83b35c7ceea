/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.infra.client.usercenter.model;

import lombok.Data;

/**
 *
 * <AUTHOR>
 * @version $ RegisterInfoByUserNo, v 0.1 2023/11/14 17:21 yye.xu Exp $
 */

public class RegisterInfoByUserNo {

    @Data
    public static class RespDto {
        private String mobile;
        private String app;
        private Long userNo;
        private String registerTime;
        private String sourceChannel;
        private String custName;
        private String custNo;
        private String idCardNumber;

        public UserInfoResp convert(){
            UserInfoResp userInfoResp = new UserInfoResp();
            userInfoResp.setCreditUserId(userNo);
            userInfoResp.setApp(app);
            userInfoResp.setCreatedTime(registerTime);
            userInfoResp.setInnerApp(sourceChannel);
            userInfoResp.setMobile(mobile);
            userInfoResp.setCustName(custName);
            return userInfoResp;
        }
    }
}