<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="statStrategyFlowDataMapper">
  <resultMap id="BaseResultMap" type="com.xftech.cdp.domain.stat.entity.StatStrategyFlowDataEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="date_value" jdbcType="INTEGER" property="dateValue" />
    <result column="strategy_id" jdbcType="BIGINT" property="strategyId" />
    <result column="strategy_name" jdbcType="VARCHAR" property="strategyName" />
    <result column="level_num" jdbcType="INTEGER" property="levelNum" />
    <result column="flow_no" jdbcType="VARCHAR" property="flowNo" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="dispatch_num" jdbcType="INTEGER" property="dispatchNum" />
    <result column="d_flag" jdbcType="SMALLINT" property="dFlag" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
  </resultMap>
  <sql id="Base_Column_List">
    `id`, `date_value`, `strategy_id`, `strategy_name`, `level_num`, `flow_no`, `batch_no`, `dispatch_num`,
    `d_flag`, `created_time`, `updated_time`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from stat_strategy_flow_data
    where `id` = #{id,jdbcType=BIGINT}
  </select>

  <select id="exitsRecord" resultType="integer">
    select count(*)
    from stat_strategy_flow_data
    where strategy_id = #{strategyId}
    and flow_no = #{flowNo}
    and batch_no = #{batchNo}
  </select>

  <select id="updateByRecord" parameterType="com.xftech.cdp.domain.stat.entity.StatStrategyFlowDataEntity">
    update stat_strategy_flow_data
    <set>
      <if test="dateValue != null">
        `date_value` = #{dateValue,jdbcType=INTEGER},
      </if>
      <if test="strategyId != null">
        `strategy_id` = #{strategyId,jdbcType=BIGINT},
      </if>
      <if test="strategyName != null">
        `strategy_name` = #{strategyName,jdbcType=VARCHAR},
      </if>
      <if test="levelNum != null">
        `level_num` = #{levelNum,jdbcType=INTEGER},
      </if>
      <if test="flowNo != null">
        `flow_no` = #{flowNo,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        `batch_no` = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="dispatchNum != null">
        `dispatch_num` = #{dispatchNum,jdbcType=INTEGER},
      </if>
      <if test="dFlag != null">
        `d_flag` = #{dFlag,jdbcType=SMALLINT},
      </if>
      <if test="createdTime != null">
        `created_time` = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        `updated_time` = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where strategy_id = #{strategyId}
      and flow_no = #{flowNo}
      and batch_no = #{batchNo}
  </select>

  <select id="selectPageByFlowNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from stat_strategy_flow_data
    where flow_no = #{flowNo}
    and level_num = 0
    order by id desc
  </select>

  <select id="selectByBatchNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from stat_strategy_flow_data
    where batch_no = #{batchNo}
    and d_flag = 0
  </select>

  <insert id="insertSelective" parameterType="com.xftech.cdp.domain.stat.entity.StatStrategyFlowDataEntity">
    insert into stat_strategy_flow_data
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="dateValue != null">
        `date_value`,
      </if>
      <if test="strategyId != null">
        `strategy_id`,
      </if>
      <if test="strategyName != null">
        `strategy_name`,
      </if>
      <if test="levelNum != null">
        `level_num`,
      </if>
      <if test="flowNo != null">
        `flow_no`,
      </if>
      <if test="batchNo != null">
        `batch_no`,
      </if>
      <if test="dispatchNum != null">
        `dispatch_num`,
      </if>
      <if test="dFlag != null">
        `d_flag`,
      </if>
      <if test="createdTime != null">
        `created_time`,
      </if>
      <if test="updatedTime != null">
        `updated_time`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="dateValue != null">
        #{dateValue,jdbcType=INTEGER},
      </if>
      <if test="strategyId != null">
        #{strategyId,jdbcType=BIGINT},
      </if>
      <if test="strategyName != null">
        #{strategyName,jdbcType=VARCHAR},
      </if>
      <if test="levelNum != null">
        #{levelNum,jdbcType=INTEGER},
      </if>
      <if test="flowNo != null">
        #{flowNo,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="dispatchNum != null">
        #{dispatchNum,jdbcType=INTEGER},
      </if>
      <if test="dFlag != null">
        #{dFlag,jdbcType=SMALLINT},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        #{updatedTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xftech.cdp.domain.stat.entity.StatStrategyFlowDataEntity">
    update stat_strategy_flow_data
    <set>
      <if test="dateValue != null">
        `date_value` = #{dateValue,jdbcType=INTEGER},
      </if>
      <if test="strategyId != null">
        `strategy_id` = #{strategyId,jdbcType=BIGINT},
      </if>
      <if test="strategyName != null">
        `strategy_name` = #{strategyName,jdbcType=VARCHAR},
      </if>
      <if test="levelNum != null">
        `level_num` = #{levelNum,jdbcType=INTEGER},
      </if>
      <if test="flowNo != null">
        `flow_no` = #{flowNo,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        `batch_no` = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="dispatchNum != null">
        `dispatch_num` = #{dispatchNum,jdbcType=INTEGER},
      </if>
      <if test="dFlag != null">
        `d_flag` = #{dFlag,jdbcType=SMALLINT},
      </if>
      <if test="createdTime != null">
        `created_time` = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        `updated_time` = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xftech.cdp.domain.stat.entity.StatStrategyFlowDataEntity">
    update stat_strategy_flow_data
    set `date_value` = #{dateValue,jdbcType=INTEGER},
      `strategy_id` = #{strategyId,jdbcType=BIGINT},
      `strategy_name` = #{strategyId,jdbcType=VARCHAR},
      `level_num` = #{levelNum,jdbcType=INTEGER},
      `flow_no` = #{flowNo,jdbcType=VARCHAR},
      `batch_no` = #{batchNo,jdbcType=VARCHAR},
      `dispatch_num` = #{dispatchNum,jdbcType=INTEGER},
      `d_flag` = #{dFlag,jdbcType=SMALLINT},
      `created_time` = #{createdTime,jdbcType=TIMESTAMP},
      `updated_time` = #{updatedTime,jdbcType=TIMESTAMP}
    where `id` = #{id,jdbcType=BIGINT}
  </update>
</mapper>