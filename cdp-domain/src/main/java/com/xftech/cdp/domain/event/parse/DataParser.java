package com.xftech.cdp.domain.event.parse;

import com.xftech.cdp.domain.event.model.dto.MqEventFieldMappingConfig;

import java.util.Map;

/**
 * 参数解析接口
 * <AUTHOR>
 * @version $ DataParser, v 0.1 2024/11/15 14:31 snail Exp $
 */
public interface DataParser {
    /**
     * 按照配置的参数信息解析消息体
     * @param content 消息体内容
     * @param config 参数解析配置信息
     * @return 解析后的消息内容
     */
    Map<String,Object> doParse(String content, MqEventFieldMappingConfig config);
}
