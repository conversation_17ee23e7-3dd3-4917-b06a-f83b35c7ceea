package com.xftech.cdp.domain.strategy.service;

import com.xftech.cdp.domain.flowctrl.model.enums.StrategyTypeEnum;
import com.xftech.cdp.domain.strategy.model.dispatch.StrategyContext;
import com.xftech.cdp.domain.strategy.model.enums.StrategyRulerEnum;
import com.xftech.cdp.infra.repository.cdp.strategy.po.UserBlankGroupDetailDo;

/**
 * @<NAME_EMAIL>
 * @date 2023/4/19 15:16
 */
public interface UserBlankGroupDetailService {

    void add(UserBlankGroupDetailDo blankGroupDetail, StrategyRulerEnum strategyRulerEnum);

    /**
     * 异步保存留白组
     *
     * @param context 策略执行初始化内容
     */
    int blankGroupAsync(StrategyContext context);
}
