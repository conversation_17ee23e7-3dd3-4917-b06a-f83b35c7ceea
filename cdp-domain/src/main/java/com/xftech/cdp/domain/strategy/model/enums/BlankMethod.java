/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.model.enums;

/**
 *
 * <AUTHOR>
 * @version $ BlankMethod, v 0.1 2023/10/20 14:51 wancheng.qu Exp $
 */

public enum BlankMethod {
    RAND("rand", "随机"),
    RATE("rate", "比例");

    private String method;
    private String description;

    BlankMethod(String method, String description) {
        this.method = method;
        this.description = description;
    }

    public String getMethod() {
        return method;
    }

    public String getDescription() {
        return description;
    }
}
