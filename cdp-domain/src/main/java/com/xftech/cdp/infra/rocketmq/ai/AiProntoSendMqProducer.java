/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.rocketmq.ai;

import com.xftech.cdp.infra.rocketmq.dto.AiSendArgs;
import com.xinfei.xfframework.common.JsonUtil;
import com.xinfei.xfframework.common.starter.mq.MqTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version $ AiProntoSendMqProducer, v 0.1 2024/8/20 18:00 lingang.han Exp $
 */
@Slf4j
@Component
public class AiProntoSendMqProducer {
    @Autowired
    private MqTemplate mqTemplate;

    public SendResult asyncSend(AiSendArgs aiSendArgs) {
        log.info("aiProntoSendMqProducer message:{}", JsonUtil.toJson(aiSendArgs));
        try {
            return mqTemplate.syncSend("tp_telemkt_ai_roster", JsonUtil.toJson(aiSendArgs));
        } catch (Exception e) {
            log.error("aiProntoSendMqProducer error", e);
        }
        return null;
    }
}