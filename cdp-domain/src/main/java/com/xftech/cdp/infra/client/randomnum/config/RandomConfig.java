package com.xftech.cdp.infra.client.randomnum.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * @<NAME_EMAIL>
 */

@RefreshScope
@Component
@Setter
@Getter
public class RandomConfig {
    @Value("${usergroup-metadata.host}")
    private String host;

    @Value("${cdp.random.route.query.list:/abTest/abTests}")
    private String randomListRoute;
}
