/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.rocketmq.distribution;

import java.util.Date;

import javax.annotation.Resource;

import com.xftech.cdp.domain.strategy.service.dispatch.event.StrategyEventDispatchService;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventMessageVO;
import com.xftech.cdp.infra.rocketmq.EventEnum;
import com.xftech.cdp.infra.rocketmq.MessageHandler;
import com.xftech.cdp.infra.rocketmq.RocketMQEnum;
import com.xftech.cdp.infra.utils.DateUtil;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class DistributionGrantedSuccessHandler implements MessageHandler {

    @Resource
    private StrategyEventDispatchService strategyEventDispatchService;

    @Override
    public String getTopic() {
        return RocketMQEnum.DISTRIBUTION_GRANTED_SUCCESS.getTopic();
    }

    @Override
    public boolean execute(String messageId, Object message) {
        DistributionGrantedSuccessVO messageVO = JSONObject.parseObject(message.toString(), DistributionGrantedSuccessVO.class);
        if (StringUtils.isAnyBlank(messageVO.getApp(), messageVO.getUserNo(), messageVO.getProductNo(), messageVO.getOrderNo())) {
            log.info("DistributionGrantedSuccessHandler 不符合[分发授信成功]事件条件 message={}", JSONObject.toJSONString(messageVO));
        }
        BizEventMessageVO bizEventMessageVO = transform(messageVO);
        bizEventMessageVO.setBizEventType(EventEnum.DISTRIBUTION_GRANTED_SUCCESS.getEventType());
        strategyEventDispatchService.prescreen(messageId, bizEventMessageVO);
        return true;
    }

    private BizEventMessageVO transform(DistributionGrantedSuccessVO distributionGrantedSuccessVO) {
        BizEventMessageVO bizEventMessageVO = new BizEventMessageVO();
        bizEventMessageVO.setApp(distributionGrantedSuccessVO.getApp());
        bizEventMessageVO.setInnerApp(distributionGrantedSuccessVO.getInnerApp());
        bizEventMessageVO.setAppUserId(Long.parseLong(distributionGrantedSuccessVO.getUserNo()));
        BizEventMessageVO.ExtrData extrData = new BizEventMessageVO.ExtrData();
        extrData.setProduct(distributionGrantedSuccessVO.getProductNo());
        extrData.setOrderNo(distributionGrantedSuccessVO.getOrderNo());
        bizEventMessageVO.setExtrData(extrData);
        bizEventMessageVO.setTriggerDatetime(DateUtil.convertStr(new Date()));
        return bizEventMessageVO;
    }

}