package com.xftech.cdp.domain.dispatch.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.google.common.collect.Lists;
import com.xftech.cdp.domain.cache.CacheStrategyMarketChannelService;
import com.xftech.cdp.domain.dispatch.BatchDispatchService;
import com.xftech.cdp.domain.dispatch.dto.APIOpenAmountDto;
import com.xftech.cdp.domain.dispatch.dto.APIOpenAmountPushArgs;
import com.xftech.cdp.domain.dispatch.dto.DispatchDto;
import com.xftech.cdp.domain.dispatch.function.FourParamsFunction;
import com.xftech.cdp.domain.strategy.model.dto.AiProntoChannelDto;
import com.xftech.cdp.domain.strategy.model.enums.MarketChannelTypeEnum;
import com.xftech.cdp.domain.strategy.service.TelePushService;
import com.xftech.cdp.domain.strategy.service.UserDispatchFailDetailService;
import com.xftech.cdp.domain.strategy.service.impl.GoodsServiceImpl;
import com.xftech.cdp.feign.model.GoodsDetail;
import com.xftech.cdp.feign.model.PushUserData;
import com.xftech.cdp.feign.model.SendPushInfo;
import com.xftech.cdp.feign.model.requset.PushBaseRequest;
import com.xftech.cdp.feign.model.requset.SendPushRequest;
import com.xftech.cdp.feign.model.response.PushResponse;
import com.xftech.cdp.infra.client.ai.AiClient;
import com.xftech.cdp.infra.client.coupon.CouponClient;
import com.xftech.cdp.infra.client.coupon.model.req.BaseCouponRequester;
import com.xftech.cdp.infra.client.coupon.model.req.CouponSendBatchReq;
import com.xftech.cdp.infra.client.coupon.model.resp.BaseCouponResponse;
import com.xftech.cdp.infra.client.increaseamt.model.dto.UserInfoDto;
import com.xftech.cdp.infra.client.push.PushClient;
import com.xftech.cdp.infra.client.sms.SmsClient;
import com.xftech.cdp.infra.client.sms.model.SmsBatchSendArgs;
import com.xftech.cdp.infra.client.sms.model.SmsBatchWithParamArgs;
import com.xftech.cdp.infra.client.sms.model.SmsBatchWithParamRequester;
import com.xftech.cdp.infra.client.sms.model.SmsSendRequester;
import com.xftech.cdp.infra.client.sms.model.resp.BaseSmsResp;
import com.xftech.cdp.infra.client.telemarketing.TelemarketingClient;
import com.xftech.cdp.infra.client.telemarketing.model.TelePushArgs;
import com.xftech.cdp.infra.client.telemarketing.model.TeleSaveBatchArgs;
import com.xftech.cdp.infra.client.telemarketing.model.TeleSaveBatchRequest;
import com.xftech.cdp.infra.client.telemarketing.model.resp.AiSendResp;
import com.xftech.cdp.infra.client.telemarketing.model.resp.TelePushResp;
import com.xftech.cdp.infra.client.telemarketing.model.resp.TeleSaveBatchResp;
import com.xftech.cdp.infra.client.usercenter.CisService;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPushBatchDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketChannelDo;
import com.xftech.cdp.infra.rocketmq.dto.AiSendArgs;
import com.xftech.cdp.infra.rocketmq.dto.AiUserData;
import com.xftech.cdp.infra.utils.JsonUtil;
import com.xinfei.xfframework.common.starter.mq.MqTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.rocketmq.client.producer.SendResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 触达服务
 *
 * @<NAME_EMAIL>
 */
@Slf4j
@Service("batchDispatchService")
public class BatchDispatchServiceImpl extends AbstractDispatchService implements BatchDispatchService {

    @Autowired
    private SmsClient smsClient;
    @Autowired
    private CouponClient couponClient;
    @Autowired
    private TelemarketingClient telemarketingClient;
    @Autowired
    private TelePushService telePushService;
    @Autowired
    private PushClient pushClient;
    @Autowired
    private CisService cisService;
    @Autowired
    private AiClient aiClient;
    @Autowired
    private UserDispatchFailDetailService userDispatchFailDetailService;
    @Autowired
    private GoodsServiceImpl goodsService;
    @Autowired
    private MqTemplate mqTemplate;
    @Autowired
    private CacheStrategyMarketChannelService cacheStrategyMarketChannelService;

    /**
     * 下发短信
     *
     * @param reach    策略上下文
     * @param app      appwhite_list_models
     * @param innerApp innerApp
     * @param batch    手机号
     * @return 下发数量
     */
    @Override
    public ImmutablePair<Integer, CrowdPushBatchDo> sendSms(DispatchDto reach, String app, String innerApp, List<CrowdDetailDo> batch) {
        //List<String> mobileList = getMobileList(batch);
        FourParamsFunction<MarketChannelTypeEnum, List<CrowdDetailDo>, List<CrowdDetailDo>, List<SmsBatchWithParamArgs.Sms>, List<SmsBatchWithParamArgs.Sms>> filterFunction = this::filterBatchParamsList;
        return request(MarketChannelTypeEnum.BATCH_SMS, reach, app, innerApp, batch, this::getUserIdMobileList, filterFunction, (batchNum, params) -> {
            SmsBatchSendArgs args = new SmsBatchSendArgs();
            args.setMobiles(params.stream().map(SmsBatchWithParamArgs.Sms::getMobile).distinct().collect(Collectors.toList()));
            args.setUserNoList(params.stream().map(SmsBatchWithParamArgs.Sms::getUserNo).distinct().collect(Collectors.toList()));
            args.setTemplateId(reach.getStrategyMarketChannelTemplateId());
            args.setApp(app);
            args.setInnerApp(innerApp);
            args.setBatchNum(batchNum);
            args.setSignatureKey(reach.getSignatureKey());
            SmsSendRequester requester = new SmsSendRequester();
            requester.setArgs(args);
            BaseSmsResp<?> resp = smsClient.doBatchSendSms(requester);
            return Pair.of(resp.isSuccess(), Pair.of(String.valueOf(resp.getStatus()), resp.getMessage()));
        });
    }

    private List<SmsBatchWithParamArgs.Sms> getUserIdMobileList(List<CrowdDetailDo> batch) {
        return batch.stream().map(x -> new SmsBatchWithParamArgs.Sms(x.getMobile(), x.getUserId())).collect(Collectors.toList());
    }

    public <T> List<T> filterBatchParamsList(MarketChannelTypeEnum channelType, List<CrowdDetailDo> batch, List<CrowdDetailDo> excludes, List<T> params) {
        Set<Long> ext = excludes.stream().map(CrowdDetailDo::getUserId)
                .collect(Collectors.toSet());
        if (channelType == MarketChannelTypeEnum.BATCH_SMS || channelType == MarketChannelTypeEnum.BATCH_SMS_PARAM) {
            params = params.stream().filter(x -> !ext.contains(((SmsBatchWithParamArgs.Sms) x).getUserNo()))
                    .distinct()
                    .collect(Collectors.toList());
            log.info("排除的userId:{}", JsonUtil.toJson(ext));
            return params;
        } else if (channelType == MarketChannelTypeEnum.BATCH_VOICE) {
            params = params.stream().filter(x -> !ext.contains(x))
                    .collect(Collectors.toList());
            return params;
        } else if (channelType == MarketChannelTypeEnum.BATCH_SALE_TICKET) {
            params = params.stream().filter(x -> !ext.contains(((CouponSendBatchReq.User) x).getUserId()))
                    .collect(Collectors.toList());
            return params;
        } else if (channelType == MarketChannelTypeEnum.BATCH_NEW_VOICE) {
            params = params.stream().filter(x -> !ext.contains(((TelePushArgs.UserData) x).getUserId()))
                    .collect(Collectors.toList());
            return params;
        } else if (channelType == MarketChannelTypeEnum.BATCH_PUSH) {
            params = params.stream().filter(x -> !ext.contains(Long.valueOf(((PushUserData) x).getUserNo())))
                    .collect(Collectors.toList());
            return params;
        } else if (channelType == MarketChannelTypeEnum.BATCH_PUSH_PARAM) {
            params = params.stream().filter(x -> !ext.contains(Long.valueOf(((PushUserData) x).getUserNo())))
                    .collect(Collectors.toList());
            return params;
        } else if (channelType == MarketChannelTypeEnum.INCREASE_AMT) {
            params = params.stream().filter(x -> !ext.contains(x))
                    .collect(Collectors.toList());
            return params;
        } else if (channelType == MarketChannelTypeEnum.X_DAY_INTEREST_FREE) {
            params = params.stream().filter(x -> !ext.contains(((CouponSendBatchReq.User) x).getUserId()))
                    .collect(Collectors.toList());
            return params;
        } else if (channelType == MarketChannelTypeEnum.LIFE_RIGHTS) {
            params = params.stream().filter(x -> !ext.contains(((CouponSendBatchReq.User) x).getUserId()))
                    .collect(Collectors.toList());
            return params;
        } else if (channelType == MarketChannelTypeEnum.AI_PRONTO) {
            params = params.stream().filter(x -> !ext.contains(((AiUserData) x).getUserNo()))
                    .collect(Collectors.toList());
            return params;
        }
        return params;
    }

    /**
     * 发送批量代偿短信
     *
     * @param reach    策略上下文
     * @param app      app
     * @param innerApp innerApp
     * @param batch    手机号
     * @return 下发数量
     */
    @Override
    public ImmutablePair<Integer, CrowdPushBatchDo> sendSmsWithParam(DispatchDto reach, String app, String innerApp, List<CrowdDetailDo> batch, List<SmsBatchWithParamArgs.Sms> params) {
        FourParamsFunction<MarketChannelTypeEnum, List<CrowdDetailDo>, List<CrowdDetailDo>, List<SmsBatchWithParamArgs.Sms>, List<SmsBatchWithParamArgs.Sms>> filterFunction = this::filterBatchParamsList;
        return request(MarketChannelTypeEnum.BATCH_SMS_PARAM, reach, app, innerApp, batch, crowdDetailList -> params, filterFunction, (batchNum, batchParams) -> {
            SmsBatchWithParamArgs args = new SmsBatchWithParamArgs();
            args.setTemplateId(reach.getStrategyMarketChannelTemplateId());
            args.setApp(app);
            args.setInnerApp(innerApp);
            args.setBatchNum(batchNum);
            args.setSmsList(batchParams);
            args.setSignatureKey(reach.getSignatureKey());
            SmsBatchWithParamRequester requester = new SmsBatchWithParamRequester();
            requester.setArgs(args);
            BaseSmsResp<?> resp = smsClient.smsBatchWithParam(requester);
            return Pair.of(resp.isSuccess(), Pair.of(String.valueOf(resp.getStatus()), resp.getMessage()));
        });
    }

    /**
     * @param reach:
     * @param app:
     * @param innerApp:
     * @param batch:
     * @return ImmutablePair<Integer, CrowdPushBatchDo>
     * <AUTHOR>
     * @description 发送新电销
     * @date 2023/10/17 17:12
     */
    @Override
    public ImmutablePair<Integer, CrowdPushBatchDo> sendNewTele(DispatchDto reach, String app, String innerApp, List<CrowdDetailDo> batch) {
        String tempId = reach.getStrategyMarketChannelTemplateId();
        TelePushArgs policyDetail = telePushService.getTelePushArgs(Integer.valueOf(tempId), batch, reach, app);
        FourParamsFunction<MarketChannelTypeEnum, List<CrowdDetailDo>, List<CrowdDetailDo>, List<TelePushArgs.UserData>, List<TelePushArgs.UserData>> filterFunction = this::filterBatchParamsList;
        return request(MarketChannelTypeEnum.BATCH_NEW_VOICE, reach, app, innerApp, batch, alist -> policyDetail.getData(), filterFunction, (batchNum, batchParams) -> {
            TelePushArgs requester = TelePushArgs.builder()
                    .batchNumber(batchNum)
                    .traceId(UUID.randomUUID().toString())
                    .policyId(policyDetail.getPolicyId())
                    .policyType(policyDetail.getPolicyType())
                    .strategyType(policyDetail.getStrategyType())
                    .data(batchParams)
                    .ua("xyf-cdp")
                    .ts(1L)
                    .nameTypeId(policyDetail.getNameTypeId())
                    .cdpStrategyId(policyDetail.getCdpStrategyId())
                    .build();
            TelePushResp teleSp = telemarketingClient.pushTeleData(requester);
            return Pair.of(teleSp.isSuccess(), Pair.of(String.valueOf(teleSp.getStatus()), teleSp.getMessage()));
        });
    }

    @Override
    public ImmutablePair<Integer, CrowdPushBatchDo> sendPush(DispatchDto reach, String app, String innerApp, List<CrowdDetailDo> batch) {
        FourParamsFunction<MarketChannelTypeEnum, List<CrowdDetailDo>, List<CrowdDetailDo>, List<PushUserData>, List<PushUserData>> filterFunction = this::filterBatchParamsList;
        return request(MarketChannelTypeEnum.BATCH_PUSH, reach, app, innerApp, batch, crowdDetailList -> crowdDetailList.stream().map(PushUserData::new).collect(Collectors.toList()),
                filterFunction, (batchNum, batchParams) -> {
                    PushBaseRequest<SendPushRequest> request = new PushBaseRequest<>();
                    SendPushRequest sendPushRequest = new SendPushRequest();
                    sendPushRequest.setApp(app);
                    sendPushRequest.setInnerApp(innerApp);
                    sendPushRequest.setTemplateId(reach.getStrategyMarketChannelTemplateId());
                    sendPushRequest.setBatchNum(batchNum);
                    sendPushRequest.setPushDataList(batchParams);
                    request.setTimestamp(String.valueOf(Instant.now().getEpochSecond()));
                    request.setArgs(sendPushRequest);
                    PushResponse<SendPushInfo> resp = pushClient.doBatchSendPush(request);
                    return Pair.of(resp.isSuccess(), Pair.of(String.valueOf(resp.getStatus()), resp.getMessage()));
                });
    }

    @Override
    public ImmutablePair<Integer, CrowdPushBatchDo> sendPushWithParam(DispatchDto reach, String app, String innerApp, List<CrowdDetailDo> batch, List<PushUserData> params) {
        FourParamsFunction<MarketChannelTypeEnum, List<CrowdDetailDo>, List<CrowdDetailDo>, List<PushUserData>, List<PushUserData>> filterFunction = this::filterBatchParamsList;
        return request(MarketChannelTypeEnum.BATCH_PUSH_PARAM, reach, app, innerApp, batch, crowdDetailList -> params,
                filterFunction, (batchNum, batchParams) -> {
                    PushBaseRequest<SendPushRequest> request = new PushBaseRequest<>();
                    SendPushRequest sendPushRequest = new SendPushRequest();
                    sendPushRequest.setApp(app);
                    sendPushRequest.setInnerApp(innerApp);
                    sendPushRequest.setTemplateId(reach.getStrategyMarketChannelTemplateId());
                    sendPushRequest.setBatchNum(batchNum);
                    sendPushRequest.setPushDataList(batchParams);
                    request.setTimestamp(String.valueOf(Instant.now().getEpochSecond()));
                    request.setArgs(sendPushRequest);
                    PushResponse<SendPushInfo> resp = pushClient.doBatchSendPush(request);
                    return Pair.of(resp.isSuccess(), Pair.of(String.valueOf(resp.getStatus()), resp.getMessage()));
                });
    }

    @Override
    public ImmutablePair<Integer, CrowdPushBatchDo> sendAiProto(DispatchDto reach, String app, String innerApp, List<CrowdDetailDo> batch) {
        FourParamsFunction<MarketChannelTypeEnum, List<CrowdDetailDo>, List<CrowdDetailDo>, List<AiUserData>, List<AiUserData>> filterFunction = this::filterBatchParamsList;
        return request(MarketChannelTypeEnum.AI_PRONTO, reach, app, innerApp, batch, crowdDetailList -> crowdDetailList.stream().map(AiUserData::new).collect(Collectors.toList()),
                filterFunction,
                (batchNum, batchParams) -> {
                    AiSendArgs aiSendArgs = new AiSendArgs();
                    aiSendArgs.setBatchNumber(batchNum);

                    AiProntoChannelDto aiProntoChannelDto = JsonUtil.parse(reach.getStrategyMarketChannelDo().getExtInfo(), AiProntoChannelDto.class);
                    if (aiProntoChannelDto != null && StringUtils.isNotBlank(aiProntoChannelDto.getNameTypeId())) {
                        aiSendArgs.setAiChannelType(aiProntoChannelDto.getNameTypeId());
                    }

                    aiSendArgs.setStrategyId(reach.getStrategyId());
                    aiSendArgs.setStrategyType(reach.getStrategyRulerEnum().getStrategyType());
                    aiSendArgs.setUserDataList(batchParams);
                    AiSendResp aiSendResp = aiClient.doBatchSendAi(aiSendArgs);
                    return Pair.of(aiSendResp.isSuccess(), Pair.of(String.valueOf(aiSendResp.getStatus()), aiSendResp.getMessage()));
                });
    }

    @Override
    public ImmutablePair<Integer, CrowdPushBatchDo> sendAiProtoWithParam(DispatchDto reach, String app, String innerApp, List<CrowdDetailDo> batch, List<AiUserData> params) {
        FourParamsFunction<MarketChannelTypeEnum, List<CrowdDetailDo>, List<CrowdDetailDo>, List<AiUserData>, List<AiUserData>> filterFunction = this::filterBatchParamsList;
        return request(MarketChannelTypeEnum.AI_PRONTO, reach, app, innerApp, batch, crowdDetailList -> params,
                filterFunction,
                (batchNum, batchParams) -> {
                    AiSendArgs aiSendArgs = new AiSendArgs();
                    aiSendArgs.setBatchNumber(batchNum);

                    AiProntoChannelDto aiProntoChannelDto = JsonUtil.parse(reach.getStrategyMarketChannelDo().getExtInfo(), AiProntoChannelDto.class);
                    if (aiProntoChannelDto != null && StringUtils.isNotBlank(aiProntoChannelDto.getNameTypeId())) {
                        aiSendArgs.setAiChannelType(aiProntoChannelDto.getNameTypeId());
                    }

                    aiSendArgs.setStrategyId(reach.getStrategyId());
                    aiSendArgs.setStrategyType(reach.getStrategyRulerEnum().getStrategyType());
                    aiSendArgs.setUserDataList(batchParams);
                    AiSendResp aiSendResp = aiClient.doBatchSendAi(aiSendArgs);
                    return Pair.of(aiSendResp.isSuccess(), Pair.of(String.valueOf(aiSendResp.getStatus()), aiSendResp.getMessage()));
                });
    }

    /**
     * 调用电销保存接口
     *
     * @param reach    策略执行初始化内容
     * @param app      app
     * @param innerApp innerApp
     * @param batch    需要下发的用户ID集合
     * @return ImmutablePair Left-下发成功数 Right-下发批次记录
     */
    @Override
    public ImmutablePair<Integer, CrowdPushBatchDo> sendTele(DispatchDto reach, String app, String innerApp, List<CrowdDetailDo> batch) {
        //List<Long> userIdList = getUserIdList(batch);
        FourParamsFunction<MarketChannelTypeEnum, List<CrowdDetailDo>, List<CrowdDetailDo>, List<Long>, List<Long>> filterFunction = this::filterBatchParamsList;
        return request(MarketChannelTypeEnum.BATCH_VOICE, reach, app, innerApp, batch, this::getUserIdList, filterFunction, (batchNum, batchParams) -> {
            TeleSaveBatchRequest requester = new TeleSaveBatchRequest();
            TeleSaveBatchArgs args = new TeleSaveBatchArgs();
            args.setCreditIdArr(batchParams);
            args.setUserType(Integer.valueOf(reach.getStrategyMarketChannelTemplateId()));
            args.setFlowNo(batchNum);
            args.setBatchCount(1);
            args.setCurrentBatch(1);
            requester.setArgs(args);
            TeleSaveBatchResp teleSaveBatchResp = telemarketingClient.saveBatch(requester);
            return Pair.of(teleSaveBatchResp.isSuccess(), Pair.of(teleSaveBatchResp.getCode(), teleSaveBatchResp.getMessage()));
        });
    }

    private List<Long> getUserIdList(List<CrowdDetailDo> batch) {
        return batch.stream().map(CrowdDetailDo::getUserId).distinct().collect(Collectors.toList());
    }

    /**
     * 调用优惠券批量下发接口
     *
     * @param reach    策略执行初始化内容
     * @param app      app
     * @param innerApp innerApp
     * @param batch    需要下发的用户集合
     * @return ImmutablePair Left-下发成功数 Right-下发批次记录
     */
    @Override
    public ImmutablePair<Integer, CrowdPushBatchDo> sendCoupon(DispatchDto reach, String app, String innerApp, List<CrowdDetailDo> batch) {
        //List<CouponSendBatchReq.User> list = this.getUserList(batch);
        FourParamsFunction<MarketChannelTypeEnum, List<CrowdDetailDo>, List<CrowdDetailDo>, List<CouponSendBatchReq.User>, List<CouponSendBatchReq.User>> filterFunction = this::filterBatchParamsList;
        return request(MarketChannelTypeEnum.BATCH_SALE_TICKET, reach, app, innerApp, batch, this::getUserList, filterFunction, (batchNum, batcParams) -> {
            CouponSendBatchReq req = new CouponSendBatchReq();
            req.setActivityId(Convert.toLong(reach.getStrategyMarketChannelTemplateId()));
            req.setBatchNum(batchNum);
            req.setApp(app);
            req.setInnerApp(innerApp);
            req.setAllBatchCount(batcParams.size());
            req.setUserList(batcParams);
            BaseCouponResponse<Object> response = couponClient.sendBatch(new BaseCouponRequester<>(req));
            return Pair.of(response.isSuccess(), Pair.of(String.valueOf(response.getStatus()), response.getMessage()));
        });
    }

    private List<CouponSendBatchReq.User> getUserList(List<CrowdDetailDo> batch) {
        return batch.stream().map(crowdDetailDo -> {
            CouponSendBatchReq.User user = new CouponSendBatchReq.User();
            user.setUserId(crowdDetailDo.getUserId());
            user.setMobile(crowdDetailDo.getMobile());
            //发送优惠券接口文档约定,name字段可传手机号
            user.setName(crowdDetailDo.getMobile());
            return user;
        }).collect(Collectors.toList());
    }

    @Override
    public ImmutablePair<Integer, CrowdPushBatchDo> sendBatchIncreaseAmt(DispatchDto reach, String app, String innerApp, List<CrowdDetailDo> batch) {
        List<UserInfoDto> userInfoDtos = cisService.getUserInfoDtos(batch);
        Set<Long> userSet = userInfoDtos.stream().map(UserInfoDto::getUserId).collect(Collectors.toSet());
        List<CrowdDetailDo> validBatch = batch.stream()
                .filter(x -> userSet.contains(x.getUserId())).collect(Collectors.toList());
        List<CrowdDetailDo> notValidBatch = batch.stream()
                .filter(x -> !userSet.contains(x.getUserId())).collect(Collectors.toList());
        // 未查到用户的，保存失败记录
        Lists.partition(notValidBatch, 20).forEach(list -> {
            userDispatchFailDetailService.saveFailRecords(list, reach, "用户信息不全", reach.getStrategyGroupName());
        });
        ImmutablePair<Integer, CrowdPushBatchDo> ret = batchRequestIncreaseAmt(reach, app, innerApp, validBatch, userInfoDtos);
        return ret;
    }

    @Override
    public ImmutablePair<Integer, CrowdPushBatchDo> sendXDayInterestFree(DispatchDto reach, String app, String innerApp, List<CrowdDetailDo> batch) {
        FourParamsFunction<MarketChannelTypeEnum, List<CrowdDetailDo>, List<CrowdDetailDo>, List<CouponSendBatchReq.User>, List<CouponSendBatchReq.User>> filterFunction = this::filterBatchParamsList;
        return request(MarketChannelTypeEnum.X_DAY_INTEREST_FREE, reach, app, innerApp, batch, this::getUserList, filterFunction, (batchNum, batcParams) -> {
            CouponSendBatchReq req = new CouponSendBatchReq();
            req.setActivityId(Convert.toLong(reach.getStrategyMarketChannelTemplateId()));
            req.setBatchNum(batchNum);
            req.setApp(app);
            req.setInnerApp(innerApp);
            req.setAllBatchCount(batcParams.size());
            req.setUserList(batcParams);
            BaseCouponResponse<Object> response = couponClient.sendBatch(new BaseCouponRequester<>(req));
            return Pair.of(response.isSuccess(), Pair.of(String.valueOf(response.getStatus()), response.getMessage()));
        });
    }

    @Override
    public ImmutablePair<Integer, CrowdPushBatchDo> sendLifeRights(DispatchDto reach, String app, String innerApp, List<CrowdDetailDo> batch) {
        String goodsId = reach.getStrategyMarketChannelTemplateId();
        String activityId = reach.getActivityId();
        GoodsDetail goodsDetail = goodsService.getGoodsDetailByGoodsId(Long.valueOf(goodsId));
        FourParamsFunction<MarketChannelTypeEnum, List<CrowdDetailDo>, List<CrowdDetailDo>, List<CouponSendBatchReq.User>, List<CouponSendBatchReq.User>> filterFunction = this::filterBatchParamsList;
        return request(MarketChannelTypeEnum.LIFE_RIGHTS, reach, app, innerApp, batch, this::getUserList, filterFunction, (batchNum, batcParams) -> {
            CouponSendBatchReq req = new CouponSendBatchReq();
            req.setActivityId(Convert.toLong(activityId));
            req.setBatchNum(batchNum);
            req.setApp(app);
            req.setInnerApp(innerApp);
            req.setAllBatchCount(batcParams.size());
            req.setGoodsId(goodsDetail.getGoodsId().toString());
            req.setGoodsName(goodsDetail.getGoodsName());
            req.setType(2);
            req.setJumpType(goodsDetail.getJumpType());
            req.setJumpUrl(goodsDetail.getJumpUrl());
            req.setUserList(batcParams);
            BaseCouponResponse<Object> response = couponClient.sendBatch(new BaseCouponRequester<>(req));
            return Pair.of(response.isSuccess(), Pair.of(String.valueOf(response.getStatus()), response.getMessage()));
        });
    }

    @Override
    public ImmutablePair<Integer, CrowdPushBatchDo> sendAPIOpenAmount(DispatchDto reach, String app, String innerApp, List<CrowdDetailDo> batch) {
        return request(MarketChannelTypeEnum.API_OPEN_AMOUNT, reach, app, innerApp, batch, this::getUserList, this::filterBatchParamsList,
                (batchNum, batchParams) -> {
                    String topic = "tp_xyf_cdp_api_open_amount";

                    StrategyMarketChannelDo strategyMarketChannelDo = cacheStrategyMarketChannelService.cacheSelectById(reach.getStrategyChannelId());
                    String extInfo = strategyMarketChannelDo.getExtInfo();
                    APIOpenAmountDto apiOpenAmountDto = JsonUtil.parse(extInfo, APIOpenAmountDto.class);
                    APIOpenAmountPushArgs args = new APIOpenAmountPushArgs();
                    if (Objects.nonNull(apiOpenAmountDto)) {

                        LocalDateTime endDate = LocalDateTime.now().plusDays(apiOpenAmountDto.getAfterDays());
                        endDate = LocalDateTimeUtil.endOfDay(endDate);
                        String endDateText = LocalDateTimeUtil.format(endDate, "yyyy-MM-dd HH:mm:ss");
                        args.setEndDate(endDateText);
                        args.setInnerApp(apiOpenAmountDto.getInnerApps());
                    }
                    List<Long> userIdList = batchParams.stream()
                            .mapToLong(CouponSendBatchReq.User::getUserId)
                            .boxed()
                            .collect(Collectors.toList());
                    args.setUserList(userIdList);



                    String data = JsonUtil.toJson(args);
                    SendResult sendResult = mqTemplate.syncSend(topic, data);
                    log.info("[APIOpenAmount] send: {}, args: {}, sendResult: {}", topic, data, sendResult);
                    if (Objects.nonNull(sendResult)) {
                        if (Objects.nonNull(sendResult.getSendStatus())) {
                            return Pair.of(Boolean.TRUE, Pair.of("1", StringUtils.EMPTY));
                        }
                    }
                    return Pair.of(Boolean.FALSE, Pair.of("-1", StringUtils.EMPTY));
                });
    }
}
