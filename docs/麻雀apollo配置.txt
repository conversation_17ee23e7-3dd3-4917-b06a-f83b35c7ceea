########################################### Datasource Config ###########################################
server.port = 8080

spring.datasource.druid.maxActive = 100

spring.datasource.ads.jdbcUrl = *********************************************************************************************************************************************************************************************************************************************************************************************************************************************
spring.datasource.ads.username = flow_user
spring.datasource.ads.password = n8P0OI3NY4zLHsrk
spring.datasource.ads.driver-class-name = com.mysql.cj.jdbc.Driver

########################################### Datasource Config ###########################################
temp.param.config = ["user_four_phone","user_mask_name"]
########################################### Mongo Config ###########################################
########################################### Mongo Config ###########################################

########################################### Redis Config ###########################################
spring.redis.host = r-2ze50r4xqwvpwykkht.redis.rds.aliyuncs.com
spring.redis.port = 6379
spring.redis.database = 0
spring.redis.password = (cJFTdFrthMhZlYYCSoVWDkgFHR(v6
########################################### Redis Config ###########################################


########################################## XxlJob Config ##########################################
xxl.job.accessToken = oQ6iQ@Jvv%zdxgAhls*LXXGUh091J^ID
# 调度中心部署跟地址 [选填]：如调度中心集群部署存在多个地址则用逗号分隔。执行器将会使用该地址进行"执行器心跳注册"和"任务结果回调"；为空则关闭自动注册；
xxl.job.admin.addresses = http://xxl-job-admin.xinfei.io/xxl-job-admin
xxl.job.executor.address = 
# 执行器AppName [选填]：执行器心跳注册分组依据；为空则关闭自动注册
xxl.job.executor.appname = ${spring.application.name}
# 执行器IP [选填]：默认为空表示自动获取IP，多网卡时可手动设置指定IP，该IP不会绑定Host仅作为通讯实用；地址信息用于 "执行器注册" 和 "调度中心请求并触发任务"；
xxl.job.executor.ip = 
# 执行器端口号 [选填]：小于等于0则自动获取；默认端口为9999，单机部署多个执行器时，注意要配置不同执行器端口；
xxl.job.executor.port = 18080
# 执行器运行日志文件存储磁盘路径 [选填] ：需要对该路径拥有读写权限；为空则使用默认路径；
xxl.job.executor.logpath = /data/logs/xyf-cdp/xxl-job
# 执行器日志保存天数 [选填] ：值大于3时生效，启用执行器Log文件定期清理功能，否则不生效；
xxl.job.executor.logretentiondays = 30
xxl.job.config.username = xyfcdp
xxl.job.config.password = eXotYY82KbRskpMk
xxl.job.config.jobGroup = 7
#xxl.job.config.admin-addresses=http://*************:8080/xxl-job-admin
xxl.job.config.admin-addresses = http://xxl-job-admin.xinfei.io/xxl-job-admin
########################################## XxlJob Config ##########################################


########################################## OSS Config ##########################################
aliyun.oss.endpoint = oss-cn-beijing-internal.aliyuncs.com
aliyun.oss.accessKeyId = LTAI5tNvR3ypLTWac6m5wMhh
aliyun.oss.secretAccessKey = ******************************
aliyun.oss.bucketName = xyf-cdp
aliyun.oss.keyUrl.expireMinutes = 10
aliyun.oss.uploadFilePath = upload/cdp/crowd_upload
########################################## OSS Config ##########################################


########################################## 业务-短信 Config ##########################################
cdp.sms.host = http://sms.xinfei.io
########################################## 业务-短信 Config ##########################################

########################################## sso Config ##########################################
cdp.sso.host = https://api-telreport.xinfei.cn
########################################## sso Config ##########################################


########################################## sso Config ##########################################
cdp.datacenter.host = http://api.xinfei.io/data-center
cdp.datacenter.mobile.decrypt.key = prod-8e2PaRD5xLtp16JOcdTrilGV
########################################## sso Config ##########################################


spring.servlet.multipart.max-file-size = 20MB
spring.servlet.multipart.max-request-size = 20MB
spring.mvc.async.request-timeout = 60000


crowd.pool.poolName = corwdwerehouse
crowd.pool.corePoolSize = 8
crowd.pool.maximumPoolSize = 20
crowd.pool.keepAliveTime = 300
crowd.pool.queueSize = 2000
crowd.alarm.job = 2446
cdp.crowd.alarmUrl = https://oapi.dingtalk.com/robot/send?access_token=1efa5c5b45b5abd6ca92eba38e8bb8d3ad2a5f5aa078582b7c2c5d940405d9db
cdp.crowd.rockPageSize = 5000
cdp.crowd.batchSaveSize = 3000

dingtalk.alarmUrl = https://oapi.dingtalk.com/robot/send?access_token=e90a4a77db0d68577e774630ec07e2151cffc9a284feadf098cd9aabdd933652

# 短信报告查询天数
sms.query.report.day = 2

# 策略短信批次大小配置
strategy.dispatch.channel.sms.pagesize = 1000

strategy.label.query.BatchSize = 200

spring.datasource.type = com.alibaba.druid.pool.DruidDataSource
spring.datasource.driver-class-name = com.mysql.cj.jdbc.Driver

udp.datasource.configs[0].url = ${spring.datasource.ads.jdbcUrl}
udp.datasource.configs[0].username = ${spring.datasource.ads.username}
udp.datasource.configs[0].password = ${spring.datasource.ads.password}
udp.datasource.configs[0].dsname = ads
udp.datasource.configs[0].driverClassName = ${spring.datasource.ads.driver-class-name}

# 分表大小
crowd.detail.table.size = 8000000
# 数仓aesKey（注：需替换生产环境nacos，取 dataId: dw-cryp, group: aes）
ads.mobile.aes.key = <EMAIL>
# 加贷电销人群包对应类型
crowd.pack.sms.flg = {"28":"1","29":"2","30":"3"}

xxl.job.config.adminAddresses = http://xxl-job-admin.xinfei.io/xxl-job-admin

# 优惠券查询天数
coupon.query.day = 30
# 批次数量统计是否使用缓存
batch.count.useCache = false
########################################## 业务-电销 Config ##########################################
# 电销系统URL
cdp.tele.host = http://inner-api-telemarketing-backend.xinfei.io/cdp
########################################## 业务-电销 Config ##########################################

########################################## 业务-优惠券 Config ##########################################
# 优惠券系统URL
cdp.coupon.host = http://inner-coupon-api.xinyongfei.io
########################################## 业务-优惠券 Config ##########################################

########################################## 钉钉预警 Config ##########################################
#钉钉预警@人手机号，多个用逗号隔开
dingtalk.atMobile = 18502198054
########################################## 钉钉预警 Config ##########################################

########################################### 策略 相关 ###########################################
# 电销渠道分页大小
strategy.dispatch.channel.tele.pagesize = 1500
# 优惠券渠道分页大小
strategy.dispatch.channel.coupon.pagesize = 1000
# 流控指标查询每批大小
strategy.dispatch.detail.query.pagesize = 1000
# 留白组分页大小
strategy.dispatch.blank.query.pagesize = 1000
########################################### 策略 相关 ###########################################

########################################### 链路追踪 相关 ###########################################
# 上报链路追踪信息
xyf.trace.report.enable = true
# 日志服务Project的接入地址，格式为${project}.${region-endpoint}
xyf.brave.zipkin.endpoint = https://gz-trace.cn-beijing.log.aliyuncs.com/zipkin/api/v2/spans
# 日志服务Project
xyf.trace.sls.otel.project = prod-gz-java-cdp
# Trace服务实例ID
xyf.trace.sls.otel.instance.id = prod-gz-java-cdp
# 阿里云账号AccessKey ID
xyf.trace.sls.otel.ak.id = LTAI5tB8KscwGRBV1Rcx9e3A
# 阿里云账号AccessKey Secret
xyf.trace.sls.otel.ak.secret = ******************************
########################################### 链路追踪 相关 ###########################################

########################################### RabbitMQ ###########################################
# mq批量消费大小
spring.rabbitmq.batchSize = 50
# mq消费超时时间
spring.rabbitmq.timeout = 10000

# 注：配置与短信服务-MQ新集群 同一MQ
spring.rabbitmq.host = amqp-cn-j67316nyy00r.cn-beijing.amqp-9.vpc.mq.amqp.aliyuncs.com
spring.rabbitmq.port = 5672
spring.rabbitmq.username = MjphbXFwLWNuLWo2NzMxNm55eTAwcjpMVEFJNXRIV0d2TDl3aHZFWU03N2liaHU=
spring.rabbitmq.password = NjJDREYxNTQwN0VGNTExMTc4MDJBQURGNTI1QkI5NkNBRTA5MDcwNDoxNjcyODk2NzA3OTcx
spring.rabbitmq.virtual-host = prod_xyf_sms_center

# 注：配置与优惠券系统（xyf-coupon-system）同一MQ
spring.rabbitmq.host2 = amqp-cn-zim41ga25001.cn-beijing.amqp-22.vpc.mq.amqp.aliyuncs.com
spring.rabbitmq.port2 = 5672
spring.rabbitmq.username2 = MjphbXFwLWNuLXppbTQxZ2EyNTAwMTpMVEFJNXRHSDlHeVBOMjV2WmJRejdvS20=
spring.rabbitmq.password2 = ************************************************************************
spring.rabbitmq.virtual-host2 = prod_xyf_copuon_queue
########################################### RabbitMQ 相关 ###########################################
# 短信回执队列信息
sms.report.exchange = exchange_report_callback_topic
sms.report.exchangeType = topic
sms.report.routingKey = sms_center_callback_app_xyf-cdp
sms.report.queue.name = sms_supplier_report_callback
# 优惠券明细回传队列信息
coupon.callback.exchange = exchange_batch_coupon_callback_send_topic
coupon.callback.exchangeType = direct
coupon.callback.routingKey = coupon_center_callback_app_xyf_cdp
coupon.callback.queue.name = coupon_center_cash_coupon_cdp_process
coupon.callback.deadLetterExchange = exchange_batch_coupon_callback_send_topic
coupon.callback.deadLetterExchangeType = direct
coupon.callback.deadLetterRoutingKey = dl_coupon_center_callback_app_xyf_cdp
coupon.callback.deadLetterQueueName = dl_coupon_center_cash_coupon_cdp_process

# 上报系统-麻雀系统MQ（配置xyf-cdp麻雀系统使用的mq，上报系统也使用该mq）
spring.rabbitmq.host3 = amqp-cn-zim41ga25001.cn-beijing.amqp-22.vpc.mq.amqp.aliyuncs.com
spring.rabbitmq.port3 = 5672
spring.rabbitmq.username3 = MjphbXFwLWNuLXppbTQxZ2EyNTAwMTpMVEFJNXRHSDlHeVBOMjV2WmJRejdvS20=
spring.rabbitmq.password3 = ************************************************************************
spring.rabbitmq.virtual-host3 = prod_xyf_queue

# 业务事件队列信息
# 单条消费预取大小
spring.rabbitmq.biz.prefetchSize = 3
# 批量消费大小
spring.rabbitmq.biz.batchSize = 95
# # 批量消费超时时间
spring.rabbitmq.biz.timeout = 5000

biz.event.hl.exchange = exchange_biz_event_high_level_topic
biz.event.hl.exchangeType = topic
biz.event.hl.routingKey = key_biz_event_high_level_xyf_cdp
biz.event.hl.queue.name = queue_biz_event_high_level_xyf_cdp

biz.event.ml.exchange = exchange_biz_event_middle_level_topic
biz.event.ml.exchangeType = topic
biz.event.ml.routingKey = key_biz_event_middle_level_xyf_cdp
biz.event.ml.queue.name = queue_biz_event_middle_level_xyf_cdp

biz.event.atOnce.delay.time = 60
biz.event.delay.exchange = exchange_biz_event_delay_topic
biz.event.delay.xDelayedType = direct
biz.event.delay.exchangeType = x-delayed-message
biz.event.delay.hl.routingKey = key_biz_event_delay_hl_xyf_cdp
biz.event.delay.hl.queue.name = queue_biz_event_delay_hl_xyf_cdp
biz.event.delay.ml.routingKey = key_biz_event_delay_ml_xyf_cdp
biz.event.delay.ml.queue.name = queue_biz_event_delay_ml_xyf_cdp
biz.event.delay.ll.routingKey = key_biz_event_delay_ll_xyf_cdp
biz.event.delay.ll.queue.name = queue_biz_event_delay_ll_xyf_cdp

biz.event.dispatch.exchange = exchange_biz_event_dispatch_topic
biz.event.dispatch.exchangeType = topic
biz.event.sms.dispatch.routingKey = key_biz_event_sms_dispatch_xyf_cdp
biz.event.sms.dispatch.queue.name = queue_biz_event_sms_dispatch_xyf_cdp
biz.event.tele.dispatch.routingKey = key_biz_event_tele_dispatch_xyf_cdp
biz.event.tele.dispatch.queue.name = queue_biz_event_tele_dispatch_xyf_cdp
biz.event.coupon.dispatch.routingKey = key_biz_event_coupon_dispatch_xyf_cdp
biz.event.coupon.dispatch.queue.name = queue_biz_event_coupon_dispatch_xyf_cdp
biz.event.noMarket.dispatch.routingKey = key_biz_event_nomarket_dispatch_xyf_cdp
biz.event.noMarket.dispatch.queue.name = queue_biz_event_nomarket_dispatch_xyf_cdp
biz.event.increaseamt.dispatch.routingKey = key_biz_event_increaseamt_dispatch_xyf_cdp
biz.event.increaseamt.dispatch.queue.name = queue_biz_event_increaseamt_dispatch_xyf_cdp
biz.event.lifeRights.dispatch.routingKey = key_biz_event_liferights_dispatch_xyf_cdp
biz.event.lifeRights.dispatch.queue.name = queue_biz_event_liferights_dispatch_xyf_cdp
biz.event.xDayInterestFree.dispatch.routingKey = key_biz_event_xdayinterestfree_dispatch_xyf_cdp
biz.event.xDayInterestFree.dispatch.queue.name = queue_biz_event_xdayinterestfree_dispatch_xyf_cdp

biz.event.decision.exchange = exchange_biz_event_decision_topic
biz.event.decision.exchangeType = topic
biz.event.decision.routingKey = key_biz_event_decision_xyf_cdp
biz.event.decision.queue.name = queue_biz_event_decision_xyf_cdp

# 触达指标查询方式 1-查询本地库 2-查询数仓
strategy.dispatch.index.query.way = 1

# 数仓URL
cdp.ads.host = http://api-dps.xinfei.io
# 用户中心host
php.user.host = http://api.xinfei.io/user
# 商户后台host
cdp.loanMarket.host = http://api.xinyongpurse.io/loanmarket
xyf.http.connectionRequestTimeout = 20000
xyf.http.socketTimeout = 20000


# 个性化短信host
cdp.sms.personal.host = http://sms.xinfei.io

# 短信回执推送超时时间：单位：小时
sms.report.over.time = 60
#################################### 业务-bi Config
cdp.bi.host = http://bi-model.xinfei.io


# 风险模型分-线程数量&每页条数
refresh.risk.thread.num = 5
refresh.risk.page.size = 1000

usergroup-metadata.host = http://usergroup-metadata.xinfei.cn
message-proxy.host = http://prod-java-message-proxy-service-svc.java-prod:8080

########################################### 数仓pulsar对接 相关 ###########################################

dingtalk.atMobileAds = ***********

dingtalk.adsAlarmUrl = https://oapi.dingtalk.com/robot/send?access_token=8000bde01a3ce758b48e1f654cf1e0c49395c27f90afd4e0525648417d3dbbd4







xf.model.platform.prediction = http://open-business-engine.xinfei.io/marketing_model/prediction
xf.model.platform.prediction.authorization = 9a0f420b2c324cfcb7b23a49d5932c28

xf.model.platform.modellist = http://business-engine-manage.xinfei.io/model/list
xf.model.platform.modellist.authorization = 9a0f420b2c324cfcb7b23a49d5932c28

crowd.refresh.timeout.limit = 15
#特征平台
adb.realTime.variable.gray = {"allHits":false,"variableList":["user_cur_available_balance"]}
adb.realTime.variable.strategy.gray = {"allHits":true,"strategyList":[270,230,320]}
adb.realTime.variable.url = http://data-xdecision.xinfei.io/data/realCollect

###################################银行卡异常#######################################################
loanFinalFailed.failReason.list = {"defaultReason":"other","fundFailedReasonList":null,"reasonList":["risk_failed","fund_failed","orther_app_order","other"]}
loanFinalFailed.fundFailReasonDetail.list = {"defaultReason":"999_其他","fundFailedReasonList":["fund_failed"],"reasonList":["200_风控拒绝","210_风控规则拒绝","220_风控模型拒绝","230_黑名单","240_拒绝后禁申期未满","250_发卡行黑名单","310_人脸比对失败","320_身份证对比失败","330_OCR失败","340_影像件上传异常","350_文件上传失败","410_II/III类卡","420_卡挂失","420_账户冻结","430_销户","440_卡状态异常","450_卡性质限制","460_中止服务","470_户名不符","480_暂停非柜面交易","490_其他卡异常","500_保证金","510_资方额度控制","705_二要素验证失败","710_四要素鉴权失败","720_绑卡失败","910_支付失败","920_客户可用额度","930_地址问题","940_身份证有效期","950_电子签失败","960_参数异常","970_非进件区域","999_其他"]}


###################################激活#######################################################

###nacos新增配置
#####埋点事件上报-1003806借款申请页面浏览##############
###################################埋点事件上报-1004089大卡位点击#######################################################


#################特征平台变量灰度################################
##adb.realTime.variable.strategy.contain=(#label == 'user_cur_available_balance'  or  #label == 'current_utm_soucre_channel' or #label == 'not_login' or #label == 'not_finish_borrow' or #label == 'not_apply_withdraw' or #label == 'user_borrow_channel' or #label == 'is_new_user_not_market_area' or #label == 'is_add_loan_white_list' or #label == 'is_user_forbid_apply' or #label == 'market_black_list' or #label == 'risk_black_list' or  #label == 'user_cur_balance_change' or #label == 'is_start_act_borrow' or #label == 'user_register_channel' or ((#strategy == 230 or #strategy == 184) and (#label == 'not_start_borrow')))
adb.realTime.variable.strategy.contain = (#label == 'available_amt_appuser_id' or #label == 'f_user_current_repayment_status' or #label == 'f_first_order_result_apu' or #label == 'custom_old_or_new_sec_credit' or #label == 'f_last_order_result_apu' or #label == 'last_activation_result_xdata' or #label == 'is_new_have_loan_coucher' or #label == 'is_new_have_repayment_coucher' or #label == 'is_old_have_loan_coucher' or #label == 'is_old_have_repayment_coucher' or #label == 'last_increase_temporary_amount' or #label == 'is_lanzhou_bank_user' or #label == 'api_down_loan_usage_rate'  or #label == 'user_cur_available_balance'  or  #label == 'current_utm_soucre_channel' or #label == 'not_login' or #label == 'not_finish_borrow' or #label == 'not_apply_withdraw' or #label == 'user_borrow_channel' or #label == 'is_new_user_not_market_area' or #label == 'is_add_loan_white_list' or #label == 'is_user_forbid_apply' or #label == 'market_black_list' or #label == 'risk_black_list' or  #label == 'user_cur_balance_change' or #label == 'is_start_act_borrow' or #label == 'user_register_channel' or #label == 'not_start_borrow'  or  #label == 'is_mingzhu_loan' or #label == 'is_hbei_source_user'  or #label == 'f_first_loan_api_first_login_xyf01_times' or #label == 'f_avalible_amt_with_temporary_amt' or #label == 'f_is_adj_type_api_fst_login_flag_apu' or #label == 'f_user_cur_available_balance_whole_hundred_flag')

adb.realTime.variable.strategy.exclusion = false

############用户事件限流#############
eventFlcConfig = {\n    "IterableDataBase": 600,\n    "Start": 600,\n    "ApiCreditEnablenNoApplySuccess": 300,\n    "ApiCreditEnablenNoLoan": 300,\n    "Start1IncreaseCreditLimit": 600,\n    "Login": 600,\n    "CredentialStuffingNotApproved": 600,\n    "CredentialStuffingApproved": 600,\n    "RcsptIncreaseCredit": 600,\n    "RepaySuccess": 600,\n    "RiskActivation": 600,\n    "NewRepaySuccess": 600,\n    "apiCredentialStuffing": 1\n}

###############复筛常规流控关闭###################
singleDispatchFlc.1 = false
singleDispatchFlc.2 = false
singleDispatchFlc.3 = false
singleDispatchFlc.4 = false

######离线人群包限流模块线程池配置###########
dispatchFlcExecutor.pool.coreSize = 8
dispatchFlcExecutor.pool.maxSize = 20

##########告警忽略################
dispatch.reponse.ignore.failedCodes.1 = ["499110", "499104"]
dispatch.ignore.errmsg.1 = ["模板参数不全，终止发送"]
#临时测试
cdp.crowd.taskBatchCount = 500

#####新增配置 **** 周期循环策略配置####
strategyCycleDayConfig = {"digits":2,"bizKey":"mqzy"}

cdp.tele.newHost = http://telemkt.xinfei.io

###灰度人群包配置########
crowd.run.fixedNumerPage.contain = (#crowdId == 379 or #crowdId == 368 or #crowdId == 370)

#####每次获取数量#####
cdp.crowd.rockPageSizeNew = 5000

###################################新增配置，提临额#################################################

###################################放款成功#################################################

label.ding.alarm.switch = true
dispatchAlarmExecutor.pool.coreSize = 4
dispatchAlarmExecutor.pool.maxSize = 8

crowd.execution.time = 5
cdp.ads.crowdHost = http://api-dps.xinfei.io
cdp.strategy.userConvert = true
cdp.crowd.stop.white = [0]
cdp.crowd.alarm = false
dispatchTaskMaxRetryTime = 4

offline.strategy.variable.label = is_last_order_time_equal_or_after_starttime
dispatchVariableExecutor.pool.coreSize = 80
dispatchVariableExecutor.pool.maxSize = 100
rocketmq.enable = true
dispatchTaskExecutor.pool.coreSize = 6
dispatchTaskExecutor.pool.maxSize = 200

strategy.dispatch.channel.push.pagesize = 1000
xf.push-service.url = http://sms.xinfei.io
biz.event.push.dispatch.routingKey = key_biz_event_push_dispatch_xyf_cdp
biz.event.push.dispatch.queue.name = queue_biz_event_push_dispatch_xyf_cdp
dispatch.reponse.ignore.failedCodes.5 = [-1]
strategy.job.excutingErrorsIgnore = ["策略状态暂停或者结束不营销"]
dispatch.ignore.errmsg.5 = ["查询push模板详情接口失败"]
increaseAmtUrl = http://rcs-provider.xinfei.io/psnlInfo/modify-amt-decision
increaseAmtCallbackUrl = http://maque.xinfei.io/api/strategy/increaseAmt-notify
xf.cis-query-service.url = http://cis-query.xinfei.io
offline.adb.realTime.variable.strategy.exclusion = false
offline.adb.realTime.variable.strategy.contain = (#label == 'available_amt_appuser_id' or #label == 'f_user_current_repayment_status' or #label == 'f_first_order_result_apu' or #label == 'custom_old_or_new_sec_credit' or #label == 'f_last_order_result_apu' or #label == 'last_activation_result_xdata' or #label == 'market_black_list' or #label == 'risk_black_list' or #label == 'is_mingzhu_loan' or #label == 'is_hbei_source_user' or #label == 'f_is_30days_inc_amt_less_3_times' or #label == 'is_new_have_loan_coucher' or #label == 'is_new_have_repayment_coucher' or #label == 'is_old_have_loan_coucher' or #label == 'is_old_have_repayment_coucher' or #label == 'last_increase_temporary_amount' or #label == 'user_cur_balance_change' or #label == 'is_start_act_borrow' or  #label == 'not_start_borrow' or  #label == 'is_add_loan_white_list'  or  #label == 'is_user_forbid_apply'  or  #label == 'is_last_order_time_equal_or_after_starttime'  or  #label == 'user_cur_available_balance' or #label == 'not_login' or #label == 'not_finish_borrow' or #label == 'user_register_channel' or #label == 'user_borrow_channel' or #label == 'is_new_user_not_market_area' or #label == 'not_apply_withdraw' or #label == 'f_is_user_in_destined_crowd'  or #label == 'f_first_loan_api_first_login_xyf01_times' or #label == 'f_avalible_amt_with_temporary_amt' or #label == 'f_is_adj_type_api_fst_login_flag_apu' or #label == 'f_user_cur_available_balance_whole_hundred_flag')
minValue.user_cur_available_balance = 499.99
minValue.available_amt_appuser_id = 499.99

dispatchTimesRedisExpire = 7200
dispatchQueryDBExecutor.pool.coreSize = 10
dispatchQueryDBExecutor.pool.maxSize = 200

flowSpecialStartTimeLabels = ["is_last_order_time_equal_or_after_starttime","not_login"]
amountZeroLimitTempParam = ["last_increase_temporary_amount","order_pay_member_amount"]
datafeaturecore.Url = http://datafeaturecore.xinfei.io/api/featurepocket/query/

xf.goods-service.url = http://vipcore.xinfei.io
app-banner.source.url = http://prdocccore.xinfei.io
xf.vipcore-service.url = http://vipcore.xinfei.io
xf.dps-service.url = http://api-dps.xinfei.io
api-hold.ding.alarm.url = https://oapi.dingtalk.com/robot/send?access_token=0b107fc07ded5bcf667a22bf418b3fc7b9fbaa9a5ad69d95dcaf65574afe114d
accessControlUrl = http://prod-java-rcs-provider-server-svc.java-prod:8000/common/access-control

#是否启用配置
sso.client.config = true
#客户端ID，对应的是页面上配置的appCode，子系统的唯一标识
sso.client.clientId = fish-intestines
#是否开启SSO登录拦截
sso.client.authInterceptorFlag = true

#新增配置开关（true使用新sso，false使用老sso）
cdp.new.sso = true
vip-core.tag.match.url = http://vipcore.xinfei.io/tag/match
strategy.dispatch.channel.ai.pronto.pagesize = 200
biz.event.ai.dispatch.routingKey = key_biz_event_ai_dispatch_xyf_cdp
biz.event.ai.dispatch.queue.name = queue_biz_event_ai_dispatch_xyf_cdp
minValue.label = {"available_amt_appuser_id":99.99,"last_increase_temporary_amount":0,"user_cur_available_balance":499.99,"user_cur_balance_change":99.9,"order_pay_member_amount":0}
xf.call-service.url = http://call.xinfei.io
ai.param = [\n    {\n        "paramName": "可用额度-过期仍返回",\n        "paramCode": "available_amt_appuser_id"\n    },\n    {\n        "paramName": "最近一次提临额金额",\n        "paramCode": "last_increase_temporary_amount"\n    },\n    {\n        "paramName": "用户当前可用额度",\n        "paramCode": "user_cur_available_balance"\n    },\n    {\n        "paramName": "提额金额",\n        "paramCode": "user_cur_balance_change"\n    }\n]
dispatchUserDelay.async.switch = 1
ads.feature.fallback.labels.999 = last_order_time_var,first_loan_success_time_var
ads.feature.fallback.labels.blank = last_order_time_var,last_activation_time,first_loan_success_time_var,f_last_payoff_time_days,last_activation_time_xdata,f_is_user_in_magic_mirror_crowd,is_manual_telemkt_available,is_ai_market_available
ads.feature.fallback.labels.9999 = f_first_loan_api_first_login_xyf01_times,f_avalible_amt_with_temporary_amt,f_is_adj_type_api_fst_login_flag_apu,f_user_cur_available_balance_whole_hundred_flag,last_order_time_var,first_loan_api_first_login_xyf01_times,avalible_amt_with_temporary_amt,is_adj_type_api_fst_login_flag_apu,last_activation_time,first_loan_success_time_var,f_last_payoff_time_days,last_activation_time_xdata,f_is_user_in_magic_mirror_crowd,is_manual_telemkt_available,is_ai_market_available
ads.feature.fallback.labels.paramCheckNull = [\n    "f_first_loan_api_first_login_xyf01_times",\n    "f_avalible_amt_with_temporary_amt",\n    "f_is_adj_type_api_fst_login_flag_apu",\n    "f_user_cur_available_balance_whole_hundred_flag",\n    "first_loan_api_first_login_xyf01_times",\n    "avalible_amt_with_temporary_amt",\n    "is_adj_type_api_fst_login_flag_apu",\n    "last_activation_time",\n    "f_last_payoff_time_days",\n    "last_activation_time_xdata"\n]
ads.feature.fallback.labels.valueToNull = [\n    "f_first_loan_api_first_login_xyf01_times",\n    "f_avalible_amt_with_temporary_amt",\n    "f_is_adj_type_api_fst_login_flag_apu",\n    "f_user_cur_available_balance_whole_hundred_flag",\n    "last_order_time_var",\n    "first_loan_api_first_login_xyf01_times",\n    "avalible_amt_with_temporary_amt",\n    "is_adj_type_api_fst_login_flag_apu",\n    "last_activation_time",\n    "first_loan_success_time_var",\n    "f_last_payoff_time_days",\n    "last_activation_time_xdata"\n]
noDelay.strategyId = 1552,1577,1696
ads.feature.need.idcardnumber = [\n    "avalible_amt_with_temporary_amt"\n]
ads.feature.need.custno = [\n    "avalible_amt_with_temporary_amt",\n    "is_on_loan"\n]
loanFinalFailed.reason.codes = {\n    "risk_failed": [\n        "40001"\n    ],\n    "fund_failed": [\n        "XF_LN_4100",\n        "XF_LN_4200",\n        "XF_LN_4201",\n        "XF_LN_4300",\n        "XF_LN_4400",\n        "XF_LN_4500",\n        "XF_LN_4600",\n        "CA_XF_LN_4600",\n        "XF_LN_4700",\n        "XF_LN_4800",\n        "XF_LN_4900"\n    ],\n    "fund_failed_other": [\n        "XF_LN_1000",\n        "XF_LN_2000",\n        "XF_LN_2100",\n        "XF_LN_2200",\n        "XF_LN_2300",\n        "XF_LN_2400",\n        "XF_LN_2500",\n        "XF_LN_3100",\n        "XF_LN_3200",\n        "XF_LN_3300",\n        "XF_LN_3400",\n        "XF_LN_3500",\n        "XF_LN_5000",\n        "XF_LN_5100",\n        "XF_LN_5200",\n        "XF_LN_7050",\n        "XF_LN_7100",\n        "XF_LN_7200",\n        "XF_LN_9100",\n        "XF_LN_9200",\n        "XF_LN_9300",\n        "XF_LN_9400",\n        "XF_LN_9500",\n        "XF_LN_9600",\n        "XF_LN_9700",\n        "XF_LN_9800",\n        "XF_LN_9990",\n        "CA_XF_LN_2000",\n        "CA_XF_LN_2100",\n        "CA_XF_LN_2200",\n        "CA_XF_LN_2300",\n        "CA_XF_LN_2400",\n        "CA_XF_LN_4600",\n        "CA_XF_LN_9200",\n        "CA_XF_LN_9700",\n        "CA_XF_LN_3700"\n    ]\n}
http.platform-service.key = xi4udy10sdy
http.platform-service.host = http://api.xinfei.io/platform-service-api
DispatchUserDelayExecutor.pool.coreSize = 20
DispatchUserDelayExecutor.pool.maxSize = 60
appBanner.decisionRecord.switch = 1
ads.hasUserRecord.byIn = 1
xf.user-device-service.url = http://user-device.xinfei.io
xf.model.platform.prediction.param.supplement.models = [\n    "T1_Xday_free_APP_TEST",\n    "APP_T0_mkt",\n    "T1_Xday_free_APP",\n    "jinjing_increaseAmount",\n    "APP_settle_check_all_v2_beta",\n    "APP_onloan_check_all_v2_beta",\n    "APP_increase_amt_t0_v1_beta",\n    "APP_increase_amt_t1_t29_v1_beta",\n    "APP_login_t0_v1_beta",\n    "APP_login_t0_60min_v1_beta",\n    "APP_settle_t7_v1_beta",\n    "APP_common_v1_beta",\n    "APP_repay_day_all_v1_beta",\n    "APP_banned_t1_v1_beta",\n    "APP_login_t2_v1_beta",\n    "APP_t1_all_99",\n    "APP_t1_all",\n    "API_reLoan_adjLmt_fstLgn_T0",\n    "API_reLoan_check_T0",\n    "API_reLoan_offline",\n    "API_reLoan_login_T0",\n    "API_reLoan_repay_T0",\n    "API_reLoan_aply_T0",\n    "API_reLoan_loanCmmt_T0",\n    "reLoan_price_24_offline",\n    "API_reLoan_popup",\n    "APP_settle_t0",\n    "APP_reloan_popup",\n    "API_reLoan_price_24_offline",\n    "APP_reLoan_price_24_offline",\n    "APP_chunjie_increase",\n    "API_chunjie_increase",\n    "APP_t1_all_tel",\n    "API_reLoan_offline_tel",\n    "APP_check_white_list_test",\n    "API_reLoan_price_dec_camp"\n]
cis.secure.enable = true
cis.secure.secret = nS1bJ4oC4gP8kJ7
bank-core.url = http://bank-core.xinfei.io/multi/queryBankCardListHistoryByUserNo
npay-api.url = http://api.xinfei.io/pay-center/agreement/queryCardSignInfo
ads.feature.need.acctno = [\n    "f_is_bill_date_and_no_settle"\n]
activity_register_module_pack = {\n    "2": [\n        1475,\n        1443\n    ]\n}
vipcore.url = http://vipcore.xinfei.io/tag/match
xyf-vip-system.url = https://xyf-vip-system.xinfei.cn/rpc/tag/improve-times
ads.feature.need.userno = [\n    "vc_core_is_vip",\n    "f_last_payoff_time_days",\n    "f_is_airline_travel_user"\n]
xf.model.platform.prediction.param.account.supplement.models = [\n    "APP_Amt_Increase_T0_doudi"\n]
xf.datafeaturecore.url = http://datafeaturecore.xinfei.io
activity.biz.records = [\n    {\n        "prefix": "136****5690 刚刚获得",\n        "amount": "20元"\n    },\n    {\n        "prefix": "158****2345 5分钟前获得",\n        "amount": "50元"\n    },\n    {\n        "prefix": "189****7812 2小时前获得",\n        "amount": "80元"\n    },\n    {\n        "prefix": "137****4521 30分钟前获得",\n        "amount": "100元"\n    },\n    {\n        "prefix": "186****9903 10秒前获得",\n        "amount": "10元"\n    },\n    {\n        "prefix": "152****3789 45分钟前获得",\n        "amount": "25元"\n    },\n    {\n        "prefix": "138****6674 1小时前获",\n        "amount": "得5元"\n    },\n    {\n        "prefix": "159****7745 15分钟前获得",\n        "amount": "65元"\n    },\n    {\n        "prefix": "185****3421 3小时前获得",\n        "amount": "100元"\n    },\n    {\n        "prefix": "187****6632 刚刚获得",\n        "amount": "30元"\n    },\n    {\n        "prefix": "134****0987 6分钟前获得",\n        "amount": "15元"\n    },\n    {\n        "prefix": "155****8793 2分钟前获得",\n        "amount": "50元"\n    },\n    {\n        "prefix": "176****7643 20秒前获得",\n        "amount": "75元"\n    },\n    {\n        "prefix": "158****0912 2小时前获得",\n        "amount": "40元"\n    },\n    {\n        "prefix": "139****6871 7分钟前获得",\n        "amount": "10元"\n    },\n    {\n        "prefix": "136****9908 1小时前获得",\n        "amount": "95元"\n    },\n    {\n        "prefix": "188****5632 10分钟前获得",\n        "amount": "70元"\n    },\n    {\n        "prefix": "181****4553 12秒前获得",\n        "amount": "50元"\n    },\n    {\n        "prefix": "135****2134 3小时前获得",\n        "amount": "30元"\n    },\n    {\n        "prefix": "178****7766 8分钟前获得",\n        "amount": "5元"\n    },\n    {\n        "prefix": "151****4345 4小时前获得",\n        "amount": "90元"\n    },\n    {\n        "prefix": "186****9072 2分钟前获得",\n        "amount": "60元"\n    },\n    {\n        "prefix": "159****3728 刚刚获得",\n        "amount": "15元"\n    },\n    {\n        "prefix": "138****7684 30秒前获得",\n        "amount": "35元"\n    },\n    {\n        "prefix": "152****7834 6小时前获得",\n        "amount": "100元"\n    },\n    {\n        "prefix": "187****5498 50秒前获得",\n        "amount": "80元"\n    },\n    {\n        "prefix": "156****2337 1分钟前获得",\n        "amount": "70元"\n    },\n    {\n        "prefix": "139****6654 40分钟前获得",\n        "amount": "20元"\n    },\n    {\n        "prefix": "133****7920 2分钟前获得",\n        "amount": "25元"\n    },\n    {\n        "prefix": "188****8901 刚刚获得",\n        "amount": "10元"\n    },\n    {\n        "prefix": "158****4596 5分钟前获得",\n        "amount": "50元"\n    },\n    {\n        "prefix": "184****1276 1小时前获得",\n        "amount": "95元"\n    },\n    {\n        "prefix": "136****7755 2小时前获得",\n        "amount": "85元"\n    },\n    {\n        "prefix": "153****9827 10分钟前获得",\n        "amount": "15元"\n    },\n    {\n        "prefix": "185****4534 8秒前获得",\n        "amount": "5元"\n    },\n    {\n        "prefix": "177****5621 15分钟前获得",\n        "amount": "45元"\n    },\n    {\n        "prefix": "158****8741 3小时前获得",\n        "amount": "100元"\n    },\n    {\n        "prefix": "151****3245 刚刚获得",\n        "amount": "50元"\n    },\n    {\n        "prefix": "133****2378 20秒前获得",\n        "amount": "90元"\n    },\n    {\n        "prefix": "186****9887 6分钟前获得",\n        "amount": "35元"\n    },\n    {\n        "prefix": "182****1134 4小时前获得",\n        "amount": "25元"\n    },\n    {\n        "prefix": "154****3349 10秒前获得",\n        "amount": "20元"\n    },\n    {\n        "prefix": "137****9823 50分钟前获得",\n        "amount": "55元"\n    },\n    {\n        "prefix": "159****7264 1分钟前获得",\n        "amount": "65元"\n    },\n    {\n        "prefix": "152****9931 3分钟内获得",\n        "amount": "30元"\n    },\n    {\n        "prefix": "188****0992 5秒前获得",\n        "amount": "40元"\n    },\n    {\n        "prefix": "178****0124 1小时前获得",\n        "amount": "85元"\n    },\n    {\n        "prefix": "187****6793 10分钟前获得",\n        "amount": "10元"\n    },\n    {\n        "prefix": "134****2335 刚刚获得",\n        "amount": "75元"\n    },\n    {\n        "prefix": "136****3248 3小时前获得",\n        "amount": "50元"\n    }\n]
activity.remote.switch = {\n    "sendCouponEnable": true,\n    "dataFeatureEnable": true,\n    "XxlJobTelStrategyDispatchDelayEnable": true,\n    "NewTrackingLoanApplyViewMsgConsumerEnable": true,\n    "NewTrackingCardClickMsgConsumerEnable": true,\n    "NewRiskTempCreditMsgConsumerEnable": true,\n    "ApiCredentialStuffingMsgConsumerEnable": true\n}
activity.coupon.Inflate.config = [\n    {\n        "activityId": 3,\n        "startTime": "2025-01-23 00:00:00",\n        "endTime": "2025-02-04 23:59:00",\n        "rewardName": "优惠券膨胀奖励1",\n        "rewardType": "1",\n        "rewardId": "608",\n        "rewardPriority": "2",\n        "rewardCrowdPack": "cny_sale_promotion_not_withdraw_group",\n        "rewardFallback": 0\n    },\n    {\n        "activityId": 3,\n        "startTime": "2025-01-23 00:00:00",\n        "endTime": "2025-02-04 23:59:00",\n        "rewardName": "优惠券膨胀奖励2",\n        "rewardType": "1",\n        "rewardId": "610",\n        "rewardPriority": "1",\n        "rewardCrowdPack": "cny_sale_promotion_credit_unfinished_group",\n        "rewardFallback": 1\n    }\n]
activity.interface.switch = {\n"checkUserEligibility":100,\n"participateLottery":100,\n"couponInflation":100,\n"registerLottery":100,\n"activityDetail":100,\n"recordsBroadcast":100\n}
activity.user.feature.params = { \n    "f_cust_no_last_order_time": [\n        "cust_no"\n    ], "f_is_user_in_magic_mirror_crowd": [\n        "user_no",\n        "random_number"\n    ], "f_user_current_repayment_status": [\n        "user_no","cust_no","app"\n    ],"f_is_airline_travel_user": [\n        "cust_no",\n        "user_no"\n    ],"is_on_loan":["cust_no"],\n   "f_is_bill_date_and_no_settle":["acct_no"]\n}
activity.datafeature.app.accesskey = 33011a76fbc84334878e0fbdb3904bf9
activity.biz.register.feature = {\n    "3": ["cny_sale_promotion_group"],\n    "4": ["lantern_festival_group_app_on_loan_show"]\n}
activity.user.apply.feature.code = f_cust_no_last_order_time
activity.interface.whitelist = 23930310562472,293930310562472
DataInsightWhiteListCrowd = [1561]
data.insight.switch = 0
xf.datainsight-service.url = http://datainsight.xinfei.io
ads.feature.replace.config = ["last_activation_time","last_activation_time_xdata"]
dispatchEngineExecutor.pool.maxSize = 200
dispatchEngineExecutor.pool.coreSize = 80
cdp.http.client.maxPerRoute = 60
cdp.http.client.maxTotal = 400
ads.feature.need.custno.default.null = [\n    "f_is_airline_travel_user"\n]
xf.enginepredictcenter.url = http://enginepredictcenter.xinfei.io
common.gray.switch = {\n    "newFlowCtrlSwitch": {\n        "percentNum": 100,\n        "whitelist": "1319"\n    },\n    "labelDataFeatureSwitch": {\n        "percentNum": 0,\n        "whitelist": "0"\n    },\n    "strategyDataFeatureSwitch": {\n        "percentNum": 100,\n        "whitelist": "0"\n    },\n    "enableDataFeatureSwitch": {\n        "percentNum": 0,\n        "whitelist": "f_is_user_in_magic_mirror_crowd,is_manual_telemkt_available,is_ai_market_available,is_mingzhu_loan,is_lanzhou_bank_user,last_order_time_var,not_login,is_last_order_time_equal_or_after_starttime,user_register_channel,f_user_current_repayment_status,is_start_act_borrow,risk_black_list,user_borrow_channel,market_black_list,is_user_forbid_apply,vc_core_is_vip,f_last_order_result_apu,f_is_user_in_destined_crowd,is_adj_type_api_fst_login_flag_apu,is_on_loan,last_activation_time_xdata,not_start_borrow,is_old_have_loan_coucher,first_loan_success_time_var,f_is_airline_travel_user,custom_old_or_new_sec_credit,last_activation_result_xdata,f_user_cur_available_balance_whole_hundred_flag,is_new_user_not_market_area,f_is_bill_date_and_no_settle,f_is_30days_inc_amt_less_3_times,old_customer_fxk_transform_xyf01,user_is_old_cust,f_user_mobile_city,f_user_gps_city,available_amt_appuser_id"\n    },\n    "dataFeatureLogSwitch": {\n        "percentNum": 100,\n        "whitelist": "0"\n    },\n    "offlineEngineFilterSwitch": {\n        "percentNum": 0,\n        "whitelist": "0"\n    },"newFlowCtrlRuleSwitch":{\n        "percentNum": 100,\n        "whitelist": "2461"\n    },"offEngineStrategyDispatchSwitch":{\n        "percentNum": 0,\n        "whitelist": "2566,2573,2575,2626,2636,2641"\n    }\n}
model.query.gary = ["API_T0_mkt","API_Credit_failed","API_reLoan_offline_tel","API_reLoan_price_24_offline","APP_t1_v2_tel","APP_t1_all_tel","APP_settle_t0","API_reLoan_repay_T0","test_order_no","zhaungkutest","Tel_notLoan_MOT_ZK_test","APP_loan_t0_test_v1","lk_wanliu_tanchuang_test","test_wbf_mkt_weekend","notCredit_ocr_push_APP_T0","APP_check_white_list_test","APP_coupon_popup","APP_popup_whitelist","APP_chunjie_increase","API_chunjie_increase","APP_t1_all_tel_supl","APP_onloan_check_all_v3","APP_settle_check_all_v3","hsf_mkt_notcredit_ai","hsf_mkt_notcredit_sms","hsf_mkt_notloan_ai","hsf_mkt_notloan_sms","Tel_notCredit_MOT_Login_lx","dxm_score_notloan","tc_score_notloan","yt_score_notloan","bh_score_notloan","hujin_report_mkt_03","dxm_score_sms_notCredit","bh_score_sms_notCredit","tc_score_sms_notCredit","yt_score_sms_notCredit","hujin_report_test_04","hujin_report_online_test","notcredit_ab_test","Engine_test_mkt","Tel_notLoan_MOT_CardError","APP_notLoan_Amt_Increase","API_Tel_Retrieve","T1_24_up_APP","wuyi_xcx_activity_test","API_reLoan_popup","API_reLoan_offline_test","API_T0_Increase","API_withdraw_failed_Tele","Tel_notLoan_LifeCycle_T0","NTel_notLoan_MOT_Crash","APP_notLoan_sms_V2","APP_Amt_Increase_T0_doudi","APP_wanliu_tanchuang","input_huidu_test","APPTIE_sms_push","NTel_notLoan_MOT_CardError","NTel_notLoan_MOT_reject","activity_25yuanxiao_offer","Travel_Mz_T0_Increase","Travel_push_test","T3T6_24_up_APP","sms_notloan_login","wuyi_xcx_activity","notcredit_notloan_AB","API_V5_T1to15","APIcheck_realtime_tele","APIcheck_sms","Login_sms_1030","Login_blocked","API_T1_Tel_12012024","Tel_notCredit_LifeCycle_T0","NTel_notLoan_LifeCycle_T0","APP_notLoan_Amt_Increase_T0","REG_7day_free","T1_Xday_free_APP","T0_24_up_APP","notloan_sms_push_APP_T0","notloan_sms_push_APP","API_general","API_LTC_mkt","Tel_notCredit_Exposure","Tel_notCredit_Media","APP_reLoan_price_24_offline","API_reLoan_adjLmt_fstLgn_T0","API_reLoan_aply_T0","API_reLoan_loanCmmt_T0"]
kafka.bootstrap.servers = alikafka-pre-cn-yzh477omz00c-1-vpc.alikafka.aliyuncs.com:9092,alikafka-pre-cn-yzh477omz00c-2-vpc.alikafka.aliyuncs.com:9092,alikafka-pre-cn-yzh477omz00c-3-vpc.alikafka.aliyuncs.com:9092
T0EventCountProducer = 1
BizKeyXinKeYunYing2025 = xinkeyunying2025
cdp.coupon.useNewRoutes = true
cdp.coupon.newHost = http://userassetcore.xinfei.io
xf.distributecore.url = http://distributecore.xinfei.io/quota/amountQuery
NewRegisterHandler.count = 3
chgctrl.client.base.serverAddress = http://prod-java-chgctrlmng-svc.java-prod:8080
chgctrl.client.base.platform = xyf-cdp
alarm.custom.task.duration = {\n    "1954":190,\n    "1991":250,\n    "2180":70,\n    "2264":65\n}
xf.xyf-cdp.web.url = https://sso.xinfei-inc.cn/
ReDecisionWhiteList = [\n    "ALL",\n    "2458",\n    "2302",\n    "936",\n    "1603",\n    "2432",\n    "2472",\n    "2480"\n]
CrowdSliceService.crowSliceChunkSize = 5242880
oss.endpoint.buket.config = {\n    "endpoint": "https://oss-cn-beijing-internal.aliyuncs.com",\n    "bucketName": "prod-datainsight-oss",\n    "region": "cn-beijing",\n    "accessKeyId": "LTAI5tCtaubJPSYnb3YLSiMp",\n    "accessKeySecret": "******************************"\n}
InsightCrowdPack.WhiteList.BusinessLine = [\n    "test-cust"\n]
InsightCrowdPack.WhiteList.CrowdPack = [\n    782,\n    1520,\n    200,\n    198,\n    555,\n    1207,\n    199,\n    197,\n    1674,\n    1069,\n    1267,\n    1383,\n    1466,\n    1536,\n    1537,\n    1670\n]
InsightCrowdPack.WhiteList.FallbackSlice.CrowdPack = [-1]
xf.datainsightcore.url = http://datainsightcore.xinfei.io
datainsightcoreAccessKey = 1d8338fcf99e450686b1e6ccbf7ed390
xf.datainsight.url = http://dataInsight.xinfei.io
datainsightAccessKey = 1d8338fcf99e450686b1e6ccbf7ed390
CrowdNoticeMqConsumerClose = 0
crowdSyncFallBackTaskClose = 0
CrowdSyncTaskClose = 1
InsightCrowdPack.BizLineMapping = {\n    "old-cust": "老客",\n    "new-cust": "新客",\n    "test-cust": "测试业务线",\n    "loan-overload-cust": "贷超",\n    "consuer-loan-cust": "航旅"\n}
OssCrowdPack.WhiteList.OssPackage = [673,723,1642,1643]
distribute.task.page.size = 10
