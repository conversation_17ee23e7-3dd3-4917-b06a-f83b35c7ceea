package com.xftech.cdp.feign.service;

import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import com.xftech.cdp.feign.PrdOccCoreFeignClient;
import com.xftech.cdp.feign.model.prdocc.PopupCheckRequest;
import com.xftech.cdp.feign.model.prdocc.PopupCheckResponse;
import com.xftech.cdp.feign.model.prdocc.PrdOccResponse;

import com.alibaba.fastjson.JSONObject;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.google.common.base.Stopwatch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class PrdOccCoreService {

    @Resource
    private PrdOccCoreFeignClient prdOccCoreFeignClient;

    /**
     * 检查弹窗合法性
     */
    @Cached(name = "popupCheckCache", cacheType = CacheType.LOCAL, expire = 60)
    public boolean popupCheck(Integer popupId) {
        try {
            Stopwatch stopwatch = Stopwatch.createStarted();
            PopupCheckRequest request = new PopupCheckRequest();
            request.setPopupId(popupId);
            PrdOccResponse<PopupCheckResponse> response = prdOccCoreFeignClient.popupCheck(request);
            log.info("PrdOccCoreService popupCheck ms={} request={}, response={}", stopwatch.stop().elapsed(TimeUnit.MILLISECONDS), JSONObject.toJSONString(request), JSONObject.toJSONString(response));

            if (response != null && response.getData() != null) {
                PopupCheckResponse checkResult = response.getData();
                Boolean exist = checkResult.getExist();
                Boolean statusEffective = checkResult.getStatusEffective();
                Boolean timeEffective = checkResult.getTimeEffective();
                return (exist != null && exist) && (statusEffective != null && statusEffective) && (timeEffective != null && timeEffective);
            }
        } catch (Exception e) {
            log.error("PrdOccCoreService popupCheck error", e);
        }
        return true;
    }

}
