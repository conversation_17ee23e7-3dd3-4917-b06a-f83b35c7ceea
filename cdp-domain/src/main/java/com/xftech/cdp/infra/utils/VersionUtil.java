package com.xftech.cdp.infra.utils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @version v1.0 2025/5/8
 * @description VersionUtil
 */
public class VersionUtil {

    /**
     * 从版本号中提取前8位日期
     *
     * @param version 格式为YYYYMMDDxxx的Integer版本号（如20231015001）
     * @return 前8位日期（如20231015）
     * @throws IllegalArgumentException 版本号不符合要求时抛出异常
     */
    public static Long extractDateFromVersion(Long version) {
        if (version == null) {
            throw new IllegalArgumentException("VersionUtil 版本号不能为空");
        }
        // 转换为字符串（保留前导零）
        String versionStr = String.valueOf(version);
        // 校验长度
        if (versionStr.length() < 8) {
            throw new IllegalArgumentException("VersionUtil 版本号长度不足8位");
        }
        // 截取前8位
        String dateStr = versionStr.substring(0, 8);
        try {
            return Long.parseLong(dateStr);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("VersionUtil 版本号格式无效");
        }
    }

    /**
     * 判断版本号是否为今天的版本
     *
     * @param version
     * @return
     */
    public static boolean isTodayVersion(Long version) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        LocalDate inputDate = LocalDate.parse(String.valueOf(extractDateFromVersion(version)), formatter);
        // 获取今天日期
        LocalDate today = LocalDate.now();
        // 比较是否相等
        return inputDate.equals(today);
    }

}

