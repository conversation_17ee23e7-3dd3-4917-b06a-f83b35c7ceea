<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="adsOprtAutoMessageDf">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.ads.po.AdsOprtAutoMessageDfDo">
        <result column="pt" property="pt" />
        <result column="user_id" property="userId" />
        <result column="sms_flg" property="smsFlg" />
        <result column="app" property="app" />
        <result column="inner_app" property= "innerApp" />
        <result column="mobile" property="mobile" />
        <result column="user_type" property="userType" />
        <result column="run_time" property="runTime" />
        <result column="ab_num" property="abNum" />
        <result column="app_user_id_last2" property="appUserIdLast2" />
    </resultMap>

    <resultMap id="CrowdWereHouseDTO" type="com.xftech.cdp.domain.crowd.model.dispatch.CrowdWereHouse">
        <result column="app_user_id" property="appUserId" />
        <result column="app" property="app" />
        <result column="inner_app" property= "innerApp" />
        <result column="mobile" property="mobile" />
        <result column="ab_num" property="abNum" />
        <result column="app_user_id_last2" property="appUserIdLast2" />
    </resultMap>

    <select id="queryOprtAutoMessageDf" resultMap="CrowdWereHouseDTO">
        ${sql}
    </select>


    <select id="selectAutoMessageMaxAppUserId" resultType="java.lang.Long">
        select max(user_id) from ads_oprt_auto_message_df
    </select>

    <select id="selectAutoMessageMinAppUserId" resultType="java.lang.Long">
        select min(user_id) from ads_oprt_auto_message_df
    </select>



</mapper>
