<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="crowdDetailSub">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo">
        <id column="id" property="id"/>
        <result column="crowd_id" property="crowdId"/>
        <result column="user_id" property="userId"/>
        <result column="app" property="app"/>
        <result column="inner_app" property="innerApp"/>
        <result column="crowd_exec_log_id" property="crowdExecLogId"/>
        <result column="ab_num" property="abNum"/>
        <result column="app_user_id_last2" property="appUserIdLast2"/>
        <result column="register_time" property="registerTime"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="d_flag" property="dFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , crowd_id, user_id, app, inner_app, crowd_exec_log_id,ab_num,app_user_id_last2, register_time, created_time, updated_time, d_flag
    </sql>

    <select id="selectTableLastRecord" resultMap="BaseResultMap">
        select id, created_time
        from ${tableName}
        order by id desc
        limit 1
    </select>

    <select id="countCrowdDetail" resultType="integer">
        select count(*)
        from ${tableName}
    </select>

    <update id="truncateTable">
        truncate table ${tableName}
    </update>

    <!-- 不再使用 -->
    <insert id="insertBatch">
        insert into ${tableName}
        (crowd_id,user_id, app, inner_app, crowd_exec_log_id, ab_num, app_user_id_last2, register_time, created_time,
        updated_time)
        values
        <foreach collection="crowdDetailList" item="item" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.crowdId}, #{item.userId}, #{item.app}, #{item.innerApp}, #{item.crowdExecLogId},
                #{item.abNum}, #{item.appUserIdLast2}, #{item.registerTime}, now(), now(),
            </trim>
        </foreach>
    </insert>

    <insert id="saveCrowdDetailSub">
        insert into ${tableName}
        (crowd_id,user_id, app, inner_app, crowd_exec_log_id, ab_num, app_user_id_last2, register_time, created_time,
        updated_time)
        values (#{crowdId}, #{userId}, #{app}, #{innerApp}, #{crowdExecLogId},
        #{abNum}, #{appUserIdLast2}, #{registerTime}, now(), now())
    </insert>

</mapper>
