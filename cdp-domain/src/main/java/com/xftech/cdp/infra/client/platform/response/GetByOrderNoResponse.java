package com.xftech.cdp.infra.client.platform.response;

import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0 2024/10/25
 * @description GetByOrderNoResponse
 */
@Data
public class GetByOrderNoResponse {

    //private String id;
    //private String user_id;
    //private String product_no;
    //private String product_name;
    //private String product_logo_url;
    private String order_no;
    //private String loan_order_no;
    //private String person_contract_id;
    //private int user_bind_card_id;
    private Integer status;
    //private String submit_time;
    //private int is_del;
    //private String failed_reason;
    //private int can_reloan;
    //private Date audit_time;
    //private String audit_amount;
    //private String audit_min_amount;
    //private String audit_max_amount;
    //private String audit_step_amount;
    //private String audit_periods;
    //private String audit_overtime;
    //private String amount;
    //private int period;
    //private String rate_tag;
    //private String loan_apply_time;
    //private Date loan_audit_time;
    //private Date loan_remit_time;
    //private String loan_audit_overtime;
    //private int is_new_user;
    //private String download_app_url;
    //private String distribute_no;
    //private String device_id;
    //private String source_type;
    //private String app_version;
    //private String sdk_id;
    //private String os;
    //private String os_version;
    //private String remoteip;
    //private String app;
    //private String inner_app;
    //private String h5_type;
    //private String loan_app;
    //private String loan_inner_app;
    //private String loan_h5_type;
    //private Date created_time;
    //private Date updated_time;
    //private int is_sync_approval;
    //private int is_sync_loan;
    //private int fund_status;
    //private int is_supplemental_loan;
    //private String approval_failed_code;
    //private String approval_success_sms_time;

}
