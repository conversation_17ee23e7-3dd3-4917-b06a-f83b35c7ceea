<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="crowdLabelPrimary">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdLabelPrimaryDo">
        <id column="id" property="id" />
        <result column="crowd_id" property="crowdId" />
        <result column="label_group_type" property="labelGroupType" />
        <result column="primary_label" property="primaryLabel" />
        <result column="primary_label_relation" property="primaryLabelRelation" />
        <result column="exec_index" property="execIndex" />
        <result column="created_time" property="createdTime" />
        <result column="updated_time" property="updatedTime" />
        <result column="d_flag" property="dFlag" />
        <result column="created_op" property="createdOp" />
        <result column="updated_op" property="updatedOp" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, crowd_id, label_group_type, primary_label, primary_label_relation, exec_index, created_time, updated_time, d_flag, created_op, updated_op
    </sql>

    <select id="selectListByCrowdId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crowd_label_primary where crowd_id = #{crowdId} and d_flag = 0 order by exec_index
    </select>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdLabelPrimaryDo" useGeneratedKeys="true">
        insert into crowd_label_primary
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="crowdId != null">
                crowd_id,
            </if>
            <if test="labelGroupType != null">
                label_group_type,
            </if>
            <if test="primaryLabel != null">
                primary_label,
            </if>
            <if test="primaryLabelRelation != null">
                primary_label_relation,
            </if>
            <if test="execIndex != null">
                exec_index,
            </if>
            <if test="createdOp != null">
                created_op,
            </if>
            <if test="updatedOp != null">
                updated_op,
            </if>
            created_time,
            updated_time,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="crowdId != null">
                #{crowdId,jdbcType=BIGINT},
            </if>
            <if test="labelGroupType != null">
                #{labelGroupType,jdbcType=SMALLINT},
            </if>
            <if test="primaryLabel != null">
                #{primaryLabel,jdbcType=SMALLINT},
            </if>
            <if test="primaryLabelRelation != null">
                #{primaryLabelRelation,jdbcType=SMALLINT},
            </if>
            <if test="execIndex != null">
                #{execIndex,jdbcType=SMALLINT},
            </if>
            <if test="createdOp != null">
                #{createdOp,jdbcType=VARCHAR},
            </if>
            <if test="updatedOp != null">
                #{updatedOp,jdbcType=VARCHAR},
            </if>
            now(),
            now(),
        </trim>
    </insert>

    <delete id="deleteByCrowdId">
        update crowd_label_primary
        set d_flag = 1
        where crowd_id = #{crowdId}
    </delete>

    <delete id="deleteById">
        update crowd_label_primary
        set d_flag = 1
        where id = #{id}
    </delete>

</mapper>
