package com.xftech.cdp.domain.subtable.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.infra.repository.subtable.po.SplitTableRoute;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class SplitTableRouteRepository {
    public SplitTableRoute selectSplitTableRouteById(Long id) {
        return DBUtil.selectOne("splitTableRoute.selectSplitTableRouteById", id);
    }

    public List<SplitTableRoute> selectTableNos(Long crowdExecLogId) {
        return DBUtil.selectList("splitTableRoute.selectTableNos", crowdExecLogId);
    }

    public List<SplitTableRoute> selectExpireRecord(String tableName, LocalDate expireDate) {
        Map<String, Object> param = new HashMap<>();
        param.put("tableName", tableName);
        param.put("expireDate", expireDate);
        return DBUtil.selectList("splitTableRoute.selectExpireRecord", param);
    }

    public boolean existSplitTableRoute(Long crowdExecLogId, int tableNo) {
        Map<String, Object> params = new HashMap<>();
        params.put("crowdExecLogId", crowdExecLogId);
        params.put("tableNo", tableNo);
        return DBUtil.selectOne("splitTableRoute.existSplitTableRoute", params) != null;
    }

    public boolean updateDflagByTableNo(int tableNo) {
        return DBUtil.update("splitTableRoute.updateDflagByTableNo", tableNo) > 0;
    }

    public boolean insertSplitTableRoute(SplitTableRoute record) {
        return DBUtil.insert("splitTableRoute.insertSplitTableRoute", record) > 0;
    }

    public boolean updateSplitTableRouteById(SplitTableRoute record) {
        return DBUtil.update("splitTableRoute.updateSplitTableRouteById", record) > 0;
    }
}
