package com.xftech.cdp.infra.utils;


import jdk.nashorn.internal.runtime.logging.Logger;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version $ BigDecimalUtils, v 0.1 2025/1/12 16:49 mingwen.zang
 */
@Logger
public class BigDecimalUtils {
    public static BigDecimal toBigDecimal(Object value, Integer defaultValue) {
        if (value == null) {
            if (defaultValue != null)  {
                return new BigDecimal(defaultValue);
            } else {
                return null;
            }
        }
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        } else if (value instanceof Number) {
            return BigDecimal.valueOf(((Number) value).doubleValue());
        } else if (value instanceof String) {
            try {
                return new BigDecimal((String) value);
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("Invalid number format: " + value, e);
            }
        } else {
            throw new IllegalArgumentException("Unsupported type: " + value.getClass().getName());
        }
    }
}
