package com.xftech.cdp.domain.strategy.model.group;

import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSON;
import com.xftech.cdp.domain.strategy.exception.StrategyException;
import com.xftech.cdp.domain.strategy.model.dto.StrategyGroupConfig;
import com.xftech.cdp.domain.strategy.model.enums.StrategyGroupTypeEnum;

import java.util.List;
import java.util.Objects;
import java.util.function.BiPredicate;

/**
 * 策略分组批次
 *
 * @<NAME_EMAIL>
 * @date 2023/3/1 16:31
 */
public class StrategyGroupMatch {

    private static final Integer ONE_DIGITS = 1;

    /**
     * 获取分组匹配规则
     *
     * @param groupTypeEnum 分组类型
     * @param groupConfig   分组配置
     * @return 匹配规则
     */
    public static BiPredicate<String, Integer> condition(StrategyGroupTypeEnum groupTypeEnum, String groupConfig) {
        StrategyGroupConfig configOption = JSON.parseObject(groupConfig, StrategyGroupConfig.class);
        switch (groupTypeEnum) {
            case LAST_UID_2:
                return (randomNumber, userIdLastTwoDigits) -> lastTwoDigits(String.valueOf(userIdLastTwoDigits), configOption);
            case RANDOM:
            case NEW_RANDOM:
                return (randomNumber, userIdLastTwoDigits) -> randomNumber(groupTypeEnum, String.valueOf(randomNumber), configOption);
            default:
                throw new StrategyException(String.format("分组类型异常：%s", groupTypeEnum));
        }
    }

    /**
     * 随机数
     *
     * @param randomNumber 用户随机数
     * @param configOption 分组配置
     * @return 匹配结果
     */
    private static boolean randomNumber(StrategyGroupTypeEnum groupTypeEnum, String randomNumber, StrategyGroupConfig configOption) {
        if (groupTypeEnum == StrategyGroupTypeEnum.NEW_RANDOM) {
            return componentRandomNumber(randomNumber, configOption);
        }
        if (configOption.getDigits() == ONE_DIGITS) {
            return oneDigitRandomNumber(randomNumber, configOption);
        }
        return twoDigitRandomNumber(randomNumber, configOption);
    }

    /**
     * uid后两位匹配逻辑
     *
     * @param userIdLastTwoDigits uid后两位
     * @param configOption        分组配置
     * @return 匹配结果
     */
    private static boolean lastTwoDigits(String userIdLastTwoDigits, StrategyGroupConfig configOption) {
        return range(userIdLastTwoDigits, configOption);
    }

    /**
     * 一位随机数匹配逻辑
     *
     * @param randomNumber 随机数
     * @param configOption 分组配置
     * @return 匹配结果
     */
    private static boolean oneDigitRandomNumber(String randomNumber, StrategyGroupConfig configOption) {
        Long lastTwoDigits = Long.valueOf(truncate(randomNumber, configOption));
        return configOption.getCrowdLabelOption().getItems().stream().anyMatch(item -> Objects.equals(item, lastTwoDigits));
    }

    /**
     * 两位随机数匹配逻辑
     *
     * @param randomNumber 随机数
     * @param configOption 分组配置
     * @return 匹配结果
     */
    private static boolean twoDigitRandomNumber(String randomNumber, StrategyGroupConfig configOption) {
        return range(truncate(randomNumber, configOption), configOption);
    }

    /**
     * 随机数组件匹配逻辑
     *
     * @param randomNumber 随机数
     * @param configOption 分组配置
     * @return 匹配结果
     */
    private static boolean componentRandomNumber(String randomNumber, StrategyGroupConfig configOption) {
        return range(randomNumber, configOption);
    }

    /**
     * 匹配范围
     *
     * @param value        值
     * @param configOption 分组配置
     * @return 匹配结果
     */
    private static boolean range(String value, StrategyGroupConfig configOption) {
        List<StrategyGroupConfig.Segment> segments = configOption.getCrowdLabelOption().getSegments();
        for (StrategyGroupConfig.Segment segment : segments) {
            Long userValue = Long.valueOf(value);
            if (segment.getMin() <= userValue && userValue <= segment.getMax()) {
                return true;
            }
        }
        return false;
    }

    /**
     * 根据分组配置截取随机数
     *
     * @param randomNumber 用户随机数
     * @param configOption 分组配置
     * @return 随机数
     */
    private static String truncate(String randomNumber, StrategyGroupConfig configOption) {
        int fromIndex = configOption.getPositionStart() == 0 ? 0 : configOption.getPositionStart() - 1;
        int length = configOption.getPositionEnd() - configOption.getPositionStart() + 1;
        return CharSequenceUtil.subWithLength(randomNumber, fromIndex, length);
    }

    private StrategyGroupMatch() {
        throw new StrategyException("No com.xftech.cdp.domain.strategy.model.group.StrategyGroupMatch instances for you!");
    }
}
