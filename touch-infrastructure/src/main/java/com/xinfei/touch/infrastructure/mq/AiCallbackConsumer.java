package com.xinfei.touch.infrastructure.mq;

import com.alibaba.fastjson.JSON;
import com.xinfei.touch.application.dto.AiCallBackMessageDTO;
import com.xinfei.touch.application.service.ReceiptApplicationService;
import com.xinfei.touch.infrastructure.mq.dto.AiCallBackMessageVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

/**
 * AI外呼回执RocketMQ消费者
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(
    topic = "CALL_CENTER_AI_CALLBACK",
    consumerGroup = "${ai.callback.consumer.group:cg_xyf_cdp_ai_callback}"
)
public class AiCallbackConsumer implements RocketMQListener<String> {
    
    private final ReceiptApplicationService receiptApplicationService;
    
    @Override
    public void onMessage(String message) {
        try {
            log.info("收到AI外呼回执消息: {}", message);
            
            // 解析消息
            AiCallBackMessageVO aiCallBackMessage = JSON.parseObject(message, AiCallBackMessageVO.class);
            if (aiCallBackMessage == null) {
                log.warn("AI外呼回执消息为空: {}", message);
                return;
            }
            
            // 转换为应用层DTO
            AiCallBackMessageDTO aiCallBackMessageDTO = convertToDTO(aiCallBackMessage);

            // 统一回执处理
            receiptApplicationService.processAiCallbackReceipt(aiCallBackMessageDTO);

            log.info("AI外呼回执处理完成: batchNo={}, status={}",
                    aiCallBackMessageDTO.getBatchNo(), aiCallBackMessageDTO.getStatus());
            
        } catch (Exception e) {
            log.error("AI外呼回执处理异常: message={}", message, e);
            // 注意：这里不抛出异常，避免消息重复消费
        }
    }

    /**
     * 转换为应用层DTO
     */
    private AiCallBackMessageDTO convertToDTO(AiCallBackMessageVO vo) {
        AiCallBackMessageDTO dto = new AiCallBackMessageDTO();
        BeanUtils.copyProperties(vo, dto);
        return dto;
    }
}
