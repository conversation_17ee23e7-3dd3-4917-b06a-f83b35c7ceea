package com.xftech.cdp.infra.interceptor;

import com.xftech.cdp.infra.config.AppConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @since 2022/11/1 13:39
 */
@Slf4j
@Component
public class MvcInterceptor extends HandlerInterceptorAdapter {

    @Resource
    private TokenInterceptor tokenInterceptor;
    @Resource
    private NewTokenInterceptor newTokenInterceptor;
    @Resource
    private AppConfigService appConfigService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        log.info("mvc-request:{}", request.getRequestURI());
        if (handler instanceof HandlerMethod) {
            if (appConfigService.getNewSsoSwitch()) {
                newTokenInterceptor.preHandle(request, (HandlerMethod) handler);
            } else {
                tokenInterceptor.preHandle(request, (HandlerMethod) handler);
            }

        }
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        if (handler instanceof HandlerMethod) {
            tokenInterceptor.afterCompletion();
        }
    }
}
