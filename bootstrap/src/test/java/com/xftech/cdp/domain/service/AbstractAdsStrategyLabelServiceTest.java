package com.xftech.cdp.domain.service;

import com.xftech.cdp.BaseTest;
import com.xftech.cdp.domain.ads.service.ModelPlatformService;
import com.xftech.cdp.domain.ads.service.impl.AbstractAdsStrategyLabelService;
import com.xftech.cdp.domain.strategy.model.dto.AdbRealTimeVariableGrayConfig;
import com.xftech.cdp.domain.strategy.model.enums.StrategyInstantLabelTypeEnum;
import com.xftech.cdp.infra.client.ads.model.req.label.AdsLabelReq;
import com.xftech.cdp.infra.client.ads.model.resp.AdsLabelResp;
import com.xftech.cdp.infra.client.ads.model.resp.BaseAdsResponse;
import com.xftech.cdp.infra.config.AppConfigService;
import com.xftech.cdp.infra.utils.JsonUtil;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.client.RestTemplate;

import java.util.*;

public class AbstractAdsStrategyLabelServiceTest extends BaseTest {

    //@Autowired
    private AbstractAdsStrategyLabelService abstractAdsStrategyLabelService;
    @Test
    public void test(){
        // StrategyInstantLabelTypeEnum queryType, Long strategyId, Long appUserId, String mobile, List<AdsLabelReq> reqs
        AdbRealTimeVariableGrayConfig.StrategyGrayConfig grayConfig = new AdbRealTimeVariableGrayConfig.StrategyGrayConfig();
        grayConfig.setAllHits(true);

        AdbRealTimeVariableGrayConfig.VariableGrayConfig variableGrayConfig = new AdbRealTimeVariableGrayConfig.VariableGrayConfig();
        variableGrayConfig.setAllHits(false);
        variableGrayConfig.setVariableList(Arrays.asList("user_cur_available_balance"));

        AppConfigService appConfigService = Mockito.mock(AppConfigService.class);
        Mockito.when(appConfigService.getAdbRealTimeVariableUrl()).thenReturn("https://qa1-xdecision.testxinfei.cn/api/data/data/realCollect");
        Mockito.when(appConfigService.getAdbStrategyGrayConfig()).thenReturn(grayConfig);
        Mockito.when(appConfigService.getAdbVariableGrayConfig()).thenReturn(variableGrayConfig);
        //ModelPlatformService modelPlatformService = new ModelPlatformService(new RestTemplate(),appConfigService);

      /*  abstractAdsStrategyLabelService.setModelPlatformService(modelPlatformService);
        abstractAdsStrategyLabelService.setAppConfigService(appConfigService);*/

        List<AdsLabelReq> reqs = new ArrayList<>();
        AdsLabelReq adsLabelReq = new AdsLabelReq();
        adsLabelReq.setLabel("user_cur_available_balance");
        Map<String, Object> map = new HashMap<>();
        map.put("app_user_id",590207979840614401L);
        adsLabelReq.setParams(Arrays.asList(map));
        reqs.add(adsLabelReq);

        BaseAdsResponse<List<AdsLabelResp>> adsResponse = abstractAdsStrategyLabelService.queryAdsWrapper(StrategyInstantLabelTypeEnum.LABEL,
                1L, 0L, "17621372484", reqs);
        System.out.println(JsonUtil.toJson(adsResponse));

    }
}
