package com.xftech.cdp.adapter.dict;


import com.xftech.cdp.api.DictApi;
import com.xftech.cdp.api.dto.base.Response;
import com.xftech.cdp.api.dto.req.dict.DictReq;
import com.xftech.cdp.api.dto.req.dict.EventMetaDataReq;
import com.xftech.cdp.api.dto.req.external.CallingSourceEnum;
import com.xftech.cdp.api.dto.resp.dict.CallingSourceResp;
import com.xftech.cdp.api.dto.resp.dict.DictResp;
import com.xftech.cdp.api.dto.resp.dict.EventMetaDataResp;
import com.xftech.cdp.domain.dict.service.DictService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/10/17 23:15
 */
@RestController
public class DictController implements DictApi {

    @Resource
    private DictService dictService;

    @Override
    public Response<List<DictResp>> queryByDictCodes(DictReq dictReq) {
        return Response.success(dictService.queryListByDictCodeList(dictReq.getDictCodeList()));
    }

    @Override
    public Response<List<DictResp>> queryT0CallingSource(DictReq dictReq) {
        return Response.success(dictService.queryDictCodeList(dictReq));
    }

    @Override
    public Response<List<EventMetaDataResp>> queryEventMetaData(EventMetaDataReq eventMetaDataReq) {
        return Response.success(dictService.queryEventMetaData(eventMetaDataReq));
    }

    @Override
    public Response<List<CallingSourceResp>> queryT0CallingSource() {
        List<CallingSourceEnum> enumList = new ArrayList<>(Arrays.asList(CallingSourceEnum.values()));
        List<CallingSourceResp> result = new ArrayList<>(enumList.size());
        for (CallingSourceEnum callingSourceEnum : enumList) {
            CallingSourceResp resp = new CallingSourceResp();
            resp.setCode(callingSourceEnum.getCode());
            resp.setLabel(callingSourceEnum.getLabel());
            result.add(resp);
        }
        return Response.success(result);
    }

}
