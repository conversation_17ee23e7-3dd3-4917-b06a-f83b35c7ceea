package com.xftech.cdp.infra.rocketmq.credit;

import com.xftech.cdp.infra.rocketmq.EventMessageBaseProcessor;
import com.xinfei.xfframework.common.starter.mq.MqConsumerListener;
import groovy.util.logging.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RocketMQMessageListener(consumerGroup = "cg_xyf_cdp_tp_rcspt_credit_apply_message", topic = "tp_rcspt_credit_apply_message", consumeMode = ConsumeMode.CONCURRENTLY)
public class CreditApplyMsgConsumer extends MqConsumerListener<String> {

    private static final Logger log = LoggerFactory.getLogger(CreditApplyMsgConsumer.class);
    @Autowired
    private EventMessageBaseProcessor eventMessageBaseProcessor;

    @Override
    public void doMessage(String topic, String message, MessageExt messageExt) {
        try{
            RocketMQMessageListener messageListener = this.getClass().getAnnotation(RocketMQMessageListener.class);
            eventMessageBaseProcessor.doMessageProcess(messageListener,messageExt,message);
        }catch (Exception e){
            log.error("message process error, topic={},message={}",topic,message, e);
        }
    }
}
