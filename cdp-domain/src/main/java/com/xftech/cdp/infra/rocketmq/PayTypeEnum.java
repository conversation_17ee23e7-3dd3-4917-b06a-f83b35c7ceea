package com.xftech.cdp.infra.rocketmq;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version $ PayTypeEnum, v 0.1 2025/3/5 14:36 tianshuo.qiu Exp $
 */
@Getter
@AllArgsConstructor
public enum PayTypeEnum {
    NORMAL("pre", "主动还款"),

    GROUP_PAYMENT_NORMAL("group_pay_pre", "聚合支付主动发起"),
    //
    GROUP_PAYMENT_MANAGER("group_pay_manager", "聚合支付管理员发起"),

    AUTO("auto", "到期批扣"),

    OVERDUE_AUTO("overdue_auto", "代偿后批扣"),

    MANAGER_AUTO("manager_auto", "管理员代扣"),

    LIVE("live", "线下销账"),

    REDUCTION("reduction", "抵扣"),

    // 资方批扣后给信飞侧扣款结果,理论上只有成功的记录
    FUND_AUTO("fund_auto", "资方批扣"),

    // 非真实还款,仅老系统会产生这种数据
    BUY_BACK("buy_back", "代偿"),

    // 部分还款的类型仅老系统会产生这种数据,新系统不使用payType区分部分还款
    AUTO_PART("auto_part", "到期批扣部分还款"),
    OVERDUE_AUTO_PART("overdue_auto_part", "代偿后批扣部分还款"),
    MANAGER_AUTO_PART("manager_auto_part", "管理员代扣部分还款"),
    LIVE_PART("live_part", "部分线下销账");

    /**
     * 支付类型code
     */
    private final String code;

    /**
     * 支付类型名称
     */
    private final String payTypeName;
}
