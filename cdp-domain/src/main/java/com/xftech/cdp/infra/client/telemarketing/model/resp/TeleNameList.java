package com.xftech.cdp.infra.client.telemarketing.model.resp;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @date 2023/3/31 15:55
 */
@Data
public class TeleNameList {

    private Integer page;

    private Integer pageSize;

    private Integer total;

    private List<Details> list;

    private List<BaseType> nameType;

    private List<BaseType> nameSubType;


    @Data
    public static class Details {

        private Long id;

        private String name;

        private String type;

        @J<PERSON><PERSON>ield(name = "sub_type")
        private String subType;

        @J<PERSON><PERSON>ield(name = "user_type")
        private Integer userType;
    }

    @Data
    public static class BaseType {

        private String value;

        private Integer index;
    }

}
