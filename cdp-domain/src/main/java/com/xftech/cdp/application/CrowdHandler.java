package com.xftech.cdp.application;

import com.xftech.cdp.api.dto.crowd.CrowdXxlJobParam;
import com.xftech.cdp.domain.crowd.service.CrowdPackService;
import com.xftech.cdp.domain.crowd.service.CrowdPushBatchService;
import com.xftech.cdp.domain.crowd.service.dispatch.CrowdDispatchService;
import com.xftech.cdp.domain.strategy.model.enums.ReportDailyTypeEnum;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdExecLogDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPackDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.ReportDailyCrowdDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.ReportDailyStrategyDo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/2/13
 */
@Slf4j
@Component
public class CrowdHandler {

    private final CrowdDispatchService crowdDispatchStartRockService;

    private final CrowdPackService crowdPackService;

    private final CrowdPushBatchService crowdPushBatchService;

    public CrowdHandler(CrowdDispatchService crowdDispatchStartRockService, CrowdPackService crowdPackService, CrowdPushBatchService crowdPushBatchService) {
        this.crowdDispatchStartRockService = crowdDispatchStartRockService;
        this.crowdPackService = crowdPackService;
        this.crowdPushBatchService = crowdPushBatchService;
    }


    public void execute(CrowdXxlJobParam crowdXxlJobParam) {
        crowdDispatchStartRockService.execute(crowdXxlJobParam.getCrowdId());
    }


    public void crowdReset() {
        crowdPackService.crowdReset();
    }

    public void crowdWareHouseAlarm() {
        crowdPackService.crowdWareHouseAlarm();
    }

    public void crowdPushResultQuery() {
        crowdPushBatchService.crowdPushResultQuery();
    }

    public void reportDailyCrowd() {
        crowdPackService.reportDailyCrowd();
    }

    public void resetBigDataErrorResultCrowdPacks() {
        crowdPackService.resetBigDataErrorResultCrowdPacks();
    }

}
