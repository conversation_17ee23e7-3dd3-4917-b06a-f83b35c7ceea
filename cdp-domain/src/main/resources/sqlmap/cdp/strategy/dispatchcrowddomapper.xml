<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="dispatchCrowdDoMapper">
  <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.strategy.po.DispatchCrowdDo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="flow_no" jdbcType="VARCHAR" property="flowNo" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="pre_strategy_id" jdbcType="BIGINT" property="preStrategyId" />
    <result column="strategy_id" jdbcType="BIGINT" property="strategyId" />
    <result column="next_strategy_id" jdbcType="BIGINT" property="nextStrategyId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="user_info" jdbcType="VARCHAR" property="userInfo" />
    <result column="d_flag" jdbcType="SMALLINT" property="dFlag" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, flow_no, batch_no, pre_strategy_id, strategy_id, next_strategy_id, user_id, user_info, 
    d_flag, created_time, updated_time
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from dispatch_crowd_${tableNum}
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectUserList" resultMap="BaseResultMap">
    select
    user_id
    from dispatch_crowd_${tableNum}
    where d_flag = 0 and flow_no =#{flowNo}
      and strategy_id = #{strategyId}
    <if test="userIdList !=null and userIdList.size()>0">
      and user_id in
    <foreach collection="userIdList" item="userId" open="(" close=")" separator=",">
      #{userId}
    </foreach>
    </if>
  </select>

  <select id="selectList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from dispatch_crowd_${tableNum}
    where d_flag = 0 and flow_no =#{flowNo} and next_strategy_id = #{strategyId} and id > #{startIndex}
    order by id
    limit #{size}
  </select>

  <select id="selectCount" resultType="integer">
    select
    count(1) AS count
    from dispatch_crowd_${tableNum}
    where d_flag = 0 and flow_no =#{flowNo} and next_strategy_id = #{strategyId}
  </select>

  <select id="selectDispatchCount" resultType="integer">
    select
      count(1) AS count
    from dispatch_crowd_${tableNum}
    where d_flag = 0 and batch_no =#{batchNo} and strategy_id = #{strategyId}
  </select>

  <insert id="insertSelective" parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.DispatchCrowdDo">
    insert into dispatch_crowd_${tableNum}
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="flowNo != null">
        flow_no,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="preStrategyId != null">
        pre_strategy_id,
      </if>
      <if test="strategyId != null">
        strategy_id,
      </if>
      <if test="nextStrategyId != null">
        next_strategy_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="userInfo != null">
        user_info,
      </if>
      <if test="dFlag != null">
        d_flag,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="updatedTime != null">
        updated_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="flowNo != null">
        #{flowNo,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="preStrategyId != null">
        #{preStrategyId,jdbcType=BIGINT},
      </if>
      <if test="strategyId != null">
        #{strategyId,jdbcType=BIGINT},
      </if>
      <if test="nextStrategyId != null">
        #{nextStrategyId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="userInfo != null">
        #{userInfo,jdbcType=VARCHAR},
      </if>
      <if test="dFlag != null">
        #{dFlag,jdbcType=SMALLINT},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        #{updatedTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.DispatchCrowdDo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update dispatch_crowd_${tableNum}
    <set>
      <if test="flowNo != null">
        flow_no = #{flowNo,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        batch_no = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="preStrategyId != null">
        pre_strategy_id = #{preStrategyId,jdbcType=BIGINT},
      </if>
      <if test="strategyId != null">
        strategy_id = #{strategyId,jdbcType=BIGINT},
      </if>
      <if test="nextStrategyId != null">
        next_strategy_id = #{nextStrategyId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="userInfo != null">
        user_info = #{userInfo,jdbcType=VARCHAR},
      </if>
      <if test="dFlag != null">
        d_flag = #{dFlag,jdbcType=SMALLINT},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.DispatchCrowdDo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update dispatch_crowd_20231221
    set flow_no = #{flowNo,jdbcType=VARCHAR},
      batch_no = #{batchNo,jdbcType=VARCHAR},
      pre_strategy_id = #{preStrategyId,jdbcType=BIGINT},
      strategy_id = #{strategyId,jdbcType=BIGINT},
      next_strategy_id = #{nextStrategyId,jdbcType=BIGINT},
      user_id = #{userId,jdbcType=BIGINT},
      user_info = #{userInfo,jdbcType=VARCHAR},
      d_flag = #{dFlag,jdbcType=SMALLINT},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      updated_time = #{updatedTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

</mapper>