package com.xftech.cdp.api;

import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.base.Response;
import com.xftech.cdp.api.dto.req.*;
import com.xftech.cdp.api.dto.req.external.ExternalBaseRequest;
import com.xftech.cdp.api.dto.resp.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023/2/9 01:42
 */
@Api(tags = "人群包配置")
@RequestMapping("/crowd")
public interface CrowdApi {

    @ApiOperation("创建人群-圈选")
    @PostMapping("/create-by-condition")
    Response<Boolean> createByCondition(@Validated @RequestBody CrowdCreateReq crowdCreateReq);

    @ApiOperation("更新人群-圈选")
    @PostMapping("/update-by-condition")
    Response<Boolean> updateByCondition(@Validated @RequestBody CrowdUpdateReq crowdUpdateReq);

    /*
    @ApiOperation("创建人群-Excel上传")
    @PostMapping("/create-by-excel")
    Response<CrowdOneResp> createByExcel( @RequestParam("file") MultipartFile file, @RequestPart("crowdName") String crowdName );

    @ApiOperation("更新人群-Excel上传")
    @PostMapping("/update-by-excel")
    Response<CrowdOneResp> updateByExcel( @RequestParam("file") MultipartFile file, @RequestPart("crowdId") Integer crowdId, @RequestPart("crowdName") String crowdName );

    @ApiOperation("解析Excel")
    @PostMapping("/parse-excel")
    Response<CrowdParseResp> parseExcel( @Validated @RequestBody CrowdParseReq crowdParseReq );
     */

    @ApiOperation("上传Excel")
    @PostMapping("/upload-file")
    Response<CrowdUploadResp> uploadExcel(@RequestParam(name = "file", required = true) MultipartFile file);

    @ApiOperation("解析Excel")
    @PostMapping("/parse-file")
    Response<CrowdParseResp> parseExcel(@Validated @RequestBody CrowdDownLoadReq crowdDownLoadReq);

    @ApiOperation("创建人群-上传并解析Excel")
    @PostMapping("/create-by-upload")
    Response<Boolean> createCrowdByExcel(@Validated @RequestBody CrowdUploadReq crowdUploadReq);

    @ApiOperation("复制人群包")
    @PostMapping("/duplicate")
    Response<Boolean> Duplicate(@Validated @RequestBody CrowdDuplicateReq crowdDuplicateReq);

    @ApiOperation("获取人群列表")
    @PostMapping("/list")
    Response<PageResultResponse<CrowdListResp>> list(@RequestBody CrowdListReq crowdListReq);

    @ApiOperation("获取一条记录")
    @PostMapping("/get-one")
    Response<CrowdOneResp> getOne(@Validated @RequestBody CrowdOneReq crowdOneReq);

    @ApiOperation("获取标签列表")
    @PostMapping("/labels")
    Response<List<CrowdLabelsResp>> getLabels(@Validated @RequestBody LabelReq labelReq);

    @ApiOperation("执行操作")
    @PostMapping("/operate")
    Response<Boolean> operate(@Validated @RequestBody CrowdOperateReq crowdOperateReq);


    @ApiOperation("批量删除人群")
    @PostMapping("/batch-delete")
    Response<String> batchDelete(@Validated @RequestBody CrowdBatchDeleteReq crowdBatchDeleteReq);


    @ApiOperation("下载Excel")
    @PostMapping("/download-file")
    void downloadFile(@Validated @RequestBody CrowdDownLoadReq crowdDownLoadReq, HttpServletResponse response);

    @ApiOperation("当日人群包执行情况")
    @PostMapping("/report-daily")
    Response<CrowdReportDailyResp> reportDailyCrowdList();

    @ApiOperation("人群包刷新记录")
    @PostMapping("/refresh-info")
    Response<PageResultResponse<CrowdRefreshInfoResp>> crowdRefreshInfo(@Validated @RequestBody CrowdRefreshInfoReq crowdRefreshInfo);

    /*@ApiOperation("获取sql")
    @PostMapping("/getExecSql")
    Response<CrowdExecSqlResp> getExecSql(@RequestBody CrowdOneReq crowdOneReq);*/

    @ApiOperation("大数据错误人群包状态重置")
    @PostMapping("/resetBigDataErrorResultCrowdPacks")
    Response<Set<Long>> resetBigDataErrorResultCrowdPacks(@Validated @RequestBody ExternalBaseRequest req);

}
