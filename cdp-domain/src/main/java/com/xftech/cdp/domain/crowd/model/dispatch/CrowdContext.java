package com.xftech.cdp.domain.crowd.model.dispatch;

import com.alibaba.fastjson.JSON;
import com.xftech.cdp.domain.crowd.exception.CrowdException;
import com.xftech.cdp.domain.crowd.factory.AbsCrowdOptService;
import com.xftech.cdp.domain.crowd.model.enums.CrowdFilterMethodEnum;
import com.xftech.cdp.domain.crowd.model.enums.CrowdLabelEnum;
import com.xftech.cdp.domain.crowd.model.enums.CrowdLabelRelationEnum;
import com.xftech.cdp.domain.crowd.model.enums.LabelEnum;
import com.xftech.cdp.domain.crowd.model.label.crowd.CrowdLabelOption;
import com.xftech.cdp.domain.crowd.model.label.crowd.FixedCheckOption;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdExecLogDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdLabelDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdLabelPrimaryDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdLabelSubDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPackDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.LabelDo;
import com.xftech.cdp.infra.utils.MapUtils;
import com.xinfei.enginebase.util.apollo.ApolloUtil;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/2/13
 */

@Getter
@Setter
public class CrowdContext {

    private CrowdPackDo crowdPack;


    private List<Delineate> delineates;

    private Label customLabel;

    private Pair<StringBuilder, StringBuilder> labelSqlPair;

    private CrowdExecLogDo crowdExecLog;

    private AbsCrowdOptService crowdOpt;

    private transient Long maxId;

    private transient List<InvokePage> invokePages;

    private transient long beginMillis = System.currentTimeMillis();

    private transient AtomicInteger personNum = new AtomicInteger(0);

    private List<String> includeCustomLabel;

    private List<String> excludeCustomLabel;

    private String failReason;

    /**
     * 是否失败
     */
    private transient volatile boolean failed = false;

    private transient volatile boolean newRandomfailed = false;

    private boolean isRunAsFixedNumberPage;

    public CrowdContext() {

    }

    public static CrowdContext init(boolean isRunAsFixedNumberPage, CrowdPackDo crowdPack, AbsCrowdOptService crowdOpt) {
        CrowdContext crowdContext = new CrowdContext();
        crowdContext.setCrowdPack(crowdPack);
        crowdContext.setCrowdOpt(crowdOpt);
        crowdContext.setRunAsFixedNumberPage(isRunAsFixedNumberPage);
        return crowdContext;
    }

    public static CrowdContext init(boolean isRunAsFixedNumberPage, CrowdPackDo crowdPack, List<CrowdLabelPrimaryDo> crowdLabelPrimaryList, List<CrowdLabelDo> crowdLabelList, List<LabelDo> labelDos, List<CrowdLabelSubDo> crowdLabeSublList) {
        if (CrowdFilterMethodEnum.LABEL.getCode() != crowdPack.getFilterMethod()) {
            throw new CrowdException("筛选标签类型错误");
        }

        if (CollectionUtils.isEmpty(crowdLabelPrimaryList) || CollectionUtils.isEmpty(crowdLabelList) || CollectionUtils.isEmpty(labelDos)) {
            throw new CrowdException("筛选标签数据不能为空");
        }

        Map<Long, List<CrowdLabelDo>> crowdLabelMap = MapUtils.listGroup(crowdLabelList, CrowdLabelDo::getCrowdLabelPrimaryId);

        Map<Integer, List<CrowdLabelPrimaryDo>> crowdLabelPrimaryMap = MapUtils.listGroup(crowdLabelPrimaryList, CrowdLabelPrimaryDo::getLabelGroupType);

        Map<Long, LabelDo> labelMap = MapUtils.listToMap(labelDos, LabelDo::getId);

        if (ApolloUtil.containsInStrList("crowdIdList", String.valueOf(crowdPack.getId()))) {
            crowdLabeSublList = Optional.ofNullable(crowdLabeSublList).orElse(Collections.emptyList()).stream().filter(e -> {
                LabelDo labelDo = labelMap.get(e.getLabelId());
                return !Objects.equals(labelDo.getLabelType(), 1);
            }).collect(Collectors.toList());
        }
        Map<Long, List<CrowdLabelSubDo>> crowdLabelSubMap = MapUtils.listGroup(crowdLabeSublList, CrowdLabelSubDo::getCrowdLabelId);

        Map<String, LabelDo> labelNameMap = MapUtils.listToMap(labelDos, LabelDo::getLabelName);

        CrowdContext crowdContext = new CrowdContext();
        crowdContext.setRunAsFixedNumberPage(isRunAsFixedNumberPage);

        List<Delineate> delineateList = new ArrayList<>(2);
        List<String> includeNewRandom = new ArrayList<>();
        List<String> excludeNewRandom = new ArrayList<>();

        crowdLabelPrimaryMap.forEach((labelGroupType, labelPrimaryList) -> {
            List<PrimaryLabel> primaryLabels = new ArrayList<>();
            for (CrowdLabelPrimaryDo crowdLabelPrimary : labelPrimaryList) {
                PrimaryLabel primaryLabel = new PrimaryLabel();

                List<Label> labels = new ArrayList<>();
                List<CrowdLabelDo> crowdLabels = crowdLabelMap.get(crowdLabelPrimary.getId());
                Map<Long, CrowdLabelDo> crowdLabelLabelIdMap = MapUtils.listToMap(crowdLabels, CrowdLabelDo::getLabelId);
                if (CollectionUtils.isEmpty(crowdLabels)) {
                    continue;
                }
                for (CrowdLabelDo crowdLabel : crowdLabels) {
                    LabelDo labelDo = labelMap.get(crowdLabel.getLabelId());
                    labelProcess(labelDo, labelNameMap, crowdLabelLabelIdMap, crowdLabel.getLabelId());
                    if (labelDo.getLabelType() == 1) {
                        if ("new_random".equals(labelDo.getDataWarehouseField())) {
                            filterNewRandom(labelDo.getDataWarehouseField(), crowdLabel.getLabelValue(), labelGroupType, includeNewRandom, excludeNewRandom);
                        } else {
                            Label label = new Label();
                            label.setLabelId(crowdLabel.getLabelId());
                            label.setLabelName(labelDo.getLabelName());
                            label.setLabelValue(crowdLabel.getLabelV(LabelEnum.LabelOptionTypeEnum.ofCode(labelDo.getConfigurationOptionType())));
                            label.setDataWarehouseField(labelDo.getDataWarehouseField());
                            label.setLabelGroupType(labelGroupType);
                            crowdContext.setCustomLabel(label);
                        }
                        // 无二级逻辑标签
                        if (CollectionUtils.isEmpty(crowdLabelSubMap.get(crowdLabel.getId()))) {
                            continue;
                        }
                    }

                    Label label = new Label();
                    label.setLabelId(crowdLabel.getLabelId());
                    label.setLabelName(labelDo.getLabelName());
                    label.setLabelValue(crowdLabel.getLabelV(LabelEnum.LabelOptionTypeEnum.ofCode(labelDo.getConfigurationOptionType())));
                    label.setExecIndex(crowdLabel.getExecIndex());
                    label.setLabelRelation(CrowdLabelRelationEnum.ofCode(crowdLabel.getLabelRelation()));
                    label.setDataWarehouseField(labelDo.getDataWarehouseField());
                    label.setConfigurationOptionType(LabelEnum.LabelOptionTypeEnum.ofCode(labelDo.getConfigurationOptionType()));
                    label.setConfigurationOption(labelDo.getConfigurationOption());
                    label.setConfigurationReflect(labelDo.getConfigurationReflect());
                    label.setLabelType(labelDo.getLabelType());

                    if (!CollectionUtils.isEmpty(crowdLabelSubMap.get(crowdLabel.getId()))) {
                        List<SubLabel> subLabels = new ArrayList<>();
                        List<CrowdLabelSubDo> subLabels1 = crowdLabelSubMap.get(crowdLabel.getId());
                        for (CrowdLabelSubDo crowdLabelSubDo : subLabels1) {
                            LabelDo sublabelDo = labelMap.get(crowdLabelSubDo.getLabelId());
                            labelProcess(sublabelDo, labelNameMap, crowdLabelLabelIdMap, crowdLabelSubDo.getLabelId());
                            if (sublabelDo.getLabelType() == 1) {
                                if ("new_random".equals(sublabelDo.getDataWarehouseField())) {
                                    filterNewRandom(sublabelDo.getDataWarehouseField(), crowdLabelSubDo.getLabelValue(), labelGroupType, includeNewRandom, excludeNewRandom);
                                } else {
                                    Label customLabel = new Label();
                                    customLabel.setLabelId(crowdLabelSubDo.getLabelId());
                                    customLabel.setLabelName(sublabelDo.getLabelName());
                                    customLabel.setLabelValue(crowdLabelSubDo.getLabelV(LabelEnum.LabelOptionTypeEnum.ofCode(sublabelDo.getConfigurationOptionType())));
                                    customLabel.setDataWarehouseField(sublabelDo.getDataWarehouseField());
                                    customLabel.setLabelGroupType(labelGroupType);
                                    crowdContext.setCustomLabel(customLabel);
                                }
                                continue;
                            }

                            SubLabel subLabel = new SubLabel();
                            subLabel.setLabelId(crowdLabelSubDo.getLabelId());
                            subLabel.setLabelName(sublabelDo.getLabelName());
                            subLabel.setLabelValue(crowdLabelSubDo.getLabelV(LabelEnum.LabelOptionTypeEnum.ofCode(sublabelDo.getConfigurationOptionType())));
                            subLabel.setExecIndex(crowdLabelSubDo.getExecIndex());
                            subLabel.setLabelRelation(CrowdLabelRelationEnum.ofCode(crowdLabelSubDo.getLabelRelation()));
                            subLabel.setDataWarehouseField(sublabelDo.getDataWarehouseField());
                            subLabel.setConfigurationOptionType(LabelEnum.LabelOptionTypeEnum.ofCode(sublabelDo.getConfigurationOptionType()));
                            subLabel.setConfigurationOption(sublabelDo.getConfigurationOption());
                            subLabel.setConfigurationReflect(sublabelDo.getConfigurationReflect());
                            subLabel.setLabelType(sublabelDo.getLabelType());
                            subLabels.add(subLabel);
                        }
                        label.setSubLabels(subLabels);
                    }
                    labels.add(label);
                }
                labels = labels.stream().sorted(Comparator.comparing(Label::getExecIndex)).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(labels)) {
                    continue;
                }
                primaryLabel.setLabels(labels);
                primaryLabel.setPrimaryLabel(LabelEnum.PrimaryLabelEnum.ofCode(crowdLabelPrimary.getPrimaryLabel()));
                primaryLabel.setLabelRelation(CrowdLabelRelationEnum.ofCode(crowdLabelPrimary.getPrimaryLabelRelation()));
                primaryLabel.setExecIndex(crowdLabelPrimary.getExecIndex());
                primaryLabels.add(primaryLabel);
                primaryLabels = primaryLabels.stream().sorted(Comparator.comparing(PrimaryLabel::getExecIndex)).collect(Collectors.toList());
            }
            Delineate delineate = new Delineate();
            delineate.setCrowdLabel(CrowdLabelEnum.ofCode(labelGroupType));
            delineate.setPrimaryLabels(primaryLabels);
            if (primaryLabels.size() > 1 || (primaryLabels.size() == 1 && !CollectionUtils.isEmpty(primaryLabels.get(0).labels))) {
                delineateList.add(delineate);
            }
        });

        crowdContext.setIncludeCustomLabel(includeNewRandom);
        crowdContext.setExcludeCustomLabel(excludeNewRandom);
        crowdContext.setDelineates(delineateList);
        crowdContext.setCrowdPack(crowdPack);
        return crowdContext;
    }

    public void subLevel(long minId, long maxId, int size, int pageSize) {
        long current = Math.max(minId - 1, 0);
        long interval = (maxId - current) / size;
        invokePages = new ArrayList<>(size);
        int sum = 0;
        while ((current + interval) < maxId && sum < (size - 1)) {
            InvokePage invokePage = new InvokePage();
            invokePage.setAppUserId(current);
            current = current + interval;
            invokePage.setMaxAppUserId(current);
            invokePages.add(invokePage);
            invokePage.setPageSize(pageSize);
            sum++;
        }
        InvokePage invokePage = new InvokePage();
        invokePage.setAppUserId(current);
        invokePage.setMaxAppUserId(maxId);
        invokePage.setPageSize(pageSize);
        invokePages.add(invokePage);
    }

    private static void labelProcess(LabelDo labelDo, Map<String, LabelDo> labelNameMap, Map<Long, CrowdLabelDo> crowdLabelLabelIdMap, Long labelId) {
        if (labelDo == null) {
            throw new CrowdException(String.format("不存在该标签，标签id: %s", labelId));
        }
        if (labelDo.getLabelName().equals(LabelEnum.SecondaryLabelEnum.DROP_THE_NODE_DATE.getDescription())) {
            LabelDo dropNodeLabel = labelNameMap.get(LabelEnum.SecondaryLabelEnum.DROP_THE_NODE.getDescription());
            if (dropNodeLabel == null) {
                throw new CrowdException("存在脱落节点天数但不存在脱落节点！");
            }
            CrowdLabelDo dropCrowdLabel = crowdLabelLabelIdMap.get(dropNodeLabel.getId());
            if (dropCrowdLabel == null) {
                throw new CrowdException("存在脱落节点天数但不存在脱落节点！");
            }
            LabelDo replacelabelBo = labelNameMap.get(labelDo.getLabelName());
            labelDo.setDataWarehouseField((String) JSON.parseObject(replacelabelBo.getConfigurationReflect(), Map.class).get(JSON.parseObject(dropCrowdLabel.getLabelValue(), FixedCheckOption.class).getItems().get(0)));
        }
    }

    private static void filterNewRandom(String labelName, String labelValue, Integer labelGroupType, List<String> includeNewRandom, List<String> excludeNewRandom) {
        if (!"new_random".equals(labelName)) {
            return;
        }
        if (labelGroupType == CrowdLabelEnum.INCLUDE_LABEL.getCode()) {
            includeNewRandom.add(labelValue);
        } else {
            excludeNewRandom.add(labelValue);
        }
    }

    @Data
    public static class Delineate {

        private CrowdLabelEnum crowdLabel;

        private List<PrimaryLabel> primaryLabels;

    }

    @Data
    public static class PrimaryLabel {

        private LabelEnum.PrimaryLabelEnum primaryLabel;

        private CrowdLabelRelationEnum labelRelation;

        private List<Label> labels;

        private Integer execIndex;

    }

    @Data
    public static class Label {
        private Long labelId;

        private String labelName;

        private CrowdLabelOption labelValue;

        private Integer execIndex;

        private CrowdLabelRelationEnum labelRelation;

        private String dataWarehouseField;

        private LabelEnum.LabelOptionTypeEnum configurationOptionType;

        private String configurationOption;

        private String configurationReflect;

        private List<SubLabel> subLabels;

        private Integer labelGroupType;

        private Integer labelType;


        public StringBuilder condition() {
            return new StringBuilder().append("(").append(labelValue.condition(dataWarehouseField, configurationReflect)).append(")");
        }

    }

    @Data
    public static class SubLabel {
        private Long labelId;

        private String labelName;

        private CrowdLabelOption labelValue;

        private Integer execIndex;

        private CrowdLabelRelationEnum labelRelation;

        private String dataWarehouseField;

        private LabelEnum.LabelOptionTypeEnum configurationOptionType;

        private String configurationOption;

        private String configurationReflect;

        private Integer labelGroupType;

        private Integer labelType;


        public StringBuilder condition() {
            return new StringBuilder().append("(").append(labelValue.condition(dataWarehouseField, configurationReflect)).append(")");
        }

    }

}
