package com.xftech.cdp.infra.rocketmq.user;

import com.alibaba.fastjson.JSONObject;
import com.xftech.cdp.domain.mq.MqConsumeService;
import com.xftech.cdp.infra.client.usercenter.UserCenterClient;
import com.xftech.cdp.infra.client.usercenter.model.UserInfoResp;
import com.xftech.cdp.infra.config.LogUtil;
import com.xftech.cdp.infra.constant.Constants;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventMessageVO;
import com.xftech.cdp.infra.rocketmq.MessageHandler;
import com.xftech.cdp.infra.rocketmq.dto.NewTrackingCardClickVO;
import com.xftech.cdp.infra.utils.DateUtil;
import com.xyf.cis.SecureClient;
import com.xyf.cis.dto.SecureEncryptDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ NewTrackingCardClickHandler, v 0.1 2025/3/25 16:40 tianshuo.qiu Exp $
 */
@Slf4j
@Component
public class NewTrackingCardClickHandler implements MessageHandler {
    @Resource
    private MqConsumeService mqConsumeService;

    @Autowired
    private UserCenterClient userCenterClient;
    @Resource
    private SecureClient secureClient;

    @Override
    public String getTopic() {
        return "tp_cis_event_report_1004089";
    }

    @Override
    public boolean execute(String messageId, Object message) {
        NewTrackingCardClickVO messageVO = JSONObject.parseObject(message.toString(), NewTrackingCardClickVO.class);
        BizEventMessageVO bizEventMessageVO = transform(messageVO);

        if (bizEventMessageVO == null) {
            return false;
        }
        bizEventMessageVO.setBizEventType("NewTracking_CardClick");
        mqConsumeService.bizEventProcess(messageId, bizEventMessageVO);
        return true;


    }

    private BizEventMessageVO transform(NewTrackingCardClickVO newTrackingCardClickVO) {
        String eventId = newTrackingCardClickVO.getEventId();
        String creditUserId = newTrackingCardClickVO.getCreditUserId();
        //如果没有event_id,则是一条错误的消息，不做处理
        //如果没有credit_user_id,则是未登录状态的上报信息，不做处理
        if (StringUtils.isBlank(eventId) || StringUtils.isBlank(creditUserId) || StringUtils.isBlank(newTrackingCardClickVO.getApp())) {
            log.info("NewTracking_CardClick message is have event_id or credit_user_id or app, newTrackingCardClickVO:{}", JSONObject.toJSONString(newTrackingCardClickVO));
            return null;
        }

        BizEventMessageVO bizEventMessageVO = new BizEventMessageVO();
        bizEventMessageVO.setCreditUserId(Long.parseLong(creditUserId));
        bizEventMessageVO.setAppUserId(Long.parseLong(creditUserId));
        bizEventMessageVO.setApp(newTrackingCardClickVO.getApp());
        bizEventMessageVO.setInnerApp(newTrackingCardClickVO.getInnerApp());

        if (StringUtils.isNotBlank(newTrackingCardClickVO.getEventDateTime())) {
            bizEventMessageVO.setTriggerDatetime(newTrackingCardClickVO.getEventDateTime());
        } else {
            bizEventMessageVO.setTriggerDatetime(DateUtil.convertStr(new Date()));
        }
        bizEventMessageVO.setOs(newTrackingCardClickVO.getOs());
        //xyf转xyf01
        if (Constants.XYF.equals(newTrackingCardClickVO.getApp())) {
            try {
                String mobileCipher = newTrackingCardClickVO.getMobileCipher();
                String mobile = "";
                if (StringUtils.isNotBlank(mobileCipher)) {
                    List<SecureEncryptDTO> secureEncryptDTOS = secureClient.batchDecrypt(Arrays.asList(mobileCipher));
                    if (CollectionUtils.isNotEmpty(secureEncryptDTOS)) {
                        mobile = secureEncryptDTOS.get(0).getPlainText();
                        log.info("NewTracking_CardClick mobileCipher:{},mobile:{}", mobileCipher, mobile);
                    }
                    if (StringUtils.isBlank(mobile)){
                        return null;
                    }
                    UserInfoResp userInfoResp = userCenterClient.getUserByMobile(mobile, Constants.XYF01);
                    bizEventMessageVO.setApp(Constants.XYF01);
                    bizEventMessageVO.setMobile(mobile);
                    if (userInfoResp == null) {
                        LogUtil.logDebug("NewTracking_CardClick getUserByMobile is null,mobile:{}", mobile);
                        return null;
                    }
                    bizEventMessageVO.setCreditUserId(userInfoResp.getCreditUserId());
                    bizEventMessageVO.setAppUserId(userInfoResp.getCreditUserId());
                    bizEventMessageVO.setInnerApp(userInfoResp.getInnerApp());
                    log.info("NewTracking_CardClick userInfoResp:{}", JSONObject.toJSONString(userInfoResp));
                } else {
                    return null;
                }
            } catch (Exception e) {
                LogUtil.logDebug("NewTracking_CardClick convertToUserNoXcx error", e);
                return null;
            }
        }
        return bizEventMessageVO;
    }
}
