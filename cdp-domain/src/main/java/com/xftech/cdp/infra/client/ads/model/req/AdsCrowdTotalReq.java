/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.infra.client.ads.model.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ AdsCrowdTotalReq, v 0.1 2023/11/22 18:03 wancheng.qu Exp $
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AdsCrowdTotalReq implements Serializable {

    private List<DataItem> data;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DataItem {
        private Long crowdId;
    }
}
