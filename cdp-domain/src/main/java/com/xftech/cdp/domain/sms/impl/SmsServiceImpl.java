package com.xftech.cdp.domain.sms.impl;

import com.xftech.cdp.domain.sms.SmsService;
import com.xftech.cdp.infra.client.sms.SmsClient;
import com.xftech.cdp.infra.client.sms.model.SmsItemArgs;
import com.xftech.cdp.infra.client.sms.model.SmsItemRequester;
import com.xftech.cdp.infra.client.sms.model.resp.SmsItem;
import com.xftech.cdp.infra.client.sms.model.resp.SmsItemResp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
public class SmsServiceImpl implements SmsService {
    private static final Logger logger = LoggerFactory.getLogger(SmsServiceImpl.class);

    @Autowired
    private SmsClient smsClient;

    public SmsItem queryTemplateDetail(String templateId, String app) {
        SmsItemRequester requester = new SmsItemRequester();
        SmsItemArgs smsItemArgs = new SmsItemArgs();
        smsItemArgs.setTemplateId(templateId);
        smsItemArgs.setApp(app);
        smsItemArgs.setPage(1);
        smsItemArgs.setPageSize(1);
        requester.setArgs(smsItemArgs);
        SmsItemResp smsItemResp = smsClient.queryItem(requester);
        if (!smsItemResp.isSuccess() || CollectionUtils.isEmpty(smsItemResp.getResponse().getList())) {
            logger.warn("短信模板详情获取异常：模板号：{}，app：{}", templateId, app);
            return null;
        }
        return smsItemResp.getResponse().getList().get(0);
    }
}
