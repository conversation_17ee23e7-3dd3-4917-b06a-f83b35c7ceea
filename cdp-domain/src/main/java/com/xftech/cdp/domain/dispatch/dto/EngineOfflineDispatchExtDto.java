/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.dispatch.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.Map;

/**
 *
 * <AUTHOR>
 * @version $ EngineOfflineDispatchExtDto, v 0.1 2024/1/5 15:53 yye.xu Exp $
 */

@Data
public class EngineOfflineDispatchExtDto {

    private UserDetailDto userDetail;
    private Map dispatchDetail;

    @JsonIgnore
    @JSONField(serialize = false)
    public boolean isValid(){
        return userDetail != null && dispatchDetail != null;
    }

    @Data
    public static class UserDetailDto {

        private String app;
        private String mobile;
        private String innerApp;
        private Long crowdId;

        private Long strategyExecLogId;
        private Long strategyGroupId;
        private String strategyGroupName;
        private Integer strategySendRuler;
    }
}