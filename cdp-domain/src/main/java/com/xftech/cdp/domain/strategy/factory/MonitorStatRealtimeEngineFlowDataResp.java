package com.xftech.cdp.domain.strategy.factory;

import com.xftech.cdp.api.dto.resp.AbsMonitorListResp;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version $ MonitorListGroupResp, v 0.1 2023/12/14 15:20 lingang.han Exp $
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MonitorStatRealtimeEngineFlowDataResp extends AbsMonitorListResp {


    @ApiModelProperty(value = "pk")
    private Long id;

    @ApiModelProperty(value = "决策日期")
    private LocalDate bizDate;

    @ApiModelProperty(value = "人群包刷新完成时间")
    private LocalDateTime crowdRefreshTime;

    @ApiModelProperty(value = "人群包用户数")
    private Integer crowdUserNum;

    @ApiModelProperty(value = "触发事件总人次")
    private Integer eventSum;

    @ApiModelProperty(value = "事件条件过滤人次")
    private Integer filterEventSum;

    @ApiModelProperty(value = "分组后进入引擎人次")
    private Integer engineSum;

    @ApiModelProperty(value = "分组后进入引擎人数")
    private Integer engineNum;

    @ApiModelProperty(value = "决策结果为营销人次")
    private Integer marketSum;

    @ApiModelProperty(value = "决策结果为营销人数")
    private Integer marketNum;

    @ApiModelProperty(value = "排除标签过滤人次(决策结果为营销)")
    private Integer excludeMarketSum;

    @ApiModelProperty(value = "流控过滤人次")
    private Integer flowControlSum;

    @ApiModelProperty(value = "应发用户数")
    private Integer dispatchNum;

    @ApiModelProperty(value = "决策结果为不营销人次")
    private Integer notMarketSum;

    @ApiModelProperty(value = "决策结果为不营销人数")
    private Integer notMarketNum;

    @ApiModelProperty(value = "排除标签过滤人次(决策结果为不营销)")
    private Integer excludeNotMarketSum;

    @ApiModelProperty(value = "决策失败人次")
    private Integer decisionFailSum;

    @ApiModelProperty(value = "决策失败人数")
    private Integer decisionFailNum;

}
