package com.xftech.cdp.domain.event.model.annotation;

import com.xftech.cdp.domain.event.model.enums.DataProcessEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 数据转换处理器配置注解
 * <AUTHOR>
 * @version $ ProcessConfig, v 0.1 2024/11/12 17:54 snail Exp $
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface ProcessConfig {
    DataProcessEnum processor();
}
