package com.xftech.cdp.domain.strategy.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategySnapshotDo;
import org.springframework.stereotype.Component;

/**
 * 策略执行快照表操作
 *
 * <AUTHOR> xugong<PERSON>@mzsk.com
 * @date 2023/2/25 16:22
 */

@Component
public class StrategySnapshotRepository {

    /**
     * 插入一条策略执行快照
     *
     * @param strategySnapshotDo 策略执行快照对象
     */
    public void insert(StrategySnapshotDo strategySnapshotDo) {
        DBUtil.insert("strategySnapshot.insertSelective", strategySnapshotDo);
    }
}
