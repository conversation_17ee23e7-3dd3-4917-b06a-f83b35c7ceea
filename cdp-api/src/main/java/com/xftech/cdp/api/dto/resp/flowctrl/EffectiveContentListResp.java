package com.xftech.cdp.api.dto.resp.flowctrl;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @<NAME_EMAIL>
 */

@Data
public class EffectiveContentListResp {

    @ApiModelProperty("id")
    private Long contentId;

    @ApiModelProperty("id_名称")
    private String contentName;

    @ApiModelProperty("是否可选 0可选 1不可选")
    private Integer optional;

    @ApiModelProperty("当前规则是否选中 0未选中 1选中(当编辑规则的时候才会有值)")
    private Integer selected;
}
