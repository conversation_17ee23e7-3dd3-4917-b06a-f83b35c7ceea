package com.xftech.cdp.domain.flowctrl.service.impl;

import com.alibaba.fastjson.JSON;
import com.xftech.base.database.Page;
import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.req.flowctrl.FlowCtrlCreateReq;
import com.xftech.cdp.api.dto.req.flowctrl.FlowCtrlDetailReq;
import com.xftech.cdp.api.dto.req.flowctrl.FlowCtrlEffectiveContentReq;
import com.xftech.cdp.api.dto.req.flowctrl.FlowCtrlListReq;
import com.xftech.cdp.api.dto.req.flowctrl.FlowCtrlOperateReq;
import com.xftech.cdp.api.dto.req.flowctrl.FlowCtrlUpdateReq;
import com.xftech.cdp.api.dto.resp.flowctrl.EffectiveContentListResp;
import com.xftech.cdp.api.dto.resp.flowctrl.FlowCtrlDetailResp;
import com.xftech.cdp.api.dto.resp.flowctrl.FlowCtrlListResp;
import com.xftech.cdp.domain.cache.CacheFlowCtrlSerivce;
import com.xftech.cdp.domain.flowctrl.model.enums.ChannelTypeEnum;
import com.xftech.cdp.domain.flowctrl.model.enums.EffectiveContentTypeEnum;
import com.xftech.cdp.domain.flowctrl.model.enums.FlowCtrlRuleStatusEnum;
import com.xftech.cdp.domain.flowctrl.model.enums.FlowCtrlTypeEnum;
import com.xftech.cdp.domain.flowctrl.model.enums.StrategyTypeEnum;
import com.xftech.cdp.domain.flowctrl.repository.FlowCtrlRepository;
import com.xftech.cdp.domain.flowctrl.service.FlowCtrlService;
import com.xftech.cdp.domain.strategy.repository.StrategyRepository;
import com.xftech.cdp.domain.strategy.service.impl.StrategyCommonService;
import com.xftech.cdp.infra.exception.BizException;
import com.xftech.cdp.infra.repository.cdp.flowctrl.po.FlowCtrlDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;
import com.xftech.cdp.infra.utils.ListUtils;
import com.xftech.cdp.infra.utils.MapUtils;
import com.xftech.cdp.infra.utils.OperateLogObjectIdUtils;
import com.xftech.cdp.infra.utils.SsoUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 流控前后端交互
 *
 * @<NAME_EMAIL>
 * @date 2023/3/24 17:06
 */

@Slf4j
@Service
public class FlowCtrlServiceImpl implements FlowCtrlService {
    @Autowired
    FlowCtrlRepository flowCtrlRepository;

    @Autowired
    StrategyRepository strategyRepository;

    @Autowired
    private CacheFlowCtrlSerivce cacheFlowCtrlSerivce;

    @Autowired
    private StrategyCommonService strategyCommonService;

    public static final int YES = 1;
    public static final int NO = 0;

    @Override
    public boolean insert(FlowCtrlCreateReq flowCtrlCreateReq) {
        //校验参数
        this.verifyRuleConfig(flowCtrlCreateReq, null);
        // 新增全局规则需要转为type 2, bizType为空写入数据库

        //组装DO实体  新增业务线信息 25-03-26
        FlowCtrlDo flowCtrlDo = new FlowCtrlDo();
        flowCtrlDo.setStatus(FlowCtrlRuleStatusEnum.INIT.getCode());
        this.generateDo(StrategyTypeEnum.RULE_PAGE.getCode(), flowCtrlCreateReq, flowCtrlDo);
        flowCtrlDo.setCreatedOp(flowCtrlDo.getUpdatedOp());
        //入库
        boolean result = cacheFlowCtrlSerivce.insert(flowCtrlDo);
        OperateLogObjectIdUtils.set(Collections.singletonList(flowCtrlDo.getId()));
        return result;
    }

    @Override
    public boolean update(FlowCtrlUpdateReq flowCtrlUpdateReq) {
        FlowCtrlDo existFlowCtrlDo = flowCtrlRepository.selectById(flowCtrlUpdateReq.getId());
        if (Objects.isNull(existFlowCtrlDo)) {
            throw new BizException("规则不存在");
        }
        FlowCtrlCreateReq flowCtrlCreateReq = new FlowCtrlCreateReq();
        flowCtrlCreateReq.setName(flowCtrlUpdateReq.getName());
        flowCtrlCreateReq.setType(flowCtrlUpdateReq.getType());
        flowCtrlCreateReq.setDescription(flowCtrlUpdateReq.getDescription());
        flowCtrlCreateReq.setEffectiveContent(flowCtrlUpdateReq.getEffectiveContent());
        flowCtrlCreateReq.setLimitDays(flowCtrlUpdateReq.getLimitDays());
        flowCtrlCreateReq.setLimitTimes(flowCtrlUpdateReq.getLimitTimes());
        flowCtrlCreateReq.setBizType(flowCtrlUpdateReq.getBizType());

        //校验参数
        this.verifyRuleConfig(flowCtrlCreateReq, flowCtrlUpdateReq.getId());
        //组装DO实体
        existFlowCtrlDo.setEffectiveChannel(null);
        existFlowCtrlDo.setEffectiveStrategy(null);
        this.generateDo(existFlowCtrlDo.getStrategyType(), flowCtrlCreateReq, existFlowCtrlDo);
        OperateLogObjectIdUtils.set(Collections.singletonList(existFlowCtrlDo.getId()));
        return cacheFlowCtrlSerivce.updateById(existFlowCtrlDo);

    }

    @Override
    public PageResultResponse<FlowCtrlListResp> list(FlowCtrlListReq flowCtrlListReq) {
        List<FlowCtrlListResp> flowCtrlListRespList = new ArrayList<>();
        //模糊匹配策略名称，获取匹配的策略id 加入查询
//        if (flowCtrlListReq.getStrategyName() != null && !flowCtrlListReq.getStrategyName().isEmpty()) {
//            List<StrategyDo> strategyDoList = strategyRepository.getByNameLike(flowCtrlListReq.getStrategyName());
//            if (CollectionUtils.isEmpty(strategyDoList)) {
//                return null;
//            }
//            flowCtrlListReq.setStrategyIds(ListUtils.distinctMap(strategyDoList, StrategyDo::getId));
//        }

        if("all".equals(flowCtrlListReq.getBizType())) {
            // 全部业务线 兼容老配置
            flowCtrlListReq.setBizType(null);
            flowCtrlListReq.setType(FlowCtrlTypeEnum.CHANNEL.getType());
        }

        Page<FlowCtrlDo> records = flowCtrlRepository.selectByPage(flowCtrlListReq);
        List<FlowCtrlDo> list = records.getList();
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        //都号分隔的策略id的list
        List<String> strategyStringList = ListUtils.distinctMap(list, FlowCtrlDo::getEffectiveStrategy);

        //处理成单个策略id的list
        List<Long> allStrategyIds = new ArrayList<>();
        for (String strategyString : strategyStringList) {
            if (StringUtils.isNotBlank(strategyString)) {
                String[] strategyArr = strategyString.split(",");
                for (String StrategyItem : strategyArr) {
                    allStrategyIds.add(Long.parseLong(StrategyItem));
                }
            }
        }
        Map<Long, StrategyDo> strategyDoMap = new HashMap<>();

        if (!CollectionUtils.isEmpty(allStrategyIds)) {
            List<StrategyDo> strategyDoListALl = strategyRepository.getByIds(allStrategyIds);
            strategyDoMap = MapUtils.listToMap(strategyDoListALl, StrategyDo::getId);
        }

        log.info("FlowCtrlServiceImpl list strategyDoMap:{}", JSON.toJSONString(strategyDoMap));
        for (FlowCtrlDo item : list) {
            FlowCtrlListResp flowCtrlListResp = new FlowCtrlListResp();
            flowCtrlListResp.setId(item.getId());
            flowCtrlListResp.setName(item.getName());
            flowCtrlListResp.setStatus(item.getStatus());
            flowCtrlListResp.setUpdatedTime(item.getUpdatedTime());
            flowCtrlListResp.setUpdatedOp(item.getUpdatedOp());
            flowCtrlListResp.setType(item.getType());
            flowCtrlListResp.setStrategyType(item.getStrategyType());
//            flowCtrlListResp.setEffectiveContent(item.getEffectiveContent(strategyDoMap));
            flowCtrlListResp.setEffectiveChannel(item.getEffectiveChannel());
            flowCtrlListResp.setCreatedOp(item.getCreatedOp());
            flowCtrlListResp.setBizType(item.getBizType());
            flowCtrlListRespList.add(flowCtrlListResp);

        }
        if(flowCtrlListRespList.size() > 1) {
            flowCtrlListRespList.sort(Comparator.comparing(FlowCtrlListResp::getUpdatedTime).reversed());
        }
        return new PageResultResponse<>(flowCtrlListRespList, records.getBeginNum(), records.getFetchNum(), records.getTotalNum());
    }

    @Override
    public FlowCtrlDetailResp getDetail(FlowCtrlDetailReq flowCtrlDetailReq) {
        FlowCtrlDo flowCtrlDo = flowCtrlRepository.selectById(flowCtrlDetailReq.getRuleId());
        if (Objects.isNull(flowCtrlDo)) {
            throw new BizException("规则不存在");
        }
        FlowCtrlDetailResp flowCtrlDetailResp = new FlowCtrlDetailResp();
        flowCtrlDetailResp.setId(flowCtrlDo.getId());
        flowCtrlDetailResp.setName(flowCtrlDo.getName());
        flowCtrlDetailResp.setDescription(flowCtrlDo.getDescription());
        flowCtrlDetailResp.setType(flowCtrlDo.getType());
        flowCtrlDetailResp.setDayCount(flowCtrlDo.getDayCount());
        flowCtrlDetailResp.setWeekCount(flowCtrlDo.getWeekCount());
        flowCtrlDetailResp.setMonthCount(flowCtrlDo.getMonthCount());
        flowCtrlDetailResp.setStatus(flowCtrlDo.getStatus());
        flowCtrlDetailResp.setLimitDays(flowCtrlDo.getLimitDays());
        flowCtrlDetailResp.setLimitTimes(flowCtrlDo.getLimitTimes());
        flowCtrlDetailResp.setBizType(flowCtrlDo.getBizType());
        flowCtrlDetailResp.setEffectiveChannel(flowCtrlDo.getEffectiveChannel());
        return flowCtrlDetailResp;
    }

    @Override
    public boolean operate(FlowCtrlOperateReq flowCtrlOperateReq) {
        FlowCtrlDo flowCtrlDo = flowCtrlRepository.selectById(flowCtrlOperateReq.getRuleId());
        if (Objects.isNull(flowCtrlDo)) {
            throw new BizException("规则不存在");
        }
        if(FlowCtrlRuleStatusEnum.EFFECTIVE.getCode().equals(flowCtrlOperateReq.getRunType())
                || FlowCtrlRuleStatusEnum.CLOSE.getCode().equals(flowCtrlOperateReq.getRunType())) {
            //判断是否能开启规则
            if (FlowCtrlRuleStatusEnum.EFFECTIVE.getCode().equals(flowCtrlOperateReq.getRunType())
                    && !FlowCtrlTypeEnum.MULTI_STRATEGY.getType().equals(flowCtrlDo.getType())) {

                // 2025-05-20不再校验同一渠道下的业务线频控规则是否重复
//                boolean strategyTypeFlag = FlowCtrlTypeEnum.STRATEGY.getType().equals(flowCtrlDo.getType());
//                // 当前要开启的规则中的策略/渠道 是否已在其它生效规则中，若是则不能开启
//                List<String> existEffectiveContent = this.existEffectiveContent(flowCtrlDo.getType(), null, flowCtrlDo.getBizType());
//                String currentRuleEffectContentStr = strategyTypeFlag ? flowCtrlDo.getEffectiveStrategy() : flowCtrlDo.getEffectiveChannel();
//                List<String> currentRuleEffectContent = this.convertList(currentRuleEffectContentStr);
//                List<String> intersection = currentRuleEffectContent.stream().filter(existEffectiveContent::contains).collect(Collectors.toList());
//
//                if (!CollectionUtils.isEmpty(intersection)) {
//                    StringBuilder sb = new StringBuilder();
//                    sb.append(FlowCtrlTypeEnum.getInstance(flowCtrlDo.getType()).getDesc()).append("[");
//                    for (String s : intersection) {
//                        String allFlag = "0".equals(s) ? "全部策略" : s;
//                        sb.append(strategyTypeFlag ? allFlag : ChannelTypeEnum.getInstance(Integer.parseInt(s)).getDesc()).append("、");
//                    }
//                    sb.deleteCharAt(sb.lastIndexOf("、"));
//                    sb.append("]");
//                    throw new BizException(sb + "已在其它生效规则中，该规则不可开启生效");
//                }
            }

            flowCtrlDo.setStatus(flowCtrlOperateReq.getRunType());
            return cacheFlowCtrlSerivce.updateStatusById(flowCtrlDo);
        } else if(FlowCtrlRuleStatusEnum.DELETED.getCode().equals(flowCtrlOperateReq.getRunType())) {
            // 未开启的规则允许删除
            if(!flowCtrlDo.getStatus().equals(FlowCtrlRuleStatusEnum.EFFECTIVE.getCode())) {
                flowCtrlDo.setStatus(flowCtrlOperateReq.getRunType());
                flowCtrlDo.setDFlag(1);
                return cacheFlowCtrlSerivce.updateStatusById(flowCtrlDo);
            } else {
                new BizException(flowCtrlDo.getId() + "开启状态，该规则不可删除");
            }
        }
        return false;
    }

    /**
     * 查询触达渠道按业务线判断是否重复
     * @param flowCtrlEffectiveContentReq 请求参数
     * @return
     */
    @Override
    public List<EffectiveContentListResp> getEffectiveContentList(FlowCtrlEffectiveContentReq flowCtrlEffectiveContentReq) {

        if("all".equals(flowCtrlEffectiveContentReq.getBizType())) {
            // 全局规则兼容之前的逻辑
            flowCtrlEffectiveContentReq.setType(FlowCtrlTypeEnum.CHANNEL.getType());
        }

        List<String> existEffectiveContent = this.existEffectiveContent(flowCtrlEffectiveContentReq.getType(), flowCtrlEffectiveContentReq.getRuleId(), flowCtrlEffectiveContentReq.getBizType());
        List<EffectiveContentListResp> effectiveContentRespList = new ArrayList<>();

        if (FlowCtrlTypeEnum.STRATEGY.getType().equals(flowCtrlEffectiveContentReq.getType()) || FlowCtrlTypeEnum.MULTI_STRATEGY.getType().equals(flowCtrlEffectiveContentReq.getType())) {
            if (FlowCtrlTypeEnum.STRATEGY.getType().equals(flowCtrlEffectiveContentReq.getType())) {
                EffectiveContentListResp allEffectiveStrategy = new EffectiveContentListResp();
                allEffectiveStrategy.setContentId(Long.valueOf(ChannelTypeEnum.ALL.getType()));
                allEffectiveStrategy.setContentName("全部策略");
                Integer optional = !CollectionUtils.isEmpty(existEffectiveContent) && existEffectiveContent.contains(String.valueOf(ChannelTypeEnum.ALL.getType())) ? YES : NO;
                allEffectiveStrategy.setOptional(optional);
                effectiveContentRespList.add(allEffectiveStrategy);
            }

            List<StrategyDo> strategyList = strategyRepository.selectAll();
            for (StrategyDo item : strategyList) {
                EffectiveContentListResp effectiveContentListResp = new EffectiveContentListResp();
                effectiveContentListResp.setContentId(item.getId());
                effectiveContentListResp.setContentName(item.getId() + "_" + item.getName());
                effectiveContentListResp.setOptional(!CollectionUtils.isEmpty(existEffectiveContent) && existEffectiveContent.contains(String.valueOf(item.getId())) ? YES : NO);
                effectiveContentListResp.setSelected(NO);
                effectiveContentRespList.add(effectiveContentListResp);
            }

        } else if (FlowCtrlTypeEnum.CHANNEL.getType().equals(flowCtrlEffectiveContentReq.getType())) {
            List<ChannelTypeEnum> effectiveChannelEnumList = Arrays.stream(ChannelTypeEnum.values()).collect(Collectors.toList());
            effectiveContentRespList = effectiveChannelEnumList.stream().map(item -> {
                EffectiveContentListResp effectiveContentListResp = new EffectiveContentListResp();
                effectiveContentListResp.setContentId(Long.valueOf(item.getType()));
                effectiveContentListResp.setContentName(item.getDesc());
                effectiveContentListResp.setOptional(!CollectionUtils.isEmpty(existEffectiveContent) && existEffectiveContent.contains(String.valueOf(item.getType())) ? YES : NO);
                effectiveContentListResp.setSelected(NO);
                return effectiveContentListResp;
            }).collect(Collectors.toList());
        } else if (FlowCtrlTypeEnum.BIZ_LINE.getType().equals(flowCtrlEffectiveContentReq.getType())) { //  业务线规则
            List<ChannelTypeEnum> effectiveChannelEnumList = Arrays.stream(ChannelTypeEnum.values()).collect(Collectors.toList());
            effectiveContentRespList = effectiveChannelEnumList.stream().map(item -> {
                EffectiveContentListResp effectiveContentListResp = new EffectiveContentListResp();
                effectiveContentListResp.setContentId(Long.valueOf(item.getType()));
                effectiveContentListResp.setContentName(item.getDesc());
                effectiveContentListResp.setOptional(!CollectionUtils.isEmpty(existEffectiveContent) && existEffectiveContent.contains(String.valueOf(item.getType())) ? YES : NO);
                effectiveContentListResp.setSelected(NO);
                return effectiveContentListResp;
            }).collect(Collectors.toList());
        }


        //编辑才有的逻辑
        if (Objects.nonNull(flowCtrlEffectiveContentReq.getRuleId())) {
            this.checkSelected(flowCtrlEffectiveContentReq, effectiveContentRespList);
        }
        return effectiveContentRespList;
    }

    /**
     * 匹配当前规则已经选中的策略/渠道
     *
     * @param flowCtrlEffectiveContentReq 参数
     * @param effectiveContentRespList    策略/渠道列表
     */
    public void checkSelected(FlowCtrlEffectiveContentReq flowCtrlEffectiveContentReq, List<EffectiveContentListResp> effectiveContentRespList) {
        List<String> ruleEffectiveContentList = new ArrayList<>();
        FlowCtrlDo flowCtrlDo = flowCtrlRepository.selectById(flowCtrlEffectiveContentReq.getRuleId());
        if ((FlowCtrlTypeEnum.STRATEGY.getType().equals(flowCtrlEffectiveContentReq.getType()) || FlowCtrlTypeEnum.MULTI_STRATEGY.getType().equals(flowCtrlEffectiveContentReq.getType())) && StringUtils.isNotBlank(flowCtrlDo.getEffectiveStrategy())) {
            ruleEffectiveContentList = this.convertList(flowCtrlDo.getEffectiveStrategy());
        } else if (FlowCtrlTypeEnum.CHANNEL.getType().equals(flowCtrlEffectiveContentReq.getType()) && StringUtils.isNotBlank(flowCtrlDo.getEffectiveChannel())) {
            ruleEffectiveContentList = this.convertList(flowCtrlDo.getEffectiveChannel());
        }
        for (EffectiveContentListResp effectiveContentListResp : effectiveContentRespList) {
            if (ruleEffectiveContentList.contains(String.valueOf(effectiveContentListResp.getContentId()))) {
                effectiveContentListResp.setSelected(YES);
            }
        }
    }

    /**
     * insert or update check
     * @param flowCtrlCreateReq
     * @param flowCtrlId
     */
    public void verifyRuleConfig(FlowCtrlCreateReq flowCtrlCreateReq, Long flowCtrlId) {
        strategyCommonService.verifyRuleConfig(flowCtrlCreateReq.getLimitDays(), flowCtrlCreateReq.getLimitTimes());
        // 校验规则名称
        FlowCtrlDo existDo = flowCtrlRepository.selectByName(flowCtrlCreateReq.getName());
        if (Objects.nonNull(existDo) && !existDo.getId().equals(flowCtrlId)) {
            throw new BizException("规则名称已存在");
        }
        //校验生效策略/渠道配置
        List<String> effectiveContent = flowCtrlCreateReq.getEffectiveContent();
        if (!FlowCtrlTypeEnum.MULTI_STRATEGY.getType().equals(flowCtrlCreateReq.getType())) {
            FlowCtrlTypeEnum flowCtrlTypeEnum = FlowCtrlTypeEnum.getInstance(flowCtrlCreateReq.getType());
            if (effectiveContent.contains(String.valueOf(ChannelTypeEnum.ALL.getType())) && effectiveContent.size() > 1) {
                String typeDesc = flowCtrlTypeEnum.getDesc();
                throw new BizException("不能同时配置全部" + typeDesc + "和其他" + typeDesc);
            }
            if ("all".equals(flowCtrlCreateReq.getBizType())) {
                // 全局规则兼容之前
                flowCtrlCreateReq.setType(flowCtrlTypeEnum.CHANNEL.getType());
            }
//            List<String> existEffectiveContent = this.existEffectiveContent(flowCtrlCreateReq.getType(), flowCtrlId, flowCtrlCreateReq.getBizType());
//            boolean existSame = effectiveContent.stream().anyMatch(existEffectiveContent::contains);
//            if (existSame) {
//                throw new BizException("同一个策略/渠道仅支持存在1个生效中的规则");
//            }
        }
        //校验配置的策略/渠道是否存在
        boolean existFlag;
        if (FlowCtrlTypeEnum.STRATEGY.getType().equals(flowCtrlCreateReq.getType()) || FlowCtrlTypeEnum.MULTI_STRATEGY.getType().equals(flowCtrlCreateReq.getType())) {
//            List<StrategyDo> strategyDoList = strategyRepository.selectAll();
//            List<String> strategyIdList = strategyDoList.stream().map(item -> String.valueOf(item.getId())).collect(Collectors.toList());
//            existFlag = effectiveContent.stream().filter(item -> !item.equals(String.valueOf(ChannelTypeEnum.ALL.getType()))).anyMatch(item -> !strategyIdList.contains(item));
            existFlag = false;
        } else {
            List<ChannelTypeEnum> effectiveChannelEnumList = Arrays.stream(ChannelTypeEnum.values()).collect(Collectors.toList());
            List<String> effectiveChannel = effectiveChannelEnumList.stream().map(item -> String.valueOf(item.getType())).collect(Collectors.toList());
            existFlag = effectiveContent.stream().anyMatch(item -> !effectiveChannel.contains(item));
        }
        if (existFlag) {
            throw new BizException("策略/渠道编号配置有误");
        }

    }

    public void generateDo(Integer strategyType, FlowCtrlCreateReq flowCtrlCreateReq, FlowCtrlDo flowCtrlDo) {
        flowCtrlDo.setName(flowCtrlCreateReq.getName());
        flowCtrlDo.setDescription(flowCtrlCreateReq.getDescription());
        flowCtrlDo.setUpdatedOp(SsoUtil.get().getName());

        FlowCtrlTypeEnum.getInstance(flowCtrlCreateReq.getType());
        String effectiveContent = flowCtrlCreateReq.getEffectiveContent().stream().map(String::valueOf).collect(Collectors.joining(","));
        if (FlowCtrlTypeEnum.CHANNEL.getType().equals(flowCtrlCreateReq.getType()) ||
                FlowCtrlTypeEnum.BIZ_LINE.getType().equals(flowCtrlCreateReq.getType()) ||
                "all".equals(flowCtrlCreateReq.getBizType())) {
            flowCtrlDo.setEffectiveChannel(effectiveContent);
        } else {
            flowCtrlDo.setEffectiveStrategy(effectiveContent);
        }
        flowCtrlDo.setType(FlowCtrlTypeEnum.getInstance(flowCtrlCreateReq.getType()).getType());
//        flowCtrlDo.setStrategyType(strategyType);
        flowCtrlDo.setLimitDays(flowCtrlCreateReq.getLimitDays());
        flowCtrlDo.setLimitTimes(flowCtrlCreateReq.getLimitTimes());
        if(!"all".equals(flowCtrlCreateReq.getBizType())) {
            flowCtrlDo.setBizType(flowCtrlCreateReq.getBizType());
        }
    }

    /**
     * 查询所有生效的策略或渠道
     *
     * @param type       规则类型 策略/渠道
     * @param flowCtrlId 流控规则id（id不为空的时候则查询除此规则外的所有生效的策略或渠道）
     * @return 所有生效的策略或渠道
     */
    public List<String> existEffectiveContent(Integer type, Long flowCtrlId, String bizType) {
        List<FlowCtrlDo> flowCtrlDoList = flowCtrlRepository.selectAllEffectContentByType(type, flowCtrlId, bizType);
        if (CollectionUtils.isEmpty(flowCtrlDoList)) {
            return Collections.emptyList();
        }
        List<String> existEffectiveContentSplit;
        List<String> existEffectiveContent = new ArrayList<>();
        if (FlowCtrlTypeEnum.STRATEGY.getType().equals(type) || FlowCtrlTypeEnum.MULTI_STRATEGY.getType().equals(type)) {
            existEffectiveContentSplit = flowCtrlDoList.stream().map(FlowCtrlDo::getEffectiveStrategy).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        } else {// 查询业务线和
            existEffectiveContentSplit = flowCtrlDoList.stream().map(FlowCtrlDo::getEffectiveChannel).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        }
        if (!CollectionUtils.isEmpty(existEffectiveContentSplit)) {
            existEffectiveContentSplit.forEach(item -> existEffectiveContent.addAll(this.convertList(item)));
        }
        return existEffectiveContent;
    }

    List<String> convertList(String oriString) {
        return Arrays.asList(oriString.split(","));
    }

    /**
     * 获取符合当前执行渠道的流控规则
     *
     * @param flowCtrlTypeEnum  流控类型
     * @param effectiveTypeEnum 生效类型
     * @param strategyId        策略ID
     * @param channel           渠道
     * @return 流控规则
     */
    @Override
    public Optional<FlowCtrlDo> getByType(FlowCtrlTypeEnum flowCtrlTypeEnum, EffectiveContentTypeEnum effectiveTypeEnum, Long strategyId, Integer channel) {
        return Optional.ofNullable(flowCtrlRepository.getByType(flowCtrlTypeEnum, effectiveTypeEnum, strategyId, channel));
    }
}
