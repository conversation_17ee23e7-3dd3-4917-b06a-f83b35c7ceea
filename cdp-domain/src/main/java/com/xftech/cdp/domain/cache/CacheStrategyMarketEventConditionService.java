package com.xftech.cdp.domain.cache;

import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketEventConditionDo;

import java.util.List;

public interface CacheStrategyMarketEventConditionService {

    /**
     * 根据策略ID查询实时标签配置
     *
     * @param strategyId 策略ID
     * @return 实时标签配置
     */
    List<StrategyMarketEventConditionDo> getByStrategyId(Long strategyId);

    /**
     * 插入
     *
     * @param param 对象
     * @return 是否插入成功标识
     */
    boolean insert(StrategyMarketEventConditionDo param);

    /**
     * 插入
     *
     * @param strategyMarketEventConditionDoList 对象
     * @return 是否插入成功标识
     */
    boolean insertBatch(List<StrategyMarketEventConditionDo> strategyMarketEventConditionDoList);

    /**
     * 根据主键id更新
     *
     * @param param 对象
     * @return 是否更新成功标识
     */
    boolean updateById(StrategyMarketEventConditionDo param);

    void deleteBatch(List<Long> delEventConditionIdList);

    void updateEventConditionCache(Long strategyId);
}
