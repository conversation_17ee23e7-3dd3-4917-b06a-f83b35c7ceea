package com.xftech.cdp.infra.client.dingtalk.config;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/2/14
 */
@Component
@Setter
@Getter
@RefreshScope
@ConfigurationProperties(prefix = "dingtalk")
public class DingTalkConfig {

    private String alarmUrl = "https://oapi.dingtalk.com/robot/send?access_token=e3060e687917359a9d0570468665121de4f1a591b928269ae21c5455e0b3e502";

    private String adsAlarmUrl = "https://oapi.dingtalk.com/robot/send?access_token=8b77c343eb62c753580c72752122b074af21d35644fbce49ed88683e77daf443";

    private String atMobile;

    private String atMobileAds = "15210648300,13294197006";

    public List<String> atMobileList() {
        if (StringUtils.isNotBlank(atMobile)) {
            String[] atMobiles = atMobile.split(",");
            return Arrays.asList(atMobiles);
        }
        return Collections.emptyList();
    }

    public List<String> atMobileAdsList() {
        if (StringUtils.isNotBlank(atMobileAds)) {
            String[] atMobiles = atMobileAds.split(",");
            return Arrays.asList(atMobiles);
        }
        return Collections.emptyList();
    }
}
