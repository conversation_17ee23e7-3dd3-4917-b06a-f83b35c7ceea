/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.rocketmq.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version $ LendtradeDeratingRocketMessageVO, v 0.1 2024/8/16 15:14 lingang.han Exp $
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LendtradeDeratingRocketMessageVO {


    //APP维度用户号
    private Long userNo;

    //用户真实APP
    private String app;

    //用户innerApp
    private String innerApp;

    //事件发生时间戳(ms)
    private Long eventTime;

    //消息发送时间戳(ms)
    private Long sendTime;

    //原借款金额(元)
    private BigDecimal oriLoanAmt;

    //冻结后剩余可借金额(元)
    private BigDecimal loanAmt;

    //降额卡单时长(小时)
    private Integer expirationHour;

    //冻结类型:1风控冻额,2风控引导买会员卡
    private String freezeType;

}