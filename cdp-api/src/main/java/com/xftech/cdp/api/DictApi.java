package com.xftech.cdp.api;


import com.xftech.cdp.api.dto.base.Response;
import com.xftech.cdp.api.dto.req.dict.DictReq;
import com.xftech.cdp.api.dto.req.dict.EventMetaDataReq;
import com.xftech.cdp.api.dto.req.external.CallingSourceEnum;
import com.xftech.cdp.api.dto.resp.dict.CallingSourceResp;
import com.xftech.cdp.api.dto.resp.dict.DictResp;
import com.xftech.cdp.api.dto.resp.dict.EventMetaDataResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/10/17 23:11
 */
@Api(tags = "字典")
@RequestMapping("/dict")
public interface DictApi {

    @ApiOperation("查询字典")
    @PostMapping("/queryByDictCodes")
    Response<List<DictResp>> queryByDictCodes(@RequestBody DictReq dictReq);

    @ApiOperation("查询字典")
    @PostMapping("/queryDictCodes")
    Response<List<DictResp>> queryT0CallingSource(@RequestBody DictReq dictReq);

    @ApiOperation("查询事件元数据")
    @PostMapping("/queryEventMetaData")
    Response<List<EventMetaDataResp>> queryEventMetaData(@RequestBody EventMetaDataReq eventMetaDataReq);

    @ApiOperation("查询通知类T0策略的触发场景")
    @PostMapping("/queryT0CallingSource")
    Response<List<CallingSourceResp>> queryT0CallingSource();
}
