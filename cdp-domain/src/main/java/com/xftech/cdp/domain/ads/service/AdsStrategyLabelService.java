package com.xftech.cdp.domain.ads.service;

import com.xftech.cdp.domain.flowctrl.model.enums.StrategyTypeEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyInstantLabelTypeEnum;
import com.xftech.cdp.domain.strategy.vo.BatchAdsLabelVO;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventVO;

import java.util.Collection;
import java.util.Map;

/**
 * 策略数仓标签查询
 *
 * @<NAME_EMAIL>
 */
public interface AdsStrategyLabelService {


    Map<Long, Map<String, Object>> query(BizEventVO bizEventVO, Collection<String> labelNameList, StrategyInstantLabelTypeEnum queryType);

    Map<Long, Map<Long, Map<String, Object>>> queryLabelBatch(BizEventVO bizEventVO, StrategyInstantLabelTypeEnum queryType, Map<Long, Collection<String>> strategyId2LabelListMap);

    Map<Long, Map<String, Object>> queryBatch(BatchAdsLabelVO batchAdsLabelVO, Collection<String> labelNameList, StrategyInstantLabelTypeEnum queryType, StrategyTypeEnum strategyType);

    boolean filterLabelMinValue(Map<String, Object> param, Long userId, Long strategyId);

}
