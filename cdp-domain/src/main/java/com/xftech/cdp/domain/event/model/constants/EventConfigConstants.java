package com.xftech.cdp.domain.event.model.constants;

/**
 * 实时事件配置常量管理类
 * <AUTHOR>
 * @version $ EventConfigConstants, v 0.1 2024/11/13 17:48 snail Exp $
 */
public class EventConfigConstants {

    /** 实时事件配置的数据处理器列表属性key */
    public static final String FIELD_CONFIG_KEY = "processList";

    /** 实时事件配置的数据处理器列表中某个处理器对应的处理器类型属性key */
    public static final String FIELD_CONFIG_PROCESSOR_KEY = "process";

    /** 数据解析器参数链接字符 */
    public static final String FIELD_JOIN_CHAR = ".";

    /** 金额转换，分to元 */
    public static final String TO_YUAN = "toYuan";

    /** 金额转换，元to分 */
    public static final String TO_FEN = "toFen";

    /** 元转分之间的单位差 */
    public static final Integer YUAN_TO_FEN = 100;

    /** 消息体的属性名称 */
    public static final String EXT_PARAM_KEY = "ext";

    /** 参数解析后的扩展参数前缀信息 */
    public static final String EXT_PARAM_PREFIX = EXT_PARAM_KEY+FIELD_JOIN_CHAR;

    /** 扩展参数在数组中的位置 */
    public static final Integer EXT_PARAM_INDEX = 1;

    /** 默认的消息Tag */
    public static final String DEFAULT_TAG = "*";
}
