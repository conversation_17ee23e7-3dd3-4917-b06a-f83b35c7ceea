package com.xftech.cdp.infra.client.bi.model.req;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Map;

@Data
public class ModelMarketingReq extends BaseBiRequester {
    /**
     * 基本信息
     */
    @JsonProperty("context")
    @JSONField(name = "context")
    private Context context;


    /**
     * 基本信息
     */
    @JsonProperty("element_id_list")
    @JSONField(name = "element_id_list")
    private ArrayList<String> elementIdList;

    @Data
    public static class Context {
        @JsonProperty("id_card_number")
        @JSONField(name = "id_card_number")
        private String idCardNumber;

        @JsonProperty("mobile")
        @JSONField(name = "mobile")
        private String mobile;

        @JsonProperty("name")
        @JSONField(name = "name")
        private String name;

        @JsonProperty("current_loan_type")
        @JSONField(name = "current_loan_type")
        private String currentLoanType;

        @JsonProperty("device_id")
        @JSONField(name = "device_id")
        private String deviceId;

        @JsonProperty("app")
        @JSONField(name = "app")
        private String app;

        @JsonProperty("is_data_share")
        @JSONField(name = "is_data_share")
        private Boolean isDataShare = true;

    }
}
