package com.xftech.cdp.infra.config;

import java.util.concurrent.TimeUnit;

import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

@Component
public class HttpCustomConfig {

    @Value("${cdp.http.custom.connectTimeout:10000}")
    private Integer connectTimeout;
    @Value("${cdp.http.custom.connectionRequestTimeout:60000}")
    private Integer connectionRquestTimeout;
    @Value("${cdp.http.custom.socketTimeout:60000}")
    private Integer socketTimeout;

    @Value("${cdp.http.client.maxTotal:500}")
    private Integer httpMaxTotal;
    @Value("${cdp.http.client.maxPerRoute:120}")
    private Integer httpMaxPerRoute;
    @Value("${cdp.http.client.validateAfterInactivity:5000}")
    private Integer httpValidateAfterInactivity;
    @Value("${cdp.http.client.socketTimeout:25000}")
    private Integer httpSocketTimeout;
    @Value("${cdp.http.client.connectTimeout:25000}")
    private Integer httpConnectTimeout;
    @Value("${cdp.http.client.connectionRequestTimeout:5000}")
    private Integer httpConnectionRequestTimeout;
    @Value("${cdp.http.client.timeToLive:50}")
    private Integer timeToLive;

    public RequestConfig getCustomRequestConfig() {
        RequestConfig.Builder builder = RequestConfig.custom();
        builder.setConnectTimeout(this.connectTimeout).setConnectionRequestTimeout(this.connectionRquestTimeout).setSocketTimeout(this.socketTimeout);
        return builder.build();
    }

    //@Bean
    //public RestTemplate restTemplate(ClientHttpRequestFactory factory) {
    //    return new RestTemplate(factory);
    //}
    //
    //@Bean
    //public ClientHttpRequestFactory simpleClientHttpRequestFactory() {
    //    SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
    //    factory.setReadTimeout(25000);
    //    factory.setConnectTimeout(25000);
    //    return factory;
    //}

    @Bean
    public RestTemplate restTemplate(ClientHttpRequestFactory clientHttpRequestFactory) {
        return new RestTemplate(clientHttpRequestFactory);
    }

    @Bean
    public ClientHttpRequestFactory httpRequestFactory(HttpClient httpClient) {
        return new HttpComponentsClientHttpRequestFactory(httpClient);
    }

    @Bean
    public HttpClient httpClient() {
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager(timeToLive, TimeUnit.SECONDS);
        // 设置整个连接池最大连接数
        connectionManager.setMaxTotal(httpMaxTotal);
        // 路由是对maxTotal的细分
        connectionManager.setDefaultMaxPerRoute(httpMaxPerRoute);
        // 5秒未使用的连接需要进行验证
        connectionManager.setValidateAfterInactivity(httpValidateAfterInactivity);

        RequestConfig requestConfig = RequestConfig.custom()
                .setSocketTimeout(httpSocketTimeout)  //返回数据的超时时间
                .setConnectTimeout(httpConnectTimeout) //连接上服务器的超时时间
                .setConnectionRequestTimeout(httpConnectionRequestTimeout) //从连接池中获取连接的超时时间
                .build();

        return HttpClientBuilder.create()
                .setDefaultRequestConfig(requestConfig)
                .setConnectionManager(connectionManager)
                .build();
    }
}
