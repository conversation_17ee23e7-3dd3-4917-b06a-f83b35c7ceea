package com.xftech.cdp.adapter.strategy.label.request;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
/**
 * <AUTHOR>
 * @version $ BankCardListHistoryResponse, v 0.1 2024/11/25 14:12 tianshuo.qiu Exp $
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BankCardListHistoryResponse {
    private String code;
    private String message;
    private List<BankCardInfo> data;

    public static class BankCardInfo {
        private String  cardNo;
        private Integer status; // "status": 2,      // 1-默认卡 2-绑定 3-解绑

        public String  getCardNo() {
            return cardNo;
        }

        public void setCardNo(String  cardNo) {
            this.cardNo = cardNo;
        }



        public Integer getStatus() {
            return status;
        }
        public void setStatus(Integer status) {
            this.status = status;
        }


    }
}
