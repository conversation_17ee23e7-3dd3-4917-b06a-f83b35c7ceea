package com.xftech.cdp.domain.cache.impl;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.domain.cache.CacheCrowdPackService;
import com.xftech.cdp.domain.crowd.model.enums.CrowdStatusEnum;
import com.xftech.cdp.domain.crowd.repository.CrowdPackRepository;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPackDo;
import com.xftech.cdp.infra.utils.JsonUtil;
import com.xftech.cdp.infra.utils.RedisKeyUtils;
import com.xftech.cdp.infra.utils.RedisUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
public class CacheCrowdPackServiceImpl implements CacheCrowdPackService {

    @Autowired
    private CrowdPackRepository crowdPackRepository;
    @Autowired
    private RedisUtils redisUtils;

    private static final String DEFAULT_VALUE = "(null)";


    @Override
    public CrowdPackDo selectById(Long crowdId) {
        String redisKey = String.format("xyf-cdp:crowd_pack:%d", crowdId);
        String data = redisUtils.get(redisKey);
        if (StringUtils.isNotBlank(data) && data.equals(DEFAULT_VALUE)) {
            return null;
        }

        CrowdPackDo crowdPackDo = null;
        if (StringUtils.isNotBlank(data)) {
            crowdPackDo = JsonUtil.parse(data, CrowdPackDo.class);
        }

        if (Objects.isNull(crowdPackDo)) {
            crowdPackDo = DBUtil.selectOne("crowd.selectByPrimaryKey", crowdId);

            if (Objects.nonNull(crowdPackDo)) {
                redisUtils.set(redisKey, crowdPackDo, RedisUtils.DEFAULT_EXPIRE_SECONDS);
            } else {
                redisUtils.set(redisKey, DEFAULT_VALUE, RedisUtils.DEFAULT_EXPIRE_SECONDS);
            }
        }
        return crowdPackDo;
    }

    /**
     * 插入一条人群包记录
     *
     * @param crowdPackDo 人群包对象
     */
    @Override
    public boolean insert(CrowdPackDo crowdPackDo) {
        boolean isInsert = crowdPackRepository.insert(crowdPackDo);
        return isInsert;
    }

    /**
     * 根据人群包id更新人群包
     *
     * @param crowdPackDo 人群包对象
     */
    @Override
    public boolean updateById(CrowdPackDo crowdPackDo) {
        boolean isUpdate = crowdPackRepository.updateById(crowdPackDo);
        return isUpdate;
    }

    /**
     * 批量更新人群包
     *
     * @param crowdPackDos 人群包对象列表
     */
    public void updateBatchById(List<CrowdPackDo> crowdPackDos) {
        crowdPackRepository.updateBatchById(crowdPackDos);
    }

}
