/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.service.impl;

import cn.hutool.core.util.IdUtil;
import com.xftech.cdp.domain.stat.entity.OfflineDecisionRecordEntity;
import com.xftech.cdp.domain.stat.repository.OfflineDecisionRecordDao;
import com.xftech.cdp.domain.strategy.model.enums.StrategyMarketChannelEnum;
import com.xftech.cdp.domain.strategy.repository.UserDispatchDetailRepository;
import com.xftech.cdp.domain.strategy.service.AppBannerLogService;
import com.xftech.cdp.infra.config.ApolloUtil;
import com.xftech.cdp.infra.config.LogUtil;
import com.xftech.cdp.infra.rabbitmq.enums.DecisionResultEnum;
import com.xftech.cdp.infra.rabbitmq.vo.HitResult;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketChannelDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.UserDispatchDetailDo;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

/**
 *
 * <AUTHOR>
 * @version $ AppBannerLogServiceImpl, v 0.1 2024/5/6 14:47 benlin.wang Exp $
 */

@Slf4j
@Service
public class AppBannerLogServiceImpl implements AppBannerLogService {
    @Autowired
    private UserDispatchDetailRepository userDispatchDetailRepository;
    @Autowired
    private OfflineDecisionRecordDao offlineDecisionRecordDao;

    @Async("appBannerRecommendExecutorWrapper")
    public void saveLogs(Long userId,
                         Map<String, List<StrategyDo>> appBannerId2StrategyListMap,
                         Map<String, Map<Long, StrategyMarketChannelDo>> appBannerId2StrategyId2StrategyChannelMap,
                         Map<Long, HitResult> strategyId2HitResultMap,
                         List<Long> crowdNotHitStrategyIdList,
                         List<Long> labelNotHitStrategyIdList,
                         List<Long> abGroupNotHitStrategyIdList,
                         List<OfflineDecisionRecordEntity> succeedDecisionList,
                         List<UserDispatchDetailDo> dispatchDetailDoList,
                         LocalDateTime triggerDatetime, String tableNumber) {

        Set<Long> crowdNotHitStrategyIdSet = new HashSet<>(crowdNotHitStrategyIdList);
        Set<Long> labelNotHitStrategyIdSet = new HashSet<>(labelNotHitStrategyIdList);
        Set<Long> abGroupNotHitStrategyIdSet = new HashSet<>(abGroupNotHitStrategyIdList);

        List<OfflineDecisionRecordEntity> recordList = new ArrayList<>();

        appBannerId2StrategyListMap.forEach((appBannerId, strategyList) -> {
            for (StrategyDo strategyDo : strategyList) {
                Long strategyId = strategyDo.getId();
                if (abGroupNotHitStrategyIdSet.contains(strategyId)) {
                    OfflineDecisionRecordEntity recordEntity = new OfflineDecisionRecordEntity();
                    recordEntity.setTraceId("app_banner");
                    recordEntity.setStrategyId(strategyId);
                    recordEntity.setDecisionResult(0);
                    recordEntity.setDecisionDetail(String.format("用户:%d, app资源位id:%s, 策略id:%d", userId, appBannerId, strategyId));
                    recordEntity.setAppUserId(userId);
                    recordEntity.setCreatedTime(triggerDatetime);
                    recordEntity.setFailCode(DecisionResultEnum.APP_BANNER_NOT_HIT_AB_GROUP.getFailCode());
                    recordEntity.setFailReason(DecisionResultEnum.APP_BANNER_NOT_HIT_AB_GROUP.getFailReason());
                    recordEntity.setMarketChannel(StrategyMarketChannelEnum.APP_BANNER.getCode());
                    //没有命中分组，设为-1
                    recordEntity.setMarketChannelId(-1L);
                    recordEntity.setUnionId(IdUtil.fastSimpleUUID()); // 统一ID
                    recordEntity.setTableNameNo(tableNumber);
                    recordEntity.setTableName("offline_decision_record_" + tableNumber);
                    recordList.add(recordEntity);
                } else if (crowdNotHitStrategyIdSet.contains(strategyId)) {
                    OfflineDecisionRecordEntity recordEntity = new OfflineDecisionRecordEntity();
                    recordEntity.setTraceId("app_banner");
                    recordEntity.setStrategyId(strategyId);
                    recordEntity.setDecisionResult(0);
                    recordEntity.setDecisionDetail(String.format("用户:%d, app资源位id:%s, 策略id:%d", userId, appBannerId, strategyId));
                    recordEntity.setAppUserId(userId);
                    recordEntity.setCreatedTime(triggerDatetime);
                    recordEntity.setFailCode(DecisionResultEnum.APP_BANNER_NOT_HIT_CROWD_PACK.getFailCode());
                    recordEntity.setFailReason(DecisionResultEnum.APP_BANNER_NOT_HIT_CROWD_PACK.getFailReason());
                    recordEntity.setMarketChannel(StrategyMarketChannelEnum.APP_BANNER.getCode());
                    setMarketChannelId(userId, appBannerId2StrategyId2StrategyChannelMap, appBannerId, strategyId, recordEntity);
                    recordEntity.setUnionId(IdUtil.fastSimpleUUID()); // 统一ID
                    recordEntity.setTableNameNo(tableNumber);
                    recordEntity.setTableName("offline_decision_record_" + tableNumber);
                    recordList.add(recordEntity);
                } else if (labelNotHitStrategyIdSet.contains(strategyId)) {
                    OfflineDecisionRecordEntity recordEntity = new OfflineDecisionRecordEntity();
                    recordEntity.setTraceId("app_banner");
                    recordEntity.setStrategyId(strategyId);
                    recordEntity.setDecisionResult(0);
                    recordEntity.setDecisionDetail(String.format("用户:%d, app资源位id:%s, 策略id:%d", userId, appBannerId, strategyId));
                    recordEntity.setAppUserId(userId);
                    recordEntity.setCreatedTime(triggerDatetime);
                    recordEntity.setFailCode(DecisionResultEnum.APP_BANNER_NOT_HIT_LABELS.getFailCode());
                    recordEntity.setFailReason(DecisionResultEnum.APP_BANNER_NOT_HIT_LABELS.getFailReason());
                    recordEntity.setMarketChannel(StrategyMarketChannelEnum.APP_BANNER.getCode());
                    setMarketChannelId(userId, appBannerId2StrategyId2StrategyChannelMap, appBannerId, strategyId, recordEntity);
                    recordEntity.setUnionId(IdUtil.fastSimpleUUID()); // 统一ID
                    recordEntity.setTableNameNo(tableNumber);
                    if (strategyId2HitResultMap.containsKey(strategyId)) {
                        HitResult hitResult = strategyId2HitResultMap.get(strategyId);
                        recordEntity.setHitResultList(Collections.singletonList(hitResult));
                    }
                    recordEntity.setTableName("offline_decision_record_" + tableNumber);
                    recordList.add(recordEntity);
                }
            }
        });

        recordList.addAll(succeedDecisionList);

        LogUtil.logDebug("AppBannerLogServiceImpl saveLogs recordList={}", JSONObject.toJSONString(recordList));
        LogUtil.logDebug("AppBannerLogServiceImpl saveLogs dispatchDetailDoList={}", JSONObject.toJSONString(dispatchDetailDoList));

        if (CollectionUtils.isNotEmpty(recordList) && ApolloUtil.switchStatus("appBanner.decisionRecord.switch")) {
            try {
                offlineDecisionRecordDao.saveOfflineDecisionRecordWithoutTx(recordList);
            } catch (Exception ex) {
                log.error("[AppBanner] 保存OfflineDecision日志记录失败. recordList={}", JSONObject.toJSONString(recordList), ex);
            }
        }

        if (!dispatchDetailDoList.isEmpty()) {
            try {
                userDispatchDetailRepository.saveBatchWithoutTx(tableNumber, dispatchDetailDoList);
            } catch (Exception ex) {
                log.error("[AppBanner] 保存Dispatch日志记录失败. dispatchDetailDoList={}", JSONObject.toJSONString(dispatchDetailDoList), ex);
            }
        }
    }

    private static void setMarketChannelId(Long userId, Map<String, Map<Long, StrategyMarketChannelDo>> appBannerId2StrategyId2StrategyChannelMap, String appBannerId, Long strategyId, OfflineDecisionRecordEntity recordEntity) {
        Long marketChannelId = -1L;
        if (appBannerId2StrategyId2StrategyChannelMap.containsKey(appBannerId)) {
            Map<Long, StrategyMarketChannelDo> subM = appBannerId2StrategyId2StrategyChannelMap.get(appBannerId);
            if (subM.containsKey(strategyId)) {
                marketChannelId = subM.get(strategyId).getId();
            }
        }
        if (marketChannelId > 0) {
            recordEntity.setMarketChannelId(marketChannelId);
        } else {
            log.warn("[AppBanner] 记录没有命中标签的数据, 用户:{}, app资源位id:{}, 策略id:{}",
                    userId, appBannerId, strategyId);
        }
    }
}