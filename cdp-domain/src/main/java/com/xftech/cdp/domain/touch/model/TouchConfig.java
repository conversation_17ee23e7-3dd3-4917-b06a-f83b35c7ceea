package com.xftech.cdp.domain.touch.model;

import lombok.Data;
import java.util.Map;

/**
 * 触达配置模型
 * 包含触达相关的各种配置信息
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-23
 */
@Data
public class TouchConfig {
    
    /**
     * 触达类型：MKT(营销), NOTIFY(通知)
     * 对应现有的dispatchType字段
     */
    private String dispatchType;
    
    /**
     * 超时时间（毫秒）
     */
    private Integer timeout;
    
    /**
     * 重试次数
     */
    private Integer retryTimes;
    
    /**
     * 是否启用流控
     */
    private Boolean enableFlowControl;
    
    /**
     * 是否启用回执
     */
    private Boolean enableReceipt;
    
    /**
     * 是否启用监控上报
     */
    private Boolean enableMonitor;
    
    /**
     * 是否启用异步处理
     */
    private Boolean enableAsync;
    
    /**
     * 渠道特定配置
     * 不同渠道可能有不同的配置参数
     */
    private Map<String, Object> channelConfig;
    
    /**
     * 模板配置
     */
    private Map<String, Object> templateConfig;
    
    /**
     * 扩展配置
     */
    private Map<String, Object> extConfig;
    
    /**
     * 创建默认配置
     */
    public static TouchConfig createDefault() {
        TouchConfig config = new TouchConfig();
        config.setDispatchType("MKT");
        config.setTimeout(30000); // 30秒
        config.setRetryTimes(3);
        config.setEnableFlowControl(true);
        config.setEnableReceipt(true);
        config.setEnableMonitor(true);
        config.setEnableAsync(false);
        return config;
    }
    
    /**
     * 创建营销类型配置
     */
    public static TouchConfig createMarketingConfig() {
        TouchConfig config = createDefault();
        config.setDispatchType("MKT");
        config.setEnableFlowControl(true);
        return config;
    }
    
    /**
     * 创建通知类型配置
     */
    public static TouchConfig createNotifyConfig() {
        TouchConfig config = createDefault();
        config.setDispatchType("NOTIFY");
        config.setEnableFlowControl(false); // 通知类型通常不需要流控
        return config;
    }
    
    /**
     * 判断是否为营销类型
     */
    public boolean isMarketing() {
        return "MKT".equals(dispatchType);
    }
    
    /**
     * 判断是否为通知类型
     */
    public boolean isNotify() {
        return "NOTIFY".equals(dispatchType);
    }
    
    /**
     * 获取渠道配置值
     */
    public Object getChannelConfig(String key) {
        return channelConfig != null ? channelConfig.get(key) : null;
    }
    
    /**
     * 设置渠道配置值
     */
    public void setChannelConfig(String key, Object value) {
        if (channelConfig == null) {
            channelConfig = new java.util.HashMap<>();
        }
        channelConfig.put(key, value);
    }
    
    /**
     * 获取模板配置值
     */
    public Object getTemplateConfig(String key) {
        return templateConfig != null ? templateConfig.get(key) : null;
    }
    
    /**
     * 设置模板配置值
     */
    public void setTemplateConfig(String key, Object value) {
        if (templateConfig == null) {
            templateConfig = new java.util.HashMap<>();
        }
        templateConfig.put(key, value);
    }
    
    /**
     * 获取扩展配置值
     */
    public Object getExtConfig(String key) {
        return extConfig != null ? extConfig.get(key) : null;
    }
    
    /**
     * 设置扩展配置值
     */
    public void setExtConfig(String key, Object value) {
        if (extConfig == null) {
            extConfig = new java.util.HashMap<>();
        }
        extConfig.put(key, value);
    }
    
    /**
     * 合并配置
     * 将另一个配置的非空值合并到当前配置中
     */
    public void merge(TouchConfig other) {
        if (other == null) {
            return;
        }
        
        if (other.getDispatchType() != null) {
            this.setDispatchType(other.getDispatchType());
        }
        if (other.getTimeout() != null) {
            this.setTimeout(other.getTimeout());
        }
        if (other.getRetryTimes() != null) {
            this.setRetryTimes(other.getRetryTimes());
        }
        if (other.getEnableFlowControl() != null) {
            this.setEnableFlowControl(other.getEnableFlowControl());
        }
        if (other.getEnableReceipt() != null) {
            this.setEnableReceipt(other.getEnableReceipt());
        }
        if (other.getEnableMonitor() != null) {
            this.setEnableMonitor(other.getEnableMonitor());
        }
        if (other.getEnableAsync() != null) {
            this.setEnableAsync(other.getEnableAsync());
        }
        
        // 合并Map类型的配置
        if (other.getChannelConfig() != null) {
            if (this.channelConfig == null) {
                this.channelConfig = new java.util.HashMap<>();
            }
            this.channelConfig.putAll(other.getChannelConfig());
        }
        
        if (other.getTemplateConfig() != null) {
            if (this.templateConfig == null) {
                this.templateConfig = new java.util.HashMap<>();
            }
            this.templateConfig.putAll(other.getTemplateConfig());
        }
        
        if (other.getExtConfig() != null) {
            if (this.extConfig == null) {
                this.extConfig = new java.util.HashMap<>();
            }
            this.extConfig.putAll(other.getExtConfig());
        }
    }
    
    /**
     * 验证配置
     */
    public void validate() {
        if (dispatchType == null || dispatchType.trim().isEmpty()) {
            throw new IllegalArgumentException("dispatchType不能为空");
        }
        
        if (!"MKT".equals(dispatchType) && !"NOTIFY".equals(dispatchType)) {
            throw new IllegalArgumentException("dispatchType必须为MKT或NOTIFY");
        }
        
        if (timeout != null && timeout <= 0) {
            throw new IllegalArgumentException("timeout必须大于0");
        }
        
        if (retryTimes != null && retryTimes < 0) {
            throw new IllegalArgumentException("retryTimes不能小于0");
        }
    }
}
