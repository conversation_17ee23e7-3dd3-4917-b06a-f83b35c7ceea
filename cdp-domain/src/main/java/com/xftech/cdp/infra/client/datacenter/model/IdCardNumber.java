package com.xftech.cdp.infra.client.datacenter.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;


@Setter
@Getter
public class IdCardNumber {


    @JsonProperty("id_card_protyle")
    @JSONField(name = "id_card_protyle")
    private String idCardProtyle;

    @JsonProperty("id_card")
    @JSONField(name = "id_card")
    private transient String idCard;

}
