/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.rocketmq.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version $ BizEventRocketMessageVO, v 0.1 2024/3/18 14:01 lingang.han Exp $
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BizEventRocketMessageVO {

    /**
     * 用户id
     */
    private Long appUserId;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 事件触发 app
     */
    private String app;

    /**
     * 事件触发 innerApp
     */
    private String innerApp;

    /**
     * 渠道
     */
    private String utmSource;

    /**
     * 发送时间戳(ms)
     */
    private Long sendTime;

    /**
     * 事件触发时间戳(ms)
     */
    private Long eventTime;


    private ExtraData extraData;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExtraData {

        /**
         * event:userLogOff
         * 注销来源
         */
        private String source;

        /**
         * event:ExtractQuotaCardGuide
         * 提额场景
         */
        private String useManageType;

        /**
         * event:ExtractQuotaCardGuide
         * 定价组
         */
        private String priceGroup;

        /**
         * event:ExtractQuotaCardGuide
         * 初审提升额度(分)
         */
        private String expectedAdvanceAmount;

        /**
         * event:ExtractQuotaCardGuide
         * 提额状态
         */
        private String adjustmentStatus;

        /**
         * event:ExtractQuotaCardGuide
         * 是否已购买提额卡
         * 值为user时表示已购买
         */
        private String useTriggerType;

        /**
         * event:LifeRights
         * 麻雀策略ID
         */
        private Long lifeRightsCallBackStrategyId;
        /**
         * event:LifeRights
         * 生活权益发送状态
         */
        private Integer lifeRightCallBackStatus;

        /**
         * event:xDayInterestFree
         * 麻雀策略ID
         */
        private Long xDayInterestFreeStrategyId;
        /**
         * event:xDayInterestFree
         * 生活权益发送状态
         */
        private Integer xDayInterestFreeStatus;

        /**
         * event:vcSign
         * 会员卡签约 动作类型：1:首次签约， 2:开通续约，3取消续约，4支付成功，5 对应月月惊喜权益发放成功事件
         */
        private Integer type;

        /**
         * event:vcSign
         * 会员卡签约 签约类型：1:月卡，2:连续包月，3:季卡
         */
        private Integer cardType;

        /**
         * event:vcSign
         * 会员卡签约 扣款期数类型：1:首期，2:非首期
         */
        private Integer isRenew;

        /**
         * event:vcSign
         * 会员卡签约 支付类型：1:主动支付，2：批扣
         */
        private Integer deductType;

        /**
         * event:vcSign
         * 会员卡签约 订单实际支付金额。单位：分
         */
        private String orderPrice;

        /**
         * event:vcSign
         * 会员卡签约 是否参与续费活动
         */
        private Integer isRenewActivity;

        /**
         * event:vcSign
         * 会员卡签约 是否领取vip资金权益
         */
        private Integer isVipFund;

    }

    private SurpriseRightData surpriseRightData;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SurpriseRightData {
        //权益类型 1-超级提现机会；2-送提额卡；3-VIP特批资金；4-金融券；5-消费券
        private String surpriseRightType;

        //提额卡金额（单位分）
        private String surpriseRightLimitAmtCardAmount;

        // vip特批资金金额（单位分）
        private String surpriseRightVipFund;

        //金融券名称
        private String surpriseRightCashCouponName;

        //消费券名称
        private String surpriseRightConsumeCouponName;
    }
}