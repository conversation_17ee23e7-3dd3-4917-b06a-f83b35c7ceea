package com.xftech.cdp.infra.rabbitmq.factory.impl;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.proxy.Tracer;
import com.rabbitmq.client.Channel;
import com.xftech.cdp.domain.mq.MqConsumeService;
import com.xftech.cdp.infra.rabbitmq.config.CouponMqConfig;
import com.xftech.cdp.infra.rabbitmq.vo.CouponCallbackVO;
import com.xftech.cdp.infra.utils.JsonUtil;
import com.xftech.rabbitmq.UdpMqConsumerManager;
import com.xftech.rabbitmq.build.BindParamBuilder;
import com.xftech.rabbitmq.build.ConnectionBuilder;
import com.xftech.rabbitmq.consumer.TraceBatchConsumer;
import com.xftech.rabbitmq.core.UdpMqConsumer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

@Service
public class CouponMqService extends BaseMqService {
    private static final Logger logger = LoggerFactory.getLogger(CouponMqConfig.class);
    public static final String COUPON_CONNECTION_ID = "COUPON_CONNECTION_ID";
    public static final String COUPON_CONSUMER_01 = "COUPON_CONSUMER_01";
    public static final String COUPON_DEAD_CONSUMER = "COUPON_DEAD_CONSUMER";

    @Autowired
    private CouponMqConfig couponMqConfig;
    @Autowired
    private MqConsumeService mqConsumeService;

    public void init() throws IOException, TimeoutException {
        // 优惠券明细-创建连接、消费者
        logger.info("优惠券明细队列MQ-创建连接、消费者......");
        UdpMqConsumer couponConsumer = this.initConnection(COUPON_CONNECTION_ID);
        logger.info("优惠券明细队列MQ-连接创建成功......");
        this.createConsumer(couponConsumer, COUPON_CONSUMER_01);
        logger.info("优惠券明细队列MQ-消费者创建成功......");
        this.createDeadLetterConsumer(couponConsumer, COUPON_DEAD_CONSUMER);
        logger.info("优惠券明细队列MQ-死信队列消费者创建成功......");
    }

    public UdpMqConsumer initConnection(String connectionId) throws IOException, TimeoutException {
        logger.info("queueConfig={}", this);
        ConnectionBuilder builder = new ConnectionBuilder().host(couponMqConfig.getHost2())
                .port(couponMqConfig.getPort2())
                .username(couponMqConfig.getUsername2())
                .password(couponMqConfig.getPassword2())
                .vHostName(couponMqConfig.getVirtualHost2());
        return  UdpMqConsumerManager.getInstance().createUdpMqConsumer(builder, connectionId);
    }

    public void createConsumer(UdpMqConsumer udpMqConsumer, String channelId) throws IOException, TimeoutException {
        logger.info("queueConfig={}", this);
        BindParamBuilder builder = new BindParamBuilder()
                .exchangeName(couponMqConfig.getCouponCallbackExchange())
                .queueName(couponMqConfig.getCouponCallbackQueueName())
                .routingKey(couponMqConfig.getCouponCallbackRoutingKey())
                .exchangeType(couponMqConfig.getCouponExchangeType())
                .deadLetterExchange(couponMqConfig.getDeadLetterExchange())
                .deadLetterQueueName(couponMqConfig.getDeadLetterQueueName())
                .deadLetterRoutingKey(couponMqConfig.getDeadLetterRoutingKey());
        Channel channel = udpMqConsumer.bindMqQueue(builder, channelId);
        udpMqConsumer.basicConsume(new TraceBatchConsumer(channel, couponMqConfig.getBatchSize(), couponMqConfig.getTimeout()) {
            @Override
            public void batchHandleDelivery(List<Message> messageList) {
                try {
                    // 业务处理批量消息
                    logger.info("优惠券批量消费, size={}, list={}", messageList.size(), messageList);
                    List<CouponCallbackVO> couponCallbackVOList = messageList.stream().map(message -> {
                        CouponCallbackVO couponCallbackVO = null;
                        String body = null;
                        try {
                            body = new String(message.getBody(), "UTF-8");
                            logger.info("消息内容：{}", body);
                            couponCallbackVO = JSONObject.parseObject(body, CouponCallbackVO.class);
                        } catch (Exception e) {
                            logger.warn("优惠券批量消费异常，消息内容：{}", body, e);
                        }
                        return couponCallbackVO;
                    }).collect(Collectors.toList());
                    // 优惠券回传明细消费逻辑
                    try {
                        // 暂且这样特殊处理
                        mqConsumeService.couponCallbackProcess(couponCallbackVOList);
                    }catch (Exception ex) {
                        Tracer.logEvent("CallbackError", "CouponCallback");
                        logger.warn("优惠券批量消费异常, message={}, couponCallbackVOList:{}", JsonUtil.toJson(messageList), JsonUtil.toJson(couponCallbackVOList));
                    }
                    // 消息确认
                    long deliveryTag = messageList.get(messageList.size() - 1).getMessageProperties().getDeliveryTag();
                    logger.info("deliveryTag={}, batchSize={}", deliveryTag, messageList.size());
                    getChannel().basicAck(deliveryTag, true);
                } catch (Exception e) {
                    logger.error("优惠券批量消费异常", e);
                }
            }
        });
    }

    public void createDeadLetterConsumer(UdpMqConsumer udpMqConsumer, String channelId) throws IOException, TimeoutException {
        logger.info("queueConfig={}", this);
        BindParamBuilder builder = new BindParamBuilder()
                .exchangeName(couponMqConfig.getDeadLetterExchange())
                .queueName(couponMqConfig.getDeadLetterQueueName())
                .routingKey(couponMqConfig.getDeadLetterRoutingKey())
                .exchangeType(couponMqConfig.getDeadLetterExchangeType());
        Channel channel = udpMqConsumer.bindMqQueue(builder, channelId);
        udpMqConsumer.basicConsume(new TraceBatchConsumer(channel, couponMqConfig.getBatchSize(), couponMqConfig.getTimeout()) {
            @Override
            public void batchHandleDelivery(List<Message> messageList) {
                try {
                    // 业务处理批量消息
                    logger.info("死信队列，优惠券批量消费, size={}, list={}", messageList.size(), messageList);
                    List<CouponCallbackVO> couponCallbackVOList = messageList.stream().map(message -> {
                        CouponCallbackVO couponCallbackVO = null;
                        String body = null;
                        try {
                            body = new String(message.getBody(), "UTF-8");
                            couponCallbackVO = JSONObject.parseObject(body, CouponCallbackVO.class);
                        } catch (Exception e) {
                            logger.warn("死信队列，优惠券批量消费异常，消息内容：{}", body, e);
                        }
                        return couponCallbackVO;
                    }).collect(Collectors.toList());
                    try {
                        // 优惠券回传明细消费逻辑
                        mqConsumeService.couponCallbackProcess(couponCallbackVOList);
                    }catch (Exception ex){
                        Tracer.logEvent("callbackError", "CouponDeadLetter");
                        logger.warn("优惠券批量消费异常DeadLetter, message={}, couponCallbackVOList:{}", JsonUtil.toJson(messageList), JsonUtil.toJson(couponCallbackVOList));
                    }
                    // 消息确认
                    long deliveryTag = messageList.get(messageList.size() - 1).getMessageProperties().getDeliveryTag();
                    logger.info("死信队列，deliveryTag={}, batchSize={}, total={}", deliveryTag, messageList.size());
                    getChannel().basicAck(deliveryTag, true);
                } catch (Exception e) {
                    logger.info("死信队列，优惠券批量消费异常", e);
                }
            }
        });
    }
}
