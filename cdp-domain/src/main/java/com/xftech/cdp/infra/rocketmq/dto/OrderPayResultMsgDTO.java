/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.rocketmq.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @version $ OrderPayResultMsgDTO, v 0.1 2024/4/24 10:44 lingang.han Exp $
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderPayResultMsgDTO {

    /**
     * 商户外部订单号
     */
    public String outBizNo;

    /**
     * 发起支付贷款的业务单号
     */
    public String payBizOrderNo;

    /**
     * 订单总金额,单位分
     */
    public Integer amount;

    /**
     * 用户编号
     */
    public String userNo;

    /**
     * 订单状态  INITIAL("initial", "初始化"), PENDING("pending", "处理中"), SUCCESS("success", "成功"), FAIL("fail", "失败");
     */
    public String status;

    /**
     * 标识代扣任务是否最终结束，false 不是最终结束 true 是最终结束
     */
    public Boolean isFinallyOver;

    /**
     * 错误码，成功时为空或特定成功码
     */
    public String code;

    /**
     * 错误消息描述，成功时为空
     */
    public String msg;

    /**
     * 支付完成时间，代扣成功时间、代扣失败时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    public Date completeTime;

    /**
     * 支付场景编码: vip_card(会员卡)
     */
    public String sceneCode;

    /**
     * 额外信息，键值对形式存储扩展信息
     */
    public Map<String, String> extraInfo;

    /**
     * 当前的代扣是第几次，1为第一次批扣 ，2为第二次，依次类推
     */
    public Integer currentDeductNumber;

    /**
     * 首次代扣执行时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    public Date firstDeductExecutionTime;

    /**
     * app
     */
    public String app;

    /**
     * inner_app
     */
    public String innerApp;

    /**
     * 消息发送时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    public Date eventTime;

}