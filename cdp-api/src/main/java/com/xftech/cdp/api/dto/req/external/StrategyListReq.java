package com.xftech.cdp.api.dto.req.external;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @<NAME_EMAIL>
 * @date 2023-07-21 14:53
 */
@Data
public class StrategyListReq {
    @ApiModelProperty("调用源")
    private CallingSourceEnum callingSource;

    @ApiModelProperty("策略编号")
    private Long strategyId;

    @ApiModelProperty(value = "业务线：新客:new-cust; 老客:old-cust; 测试专用:test-cust")
    private String businessType;

    @ApiModelProperty("策略状态")
    private List<Integer> strategyStatusList;

    /**
     * 当前页数
     */
    private Integer pageNum = 1;
    /**
     * 当前页记录数
     */
    private Integer pageSize = 10;

    public Integer getPageNum() {
        return (pageNum - 1) * pageSize;
    }
}
