package com.xftech.cdp.feign;

import com.xftech.cdp.feign.contants.FeignConstants;
import com.xftech.cdp.feign.model.requset.VipCoreRenewStatusBatchRequest;
import com.xftech.cdp.feign.model.response.VipCoreResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 *
 * <AUTHOR>
 */
@FeignClient(name = FeignConstants.VIP_CORE_SERVICE, contextId = FeignConstants.VIP_CORE_SERVICE + ".FeignClient", path = "/")
public interface VipCoreFeignClient {

    @PostMapping("/vip/renewStatusBatch")
    VipCoreResponse<Map<Long, Boolean>> renewStatusBatch(@RequestBody VipCoreRenewStatusBatchRequest request);
}
