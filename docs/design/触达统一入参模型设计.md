# 触达统一入参模型设计

## 1. 现有触达入口分析

### 1.1 当前触达入口对比

| 触达方式 | 入口方法 | 核心入参 | 特点 |
|---------|---------|---------|------|
| T0普通触达 | `StrategyEventDispatchServiceImpl.execSend()` | `DispatchDto + CrowdDetailDo + BizEventVO` | 单用户实时触达，包含完整事件信息 |
| T0引擎触达 | `StrategyEventDispatchServiceImpl.marketingSend()` | `DispatchDto + CrowdDetailDo + groupName + detailInfo` | 单用户引擎决策触达，可选事件信息 |
| 离线引擎触达 | `StrategyEventDispatchServiceImpl.marketingSend()` | 同T0引擎触达 | 批量引擎决策触达 |
| 离线普通触达 | `AbstractStrategyDispatchService.dispatchHandler()` | `StrategyContext + List<CrowdDetailDo>` | 批量普通触达，基于人群包 |

### 1.2 参数结构差异分析

**共同参数：**
- 策略信息（策略ID、分组ID、渠道信息）
- 用户信息（用户ID、手机号、APP等）
- 渠道配置信息

**差异参数：**
- **事件信息**：T0触达包含完整BizEventVO，离线触达不包含
- **批量处理**：离线触达支持批量用户，T0触达单用户处理
- **引擎信息**：引擎触达包含分组名称和详细信息
- **上下文信息**：离线触达包含完整StrategyContext

## 2. 统一触达领域模型设计

### 2.1 核心领域模型

```java
// 触达请求统一模型
@Data
public class TouchRequest {
    // ========== 基础信息 ==========
    private String requestId;                    // 请求唯一标识
    private TouchType touchType;                 // 触达类型：标识触达方式
    private TouchMode touchMode;                 // 触达模式：SINGLE(单用户), BATCH(批量)
    private Long timestamp;                      // 请求时间戳
    private String traceId;                      // 链路追踪ID
    
    // ========== 策略信息 ==========
    private Long strategyId;                     // 策略ID
    private String strategyExecId;               // 策略执行ID
    private Long strategyGroupId;                // 策略分组ID
    private String strategyGroupName;            // 策略分组名称
    private Long strategyChannelId;              // 策略渠道ID
    private TouchChannel channel;                // 触达渠道：SMS, VOICE, PUSH, COUPON等
    private String templateId;                   // 模板ID
    
    // ========== 用户信息 ==========
    private TouchUserInfo userInfo;              // 单用户信息（单用户模式）
    private List<TouchUserInfo> userList;        // 用户列表（批量模式）
    
    // ========== 业务信息 ==========
    private String bizEventType;                 // 业务事件类型（T0触达必填）
    private Map<String, Object> bizEventData;    // 业务事件数据（T0触达）
    private Map<String, Object> templateParams;  // 模板参数
    private Map<String, Object> extData;         // 扩展数据
    
    // ========== 引擎信息 ==========
    private String engineCode;                   // 引擎编码（引擎触达）
    private String engineGroupName;              // 引擎分组名称（引擎触达）
    private Map<String, Object> engineDetail;    // 引擎详细信息（引擎触达）
    
    // ========== 触达配置 ==========
    private TouchConfig touchConfig;             // 触达配置
    private FlowControlConfig flowControlConfig; // 流控配置
    
    // ========== 批量处理信息 ==========
    private BatchInfo batchInfo;                 // 批量处理信息（批量模式）
}

// 触达类型枚举
public enum TouchType {
    T0_NORMAL("T0_NORMAL", "T0普通触达"),
    T0_ENGINE("T0_ENGINE", "T0引擎触达"), 
    OFFLINE_NORMAL("OFFLINE_NORMAL", "离线普通触达"),
    OFFLINE_ENGINE("OFFLINE_ENGINE", "离线引擎触达");
    
    private final String code;
    private final String desc;
}

// 触达模式枚举
public enum TouchMode {
    SINGLE("SINGLE", "单用户触达"),
    BATCH("BATCH", "批量触达");
    
    private final String code;
    private final String desc;
}

// 触达渠道枚举
public enum TouchChannel {
    SMS("SMS", "短信"),
    VOICE("VOICE", "语音外呼"),
    PUSH("PUSH", "推送"),
    COUPON("COUPON", "优惠券"),
    AI_CALL("AI_CALL", "AI外呼"),
    NONE("NONE", "无渠道");
    
    private final String code;
    private final String desc;
}

// 用户信息模型
@Data
public class TouchUserInfo {
    private Long userId;                         // 用户ID
    private String mobile;                       // 手机号
    private String app;                          // APP标识
    private String innerApp;                     // 内部APP标识
    private String deviceId;                     // 设备ID
    private String abNum;                        // AB测试编号
    private Integer appUserIdLast2;              // 用户ID后两位
    private Long crowdId;                        // 人群包ID（离线触达）
    private Map<String, Object> userLabels;      // 用户标签
    private Map<String, Object> userExt;         // 用户扩展信息
}

// 触达配置模型
@Data
public class TouchConfig {
    private String dispatchType;                 // 触达类型：MKT(营销), NOTIFY(通知)
    private Integer timeout;                     // 超时时间
    private Integer retryTimes;                  // 重试次数
    private Boolean enableFlowControl;           // 是否启用流控
    private Boolean enableReceipt;               // 是否启用回执
    private Map<String, Object> channelConfig;   // 渠道特定配置
}

// 流控配置模型
@Data
public class FlowControlConfig {
    private Boolean enableEventFlowControl;      // 是否启用事件级流控
    private Boolean enableTouchFlowControl;      // 是否启用触达级流控
    private Boolean enableDistributedFlowControl; // 是否启用分布式流控
    private Boolean enableBatchFlowControl;      // 是否启用批量流控
    private List<FlowControlRule> rules;         // 流控规则列表
}

// 批量处理信息
@Data
public class BatchInfo {
    private String batchNo;                      // 批次号
    private Integer batchSize;                   // 批次大小
    private Integer totalCount;                  // 总用户数
    private Integer currentBatch;                // 当前批次
    private Integer totalBatch;                  // 总批次数
    private String detailTableNo;                // 明细表序号
}
```

### 2.2 触达响应模型

```java
// 触达响应统一模型
@Data
public class TouchResponse {
    private String requestId;                    // 请求唯一标识
    private TouchStatus status;                  // 触达状态
    private String batchNo;                      // 批次号
    private String errorCode;                    // 错误码
    private String errorMessage;                 // 错误信息
    private Long timestamp;                      // 响应时间戳
    
    // 单用户响应结果
    private TouchUserResult userResult;          // 单用户结果（单用户模式）
    
    // 批量响应结果
    private List<TouchUserResult> userResults;   // 用户结果列表（批量模式）
    private BatchResult batchResult;             // 批量处理结果
    
    // 统计信息
    private TouchStatistics statistics;          // 触达统计信息
}

// 触达状态枚举
public enum TouchStatus {
    SUCCESS("SUCCESS", "成功"),
    FAILED("FAILED", "失败"),
    FLOW_CONTROLLED("FLOW_CONTROLLED", "流控拦截"),
    PENDING("PENDING", "处理中"),
    PARTIAL_SUCCESS("PARTIAL_SUCCESS", "部分成功");
    
    private final String code;
    private final String desc;
}

// 用户触达结果
@Data
public class TouchUserResult {
    private Long userId;                         // 用户ID
    private TouchStatus status;                  // 触达状态
    private String batchNo;                      // 批次号
    private String errorCode;                    // 错误码
    private String errorMessage;                 // 错误信息
    private Map<String, Object> resultData;      // 结果数据
}

// 批量处理结果
@Data
public class BatchResult {
    private Integer totalCount;                  // 总处理数
    private Integer successCount;                // 成功数
    private Integer failedCount;                 // 失败数
    private Integer flowControlledCount;         // 流控拦截数
    private List<String> failedReasons;          // 失败原因列表
}

// 触达统计信息
@Data
public class TouchStatistics {
    private Long startTime;                      // 开始时间
    private Long endTime;                        // 结束时间
    private Long duration;                       // 处理耗时
    private Integer totalUsers;                  // 总用户数
    private Integer processedUsers;              // 已处理用户数
    private Map<TouchStatus, Integer> statusCount; // 各状态统计
}
```

## 3. 现有参数映射关系

### 3.1 T0普通触达参数映射

```java
// execSend 参数映射到 TouchRequest
public TouchRequest convertFromExecSend(DispatchDto dispatchDto, CrowdDetailDo crowdDetail, 
                                       StrategyMarketChannelEnum channelEnum, 
                                       StrategyMarketChannelDo channelDo, BizEventVO bizEvent) {
    TouchRequest request = new TouchRequest();
    
    // 基础信息
    request.setRequestId(UUID.randomUUID().toString());
    request.setTouchType(TouchType.T0_NORMAL);
    request.setTouchMode(TouchMode.SINGLE);
    request.setTimestamp(System.currentTimeMillis());
    request.setTraceId(bizEvent.getTraceId());
    
    // 策略信息
    request.setStrategyId(dispatchDto.getStrategyId());
    request.setStrategyExecId(dispatchDto.getStrategyExecId());
    request.setStrategyGroupId(dispatchDto.getStrategyGroupId());
    request.setStrategyGroupName(dispatchDto.getStrategyGroupName());
    request.setStrategyChannelId(dispatchDto.getStrategyChannelId());
    request.setChannel(TouchChannel.valueOf(channelEnum.name()));
    request.setTemplateId(dispatchDto.getStrategyMarketChannelTemplateId());
    
    // 用户信息
    TouchUserInfo userInfo = new TouchUserInfo();
    userInfo.setUserId(crowdDetail.getUserId());
    userInfo.setMobile(crowdDetail.getMobile());
    userInfo.setApp(crowdDetail.getApp());
    userInfo.setInnerApp(crowdDetail.getInnerApp());
    userInfo.setDeviceId(crowdDetail.getDeviceId());
    userInfo.setAbNum(crowdDetail.getAbNum());
    userInfo.setAppUserIdLast2(crowdDetail.getAppUserIdLast2());
    request.setUserInfo(userInfo);
    
    // 业务信息
    request.setBizEventType(bizEvent.getBizEventType());
    request.setBizEventData(convertBizEventToMap(bizEvent));
    request.setTemplateParams(extractTemplateParams(bizEvent));
    
    // 触达配置
    TouchConfig touchConfig = new TouchConfig();
    touchConfig.setDispatchType(dispatchDto.getDispatchType());
    touchConfig.setChannelConfig(convertChannelConfig(channelDo));
    request.setTouchConfig(touchConfig);
    
    return request;
}
```

### 3.2 引擎触达参数映射

```java
// marketingSend 参数映射到 TouchRequest
public TouchRequest convertFromMarketingSend(DispatchDto dispatchDto, CrowdDetailDo crowdDetailDo,
                                           StrategyMarketChannelEnum channelEnum, String groupName,
                                           Map detailInfo, BizEventVO bizEventVO) {
    TouchRequest request = new TouchRequest();
    
    // 基础信息
    request.setRequestId(UUID.randomUUID().toString());
    request.setTouchType(bizEventVO != null ? TouchType.T0_ENGINE : TouchType.OFFLINE_ENGINE);
    request.setTouchMode(TouchMode.SINGLE);
    request.setTimestamp(System.currentTimeMillis());
    
    // 策略信息 - 同T0普通触达
    // ...
    
    // 引擎信息
    request.setEngineGroupName(groupName);
    request.setEngineDetail(detailInfo);
    if (bizEventVO != null) {
        request.setEngineCode(bizEventVO.getEngineCode());
    }
    
    // 业务信息（可选）
    if (bizEventVO != null) {
        request.setBizEventType(bizEventVO.getBizEventType());
        request.setBizEventData(convertBizEventToMap(bizEventVO));
    }
    
    return request;
}
```

### 3.3 离线普通触达参数映射

```java
// dispatchHandler 参数映射到 TouchRequest
public TouchRequest convertFromDispatchHandler(StrategyContext context, String app,
                                             List<CrowdDetailDo> userList, List<Object> params) {
    TouchRequest request = new TouchRequest();
    
    // 基础信息
    request.setRequestId(UUID.randomUUID().toString());
    request.setTouchType(TouchType.OFFLINE_NORMAL);
    request.setTouchMode(TouchMode.BATCH);
    request.setTimestamp(System.currentTimeMillis());
    
    // 策略信息
    request.setStrategyId(context.getStrategyDo().getId());
    request.setStrategyGroupId(context.getStrategyGroupDo().getId());
    request.setStrategyGroupName(context.getStrategyGroupDo().getName());
    request.setStrategyChannelId(context.getStrategyMarketChannelDo().getId());
    request.setChannel(TouchChannel.valueOf(
        StrategyMarketChannelEnum.getInstance(context.getStrategyMarketChannelDo().getMarketChannel()).name()));
    
    // 用户信息列表
    List<TouchUserInfo> touchUserList = userList.stream().map(crowdDetail -> {
        TouchUserInfo userInfo = new TouchUserInfo();
        userInfo.setUserId(crowdDetail.getUserId());
        userInfo.setMobile(crowdDetail.getMobile());
        userInfo.setApp(crowdDetail.getApp());
        userInfo.setInnerApp(crowdDetail.getInnerApp());
        userInfo.setCrowdId(crowdDetail.getCrowdId());
        // ... 其他字段映射
        return userInfo;
    }).collect(Collectors.toList());
    request.setUserList(touchUserList);
    
    // 批量处理信息
    BatchInfo batchInfo = new BatchInfo();
    batchInfo.setTotalCount(userList.size());
    batchInfo.setDetailTableNo(context.getDetailTableNo());
    request.setBatchInfo(batchInfo);
    
    // 模板参数
    request.setTemplateParams(convertTemplateParams(params));
    
    // 流控配置
    FlowControlConfig flowControlConfig = new FlowControlConfig();
    flowControlConfig.setRules(convertFlowControlRules(context.getFlowCtrlList()));
    request.setFlowControlConfig(flowControlConfig);
    
    return request;
}
```

## 4. 设计优势

### 4.1 统一性
- **统一入参模型**：所有触达方式使用相同的TouchRequest模型
- **统一响应模型**：所有触达方式使用相同的TouchResponse模型
- **统一枚举定义**：TouchType明确标识不同触达方式

### 4.2 扩展性
- **模式区分**：TouchMode支持单用户和批量两种模式
- **扩展字段**：extData支持业务扩展
- **配置化**：TouchConfig支持渠道特定配置

### 4.3 兼容性
- **向下兼容**：通过转换器保持与现有代码的兼容
- **渐进迁移**：可以逐步迁移现有触达方式
- **类型安全**：强类型定义避免参数错误

### 4.4 可维护性
- **清晰结构**：参数分类明确，职责清晰
- **标准化**：统一的命名和结构规范
- **可追踪**：完整的请求ID和链路追踪支持

## 5. 实施建议

### 5.1 分阶段实施
1. **第一阶段**：定义统一模型，实现转换器
2. **第二阶段**：新增统一触达服务入口
3. **第三阶段**：逐步迁移现有触达方式
4. **第四阶段**：废弃旧的触达入口

### 5.2 兼容性保证
- 保留现有触达方法，内部调用统一服务
- 提供完整的参数转换器
- 确保业务逻辑不变

### 5.3 测试策略
- 单元测试覆盖所有转换器
- 集成测试验证各触达方式
- 性能测试确保无性能退化
