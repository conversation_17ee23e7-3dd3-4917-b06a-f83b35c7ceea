package com.xftech.cdp;

import cn.hutool.core.thread.ThreadUtil;
import com.google.common.base.Stopwatch;
import com.xftech.cdp.domain.strategy.model.dto.LoanFinalFailedReasonDto;
import com.xftech.cdp.infra.utils.AviatorUtil;
import com.xftech.cdp.infra.utils.JsonUtil;
import org.junit.Test;

import java.util.*;
import java.util.concurrent.TimeUnit;

public class TestUnit {

    @Test
    public void test(){
        List<String> ss = Arrays.asList("1","2",null);

        System.out.println(ss.contains(""));
        System.out.println(ss.contains(null));

        System.out.println(JsonUtil.toJson(ss));

        LoanFinalFailedReasonDto loanFinalFailedReasonDto = new LoanFinalFailedReasonDto();
        loanFinalFailedReasonDto.setDefaultReason("其他原因");
        loanFinalFailedReasonDto.setReasonList(Arrays.asList("风险失败","资金失败","其他app有订单","其他原因"));

        System.out.println(JsonUtil.toJson(loanFinalFailedReasonDto));
        loanFinalFailedReasonDto = new LoanFinalFailedReasonDto();
        loanFinalFailedReasonDto.setDefaultReason("999_其他");
        loanFinalFailedReasonDto.setFundFailedReasonList(Arrays.asList("资金失败"));
        loanFinalFailedReasonDto.setReasonList(Arrays.asList("200_风控拒绝",
                "210_风控规则拒绝",
                "220_风控模型拒绝",
                "230_黑名单",
                "240_拒绝后禁申期未满",
                "250_发卡行黑名单",
                "310_人脸比对失败",
                "320_身份证对比失败",
                "330_OCR失败",
                "340_影像件上传异常",
                "350_文件上传失败",
                "410_II/III类卡",
                "420_卡挂失",
                "420_账户冻结",
                "430_销户",
                "440_卡状态异常",
                "450_卡性质限制",
                "460_中止服务",
                "470_户名不符",
                "480_暂停非柜面交易",
                "490_其他卡异常",
                "500_保证金",
                "510_资方额度控制",
                "705_二要素验证失败",
                "710_四要素鉴权失败",
                "720_绑卡失败",
                "910_支付失败",
                "920_客户可用额度",
                "930_地址问题",
                "940_身份证有效期",
                "950_电子签失败",
                "960_参数异常",
                "970_非进件区域",
                "999_其他"));
        System.out.println(JsonUtil.toJson(loanFinalFailedReasonDto));
    }

    @Test
    public void test2(){
        Stopwatch stopwatch = Stopwatch.createUnstarted();
        for (int i = 0; i< 100; i++){
            stopwatch.start();
            ThreadUtil.sleep(100);
            stopwatch.stop();
            System.out.println(stopwatch.elapsed(TimeUnit.MILLISECONDS));

        }
    }
}
