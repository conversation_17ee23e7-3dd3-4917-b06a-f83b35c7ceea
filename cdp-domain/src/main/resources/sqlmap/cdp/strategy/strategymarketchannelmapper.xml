<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="strategyMarketChannel">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketChannelDo">
        <id column="id" property="id"/>
        <result column="strategy_id" property="strategyId"/>
        <result column="strategy_group_id" property="strategyGroupId"/>
        <result column="market_channel" property="marketChannel"/>
        <result column="template_id" property="templateId"/>
        <result column="app" property="app"/>
        <result column="dispatch_app" property="dispatchApp"/>
        <result column="send_time" property="sendTime"/>
        <result column="cron" property="cron"/>
        <result column="xxl_job_id" property="xxlJobId"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="d_flag" property="dFlag"/>
        <result column="created_op" property="createdOp"/>
        <result column="updated_op" property="updatedOp"/>
        <result column="ext_info" property="extInfo"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, strategy_id, strategy_group_id, market_channel, template_id,app,dispatch_app, send_time, cron, xxl_job_id, created_time, updated_time, d_flag, created_op, updated_op, ext_info
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_market_channel
        where id = #{id,jdbcType=BIGINT} and d_flag = 0
    </select>

    <select id="selectByStrategyId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_market_channel
        where strategy_id = #{strategyId}
        and d_flag = 0
    </select>

    <select id="selectDelByStrategyId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_market_channel
        where strategy_id = #{strategyId}
        and d_flag = 1
    </select>

    <select id="selectByStrategyGroupId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_market_channel
        where strategy_group_id = #{strategyGroupId}
        and d_flag = 0
    </select>

    <select id="selectAllByStrategyGroupId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_market_channel
        where strategy_group_id = #{strategyGroupId}
    </select>

    <select id="selectByStrategyIdAndChannel" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_market_channel
        where strategy_id = #{strategyId}
        <if test="marketChannelList != null and marketChannelList.size() != 0">
            and market_channel in
            <foreach collection="marketChannelList" item="marketChannel" open="(" separator="," close=")">
                #{marketChannel}
            </foreach>
        </if>
        and d_flag = 0
    </select>

    <select id="selectByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_market_channel
        where d_flag = 0
        and id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectByStrategyIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_market_channel
        where d_flag = 0
        and strategy_id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectByStrategyGroupIdAndChannel" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_market_channel
        where d_flag = 0
        and strategy_group_id = #{strategyGroupId} and market_channel = #{marketChannel}
    </select>

    <select id="selectStrategyIdsWithAppBannerChannel" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_market_channel
        where d_flag = 0
        and market_channel = #{marketChannel}
    </select>

    <select id="selectStrategyIdsByTemplateIdAndChannel" resultMap="BaseResultMap">
        select strategy_id
        from strategy_market_channel
        where d_flag = 0
          and market_channel = #{marketChannel}
          and template_id = #{templateId}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        update
            strategy_market_channel
        set d_flag =1
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <delete id="deleteByStrategyId">
        update
            strategy_market_channel
        set d_flag = 1
        where strategy_id = #{strategyId}
    </delete>

    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketChannelDo"
            useGeneratedKeys="true">
        insert into strategy_market_channel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="strategyId != null">strategy_id,</if>
            <if test="strategyGroupId != null">strategy_group_id,</if>
            <if test="marketChannel != null">market_channel,</if>
            <if test="templateId != null">template_id,</if>
            <if test="app != null">app,</if>
            <if test="dispatchApp != null">dispatch_app,</if>
            <if test="sendTime != null">send_time,</if>
            <if test="cron != null">cron,</if>
            <if test="xxlJobId != null">xxl_job_id,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updatedTime != null">updated_time,</if>
            <if test="dFlag != null">d_flag,</if>
            <if test="createdOp != null">created_op,</if>
            <if test="updatedOp != null">updated_op,</if>
            <if test="extInfo != null">ext_info,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="strategyId != null">#{strategyId,jdbcType=BIGINT},</if>
            <if test="strategyGroupId != null">#{strategyGroupId,jdbcType=BIGINT},</if>
            <if test="marketChannel != null">#{marketChannel,jdbcType=SMALLINT},</if>
            <if test="templateId != null">#{templateId,jdbcType=VARCHAR},</if>
            <if test="app != null">#{app,jdbcType=VARCHAR},</if>
            <if test="dispatchApp != null">#{dispatchApp,jdbcType=VARCHAR},</if>
            <if test="sendTime != null">#{sendTime,jdbcType=TIME},</if>
            <if test="cron != null">#{cron,jdbcType=VARCHAR},</if>
            <if test="xxlJobId != null">#{xxlJobId,jdbcType=INTEGER},</if>
            <if test="createdTime != null">#{createdTime,jdbcType=TIMESTAMP},</if>
            <if test="updatedTime != null">#{updatedTime,jdbcType=TIMESTAMP},</if>
            <if test="dFlag != null">#{dFlag,jdbcType=SMALLINT},</if>
            <if test="createdOp != null">#{createdOp,jdbcType=VARCHAR},</if>
            <if test="updatedOp != null">#{updatedOp,jdbcType=VARCHAR},</if>
            <if test="extInfo != null">#{extInfo,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketChannelDo">
        update strategy_market_channel
        <set>
            <if test="strategyId != null">
                strategy_id = #{strategyId,jdbcType=BIGINT},
            </if>
            <if test="strategyGroupId != null">
                strategy_group_id = #{strategyGroupId,jdbcType=BIGINT},
            </if>
            <if test="marketChannel != null">
                market_channel = #{marketChannel,jdbcType=SMALLINT},
            </if>
            <if test="templateId != null">
                template_id = #{templateId,jdbcType=VARCHAR},
            </if>
            <if test="app != null">
                app = #{app,jdbcType=VARCHAR},
            </if>
            <if test="dispatchApp != null">
                dispatch_app = #{dispatchApp,jdbcType=VARCHAR},
            </if>
            <if test="sendTime != null">
                send_time = #{sendTime,jdbcType=TIME},
            </if>
            <if test="cron != null">
                cron = #{cron,jdbcType=VARCHAR},
            </if>
            <if test="xxlJobId != null">
                xxl_job_id = #{xxlJobId,jdbcType=INTEGER},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedTime != null">
                updated_time = #{updatedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="dFlag != null">
                d_flag = #{dFlag,jdbcType=SMALLINT},
            </if>
            <if test="createdOp != null">
                created_op = #{createdOp,jdbcType=VARCHAR},
            </if>
            <if test="updatedOp != null">
                updated_op = #{updatedOp,jdbcType=VARCHAR},
            </if>
            <if test="extInfo != null">
                ext_info = #{extInfo,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

</mapper>
