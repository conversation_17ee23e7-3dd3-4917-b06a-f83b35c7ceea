package com.xftech.cdp.domain.crowd.factory;

import com.xftech.cdp.domain.crowd.factory.impl.AutoMessOptService;
import com.xftech.cdp.domain.crowd.factory.impl.UserLableOptService;
import com.xftech.cdp.domain.crowd.model.enums.CrowdPullTypeEnum;
import com.xftech.cdp.infra.config.CrowdConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CrowdOptFactory {
    @Autowired
    protected CrowdConfig crowdConfig;
    @Autowired
    protected AutoMessOptService autoMessOptService;
    @Autowired
    protected UserLableOptService userLableOptService;

//    private CrowdOptFactory() {
//    }

    public AbsCrowdOptService createOpt(Integer pullType) {
        if (CrowdPullTypeEnum.AUTO_MESSAGE.getCode() == pullType) {
            return autoMessOptService;
        } else if (CrowdPullTypeEnum.USER_LABLE.getCode() == pullType) {
            return userLableOptService;
        }
        return null;
    }
}
