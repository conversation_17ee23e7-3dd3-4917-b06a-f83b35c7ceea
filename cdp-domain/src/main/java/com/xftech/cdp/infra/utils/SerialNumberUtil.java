package com.xftech.cdp.infra.utils;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import com.xftech.cdp.infra.redis.sequence.Sequence;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * @<NAME_EMAIL>
 * @date 2023/4/10 16:10
 */
@Component
@DependsOn("sequence")
public class SerialNumberUtil {

    /**
     * 获取批次号
     * <p>
     * 默认取雪花算法，兜底时间
     *
     * @return 批次号
     */
    public String batchNum() {
        String batchNum = String.valueOf(Sequence.nextId());
        if (ObjectUtil.isNull(batchNum)) {
            batchNum = LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMMddHHmmssSSS");
        }
        return batchNum;
    }

    public static String nextId() {
        String batchNum = String.valueOf(Sequence.nextId());
        if (ObjectUtil.isNull(batchNum)) {
            batchNum = LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMMddHHmmssSSS");
        }
        return batchNum;
    }
}