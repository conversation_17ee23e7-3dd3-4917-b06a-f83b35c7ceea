package com.xftech.cdp.api.dto.req;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xftech.cdp.api.dto.base.TimeFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Data
@EqualsAndHashCode(callSuper = true)
public class StrategyCreateReq extends BaseReq {

    @Size(max = 20, message = "策略名称不能超过20个字符")
    @NotEmpty(message = "策略名称不能为空")
    @ApiModelProperty(value = "策略名称", required = true)
    private String name;

    @Size(max = 100, message = "策略描述不能超过100个字符")
    @ApiModelProperty(value = "策略描述")
    private String detailDescription;

    @ApiModelProperty(value = "人群包id,多个人群\";\"分隔")
    private String crowdPackIds;

    @ApiModelProperty(value = "策略分组")
    private Integer strategyGroupType;

    @Min(0)
    @Max(1)
    @ApiModelProperty(value = "用户是否转换 0-否 1-是")
    private Integer userConvert;

    @Min(0)
    @Max(1)
    @ApiModelProperty(value = "abTest 0-否 1-是", required = true)
    private Integer abTest;

    @Min(0)
    @ApiModelProperty(value = "分组方式，0：uid倒数两位 1：随机数")
    private Integer abType;

    @ApiModelProperty(value = "随机数场景key值")
    private String bizKey;

    @Min(0)
    @Max(3)
    @ApiModelProperty(value = "0-单次,1-例行, 2-事件, 3-周期", required = true)
    private Integer sendRuler;

    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    @ApiModelProperty(value = "策略开始时间")
    @NotNull(message = "策略开始时间不能为空")
    private LocalDateTime validityBegin;

    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    @ApiModelProperty(value = "策略结束时间")
    private LocalDateTime validityEnd;

    @ApiModelProperty(value = "发送频次")
    private SendFrequency sendFrequency;

    @ApiModelProperty(value = "人群分组")
    private List<StrategyGroup> strategyGroups;

    @ApiModelProperty(value = "营销条件配置")
    List<InstantStrategyCreateReq.MarketCondition> marketCondition;

    @ApiModelProperty(value = "仍要执行 0校验 1不校验")
    private Integer forceExec;

    @ApiModelProperty(value = "业务线 new-cust-新客  old-cust-老客")
    @NotBlank(message = "业务线不能为空")
    private String businessType;

    @ApiModelProperty(value = "业务线名称")
    private String businessTypeName;

    @ApiModelProperty(value = "发布类型 -1 草稿  0 发布")
    private Integer publishType;

    @ApiModelProperty(value = "限制天数")
    private Integer limitDays;

    @ApiModelProperty(value = "限制次数")
    private Integer limitTimes;
    @ApiModelProperty(value = "排除项类型")
    private List<Integer> excludeType;

    @ApiModelProperty(value = "策略类型 0-默认其它，1-引擎规则")
    private Integer type = 0;

    @ApiModelProperty(value = "引擎code")
    private String engineCode;

    private InstantStrategyCreateReq.DispatchConfig dispatchConfig;

    @JsonIgnore
    @JSONField(serialize = false)
    public boolean isEngineCodeValid() {
        if (Objects.equals(1, type)) {
            return !StringUtils.isEmpty(engineCode);
        }
        return true;
    }

    @Data
    public static class StrategyGroup {
        @ApiModelProperty(value = "组id")
        private String groupId;

        @NotEmpty
        @ApiModelProperty(value = "组名")
        private String name;

        @ApiModelProperty(value = "分组配置")
        private GroupConfig groupConfig;

        @ApiModelProperty(value = "触达渠道")
        private List<StrategyMarketChannel> strategyMarketChannels;

        @ApiModelProperty(value = "是否执行：0-不执行 1-执行")
        private Integer isExecutable;

        @ApiModelProperty(value = "扩展信息：json序列化数据")
        private String extInfo;
    }

    @Data
    public static class StrategyMarketChannel {

        @ApiModelProperty(value = "渠道id")
        private String channelId;

        @ApiModelProperty(value = "营销渠道，0: 不营销，1:短信，2:电销，3:优惠券，4:新电销，5:push，6:AI-即时触达  100:app资源位， 200:提额, 201:X天还款免息, 202:生活权益 ")
        private Integer marketChannel;

        @ApiModelProperty(value = "模板id")
        private String templateId;

        private String template;

        @ApiModelProperty(value = "app")
        private String app;

        @ApiModelProperty(value = "dispatchApp")
        private String dispatchApp;

        @JsonFormat(pattern = TimeFormat.TIME_SEC)
        @ApiModelProperty(value = "发送时间")
        private LocalTime sendTime;
        
        @ApiModelProperty(value = "extInfo")
        private String extInfo;
    }

    @Data
    public static class GroupConfig {

        private Integer digits;

        private Integer positionStart;

        private Integer positionEnd;

        private Object crowdLabelOption;

        // 是否选中，灰度选中
        @ApiModelProperty(value = "频率类型，0：非选中进入流量组，1：选中进入流量组")
        private Integer selected;
    }

    @Data
    public static class SendFrequency {
        @ApiModelProperty(value = " 频率类型, 0:每天, 1:每周, 2:每月 3-每日循环周期")
        private Integer type;

        @ApiModelProperty(value = "每周/每月第几天/周期数")
        private List<Integer> value;

        @JsonFormat(pattern = TimeFormat.TIME_SEC)
        private LocalTime sendTime;
    }

    String getMarketChannelStr() {
        List<String> channelIds = new ArrayList<>();
        for (StrategyGroup strategyGroup : strategyGroups) {
            if (!CollectionUtils.isEmpty(strategyGroup.getStrategyMarketChannels())) {
                strategyGroup.getStrategyMarketChannels().forEach(item -> channelIds.add(String.valueOf(item.getMarketChannel())));
            }
        }
        return channelIds.stream().distinct().collect(Collectors.joining(","));
    }
}
