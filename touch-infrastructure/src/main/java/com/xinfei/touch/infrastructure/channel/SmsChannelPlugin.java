package com.xinfei.touch.infrastructure.channel;

import com.xinfei.touch.application.dto.TouchResponse;
import com.xinfei.touch.application.command.TouchCommand;
import com.xinfei.touch.application.service.ChannelConfig;
import com.xinfei.touch.application.service.ChannelPlugin;
import com.xinfei.touch.domain.model.TouchChannel;
import com.xinfei.touch.infrastructure.config.TouchConfigService;
import com.xinfei.touch.infrastructure.external.SmsService;
import com.xinfei.touch.infrastructure.external.dto.SmsRequest;
import com.xinfei.touch.infrastructure.external.dto.SmsResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 短信渠道插件
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SmsChannelPlugin implements ChannelPlugin {
    
    private final SmsService smsService;
    private final TouchConfigService touchConfigService;
    
    @Override
    public TouchChannel getChannel() {
        return TouchChannel.SMS;
    }
    
    @Override
    public TouchResponse send(TouchCommand command) {
        String requestId = command.getRequestId();
        
        try {
            log.info("发送短信触达: requestId={}, userId={}", requestId, command.getUserId());
            
            // 1. 参数转换
            SmsRequest smsRequest = convertToSmsRequest(command);
            
            // 2. 调用短信服务
            SmsResponse smsResponse = smsService.send(smsRequest);
            
            // 3. 结果转换
            TouchResponse response = convertToTouchResponse(smsResponse, requestId);
            
            log.info("短信触达完成: requestId={}, status={}, batchNo={}", 
                    requestId, response.getStatus(), response.getBatchNo());
            
            return response;
            
        } catch (Exception e) {
            log.error("短信触达失败: requestId={}", requestId, e);
            return TouchResponse.failed(requestId, "SMS_SEND_FAILED", "短信发送失败: " + e.getMessage());
        }
    }
    
    @Override
    public boolean isAvailable() {
        try {
            return smsService.healthCheck();
        } catch (Exception e) {
            log.warn("短信服务健康检查失败", e);
            return false;
        }
    }
    
    @Override
    public ChannelConfig getConfig() {
        return touchConfigService.getChannelConfig(TouchChannel.SMS);
    }
    
    /**
     * 转换为短信请求
     */
    private SmsRequest convertToSmsRequest(TouchCommand command) {
        SmsRequest request = new SmsRequest();
        request.setRequestId(command.getRequestId());
        request.setUserId(command.getUserId());
        request.setStrategyId(command.getStrategyId());
        
        // 从模板参数中提取短信相关参数
        if (command.getTemplateParams() != null) {
            request.setMobile((String) command.getTemplateParams().get("mobile"));
            request.setContent((String) command.getTemplateParams().get("content"));
            request.setTemplateId((String) command.getTemplateParams().get("templateId"));
            request.setTemplateParams(command.getTemplateParams());
        }
        
        // 设置扩展参数
        if (command.getExtParams() != null) {
            request.setExtParams(command.getExtParams());
        }
        
        return request;
    }
    
    /**
     * 转换为触达响应
     */
    private TouchResponse convertToTouchResponse(SmsResponse smsResponse, String requestId) {
        if (smsResponse.isSuccess()) {
            return TouchResponse.success(requestId, smsResponse.getBatchNo());
        } else {
            return TouchResponse.failed(requestId, smsResponse.getErrorCode(), smsResponse.getErrorMessage());
        }
    }
}
