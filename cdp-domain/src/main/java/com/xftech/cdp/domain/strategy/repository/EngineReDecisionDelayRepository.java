package com.xftech.cdp.domain.strategy.repository;

import java.util.HashMap;
import java.util.Map;

import com.xftech.base.common.util.DBUtil;
import com.xftech.base.database.Page;
import com.xftech.cdp.domain.stat.entity.StatStrategyGroupDataEntity;
import com.xftech.cdp.infra.repository.cdp.strategy.po.EngineReDecisionDelayDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.EngineReDecisionDelayReportDo;

import org.springframework.stereotype.Repository;

/**
 * T0引擎策略-推入引擎频次限制表
 *
 * <AUTHOR>
 * @since 2023/2/9 01:38
 */

@Repository
public class EngineReDecisionDelayRepository {

    public boolean insert(EngineReDecisionDelayDo engineReDecisionDelayDo) {
        return DBUtil.insert("engineredecisiondelay.insertSelective", engineReDecisionDelayDo) > 0;
    }

    public boolean updateByPrimaryKeySelective(EngineReDecisionDelayDo engineReDecisionDelayDo) {
        return DBUtil.update("engineredecisiondelay.updateByPrimaryKeySelective", engineReDecisionDelayDo) > 0;
    }

    public int deleteById(Long id) {
        return DBUtil.update("engineredecisiondelay.deleteByPrimaryKey", id);
    }

    public EngineReDecisionDelayDo selectLastRecordByStrategyMsgId(Long strategyId, String messageId) {
        Map<String, Object> param = new HashMap<>();
        param.put("strategyId", strategyId);
        param.put("messageId", messageId);
        return DBUtil.selectOne("engineredecisiondelay.selectLastRecordByStrategyMsgId", param);
    }

    public EngineReDecisionDelayDo selectByPrimaryKey(Long id) {
        return DBUtil.selectOne("engineredecisiondelay.selectByPrimaryKey", id);
    }

    public Page<EngineReDecisionDelayReportDo> selectPageByStrategyIdAndDate(Long strategyId, Integer queryDate, Integer pageNum, Integer pageSize) {
        Map<String, Object> param = new HashMap<>();
        param.put("strategyId", strategyId);
        param.put("dateValue", queryDate);
        return DBUtil.selectPage("engineredecisiondelay.selectReportByStrategyIdAndDate", param, pageNum, pageSize);
    }

}
