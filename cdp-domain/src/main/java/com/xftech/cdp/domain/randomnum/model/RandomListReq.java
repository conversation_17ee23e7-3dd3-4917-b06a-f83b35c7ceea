package com.xftech.cdp.domain.randomnum.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @<NAME_EMAIL>
 */

@Data
public class RandomListReq {
    @ApiModelProperty(value = "归属业务线")
    private List<String> bizNames;
    @ApiModelProperty(value = "场景key")
    private String bizKey;
    @ApiModelProperty(value = "场景名称")
    private String testName;
    @ApiModelProperty(value = "状态")
    private Integer status;
    // bizKey 和 bizKeyAccurate 2选一
    @ApiModelProperty(value = "精确匹配场景key")
    private String bizKeyAccurate;
}
