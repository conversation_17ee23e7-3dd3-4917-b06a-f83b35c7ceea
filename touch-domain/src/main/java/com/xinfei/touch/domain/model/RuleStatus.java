package com.xinfei.touch.domain.model;

import lombok.Getter;

/**
 * 规则状态枚举
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
public enum RuleStatus {
    
    /**
     * 禁用
     */
    DISABLED(0, "禁用"),
    
    /**
     * 启用
     */
    ENABLED(1, "启用");
    
    /**
     * 状态编码
     */
    private final Integer code;
    
    /**
     * 状态名称
     */
    private final String name;
    
    RuleStatus(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
    
    /**
     * 根据编码获取状态
     */
    public static RuleStatus getByCode(Integer code) {
        for (RuleStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的规则状态编码: " + code);
    }
    
    /**
     * 是否启用
     */
    public boolean isEnabled() {
        return this == ENABLED;
    }
    
    /**
     * 是否禁用
     */
    public boolean isDisabled() {
        return this == DISABLED;
    }
}
