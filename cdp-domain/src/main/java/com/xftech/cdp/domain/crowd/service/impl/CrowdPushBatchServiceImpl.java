package com.xftech.cdp.domain.crowd.service.impl;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.xftech.cdp.domain.cache.CacheCrowdExecLogService;
import com.xftech.cdp.domain.cache.CacheCrowdPackService;
import com.xftech.cdp.domain.crowd.exception.CrowdException;
import com.xftech.cdp.domain.crowd.model.enums.CrowdPullTypeEnum;
import com.xftech.cdp.domain.crowd.model.enums.CrowdPushExecTypeEnum;
import com.xftech.cdp.domain.crowd.model.enums.CrowdRefreshTypeEnum;
import com.xftech.cdp.domain.crowd.model.enums.CrowdStatusEnum;
import com.xftech.cdp.domain.crowd.repository.CrowdPackRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdPushBatchRepository;
import com.xftech.cdp.domain.crowd.service.CrowdPushBatchService;
import com.xftech.cdp.domain.crowd.service.dispatch.impl.AbstractCrowdPushBatchService;
import com.xftech.cdp.domain.dispatch.dto.DispatchDto;
import com.xftech.cdp.domain.flowctrl.model.enums.ChannelTypeEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyMarketChannelEnum;
import com.xftech.cdp.domain.strategy.service.dispatch.offline.StrategyDispatchForSmsService;
import com.xftech.cdp.domain.strategy.service.impl.DataInsightServiceImpl;
import com.xftech.cdp.infra.client.ads.AdsClient;
import com.xftech.cdp.infra.client.ads.model.req.AdsCrowdReq;
import com.xftech.cdp.infra.client.ads.model.req.AdsCrowdTotalReq;
import com.xftech.cdp.infra.client.ads.model.resp.CrowdBaseResp;
import com.xftech.cdp.infra.client.ads.model.resp.CrowdPushResp;
import com.xftech.cdp.infra.client.ads.model.resp.CrowdTotalResp;
import com.xftech.cdp.infra.client.dingtalk.DingTalkUtil;
import com.xftech.cdp.infra.client.dingtalk.config.DingTalkConfig;
import com.xftech.cdp.infra.client.sms.SmsClient;
import com.xftech.cdp.infra.config.ApolloUtil;
import com.xftech.cdp.infra.config.AppConfigService;
import com.xftech.cdp.infra.config.CrowdConfig;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPackDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPushBatchDo;
import com.xftech.cdp.infra.utils.DateUtil;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @since 2023-02-28
 */
@Slf4j
@Service
public class CrowdPushBatchServiceImpl extends AbstractCrowdPushBatchService implements CrowdPushBatchService {

    private static final String[] SEND_SUCCESS_CODE = {"1", "000000", "success"};
    private static final Integer BATCH_SIZE = 500;

    @Autowired
    private SmsClient smsClient;
    @Autowired
    private CrowdPushBatchRepository crowdPushBatchRepository;
    @Autowired
    private StrategyDispatchForSmsService strategyDispatchForSmsService;
    @Autowired
    private AdsClient adsClient;
    @Autowired
    private CacheCrowdPackService cacheCrowdPackService;
    @Autowired
    public CrowdPackRepository crowdPackRepository;
    @Autowired
    private DingTalkConfig dingTalkConfig;
    @Autowired
    private CrowdConfig crowdConfig;
    @Autowired
    private AppConfigService appConfigService;
    @Autowired
    private CacheCrowdExecLogService cacheCrowdExecLogService;
    @Resource
    private DataInsightServiceImpl dataInsightService;

    @Override
    public void crowdPushResultQuery() {
        InvokePageById invokePage = new InvokePageById(BATCH_SIZE);
        invokePaginationById(invokePage, () -> crowdPushBatchRepository.selectUnFinishQuery(invokePage.getId(), invokePage.getPageSize()),
                list -> {
                    List<CrowdPushBatchDo> crowdPushBatchDoList = list.stream().collect(Collectors.toList());
                    crowdPushBatchDoList.forEach(crowdPushBatchDo -> {
                        try {
                            // 短信
                            if (ChannelTypeEnum.MESSAGE.getType() == crowdPushBatchDo.getMarketChannel()) {
                                strategyDispatchForSmsService.smsStrategyPushBatchUpdate(crowdPushBatchDo);
                            }
                            // 优惠券
                            if (ChannelTypeEnum.COUPON.getType() == crowdPushBatchDo.getMarketChannel()) {
                                strategyDispatchForSmsService.couponStrategyPushBatchUpdate(crowdPushBatchDo);
                            }
                        } catch (Exception e) {
                            log.warn("查询指定批次短信发送报告，批次记录id:{}，批次号：{}，查询异常", crowdPushBatchDo.getId(), crowdPushBatchDo.getBatchNum(), e);
                        }
                    });
                }
        );
    }

    @Override
    public void updateById(CrowdPushBatchDo crowdPushBatchDo) {
        crowdPushBatchRepository.updateById(crowdPushBatchDo);
    }

    /**
     * 保存批次下发记录
     *
     * @param dispatchDto 策略执行初始化内容
     * @param app      app
     * @param innerApp innerApp
     * @param records  需要下发的用户ID集合
     * @param code     响应码
     * @param message  下发响应消息
     * @param batchNum 批次号
     * @return 批次下发记录
     */
    @Override
    public <T> void initCrowdPushBatch(DispatchDto dispatchDto, CrowdPushBatchDo crowdPushBatchDo, String app, String innerApp, List<T> records, String code, String message, String batchNum) {
        CrowdPushBatchServiceImpl.BatchItem<T> item = new CrowdPushBatchServiceImpl.BatchItem<>();
        item.setApp(app);
        item.setInnerApp(innerApp);
        item.setRecords(records);
        if (ObjectUtil.isNull(message)) {
            message = "failed";
        }
        init(dispatchDto, crowdPushBatchDo, batchNum, item, code, message);
    }


    @Override
    public void init(DispatchDto dispatchDto, CrowdPushBatchDo crowdPushBatchDo, String batchNum, BatchItem<?> item, String sendCode, String sendMsg) {
        crowdPushBatchDo.setXxlJobId(dispatchDto.getStrategyChannelXxlJobId());
        crowdPushBatchDo.setStrategyId(dispatchDto.getStrategyId());
        crowdPushBatchDo.setStrategyGroupId(dispatchDto.getStrategyGroupId());
        crowdPushBatchDo.setStrategyMarketChannelId(dispatchDto.getStrategyChannelId());
        crowdPushBatchDo.setStrategyExecLogId(dispatchDto.getStrategyExecLogId());
        crowdPushBatchDo.setMarketChannel(dispatchDto.getStrategyChannel());
        if(StrategyMarketChannelEnum.VOICE_NEW.getCode() == dispatchDto.getStrategyChannel()){
            if(!StringUtils.isEmpty(dispatchDto.getNameTypeId())){
                crowdPushBatchDo.setTemplateId(dispatchDto.getNameTypeId());
            }else{
                crowdPushBatchDo.setTemplateId(dispatchDto.getStrategyMarketChannelTemplateId());
            }
        }else{
            String templateId = Optional.ofNullable(dispatchDto.getStrategyMarketChannelTemplateId()).orElse("");
            crowdPushBatchDo.setTemplateId(templateId);
        }
        
        crowdPushBatchDo.setBatchTotal(item.records.size());
        crowdPushBatchDo.setBatchNum(batchNum);
        crowdPushBatchDo.setSendCode(sendCode);
        crowdPushBatchDo.setSendMsg(sendMsg);
        crowdPushBatchDo.initQueryStatus(sendCode);
        crowdPushBatchDo.setExecType(dispatchDto.getStrategyExecLogRetryId() != null ? CrowdPushExecTypeEnum.RETRY.getCode() : null);
        crowdPushBatchDo.setMobileBatch(JSON.toJSONString(item));
        crowdPushBatchDo.setDetailTableNo(dispatchDto.getDetailTableNo());
    }

    @Override
    public <T> void insert(DispatchDto dispatchDto, CrowdPushBatchDo crowdPushBatchDo, String app, String innerApp, List<T> idList, String code, String message, String flowNo) {
        initCrowdPushBatch(dispatchDto, crowdPushBatchDo, app, innerApp, idList, code, message, flowNo);
        crowdPushBatchRepository.insert(crowdPushBatchDo);
    }

    @Override
    public void pushCrowdSqlToAds(CrowdPackDo crowdPackDo) {
        CrowdPushResp crowdPushResp=null;
        try {
            crowdPackDo.runCheck();
            crowdPackDo.setLatestRefreshTime(LocalDateTime.now());

            AdsCrowdReq req = new AdsCrowdReq();
            CrowdBaseResp<List<CrowdPushResp>> response;
            if (isDataInsightWhiteListCrowd(crowdPackDo.getId())) {
                req.setData(Collections.singletonList(new AdsCrowdReq.DataItem(crowdPackDo.getId(), replaceCrowdSql(crowdPackDo.getCrowdSql()))));
                response = dataInsightService.pushCrowd(req);
            } else {
                req.setData(Collections.singletonList(new AdsCrowdReq.DataItem(crowdPackDo.getId(), crowdPackDo.getCrowdSql())));
                response = adsClient.pushCrowd(req);
            }

            if (!response.isSuccess()) {
                throw new CrowdException("人群包执行接口返回失败");
            }

            List<CrowdPushResp> payload = response.getPayload();
            crowdPushResp = payload.stream()
                    .filter(t -> Objects.equals(crowdPackDo.getId(), t.getCrowdId()))
                    .findFirst()
                    .orElse(null);

            if (crowdPushResp == null || Objects.equals(crowdPushResp.getStatus(), 0)) {
                throw new CrowdException("人群包执行接口返回结果异常");
            }
        } catch (Exception e) {
            throw new CrowdException("人群包执行接口请求异常", e);
        }finally {
            handleCrowdPushResponse(crowdPackDo, crowdPushResp);
        }
    }

    /**
     * @param :
     * @return null
     * <AUTHOR>
     * @description 人群包跑批结果查询，只查询状态是正在执行的，
     * @date 2023/11/22 11:19
     */
    @Override
    public void getAllCrowTotal() {
        List<CrowdPackDo> crowdPackAll = crowdPackRepository.getCrowdPackAll(CrowdPullTypeEnum.USER_LABLE.getCode())
                .stream().filter(p -> CrowdStatusEnum.EXECUTING.getCode() == p.getStatus()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(crowdPackAll)) {
            return;
        }
        //超过n小时未完成刷新报警
        int timePass=appConfigService.timePassCrowdTask();
        List<Long> timeOut = crowdPackAll.stream().filter(f -> DateUtil.isTimePassed(f.getLatestRefreshTime(),timePass))
                .map(CrowdPackDo::getId)
                .collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(timeOut)){
            alarmProdMsg("人群包"+JSON.toJSONString(timeOut)+"超过"+timePass+"小时未完成刷新");
        }

        AdsCrowdTotalReq adsCrowdTotalReq = new AdsCrowdTotalReq();
        List<AdsCrowdTotalReq.DataItem> adsCrowdTotalReqItem = crowdPackAll.stream().filter(x -> !isDataInsightWhiteListCrowd(x.getId())).map(t -> new AdsCrowdTotalReq.DataItem(t.getId())).collect(Collectors.toList());
        adsCrowdTotalReq.setData(adsCrowdTotalReqItem);
        CrowdBaseResp<List<CrowdTotalResp>> crowdTotal;
        if (CollectionUtils.isNotEmpty(adsCrowdTotalReqItem)) {
            crowdTotal = adsClient.getCrowdTotal(adsCrowdTotalReq);
        } else {
            crowdTotal = new CrowdBaseResp<>();
            crowdTotal.setSuccess(true);
            crowdTotal.setPayload(Lists.newArrayList());
        }

        AdsCrowdTotalReq dataInsightCrowdTotalReq = new AdsCrowdTotalReq();
        List<AdsCrowdTotalReq.DataItem> dataInsightDataItems = crowdPackAll.stream().filter(x -> isDataInsightWhiteListCrowd(x.getId())).map(t -> new AdsCrowdTotalReq.DataItem(t.getId())).collect(Collectors.toList());
        dataInsightCrowdTotalReq.setData(dataInsightDataItems);
        CrowdBaseResp<List<CrowdTotalResp>> dataInsightCrowdTotal = dataInsightService.getCrowdTotal(dataInsightCrowdTotalReq);

        //if (!crowdTotal.isSuccess() || (CollectionUtils.isNotEmpty(dataInsightDataItems) && !dataInsightCrowdTotal.isSuccess())) {
        //    alarmProdMsg("人群包查询数量接口调用失败,请排查！msg："+crowdTotal.getMessage());
        //    return;
        //}
        if (!crowdTotal.isSuccess()) {
            alarmProdMsg("人群包查询数量接口调用失败,请排查！msg："+crowdTotal.getMessage());
            return;
        }
        if (CollectionUtils.isNotEmpty(dataInsightDataItems) && !dataInsightCrowdTotal.isSuccess()) {
            log.error("标签平台-人群推送结果查询失败!!!");
        }
        crowdTotal.getPayload().stream()
                .filter(f -> f.isFail()  && appConfigService.whetherGrayscaleCrowd(f.getCrowdId()))
                .forEach(m -> {
                    alarmProdMsg("人群包查询数量接口返回异常，人群包id=："+m.getCrowdId()+"msg="+crowdTotal.getMessage());
                });
        if (dataInsightCrowdTotal.isSuccess()) {
            dataInsightCrowdTotal.getPayload().stream()
                    .filter(f -> f.isFail()  && appConfigService.whetherGrayscaleCrowd(f.getCrowdId()))
                    .forEach(m -> {
                        alarmProdMsg("人群包查询数量接口返回异常，人群包id=："+m.getCrowdId()+"msg="+dataInsightCrowdTotal.getMessage());
                    });
        }

        List<CrowdPackDo> res = crowdTotal.getPayload().stream()
                .filter(f -> Objects.equals(1,f.getStatus())  && appConfigService.whetherGrayscaleCrowd(f.getCrowdId()))
                .map(m -> {
                    CrowdPackDo cpd = new CrowdPackDo();
                    cpd.setId(m.getCrowdId());
                    LocalDateTime lastRefreshTime = crowdPackAll.stream().filter(item -> item.getId().equals(m.getCrowdId())).map(CrowdPackDo::getLatestRefreshTime)
                            .findFirst().orElse(null);
                    cpd.setLatestRefreshTime(lastRefreshTime);
                    cpd.execSuccess(m.getTotal());
                    return cpd;
                })
                .collect(Collectors.toList());
        if (dataInsightCrowdTotal.isSuccess()) {
            List<CrowdPackDo> dataInsightRes = dataInsightCrowdTotal.getPayload().stream()
                    .filter(f -> Objects.equals(1,f.getStatus())  && appConfigService.whetherGrayscaleCrowd(f.getCrowdId()))
                    .map(m -> {
                        CrowdPackDo cpd = new CrowdPackDo();
                        cpd.setId(m.getCrowdId());
                        LocalDateTime lastRefreshTime = crowdPackAll.stream().filter(item -> item.getId().equals(m.getCrowdId())).map(CrowdPackDo::getLatestRefreshTime)
                                .findFirst().orElse(null);
                        cpd.setLatestRefreshTime(lastRefreshTime);
                        cpd.execSuccess(m.getTotal());
                        return cpd;
                    })
                    .collect(Collectors.toList());

            res.addAll(dataInsightRes);
        }

        if (CollectionUtils.isNotEmpty(res)) {
            cacheCrowdPackService.updateBatchById(res);
            //记crow_exe_log
            cacheCrowdExecLogService.saveBatchCrowdExeLog(res, LocalDateTime.now());
        }

    }

    public void alarmMsg(String msg){
        try {
            if(appConfigService.getAlarmSwitch()){
                log.warn("crowd alarmMsg==="+msg);
                return;
            }
            List<String> refreshTimeoutAtMobiles = crowdConfig.getCrowdalarmMobiles();
            DingTalkUtil.dingTalk(dingTalkConfig.getAdsAlarmUrl(),msg,refreshTimeoutAtMobiles);
        }catch (Exception e){
            log.error("dingTalk errror");
        }
    }

    public void alarmProdMsg(String msg){
        try {
            if(appConfigService.getAlarmSwitch()){
                log.warn("alarmProdMsg alarmMsg==="+msg);
                return;
            }
            List<String> refreshTimeoutAtMobiles = crowdConfig.getCrowdalarmProdMobiles();
            DingTalkUtil.dingTalk(dingTalkConfig.getAlarmUrl(),msg,refreshTimeoutAtMobiles);
        }catch (Exception e){
            log.error("dingTalk errror");
        }
    }

    private  List<AdsCrowdReq.DataItem> prepareAdsCrowdReqs(List<CrowdPackDo> crowdPackAll, StringBuilder alarm) {
        return crowdPackAll.stream()
                .map(p -> {
                    try {
                        p.startCheck();
                        return new AdsCrowdReq.DataItem(p.getId(), p.getCrowdSql());
                    } catch (CrowdException e) {
                        alarm.append("人群包id=").append(p.getId()).append(e.getMessage());
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private List<AdsCrowdReq.DataItem> prepareRetryAdsCrowdReqs(List<CrowdPackDo> crowdPackAll) {
        return crowdPackAll.stream().filter(p->p.retry() && appConfigService.whetherGrayscaleCrowd(p.getId())).map(m-> new AdsCrowdReq.DataItem(m.getId(), m.getCrowdSql())).collect(Collectors.toList());
    }

    @Override
    public void pushCrowdList() {
        AdsCrowdReq adq = new AdsCrowdReq();
        StringBuilder alarm = new StringBuilder();
        StringBuilder alarmProd = new StringBuilder();
        List<CrowdPackDo> crowdPackAll = crowdPackRepository.getCrowdPackAll(CrowdPullTypeEnum.USER_LABLE.getCode());
        List<AdsCrowdReq.DataItem> req = prepareAdsCrowdReqs(crowdPackAll, alarm);
        if(StringUtils.isNotBlank(alarm.toString())){
            alarm.insert(0, "无效人群包如下->");
            log.warn(alarm.toString());
        }
        if (CollectionUtils.isEmpty(req)) {
            alarmMsg("本次无需要执行的人群包数据");
            return;
        }
        adq.setData(req);
        pushCrowdWithAlarm(adq,alarmProd);
    }

    public void pushCrowdWithAlarm(AdsCrowdReq req,StringBuilder alarmProd){
        try {
            List<AdsCrowdReq.DataItem> dpsCrowdItemList = req.getData().stream().filter(x -> !isDataInsightWhiteListCrowd(x.getCrowdId())).collect(Collectors.toList());
            List<AdsCrowdReq.DataItem> dataInsightCrowdItemList = req.getData().stream().filter(x -> isDataInsightWhiteListCrowd(x.getCrowdId())).collect(Collectors.toList());
            dataInsightCrowdItemList.forEach(x -> x.setCrowdSql(replaceCrowdSql(x.getCrowdSql())));

            req.setData(dpsCrowdItemList);
            CrowdBaseResp<List<CrowdPushResp>> response = adsClient.pushCrowd(req);

            AdsCrowdReq dataInsightRequest = new AdsCrowdReq();
            dataInsightRequest.setData(dataInsightCrowdItemList);
            CrowdBaseResp<List<CrowdPushResp>> dataInsightCrowdResponse = dataInsightService.pushCrowd(dataInsightRequest);
            // todo 切换数据平台接口
            //if (!response.isSuccess() || (CollectionUtils.isNotEmpty(dataInsightCrowdItemList) && !dataInsightCrowdResponse.isSuccess())) {
            //    throw new CrowdException("人群包执行接口返回失败");
            //}
            if (!response.isSuccess()) {
                throw new CrowdException("人群包执行接口返回失败");
            }
            if (CollectionUtils.isNotEmpty(dataInsightCrowdItemList) && !dataInsightCrowdResponse.isSuccess()) {
                log.error("标签平台-人群推送失败!!!");
            }
            Map<Long, CrowdPushResp> payloadMap = response.getPayload()
                    .stream()
                    .collect(Collectors.toMap(CrowdPushResp::getCrowdId, Function.identity()));
            if (dataInsightCrowdResponse.isSuccess()) {
                Map<Long, CrowdPushResp> dataInsightPayloadMap = dataInsightCrowdResponse.getPayload()
                        .stream()
                        .collect(Collectors.toMap(CrowdPushResp::getCrowdId, Function.identity()));

                payloadMap.putAll(dataInsightPayloadMap);
            }

            req.getData().addAll(dataInsightCrowdItemList);
            for(AdsCrowdReq.DataItem ad:req.getData()){
                CrowdPushResp contain = payloadMap.get(ad.getCrowdId());
                CrowdPackDo cpd = new CrowdPackDo();
                cpd.setId(ad.getCrowdId());
                cpd.setLatestRefreshTime(LocalDateTime.now());
                if(Objects.isNull(contain) ||  (!contain.isSuccess())){
                    alarmProd.append("人群包").append(ad.getCrowdId()).append("提交任务返回异常");
                }
                handleCrowdPushResponse(cpd, contain);
            }
            if(StringUtils.isNotBlank(alarmProd.toString())){
                alarmProdMsg(alarmProd.toString());
            }
        } catch (Exception e) {
            handleCrowdPushFailResponse(req);
            alarmProdMsg("人群包执行接口异常,请排查！");
        }
    }

    @Override
    public void pushCrowdListRetry() {
        StringBuilder alarmProd = new StringBuilder();
        List<AdsCrowdReq.DataItem> req= Lists.newArrayList();
        List<CrowdPackDo> crowdPackAll = crowdPackRepository.getCrowdPackAll(CrowdPullTypeEnum.USER_LABLE.getCode());
        List<AdsCrowdReq.DataItem> alarm = prepareAlarmAdsCrowdReqs(crowdPackAll);
        List<AdsCrowdReq.DataItem> retry = prepareRetryAdsCrowdReqs(crowdPackAll);
        if(CollectionUtils.isNotEmpty(alarm)){
            //如果发现符合条件还未触发，报警并执行
            req.addAll(alarm);
            String crowdIds = alarm.stream()
                    .map(AdsCrowdReq.DataItem::getCrowdId)
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));
            alarmProdMsg("人群包:".concat(crowdIds).concat("今天还未执行！"));
        }
        if(CollectionUtils.isNotEmpty(retry))req.addAll(retry);
        if(CollectionUtils.isEmpty(req)){
            return;
        }
        AdsCrowdReq areq = new AdsCrowdReq();
        areq.setData(req);
        pushCrowdWithAlarm(areq,alarmProd);
    }

    @Override
    public void pushCrowdListLog() {
        //筛选人群包
        List<CrowdPackDo> crowdPackDos = crowdPackRepository.queryAllEffectiveCrowdsByTime(LocalDateTime.now());
        //剔除加贷电销表和手动执行的
        List<CrowdPackDo> effectiveCrowdList = crowdPackDos.stream()
                .filter(item -> Objects.equals(item.getPullType(), CrowdPullTypeEnum.USER_LABLE.getCode())
                        && Objects.equals(item.getRefreshType(), CrowdRefreshTypeEnum.AUTOMATIC.getCode())
                ).collect(Collectors.toList());
        List<CrowdPackDo> recordExecLog = new ArrayList<>();
        for (CrowdPackDo crowdPackDo : effectiveCrowdList) {
            //判断是否人群包新方案
            Boolean isNewMode = appConfigService.whetherGrayscaleCrowd(crowdPackDo.getId());
            if (isNewMode && !Objects.equals(CrowdStatusEnum.INIT.getCode(), crowdPackDo.getStatus())) {
                recordExecLog.add(crowdPackDo);
            }
        }
        cacheCrowdExecLogService.saveBatchCrowdExeLog(recordExecLog, null);
        log.info("pushCrowdListCrowdExecLog end !");
    }

    private List<AdsCrowdReq.DataItem> prepareAlarmAdsCrowdReqs(List<CrowdPackDo> crowdPackAll) {
        //今天还未执行的
        return crowdPackAll.stream().filter(p->p.undo() && appConfigService.whetherGrayscaleCrowd(p.getId())).map(m-> new AdsCrowdReq.DataItem(m.getId(), m.getCrowdSql())).collect(Collectors.toList());
    }

    private void handleCrowdPushFailResponse(AdsCrowdReq req) {
        List<CrowdPackDo> res = req.getData().stream().filter(p -> appConfigService.whetherGrayscaleCrowd(p.getCrowdId()))
                .map(m -> {
                    CrowdPackDo cpd = new CrowdPackDo();
                    cpd.setId(m.getCrowdId());
                    cpd.setLatestRefreshTime(LocalDateTime.now());
                    cpd.setStatus(CrowdStatusEnum.FAILED.getCode());
                    return cpd;
                })
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(res)) cacheCrowdPackService.updateBatchById(res);

    }

    private void handleCrowdPushResponse(CrowdPackDo crowdPackDo, CrowdPushResp crowdPushResp) {
        Boolean isStop = appConfigService.whetherGrayscaleCrowd(crowdPackDo.getId());
        if(isStop) {
            crowdPackDo.setStatus(Objects.nonNull(crowdPushResp) && crowdPushResp.isSuccess()
                    ? CrowdStatusEnum.EXECUTING.getCode()
                    : CrowdStatusEnum.FAILED.getCode());

            cacheCrowdPackService.updateById(crowdPackDo);
        }
    }

    @Data
    public static class BatchItem<T> {

        private String app;

        private String innerApp;

        private List<T> records;

    }

    /**
     * 是否灰度到标签平台人群包
     * @param crowdId
     * @return
     */
    private boolean isDataInsightWhiteListCrowd(Long crowdId) {
        try {
            List<Long> dataInsightWhiteListCrowd = JSON.parseArray(ApolloUtil.getAppProperty("DataInsightWhiteListCrowd", "[]"), Long.class);
            return dataInsightWhiteListCrowd.contains(crowdId);
        } catch (Exception e) {
            log.error("isDataInsightWhiteListCrowd error", e);
        }
        return false;
    }

    /**
     * 标签平台人群包-替换表名
     * @param crowdSql
     * @return
     */
    private String replaceCrowdSql(String crowdSql) {
        if (StringUtils.isBlank(crowdSql)) {
            return crowdSql;
        }
        try {
            return crowdSql.replace("ads_user_label_detail_info_df_view", "dp_user_label_wide_table_info_df_view");
        } catch (Exception e) {
            log.error("replaceCrowdSql error", e);
        }
        return crowdSql;
    }

}
