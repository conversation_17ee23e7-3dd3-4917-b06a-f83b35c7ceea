package com.xftech.cdp.domain.strategy.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */

@Getter
@AllArgsConstructor
public enum FlowTypeEnum {

    OFFLINE(1, "离线"),
    REALTIME(2, "暂停中"),

    ;

    private final int code;
    private final String description;


    public static boolean isOffline(Integer code){
        return Objects.equals(OFFLINE.code, code);
    }

    public static boolean isOffline(Short code){
        return Objects.equals(OFFLINE.code, code.intValue());
    }

    public static boolean isRealTime(Integer code){
        return Objects.equals(REALTIME.code, code);
    }

    public static boolean isRealTime(Short code){
        return Objects.equals(REALTIME.code, code.intValue());
    }
}
