/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.resp.flow.strategy;

import com.xftech.cdp.api.dto.req.DispatchConfig;
import com.xftech.cdp.api.dto.req.InstantStrategyCreateReq;
import com.xftech.cdp.api.dto.req.StrategyCreateReq;
import com.xftech.cdp.api.dto.req.flow.strategy.FlowCtrlLimit;
import com.xftech.cdp.api.dto.req.flow.strategy.Node;
import com.xftech.cdp.api.dto.resp.InstantStrategyDetailResp;
import com.xftech.cdp.api.dto.resp.StrategyDetailResp;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ DetailStragegyConfig, v 0.1 2023/12/19 17:07 yye.xu Exp $
 */

@Data
public class DetailStragegyConfig {
    private Node node;
    private Node nextNode;

    private String flowNo;
    private Long strategyId;
    @ApiModelProperty(value = "策略名称", required = true)
    private String name;

    @ApiModelProperty(value = "发送频次")
    private StrategyCreateReq.SendFrequency sendFrequency;

    @ApiModelProperty(value = "策略描述")
    private String detailDescription;

    @ApiModelProperty(value = "首个策略有：人群包id,多个人群\";\"分隔", required = true)
    @NotBlank(message = "人群包id不能为空")
    private String crowdPackIds;

    @ApiModelProperty(value = "触达渠道, json字符串列表[1,2,3,4]")
    private List<Integer> marketChannels;

    @ApiModelProperty(value = "abTest 0-否 1-是", required = true)
    private Integer abTest;

    @ApiModelProperty(value = "分组方式，0：uid倒数两位 1：随机数")
    private Integer abType;

    @ApiModelProperty(value = "随机数")
    private String randomItem;

    @ApiModelProperty(value = "0-单次, 1-例行, 2-事件, 3-周期, 9-引擎", required = true)
    private Integer sendRuler;

    @ApiModelProperty(value = "状态：-1-未发布 0-已发布 1-执行中 4-暂停 5-已结束")
    private Integer status;

    @ApiModelProperty(value = "人群分组")
    private List<StrategyDetailResp.StrategyGroup> strategyGroups;

    @ApiModelProperty(value = "营销条件配置")
    private List<InstantStrategyDetailResp.MarketCondition> marketCondition;

    @ApiModelProperty(value = "流控规则限制")
    private FlowCtrlLimit flowCtrlLimit;

    @ApiModelProperty(value = "排除项类型")
    private List<Integer> excludeType;

    // 派发数据配置
    @ApiModelProperty(value = "派发数据配置")
    private DispatchConfig dispatchConfig;

    // 触达类型: MKT-营销,  NOTIFY-通知
    @ApiModelProperty(value = "触达类型: MKT-营销,  NOTIFY-通知")
    private String dispatchType;
}