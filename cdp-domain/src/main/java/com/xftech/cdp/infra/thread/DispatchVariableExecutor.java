/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.thread;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigChangeListener;
import com.ctrip.framework.apollo.ConfigService;
import com.ctrip.framework.apollo.model.ConfigChange;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.xftech.cdp.infra.config.AppConfigService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.HashSet;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 *
 * <AUTHOR>
 * @version $ DispatchAlarmExecutor, v 0.1 2024/1/10 17:01 lingang.han Exp $
 */

@Slf4j
@Service
@AllArgsConstructor
public class DispatchVariableExecutor {

    private static int pooloDefaultCoreSize = 10;
    private static int pooloDefaultMaxSize = 20;
    private static String poolCoreSizeKey = "dispatchVariableExecutor.pool.coreSize";
    private static String poolMaxSizeKey = "dispatchVariableExecutor.pool.maxSize";

    private AppConfigService appConfigService;
    private static ExecutorService pool = new ThreadPoolExecutor(pooloDefaultCoreSize, pooloDefaultMaxSize,
            15L, TimeUnit.MINUTES,
            new ArrayBlockingQueue<>(1000),
            new CustomizableThreadFactory("dispatchVariableExecutor-"),
            new ThreadPoolExecutor.CallerRunsPolicy());

    public static ExecutorService getPool() {
        return pool;
    }

    @PostConstruct
    public void init() throws Exception {
        int coreSize = Integer.parseInt(appConfigService.getPropertyValue(poolCoreSizeKey,
                String.valueOf(pooloDefaultCoreSize)));
        int maxSize = Integer.parseInt(appConfigService.getPropertyValue(poolMaxSizeKey,
                String.valueOf(pooloDefaultMaxSize)));
        setPoolCoreSize(coreSize);
        setPoolMaxSize(maxSize);
        Config config = ConfigService.getAppConfig();
        config.addChangeListener(new ConfigChangeListener() {
                                     @Override
                                     public void onChange(ConfigChangeEvent configChangeEvent) {
                                         for (String key : configChangeEvent.changedKeys()) {
                                             ConfigChange change = configChangeEvent.getChange(key);
                                             if (change != null) {
                                                 if (poolCoreSizeKey.equals(key)) {
                                                     setPoolCoreSize(Integer.parseInt(change.getNewValue()));
                                                 } else if (poolMaxSizeKey.equals(key)) {
                                                     setPoolMaxSize(Integer.parseInt(change.getNewValue()));
                                                 }
                                             }
                                         }
                                     }
                                 }, new HashSet<>(Arrays.asList(poolCoreSizeKey, poolMaxSizeKey))
        );
    }

    public void setPoolCoreSize(int size) {
        if (size <= 0) {
            return;
        }
        if (((ThreadPoolExecutor) pool).getCorePoolSize() != size) {
            log.info("dispatchVariableExecutor, 线程池核心大小重新设置, coreSize = {}", size);
            ((ThreadPoolExecutor) pool).setCorePoolSize(size);
        }
    }

    public void setPoolMaxSize(int size) {
        if (size <= 0) {
            return;
        }
        if (((ThreadPoolExecutor) pool).getMaximumPoolSize() != size) {
            log.info("dispatchVariableExecutor, 线程池最大值重新设置, maxSize = {}", size);
            ((ThreadPoolExecutor) pool).setMaximumPoolSize(size);
        }
    }
}