package com.xftech.cdp.domain.marketing.repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.infra.repository.cdp.marketing.po.MarketingActivityUserDetailDo;
import com.xftech.cdp.infra.repository.cdp.marketing.po.MarketingActivityUserRegisterDo;

import org.springframework.stereotype.Repository;


/**
 * 营销活动信息表 marketing_activity_info dao
 */
@Repository
public class MarketingActivityUserRegisterRepository {

    /**
     * 根据活动ID和用户ID查询报名信息
     *
     * @param activityId 活动ID
     * @param userId     用户ID
     * @return
     */
    public List<MarketingActivityUserRegisterDo> selectByActivityAndUserId(Long activityId, Long userId) {
        Map<String, Object> param = new HashMap<>();
        param.put("activityId", activityId);
        param.put("userId", userId);
        return DBUtil.selectList("marketingActivityUserRegister.selectByActivityAndUserId", param);
    }

    /**
     * 新增报名信息
     *
     * @param marketingActivityUserRegisterDo
     */
    public void insert(MarketingActivityUserRegisterDo marketingActivityUserRegisterDo) {
        DBUtil.insert("marketingActivityUserRegister.insertSelective", marketingActivityUserRegisterDo);
    }

}