package com.xftech.cdp.domain.strategy.service;

import com.xftech.cdp.api.dto.req.StrategyCreateReq;
import com.xftech.cdp.api.dto.req.UpdatePolicyPriorityReq;
import com.xftech.cdp.domain.dispatch.dto.DispatchDto;
import com.xftech.cdp.infra.client.telemarketing.model.TelePushArgs;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;

import java.util.List;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
public interface TelePushService {

     void pushTeleAndSetTemplateId(StrategyCreateReq.StrategyMarketChannel strategyMarketChannel);

     String getPolicyList(List<Integer> ids);

     String getPolicyDetail(Integer id);

     TelePushArgs getTelePushArgs(Integer id, List<CrowdDetailDo> userIdList, DispatchDto reach, String app);

     boolean updatePolicyPriority(UpdatePolicyPriorityReq req);
}
