package com.xftech.cdp.api;

import java.util.List;

import com.xftech.cdp.api.dto.base.Response;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
@Api(tags = "测试工具类")
@RequestMapping("/test")
public interface TestUtilApi {

    @ApiOperation("配置项测试")
    @GetMapping("/config")
    Response<String> testConfig(@RequestParam("type") String type,@RequestParam("name") List<String> name) throws InterruptedException;

    @ApiOperation("刷新人群包sql")
    @GetMapping("/operateSql")
    Response<String> operateSql();

    @ApiOperation("变量特征查询测试")
    @GetMapping("/labelQueryAll")
    Response<String> labelQueryAll(@RequestParam("labels") String labels, @RequestParam("userNo") Long userNo);

    @ApiOperation("变量特征查询测试")
    @GetMapping("/labelQueryNew")
    Response<String> labelQueryNew(@RequestParam("labels") String labels, @RequestParam("userNo") Long userNo);

    @ApiOperation("变量特征查询测试")
    @GetMapping("/labelQueryOld")
    Response<String> labelQueryOld(@RequestParam("labels") String labels, @RequestParam("userNo") Long userNo);

    @ApiOperation("分片写入测试")
    @GetMapping("/sliceTaskWrite")
    Response<String> sliceTaskWrite(@RequestParam("strategyId") Long strategyId);

    @ApiOperation("分片执行测试")
    @GetMapping("/sliceTaskExecute")
    Response<String> sliceTaskExecute(@RequestParam("labels") String labels, @RequestParam("userNo") Long userNo);

    @ApiOperation("分片OSS读取测试")
    @GetMapping("/readSliceFile")
    Response<String> readSliceFile(@RequestParam("crowdSliceId") Long crowdSliceId, @RequestParam("crowdId") Long crowdId);

    @ApiOperation("分片OSS本地读取测试")
    @GetMapping("/readSliceFileByDetail")
    Response<String> readSliceFileByDetail(@RequestParam("startPos") String filePath,
                                           @RequestParam("startPos") Long startPos,
                                           @RequestParam("endPos") Long endPos);

    @ApiOperation("读取时间查询测试")
    @GetMapping("/marketEventQuery")
    Response<String> marketEventQuery(@RequestParam("filePath") String eventName);
}
