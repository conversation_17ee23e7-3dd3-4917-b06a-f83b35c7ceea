package com.xftech.cdp.infra.repository.cdp.strategy.po;

import com.xftech.cdp.domain.strategy.model.enums.StrategyGroupTypeEnum;
import com.xftech.cdp.domain.strategy.model.group.StrategyGroupMatch;
import com.xftech.cdp.infra.repository.Do;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.function.BiPredicate;

/**
 * <p>
 * 策略分组信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class StrategyGroupDo extends Do {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 策略id
     */
    private Long strategyId;

    /**
     * 组名
     */
    private String name;

    /**
     * 分组配置
     */
    private String groupConfig;

    /**
     * 是否执行
     * 0 - 不执行
     * 1 - 执行
     */
    private Integer isExecutable;


    /**
     * 扩展信息
     */
    private String extInfo;

    /**
     * 获取分组匹配规则
     *
     * @param strategyGroupTypeEnum 分组类型
     * @return 匹配规则
     */
    public BiPredicate<String, Integer> match(StrategyGroupTypeEnum strategyGroupTypeEnum) {
        return StrategyGroupMatch.condition(strategyGroupTypeEnum, this.groupConfig);
    }

}
