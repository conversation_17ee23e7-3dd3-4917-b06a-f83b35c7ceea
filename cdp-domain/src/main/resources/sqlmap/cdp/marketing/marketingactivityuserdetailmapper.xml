<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="marketingActivityUserDetail">

    <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.marketing.po.MarketingActivityUserDetailDo">
        <id property="id" column="id"/>
        <result property="activityId" column="activity_id"/>
        <result property="userId" column="user_id"/>
        <result property="rewardType" column="reward_type"/>
        <result property="rewardId" column="reward_id"/>
        <result property="status" column="status"/>
        <result property="dFlag" column="d_flag"/>
        <result property="createdOp" column="created_op"/>
        <result property="updatedOp" column="updated_op"/>
        <result property="createdTime" column="created_time"/>
        <result property="updatedTime" column="updated_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,activity_id, user_id, reward_type, reward_id, status, d_flag, created_op, updated_op, created_time, updated_time
    </sql>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from marketing_activity_user_detail where id = #{id} and d_flag = 0
    </select>

    <select id="selectByActivityAndUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from marketing_activity_user_detail where activity_id = #{activityId} and user_id = #{userId} and reward_type = #{rewardType} and d_flag = 0 order by id desc
    </select>

    <insert id="insertSelective"
            parameterType="com.xftech.cdp.infra.repository.cdp.marketing.po.MarketingActivityUserDetailDo"
            keyColumn="id" keyProperty="id" useGeneratedKeys="true">
        insert into marketing_activity_user_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="activityId != null">
                activity_id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="rewardType != null">
                reward_type,
            </if>
            <if test="rewardId != null">
                reward_id,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="createdOp != null">
                created_op,
            </if>
            <if test="updatedOp != null">
                updated_op,
            </if>
            created_time,
            updated_time,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="activityId != null">
                #{activityId},
            </if>
            <if test="userId != null">
                #{userId},
            </if>
            <if test="rewardType != null">
                #{rewardType},
            </if>
            <if test="rewardId != null">
                #{rewardId},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="createdOp != null">
                #{createdOp},
            </if>
            <if test="updatedOp != null">
                #{updatedOp},
            </if>
            now(),
            now(),
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.xftech.cdp.infra.repository.cdp.marketing.po.MarketingActivityUserDetailDo">
        update marketing_activity_user_detail
        <set>
            <if test="activityId != null">
                activity_id = #{activityId},
            </if>
            <if test="userId != null">
                user_id = #{userId},
            </if>
            <if test="rewardType != null">
                reward_type = #{rewardType},
            </if>
            <if test="rewardId != null">
                reward_id = #{rewardId},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="createdOp != null">
                created_op = #{createdOp},
            </if>
            <if test="updatedOp != null">
                updated_op = #{updatedOp},
            </if>
            updated_time = now()
        </set>
        where id = #{id}
    </update>

    <delete id="deleteByActivityAndUserId">
        update marketing_activity_user_detail
        set d_flag = 1
        where activity_id = #{id} and user_id = #{userId}
    </delete>

</mapper>
