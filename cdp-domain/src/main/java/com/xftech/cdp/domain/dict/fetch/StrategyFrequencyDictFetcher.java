package com.xftech.cdp.domain.dict.fetch;

import com.xftech.cdp.api.dto.resp.dict.Dict;
import com.xftech.cdp.domain.dict.model.enums.DictCode;
import com.xftech.cdp.domain.strategy.model.enums.StrategyFrequencyEnum;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/2/21
 */
@Component
public class StrategyFrequencyDictFetcher implements DictFetcher {
    @Override
    public List<Dict> getDict() {
        List<Dict> result = new ArrayList<>();
        List<StrategyFrequencyEnum> strategyStatusEnums = Arrays.stream(StrategyFrequencyEnum.values()).collect(Collectors.toList());
        for (StrategyFrequencyEnum strategyStatusEnum : strategyStatusEnums) {
            result.add(Dict.builder().dictCode(String.valueOf(strategyStatusEnum.getCode())).dictValue(strategyStatusEnum.getDescription()).build());
        }
        return result;
    }

    @Override
    public DictCode getDictCode() {
        return DictCode.STRATEGY_FREQUENCY;
    }

    @Override
    public String getDescription() {
        return "发送频率";
    }
}
