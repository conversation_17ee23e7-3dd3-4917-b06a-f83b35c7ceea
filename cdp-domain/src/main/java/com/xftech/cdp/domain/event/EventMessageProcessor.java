package com.xftech.cdp.domain.event;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.xftech.cdp.domain.event.model.config.FieldConfig;
import com.xftech.cdp.domain.event.model.constants.EventConfigConstants;
import com.xftech.cdp.domain.event.model.dto.Field;
import com.xftech.cdp.domain.event.model.dto.FieldDetail;
import com.xftech.cdp.domain.event.model.dto.MqEventFieldMappingConfig;
import com.xftech.cdp.domain.event.parse.DataParser;
import com.xftech.cdp.domain.event.parse.DataParserStrategy;
import com.xftech.cdp.domain.event.processor.DataProcessor;
import com.xftech.cdp.domain.event.processor.DataProcessorStrategy;
import com.xftech.cdp.domain.event.service.MqEventFieldConfigService;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventMessageVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 实时事件消息处理器
 * <AUTHOR>
 * @version $ EventMessageProcessor, v 0.1 2024/11/19 15:22 snail Exp $
 */
@Slf4j
@Component
public class EventMessageProcessor {
    @Autowired
    private MqEventFieldConfigService mqEventFieldConfigService;

    @Autowired
    private DataProcessorStrategy dataProcessorStrategy;

    @Autowired
    private DataParserStrategy dataParserStrategy;

    /**
     * 获取某个消费者的消息处理器并对消息体执行处理
     * @param topic 消息的生产者名称
     * @param consumer 消息的消费者名称
     * @param tag 消息的tag信息
     * @param messageBody 消息体信息
     * @return 处理后的消息信息
     */
    public BizEventMessageVO doMessageProcessor(String topic, String consumer, String tag, String messageBody){
        String validateResult = messageProcessCheck(topic,consumer,messageBody);
        if(StringUtils.isNotEmpty(validateResult)){
            log.warn("message validate failed, topic={},consumer={},message={},validate={}",topic,consumer,messageBody,validateResult);
            return null;
        }

        MqEventFieldMappingConfig mappingConfig = mqEventFieldConfigService.getMqEventFieldMappingConfig(topic,consumer,tag);
        if(Objects.isNull(mappingConfig)){
            log.warn("can't find mq event field config, topic={},consumer={},tag={}",topic,consumer,tag);
            return null;
        }

        BizEventMessageVO bizEventMessage = messageProcess(mappingConfig,messageBody);
        log.info("event message parse,consumer={},bizEventMessage={}",consumer,bizEventMessage);
        return bizEventMessage;
    }

    /**
     * 解析消息体和执行数据处理
     * @param mappingConfig 字段映射关系信息
     * @param messageBody 消息体信息
     * @return 处理后的消息体信息
     */
    private BizEventMessageVO messageProcess(MqEventFieldMappingConfig mappingConfig, String messageBody){
        Map<String,Object> parsedMap = messageParse(mappingConfig,messageBody);
        if(CollectionUtils.isEmpty(parsedMap) || CollectionUtils.isEmpty(mappingConfig.getFieldList())){
            return null;
        }

        //对字段配置的数据处理器执行处理操作
        messageDataProcess(mappingConfig,parsedMap);

        //处理扩展参数的属性名称
        processExtParam(parsedMap);

        //反序列化消息体信息
        BizEventMessageVO bizEventMessage = convert(mappingConfig,parsedMap);
        log.info("event message process,bizEventMessage={}",bizEventMessage);

        return bizEventMessage;
    }

    /**
     * 将解析后的消息体转换为实体类
     * @param parsedMap 解析处理后的消息体信息
     * @return 反序列化后的消息体信息
     */
    private BizEventMessageVO convert(MqEventFieldMappingConfig mappingConfig, Map<String,Object> parsedMap){
        BizEventMessageVO bizEventMessage = JSONObject.parseObject(JSON.toJSONString(parsedMap),BizEventMessageVO.class);
        bizEventMessage.setBizEventType(mappingConfig.getEventType());
        return bizEventMessage;
    }

    /**
     * 处理扩展参数的属性名称，即将ext.开头的参数放到扩展参数的结构体中
     * @param parsedMap 处理后的消息体信息
     */
    private void processExtParam(Map<String,Object> parsedMap){
        Map<String,Object> extParam = new HashMap<>();
        for (Map.Entry<String,Object> entry : parsedMap.entrySet()){
            String key = entry.getKey();
            Object value = entry.getValue();
            if(!isExtParam(key)){
                continue;
            }
            extParam.put(getExtKey(key),value);
        }

        parsedMap.put(EventConfigConstants.EXT_PARAM_KEY,extParam);
    }

    /**
     * 获取扩展参数的属性名称，如ext.userId则返回userId
     * @param key 扩展参数的配置信息
     * @return 属性名称
     */
    private String getExtKey(String key){
        String[] keys = StringUtils.split(key,EventConfigConstants.FIELD_JOIN_CHAR);
        return keys[EventConfigConstants.EXT_PARAM_INDEX];
    }

    /**
     * 判断是否为扩展参数，如ext.userId则为扩展参数，即ext.开头的参数
     * @param key 参数名称
     * @return 是否为扩展参数，true为扩展参数
     */
    private boolean isExtParam(String key){
        return key.startsWith(EventConfigConstants.EXT_PARAM_PREFIX);
    }



    private void messageDataProcess(MqEventFieldMappingConfig mappingConfig, Map<String,Object> valueMap){
        List<FieldDetail> details = mappingConfig.getFieldList();
        for (FieldDetail detail : details){
            //不需要进行数据处理直接跳过
            if(!isNeedDataProcess(detail)){
                continue;
            }

            fieldDataProcess(detail,valueMap);
        }
    }

    /**
     * 判断该字段配置信息是否需要进行数据处理
     * @param detail 字段的映射配置信息
     * @return 是否需要进行数据处理，true需要处理
     */
    private boolean isNeedDataProcess(FieldDetail detail){
        return !CollectionUtils.isEmpty(detail.getProcessList());
    }

    /**
     * 处理某个字段的数据处理
     * @param detail 字段的映射配置信息
     * @param valueMap 消息体信息
     * @return 处理后的消息体信息
     */
    private void fieldDataProcess(FieldDetail detail, Map<String,Object> valueMap){
        List<FieldConfig> fieldConfigs = detail.getProcessList();
        for (FieldConfig config : fieldConfigs){
            doFieldConfigProcess(detail,config,valueMap);
        }
    }

    /**
     * 处理某个字段配置的某个数据处理器信息
     * @param detail 字段映射配置信息
     * @param config 数据处理器配置信息
     * @param valueMap 消息体信息
     * @return 处理后的消息体信息
     */
    private void doFieldConfigProcess(FieldDetail detail, FieldConfig config, Map<String,Object> valueMap){
        DataProcessor processor = dataProcessorStrategy.getProcessor(config.getProcess());
        if(Objects.isNull(processor)){
            return;
        }

        Field field = processor.process(detail,config,valueMap);
        if(Objects.nonNull(field)){
            valueMap.put(field.getName(),field.getValue());
        }
    }

    /**
     * 将消息的信息解析为配置的映射信息
     * @param mappingConfig 消息体字段映射的配置信息
     * @param messageBody 消息体信息
     * @return 解析后的消息体参数信息
     */
    private Map<String,Object> messageParse(MqEventFieldMappingConfig mappingConfig, String messageBody){
        DataParser parser = dataParserStrategy.getParser(mappingConfig.getParse());
        if(Objects.isNull(parser)){
            log.warn("can't find data parser factory, mappingConfig={}",mappingConfig);
            return Collections.emptyMap();
        }

        return parser.doParse(messageBody,mappingConfig);
    }

    private String messageProcessCheck(String topic, String consumer, String messageBody){
        if(StringUtils.isEmpty(topic)){
            return "topic不能为空";
        }
        if(StringUtils.isEmpty(consumer)){
            return "consumer不能为空";
        }
        if(StringUtils.isEmpty(messageBody)){
            return "messageBody不能为空";
        }

        return null;
    }
}
