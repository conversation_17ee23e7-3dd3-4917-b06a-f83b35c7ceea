package com.xftech.cdp.infra.utils;

import com.xftech.base.common.util.LogUtil;
import com.xftech.base.log.IUdpLogger;
import sun.misc.BASE64Decoder;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;

public class AesUtil {
    private static final IUdpLogger logger = LogUtil.getLogger(AesUtil.class);

    public static String mobileDecrypt(String sSrc, String sKey) {
        if (sSrc == null || sSrc.length() <= 11) {
            return sSrc;
        }
        return decrypt(sSrc, sKey);
    }

    // 解密
    public static String decrypt(String sSrc, String sKey) {
        try {
            // 判断Key是否正确
            if (sKey == null) {
                logger.warn("Key为空null");
                return null;
            }
            // 判断Key是否为16位
            if (sKey.length() != 16) {
                logger.warn("Key长度不是16位");
                return null;
            }
            byte[] raw = sKey.getBytes("utf-8");
            SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, skeySpec);
            //先用base64解密
            byte[] encrypted1 = new BASE64Decoder().decodeBuffer(sSrc);
            try {
                byte[] original = cipher.doFinal(encrypted1);
                String originalString = new String(original, "utf-8");
                return originalString;
            } catch (Exception e) {
                logger.warn("解密异常", e);
                return null;
            }
        } catch (Exception ex) {
            logger.warn("解密异常", ex);
            return null;
        }
    }
}
