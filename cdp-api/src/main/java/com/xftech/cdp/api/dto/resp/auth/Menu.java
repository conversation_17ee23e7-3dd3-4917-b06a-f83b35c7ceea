package com.xftech.cdp.api.dto.resp.auth;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR> wanghu
 * @since : 2022/8/2 15:23:35
 */
@Setter
@Getter
public class Menu {


    /**
     * 2,
     */
    @JsonProperty("id")
    @JSONField(name = "id")
    private Long id;

    /**
     * "系统管理",
     */
    @JsonProperty("name")
    @JSONField(name = "name")
    private String name;

    /**
     * 0,
     */
    @JsonProperty("parent_id")
    @JSONField(name = "parent_id")
    private Long parentId;

    /**
     * "system",
     */
    @JsonProperty("url")
    @JSONField(name = "url")
    private String url;

    /**
     * 0,
     */
    @JsonProperty("order")
    @JSONField(name = "order")
    private Integer order;

    /**
     * 0,
     */
    @JsonProperty("is_hide")
    @JSONField(name = "is_hide")
    private Integer isHide;

    /**
     * "系统管理",
     */
    @JsonProperty("label")
    @JSONField(name = "label")
    private String label;

    /**
     * 2,
     */
    @JsonProperty("value")
    @JSONField(name = "value")
    private Integer value;

    @JsonProperty("children")
    @JSONField(name = "children")
    private List<Menu> children;

}
