package com.xftech.cdp.domain.event.processor;

import com.xftech.cdp.domain.event.model.config.FieldConfig;
import com.xftech.cdp.domain.event.model.dto.Field;
import com.xftech.cdp.domain.event.model.dto.FieldDetail;

import java.util.Map;

/**
 * 数据处理器的接口定义
 * <AUTHOR>
 * @version $ DataProcessor, v 0.1 2024/11/18 10:48 snail Exp $
 */
public interface DataProcessor {
    /**
     * 将某个字段按照配置的处理器进行数据处理
     * @param detail 字段映射配置信息
     * @param fieldConfig 处理器处理器配置信息
     * @param values 消息体信息
     * @return 处理后的字段信息
     */
    Field process(FieldDetail detail, FieldConfig fieldConfig, Map<String,Object> values);
}
