package com.xftech.cdp.infra.client.ads.model.resp;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-06-08
 */
@Data
public class UserDispatchResp {

    private String label;

    private List<Params> params;

    @Data
    public static class Params {
        /**
         * 用户id
         */
        private Long appUserId;
        /**
         * 触达次数
         */
        @JsonProperty("dispatch_cnt")
        @JSONField(name = "dispatch_cnt")
        private Integer dispatchCnt;
    }
}
