package com.xftech.cdp.adapter.strategy.label.handler;

import com.xftech.cdp.adapter.strategy.label.LabelHandler;
import com.xftech.cdp.adapter.strategy.label.QueryLabelRequest;
import com.xftech.cdp.infra.aviator.util.DateTimeUtil;
import com.xftech.cdp.infra.client.ads.model.resp.AdsLabelResp;
import com.xftech.cdp.infra.client.increaseamt.RcsProviderClient;
import com.xftech.cdp.infra.client.increaseamt.model.req.AccessControlQueryReq;
import com.xftech.cdp.infra.client.increaseamt.model.req.RequestHeader;
import com.xftech.cdp.infra.client.increaseamt.model.resp.AccessControlResp;
import com.xftech.cdp.infra.client.increaseamt.model.resp.Response;
import com.xftech.cdp.infra.client.usercenter.CisService;
import com.xftech.cdp.infra.client.usercenter.model.BaseCisResp;
import com.xftech.cdp.infra.client.usercenter.model.RegisterInfoByUserNo;
import com.xftech.cdp.infra.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @version $ BanDistributionLabelDiversionLabel, v 0.1 2025/1/13 17:01 tianshuo.qiu Exp $
 */
@Slf4j
@Component
public class BanDistributionLabelDiversionLabelHandler implements LabelHandler {
    @Autowired
    private RcsProviderClient rcsProviderClient;
    @Autowired
    private CisService cisService;

    @Override
    public String getLabel() {
        return "ban_distribution_label_diversion_label";
    }

    @Override
    public AdsLabelResp execute(String label, QueryLabelRequest batchAdsLabelVO) {
        List<AdsLabelResp.Param> result = new ArrayList<>();
        for (Long userNo : batchAdsLabelVO.getUserNoList()) {
            BaseCisResp<RegisterInfoByUserNo.RespDto> respDtoBaseCisResp = cisService.queryRegisterInfoByUserNo(userNo);
            log.info("贷超实时标签查询cis接口req:{},resp:{}", userNo, JsonUtil.toJson(respDtoBaseCisResp));
            if (respDtoBaseCisResp != null && respDtoBaseCisResp.getData() != null
                    && StringUtils.isNotBlank(respDtoBaseCisResp.getData().getCustNo()) &&
                    StringUtils.isNotBlank(respDtoBaseCisResp.getData().getCustNo()) && StringUtils.isNotBlank(respDtoBaseCisResp.getData().getIdCardNumber())) {
                RegisterInfoByUserNo.RespDto userInfo = respDtoBaseCisResp.getData();
                String orderNo = String.format("%s_%s", DateTimeUtil.formatDateToStr(new Date(), null), userInfo.getUserNo());
                RequestHeader requestHeader = rcsProviderClient.buildAccessControlHeader(orderNo, userInfo.getApp(), userInfo.getSourceChannel());
                AccessControlQueryReq accessControlQueryReq = new AccessControlQueryReq(userInfo.getIdCardNumber(), userInfo.getCustNo(), null);
                Response<AccessControlResp> accessControlRespResponse = rcsProviderClient.accessControlQuery(requestHeader, accessControlQueryReq);
                // 风险禁申标签diversion_label属于DL01/appDL01
                if (accessControlRespResponse != null && accessControlRespResponse.getData() != null) {
                    AccessControlResp data = accessControlRespResponse.getData();
                    if (Objects.equals(data.getDiversion_label(), "DL01")||Objects.equals(data.getDiversion_label(), "appDL01")) {
                        result.add(new AdsLabelResp.Param(userNo, null, data.getDiversion_label()));
                    } else {
                        result.add(new AdsLabelResp.Param(userNo, null, data.getDiversion_label()));
                    }
                }
            } else {
                result.add(new AdsLabelResp.Param(userNo, null, null));
            }
        }
        return new AdsLabelResp(label, result);
    }
}