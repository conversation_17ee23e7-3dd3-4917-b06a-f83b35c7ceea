package com.xftech.cdp.domain.strategy.service.impl;

import com.xftech.cdp.domain.strategy.service.StrategyGroupService;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.function.BiPredicate;
import java.util.stream.Collectors;

/**
 * @<NAME_EMAIL>
 * @date 2023/4/23 10:38
 */
@Slf4j
@Service
public class StrategyGroupServiceImpl implements StrategyGroupService {

    /**
     * 策略分组匹配
     *
     * @param bizKey        场景值
     * @param matchFunction 分组匹配函数
     * @param list          人群明细集合
     * @return 符合分组的配置的人群
     */
    @Override
    public List<CrowdDetailDo> matchGroupRule(String bizKey, BiPredicate<String, Integer> matchFunction, List<CrowdDetailDo> list) {
        if (Objects.isNull(matchFunction)) {
            return list;
        }
        List<CrowdDetailDo> matchList = list.stream().filter(item -> matchCondition(matchFunction, item)).collect(Collectors.toList());
        log.info("策略分组匹配，匹配人数：{}，符合人数：{}", list.size(), matchList.size());
        return matchList;
    }

    /**
     * 使用 分组匹配函数 判断当前用户是否归属于当前分组
     *
     * @param matchFunction 分组匹配函数
     * @param crowdDetail   人群明细集合
     * @return 符合分组的配置的人群
     */
    private boolean matchCondition(BiPredicate<String, Integer> matchFunction, CrowdDetailDo crowdDetail) {
        try {
            return matchFunction.test(crowdDetail.getAbNum(), crowdDetail.getAppUserIdLast2());
        } catch (Exception e) {
            return false;
        }
    }
}
