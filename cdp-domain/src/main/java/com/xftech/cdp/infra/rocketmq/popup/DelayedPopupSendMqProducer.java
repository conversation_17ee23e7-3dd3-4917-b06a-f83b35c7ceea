package com.xftech.cdp.infra.rocketmq.popup;

import javax.annotation.Resource;

import com.xinfei.xfframework.common.JsonUtil;
import com.xinfei.xfframework.common.starter.mq.MqTemplate;

import com.xftech.cdp.feign.service.PrdOccCoreService;
import com.xftech.cdp.infra.config.ApolloUtil;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class DelayedPopupSendMqProducer {

    private static final String DELAYED_POPUP_TOPIC = "DELAYED_POPUP_TOPIC";
    private static final String DELAYED_POPUP_DEFAULT_TOPIC = "tp_xyf_cdp_delayed_popup";

    @Resource
    private MqTemplate mqTemplate;
    @Resource
    private PrdOccCoreService prdOccCoreService;

    public boolean asyncSend(DelayedPopupMsg delayedPopupMsg) {
        try {
            boolean popValidCheck = checkPopValid(delayedPopupMsg);
            if (!popValidCheck) {
                log.warn("DelayedPopupSendMqProducer asyncSend popValidCheck=false delayedPopupMsg={}", delayedPopupMsg);
                return false;
            }

            String message = JsonUtil.toJson(delayedPopupMsg);
            log.info("DelayedPopupSendMqProducer asyncSend message:{}", message);
            mqTemplate.syncSend(ApolloUtil.getAppProperty(DELAYED_POPUP_TOPIC, DELAYED_POPUP_DEFAULT_TOPIC), message);
            return true;
        } catch (Exception e) {
            log.error("DelayedPopupSendMqProducer asyncSend error delayedPopupMsg={}", delayedPopupMsg, e);
        }
        return false;
    }

    public boolean checkPopValid(DelayedPopupMsg delayedPopupMsg) {
        if (delayedPopupMsg == null || StringUtils.isBlank(delayedPopupMsg.getPopupId()) || delayedPopupMsg.getExpiredHours() == null) {
            return false;
        }

        try {
            return prdOccCoreService.popupCheck(Integer.parseInt(delayedPopupMsg.getPopupId()));
        } catch (Exception e) {
            log.error("DelayedPopupSendMqProducer checkPopValid error", e);
        }
        return true;
    }

}