/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.adapter.label;

import com.google.common.base.Stopwatch;
import com.xftech.cdp.application.LabelHandler;
import com.xftech.cdp.infra.constant.XxlJobConstants;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version $ LabelJob, v 0.1 2024/6/19 11:45 lingang.han Exp $
 */

@Slf4j
@Service
@AllArgsConstructor
public class LabelJob {

    private final LabelHandler labelHandler;


    @XxlJob(XxlJobConstants.LABEL_SYNC_TASK)
    public ReturnT<String> flowStatusChanged(String param) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            log.info("Xxl-Job 同步元数据标签开始执行");
            labelHandler.syncMetaLabel();
        } finally {
            stopwatch.stop();
            log.info("Xxl-Job 同步元数据标签执行结束, 总计耗时:{}", stopwatch.elapsed(TimeUnit.SECONDS));
        }
        return ReturnT.SUCCESS;
    }
}