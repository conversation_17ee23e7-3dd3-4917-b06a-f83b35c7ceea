package com.xftech.cdp.domain.strategy.service;

import com.xftech.cdp.api.dto.base.Response;
import com.xftech.cdp.api.dto.req.external.DispatchRecordQueryReq;
import com.xftech.cdp.api.dto.req.external.ExistDispatchRecordReq;
import com.xftech.cdp.api.dto.req.external.IncreaseAmtCallbakRequest;
import com.xftech.cdp.api.dto.req.external.TeleImportResultReq;
import com.xftech.cdp.api.dto.resp.external.DispatchRecordQueryResp;
import com.xftech.cdp.api.dto.resp.external.ExistDispatchRecordResp;
import com.xftech.cdp.domain.dispatch.dto.DispatchDto;
import com.xftech.cdp.domain.flowctrl.model.dto.UserDispatchIndexDto;
import com.xftech.cdp.domain.strategy.model.enums.StrategyRulerEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyMarketChannelEnum;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPushBatchDo;
import com.xftech.cdp.infra.repository.cdp.flowctrl.po.FlowCtrlDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.EventPushBatchDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.UserDispatchDetailDo;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 用户触达明细相关功能
 *
 * @<NAME_EMAIL>
 * @date 2023/3/28 9:22
 */
public interface UserDispatchDetailService {

    /**
     * 获取用户流控指标
     *
     * @param tableNameNo 当前下发明细用户所有表序号
     * @param triple      left-策略ID Middle-渠道类型 Right-流控规则
     * @param userIdList  用户ID集合
     * @return 用户流控指标
     */
    List<UserDispatchIndexDto> getUserIndex(String tableNameNo, Triple<Long, Integer, FlowCtrlDo> triple, List<Long> userIdList, List<Integer> statusList);


    /**
     * 新版用户流控指标查询接口
     *
     * @param tableNameNo 当前下发明细用户所有表序号
     * @param triple      left-策略ID Middle-渠道类型 Right-流控规则
     * @param userIdList  用户ID集合
     * @return 用户流控指标
     */
    List<UserDispatchIndexDto> getUserIndexNew(String tableNameNo, Triple<Long, Integer, FlowCtrlDo> triple, List<Long> userIdList, List<Integer> statusList);

//    /**
//     * 批量报销下发明细
//     *
//     * @param tableNameNo 表序号
//     * @param list        用户集合
//     */
//    void saveBatch(String tableNameNo, List<UserDispatchDetailDo> list);

    /**
     * 保存策略下发明细
     *
     * @param dispatchDto   初始化参数
     * @param batchNum   批次号
     * @param app        app
     * @param innerApp   innerApp
     * @param list       批量参数
     * @param detailList 人群明细
     * @param resp       接口响应
     */
    <T> List<UserDispatchDetailDo> saveDispatchDetail(DispatchDto dispatchDto, CrowdPushBatchDo crowdPushBatchDo, String batchNum, String app, String innerApp, List<T> list, List<CrowdDetailDo> detailList, Pair<String, String> resp);


    void saveDispatchDetail(String detailTableNo, StrategyRulerEnum strategyRulerEnum, UserDispatchDetailDo dispatchDetailDo, CrowdPushBatchDo crowdPushBatchDo, CrowdDetailDo crowdDetailDo);

    EventPushBatchDo saveEventDispatchDetail(DispatchDto reach, EventPushBatchDo eventPushBatch, UserDispatchDetailDo dispatchDetail, String batchNum, CrowdDetailDo crowdDetail, Triple<String, String, String> resp);

    Boolean teleCall(TeleImportResultReq req, StrategyMarketChannelEnum strategyMarketChannelEnum);
    /**
     * 查询指定批次的用户下发明细
     *
     * @param tableNameNo   明细表序号
     * @param marketChannel 渠道
     * @param batchNum      批次号
     * @return 用户下发明细
     */
    List<CrowdDetailDo> selectListByBatchNo(String tableNameNo, Integer marketChannel, String batchNum);

    /**
     * 更新用户下发结果
     *
     * @param req 推送记录
     * @return 更新结果
     */
    Boolean teleImportResult(TeleImportResultReq req);

    /**
     * 统计策略成功/下发数
     *
     * @param tableNameNo 表序号
     * @param strategyId  策略ID
     * @return 数量
     */
    ImmutablePair<Integer, Integer> count(String tableNameNo, Long strategyId);

    /**
     * 统计策略成功数
     *
     * @param tableNameNo 表序号
     * @param strategyId  策略ID
     * @return 数量
     */
    Integer successCount(String tableNameNo, Long strategyId);

    /**
     * 统计策略成功数
     *
     * @param tableNameNo 表序号
     * @param strategyId  策略ID
     * @return 数量
     */
    Integer totalCount(String tableNameNo, Long strategyId);

    /**
     * 更新超过回执推送时间，用户状态为失败
     */
    void dispatchFailUserUpdate();

    DispatchRecordQueryResp hasDispatchRecord(DispatchRecordQueryReq args);

    boolean updateById(String tableNameNo, UserDispatchDetailDo userDispatchDetailDo);
    int batchUpdateDispatchFail(String tableNo, List<Long> idList);

    boolean increaseAmtNotify(IncreaseAmtCallbakRequest request);

    /**
     * 24小时内是否存在指定策略下发记录
     * @param args req
     * @return result
     */
    ExistDispatchRecordResp existDispatchRecord(ExistDispatchRecordReq args);
}
