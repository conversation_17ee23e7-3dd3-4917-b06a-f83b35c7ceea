package com.xftech.cdp.strategy;

import com.xftech.cdp.BaseTest;
import com.xftech.cdp.domain.strategy.repository.StrategyCrowdPackRepository;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyCrowdPackDo;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * @<NAME_EMAIL>
 */
public class StrategyCrowdPackRepositoryTest extends BaseTest {
    protected static final Logger LOGGER = LoggerFactory.getLogger(StrategyCrowdPackRepositoryTest.class);

    @Autowired
    StrategyCrowdPackRepository strategyCrowdPackRepository;

    @Test
    public void findByStrategyId() {
        List<StrategyCrowdPackDo> strategyCrowdPackDos = strategyCrowdPackRepository.selectByStrategyId(47L);
        LOGGER.info("{}", strategyCrowdPackDos);
    }

    @Test
    public void findCrowdPackIdByStrategyId() {
        List<Long> strategyIdList = new ArrayList<>();
        strategyIdList.add(47L);
        strategyIdList.add(50L);
        List<StrategyCrowdPackDo> strategyCrowdPackDos = strategyCrowdPackRepository.selectCrowdPackIdByStrategyId(strategyIdList);
        LOGGER.info("{}", strategyCrowdPackDos);
    }

    @Test
    public void deleteByStrategyId() {
        boolean flag = strategyCrowdPackRepository.deleteByStrategyId(53L);
        LOGGER.info("{}", flag);
    }

    @Test
    public void saveBatch() {
        List<StrategyCrowdPackDo> strategyCrowdPackDos = new ArrayList<>();
        StrategyCrowdPackDo strategyCrowdPackDo = new StrategyCrowdPackDo();
        strategyCrowdPackDo.setStrategyId(53L);
        strategyCrowdPackDo.setCrowdPackId(2L);
        strategyCrowdPackDo.setCreatedOp("hhh");
        strategyCrowdPackDo.setUpdatedOp("hhh");
        strategyCrowdPackDos.add(strategyCrowdPackDo);
        boolean insertFlag = strategyCrowdPackRepository.saveBatch(strategyCrowdPackDos);
        System.out.println(insertFlag);
    }
}