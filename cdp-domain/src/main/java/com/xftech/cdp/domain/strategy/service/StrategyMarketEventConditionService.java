package com.xftech.cdp.domain.strategy.service;

import com.xftech.cdp.infra.rabbitmq.vo.BizEventMessageVO;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventVO;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketEventConditionDo;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * @<NAME_EMAIL>
 */
public interface StrategyMarketEventConditionService {

    List<StrategyMarketEventConditionDo> getByStrategyId(Long strategyId);

    Map<String, List<StrategyMarketEventConditionDo>> getStringToListByStrategyId(Long strategyId);

    /**
     * 是否可营销
     * @param utmSource utm类型
     * @return 0：不可营销 1：可营销
     */
    Integer isMarketable(Long strategyId, String utmSource, String labelName, LocalDateTime registerTime);

    Integer isMarketableBatch(Collection<Long> strategyIdList, String utmSource, String labelName, LocalDateTime registerTime);

    /**
     * 参数补全-用户信息补全
     */
    void userInfoComplete(BizEventVO bizEventVO, BizEventMessageVO bizEventMessageVO);
    /**
     * 参数补全
     */
    void paramComplete(BizEventVO bizEventVO, BizEventMessageVO bizEventMessageVO);

    /**
     * 随机数补全
     */
    void abNumComplete(BizEventVO bizEventVO);
}
