/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.infra.rocketmq;

import com.alibaba.fastjson.JSONObject;
import com.xftech.cdp.api.dto.req.external.PushReportReq;
import com.xftech.cdp.domain.mq.MqConsumeService;
import com.xinfei.xfframework.common.starter.mq.MqConsumerListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * push回执消息
 *
 * <AUTHOR>
 * @version $ pushCallRocketMqConsumer, v 0.1 2024/02/22 10:10 lhan Exp $
 */

@Component
@RocketMQMessageListener(consumerGroup = "cg_xyf_cdp_tp_app_push_report_callback_tg_xyf_cdp", topic = "tp_app_push_report_callback", selectorExpression = "tg_xyf_cdp", consumeMode = ConsumeMode.CONCURRENTLY)
@Slf4j
public class PushCallRocketMqConsumer extends MqConsumerListener<String> {
    @Autowired
    private MqConsumeService mqConsumeService;

    @Override
    protected void doMessage(String s, String s1, MessageExt messageExt) {
        log.info("pushCallbackRocketMqConsumer receive message topic={},messageid={},body={}", s, messageExt.getMsgId(), s1);
        try {
            PushReportReq pushReportReq = JSONObject.parseObject(s1, PushReportReq.class);
            mqConsumeService.pushReportProcess(pushReportReq);
        } catch (Exception e) {
            log.error("pushCallbackRocketMqConsumer consumer error, messageid={},body={}", messageExt.getMsgId(), s1, e);
        }
    }
}