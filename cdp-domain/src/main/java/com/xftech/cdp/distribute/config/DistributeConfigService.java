/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2025. All Rights Reserved.
 */

package com.xftech.cdp.distribute.config;

import com.alibaba.fastjson2.JSON;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;

import static cn.dev33.satoken.SaManager.log;

/**
 * //    @Value("#{'${crowd.alarm.prod.atMobile:13046631879,13681832724,18321308699}'.split(',')}")
 * <AUTHOR>
 * @version $ DistributeConfig, v 0.1 2025/5/9 11:28 xu.fan Exp $
 */
@Getter
@RefreshScope
@Component
public class DistributeConfigService {

    /**
     * 分页大小配置
     */
    @Value("${distribute.task.page.size:100}")
    private Integer crowdSliceBatchSize;

    /**
     * OSS特殊行读取长度配置:
     * 1、每个文件的第一行标注列名
     * 2、向后补充不完整行
     */
    @Value("${oss.special.line.read.length:1000}")
    private Long ossSpecialLineReadLength;


    private BucketConfig ossBucketConfig;

    @Value("${oss.endpoint.buket.config}")
    public void setOssBucketConfig(String ossConfigStr) {
        if(StringUtils.isEmpty(ossConfigStr)) {
            log.error("setOssBucketConfig is empty error");
            return;
        }
        ossBucketConfig = JSON.parseObject(ossConfigStr, BucketConfig.class);
    }


}
