package com.xftech.cdp.domain.crowd.factory.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.xftech.cdp.domain.ads.repository.AdsOprtAutoMessageDfRepository;
import com.xftech.cdp.domain.crowd.exception.CrowdException;
import com.xftech.cdp.domain.crowd.factory.AbsCrowdOptService;
import com.xftech.cdp.domain.crowd.model.dispatch.CrowdConstant;
import com.xftech.cdp.domain.crowd.model.dispatch.CrowdContext;
import com.xftech.cdp.domain.crowd.model.dispatch.CrowdWereHouse;
import com.xftech.cdp.domain.crowd.model.dispatch.InvokePage;
import com.xftech.cdp.domain.crowd.repository.AdsLabelMonitorDfRepository;
import com.xftech.cdp.domain.crowd.repository.AdsUserLabelDetailInfoDfRepository;
import com.xftech.cdp.infra.config.Config;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPackDo;
import com.xftech.cdp.infra.utils.AesUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.MutablePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Service
public class AutoMessOptService extends AbsCrowdOptService {
    private static final String ADS_OPRT_AUTO_MESSAGE_DF_SQL = "select user_id as app_user_id,app,inner_app,mobile, ab_num, app_user_id_last2 from ads_oprt_auto_message_df where (";

    @Autowired
    private AdsLabelMonitorDfRepository adsLabelMonitorDfRepository;
    @Autowired
    private AdsOprtAutoMessageDfRepository adsOprtAutoMessageDfRepository;
    @Autowired
    private AdsUserLabelDetailInfoDfRepository adsUserLabelDetailInfoDfRepository;
    @Autowired
    private Config config;

    @Override
    public void preHandler(CrowdContext crowdContext) {
        LocalDateTime dateTime = LocalDateTimeUtil.beginOfDay(LocalDateTime.now());
        Boolean existAutoMessage = adsLabelMonitorDfRepository.selectExistByDataDate(dateTime, CrowdConstant.AUTO_MESSAGE);
        if (!existAutoMessage) {
            crowdContext.setFailReason("数仓数据尚未初始化");
            throw new CrowdException("人群刷行失败，数仓数据尚未初始化");
        }
        Long todayCrowdMaxId = adsOprtAutoMessageDfRepository.selectAutoMessageMaxAppUserId(dateTime);
        if (todayCrowdMaxId == null) {
            crowdContext.setFailReason("数仓数据尚未初始化");
            throw new CrowdException("人群刷行失败，数仓数据尚未初始化");
        }
        Long todayCrowdMinId = adsOprtAutoMessageDfRepository.selectAutoMessageMinAppUserId(dateTime);
        int rockPageSize = crowdContext.isRunAsFixedNumberPage() ? crowdConfig.getRockPageSizeNew() : crowdConfig.getRockPageSize();
        log.info("跑人群包数据, 是否使用新的分页方式:{}, 分页大小:{}", crowdContext.isRunAsFixedNumberPage(), rockPageSize);
        crowdContext.subLevel(todayCrowdMinId, todayCrowdMaxId,
                crowdConfig.getTaskBatchCount(), rockPageSize);
    }

    @Override
    public CrowdContext initContext(boolean isRunAsFixedNumberPage, CrowdPackDo crowdPack, AbsCrowdOptService crowdOpt) {
        return CrowdContext.init(isRunAsFixedNumberPage, crowdPack, crowdOpt);
    }

    @Override
    public void organizeSql(CrowdContext crowdContext) {
        MutablePair<StringBuilder, StringBuilder> pair = new MutablePair<>();
        StringBuilder sql = new StringBuilder(String.format(ADS_OPRT_AUTO_MESSAGE_DF_SQL, config.getAdsMobileAesKey()));
        sql.append(" sms_flg = ").append(config.getCrowdPackSmsFlg().get(crowdContext.getCrowdPack().getId()).toString());
        pair.setLeft(sql);
        crowdContext.setLabelSqlPair(pair);
    }

    @Override
    public void andPage(StringBuilder sql, InvokePage invokePage) {
        sql.append(" ) and user_id > ").append(invokePage.getAppUserId()).append(" and user_id <= ").append(invokePage.getMaxAppUserId()).append(" order by user_id limit ").append(invokePage.getPageSize());
    }

    public void andPageNew(StringBuilder sql, InvokePage invokePage) {
        sql.append(" ) and user_id > ").append(invokePage.getAppUserId()).append(" and user_id <= ").append(invokePage.getMaxAppUserId());
    }

    @Override
    public List<CrowdWereHouse> queryAdsDataInfo(String sql, boolean isRunAsFixedNumberPage) {

        return adsOprtAutoMessageDfRepository.queryOprtAutoMessageDf(sql);
    }
}
