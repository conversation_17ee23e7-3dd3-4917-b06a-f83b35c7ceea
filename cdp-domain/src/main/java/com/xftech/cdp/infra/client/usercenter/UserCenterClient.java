package com.xftech.cdp.infra.client.usercenter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xftech.cdp.infra.client.Signer;
import com.xftech.cdp.infra.client.usercenter.config.CisConfig;
import com.xftech.cdp.infra.client.usercenter.config.UserCenterConfig;
import com.xftech.cdp.infra.client.usercenter.model.*;
import com.xftech.cdp.infra.constant.Constants;
import com.xftech.cdp.infra.exception.InfraException;
import com.xftech.cdp.infra.utils.HttpClientUtil;
import com.xftech.cdp.infra.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/2/16
 */
@Slf4j
@Component
public class UserCenterClient {
    @Resource
    private UserCenterConfig userCenterConfig;
    @Resource
    private Signer signer;
    @Resource
    private HttpClientUtil httpClientUtil;
    @Resource
    private CisService cisService;

    public UserInfoResp getUserByUserId(Long userid){
        BaseCisResp<RegisterInfoByUserNo.RespDto> baseCisResp = cisService.queryRegisterInfoByUserNo(userid);
        if (baseCisResp == null || !baseCisResp.isCodeSucceed() || baseCisResp.getData() == null) {
            throw new InfraException("从cis获取user数据错误");
        }
        return baseCisResp.getData().convert();
    }

    public List<UserInfoResp> queryBatchUserByUserNo(List<Long> userid){
        BaseCisResp<List<RegisterInfoByUserNo.RespDto>> listBaseCisResp = cisService.queryBatchUserByUserNo(userid);
        if (listBaseCisResp == null || !listBaseCisResp.isCodeSucceed() || listBaseCisResp.getData() == null) {
            throw new InfraException("从cis获取user数据错误");
        }
        List<RegisterInfoByUserNo.RespDto> respDtos = listBaseCisResp.getData();
        if (CollectionUtils.isEmpty(respDtos)) {
            return Collections.emptyList();
        }
        return respDtos.stream()
                .map(RegisterInfoByUserNo.RespDto::convert)
                .collect(Collectors.toList());
    }

    public UserInfoResp getUserByMobile(String mobile, String app) {
        if(cisService.isQueryUserInfoByCis()) {
            log.info("getUserByMobile from cis, mobile:{}, app:{}", mobile, app);
            UserNoByMobileAndApp.Req req = new UserNoByMobileAndApp.Req(mobile, app);
            BaseCisResp<UserNoByMobileAndApp.RespDto> respDtoBaseCisResp = cisService.queryUserNoByMobileAndApp(req);
            if (respDtoBaseCisResp == null || !respDtoBaseCisResp.isCodeSucceed()) {
                throw new InfraException("从cis获取user数据错误");
            }
            if (respDtoBaseCisResp.getData() == null || respDtoBaseCisResp.getData()
                    .getUserNo() == null) {
                throw new InfraException("从cis获取user数据错误");
            }
            BaseCisResp<RegisterInfoByUserNo.RespDto> baseCisResp = cisService.queryRegisterInfoByUserNo(respDtoBaseCisResp.getData().getUserNo());
            if (baseCisResp == null || !baseCisResp.isCodeSucceed()) {
                throw new InfraException("从cis获取user数据错误");
            }
            if (baseCisResp.getData() == null || respDtoBaseCisResp.getData()
                    .getUserNo() == null) {
                throw new InfraException("从cis获取user数据错误");
            }
            return baseCisResp.getData().convert();
        }

        UserInfoReq userInfoReq = new UserInfoReq(mobile, app);
        String signKey = signer.sign(userCenterConfig.getUserByMobile(), JSON.toJSONString(userInfoReq), userCenterConfig.getKey());
        BasePhpReq<UserInfoReq> basePhpReq = new BasePhpReq<>();
        basePhpReq.setArgs(userInfoReq);
        basePhpReq.setUa(Constants.APP_NAME);
        basePhpReq.setSign(signKey);
        String s = request(basePhpReq, userCenterConfig.getUserByMobile());
//        String s = httpClientUtil.postJson(userCenterConfig.getPhpUserHost() + userCenterConfig.getUserByMobile(), basePhpReq);
        UserInfoAllResp userInfoAllResp = JSON.parseObject(s, UserInfoAllResp.class);
        if (userInfoAllResp == null || !userInfoAllResp.isSuccess()) {
            log.error("从php获取person数据错误 param:{}", JSON.toJSONString(userInfoReq));
            throw new InfraException("从php获取user数据错误");
        }
        return userInfoAllResp.getResponse();
    }

    public UserIdResp getUsersById(Long userId) {
        if (cisService.isQueryUserInfoByCis()) {
            // todo 目前只有单个查询，多个查询请使用批量接口
            BaseCisResp<MobileByUserNo.RespDto> baseCisResp = cisService.queryMobileByUserNo(userId);
            if (baseCisResp == null || !baseCisResp.isCodeSucceed()) {
                throw new InfraException("从cis获取user数据错误");
            }
            if (baseCisResp.getData() == null || baseCisResp.getData()
                    .getUserNo() == null) {
                throw new InfraException("从cis获取user数据错误");
            }
            UserIdResp userIdResp = new UserIdResp();
            userIdResp.setUserList(Arrays.asList(baseCisResp.getData().convert()));
            return userIdResp;
        }

        UserIdReq userIdReq = new UserIdReq(Arrays.asList(userId));
        String signKey = signer.sign(userCenterConfig.getBatchUserId(), JSON.toJSONString(userIdReq), userCenterConfig.getKey());
        BasePhpReq<UserIdReq> basePhpReq = new BasePhpReq<>();
        basePhpReq.setArgs(userIdReq);
        basePhpReq.setUa(Constants.APP_NAME);
        basePhpReq.setSign(signKey);
        String s = request(basePhpReq, userCenterConfig.getBatchUserId());
//        String s = httpClientUtil.postJson(userCenterConfig.getPhpUserHost() + userCenterConfig.getBatchUserId(), basePhpReq);
        UserIdAllResp userCertAllResp = JSON.parseObject(s, UserIdAllResp.class);
        if (userCertAllResp == null || !userCertAllResp.isSuccess()) {
            log.error("从php获取person数据错误 param:{}", JSON.toJSONString(userIdReq));
            throw new InfraException("从php获取user数据错误");
        }
        return userCertAllResp.getResponse();
    }

    public UserAbNumResp getAbNum(List<String> mobiles) {
        UserAbNumReq userAbNumReq = new UserAbNumReq(mobiles);
        String signKey = signer.sign(userCenterConfig.getAbNum(), JSON.toJSONString(userAbNumReq), userCenterConfig.getKey());
        BasePhpReq<UserAbNumReq> basePhpReq = new BasePhpReq<>();
        basePhpReq.setArgs(userAbNumReq);
        basePhpReq.setUa(Constants.APP_NAME);
        basePhpReq.setSign(signKey);
        String s = request(basePhpReq, userCenterConfig.getAbNum());
//        String s = httpClientUtil.postJson(userCenterConfig.getPhpUserHost() + userCenterConfig.getAbNum(), basePhpReq);
        UserAbNumAllResp userAbNumAllResp = JSON.parseObject(s, UserAbNumAllResp.class);
        if (userAbNumAllResp == null || !userAbNumAllResp.isSuccess()) {
            log.error("从php获取person数据错误 param:{}", JSON.toJSONString(userAbNumReq));
            throw new InfraException("从php获取user数据错误");
        }
        return userAbNumAllResp.getResponse();
    }

    private <T extends BasePhpReq> String request(T requester, String uri) {
        String url = userCenterConfig.getPhpUserHost() + uri;
        try {
            log.info("userCenter send，url：{}，requester：{}", url, JSONObject.toJSON(requester));
            long startTime = Instant.now().toEpochMilli();
            String result = httpClientUtil.postJson(url, requester);
            log.info("userCenter send，time：{}ms，return：{}", Instant.now().toEpochMilli() - startTime, result);
            return result;
        } catch (Exception e) {
            log.error("userCenter send error，url:{}，params:{}", url, JsonUtil.toJson(requester), e);
            throw e;
        }
    }
}
