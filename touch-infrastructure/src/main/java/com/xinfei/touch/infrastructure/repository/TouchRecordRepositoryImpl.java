package com.xinfei.touch.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xinfei.touch.domain.model.TouchRecord;
import com.xinfei.touch.domain.model.TouchStatus;
import com.xinfei.touch.domain.repository.TouchRecordRepository;
import com.xinfei.touch.infrastructure.repository.entity.TouchRecordEntity;
import com.xinfei.touch.infrastructure.repository.mapper.TouchRecordMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;

/**
 * 触达记录仓储实现
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
@RequiredArgsConstructor
public class TouchRecordRepositoryImpl implements TouchRecordRepository {
    
    private final TouchRecordMapper touchRecordMapper;
    
    @Override
    public boolean save(TouchRecord touchRecord) {
        TouchRecordEntity entity = convertToEntity(touchRecord);
        return touchRecordMapper.insert(entity) > 0;
    }
    
    @Override
    public TouchRecord findByRequestId(String requestId) {
        LambdaQueryWrapper<TouchRecordEntity> wrapper = Wrappers.<TouchRecordEntity>lambdaQuery()
                .eq(TouchRecordEntity::getRequestId, requestId);
        
        TouchRecordEntity entity = touchRecordMapper.selectOne(wrapper);
        return entity != null ? convertToDomain(entity) : null;
    }
    
    @Override
    public TouchRecord findByBatchNo(String batchNo) {
        LambdaQueryWrapper<TouchRecordEntity> wrapper = Wrappers.<TouchRecordEntity>lambdaQuery()
                .eq(TouchRecordEntity::getBatchNo, batchNo);
        
        TouchRecordEntity entity = touchRecordMapper.selectOne(wrapper);
        return entity != null ? convertToDomain(entity) : null;
    }
    
    @Override
    public int updateStatusByBatchNo(String batchNo, TouchStatus status, String errorCode, 
                                   String errorMessage, LocalDateTime callbackTime) {
        LambdaUpdateWrapper<TouchRecordEntity> wrapper = Wrappers.<TouchRecordEntity>lambdaUpdate()
                .eq(TouchRecordEntity::getBatchNo, batchNo)
                .set(TouchRecordEntity::getStatus, status.getCode())
                .set(TouchRecordEntity::getCallbackTime, callbackTime)
                .set(TouchRecordEntity::getUpdatedTime, LocalDateTime.now());
        
        if (errorCode != null) {
            wrapper.set(TouchRecordEntity::getErrorCode, errorCode);
        }
        if (errorMessage != null) {
            wrapper.set(TouchRecordEntity::getErrorMessage, errorMessage);
        }
        
        return touchRecordMapper.update(null, wrapper);
    }
    
    /**
     * 领域模型转实体
     */
    private TouchRecordEntity convertToEntity(TouchRecord touchRecord) {
        TouchRecordEntity entity = new TouchRecordEntity();
        entity.setRequestId(touchRecord.getRequestId());
        entity.setBatchNo(touchRecord.getBatchNo());
        entity.setTouchType(touchRecord.getTouchType().getCode());
        entity.setChannel(touchRecord.getChannel().getCode());
        entity.setStrategyId(touchRecord.getStrategyId());
        entity.setUserId(touchRecord.getUserId());
        entity.setBizEventType(touchRecord.getBizEventType());
        entity.setTemplateParams(touchRecord.getTemplateParams());
        entity.setStatus(touchRecord.getStatus().getCode());
        entity.setErrorCode(touchRecord.getErrorCode());
        entity.setErrorMessage(touchRecord.getErrorMessage());
        entity.setSendTime(touchRecord.getSendTime());
        entity.setCallbackTime(touchRecord.getCallbackTime());
        entity.setCreatedTime(touchRecord.getCreatedTime());
        entity.setUpdatedTime(touchRecord.getUpdatedTime());
        return entity;
    }
    
    /**
     * 实体转领域模型
     */
    private TouchRecord convertToDomain(TouchRecordEntity entity) {
        TouchRecord touchRecord = new TouchRecord();
        touchRecord.setId(entity.getId());
        touchRecord.setRequestId(entity.getRequestId());
        touchRecord.setBatchNo(entity.getBatchNo());
        touchRecord.setTouchType(com.xinfei.touch.domain.model.TouchType.fromCode(entity.getTouchType()));
        touchRecord.setChannel(com.xinfei.touch.domain.model.TouchChannel.fromCode(entity.getChannel()));
        touchRecord.setStrategyId(entity.getStrategyId());
        touchRecord.setUserId(entity.getUserId());
        touchRecord.setBizEventType(entity.getBizEventType());
        touchRecord.setTemplateParams(entity.getTemplateParams());
        touchRecord.setStatus(TouchStatus.fromCode(entity.getStatus()));
        touchRecord.setErrorCode(entity.getErrorCode());
        touchRecord.setErrorMessage(entity.getErrorMessage());
        touchRecord.setSendTime(entity.getSendTime());
        touchRecord.setCallbackTime(entity.getCallbackTime());
        touchRecord.setCreatedTime(entity.getCreatedTime());
        touchRecord.setUpdatedTime(entity.getUpdatedTime());
        return touchRecord;
    }
}
