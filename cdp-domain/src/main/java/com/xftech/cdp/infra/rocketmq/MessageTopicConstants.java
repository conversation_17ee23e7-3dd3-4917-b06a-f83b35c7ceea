/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.rocketmq;

/**
 * <AUTHOR>
 * @version $ MessageTopicConstants, v 0.1 2024/5/17 16:56 lingang.han Exp $
 */
/*
订阅的topic有tag的情况下，应该topic_tag
 */
public class MessageTopicConstants {
    public static final String APP_LOGIN_INCREASE_CREDIT = "tp_rcspt_risk_amount_change_message_tg_personal_api_fst_login_temp";

    public static final String CXH_REPAY_XYF01_INCREASE_CREDIT = "tp_rcspt_risk_amount_change_message_tg_cxh_repay_xyf01_increase_amount";

    public static final String RCSPT_INCREASE_CREDIT = "tp_rcspt_risk_amount_change_message";

    public static final String AI_PRONTO_CALLBACK = "tp_telemkt_name_ai_call_result";

    /**
     * 登录事件上报topic
     */
    public static final String LOGIN1_INCREASE_CREDIT = "tp_biz_report_Login1";
    /**
     * 启动事件上报topic
     */
    public static final String START1_INCREASE_CREDIT = "tp_biz_report_Start1";


    public static final String LENDTRADE_DERATE = "tp_lendtrade_derating";

    public static final String CALL_CENTER_AI_CALLBACK = "tp_call_center_ai_callback_tg_telemkt";
}