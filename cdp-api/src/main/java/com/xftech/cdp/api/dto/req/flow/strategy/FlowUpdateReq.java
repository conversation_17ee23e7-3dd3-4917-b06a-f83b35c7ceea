/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.req.flow.strategy;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xftech.cdp.api.dto.base.TimeFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * @version $ FlowStrategyUpdateReq, v 0.1 2023/12/19 17:34 yye.xu Exp $
 */

@Data
public class FlowUpdateReq {

    @ApiModelProperty(value = "画布编号")
    private String flowNo;

    @ApiModelProperty(value = "画布名称")
    private String name;

    @ApiModelProperty(value = "画布类型：1-离线, 2-T0")
    private Integer flowType;

    @ApiModelProperty(value = "业务线： new-cust-新客, old-cust-老客, test-cust-测试")
    private String businessType;

    @ApiModelProperty(value = "有效开始")
    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    private LocalDateTime validityBegin;

    @ApiModelProperty(value = "有效结束")
    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    private LocalDateTime validityEnd;

    @ApiModelProperty(value = "状态")
    private Integer status;

    // 策略组定义
    @ApiModelProperty(value = "策略组定义")
    private List<UpdateStrategyConfig> stragegyList;

    @JsonIgnore
    @JSONField(serialize = false)
    public boolean isValid() {
        if (CollectionUtils.isEmpty(stragegyList)) {
            return false;
        }
        List<Long> strategyIdList = stragegyList.stream()
                .filter(x -> x.getStrategyId() != null)
                .map(UpdateStrategyConfig::getStrategyId)
                .collect(Collectors.toList());
        if (strategyIdList.size() != new HashSet<>(strategyIdList).size()) {
            return false;
        }
        List<Node> nodes = stragegyList.stream().map(UpdateStrategyConfig::getNode)
                .collect(Collectors.toList());
        if (nodes.size() != nodes.stream().map(Node::getNodeId)
                .collect(Collectors.toSet()).size()) {
            return false;
        }
        return true;
    }
}