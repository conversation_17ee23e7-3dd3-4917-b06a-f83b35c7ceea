package com.xftech.cdp.api.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2023/2/23
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ExportMonitorReq extends BaseReq {

    @NotNull(message = "策略id不能为空")
    @ApiModelProperty(value = "策略id")
    private Long strategyId;

    @NotNull(message = "策略渠道不能为空")
    @ApiModelProperty(value = "策略渠道")
    private Integer strategyMarketChannel;
}
