package com.xftech.cdp.infra.rocketmq.repay;

import com.alibaba.fastjson.JSONObject;
import com.xftech.cdp.domain.mq.MqConsumeService;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventMessageVO;
import com.xftech.cdp.infra.rocketmq.MessageHandler;
import com.xftech.cdp.infra.rocketmq.PayTypeEnum;
import com.xftech.cdp.infra.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

import static com.xftech.cdp.infra.rocketmq.EventEnum.REPAY_SUCCESS;
import static com.xftech.cdp.infra.rocketmq.RocketMQEnum.TP_REPAY_TRADE_REPAY_RESULT_REPAY_SUCCESS;

/**
 * <AUTHOR>
 * @version $ RepayResultHandler, v 0.1 2025/3/5 13:28 tianshuo.qiu Exp $
 */
@Slf4j
@Component
public class RepaySuccessHandler implements MessageHandler {
    @Autowired
    private MqConsumeService mqConsumeService;

    @Override
    public String getTopic() {
        return TP_REPAY_TRADE_REPAY_RESULT_REPAY_SUCCESS.getTopic() + "_" + "repaySuccess";
    }

    @Override
    public boolean execute(String messageId, Object message) {
        RepayResultNotifyMessageDTO messageVO = JSONObject.parseObject(message.toString(), RepayResultNotifyMessageDTO.class);
        BizEventMessageVO bizEventMessageVO = transform(messageVO);
        if (StringUtils.isNotBlank(bizEventMessageVO.getBizEventType())) {
            mqConsumeService.bizEventProcess(messageId, bizEventMessageVO);
            return true;
        } else {
            log.info("RepaySuccessHandler execute 不符合还款成功筛选条件, message={}", JSONObject.toJSONString(messageVO));
            return false;
        }
    }

    private BizEventMessageVO transform(RepayResultNotifyMessageDTO repayResultNotifyMessageDTO) {
        BizEventMessageVO bizEventMessageVO = new BizEventMessageVO();
        if (repayResultNotifyMessageDTO.getUserNo() == null ) {
            return bizEventMessageVO;
        }
        bizEventMessageVO.setAppUserId(Long.valueOf(repayResultNotifyMessageDTO.getUserNo()));
        bizEventMessageVO.setApp(repayResultNotifyMessageDTO.getApp());
        bizEventMessageVO.setInnerApp(repayResultNotifyMessageDTO.getInnerApp());
        bizEventMessageVO.setTriggerDatetime(DateUtil.convertStr(new Date()));
        BizEventMessageVO.ExtrData extrData = new BizEventMessageVO.ExtrData();

        //is_settle（是否结清）
        boolean isSettle = repayResultNotifyMessageDTO.isSettle();
        //deduction_status（扣款单状态）
        String deductionStatus = repayResultNotifyMessageDTO.getDeductionStatus();
        String orderNumber = repayResultNotifyMessageDTO.getLoanNo();
        String payType = repayResultNotifyMessageDTO.getPayType();
        String payChannel = repayResultNotifyMessageDTO.getPayChannel();
        String bizPaymentNumber = repayResultNotifyMessageDTO.getDeductionNo();

        extrData.setSettle(isSettle);
        extrData.setDeductionStatus(deductionStatus);
        extrData.setOrderNumber(orderNumber);
        extrData.setPayType(payType);
        extrData.setPayChannel(payChannel);
        extrData.setBizPaymentNumber(bizPaymentNumber);
        extrData.setRepayType(repayType(payType));
        bizEventMessageVO.setExtrData(extrData);

        if (Objects.equals(deductionStatus, "03")) {
            bizEventMessageVO.setBizEventType(REPAY_SUCCESS.getEventType());
        }
        log.info("RepaySuccessHandler transform, message={}", JSONObject.toJSONString(bizEventMessageVO));
        return bizEventMessageVO;
    }

    private Integer repayType(String payType) {
        if (StringUtils.equalsIgnoreCase(payType, PayTypeEnum.NORMAL.getCode())) {
            //用户主动还款
            return 1;
        } else if (StringUtils.equalsIgnoreCase(payType, PayTypeEnum.AUTO.getCode())
            || StringUtils.equalsIgnoreCase(payType, PayTypeEnum.AUTO_PART.getCode())
            || StringUtils.equalsIgnoreCase(payType, PayTypeEnum.OVERDUE_AUTO.getCode())
            || StringUtils.equalsIgnoreCase(payType, PayTypeEnum.OVERDUE_AUTO_PART.getCode())) {
            //批扣
            return 0;
        } else if (StringUtils.equalsIgnoreCase(payType, PayTypeEnum.LIVE.getCode())
            || StringUtils.equalsIgnoreCase(payType, PayTypeEnum.LIVE_PART.getCode())) {
            //线下还款
            return 2;
        } else {
            //其他
            return 3;
        }
    }
}
