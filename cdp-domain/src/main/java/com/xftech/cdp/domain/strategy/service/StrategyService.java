package com.xftech.cdp.domain.strategy.service;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.api.dto.aitel.AITelBizSourceRequest;
import com.xftech.cdp.api.dto.aitel.AiParam;
import com.xftech.cdp.api.dto.aitel.BizSourceInfo;
import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.req.*;
import com.xftech.cdp.api.dto.req.sms.SmsGroupReq;
import com.xftech.cdp.api.dto.req.sms.SmsTemplateCheckReq;
import com.xftech.cdp.api.dto.resp.*;
import com.xftech.cdp.api.dto.resp.dict.Dict;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyCrowdPackDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/2/9 01:30
 */
public interface StrategyService {

    boolean insert(StrategyCreateReq strategyCreateReq);

    boolean update(StrategyUpdateReq strategyUpdateReq);

//    boolean delete(StrategyDeleteReq strategyDeleteReq);

    PageResultResponse<StrategyListResp> list(StrategyListReq strategyListReq);

    StrategyDetailResp getDetail(Long strategyId);

    PageResultResponse<AbsMonitorListResp> monitorList(MonitorListReq monitorListReq);

    PageResultResponse<AbsMonitorListResp> queryStrategyEngineMonitorList(MonitorEngineListReq request);

    Integer countCrowdPackUserNum(CrowdPackUserNumReq crowdPackUserNumReq);

    PageResultResponse<CrowdPackListResp> getCrowdPackList(CrowdPackListReq crowdPackListReq);

    void exportMonitor(ExportMonitorReq exportMonitorReq, HttpServletResponse response) throws IOException;

    boolean operate(StrategyOperateReq strategyOperateReq);

    String batchDelete(StrategyBatchDeleteReq strategyBatchDeleteReq);

    void refreshStatus();

    void strategyEvent30MinAlarm();

    void countTodayDispatch();

    List<Dict> getSmsTemplateGroup(SmsGroupReq smsGroupReq);

    boolean templateParamCheck(SmsTemplateCheckReq smsTemplateCheckReq);

    boolean existStrategyFlowCtrlUpdate();

    void offlineStrategyCrowdStatusAlarm();

    void strategyCrowdPackExpireAlarm();

    void strategyEndAlarm(StrategyDo strategyDo);

    void strategy14DayEndAlarm(StrategyDo strategyDo);

    PageResultResponse<com.xftech.cdp.api.dto.resp.external.StrategyListResp>
    getStrategyList(com.xftech.cdp.api.dto.req.external.StrategyListReq args);

    /**
     * 根据类型和状态查询策略（不包含画布策略）
     * @param sendRulerList 发送规则列表
     * @param statusList 状态列表
     * @return 返回值
     */
    List<StrategyDo> getStrategyList(List<Integer> sendRulerList, List<Integer> statusList);

    /**
     * 根据类型和状态查询所有策略（包含画布策略）
     * @param sendRulerList
     * @param statusList
     * @return
     */
    List<StrategyDo> getAllStrategyList(List<Integer> sendRulerList, List<Integer> statusList);

    List<CyclePreviewDto> cyclePreview(Integer cycleNum);

    PageResultResponse<NameTypeResp> getNameTypeList(NameTypeReq nameTypeReq);

    NameTypeConfigResp getNameTypeConfigList();

    StrategyReportDailyResp queryReportDailyList();

    void updateStrategyCrowdPack(String crowdPackIds, List<StrategyCrowdPackDo> strategyCrowdPackDoList, Long strategyId);

    void verifyChannel(StrategyCreateReq.StrategyMarketChannel strategyMarketChannel);

    void updateRuleConfig(StrategyUpdateReq strategyUpdateReq, StrategyDo strategyDo);

    int deleteById(Long id);

    PageResultResponse<PolicyListResp> getPolicyList(PolicyListReq policyListReq);

    boolean operatePolicy(OperatePolicyReq operatePolicyReq);

    String policyDetail(Integer policyId);

    NameTypeResp getNameTypeDetail( Integer nameTypeId);

    PageResultResponse<PushTemplateResp> getPushTemplateList(PushTemplateReq pushTemplateReq);

    PageResultResponse<GoodsListResp> getGoodsList(GoodsListReq goodsListReq);

    boolean updatePolicyPriority(UpdatePolicyPriorityReq req);

    List<BizSourceInfo> bizSourceList(AITelBizSourceRequest aiTelBizSourceRequest);

    List<AiParam> aiParams();

}
