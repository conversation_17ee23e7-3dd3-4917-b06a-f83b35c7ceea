/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.infra.client.telemarketing.model.resp;

import lombok.Data;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @version $ TeleNameConfigDetailResp, v 0.1 2023/10/19 15:50 wancheng.qu Exp $
 */
@Data
public class TeleNameConfigDetailResp implements Serializable {

    private Integer id;
    private String  type;
    private String  blankBizKey;
    private String  blankMethod;  //留白方式,rand:随机数,rate:比例
    private Integer isBlank;  //是否留白，0:否,1:是



}