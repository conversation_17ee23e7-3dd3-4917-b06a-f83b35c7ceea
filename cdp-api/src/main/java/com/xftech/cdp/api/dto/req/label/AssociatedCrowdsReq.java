/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.req.label;

import com.xftech.cdp.api.dto.base.PageRequestDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version $ AssociatedCrowdsReq, v 0.1 2024/6/20 16:04 lingang.han Exp $
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AssociatedCrowdsReq extends PageRequestDto {
    @ApiModelProperty(value = "标签codeId")
    @NotNull
    private Long labelCodeId;
}