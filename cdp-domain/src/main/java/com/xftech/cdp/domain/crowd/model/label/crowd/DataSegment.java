package com.xftech.cdp.domain.crowd.model.label.crowd;

import com.xftech.cdp.domain.crowd.exception.CrowdException;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/2/21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DataSegment extends CrowdLabelOption {

    private List<Segment> segments;

    /**
     * (column between min and max or column between min and max)
     */
    @Override
    public StringBuilder condition(String column, String configOptionReflect) {
        StringBuilder condition = new StringBuilder(" ( ");
        for (int i = 0; i < segments.size(); i++) {
            Segment segment = segments.get(i);
            condition.append(column).append(" between ").append(segment.min).append(" and ").append(segment.max);
            if (i != segments.size() - 1) {
                condition.append(" or ");
            }
        }
        condition.append(" ) ");
        return condition;
    }

    @Override
    public void verify() {
        if (CollectionUtils.isEmpty(segments)) {
            throw new CrowdException("数值段数据错误！");
        }
        for (Segment segment : segments) {
            if (segment.min > segment.max) {
                throw new CrowdException("数值段数据 min > max！");
            }
        }
    }

    @Data
    public static class Segment {
        private Long min;

        private Long max;
    }
}
