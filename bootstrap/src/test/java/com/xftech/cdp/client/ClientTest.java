package com.xftech.cdp.client;

import com.alibaba.fastjson.JSON;
import com.xftech.cdp.BaseTest;
import com.xftech.cdp.infra.client.datacenter.DataCenterClient;
import com.xftech.cdp.infra.client.datacenter.model.MobileNumber;
import com.xftech.cdp.infra.client.sms.SmsClient;
import com.xftech.cdp.infra.client.sso.model.constants.SsoConstants;
import com.xftech.cdp.infra.client.usercenter.UserCenterClient;
import com.xftech.cdp.infra.client.usercenter.model.UserIdDetailResp;
import com.xftech.cdp.infra.client.usercenter.model.UserIdReq;
import com.xftech.cdp.infra.client.usercenter.model.UserIdResp;
import com.xftech.cdp.infra.constant.Constants;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.utils.HttpClientUtil;
import com.xftech.cdp.infra.utils.JsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import sun.misc.BASE64Decoder;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/2/16
 */
public class ClientTest extends BaseTest {

    @Resource
    private UserCenterClient userCenterClient;

    @Resource
    private DataCenterClient dataCenterClient;

    @Resource
    private SmsClient smsClient;
    @Resource
    private HttpClientUtil httpClientUtil;

    private static MultiValueMap<String, String> headers(String token) {
        LinkedMultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        headers.add(SsoConstants.APP_HEADER_NAME, Constants.SSO_APP_NAME);
        headers.add(SsoConstants.TOKEN_HEADER_NAME, token);
        return headers;
    }

//    @Test
//    public void t3(){
//        smsClient.queryGroup(  )
//    }

    @Test
    public void t1() {
        CrowdDetailDo crowdDetail = new CrowdDetailDo();
        crowdDetail.setApp("dispatchApp");
        crowdDetail.setUserId(1005144L);
        if (StringUtils.isBlank(crowdDetail.getMobile())) {
            UserIdResp users = userCenterClient.getUsersById(crowdDetail.getUserId());
            users.getUserList().stream().findFirst().map(UserIdDetailResp::getMobile).ifPresent(crowdDetail::setMobile);
        }
        System.out.println(crowdDetail.getMobile());

        Long userIds = 1005144L;
        UserIdResp usersByIds = userCenterClient.getUsersById(userIds);
        System.out.println(JSON.toJSONString(usersByIds));

    }

    @Test
    public void t2() {
        JsonUtil.toJson(userCenterClient.getUserByMobile("13472432767", "xyf"));
    }
}
