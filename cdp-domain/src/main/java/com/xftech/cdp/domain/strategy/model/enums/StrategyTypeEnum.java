package com.xftech.cdp.domain.strategy.model.enums;

import com.xftech.cdp.domain.strategy.exception.StrategyException;

import java.util.Objects;

public enum StrategyTypeEnum {

    EVENT(0, "事件"),
    EVENT_ENGINE(1, "事件引擎"),

    ;

    private final int code;
    private final String description;

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    StrategyTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public static StrategyTypeEnum getInstance(Integer code) {
        for (StrategyTypeEnum strategyTypeEnum : values()) {
            if (Objects.equals(strategyTypeEnum.code, code)) {
                return strategyTypeEnum;
            }
        }
        return EVENT;
    }
}
