# 离线-触达汇总版: 普通触达与引擎触达完整链路分析报告

## 1. 概述

本文档详细分析离线触达的两种完整链路：**离线-普通触达**和**离线-引擎触达**，从定时任务调度开始，到最终的营销触达执行。离线触达系统是一个基于定时任务的批量营销系统，支持两种策略类型：

- **离线-普通触达**: 基于`AbstractStrategyDispatchService.dispatchHandler`的直接营销触达
- **离线-引擎触达**: 基于`StrategyEventDispatchServiceImpl.marketingSend`的AI引擎决策营销触达

两种触达方式在前期的任务调度、人群处理、数据过滤等环节基本相同，主要差异在于决策机制和执行时机。

## 2. 整体架构图

### 2.1 完整调用链路图

```mermaid
graph TD
    subgraph "离线-普通触达链路"
        A1["XxlJob定时任务<br/>STRATEGY_DISPATCH_TASK_GENERATOR<br/>策略执行任务生成器"] --> B1["StrategyHandler<br/>.strategyDispatchTaskProducer()<br/>生成周期策略和离线策略任务"]
        B1 --> C1["DispatchTaskService<br/>.createDispatchTask()<br/>创建dispatch_task表记录"]
        C1 --> D1["插入dispatch_task表<br/>(status=0)<br/>待执行任务记录"]

        E1["XxlJob定时任务<br/>STRATEGY_DISPATCH_TASK_EXECCUTE<br/>策略执行下发任务"] --> F1["StrategyHandler<br/>.strategyDispatchTaskConsumer()<br/>分片查询待执行任务"]
        F1 --> G1["DispatchTaskService<br/>.selectTodoList()<br/>查询待执行的dispatch_task"]
        G1 --> H1["StrategyHandler<br/>.execute()<br/>根据渠道类型路由到具体实现"]
        H1 --> I1["AbstractStrategyDispatchService<br/>.execute()<br/>策略执行入口"]

        I1 --> J1["AbstractStrategyDispatchService<br/>.coreLogicExecute()<br/>核心逻辑执行"]
        J1 --> K1["AbstractStrategyDispatchService<br/>.queryAndGroupAndSend()<br/>分页查询+分组+下发"]
        K1 --> L1["CrowdDetailRepository<br/>.selectByIdPage()<br/>分页查询crowd_detail表"]
        L1 --> M1["RandomNumService<br/>.randomNum()<br/>获取随机数用于AB测试"]
        M1 --> N1["StrategyGroupService<br/>.matchGroupRule()<br/>AB测试分组匹配"]
        N1 --> O1["AbstractStrategyDispatchService<br/>.dispatchHandler()<br/>抽象方法调用具体实现"]

        O1 --> P1["具体渠道实现<br/>StrategyDispatchForSmsServiceImpl等<br/>调用BatchDispatchService"]
        P1 --> Q1["BatchDispatchService<br/>.sendSms()/.sendTele()等<br/>调用外部服务接口"]
        Q1 --> R1["外部服务调用<br/>(SMS/电销/Push等)<br/>实际的营销触达执行"]
    end

    subgraph "离线-引擎触达链路"
        A2["XxlJob定时任务<br/>OFFLINE_ENGINE_DISTRIBUTE_TASK_GENERATE<br/>定时扫描有效策略，生成分片任务"] --> B2["OfflineStrategyJobDispatch<br/>.strategySliceTask()<br/>查询引擎策略列表，过滤有效期内策略"]
        B2 --> C2["StrategyTaskDistributeHandler<br/>.generateDispatchTask()<br/>遍历策略，为每个策略创建分片任务"]
        C2 --> D2["StrategyTaskDistributeHandler<br/>.createStrategyDistributeSliceTasks()<br/>根据人群分片创建执行任务记录"]
        D2 --> E2["插入strategy_slice_exec_log表<br/>(status=0)<br/>记录分片任务初始状态，等待执行"]

        F2["XxlJob定时任务<br/>OFFLINE_ENGINE_DISTRIBUTE_EXECUTE<br/>分布式执行分片任务，支持多节点并行"] --> G2["OfflineStrategyJobDispatch<br/>.strategySliceTaskExecute()<br/>获取分片信息，按节点分配任务"]
        G2 --> H2["StrategyTaskDistributeHandler<br/>.execDistributeSliceTask()<br/>CRC32哈希路由，筛选当前节点任务"]
        H2 --> I2["StrategyTaskDistributeHandler<br/>.dispatchTaskExecuting()<br/>分布式锁控制，防止重复执行"]
        I2 --> J2["DistributeOfflineEngineDispatchServiceImpl<br/>.execute()<br/>分片执行入口，初始化执行上下文"]

        J2 --> K2["DistributeOfflineEngineDispatchServiceImpl<br/>.coreLogicExecute()<br/>构建AB测试分组匹配规则"]
        K2 --> L2["DistributeOfflineEngineDispatchServiceImpl<br/>.batchDispatch()<br/>分页读取人群数据，避免内存溢出"]
        L2 --> M2["DistributeOfflineEngineDispatchServiceImpl<br/>.filterAndDispatch()<br/>用户去重、AB分组、标签过滤"]
        M2 --> N2["DistributeOfflineEngineDispatchServiceImpl<br/>.dispatchHandler()<br/>批量异步调用引擎，等待所有结果"]

        N2 --> O2["DispatchOfflineEngineService<br/>.pushEngine()<br/>构建引擎请求，异步调用预测中心"]
        O2 --> P2["EnginePredictionClient<br/>.modelPrediction()<br/>调用AI引擎，获取营销决策结果"]
        P2 --> Q2["DispatchOfflineEngineService<br/>.processEngineDecision()<br/>解析引擎决策，创建延迟执行任务"]
        Q2 --> R2["插入dispatch_user_delay表<br/>(status=0)<br/>记录延迟任务，等待指定时间执行"]

        S2["XxlJob定时任务<br/>DISPATCH_USER_DELAY_EXECUTE<br/>扫描到期延迟任务，执行营销触达"] --> T2["DispatchUserDelayJob<br/>.executeDelayTasks()<br/>查询到期任务，构建营销参数"]
        T2 --> U2["StrategyEventDispatchServiceImpl<br/>.marketingSend()<br/>营销发送统一入口，生成批次号"]
        U2 --> V2["StrategyEventDispatchServiceImpl<br/>.processChannelDispatch()<br/>根据渠道类型分发到不同处理器"]
        V2 --> W2["EventDispatchService<br/>.sendSmsEvent()/.sendTeleEvent()等<br/>调用具体渠道服务，发送营销内容"]
        W2 --> X2["外部服务调用<br/>(SMS/电销/Push等)<br/>实际的营销触达执行"]
    end

    style A1 fill:#e1f5fe
    style E1 fill:#e1f5fe
    style O1 fill:#ffeb3b
    style Q1 fill:#f3e5f5
    style R1 fill:#e8f5e8

    style A2 fill:#e1f5fe
    style F2 fill:#e1f5fe
    style S2 fill:#e1f5fe
    style U2 fill:#ffeb3b
    style P2 fill:#f3e5f5
    style X2 fill:#e8f5e8
```

#### 业务功能分阶段说明

**离线-普通触达业务流程**
- **第一阶段：任务生成**: 定时扫描type=0的普通策略，为每个策略的每个渠道创建执行任务
- **第二阶段：任务执行**: 分片并行处理执行任务，分页查询人群数据，进行AB测试分组
- **第三阶段：人群处理**: 用户去重、标签查询、模板参数验证
- **第四阶段：营销执行**: 直接调用具体渠道服务，执行实际的营销触达

**离线-引擎触达业务流程**
- **第一阶段：任务生成**: 定时扫描type=1的引擎策略，为每个策略的人群分片创建执行记录
- **第二阶段：分片执行**: 分布式并行处理分片任务，分页读取用户数据，数据过滤和分组
- **第三阶段：引擎决策**: 调用AI引擎获取个性化营销决策，生成延迟执行任务
- **第四阶段：营销执行**: 在指定时间执行营销触达，支持多种渠道的统一分发

### 2.2 关键类和方法对应关系

#### 离线-普通触达关键类方法

| 序号 | 类名 | 关键方法 | 代码位置 | 功能说明 |
|------|------|----------|----------|----------|
| 1 | `StrategyDispatch` | `strategyDispatchTaskProducer()` | `cdp-adapter/.../StrategyDispatch.java:36` | XxlJob任务生成入口 |
| 2 | `StrategyDispatch` | `strategyDispatchTaskConsumer()` | `cdp-adapter/.../StrategyDispatch.java:54` | XxlJob任务执行入口 |
| 3 | `StrategyHandler` | `strategyDispatchTaskProducer()` | `cdp-domain/.../StrategyHandler.java:39` | 策略任务生成逻辑 |
| 4 | `StrategyHandler` | `strategyDispatchTaskConsumer()` | `cdp-domain/.../StrategyHandler.java:602` | 策略任务消费逻辑 |
| 5 | `StrategyHandler` | `execute()` | `cdp-domain/.../StrategyHandler.java:703` | 策略执行路由 |
| 6 | `AbstractStrategyDispatchService` | `execute()` | `cdp-domain/.../AbstractStrategyDispatchService.java:195` | 策略执行入口 |
| 7 | `AbstractStrategyDispatchService` | `coreLogicExecute()` | `cdp-domain/.../AbstractStrategyDispatchService.java:631` | 核心逻辑执行 |
| 8 | `AbstractStrategyDispatchService` | `queryAndGroupAndSend()` | `cdp-domain/.../AbstractStrategyDispatchService.java:650` | 查询分组下发 |
| 9 | `AbstractStrategyDispatchService` | `dispatchHandler()` | `cdp-domain/.../AbstractStrategyDispatchService.java:915` | 抽象下发方法 |
| 10 | `StrategyDispatchForSmsServiceImpl` | `dispatchHandler()` | `cdp-domain/.../StrategyDispatchForSmsServiceImpl.java:85` | 短信渠道实现 |
| 11 | `BatchDispatchService` | `sendSms()` | `cdp-domain/.../BatchDispatchServiceImpl.java:106` | 短信发送服务 |
| 12 | `CrowdDetailRepository` | `selectByIdPage()` | `cdp-domain/.../CrowdDetailRepository.java:72` | 人群分页查询 |

#### 离线-引擎触达关键类方法

| 序号 | 类名 | 关键方法 | 代码位置 | 功能说明 |
|------|------|----------|----------|----------|
| 1 | `OfflineStrategyJobDispatch` | `strategySliceTask()` | `cdp-job/.../OfflineStrategyJobDispatch.java:39` | XxlJob任务生成入口 |
| 2 | `OfflineStrategyJobDispatch` | `strategySliceTaskExecute()` | `cdp-job/.../OfflineStrategyJobDispatch.java:54` | XxlJob任务执行入口 |
| 3 | `StrategyTaskDistributeHandler` | `generateDispatchTask()` | `cdp-domain/.../StrategyTaskDistributeHandler.java:116` | 生成分发任务 |
| 4 | `StrategyTaskDistributeHandler` | `createStrategyDistributeSliceTasks()` | `cdp-domain/.../StrategyTaskDistributeHandler.java:145` | 创建分片任务 |
| 5 | `StrategyTaskDistributeHandler` | `execDistributeSliceTask()` | `cdp-domain/.../StrategyTaskDistributeHandler.java:375` | 执行分片任务 |
| 6 | `StrategyTaskDistributeHandler` | `dispatchTaskExecuting()` | `cdp-domain/.../StrategyTaskDistributeHandler.java:457` | 分片任务执行逻辑 |
| 7 | `DistributeOfflineEngineDispatchServiceImpl` | `execute()` | `cdp-domain/.../DistributeOfflineEngineDispatchServiceImpl.java:96` | 分片执行入口 |
| 8 | `DistributeOfflineEngineDispatchServiceImpl` | `coreLogicExecute()` | `cdp-domain/.../DistributeOfflineEngineDispatchServiceImpl.java:116` | 核心逻辑执行 |
| 9 | `DistributeOfflineEngineDispatchServiceImpl` | `batchDispatch()` | `cdp-domain/.../DistributeOfflineEngineDispatchServiceImpl.java:137` | 批量分发处理 |
| 10 | `DistributeOfflineEngineDispatchServiceImpl` | `dispatchHandler()` | `cdp-domain/.../DistributeOfflineEngineDispatchServiceImpl.java:317` | 分发处理器 |
| 11 | `DispatchOfflineEngineService` | `pushEngine()` | `cdp-domain/.../DispatchOfflineEngineService.java:58` | 异步引擎调用 |
| 12 | `EnginePredictionClient` | `modelPrediction()` | `cdp-domain/.../EnginePredictionClientImpl.java:624` | 引擎预测调用 |
| 13 | `DispatchUserDelayJob` | `executeDelayTasks()` | `cdp-job/.../DispatchUserDelayJob.java:665` | 延迟任务执行 |
| 14 | `StrategyEventDispatchServiceImpl` | `marketingSend()` | `cdp-domain/.../StrategyEventDispatchServiceImpl.java:1229` | 营销发送入口 |

### 2.3 方法调用时序图

```mermaid
sequenceDiagram
    participant XxlJob as XxlJob定时任务
    participant SD as StrategyDispatch(普通)
    participant OSD as OfflineStrategyJobDispatch(引擎)
    participant SH as StrategyHandler
    participant ASDS as AbstractStrategyDispatchService
    participant DOED as DistributeOfflineEngineDispatchServiceImpl
    participant DOES as DispatchOfflineEngineService
    participant EPC as EnginePredictionClient
    participant SEDS as StrategyEventDispatchServiceImpl
    participant BDS as BatchDispatchService

    Note over XxlJob,BDS: 离线-普通触达时序
    XxlJob->>SD: @XxlJob("STRATEGY_DISPATCH_TASK_GENERATOR")
    SD->>SH: strategyDispatchTaskProducer()
    SH->>SH: 查询type=0策略，创建dispatch_task记录
    SH-->>XxlJob: 任务生成完成

    XxlJob->>SD: @XxlJob("STRATEGY_DISPATCH_TASK_EXECCUTE")
    SD->>SH: strategyDispatchTaskConsumer(total, index, bizType)
    SH->>ASDS: execute(marketChannelId, dispatchTaskDo)
    ASDS->>ASDS: coreLogicExecute() → queryAndGroupAndSend()
    ASDS->>ASDS: dispatchHandler() (抽象方法)
    ASDS->>BDS: sendSms()/sendTele()等具体渠道方法
    BDS-->>ASDS: 执行结果
    ASDS-->>XxlJob: 更新执行状态和记录

    Note over XxlJob,BDS: 离线-引擎触达时序
    XxlJob->>OSD: @XxlJob("OFFLINE_ENGINE_DISTRIBUTE_TASK_GENERATE")
    OSD->>OSD: 查询type=1策略，创建strategy_slice_exec_log记录
    OSD-->>XxlJob: 分片任务生成完成

    XxlJob->>OSD: @XxlJob("OFFLINE_ENGINE_DISTRIBUTE_EXECUTE")
    OSD->>DOED: execute(sliceExecLogDo)
    DOED->>DOED: coreLogicExecute() → batchDispatch()
    DOED->>DOED: filterAndDispatch() → dispatchHandler()
    DOED->>DOES: pushEngine() (异步)
    DOES->>EPC: modelPrediction()
    EPC-->>DOES: PredictDecisionDto
    DOES-->>DOED: 插入dispatch_user_delay表

    XxlJob->>SEDS: @XxlJob("DISPATCH_USER_DELAY_EXECUTE")
    SEDS->>SEDS: executeDelayTasks() → marketingSend()
    SEDS->>SEDS: processChannelDispatch()
    SEDS-->>XxlJob: 插入user_dispatch_detail表
```

### 2.4 关键代码跳转路径对比

#### 2.4.1 离线-普通触达代码跳转
```java
// 1. XxlJob入口
@XxlJob("STRATEGY_DISPATCH_TASK_EXECCUTE")
public ReturnT<String> strategyDispatchTaskConsumer(String param) {
    strategyHandler.strategyDispatchTaskConsumer(total, index, bizType); // 跳转到步骤2
}

// 2. 任务消费处理
public void strategyDispatchTaskConsumer(int total, int index, int bizType) {
    List<DispatchTaskDo> taskList = dispatchTaskService.selectTodoList(total, index, bizType);
    for (DispatchTaskDo task : taskList) {
        execute(Long.parseLong(task.getAssociationId()), task); // 跳转到步骤3
    }
}

// 3. 策略执行路由
public void execute(Long marketChannelId, DispatchTaskDo dispatchTaskDo) {
    this.build(marketChannel).execute(marketChannelId, dispatchTaskDo); // 跳转到步骤4
}

// 4. 核心逻辑执行
protected void coreLogicExecute(StrategyContext strategyContext) {
    this.queryAndGroupAndSend(strategyContext, Triple.of(bizKey, matchFunction, blankGroup)); // 跳转到步骤5
}

// 5. 直接调用dispatchHandler
protected void queryAndGroupAndSend(StrategyContext context, Triple funPair) {
    dispatchHandler(context, groupCount, totalCount, sendCount, app, pair.getLeft(), pair.getRight()); // 直接执行
}
```

#### 2.4.2 离线-引擎触达代码跳转
```java
// 1. XxlJob入口
@XxlJob("OFFLINE_ENGINE_DISTRIBUTE_EXECUTE")
public ReturnT<String> strategySliceTaskExecute(String param) {
    strategyTaskDistributeHandler.execDistributeSliceTask(total, index); // 跳转到步骤2
}

// 2. 分片任务执行
public void execDistributeSliceTask(int shardTotal, int shardIndex) {
    Future<Boolean> future = DispatchTaskExecutor.getPool().submit(() ->
        dispatchTaskExecuting(sliceExecLogDo)); // 跳转到步骤3
}

// 3. 分片执行入口
public void execute(StrategySliceExecLogDo sliceExecLogDo) {
    coreLogicExecute(strategyContext); // 跳转到步骤4
}

// 4. 核心逻辑执行
protected void coreLogicExecute(StrategyExecuteContext strategyContext) {
    batchDispatch(strategyContext, matchFunctions); // 跳转到步骤5
}

// 5. 异步引擎调用
protected ImmutablePair<Integer, CrowdPushBatchDo> dispatchHandler(...) {
    Future<List<DispatchUserDelayDo>> listFuture = dispatchOfflineEngineService.pushEngine(...); // 异步调用引擎
}

// 6. 延迟任务执行
@XxlJob("DISPATCH_USER_DELAY_EXECUTE")
public void executeDelayTasks() {
    int sendResult = strategyEventDispatchService.marketingSend(...); // 最终营销执行
}
```

### 2.5 数据表流转关系

```mermaid
graph TD
    subgraph "离线-普通触达数据流转"
        A1["strategy表<br/><small>SELECT查询type=0有效策略</small>"] --> B1["strategy_group表<br/><small>SELECT查询分组配置</small>"]
        B1 --> C1["strategy_market_channel表<br/><small>SELECT查询渠道配置</small>"]
        C1 --> D1["dispatch_task表<br/><small>INSERT创建执行任务<br/>SELECT查询待执行任务<br/>UPDATE更新执行状态</small>"]
        D1 --> E1["strategy_crowd_pack表<br/><small>SELECT查询策略关联人群包</small>"]
        E1 --> F1["crowd_detail表<br/><small>SELECT分页查询人群明细</small>"]
        F1 --> G1["strategy_exec_log表<br/><small>INSERT记录执行日志<br/>UPDATE更新执行状态</small>"]
        G1 --> H1["crowd_push_batch表<br/><small>INSERT记录批次信息</small>"]
        H1 --> I1["user_dispatch_detail表<br/><small>INSERT记录触达明细</small>"]
    end

    subgraph "离线-引擎触达数据流转"
        A2["strategy表<br/><small>SELECT查询type=1有效策略</small>"] --> B2["crowd_slice表<br/><small>INSERT批量插入分片记录<br/>SELECT查询人群分片</small>"]
        B2 --> C2["strategy_slice_exec_log表<br/><small>INSERT创建分片任务<br/>UPDATE更新执行状态</small>"]
        C2 --> D2["dispatch_user_delay表<br/><small>INSERT批量插入延迟任务<br/>SELECT查询到期任务<br/>UPDATE更新执行状态</small>"]
        D2 --> E2["user_dispatch_detail表<br/><small>INSERT记录触达明细</small>"]
        D2 --> F2["event_push_batch表<br/><small>INSERT记录批次信息</small>"]
    end

    subgraph "共享Redis缓存"
        G["Redis缓存<br/><small>SET/GET/DEL操作</small>"] --> H["随机数缓存<br/><small>HGET获取用户随机数</small>"]
        G --> I["流控计数<br/><small>INCR计数器<br/>EXPIRE设置过期</small>"]
        G --> J["执行统计<br/><small>INCR计数<br/>PFADD去重统计</small>"]
        G --> K["分布式锁<br/><small>SETNX获取锁<br/>DEL释放锁</small>"]
    end

    style A1 fill:#e3f2fd
    style A2 fill:#e3f2fd
    style I1 fill:#e8f5e8
    style E2 fill:#e8f5e8
    style F2 fill:#e8f5e8
    style G fill:#fff3e0
```

#### 详细数据库操作说明

**离线-普通触达数据库操作**

| 表名 | 操作类型 | 具体操作 | 代码位置 | XxlJob阶段 | 业务场景 |
|------|----------|----------|----------|------------|----------|
| **strategy** | SELECT | 查询type=0有效普通策略 | `StrategyRepository.getStrategyList()` | STRATEGY_DISPATCH_TASK_GENERATOR | 任务生成阶段筛选策略 |
| **strategy** | UPDATE | 更新策略执行状态 | `StrategyRepository.updateById()` | STRATEGY_DISPATCH_TASK_EXECCUTE | 标记策略为执行中 |
| **strategy_group** | SELECT | 查询策略分组配置 | `StrategyGroupRepository.selectListByStrategyId()` | STRATEGY_DISPATCH_TASK_EXECCUTE | 获取AB测试分组规则 |
| **strategy_market_channel** | SELECT | 查询策略渠道配置 | `StrategyMarketChannelRepository.selectById()` | STRATEGY_DISPATCH_TASK_EXECCUTE | 获取渠道配置信息 |
| **strategy_crowd_pack** | SELECT | 查询策略人群包关联 | `StrategyCrowdPackRepository.selectListByStrategyId()` | STRATEGY_DISPATCH_TASK_EXECCUTE | 获取策略关联的人群包 |
| **dispatch_task** | INSERT | 创建执行任务 | `DispatchTaskRepository.insert()` | STRATEGY_DISPATCH_TASK_GENERATOR | 为每个策略渠道创建任务 |
| **dispatch_task** | SELECT | 查询待执行任务 | `DispatchTaskRepository.selectTodoList()` | STRATEGY_DISPATCH_TASK_EXECCUTE | 分片查询待执行任务 |
| **dispatch_task** | UPDATE | 更新任务状态 | `DispatchTaskRepository.updateById()` | STRATEGY_DISPATCH_TASK_EXECCUTE | 更新任务状态(执行中/成功/失败) |
| **crowd_detail** | SELECT | 分页查询人群明细 | `CrowdDetailRepository.selectByIdPage()` | STRATEGY_DISPATCH_TASK_EXECCUTE | 分页读取人群用户数据 |
| **strategy_exec_log** | INSERT | 记录执行日志 | `StrategyExecLogRepository.insert()` | STRATEGY_DISPATCH_TASK_EXECCUTE | 记录策略执行状态和统计 |
| **strategy_exec_log** | UPDATE | 更新执行统计 | `StrategyExecLogRepository.updateById()` | STRATEGY_DISPATCH_TASK_EXECCUTE | 更新执行人数和状态 |
| **crowd_push_batch** | INSERT | 记录批次信息 | `CrowdPushBatchRepository.insert()` | STRATEGY_DISPATCH_TASK_EXECCUTE | 记录每个批次的推送信息 |
| **user_dispatch_detail** | INSERT | 记录触达明细 | `UserDispatchDetailRepository.insert()` | STRATEGY_DISPATCH_TASK_EXECCUTE | 记录每次营销触达的详细信息 |

**离线-引擎触达数据库操作**

| 表名 | 操作类型 | 具体操作 | 代码位置 | XxlJob阶段 | 业务场景 |
|------|----------|----------|----------|------------|----------|
| **strategy** | SELECT | 查询type=1有效引擎策略 | `StrategyRepository.getStrategyList()` | OFFLINE_ENGINE_DISTRIBUTE_TASK_GENERATE | 任务生成阶段筛选策略 |
| **strategy** | UPDATE | 更新策略执行状态 | `StrategyRepository.updateById()` | OFFLINE_ENGINE_DISTRIBUTE_TASK_GENERATE | 标记策略为执行中 |
| **crowd_slice** | INSERT | 批量插入分片记录 | `CrowdSliceRepository.insertBatch()` | 人群包分片阶段(前置) | 人群包分片时创建分片记录 |
| **crowd_slice** | SELECT | 查询人群分片信息 | `CrowdSliceRepository.selectByCrowdVersion()` | OFFLINE_ENGINE_DISTRIBUTE_TASK_GENERATE | 获取分片范围和文件路径 |
| **strategy_slice_exec_log** | INSERT | 创建分片执行任务 | `StrategySliceExecLogRepository.insert()` | OFFLINE_ENGINE_DISTRIBUTE_TASK_GENERATE | 为每个分片创建执行记录 |
| **strategy_slice_exec_log** | INSERT | 批量创建任务 | `StrategySliceExecLogRepository.batchInsert()` | OFFLINE_ENGINE_DISTRIBUTE_TASK_GENERATE | 批量创建多个分片任务 |
| **strategy_slice_exec_log** | SELECT | 查询待执行任务 | `StrategySliceExecLogRepository.selectTodoList()` | OFFLINE_ENGINE_DISTRIBUTE_EXECUTE | 分布式执行时查询任务 |
| **strategy_slice_exec_log** | UPDATE | 更新执行状态 | `StrategySliceExecLogRepository.updateById()` | OFFLINE_ENGINE_DISTRIBUTE_EXECUTE | 更新任务状态(执行中/成功/失败) |
| **strategy_slice_exec_log** | UPDATE | 更新重试次数 | `StrategySliceExecLogRepository.updateById()` | OFFLINE_ENGINE_DISTRIBUTE_EXECUTE | 失败重试时增加重试计数 |
| **dispatch_user_delay** | INSERT | 批量插入延迟任务 | `DispatchUserDelayRepository.batchInsert()` | OFFLINE_ENGINE_DISTRIBUTE_EXECUTE | 引擎决策后创建延迟执行任务 |
| **dispatch_user_delay** | SELECT | 查询到期任务 | `DispatchUserDelayRepository.selectTodoList()` | DISPATCH_USER_DELAY_EXECUTE | 延迟任务执行时查询到期任务 |
| **dispatch_user_delay** | UPDATE | 更新执行状态 | `DispatchUserDelayRepository.updateById()` | DISPATCH_USER_DELAY_EXECUTE | 标记任务为已执行 |
| **user_dispatch_detail** | INSERT | 记录触达明细 | `UserDispatchDetailRepository.insert()` | DISPATCH_USER_DELAY_EXECUTE | 记录每次营销触达的详细信息 |
| **user_dispatch_detail** | INSERT | 批量插入明细 | `UserDispatchDetailRepository.saveBatchWithoutTx()` | DISPATCH_USER_DELAY_EXECUTE | 批量记录触达明细 |
| **user_dispatch_detail** | SELECT | 查询历史触达 | `UserDispatchDetailRepository.selectUserList()` | DISPATCH_USER_DELAY_EXECUTE | 流控检查时查询用户触达历史 |
| **event_push_batch** | INSERT | 记录批次信息 | `EventPushBatchRepository.insert()` | DISPATCH_USER_DELAY_EXECUTE | 记录每个批次的推送信息 |
| **event_push_batch** | SELECT | 查询批次记录 | `EventPushBatchRepository.getByChannelAndBatchNum()` | DISPATCH_USER_DELAY_EXECUTE | 根据渠道和批次号查询记录 |

#### Redis操作详细说明

**离线-普通触达Redis操作**

| Redis Key类型 | 操作命令 | 具体用途 | 代码位置 | XxlJob阶段 | 过期时间 |
|---------------|----------|----------|----------|------------|----------|
| **随机数缓存** | HGET | 获取用户随机数 | `RandomNumService.randomNum()` | STRATEGY_DISPATCH_TASK_EXECCUTE | 7天 |
| **随机数缓存** | HSET | 设置用户随机数 | `RandomNumService.randomNum()` | STRATEGY_DISPATCH_TASK_EXECCUTE | 7天 |
| **流控计数** | INCR | 用户触达次数统计 | `FlowCtrlCoreService.execute()` | STRATEGY_DISPATCH_TASK_EXECCUTE | 按配置 |
| **流控计数** | INCR | 策略级别流控统计 | `FlowCtrlCoreService.execute()` | STRATEGY_DISPATCH_TASK_EXECCUTE | 按配置 |
| **执行统计** | INCR | 策略执行人数统计 | `AbstractStrategyDispatchService.groupCount()` | STRATEGY_DISPATCH_TASK_EXECCUTE | 2天 |
| **执行统计** | PFADD | 去重用户数统计 | `AbstractStrategyDispatchService.groupCount()` | STRATEGY_DISPATCH_TASK_EXECCUTE | 2天 |

**离线-引擎触达Redis操作**

| Redis Key类型 | 操作命令 | 具体用途 | 代码位置 | XxlJob阶段 | 过期时间 |
|---------------|----------|----------|----------|------------|----------|
| **分布式锁** | SETNX | 获取分片执行锁 | `RedisUtils.setNxEx()` | OFFLINE_ENGINE_DISTRIBUTE_EXECUTE | 10分钟 |
| **分布式锁** | DEL | 释放分片执行锁 | `RedisUtils.del()` | OFFLINE_ENGINE_DISTRIBUTE_EXECUTE | 立即删除 |
| **计数器** | INCR | 进入引擎用户数统计 | `RedisUtils.increment()` | OFFLINE_ENGINE_DISTRIBUTE_EXECUTE | 2天 |
| **计数器** | INCR | 引擎调用失败数统计 | `RedisUtils.increment()` | OFFLINE_ENGINE_DISTRIBUTE_EXECUTE | 2天 |
| **计数器** | INCR | 标签排除用户数统计 | `RedisUtils.increment()` | OFFLINE_ENGINE_DISTRIBUTE_EXECUTE | 2天 |
| **HyperLogLog** | PFADD | 营销用户数去重统计 | `RedisUtils.pfAddTwoDay()` | OFFLINE_ENGINE_DISTRIBUTE_EXECUTE | 2天 |
| **HyperLogLog** | PFADD | 不营销用户数去重统计 | `RedisUtils.pfAddTwoDay()` | OFFLINE_ENGINE_DISTRIBUTE_EXECUTE | 2天 |
| **流控计数** | INCR | 用户触达次数统计 | `RedisUtils.increment()` | DISPATCH_USER_DELAY_EXECUTE | 按配置 |
| **流控计数** | INCR | 策略级别流控统计 | `RedisUtils.increment()` | DISPATCH_USER_DELAY_EXECUTE | 按配置 |
| **流控计数** | INCR | 全局级别流控统计 | `RedisUtils.increment()` | DISPATCH_USER_DELAY_EXECUTE | 按配置 |

#### XxlJob阶段总览

**离线-普通触达XxlJob任务**

| XxlJob任务名称 | 执行频率 | 主要职责 | 涉及的数据库操作 | 涉及的Redis操作 |
|----------------|----------|----------|------------------|-----------------|
| **STRATEGY_DISPATCH_TASK_GENERATOR** | 每分钟 | 扫描策略，生成执行任务 | strategy表SELECT<br/>dispatch_task表INSERT | 无 |
| **STRATEGY_DISPATCH_TASK_EXECCUTE** | 每分钟 | 执行策略任务，营销触达 | dispatch_task表SELECT/UPDATE<br/>crowd_detail表SELECT<br/>strategy_exec_log表INSERT/UPDATE<br/>crowd_push_batch表INSERT<br/>user_dispatch_detail表INSERT | 随机数缓存HGET/HSET<br/>流控计数INCR<br/>执行统计INCR/PFADD |

**离线-引擎触达XxlJob任务**

| XxlJob任务名称 | 执行频率 | 主要职责 | 涉及的数据库操作 | 涉及的Redis操作 |
|----------------|----------|----------|------------------|-----------------|
| **人群包分片阶段(前置)** | 按需执行 | 人群包OSS文件分片处理 | crowd_slice表INSERT | 无 |
| **OFFLINE_ENGINE_DISTRIBUTE_TASK_GENERATE** | 每分钟 | 扫描策略，生成分片任务 | strategy表SELECT/UPDATE<br/>crowd_slice表SELECT<br/>strategy_slice_exec_log表INSERT | 无 |
| **OFFLINE_ENGINE_DISTRIBUTE_EXECUTE** | 每分钟 | 分布式执行分片任务，调用引擎 | strategy_slice_exec_log表SELECT/UPDATE<br/>dispatch_user_delay表INSERT | 分布式锁SETNX/DEL<br/>统计计数器INCR<br/>去重统计PFADD |
| **DISPATCH_USER_DELAY_EXECUTE** | 每分钟 | 执行到期延迟任务，营销触达 | dispatch_user_delay表SELECT/UPDATE<br/>user_dispatch_detail表INSERT/SELECT<br/>event_push_batch表INSERT/SELECT | 流控计数器INCR |

#### 数据操作时序流程

**离线-普通触达数据操作时序**
```sql
-- 阶段一：任务生成阶段
-- 1. 查询有效策略
SELECT * FROM strategy WHERE type = 0 AND status IN (1,2) AND validity_begin <= NOW() AND validity_end >= NOW();

-- 2. 查询策略渠道配置
SELECT * FROM strategy_market_channel WHERE strategy_id = ?;

-- 3. 插入执行任务
INSERT INTO dispatch_task (biz_id, association_id, biz_type, status, dispatch_time) VALUES (...);

-- 阶段二：任务执行阶段
-- 1. 查询待执行任务
SELECT * FROM dispatch_task WHERE status = 0 AND dispatch_time <= NOW() LIMIT ?;

-- 2. 更新任务状态为执行中
UPDATE dispatch_task SET status = 1, start_time = NOW() WHERE id = ?;

-- 3. 分页查询人群明细
SELECT * FROM crowd_detail WHERE id > ? AND crowd_id = ? ORDER BY id ASC LIMIT ?;

-- 4. Redis随机数操作
HGET random_num:{bizKey}:{userId} randomNum;
HSET random_num:{bizKey}:{userId} randomNum {value} EX 604800;

-- 5. 插入执行日志
INSERT INTO strategy_exec_log (strategy_id, strategy_group_id, market_channel_id, exec_status) VALUES (...);

-- 6. 插入批次记录
INSERT INTO crowd_push_batch (batch_num, strategy_id, market_channel, batch_size) VALUES (...);

-- 7. 插入触达明细
INSERT INTO user_dispatch_detail (user_id, mobile, batch_num, strategy_id, market_channel) VALUES (...);

-- 8. 更新任务状态为成功
UPDATE dispatch_task SET status = 2, end_time = NOW() WHERE id = ?;
```

**离线-引擎触达数据操作时序**
```sql
-- 阶段一：任务生成阶段
-- 1. 查询有效策略
SELECT * FROM strategy WHERE type = 1 AND status IN (1,2) AND validity_begin <= NOW() AND validity_end >= NOW();

-- 2. 查询人群分片
SELECT * FROM crowd_slice WHERE crowd_id = ? AND crowd_version = ?;

-- 3. 批量插入分片任务
INSERT INTO strategy_slice_exec_log (strategy_id, crowd_slice_id, status, dispatch_time) VALUES (...);

-- 4. 更新策略状态为执行中
UPDATE strategy SET status = 2 WHERE id = ?;

-- 阶段二：分片执行阶段
-- 1. 查询待执行分片任务
SELECT * FROM strategy_slice_exec_log WHERE status = 0 AND dispatch_time <= NOW() LIMIT ?;

-- 2. Redis获取分布式锁
SETNX crowd_slice_exec_lock:{strategyId}:{crowdSliceId}:{versionNo} 1 EX 600;

-- 3. 更新任务状态为执行中
UPDATE strategy_slice_exec_log SET status = 1, start_time = NOW() WHERE id = ?;

-- 4. Redis统计进入引擎用户数
INCR into_engine_sum:{date}:{strategyId};

-- 5. 批量插入延迟任务
INSERT INTO dispatch_user_delay (strategy_id, user_id, market_channel, group_name, ext_info, dispatch_time, status) VALUES (...);

-- 6. Redis统计营销用户数(去重)
PFADD market_num:{date}:{strategyId} {userId};

-- 7. 更新分片任务状态为成功
UPDATE strategy_slice_exec_log SET status = 2, exec_user_cnt = ?, dispatch_cnt = ?, end_time = NOW() WHERE id = ?;

-- 8. 释放分布式锁
DEL crowd_slice_exec_lock:{strategyId}:{crowdSliceId}:{versionNo};

-- 阶段三：营销执行阶段
-- 1. 查询到期延迟任务
SELECT * FROM dispatch_user_delay WHERE status = 0 AND dispatch_time <= NOW() LIMIT ?;

-- 2. 流控检查 - 查询用户触达历史
SELECT COUNT(*) FROM user_dispatch_detail WHERE user_id = ? AND strategy_id = ? AND market_channel = ?;

-- 3. Redis流控计数检查
INCR user_dispatch_times:{userId}:{strategyId}:{marketChannel};

-- 4. 插入触达明细记录
INSERT INTO user_dispatch_detail (user_id, mobile, batch_num, strategy_id, market_channel, dispatch_time, status) VALUES (...);

-- 5. 插入批次记录
INSERT INTO event_push_batch (batch_num, strategy_id, user_id, mobile, market_channel, group_name) VALUES (...);

-- 6. 更新延迟任务状态为已执行
UPDATE dispatch_user_delay SET status = 1, update_time = NOW() WHERE id = ?;
```

## 3. 关键差异对比分析

### 3.1 架构设计差异

| 对比维度 | 离线-普通触达 | 离线-引擎触达 | 差异说明 |
|----------|---------------|---------------|----------|
| **策略类型** | type=0 普通策略 | type=1 引擎策略 | 策略配置层面的根本区别 |
| **任务调度** | dispatch_task表 | strategy_slice_exec_log表 | 不同的任务管理机制 |
| **数据处理** | 基于ID的分页查询 | 基于分片的范围读取 | 数据读取策略不同 |
| **决策机制** | 配置规则直接执行 | AI引擎智能决策 | 决策复杂度差异巨大 |
| **执行时机** | 立即执行 | 延迟执行 | 时间控制策略不同 |
| **并发控制** | 任务级别并发 | 分片级别分布式并发 | 并发粒度和复杂度不同 |

### 3.2 技术实现差异

#### 3.2.1 数据分片策略对比

**离线-普通触达分页策略**
```java
// 基于ID的游标分页，避免OFFSET性能问题
List<CrowdDetailDo> detailList = crowdDetailRepository
    .selectByIdPage(crowdDetailId, pageSize, crowdId, crowdExecLogId);
```

**离线-引擎触达分片策略**
```java
// 基于OSS文件的字节范围分片，支持大文件处理
List<long[]> crowdChuckPositions = FileUtil.calculateChunkPositionsLastOneMerge(
    fileSize, crowSliceChunkSize); // 1KB分片
```

#### 3.2.2 并发控制机制对比

**离线-普通触达并发控制**
- 任务级别的分片查询：`(id % total) == index`
- 无分布式锁机制
- 依赖数据库事务保证一致性

**离线-引擎触达并发控制**
- 分片级别的CRC32哈希路由：`(HashUtils.crc32Hash("" + x.getId()) % shardTotal) == shardIndex`
- Redis分布式锁防重：`SETNX crowd_slice_exec_lock:{strategyId}:{crowdSliceId}:{versionNo}`
- 多层并发控制保证数据一致性

#### 3.2.3 执行流程差异

**离线-普通触达执行流程**
```
任务生成 → 任务执行 → 人群查询 → AB分组 → 标签过滤 → dispatchHandler → 直接营销执行
```

**离线-引擎触达执行流程**
```
任务生成 → 分片执行 → 人群查询 → AB分组 → 标签过滤 → 引擎调用 → 延迟任务 → marketingSend → 营销执行
```

### 3.3 业务价值差异

#### 3.3.1 营销精准度对比

**离线-普通触达**
- 基于预设规则的批量营销
- 所有用户接收相同的营销内容
- 营销时机固定，无个性化控制

**离线-引擎触达**
- 基于AI引擎的个性化营销决策
- 每个用户可能接收不同的营销内容
- 营销时机智能优化，提升用户体验

#### 3.3.2 系统性能对比

**离线-普通触达性能特点**
- 处理流程简单，执行效率高
- 内存占用相对较低
- 适合大规模批量营销场景

**离线-引擎触达性能特点**
- 处理流程复杂，但支持更大规模数据
- 分布式架构，水平扩展能力强
- 引擎调用增加延迟，但提升营销效果

#### 3.3.3 可扩展性对比

**离线-普通触达扩展性**
- 渠道扩展：通过实现`dispatchHandler`抽象方法
- 规则扩展：通过修改AB测试分组规则
- 扩展成本低，开发周期短

**离线-引擎触达扩展性**
- 引擎扩展：支持多种AI模型接入
- 决策扩展：支持复杂的营销策略组合
- 时间扩展：支持灵活的延迟执行策略
- 扩展能力强，但开发复杂度高

### 3.4 适用场景分析

#### 3.4.1 离线-普通触达适用场景

**最佳适用场景**
- 大规模批量营销活动
- 营销内容相对固定的场景
- 对实时性要求不高的营销
- 系统资源有限的环境

**典型业务案例**
- 节日祝福短信群发
- 产品促销信息推送
- 系统通知类消息
- 定期的客户关怀

#### 3.4.2 离线-引擎触达适用场景

**最佳适用场景**
- 个性化营销需求强烈
- 营销效果要求较高
- 用户体验敏感的业务
- 有充足技术资源支持

**典型业务案例**
- 个性化产品推荐
- 智能营销时机选择
- 精准客户挽留
- 差异化服务推送

## 4. 系统优化建议

### 4.1 离线-普通触达优化方向

#### 4.1.1 性能优化建议
- **数据库优化**: 优化crowd_detail表的索引设计，提升分页查询性能
- **批次大小调优**: 根据系统负载动态调整批次大小，平衡吞吐量和响应时间
- **缓存策略**: 增加策略配置缓存，减少数据库查询次数
- **异步处理**: 将非关键路径的操作异步化，提升主流程执行效率

#### 4.1.2 功能增强建议
- **智能分组**: 引入简单的规则引擎，支持更灵活的用户分组
- **执行监控**: 增加详细的执行监控和告警机制
- **失败重试**: 完善失败重试机制，提升系统稳定性
- **数据统计**: 增强营销效果统计和分析能力

### 4.2 离线-引擎触达优化方向

#### 4.2.1 架构优化建议
- **引擎池化**: 实现引擎连接池，提升引擎调用效率
- **分片优化**: 根据数据分布特点优化分片策略，提升并行度
- **缓存层次**: 建立多层缓存体系，减少重复计算和查询
- **监控完善**: 完善分布式链路监控，快速定位性能瓶颈

#### 4.2.2 业务增强建议
- **引擎多样化**: 支持多种AI引擎并行决策，提升决策准确性
- **实时反馈**: 建立营销效果实时反馈机制，动态优化策略
- **A/B测试**: 增强A/B测试能力，支持引擎效果对比
- **智能调度**: 根据系统负载和业务优先级智能调度任务

### 4.3 统一优化建议

#### 4.3.1 技术架构统一
- **接口标准化**: 统一两种触达方式的接口规范，便于切换和对比
- **配置中心**: 建立统一的配置管理中心，支持动态配置调整
- **监控体系**: 建立统一的监控和告警体系，提升运维效率
- **日志规范**: 统一日志格式和级别，便于问题排查和分析

#### 4.3.2 业务流程优化
- **策略路由**: 建立智能策略路由机制，根据业务特点自动选择触达方式
- **效果对比**: 建立两种触达方式的效果对比机制，指导业务决策
- **资源调度**: 根据业务负载动态调度资源，提升系统整体效率
- **容灾机制**: 建立完善的容灾和降级机制，保障系统稳定性

## 5. 总结

### 5.1 核心差异总结

离线-普通触达和离线-引擎触达代表了两种不同的营销理念和技术实现：

**离线-普通触达**以简单高效为核心，适合大规模批量营销场景，通过`AbstractStrategyDispatchService.dispatchHandler`直接执行营销动作，具有处理流程简单、执行效率高、资源消耗低的特点。

**离线-引擎触达**以智能个性化为核心，适合精准营销场景，通过AI引擎决策后经`StrategyEventDispatchServiceImpl.marketingSend`执行营销，具有决策智能、个性化强、效果优化的特点。

### 5.2 技术架构总结

两种触达方式在技术架构上体现了不同的设计思路：

- **数据处理**: 普通触达采用基于ID的分页策略，引擎触达采用基于文件的分片策略
- **并发控制**: 普通触达依赖数据库事务，引擎触达采用分布式锁机制
- **执行时机**: 普通触达立即执行，引擎触达支持延迟执行
- **决策机制**: 普通触达基于配置规则，引擎触达基于AI智能决策

### 5.3 业务价值总结

两种触达方式各有其业务价值和适用场景：

**离线-普通触达**适合成本敏感、规模优先的营销场景，能够以较低的技术成本实现大规模用户触达，满足基础营销需求。

**离线-引擎触达**适合效果优先、体验敏感的营销场景，通过AI技术提升营销精准度和用户体验，实现营销效果的显著提升。

### 5.4 发展方向总结

未来的发展方向应该是两种触达方式的融合和互补：

- **技术融合**: 将引擎触达的智能决策能力引入普通触达，提升整体营销效果
- **场景互补**: 根据业务特点和资源状况，智能选择最适合的触达方式
- **架构统一**: 建立统一的技术架构和管理体系，降低维护成本
- **效果优化**: 通过数据分析和A/B测试，持续优化两种触达方式的效果

通过本次汇总分析，我们深入理解了离线触达系统的技术架构和业务逻辑，为后续的系统优化和业务发展提供了重要参考。

