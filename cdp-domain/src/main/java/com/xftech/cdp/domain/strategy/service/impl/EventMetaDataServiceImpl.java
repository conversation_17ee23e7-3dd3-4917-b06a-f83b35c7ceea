package com.xftech.cdp.domain.strategy.service.impl;

import com.alibaba.fastjson.JSON;
import com.xftech.cdp.domain.strategy.repository.EventMetaDataRepository;
import com.xftech.cdp.domain.strategy.service.EventMetaDataService;
import com.xftech.cdp.infra.constant.RedisKeyConstants;
import com.xftech.cdp.infra.repository.cdp.strategy.po.EventMetaDataDo;
import com.xftech.cdp.infra.utils.RedisUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @<NAME_EMAIL>
 * @date 2023/5/26 18:45
 */
@Service
public class EventMetaDataServiceImpl implements EventMetaDataService {

    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private EventMetaDataRepository eventMetaDataRepository;

    @Override
    public EventMetaDataDo selectByEventName(String eventName) {
        String subEventJson = redisUtils.hGet(RedisKeyConstants.STRATEGY_EVENT_META_DATA, eventName);
        if (StringUtils.isNotBlank(subEventJson)) {
            return JSON.parseObject(subEventJson, EventMetaDataDo.class);
        }
        EventMetaDataDo eventMetaDataDo = eventMetaDataRepository.selectByEventName(eventName);
        redisUtils.hPut(RedisKeyConstants.STRATEGY_EVENT_META_DATA, eventName, JSON.toJSONString(eventMetaDataDo), RedisUtils.DEFAULT_EXPIRE_HOUR);
        return eventMetaDataDo;
    }
}
