/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.adapter.strategy;

import com.google.common.base.Stopwatch;
import com.xftech.cdp.application.FlowHandler;
import com.xftech.cdp.domain.strategy.model.enums.DispatchTaskBizTypeEnum;
import com.xftech.cdp.infra.constant.XxlJobConstants;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.util.ShardingUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 *
 * <AUTHOR>
 * @version $ FlowJob, v 0.1 2023/12/24 14:16 yye.xu Exp $
 */

// 画布 Job
@Slf4j
@Service
@AllArgsConstructor
public class FlowJob {
    private final FlowHandler flowHandler;

    // 变更状态逻辑相对简单, 暂不拆分, 使用一个Job处理所有的任务状态变更形式;
    // 画布状态的变更 --->  已发布 -> 执行中;
    // 画布状态的变更 --->  (已发布,执行中,暂停) ---> 过期;
    @XxlJob(XxlJobConstants.FLOW_STATUS_CHANGED)
    public ReturnT<String> flowStatusChanged(String param) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            log.info("Xxl-Job 画布状态变改任务开始执行");
            flowHandler.flowStatusChanged();
        } finally {
            stopwatch.stop();
            log.info("Xxl-Job 画布状态变改任务成功, 总计耗时:{}", stopwatch.elapsed(TimeUnit.SECONDS));
        }
        return ReturnT.SUCCESS;
    }

    // 生成画布任务
    @XxlJob(XxlJobConstants.FLOW_DISPATCH_TASK_PRODUCER)
    public ReturnT<String> flowTaskProducer(String param) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            log.info("Xxl-Job 画布任务生成开始执行");
            flowHandler.flowTaskProducer();
        } finally {
            stopwatch.stop();
            log.info("Xxl-Job 画布任务生成结束, 总计耗时:{}", stopwatch.elapsed(TimeUnit.SECONDS));
        }
        return ReturnT.SUCCESS;
    }

    // 消费画布任务
    @XxlJob(XxlJobConstants.FLOW_DISPATCH_TASK_CONSUMER)
    public ReturnT<String> flowTaskConsumer(String param) {
        int total = ShardingUtil.getShardingVo().getTotal();
        int index = ShardingUtil.getShardingVo().getIndex();
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            log.info("Xxl-Job 画布策略执行开始执行, 总分片数:{}, 当前分片数:{}", total, index);
            flowHandler.flowTaskConsumer(total, index, DispatchTaskBizTypeEnum.FLOW
                    .getCode());
        } finally {
            stopwatch.stop();
            log.info("Xxl-Job 画布策略执行结束, 当前分片数:{}, 总计耗时:{}", index, stopwatch.elapsed(TimeUnit.SECONDS));
        }
        return ReturnT.SUCCESS;
    }
}