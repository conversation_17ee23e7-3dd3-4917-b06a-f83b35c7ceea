package com.xftech.cdp.domain.marketing.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.infra.repository.cdp.marketing.po.MarketingActivityInfoDo;

import org.springframework.stereotype.Repository;


/**
 * 营销活动信息表 marketing_activity_info dao
 */
@Repository
public class MarketingActivityInfoRepository {

    /**
     * 根据活动ID查询活动详情
     *
     * @param id 活动ID
     * @return
     */
    public MarketingActivityInfoDo selectById(Long id) {

        // TODO: 这里应该使用缓存, 避免频繁查询数据库

        return DBUtil.selectOne("marketingActivityInfo.selectById", id);
    }

}