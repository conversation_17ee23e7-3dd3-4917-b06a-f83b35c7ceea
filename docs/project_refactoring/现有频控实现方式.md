# 现有频控实现方式详细分析

## 1. 概述

根据触达类型的频控差异分析，当前系统存在4种不同的频控实现方式：

| 频控方法 | 使用场景 | 实现位置 | 控制粒度 | 存储方式 |
|---------|---------|---------|---------|---------|
| `isReject()` | T0触达（普通+引擎） | MqConsumeServiceImpl | 事件级 | Redis分布式锁 |
| `dispatchFlc()` | T0普通触达 | DispatchFlcService | 触达级 | 数据库查询 |
| `dispatchFlcLock()` | T0引擎触达 | DispatchFlcService | 分布式锁级 | Redis分布式锁 |
| `flowCtrl()` | 离线普通触达 | FlowCtrlCoreServiceImpl | 批量级 | 数据库查询 |

## 2. isReject() - 事件级流控

### 2.1 实现位置
- **类**: `cdp-domain/src/main/java/com/xftech/cdp/domain/mq/impl/MqConsumeServiceImpl.java`
- **方法**: `isReject(BizEventMessageVO bizEventMessageVO)`

### 2.2 详细实现逻辑

```java
private boolean isReject(BizEventMessageVO bizEventMessageVO) {
    try {
        Long userId = Optional.ofNullable(bizEventMessageVO.getCreditUserId())
                .orElse(bizEventMessageVO.getUser_id());
        
        if (StringUtils.isNotEmpty(bizEventMessageVO.getBizEventType()) 
            && userId != null && userId > 0) {
            
            // 1. 获取事件流控配置
            EventFlcConfig eventFlcConfig = appConfigService.getEventFlcConfig();
            if (eventFlcConfig != null) {
                // 2. 获取该事件类型的限制秒数
                Integer limitSeconds = eventFlcConfig.getLimitSeconds(bizEventMessageVO.getBizEventType());
                if (limitSeconds != null) {
                    if (limitSeconds <= 0) {
                        return true; // 配置为0或负数，直接拦截
                    }
                    
                    // 3. 构造Redis Key
                    String limitKey = String.format("eventflc:%s:%s", 
                        bizEventMessageVO.getBizEventType(), userId);
                    
                    // 4. 尝试获取分布式锁
                    boolean ret = redisUtils.lock(limitKey, "1", limitSeconds);
                    if (!ret) {
                        return true; // 获取锁失败，被流控拦截
                    }
                }
            }
        }
    } catch (Exception ex) {
        log.error("isReject", ex);
    }
    return false; // 通过流控检查
}
```

### 2.3 配置管理

#### 2.3.1 配置类结构
```java
@Data
public class EventFlcConfig {
    // 配置值，key:事件名, value: 多少秒1次
    private Map<String, Integer> eventFlcMap;
    
    @JsonIgnore
    @JSONField(serialize = false)
    public Integer getLimitSeconds(String eventName) {
        if (eventFlcMap != null && eventFlcMap.containsKey(eventName)) {
            return eventFlcMap.get(eventName);
        }
        return null;
    }
}
```

#### 2.3.2 Apollo配置示例
```properties
# 事件流控配置 - JSON格式
eventFlcConfig = {
    "Login": 60,           # 登录事件60秒内只能处理一次
    "ApplySuccess": 300,   # 申请成功事件300秒内只能处理一次
    "Start": 30            # 启动事件30秒内只能处理一次
}
```

### 2.4 流控机制特点

- **Redis Key格式**: `eventflc:{事件类型}:{用户ID}`
- **流控机制**: Redis分布式锁，锁定时间为配置的限制秒数
- **拦截逻辑**: 在限制时间内，同一用户的同一事件类型只能处理一次
- **异常处理**: 异常情况下默认通过流控检查

### 2.5 isReject() 流程图

```mermaid
flowchart TD
    A[MQ事件消息] --> B[MqConsumeServiceImpl.isReject]
    B --> C{事件类型和用户ID是否有效?}
    C -->|否| D[返回false - 通过流控]
    C -->|是| E[appConfigService.getEventFlcConfig]
    E --> F[Apollo配置查询]
    F --> G{获取到EventFlcConfig?}
    G -->|否| D
    G -->|是| H[eventFlcConfig.getLimitSeconds]
    H --> I{获取到限制秒数?}
    I -->|否| D
    I -->|是| J{limitSeconds <= 0?}
    J -->|是| K[返回true - 被流控拦截]
    J -->|否| L[构造Redis Key: eventflc:事件类型:用户ID]
    L --> M[redisUtils.lock尝试获取分布式锁]
    M --> N[(Redis)]
    N --> O{获取锁成功?}
    O -->|是| P[返回false - 通过流控]
    O -->|否| Q[返回true - 被流控拦截]

    style F fill:#e1f5fe
    style N fill:#ffecb3
    style K fill:#ffcdd2
    style Q fill:#ffcdd2
    style D fill:#c8e6c9
    style P fill:#c8e6c9
```

## 3. dispatchFlc() - 触达级流控

### 3.1 实现位置
- **类**: `cdp-domain/src/main/java/com/xftech/cdp/domain/dispatch/DispatchFlcService.java`
- **方法**: `dispatchFlc(BizEventVO bizEventVO, CrowdDetailDo crowdDetail, AbstractStrategyEventDispatchService abstractStrategyEventDispatchService)`

### 3.2 详细实现逻辑

```java
public boolean dispatchFlc(BizEventVO bizEventVO, CrowdDetailDo crowdDetail, 
                          AbstractStrategyEventDispatchService abstractStrategyEventDispatchService) {
    try {
        // 1. 获取渠道枚举
        StrategyMarketChannelEnum marketChannelEnum = 
            StrategyMarketChannelEnum.getInstance(bizEventVO.getMarketChannel());
        
        // 2. 检查流控开关
        boolean switchFlag = appConfigService.getSingleDispatchFlcSwitch(marketChannelEnum);
        if (!Objects.equals(true, switchFlag)) {
            log.info("dispatchFlc, 触达流控开关未打开, 策略id:{}, 用户id:{}, marketChannel = {}",
                    bizEventVO.getStrategyId(), bizEventVO.getAppUserId(), marketChannelEnum.getCode());
            return false; // 开关未打开，不进行流控
        }
        
        // 3. 获取渠道配置
        StrategyMarketChannelDo channelDo = strategyMarketChannelService
            .getByStrategyIdAndMarketChannel(bizEventVO.getStrategyId(), bizEventVO.getMarketChannel());
        
        // 4. 执行流控检查
        List<Integer> sucStatus = Arrays.asList(-1, 1); // 成功状态列表
        List<CrowdDetailDo> crowdDetailList = abstractStrategyEventDispatchService.flowCtrl(
            bizEventVO.getMessageId(),
            Optional.ofNullable(bizEventVO.getTriggerDatetime()).orElse(LocalDateTime.now()),
            channelDo, crowdDetail, sucStatus, bizEventVO.getBizEventType());
        
        // 5. 判断流控结果
        if (CollectionUtils.isEmpty(crowdDetailList)) {
            // 记录监控指标
            Tracer.logMetricForCount(String.format("dispatchFlc:%s", bizEventVO.getMarketChannel()));
            Tracer.logEvent(String.format("dispatchFlc:%s", bizEventVO.getMarketChannel()),
                    String.valueOf(bizEventVO.getStrategyId()));
            
            log.warn("触达流控, 用户已被流控拦截, 终止下发, 用户ID:{}, 渠道Id:{}, 策略id:{}, channelId:{}",
                    bizEventVO.getAppUserId(), bizEventVO.getMarketChannel(), 
                    bizEventVO.getStrategyId(), bizEventVO.getMarketChannelId());
            return true; // 被流控拦截
        }
        
        return false; // 通过流控检查
    } catch (Exception ex) {
        log.error("dispatchFlc", ex);
        return false; // 异常情况下通过流控
    }
}
```

### 3.3 Apollo配置

```properties
# 触达级流控开关配置
singleDispatchFlc.1 = false  # 短信频控开关
singleDispatchFlc.2 = false  # 电销频控开关  
singleDispatchFlc.3 = false  # 优惠券频控开关
singleDispatchFlc.4 = false  # 新电销频控开关
singleDispatchFlc.5 = false  # Push频控开关
singleDispatchFlc.6 = false  # AI Pronto频控开关
```

### 3.4 流控检查逻辑

触达级流控通过调用`abstractStrategyEventDispatchService.flowCtrl()`方法，该方法会：
1. 查询用户历史触达记录
2. 根据流控规则判断是否超限
3. 返回通过流控的用户列表（空列表表示被拦截）

### 3.5 dispatchFlc() 流程图

```mermaid
flowchart TD
    A[T0普通触达dispatch方法] --> B[DispatchFlcService.dispatchFlc]
    B --> C[获取StrategyMarketChannelEnum]
    C --> D[appConfigService.getSingleDispatchFlcSwitch]
    D --> E[Apollo配置查询]
    E --> F{流控开关是否打开?}
    F -->|否| G[记录日志: 开关未打开]
    G --> H[返回false - 通过流控]
    F -->|是| I[strategyMarketChannelService.getByStrategyIdAndMarketChannel]
    I --> J[(strategy_market_channel表)]
    J --> K[获取渠道配置StrategyMarketChannelDo]
    K --> L[abstractStrategyEventDispatchService.flowCtrl]
    L --> M[查询用户历史触达记录]
    M --> N[(user_dispatch_detail_X表)]
    N --> O[根据流控规则判断]
    O --> P{用户是否被流控?}
    P -->|是| Q[记录监控指标: dispatchFlc:渠道]
    Q --> R[记录告警事件]
    R --> S[记录warn日志: 用户被流控拦截]
    S --> T[返回true - 被流控拦截]
    P -->|否| U[返回false - 通过流控]

    style E fill:#e1f5fe
    style J fill:#ffecb3
    style N fill:#ffecb3
    style T fill:#ffcdd2
    style H fill:#c8e6c9
    style U fill:#c8e6c9
```

## 4. dispatchFlcLock() - 分布式流控

### 4.1 实现位置
- **类**: `cdp-domain/src/main/java/com/xftech/cdp/domain/dispatch/DispatchFlcService.java`
- **方法**: `dispatchFlcLock(DispatchDto reach, EventPushBatchDo eventPushBatch, UserDispatchDetailDo dispatchDetail, String batchNum, CrowdDetailDo crowdDetail, AbstractStrategyEventDispatchService abstractStrategyEventDispatchService)`

### 4.2 详细实现逻辑

```java
public boolean dispatchFlcLock(DispatchDto reach, EventPushBatchDo eventPushBatch, 
                              UserDispatchDetailDo dispatchDetail, String batchNum, 
                              CrowdDetailDo crowdDetail, AbstractStrategyEventDispatchService abstractStrategyEventDispatchService) {
    
    Long userId = crowdDetail.getUserId();
    Integer marketChannel = reach.getMarketChannel();
    Long strategyId = reach.getStrategyId();
    String messageId = reach.getMessageId();
    
    // 1. 检查分布式流控开关
    StrategyMarketChannelEnum channelEnum = StrategyMarketChannelEnum.getInstance(marketChannel);
    boolean isStartFlc = appConfigService.getSingleDispatchFlcLockSwitch(channelEnum);
    if (!isStartFlc) {
        // 开关未打开，保存数据但不流控
        userDispatchDetailService.saveEventDispatchDetail(reach, eventPushBatch, 
            dispatchDetail, batchNum, crowdDetail, Triple.of("-1", "failed", null));
        log.info("分布式流控:开关未打开, 不流控, userId = {}, channelEnum = {}", 
            userId, channelEnum.getDescription());
        return false;
    }
    
    // 2. 获取渠道配置
    StrategyMarketChannelDo channelDo = strategyMarketChannelService
        .getByStrategyIdAndMarketChannel(strategyId, marketChannel);
    if (channelDo == null) {
        log.warn("分布式流控:渠道配置为空, userId = {}, strategyId = {}, marketChannel = {}", 
            userId, strategyId, marketChannel);
        return false;
    }
    
    // 3. 获取分布式锁
    String lockValue = String.valueOf(System.currentTimeMillis());
    tryLock(userId, lockValue);
    
    try {
        // 4. 执行流控检查
        List<Integer> sucStatus = Arrays.asList(-1, 1);
        List<CrowdDetailDo> crowdDetailList = abstractStrategyEventDispatchService.flowCtrl(
            messageId, Optional.ofNullable(reach.getTriggerDatetime()).orElse(LocalDateTime.now()),
            channelDo, crowdDetail, sucStatus, reach.getBizEventType());
        
        // 5. 判断流控结果
        if (CollectionUtils.isEmpty(crowdDetailList)) {
            // 记录监控指标
            Tracer.logMetricForCount(String.format("dispatchFlcLock:%s", marketChannel));
            Tracer.logEvent(String.format("dispatchFlcLock:%s", marketChannel),
                    String.valueOf(strategyId));
            
            log.warn("分布式流控:用户已被流控拦截, userId = {}, strategyId = {}, marketChannel = {}", 
                userId, strategyId, marketChannel);
            return true; // 被流控拦截
        }
        
        return false; // 通过流控检查
    } finally {
        // 6. 释放分布式锁
        unLock(userId, lockValue);
    }
}
```

### 4.3 分布式锁实现

```java
private boolean tryLock(Long userId, String value) {
    boolean lockRet = false;
    final int lockSeconds = 60 * 6; // 锁定时间：6分钟
    final long sleepMills = appConfigService.getDispatchFlcLockPerWaitMills(); // 等待间隔
    String lockKey = getLockKey(userId); // 锁Key：dispatchFlcLock:{userId}
    boolean isIgnore = appConfigService.getSingleIgnoreDispatchFlcLockSwitch(); // 忽略锁开关
    
    do {
        try {
            if (isIgnore) {
                log.info("分布式流控:忽略加锁,直接返回成功, userId = {}", userId);
                return true;
            }
            
            // 尝试获取Redis分布式锁
            lockRet = redisUtils.lock(lockKey, value, lockSeconds);
            if (!lockRet) {
                // 获取锁失败，记录监控并等待重试
                Tracer.logMetricForCount("DispatchFlcTryLockAgain");
                Tracer.logEvent("DispatchFlcTryLockAgain", userId.toString());
                isIgnore = appConfigService.getSingleIgnoreDispatchFlcLockSwitch();
                ThreadUtil.sleep(sleepMills);
            }
        } catch (Exception ex) {
            log.error("分布式流控:加锁异常, userId = {}", userId, ex);
            isIgnore = appConfigService.getSingleIgnoreDispatchFlcLockSwitch();
        }
    } while (!lockRet);
    
    return true;
}

private void unLock(Long userId, String value) {
    String lockKey = getLockKey(userId);
    boolean isIgnore = appConfigService.getSingleIgnoreDispatchFlcLockSwitch();
    if (isIgnore) {
        return;
    }
    
    try {
        // 验证锁值后释放锁
        if (StringUtils.equalsIgnoreCase(value, redisUtils.get(lockKey))) {
            redisUtils.delete(lockKey);
        } else {
            Tracer.logEvent("DispatchFlcUnlockUserNotMatch", userId.toString());
        }
    } catch (Exception ex) {
        log.error("分布式流控:解锁异常, userId = {}", userId, ex);
        Tracer.logEvent("DispatchFlcUnlockError", userId.toString());
    }
}

private String getLockKey(Long userId) {
    return String.format("dispatchFlcLock:%s", userId);
}
```

### 4.4 Apollo配置

```properties
# 分布式流控开关配置
singleDispatchFlcLock.1 = true   # 短信分布式流控开关
singleDispatchFlcLock.2 = true   # 电销分布式流控开关
singleDispatchFlcLock.3 = true   # 优惠券分布式流控开关
singleDispatchFlcLock.4 = true   # 新电销分布式流控开关
singleDispatchFlcLock.5 = true   # Push分布式流控开关
singleDispatchFlcLock.6 = true   # AI Pronto分布式流控开关

# 分布式锁配置
dispatchFlcLockPerWaitMills = 100        # 等待重试间隔(毫秒)
singleIgnoreDispatchFlcLockSwitch = false # 忽略分布式锁开关

# 线程池配置
dispatchFlcExecutor.pool.coreSize = 8    # 核心线程数
dispatchFlcExecutor.pool.maxSize = 20    # 最大线程数
```

### 4.5 分布式锁特点

- **Redis Key格式**: `dispatchFlcLock:{用户ID}`
- **锁定时间**: 6分钟 (360秒)
- **重试机制**: 循环等待直到获取锁成功
- **异常处理**: 加锁/解锁异常时记录监控指标
- **锁验证**: 解锁时验证锁值，防止误解锁

### 4.6 dispatchFlcLock() 时序图

```mermaid
sequenceDiagram
    participant T as T0引擎触达
    participant D as DispatchFlcService
    participant A as AppConfigService
    participant S as StrategyMarketChannelService
    participant R as Redis
    participant DB as user_dispatch_detail_X
    participant U as UserDispatchDetailService
    participant M as 监控系统

    T->>D: dispatchFlcLock(reach, eventPushBatch, ...)
    D->>A: getSingleDispatchFlcLockSwitch(channelEnum)
    A-->>D: 返回开关状态

    alt 开关未打开
        D->>U: saveEventDispatchDetail(保存数据但不流控)
        U->>DB: INSERT触达记录
        D-->>T: 返回false(通过流控)
    else 开关已打开
        D->>S: getByStrategyIdAndMarketChannel
        S->>DB: SELECT渠道配置
        DB-->>S: 返回StrategyMarketChannelDo
        S-->>D: 返回渠道配置

        D->>D: tryLock(userId, lockValue)
        loop 循环获取锁
            D->>A: getSingleIgnoreDispatchFlcLockSwitch()
            A-->>D: 返回忽略锁开关
            alt 忽略锁开关打开
                D-->>D: 直接返回成功
            else 正常加锁流程
                D->>R: lock(dispatchFlcLock:userId, value, 360s)
                R-->>D: 返回加锁结果
                alt 加锁失败
                    D->>M: logMetricForCount("DispatchFlcTryLockAgain")
                    D->>D: sleep(等待间隔)
                end
            end
        end

        D->>D: abstractStrategyEventDispatchService.flowCtrl()
        D->>DB: SELECT用户历史触达记录
        DB-->>D: 返回用户触达指标
        D->>D: 判断是否超过流控限制

        alt 用户被流控
            D->>M: logMetricForCount("dispatchFlcLock:渠道")
            D->>M: logEvent("dispatchFlcLock:渠道", strategyId)
            D->>D: unLock(userId, lockValue)
            D->>R: 验证锁值并删除锁
            D-->>T: 返回true(被流控拦截)
        else 用户通过流控
            D->>D: unLock(userId, lockValue)
            D->>R: 验证锁值并删除锁
            D-->>T: 返回false(通过流控)
        end
    end
```

## 5. flowCtrl() - 批量流控

### 5.1 实现位置
- **类**: `cdp-domain/src/main/java/com/xftech/cdp/domain/flowctrl/service/impl/FlowCtrlCoreServiceImpl.java`
- **方法**: `flowCtrl(FlowCtrlDto flowCtrlDto, List<Integer> statusList)`

### 5.2 详细实现逻辑

```java
public List<CrowdDetailDo> flowCtrl(FlowCtrlDto flowCtrlDto, List<Integer> statusList) {
    // 1. 参数校验
    if (CollectionUtils.isEmpty(flowCtrlDto.getList()) ||
        CollectionUtils.isEmpty(flowCtrlDto.getFlowCtrlRuleList())) {
        log.info("流量控制-当前批次用户数：{}，流控规则：{}",
            flowCtrlDto.getList().size(), flowCtrlDto.getFlowCtrlRuleList());
        return flowCtrlDto.getList();
    }

    StrategyMarketChannelDo strategyMarketChannelDo = flowCtrlDto.getMarketChannelDo();
    log.info("流控规则执行: 策略id:{}, 流控规则的数量:{}",
        strategyMarketChannelDo == null ? 0 : strategyMarketChannelDo.getStrategyId(),
        flowCtrlDto.getFlowCtrlRuleList().size());

    long startTime = Instant.now().toEpochMilli();
    List<Long> passUserIdList = new ArrayList<>();

    // 2. 新旧流控切换逻辑
    Long strategyId = strategyMarketChannelDo.getStrategyId();
    if(WhitelistSwitchUtil.commonGraySwitchByApollo("" + strategyId, "newFlowCtrlSwitch")) {
        log.info("流控规则执行: 新流控 strategyId:{} marketChannel:{}",
            strategyId, strategyMarketChannelDo.getMarketChannel());
        passUserIdList = this.newExecute(flowCtrlDto, statusList);
    } else {
        passUserIdList = this.execute(flowCtrlDto, statusList);
    }

    // 3. 返回通过流控的用户
    Map<Long, CrowdDetailDo> detailMap = flowCtrlDto.getList().stream()
        .collect(Collectors.toMap(CrowdDetailDo::getUserId, item -> item));
    List<CrowdDetailDo> result = passUserIdList.stream()
        .map(detailMap::get).collect(Collectors.toList());

    log.info("流量控制-当前批次用户数：{}，拦截用户数：{}，当前明细表序号：{}，耗时：{}ms",
        flowCtrlDto.getList().size(), flowCtrlDto.getList().size() - result.size(),
        flowCtrlDto.getTableNo(), Instant.now().toEpochMilli() - startTime);

    return result;
}
```

### 5.3 新流控逻辑实现

```java
private List<Long> newExecute(FlowCtrlDto flowCtrlDto, List<Integer> statusList) {
    // 1. 获取当前渠道，不需要流控直接返回
    StrategyMarketChannelDo channelDo = flowCtrlDto.getMarketChannelDo();
    Integer marketChannel = channelDo.getMarketChannel();
    if (isNotFlowCtrlChannel(marketChannel)) {
        log.info("newExecute当前渠道不需要流控直接返回 strategyId:{} marketChannel:{}",
            channelDo.getStrategyId(), marketChannel);
        return Collections.emptyList();
    }

    // 2. 初始化结果集
    List<Long> resultList = flowCtrlDto.getList().stream()
        .map(CrowdDetailDo::getUserId)
        .collect(Collectors.toCollection(CopyOnWriteArrayList::new));

    // 3. 遍历流控规则
    for (FlowCtrlDo flowCtrlDo : flowCtrlDto.getFlowCtrlRuleList()) {
        List<ImmutablePair<Long, Long>> flowCtrlRefuseList = new ArrayList<>();
        if (CollectionUtils.isEmpty(resultList)) {
            continue;
        }

        // 4. 查询用户触达指标
        Long strategyId = flowCtrlDto.getMarketChannelDo().getStrategyId();
        List<UserDispatchIndexDto> dispatchIndexList = userDispatchDetailService
            .getUserIndexNew(flowCtrlDto.getTableNo(),
                Triple.of(strategyId, marketChannel, flowCtrlDo),
                resultList, statusList);

        // 5. 获取被拦截的用户
        dispatchIndexList = dispatchIndexList.stream()
            .filter(index -> this.interception(flowCtrlDo, index))
            .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(dispatchIndexList)) {
            for (UserDispatchIndexDto index : dispatchIndexList) {
                // 把被流控的用户添加到流控集合
                flowCtrlRefuseList.add(ImmutablePair.of(flowCtrlDo.getId(), index.getUserId()));
                // 从结果集中移除被流控的用户
                resultList.remove(index.getUserId());
            }
            // 保存拦截日志
            this.saveInterceptionLogNew(flowCtrlDto, flowCtrlRefuseList);
        }
    }

    return resultList;
}
```

### 5.4 旧流控逻辑实现

```java
private List<Long> execute(FlowCtrlDto flowCtrlDto, List<Integer> statusList) {
    List<Long> resultList = flowCtrlDto.getList().stream()
        .map(CrowdDetailDo::getUserId)
        .collect(Collectors.toCollection(CopyOnWriteArrayList::new));

    for (FlowCtrlDo flowCtrlDo : flowCtrlDto.getFlowCtrlRuleList()) {
        List<ImmutablePair<Long, Long>> flowCtrlRefuseList = new ArrayList<>();
        if (CollectionUtils.isEmpty(resultList)) {
            continue;
        }

        // 查询用户触达指标
        List<UserDispatchIndexDto> dispatchIndexList = userDispatchDetailService
            .getUserIndex(flowCtrlDto.getTableNo(), flowCtrlDo, resultList, statusList);

        // 获取被拦截的用户
        dispatchIndexList = dispatchIndexList.stream()
            .filter(index -> this.interception(flowCtrlDo, index))
            .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(dispatchIndexList)) {
            for (UserDispatchIndexDto index : dispatchIndexList) {
                flowCtrlRefuseList.add(ImmutablePair.of(flowCtrlDo.getId(), index.getUserId()));
                resultList.remove(index.getUserId());
            }
            // 保存拦截日志
            this.saveInterceptionLog(flowCtrlDto, flowCtrlRefuseList);
        }
    }

    return resultList;
}
```

### 5.5 流控拦截判断逻辑

```java
private boolean interception(FlowCtrlDo rule, UserDispatchIndexDto indexDto) {
    // 简单的次数限制判断：用户触达次数 >= 规则限制次数
    return Objects.nonNull(rule.getLimitTimes()) && indexDto.getCount() >= rule.getLimitTimes();
}
```

### 5.6 Apollo配置

```properties
# 新旧流控切换配置
newFlowCtrlSwitch.{strategyId} = true    # 策略级别的新流控开关

# 离线引擎过滤开关
offlineEngineFilterSwitch.{strategyId} = false

# 幂等校验开关
distributeOfflineDuplicateFilterEnable = true
```

### 5.7 flowCtrl() 批量流控流程图

```mermaid
flowchart TD
    A[离线普通触达批处理] --> B[FlowCtrlCoreServiceImpl.flowCtrl]
    B --> C{用户列表和流控规则是否为空?}
    C -->|是| D[返回原用户列表]
    C -->|否| E[WhitelistSwitchUtil.commonGraySwitchByApollo]
    E --> F[Apollo配置查询: newFlowCtrlSwitch]
    F --> G{是否启用新流控?}

    G -->|是| H[newExecute - 新流控逻辑]
    G -->|否| I[execute - 旧流控逻辑]

    subgraph "新流控逻辑"
        H --> J[isNotFlowCtrlChannel检查]
        J --> K{当前渠道需要流控?}
        K -->|否| L[返回空列表]
        K -->|是| M[遍历流控规则FlowCtrlDo]
        M --> N[userDispatchDetailService.getUserIndexNew]
        N --> O[(user_dispatch_detail_X表)]
        O --> P[查询用户触达指标]
        P --> Q[interception判断是否拦截]
        Q --> R{用户触达次数 >= 限制次数?}
        R -->|是| S[添加到拦截列表]
        S --> T[从结果集移除用户]
        T --> U[saveInterceptionLogNew]
        U --> V[(flow_ctrl_interception_log表)]
        V --> W[记录拦截日志]
        R -->|否| X[用户通过流控]
    end

    subgraph "旧流控逻辑"
        I --> Y[遍历流控规则FlowCtrlDo]
        Y --> Z[userDispatchDetailService.getUserIndex]
        Z --> AA[(user_dispatch_detail_X表)]
        AA --> BB[查询用户触达指标]
        BB --> CC[interception判断是否拦截]
        CC --> DD{用户触达次数 >= 限制次数?}
        DD -->|是| EE[添加到拦截列表]
        EE --> FF[从结果集移除用户]
        FF --> GG[saveInterceptionLog]
        GG --> HH[(flow_ctrl_interception_log表)]
        HH --> II[记录拦截日志]
        DD -->|否| JJ[用户通过流控]
    end

    H --> KK[返回通过流控的用户列表]
    I --> KK
    KK --> LL[记录性能日志: 批次用户数、拦截数、耗时]
    LL --> MM[返回CrowdDetailDo列表]

    style F fill:#e1f5fe
    style O fill:#ffecb3
    style V fill:#ffecb3
    style AA fill:#ffecb3
    style HH fill:#ffecb3
    style S fill:#ffcdd2
    style EE fill:#ffcdd2
    style D fill:#c8e6c9
    style L fill:#c8e6c9
    style MM fill:#c8e6c9
```

## 6. 相关数据库表结构

### 6.1 flow_ctrl - 流控规则表

```sql
CREATE TABLE flow_ctrl (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255),                  -- 规则名称
    description VARCHAR(500),           -- 规则描述
    status INT,                         -- 规则状态：0-初始化，1-生效中，2-已关闭
    type INT,                           -- 规则类型：1-策略，2-渠道，3-多策略，4-业务线
    strategy_type INT,                  -- 策略类型：1-离线策略，2-事件策略
    effective_strategy VARCHAR(500),    -- 生效策略范围：0-全部，多个逗号分隔
    effective_channel VARCHAR(500),     -- 生效渠道范围：0-全部，1-短信，2-电销，多个逗号分隔
    day_count INT,                      -- 日限制次数
    week_count INT,                     -- 周限制次数
    month_count INT,                    -- 月限制次数
    limit_days INT,                     -- 限制天数
    limit_times INT,                    -- 限制次数
    priority INT,                       -- 优先级：1-渠道规则特殊，2-渠道规则兜底，3-策略规则特殊，4-策略规则兜底
    biz_type VARCHAR(50),               -- 业务类型
    created_time DATETIME,              -- 创建时间
    updated_time DATETIME,              -- 更新时间
    d_flag TINYINT DEFAULT 0            -- 删除标记
);
```

**关键字段说明**：
- `type`: 规则类型，决定流控的应用范围
- `effective_strategy`: 生效策略范围，支持多策略配置
- `effective_channel`: 生效渠道范围，支持多渠道配置
- `limit_times`: 核心限制次数字段，用于流控判断
- `limit_days`: 限制天数，与limit_times配合使用

### 6.2 user_dispatch_detail_{tableNo} - 用户触达明细表

```sql
CREATE TABLE user_dispatch_detail_1 (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    strategy_id BIGINT,                 -- 策略ID
    strategy_channel_id BIGINT,         -- 策略渠道ID
    crowd_pack_id BIGINT,               -- 人群包ID
    exec_log_id BIGINT,                 -- 执行日志ID
    market_channel SMALLINT,            -- 营销渠道：1-短信，2-电销，3-优惠券等
    strategy_exec_id BIGINT,            -- 策略执行ID
    batch_num VARCHAR(50),              -- 批次号
    user_id BIGINT,                     -- 用户ID
    mobile VARCHAR(20),                 -- 手机号
    status SMALLINT,                    -- 状态：-1-失败，1-成功
    dispatch_time DATETIME,             -- 触达时间
    used_status SMALLINT,               -- 使用状态
    message_id VARCHAR(100),            -- 消息ID
    trigger_datetime DATETIME,          -- 触发时间
    group_name VARCHAR(100),            -- 分组名称
    template_id VARCHAR(50),            -- 模板ID
    strategy_group_id BIGINT,           -- 策略组ID
    strategy_group_name VARCHAR(100),   -- 策略组名称
    ext_detail TEXT,                    -- 扩展详情
    dispatch_type VARCHAR(20),          -- 触达类型
    biz_type VARCHAR(50),               -- 业务类型
    biz_event_type VARCHAR(50),         -- 业务事件类型
    created_time DATETIME,              -- 创建时间
    updated_time DATETIME               -- 更新时间
);

-- 关键索引
CREATE INDEX idx_user_strategy_channel_time ON user_dispatch_detail_1
    (user_id, strategy_id, market_channel, created_time);
CREATE INDEX idx_strategy_channel_time ON user_dispatch_detail_1
    (strategy_id, market_channel, created_time);
```

**关键字段说明**：
- `user_id`: 用户ID，流控的核心维度
- `strategy_id`: 策略ID，用于策略级流控
- `market_channel`: 营销渠道，用于渠道级流控
- `status`: 触达状态，流控查询时过滤成功记录
- `created_time`: 创建时间，用于时间范围流控

### 6.3 flow_ctrl_interception_log - 流控拦截日志表

```sql
CREATE TABLE flow_ctrl_interception_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT,                     -- 被拦截的用户ID
    flow_ctrl_id BIGINT,                -- 流控规则ID
    strategy_id BIGINT,                 -- 策略ID
    strategy_channel_id BIGINT,         -- 策略渠道ID
    market_channel SMALLINT,            -- 营销渠道
    interception_time DATETIME,         -- 拦截时间
    message_id VARCHAR(100),            -- 消息ID
    trigger_datetime DATETIME,          -- 触发时间
    biz_event_type VARCHAR(50),         -- 业务事件类型
    new_flag SMALLINT,                  -- 新流控标记：0-旧流控，1-新流控
    created_time DATETIME,              -- 创建时间
    updated_time DATETIME               -- 更新时间
);

-- 关键索引
CREATE INDEX idx_flow_ctrl_user_time ON flow_ctrl_interception_log
    (flow_ctrl_id, user_id, created_time);
CREATE INDEX idx_strategy_channel_time ON flow_ctrl_interception_log
    (strategy_id, market_channel, created_time);
```

**关键字段说明**：
- `flow_ctrl_id`: 关联的流控规则ID
- `new_flag`: 区分新旧流控逻辑的拦截记录
- `interception_time`: 拦截发生的时间

### 6.4 数据库表关系图

```mermaid
erDiagram
    flow_ctrl ||--o{ flow_ctrl_interception_log : "规则ID关联"
    user_dispatch_detail_1 ||--o{ flow_ctrl_interception_log : "用户触达记录"

    flow_ctrl {
        bigint id PK
        varchar name "规则名称"
        int status "规则状态"
        int type "规则类型"
        varchar effective_strategy "生效策略"
        varchar effective_channel "生效渠道"
        int limit_times "限制次数"
        int limit_days "限制天数"
        datetime created_time
    }

    user_dispatch_detail_1 {
        bigint id PK
        bigint strategy_id "策略ID"
        bigint user_id "用户ID"
        smallint market_channel "营销渠道"
        smallint status "触达状态"
        datetime created_time "创建时间"
        varchar message_id "消息ID"
        varchar biz_event_type "业务事件类型"
    }

    flow_ctrl_interception_log {
        bigint id PK
        bigint user_id "被拦截用户ID"
        bigint flow_ctrl_id FK "流控规则ID"
        bigint strategy_id "策略ID"
        smallint market_channel "营销渠道"
        datetime interception_time "拦截时间"
        smallint new_flag "新旧流控标记"
    }
```

## 7. 监控指标和日志

### 7.1 监控指标

#### 7.1.1 事件级流控监控
```java
// 事件流控拦截计数
// 指标名称格式：RejectT0Event
// 事件详情：事件类型 + 用户ID
```

#### 7.1.2 触达级流控监控
```java
// 触达流控拦截计数
Tracer.logMetricForCount(String.format("dispatchFlc:%s", marketChannel));
// 触达流控拦截事件
Tracer.logEvent(String.format("dispatchFlc:%s", marketChannel), String.valueOf(strategyId));
```

#### 7.1.3 分布式流控监控
```java
// 分布式流控拦截计数
Tracer.logMetricForCount(String.format("dispatchFlcLock:%s", marketChannel));
// 分布式流控拦截事件
Tracer.logEvent(String.format("dispatchFlcLock:%s", marketChannel), String.valueOf(strategyId));

// 分布式锁重试监控
Tracer.logMetricForCount("DispatchFlcTryLockAgain");
Tracer.logEvent("DispatchFlcTryLockAgain", userId.toString());

// 分布式锁异常监控
Tracer.logEvent("DispatchFlcUnlockUserNotMatch", userId.toString());
Tracer.logEvent("DispatchFlcUnlockError", userId.toString());
```

#### 7.1.4 批量流控监控
```java
// 批量流控性能监控
log.info("流量控制-当前批次用户数：{}，拦截用户数：{}，当前明细表序号：{}，耗时：{}ms",
    totalUsers, blockedUsers, tableNo, duration);
```

### 7.2 关键日志记录

#### 7.2.1 事件级流控日志
```java
log.info("开始进行事件流控逻辑, userId:{}, 事件名称:{}", userId, eventType);
// 拦截时无特定日志，通过返回值判断
```

#### 7.2.2 触达级流控日志
```java
log.info("dispatchFlc, 触达流控开关未打开, 策略id:{}, 用户id:{}, marketChannel = {}",
    strategyId, userId, marketChannel);
log.warn("触达流控, 用户已被流控拦截, 终止下发, 用户ID:{}, 渠道Id:{}, 策略id:{}, channelId:{}",
    userId, marketChannel, strategyId, channelId);
```

#### 7.2.3 分布式流控日志
```java
log.info("分布式流控:开始触发, userId = {}, strategyId = {}, channelEnum = {}",
    userId, strategyId, channelEnum.getDescription());
log.info("分布式流控:开关未打开, 不流控, userId = {}, channelEnum = {}",
    userId, channelEnum.getDescription());
log.warn("分布式流控:用户已被流控拦截, userId = {}, strategyId = {}, marketChannel = {}",
    userId, strategyId, marketChannel);
```

#### 7.2.4 批量流控日志
```java
log.info("流控规则执行: 策略id:{}, 流控规则的数量:{}", strategyId, ruleCount);
log.info("流控规则执行: 新流控 strategyId:{} marketChannel:{}", strategyId, marketChannel);
log.info("流量控制-当前批次用户数：{}，拦截用户数：{}，当前明细表序号：{}，耗时：{}ms",
    totalUsers, blockedUsers, tableNo, duration);
```

## 8. 频控方法综合对比

### 8.1 四种频控方法时序对比图

```mermaid
sequenceDiagram
    participant MQ as MQ消息
    participant T0N as T0普通触达
    participant T0E as T0引擎触达
    participant OFF as 离线普通触达
    participant Redis as Redis
    participant Apollo as Apollo配置
    participant DB as 数据库表
    participant Monitor as 监控系统

    Note over MQ,Monitor: 1. isReject() - 事件级流控 (T0触达共用)
    MQ->>T0N: 事件消息
    MQ->>T0E: 事件消息
    T0N->>Apollo: 获取EventFlcConfig
    T0E->>Apollo: 获取EventFlcConfig
    T0N->>Redis: lock(eventflc:事件:用户ID)
    T0E->>Redis: lock(eventflc:事件:用户ID)
    Redis-->>T0N: 锁结果
    Redis-->>T0E: 锁结果

    Note over MQ,Monitor: 2. dispatchFlc() - 触达级流控 (仅T0普通)
    T0N->>Apollo: getSingleDispatchFlcSwitch
    T0N->>DB: 查询strategy_market_channel
    T0N->>DB: 查询user_dispatch_detail_X
    T0N->>Monitor: 记录dispatchFlc指标

    Note over MQ,Monitor: 3. dispatchFlcLock() - 分布式流控 (仅T0引擎)
    T0E->>Apollo: getSingleDispatchFlcLockSwitch
    T0E->>Redis: lock(dispatchFlcLock:用户ID, 360s)
    T0E->>DB: 查询user_dispatch_detail_X
    T0E->>Redis: unlock(dispatchFlcLock:用户ID)
    T0E->>Monitor: 记录dispatchFlcLock指标

    Note over MQ,Monitor: 4. flowCtrl() - 批量流控 (仅离线普通)
    OFF->>Apollo: newFlowCtrlSwitch检查
    OFF->>DB: 批量查询user_dispatch_detail_X
    OFF->>DB: 批量写入flow_ctrl_interception_log
    OFF->>Monitor: 记录批量流控性能指标
```

### 8.2 频控实现特点对比

| 频控方法 | 存储方式 | 检查粒度 | 性能特点 | 适用场景 |
|---------|---------|---------|---------|---------|
| `isReject()` | Redis分布式锁 | 事件+用户 | 高性能，实时 | T0实时事件去重 |
| `dispatchFlc()` | 数据库查询 | 用户历史记录 | 中等性能 | T0普通触达频控 |
| `dispatchFlcLock()` | Redis锁+数据库 | 用户历史记录 | 中等性能，有锁开销 | T0引擎触达频控 |
| `flowCtrl()` | 数据库批量查询 | 批量用户 | 批量优化，高吞吐 | 离线批量触达频控 |

### 8.2 主要问题

1. **实现分散**: 4种不同的频控实现，维护复杂
2. **逻辑重复**: dispatchFlc()和dispatchFlcLock()有重复的流控检查逻辑
3. **配置复杂**: 多套不同的Apollo配置管理
4. **监控分散**: 不同的监控指标和日志格式
5. **扩展困难**: 新增频控类型需要修改多处代码

### 8.3 频控决策流程综合图

```mermaid
flowchart TD
    A[触达请求] --> B{触达类型判断}

    B -->|T0普通触达| C[isReject事件级流控]
    B -->|T0引擎触达| D[isReject事件级流控]
    B -->|离线普通触达| E[flowCtrl批量流控]
    B -->|离线引擎触达| F[无频控检查]

    C --> G{事件流控通过?}
    G -->|否| H[拒绝触达]
    G -->|是| I[dispatchFlc触达级流控]
    I --> J{触达流控通过?}
    J -->|否| H
    J -->|是| K[执行触达]

    D --> L{事件流控通过?}
    L -->|否| H
    L -->|是| M[引擎决策]
    M --> N[marketingSend方法]
    N --> O[dispatchFlcLock分布式流控]
    O --> P{分布式流控通过?}
    P -->|否| H
    P -->|是| K

    E --> Q[新旧流控切换判断]
    Q --> R[批量用户流控检查]
    R --> S[返回通过流控的用户]
    S --> T[批量执行触达]

    F --> U[直接执行触达]

    subgraph "存储操作"
        V[(Redis)]
        W[(Apollo配置)]
        X[(user_dispatch_detail_X)]
        Y[(flow_ctrl)]
        Z[(flow_ctrl_interception_log)]
    end

    C -.-> V
    C -.-> W
    I -.-> W
    I -.-> X
    O -.-> V
    O -.-> X
    R -.-> W
    R -.-> X
    R -.-> Y
    R -.-> Z

    style H fill:#ffcdd2
    style K fill:#c8e6c9
    style T fill:#c8e6c9
    style U fill:#c8e6c9
    style V fill:#fff3e0
    style W fill:#e8f5e8
    style X fill:#e3f2fd
    style Y fill:#e3f2fd
    style Z fill:#e3f2fd
```

### 8.4 重构建议

1. **统一接口**: 设计统一的频控服务接口
2. **策略模式**: 使用策略模式支持不同的频控实现
3. **配置统一**: 统一频控配置管理和Apollo配置
4. **监控统一**: 统一监控指标和日志格式
5. **向下兼容**: 保持现有频控逻辑的兼容性
