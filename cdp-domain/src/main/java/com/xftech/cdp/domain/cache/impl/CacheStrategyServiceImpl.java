package com.xftech.cdp.domain.cache.impl;

import com.xftech.base.common.util.LogUtil;
import com.xftech.base.log.IUdpLogger;
import com.xftech.cdp.domain.cache.*;
import com.xftech.cdp.domain.crowd.repository.CrowdExecLogRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdPackRepository;
import com.xftech.cdp.domain.flowctrl.repository.FlowCtrlRepository;
import com.xftech.cdp.domain.strategy.model.enums.StrategyRulerEnum;
import com.xftech.cdp.domain.strategy.repository.*;
import com.xftech.cdp.domain.subtable.service.SplitTableService;
import com.xftech.cdp.infra.repository.cdp.flowctrl.po.FlowCtrlDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyGroupDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketChannelDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketEventDo;
import com.xftech.cdp.infra.utils.RedisKeyUtils;
import com.xftech.cdp.infra.utils.RedisUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 策略表操作
 */
@Service
public class CacheStrategyServiceImpl implements CacheStrategyService {

    private static final IUdpLogger logger = LogUtil.getLogger(CacheStrategyService.class);

    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private StrategyRepository strategyRepository;
    @Autowired
    private StrategyMarketEventRepository strategyMarketEventRepository;
    @Autowired
    private StrategyMarketEventConditionRepository strategyMarketEventConditionRepository;
    @Autowired
    private StrategyMarketSubEventRepository strategyMarketSubEventRepository;
    @Autowired
    private CacheStrategyGroupService cacheStrategyGroupService;
    @Autowired
    private StrategyMarketChannelRepository strategyMarketChannelRepository;
    @Autowired
    private FlowCtrlRepository flowCtrlRepository;
    @Autowired
    private CacheStrategyMarketEventService cacheStrategyMarketEventService;
    @Autowired
    private CacheStrategyMarketSubEventService cacheStrategyMarketSubEventService;
    @Autowired
    private CrowdPackRepository crowdPackRepository;
    @Autowired
    private CrowdExecLogRepository crowdExecLogRepository;
    @Autowired
    private SplitTableService splitTableService;
    @Autowired
    private CacheStrategyMarketEventConditionService cacheStrategyMarketEventConditionService;
    @Autowired
    private StrategyLocalCacheService strategyLocalCacheService;

    /**
     * 根据主键id查询某条策略
     * 250403 新增本地缓存
     * @param strategyId 策略id
     * @return id对应的记录
     */
    public StrategyDo selectById(Long strategyId) {
        if (strategyId == null) {
            return null;
        }
        try {
            StrategyDo strategyDo = strategyLocalCacheService.getStrategyDo(strategyId);
            if (strategyDo != null) {
                return strategyDo;
            }
            String redisKey = RedisKeyUtils.genStrategyIdKey(strategyId);
            strategyDo = redisUtils.get(redisKey, StrategyDo.class);
            if (strategyDo == null) {
                strategyDo = strategyRepository.selectById(strategyId);
                redisUtils.set(redisKey, strategyDo, RedisUtils.DEFAULT_EXPIRE_SECONDS);
            }
            strategyLocalCacheService.refreshStrategy(strategyId, strategyDo);
            return strategyDo;
        } catch (Exception e) {
            logger.error("CacheStrategyServiceImpl-selectById {} Exception", strategyId, e);
        }
        return null;
    }

    public StrategyDo selectByIdNoCache(Long strategyId) {
        return strategyRepository.selectById(strategyId);
    }

    /**
     * 根据主键id删除某条策略
     *
     * @param strategyId 策略主键id
     * @return 是否删除成功标识
     */
//    public boolean delete(Long strategyId) {
//        boolean isDelete = strategyRepository.delete(strategyId);
//        if (isDelete) {
//            this.delOneKey(strategyId);
//        }
//        return isDelete;
//    }

    /**
     * 根据主键id更新某条策略记录
     *
     * @param updateStrategy 策略对象
     * @return 是否更新成功标识
     */
    public boolean updateById(StrategyDo updateStrategy) {
        boolean isUpdate = strategyRepository.updateById(updateStrategy);
        if (isUpdate && StrategyRulerEnum.EVENT.getCode() == updateStrategy.getSendRuler()) {
            refreshStrategy(updateStrategy.getId(), updateStrategy);

            List<StrategyMarketEventDo> strategyMarketEventDo = strategyMarketEventRepository.selectByStrategyId(updateStrategy.getId());
            if (CollectionUtils.isNotEmpty(strategyMarketEventDo)) {
                strategyMarketEventDo.forEach(t->{
                    cacheStrategyMarketEventService.refreshStrategyMarketEventCache(t.getEventName());
                });

            }
        }
        return isUpdate;
    }

    public StrategyDo refreshStrategy(Long strategyId, StrategyDo strategyDo) {
        String redisKey = RedisKeyUtils.genStrategyIdKey(strategyId);
        redisUtils.set(redisKey, strategyDo, RedisUtils.DEFAULT_EXPIRE_SECONDS);
        return strategyDo;
    }

    public void delCacheStrategy(Long strategyId) {
        String redisKey = RedisKeyUtils.genStrategyIdKey(strategyId);
        redisUtils.delete(redisKey);
    }

    @Override
    public boolean insert(StrategyDo strategyDo) {
        boolean insert = strategyRepository.insert(strategyDo);
        if (insert) {
            refreshStrategy(strategyDo.getId(), strategyDo);
        }
        return insert;
    }

    public void batchUpdate(List<StrategyDo> list) {
        strategyRepository.batchUpdate(list);
    }

//    private void delOneKey(Long id) {
//        String redisKey = String.format(RedisKeyConstants.STRATEGY_ONE, id);
//        if (redisUtils.hasKey(redisKey)) {
//            redisUtils.delete(redisKey);
//        }
//    }


    public void refreshT0ExecutingStrategy() {

        List<StrategyDo> strategyDoList = strategyRepository.listT0ExecutingStrategy();
        List<StrategyMarketEventDo> smeList = new ArrayList<>(strategyDoList.size() * 2);
        if (CollectionUtils.isNotEmpty(strategyDoList)) {
            for (StrategyDo strategyDo : strategyDoList) {

                refreshStrategy(strategyDo.getId(), strategyDo);

                smeList.addAll(strategyMarketEventRepository.selectByStrategyId(strategyDo.getId()));

                cacheStrategyMarketEventConditionService.updateEventConditionCache(strategyDo.getId());

                List<StrategyGroupDo> strategyGroupDoList = cacheStrategyGroupService.updateStrategyGroupCache(strategyDo.getId());

                strategyGroupDoList.stream().forEach(strategyGroupDo -> {
                    redisUtils.set(RedisKeyUtils.genStrategyGroupOneKey(strategyGroupDo.getId()), strategyGroupDo, RedisUtils.DEFAULT_EXPIRE_SECONDS);

                    List<StrategyMarketChannelDo> marketChannelList = strategyMarketChannelRepository.selectByStrategyGroupId((strategyGroupDo.getId()));
                    redisUtils.set(RedisKeyUtils.genListByStrategyGroupIdKey(strategyGroupDo.getId()), marketChannelList, RedisUtils.DEFAULT_EXPIRE_SECONDS);

                    marketChannelList.stream().forEach(marketChannelInfo -> {
                        StrategyMarketChannelDo strategyMarketChannelDo = strategyMarketChannelRepository.selectByStrategyGroupIdAndChannel(strategyGroupDo.getId(), marketChannelInfo.getMarketChannel());
                        redisUtils.set(RedisKeyUtils.genOneByGroupKey(strategyGroupDo.getId(), marketChannelInfo.getMarketChannel()), strategyMarketChannelDo, RedisUtils.DEFAULT_EXPIRE_SECONDS);

                        List<FlowCtrlDo> flowCtrlDoList = flowCtrlRepository.getFlowCtrlConfig(marketChannelInfo.getMarketChannel(), strategyDo.getId());
                        redisUtils.set(RedisKeyUtils.genFcListByStrategyIdKey(strategyDo.getId(), marketChannelInfo.getMarketChannel()), flowCtrlDoList, RedisUtils.DEFAULT_EXPIRE_SECONDS);

                    });

                });

                List<StrategyMarketChannelDo> strategyMarketChannelDoList = strategyMarketChannelRepository.selectByStrategyId(strategyDo.getId());
                redisUtils.set(RedisKeyUtils.genListByStrategyIdKey(strategyDo.getId()), strategyMarketChannelDoList, RedisUtils.DEFAULT_EXPIRE_SECONDS);


            }
        }
        if (CollectionUtils.isNotEmpty(smeList)) {
            Map<String, List<StrategyMarketEventDo>> map = smeList.stream().collect(Collectors.groupingBy(t -> t.getEventName()));
            for (String key : map.keySet()) {
                cacheStrategyMarketEventService.refreshStrategyMarketEventCache(key, map.get(key));
            }

            smeList.stream().forEach(marketEvent -> {
                cacheStrategyMarketSubEventService.refreshCacheListByEventId(marketEvent.getId());
            });

        }

//        List<CrowdPackDo> crowdPackDoList = crowdPackRepository.refreshT0CrowdPack();
//        if (CollectionUtils.isNotEmpty(crowdPackDoList)) {
//            for (CrowdPackDo crowdPackDo : crowdPackDoList) {
                // 缓存人群包信息
//                redisUtils.set(RedisKeyUtils.genCrowdPackKey(crowdPackDo.getId()), crowdPackDo, RedisUtils.DEFAULT_EXPIRE_MONTH);

//                Map<String, Object> execLogIdMap = crowdExecLogRepository.selectCrowdMaxExecLogIdMap(crowdPackDo.getId());
//                if (execLogIdMap != null && !execLogIdMap.isEmpty()) {
                    // 缓存人群包最新执行成功日志id
//                    redisUtils.set(RedisKeyUtils.genMaxCrowdExecLogIdKey(crowdPackDo.getId()), execLogIdMap, RedisUtils.DEFAULT_EXPIRE_DAYS);

//                    List<String> tableName = splitTableService.getTableNameByLogId(Long.valueOf(execLogIdMap.get("id").toString()));
                    // 缓存人群包执行日志id，对应人群保存表名
//                    redisUtils.set(RedisKeyUtils.genTableNameByLogId(Long.valueOf(execLogIdMap.get("id").toString())), tableName, RedisUtils.DEFAULT_EXPIRE_DAYS);
//                }
//            }
//        }
    }

//    private void refreshStrategyMarketEvent(String eventName, Map<String, List<StrategyMarketEventDo>> map) {
//        redisUtils.set(RedisKeyUtils.genEventNameKey(eventName), map.get(eventName), RedisUtils.DEFAULT_EXPIRE_MONTH);
//    }


}
