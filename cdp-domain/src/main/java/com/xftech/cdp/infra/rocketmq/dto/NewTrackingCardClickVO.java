package com.xftech.cdp.infra.rocketmq.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version $ NewTrackingCardClickVO, v 0.1 2025/3/25 17:16 tianshuo.qiu Exp $
 */
@Data
public class NewTrackingCardClickVO {
    private String app;
    @JsonProperty("inner_app")
    @J<PERSON>NField(name = "inner_app")
    private String innerApp;
    @JsonProperty("event_id")
    @JSONField(name = "event_id")
    private String eventId;
    private String appName;
    @JsonProperty("device_id")
    @JSONField(name = "device_id")
    private String deviceId;
    private String sessionId;
    private String sourceType;
    @JsonProperty("event_time")
    @JSONField(name = "event_time")
    private String eventTime;
    @JsonProperty("event_date_time")
    @JSONField(name = "event_date_time")
    private String eventDateTime;
    private String eventType;
    @JsonProperty("event_name")
    @J<PERSON><PERSON>ield(name = "event_name")
    private String eventName;
    private String token;
    @JsonProperty("credit_user_id")
    @JSONField(name = "credit_user_id")
    private String creditUserId;
    private String mobile;
    @JsonProperty("mobile_cipher")
    @JSONField(name = "mobile_cipher")
    private String mobileCipher;
    private String os;
}
