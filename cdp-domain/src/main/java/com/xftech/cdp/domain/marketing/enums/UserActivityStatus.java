package com.xftech.cdp.domain.marketing.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version v1.0 2024/10/15
 * @description 用户活动资格枚举
 */
@Getter
@AllArgsConstructor
public enum UserActivityStatus {

    NOT_PARTICIPATED(1, "未参加活动"),

    PARTICIPATED_TO_BE_USED(2, "已参与&待使用优惠券"),

    PARTICIPATED_ALREADY_USED(3, "已参与&已使用优惠券"),

    PARTICIPATED_ALREADY_INVALID(4, "已参与&优惠券已失效"),

    UNABLE_TO_PARTICIPATE(5, "用户不可参加活动"),

    ACTIVITY_HAS_ENDED(6, "活动已结束"),

    OTHER(7, "业务兜底状态");

    private final Integer status;
    private final String desc;
}
