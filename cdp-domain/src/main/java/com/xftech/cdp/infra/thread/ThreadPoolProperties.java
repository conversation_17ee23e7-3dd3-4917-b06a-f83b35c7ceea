package com.xftech.cdp.infra.thread;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/2/24
 */
@Component
@RefreshScope
@ConfigurationProperties(prefix = "crowd.pool")
@Configuration
@Data
public class ThreadPoolProperties {

    private String poolName;
    private int corePoolSize;
    private int maximumPoolSize;
    private long keepAliveTime;
    private int queueSize;
}
