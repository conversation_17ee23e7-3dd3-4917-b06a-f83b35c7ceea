package com.xftech.cdp.api.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2023/2/23
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StrategyOperateReq extends BaseReq {

    @ApiModelProperty(value = "策略id")
    private Long strategyId;

    @ApiModelProperty(value = "执行类型 0-暂停,1-开始,2-重试 3-发布")
    private Integer runType;
}
