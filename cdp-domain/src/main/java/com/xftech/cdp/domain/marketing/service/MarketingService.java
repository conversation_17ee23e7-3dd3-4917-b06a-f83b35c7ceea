package com.xftech.cdp.domain.marketing.service;

import com.xftech.cdp.api.dto.marketing.request.ActivityBaseRequest;
import com.xftech.cdp.api.dto.marketing.request.RegisterRequest;
import com.xftech.cdp.api.dto.marketing.request.WinningRequest;
import com.xftech.cdp.api.dto.marketing.response.ActivityDetailResponse;
import com.xftech.cdp.api.dto.marketing.response.EligibilityResponse;
import com.xftech.cdp.api.dto.marketing.response.RecordsBroadcastResponse;
import com.xftech.cdp.api.dto.marketing.response.RegisterResponse;


/**
 * <AUTHOR>
 * @since 2023/2/13
 */
public interface MarketingService {

    EligibilityResponse checkUserEligibility(Long activityId, Long userId);

    Object participateLottery(WinningRequest participateRequest);

    RegisterResponse registerLottery(RegisterRequest request);

    Object couponInflation(ActivityBaseRequest couponInflationRequest);

    RecordsBroadcastResponse recordsBroadcast(ActivityBaseRequest commonRequest);

    ActivityDetailResponse activityDetail(ActivityBaseRequest commonRequest);
}