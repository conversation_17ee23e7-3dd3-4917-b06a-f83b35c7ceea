package com.xftech.cdp.infra.rocketmq.judge;

import com.xftech.cdp.infra.rocketmq.MessageHandlerSelector;
import com.xftech.cdp.infra.utils.WhitelistSwitchUtil;

import com.xinfei.xfframework.common.starter.mq.MqConsumerListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version $ apiCredentialStuffingMsgConsumer, v 0.1 2025/3/14 15:07 tianshuo.qiu Exp $
 */
@Component
@Slf4j
@RocketMQMessageListener(consumerGroup = "cg_xyf_cdp_tp_ugchanprod_api_judge_engine", topic = "tp_ugchanprod_api_judge_engine", consumeMode = ConsumeMode.CONCURRENTLY)
public class ApiCredentialStuffingMsgConsumer extends MqConsumerListener<String> {
    @Resource
    private MessageHandlerSelector handlerSelector;

    @Override
    protected void doMessage(String topic, String bodyMessage, MessageExt messageExt) {
        if (WhitelistSwitchUtil.boolSwitchByApollo("ApiCredentialStuffingMsgConsumerEnable")) {
            log.info("apiCredentialStuffingMsgConsumer doMessage topic={}, messageId={}, body={}", topic, messageExt.getMsgId(), bodyMessage);
            try {
                handlerSelector.doMessage(topic, null, messageExt.getMsgId(), bodyMessage);
            } catch (Exception e) {
                log.error("apiCredentialStuffingMsgConsumer doMessage error, topic={}, messageId={}, body={}", topic, messageExt.getMsgId(), bodyMessage, e);
            }
        }
    }
}
