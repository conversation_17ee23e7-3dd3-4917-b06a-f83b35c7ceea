package com.xftech.cdp.domain.strategy.factory;

import com.alibaba.excel.annotation.ExcelProperty;
import com.xftech.cdp.domain.strategy.model.enums.StrategyExecStatusEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyMarketChannelEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyRulerEnum;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyExecLogDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/2/23
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MonitorListSmsResp extends AbsMonitorListRespFinal {

    @ApiModelProperty(value = "模板编号")
    @ExcelProperty(index = 3, value = "模板编号")
    protected String templateId;

    @ApiModelProperty(value = "应发短信人数")
    @ExcelProperty(index = 5, value = "应发短信人数")
    protected String triggerCount;

    @ApiModelProperty(value = "麻雀推送进度")
    @ExcelProperty(index = 6, value = "麻雀推送进度")
    protected String selfProcess;

    @ApiModelProperty(value = "短信中心发送进度")
    @ExcelProperty(index = 7, value = "短信中心发送进度")
    protected String smsProcess;

    @ApiModelProperty(value = "供应商发送人数")
    @ExcelProperty(index = 8, value = "供应商发送人数")
    protected String supplierSendCount;

    @ApiModelProperty(value = "发送率")
    @ExcelProperty(index = 9, value = "发送率")
    protected String supplierSendRate;

    @ApiModelProperty(value = "回执成功人数")
    @ExcelProperty(index = 10, value = "回执成功人数")
    protected String succCount;

    @ApiModelProperty(value = "供应商成功率")
    @ExcelProperty(index = 11, value = "供应商成功率")
    protected String supplierSuccRate;

    @ApiModelProperty(value = "营销触达成功率")
    @ExcelProperty(index = 12, value = "营销触达成功率")
    protected String reachSuccRate;

    @Override
    public AbsMonitorListRespFinal convertRes(StrategyDo strategyDo, StrategyExecLogDo item) {
        boolean executingFlag = strategyDo.getSendRuler() != StrategyRulerEnum.EVENT.getCode() && StrategyExecStatusEnum.EXECUTING.getCode() == item.getExecStatus();
        boolean sendCountZero = item.getSendCount() == 0;
        boolean supplierCountZero = item.getSupplierCount() == 0;
        MonitorListSmsResp monitorListSmsResp = new MonitorListSmsResp();
        StrategyExecStatusEnum strategyExecStatusEnum = StrategyExecStatusEnum.getInstance(item.getExecStatus());
        monitorListSmsResp.setDateTime(item.getExecTime());
        monitorListSmsResp.setGroupName(item.getStrategyGroupName());
        monitorListSmsResp.setMarketChannel(StrategyMarketChannelEnum.SMS.getDescription());
        monitorListSmsResp.setExecStatus(Objects.nonNull(strategyExecStatusEnum) ? strategyExecStatusEnum.getDescription() : "");
        monitorListSmsResp.setTemplateId(item.getTemplateId());
        monitorListSmsResp.setTriggerCount(String.valueOf(item.getExecCount()));
        monitorListSmsResp.setSelfProcess(item.getExecCount() + "/" + item.getSendCount());
        monitorListSmsResp.setSmsProcess(executingFlag || sendCountZero ? "-" : item.getSendCount() + "/" + item.getSupplierCount());
        monitorListSmsResp.setSupplierSendCount(executingFlag || sendCountZero || supplierCountZero ? "-" : String.valueOf(item.getActualCount()));
        monitorListSmsResp.setSupplierSendRate(executingFlag || sendCountZero || supplierCountZero ? "-" : BigDecimal.valueOf(((float) item.getActualCount() / item.getSupplierCount()) * 100).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
        monitorListSmsResp.setSuccCount(executingFlag || sendCountZero || supplierCountZero ? "-" : String.valueOf(item.getSuccCount()));
        String supplierSuccRateStr = item.getActualCount().equals(0) ? "0.00%" : BigDecimal.valueOf(((float) item.getSuccCount() / item.getActualCount()) * 100).setScale(2, BigDecimal.ROUND_HALF_UP) + "%";
        monitorListSmsResp.setSupplierSuccRate(executingFlag || sendCountZero || supplierCountZero ? "-" : supplierSuccRateStr);
        monitorListSmsResp.setReachSuccRate(sendCountZero || supplierCountZero || item.getExecCount() == 0 ? "-" : BigDecimal.valueOf(((float) item.getSuccCount() / item.getExecCount()) * 100).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
        return monitorListSmsResp;
    }
}
