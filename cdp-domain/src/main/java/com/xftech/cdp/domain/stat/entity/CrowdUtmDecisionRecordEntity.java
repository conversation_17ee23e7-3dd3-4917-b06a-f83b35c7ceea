package com.xftech.cdp.domain.stat.entity;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 人群包渠道过滤决策结果
 */
@Data
public class CrowdUtmDecisionRecordEntity {

    private Long id;

    /**
     * crowd_pack.id
     */
    private Long crowdId;

    /**
     * credit_user.id
     */
    private Long appUserId;

    /**
     * APP
     */
    private String app;

    /**
     * crowd_exec_log.id
     */
    private Long crowdExecLogId;

    /**
     * 首次注册渠道
     */
    private String mobileUtmSource;

    /**
     * 历史进件渠道
     */
    private String historyBorrowUtmSource;

    /**
     * 最近一次放款成功渠道
     */
    private String lastLoanSuccessUtmSource;

    /**
     * 注册时间
     */
    private LocalDateTime registerTime;
    /**
     * 渠道过滤原因码
     */
    private Integer failCode;
    /**
     * 渠道过滤原因描述
     */
    private String failReason;
    /**
     * 最终决策结果 1通过 0不通过
     */
    private Integer decisionResult;
    /**
     * 决策明细
     */
    private String decisionDetail;
    /**
     * 渠道决策结果明细
     */
    private List<UtmResult> utmResultList;

    private String traceId;

    private String tableNameNo;

    private LocalDateTime createdTime;

    public String getTableName() {
        return "crowd_utm_decision_record_" + getTableNameNo();
    }

    public String getDecisionDetail() {
        return JSONObject.toJSONString(utmResultList);
    }

    public void setHistoryBorrowUtmSource(String utmSource) {
        if (utmSource != null && utmSource.length() > 127) {
           this.historyBorrowUtmSource = utmSource.substring(0, 127);
        } else {
            this.historyBorrowUtmSource = utmSource;
        }
    }
}
