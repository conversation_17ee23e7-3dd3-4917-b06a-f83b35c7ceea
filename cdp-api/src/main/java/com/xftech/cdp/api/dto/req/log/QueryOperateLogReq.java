/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.req.log;

import com.xftech.cdp.api.dto.base.PageRequestDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 *
 * <AUTHOR>
 * @version $ QueryOperateLogReq, v 0.1 2024/5/28 11:26 lingang.han Exp $
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryOperateLogReq extends PageRequestDto {

    @NotNull(message = "参数不能为空")
    @ApiModelProperty(value = "参数名称", required = true)
    private Long id;

    @ApiModelProperty(value = "操作模块：1:策略模块 2:人群模块 3:画布模块 4:流控规则模块 5:模版参数模块")
    private Integer model;

}