package com.xftech.cdp.domain.mq.impl;

import java.util.Objects;
import java.util.Optional;

import com.xftech.cdp.domain.mq.MqProducerService;
import com.xftech.cdp.domain.strategy.model.enums.StrategyTypeEnum;
import com.xftech.cdp.infra.config.ApolloUtil;
import com.xftech.cdp.infra.config.LogUtil;
import com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig;
import com.xftech.cdp.infra.rabbitmq.factory.impl.BizEventMqService;
import com.xftech.cdp.domain.strategy.model.enums.StrategyMarketChannelEnum;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventVO;

import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class MqProducerServiceImpl implements MqProducerService {
    private static final Logger logger = LoggerFactory.getLogger(MqProducerServiceImpl.class);

    @Autowired
    private BizEventMqService bizEventMqService;
    @Autowired
    private BizEventMqConfig bizEventMqConfig;

    @Override
    public void bizEventDelay(BizEventVO bizEventVO, long seconds, StrategyTypeEnum strategyType) {
        LogUtil.logDebug("预筛通过发送至复筛MQ bizEventVO={} seconds={} strategyType={}", JSONObject.toJSONString(bizEventVO), seconds, strategyType);
        try {
            if (seconds == 0 && strategyType == StrategyTypeEnum.EVENT_ENGINE) {
                logger.info("策略引擎版本, 事件规则后不需要进行延迟, 策略id:{}, 消息id:{}", bizEventVO.getStrategyId(), bizEventVO.getMessageId());
                bizEventMqService.sendHighLevelDelayMessage(bizEventVO, seconds);
            } else if (seconds == 0 && ApolloUtil.getAppListProperty("noDelay.strategyId").contains(Optional.ofNullable(bizEventVO.getStrategyId()).orElse(0L).toString())) {
                logger.info("TO立即触达策略, 事件规则不需要进行延迟, 事件={} 策略id={} 消息id={}",
                        bizEventVO.getBizEventType(), bizEventVO.getStrategyId(), bizEventVO.getMessageId());
                bizEventMqService.sendHighLevelDelayMessage(bizEventVO, seconds);
            } else if (seconds / 60 <= 1) {
                if (seconds < bizEventMqConfig.getBizEventAtOnceDelayTime()) {
                    seconds = bizEventMqConfig.getBizEventAtOnceDelayTime();
                }
                bizEventMqService.sendHighLevelDelayMessage(bizEventVO, seconds);
            } else if (seconds / 60 <= 5) {
                bizEventMqService.sendMiddleLevelDelayMessage(bizEventVO, seconds);
            } else {
                bizEventMqService.sendLowLevelDelayMessage(bizEventVO, seconds);
            }
        } catch (Exception e) {
            logger.warn("延迟消息发送异常", e);
        }
    }

    @Override
    public void channelDelivery(BizEventVO bizEventVO) {
        try {
            if (StrategyMarketChannelEnum.SMS.getCode() == bizEventVO.getMarketChannel()) {
                bizEventMqService.sendSmsDispatchMessage(bizEventVO);
            } else if (StrategyMarketChannelEnum.VOICE.getCode() == bizEventVO.getMarketChannel() || StrategyMarketChannelEnum.VOICE_NEW.getCode() == bizEventVO.getMarketChannel()) {
                bizEventMqService.sendTeleDispatchMessage(bizEventVO);
            } else if (StrategyMarketChannelEnum.SALE_TICKET.getCode() == bizEventVO.getMarketChannel()) {
                bizEventMqService.sendCouponDispatchMessage(bizEventVO);
            } else if (StrategyMarketChannelEnum.NONE.getCode() == bizEventVO.getMarketChannel()) {
                bizEventMqService.sendNoMarketMessage(bizEventVO);
            } else if (StrategyMarketChannelEnum.PUSH.getCode() == bizEventVO.getMarketChannel()) {
                bizEventMqService.sendPushDispatchMessage(bizEventVO);
            } else if (StrategyMarketChannelEnum.INCREASE_AMOUNT.getCode() == bizEventVO.getMarketChannel()) {
                bizEventMqService.sendIncreaseAmtDispatchMessage(bizEventVO);
            } else if (StrategyMarketChannelEnum.LIFE_RIGHTS.getCode() == bizEventVO.getMarketChannel()) {
                bizEventMqService.sendLifeRightsDispatchMessage(bizEventVO);
            } else if (StrategyMarketChannelEnum.X_DAY_INTEREST_FREE.getCode() == bizEventVO.getMarketChannel()) {
                bizEventMqService.sendXDayInterestFreeDispatchMessage(bizEventVO);
            } else if (StrategyMarketChannelEnum.AI_PRONTO.getCode() == bizEventVO.getMarketChannel()){
                bizEventMqService.sendAiDispatchMessage(bizEventVO);
            }
        } catch (Exception e) {
            logger.warn("发送触达消息异常",e);
        }
    }

}
