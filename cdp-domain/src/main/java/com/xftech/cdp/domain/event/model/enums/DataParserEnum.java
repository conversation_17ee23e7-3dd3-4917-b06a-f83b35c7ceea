package com.xftech.cdp.domain.event.model.enums;

import lombok.Getter;

/**
 * 数据解析器类型枚举
 * <AUTHOR>
 * @version $ DataParserEnum, v 0.1 2024/11/15 14:25 snail Exp $
 */
@Getter
public enum DataParserEnum {
    SIMPLE("simple","简单参数解析模式"),;

    DataParserEnum(String type,String desc){
        this.type = type;
        this.desc = desc;
    }

    /** 解析器类型 */
    private String type;
    /** 解析器描述 */
    private String desc;
}
