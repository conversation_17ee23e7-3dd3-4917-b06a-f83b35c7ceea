/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.infra.repository.cdp.strategy.po.ReportDailyStrategyDo;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 */
@Component
public class ReportDailyStrategyRepository {

    public ReportDailyStrategyDo selectById(Long id) {
        return DBUtil.selectOne("reportDailyStrategyMapper.selectByPrimaryKey", id);
    }

    public boolean insert(ReportDailyStrategyDo reportDailyStrategyDo) {
        return DBUtil.insert("reportDailyStrategyMapper.insertSelective", reportDailyStrategyDo) > 0;
    }

    public boolean updateById(ReportDailyStrategyDo reportDailyStrategyDo) {
        reportDailyStrategyDo.setUpdatedTime(new Date());
        return DBUtil.update("reportDailyStrategyMapper.updateByPrimaryKeySelective", reportDailyStrategyDo) > 0;
    }

    public boolean existReportDailyStrategy(ReportDailyStrategyDo reportDailyStrategyDo) {
        Integer num = DBUtil.selectOne("reportDailyStrategyMapper.existReportDailyStrategy", reportDailyStrategyDo);
        return num > 0;
    }

    public void updateByDateAndChannelId(ReportDailyStrategyDo reportDailyStrategyDo) {
        DBUtil.update("reportDailyStrategyMapper.updateByDateAndChannelId", reportDailyStrategyDo);
    }

    public List<ReportDailyStrategyDo> selectTodayFail() {
        return DBUtil.selectList("reportDailyStrategyMapper.selectTodayFail", null);
    }

    public List<ReportDailyStrategyDo> selectToday() {
        return DBUtil.selectList("reportDailyStrategyMapper.selectToday", null);
    }
}