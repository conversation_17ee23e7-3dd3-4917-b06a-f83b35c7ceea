package com.xftech.cdp.domain.crowd.factory;

import com.xftech.cdp.domain.crowd.model.dispatch.CrowdContext;
import com.xftech.cdp.domain.crowd.model.dispatch.CrowdWereHouse;
import com.xftech.cdp.domain.crowd.model.dispatch.InvokePage;
import com.xftech.cdp.infra.config.CrowdConfig;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPackDo;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public abstract class AbsCrowdOptService {
    @Autowired
    public CrowdConfig crowdConfig;

    /**
     * 前置校验
     * 1. 数仓
     * 2. 钉钉告警
     */
    public abstract void preHandler(CrowdContext crowdContext);

    public abstract CrowdContext initContext(boolean isRunAsFixedNumberPage, CrowdPackDo crowdPack, AbsCrowdOptService crowdOpt);

    /**
     * left: include sql  right: exclude sql
     * 组织查询sql、分段执行
     */
    public abstract void organizeSql(CrowdContext crowdContext);

    public abstract void andPage(StringBuilder sql, InvokePage invokePage);

    public abstract void andPageNew(StringBuilder sql, InvokePage invokePage);

    public abstract List<CrowdWereHouse> queryAdsDataInfo(String sql, boolean isRunAsFixedNumberPage);
}
