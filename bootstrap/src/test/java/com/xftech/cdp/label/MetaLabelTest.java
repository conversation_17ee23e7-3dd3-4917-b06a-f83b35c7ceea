/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.label;

import com.xftech.cdp.BaseTest;
import com.xftech.cdp.api.dto.req.label.*;
import com.xftech.cdp.application.LabelHandler;
import com.xftech.cdp.domain.label.biz.LabelBiz;
import com.xftech.cdp.feign.model.response.MetaLabelDto;
import com.xftech.cdp.infra.utils.JsonUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @version $ MetaLabelTest, v 0.1 2024/6/21 17:56 lingang.han Exp $
 */
public class MetaLabelTest extends BaseTest {
    @Autowired
    private LabelBiz labelBiz;
    @Autowired
    private LabelHandler labelHandler;
    @Test
    public void classificationTest() {
        System.out.println(JsonUtil.toJson(labelBiz.classification()));
    }

    @Test
    public void configList(){
        System.out.println(JsonUtil.toJson(labelBiz.configList()));
    }

    @Test
    public void list(){
        LabelConfigListReq labelConfigListReq = new LabelConfigListReq();
        labelConfigListReq.setMetaLabelCode("last_withdrawals_main_status");
        System.out.println(JsonUtil.toJson(labelBiz.list(labelConfigListReq)));
    }

    @Test
    public void labelPush(){
        LabelConfigListReq labelConfigListReq = new LabelConfigListReq();
        labelConfigListReq.setMetaLabelCode("last_withdrawals_main_status");
        LabelPublishReq labelPublishReq = JsonUtil.parse("{\"businessTypes\":[\"new-cust\",\"old-cust\",\"test-cust\",\"loan-overload-cust\"],\"enumResult\":null,\"firstLevel\":0,\"ifAdd\":1,\"labelCode\":\"new_user_marketing_type\",\"labelExchangeCode\":1,\"labelName\":\"ccc\",\"secondLevel\":1}", LabelPublishReq.class);
        System.out.println(JsonUtil.toJson(labelPublishReq));
        labelBiz.labelPublish(labelPublishReq);
        System.out.println(JsonUtil.toJson(labelBiz.labelPublish(labelPublishReq)));
    }

    @Test
    public void refCrowd() {
        AssociatedCrowdsReq associatedCrowdsReq = new AssociatedCrowdsReq();
        associatedCrowdsReq.setLabelCodeId(4L);
        System.out.println(JsonUtil.toJson(labelBiz.associatedCrowds(associatedCrowdsReq)));
    }

    @Test
    public void labelRemark() {
        LabelRemarkReq labelRemarkReq = new LabelRemarkReq();
        labelRemarkReq.setLabelId(299L);
        labelRemarkReq.setDescription("");
        System.out.println(JsonUtil.toJson(labelBiz.labelRemark(labelRemarkReq)));
    }


    @Test
    public void labelDiscard() {
        LabelDiscardReq labelDiscardReq = new LabelDiscardReq();
        labelDiscardReq.setLabelId(299L);
        System.out.println(JsonUtil.toJson(labelBiz.labelDiscard(labelDiscardReq)));
    }

    @Test
    public void syncLabel(){
        labelHandler.syncMetaLabel();
//        MetaLabelDto parse = JsonUtil.parse("", MetaLabelDto.class);
//        labelHandler.metaLabelExecute(parse);
    }

}