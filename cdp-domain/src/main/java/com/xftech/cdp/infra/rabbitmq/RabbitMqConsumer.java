package com.xftech.cdp.infra.rabbitmq;

import com.xftech.base.common.util.LogUtil;
import com.xftech.base.log.IUdpLogger;
import com.xftech.cdp.infra.rabbitmq.factory.impl.BizEventMqService;
import com.xftech.cdp.infra.rabbitmq.factory.impl.CouponMqService;
import com.xftech.cdp.infra.rabbitmq.factory.impl.SmsMqService;
import com.xftech.rabbitmq.UdpMqConsumerManager;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@DependsOn({"udpMqConfig", "udpDataSourceConfigBuild", "abClient"})
public class RabbitMqConsumer implements InitializingBean {

    private static final IUdpLogger logger = LogUtil.getLogger(RabbitMqConsumer.class);

    @Autowired
    private SmsMqService smsMqService;
    @Autowired
    private CouponMqService couponMqService;
    @Autowired
    private BizEventMqService bizEventMqService;

    @Override
    public void afterPropertiesSet() throws Exception {
        try {
            // 短信mq
            smsMqService.init();
            // 优惠券mq
            couponMqService.init();
            // 事件节点mq
            bizEventMqService.init();
        } catch (Exception e) {
            logger.warn("mq consumer create fail.", e);
        }
    }

    public void close(String connectionId) {
        try {
            UdpMqConsumerManager.getInstance().closeUdpMqConsumer(connectionId);
        } catch (Exception e) {
            logger.warn("mq consumer close fail.", e);
        }
        logger.info("mq close finish.");
    }
}
