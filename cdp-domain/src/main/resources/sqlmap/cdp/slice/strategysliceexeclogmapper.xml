<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="strategySliceExecLogMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.slice.po.StrategySliceExecLogDo">
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="strategy_id" property="strategyId" jdbcType="BIGINT" />
        <result column="crowd_id" property="crowdId" jdbcType="BIGINT" />
        <result column="crowd_slice_id" property="crowdSliceId" jdbcType="BIGINT" />
        <result column="crowd_version" property="crowdVersion" jdbcType="BIGINT" />
        <result column="dispatch_time" property="dispatchTime" jdbcType="TIMESTAMP" />
        <result column="total_user_cnt" property="totalUserCnt" jdbcType="BIGINT" />
        <result column="exec_user_cnt" property="execUserCnt" jdbcType="BIGINT" />
        <result column="dispatch_cnt" property="dispatchCnt" jdbcType="BIGINT" />
        <result column="status" property="status" jdbcType="SMALLINT" />
        <result column="retry_times" property="retryTimes" jdbcType="SMALLINT" />
        <result column="error_msg" property="errorMsg" jdbcType="VARCHAR" />
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
        <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP" />
        <result column="d_flag" property="dFlag" jdbcType="SMALLINT" />
        <result column="created_op" property="createdOp" jdbcType="VARCHAR" />
        <result column="updated_op" property="updatedOp" jdbcType="VARCHAR" />
    </resultMap>
    <resultMap id="cntMap" type="map">
        <result column="total_user_cnt" property="totalUserCnt" javaType="long" />
        <result column="exec_user_cnt" property="execUserCnt" javaType="long" />
        <result column="dispatch_cnt" property="dispatchCnt" javaType="long" />
    </resultMap>

    <!-- 实体字段 -->
    <sql id="Base_Column_List">
        id, strategy_id, crowd_id, crowd_slice_id,crowd_version,dispatch_time, total_user_cnt, exec_user_cnt, dispatch_cnt,
        status, retry_times,error_msg, created_time, updated_time, d_flag, created_op, updated_op
    </sql>

    <!-- 插入语句 -->
    <insert id="insert" parameterType="com.xftech.cdp.infra.repository.cdp.slice.po.StrategySliceExecLogDo">
        INSERT INTO strategy_slice_exec_log
        (strategy_id, crowd_id, crowd_slice_id,crowd_version, dispatch_time,total_user_cnt, exec_user_cnt, dispatch_cnt, status,
        retry_times, error_msg, created_time, updated_time, d_flag, created_op, updated_op)
        VALUES
        (#{strategyId,jdbcType=BIGINT}, #{crowdId,jdbcType=BIGINT}, #{crowdSliceId,jdbcType=BIGINT}, #{crowdVersion,jdbcType=BIGINT},#{dispatchTime,jdbcType=TIMESTAMP},
         #{totalUserCnt,jdbcType=BIGINT}, #{execUserCnt,jdbcType=BIGINT},
        #{dispatchCnt,jdbcType=BIGINT}, #{status,jdbcType=SMALLINT}, #{retryTimes,jdbcType=SMALLINT},#{errorMsg,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP},
        #{updatedTime,jdbcType=TIMESTAMP}, #{dFlag,jdbcType=SMALLINT}, #{createdOp,jdbcType=VARCHAR}, #{updatedOp,jdbcType=VARCHAR})
    </insert>

    <insert id="batchInsert">
        INSERT INTO strategy_slice_exec_log
        (strategy_id, crowd_id, crowd_slice_id, crowd_version, dispatch_time, status,
         retry_times, created_time, updated_time)
        VALUES
        <foreach collection="execLogDoList" item="item" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.strategyId,jdbcType=BIGINT}, #{item.crowdId,jdbcType=BIGINT}, #{item.crowdSliceId,jdbcType=BIGINT},#{item.crowdVersion,jdbcType=BIGINT},
            #{item.dispatchTime,jdbcType=TIMESTAMP},
                 #{item.status,jdbcType=SMALLINT}, #{item.retryTimes,jdbcType=SMALLINT}, now(), now()
            </trim>
        </foreach>
    </insert>

    <!-- 插入语句（使用默认值） -->
    <insert id="insertSelective" parameterType="com.xftech.cdp.infra.repository.cdp.slice.po.StrategySliceExecLogDo" useGeneratedKeys="true">
        INSERT INTO strategy_slice_exec_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="strategyId != null">
                strategy_id,
            </if>
            <if test="crowdId != null">
                crowd_id,
            </if>
            <if test="crowdSliceId != null">
                crowd_slice_id,
            </if>
            <if test="crowdVersion != null">
                crowd_version,
            </if>
            <if test="dispatchTime != null">
                dispatch_time,
            </if>
            <if test="totalUserCnt != null">
                total_user_cnt,
            </if>
            <if test="execUserCnt != null">
                exec_user_cnt,
            </if>
            <if test="dispatchCnt != null">
                dispatch_cnt,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="retryTimes != null">
                retry_times,
            </if>
            <if test="errorMsg != null">
                error_mag,
            </if>
            <if test="createdTime != null">
                created_time,
            </if>
            <if test="updatedTime != null">
                updated_time,
            </if>
            <if test="dFlag != null">
                d_flag,
            </if>
            <if test="createdOp != null">
                created_op,
            </if>
            <if test="updatedOp != null">
                updated_op,
            </if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="strategyId != null">
                #{strategyId,jdbcType=BIGINT},
            </if>
            <if test="crowdId != null">
                #{crowdId,jdbcType=BIGINT},
            </if>
            <if test="crowdSliceId != null">
                #{crowdSliceId,jdbcType=BIGINT},
            </if>
            <if test="crowdVersion != null">
                #{item.crowdVersion,jdbcType=BIGINT},
            </if>
            <if test="dispatchTime != null">
                #{dispatchTime,jdbcType=TIMESTAMP},
            </if>
            <if test="totalUserCnt != null">
                #{totalUserCnt,jdbcType=BIGINT},
            </if>
            <if test="execUserCnt != null">
                #{execUserCnt,jdbcType=BIGINT},
            </if>
            <if test="dispatchCnt != null">
                #{dispatchCnt,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                #{status,jdbcType=SMALLINT},
            </if>
            <if test="retryTimes != null">
                #{retryTimes,jdbcType=SMALLINT},
            </if>
            <if test="errorMsg != null">
                #{errorMsg,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedTime != null">
                #{updatedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="dFlag != null">
                #{dFlag,jdbcType=SMALLINT},
            </if>
            <if test="createdOp != null">
                #{createdOp,jdbcType=VARCHAR},
            </if>
            <if test="updatedOp != null">
                #{updatedOp,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 更新语句 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.xftech.cdp.infra.repository.cdp.slice.po.StrategySliceExecLogDo">
        UPDATE strategy_slice_exec_log
        <set>
            <if test="strategyId != null">
                strategy_id = #{strategyId,jdbcType=BIGINT},
            </if>
            <if test="crowdId != null">
                crowd_id = #{crowdId,jdbcType=BIGINT},
            </if>
            <if test="crowdSliceId != null">
                crowd_slice_id = #{crowdSliceId,jdbcType=BIGINT},
            </if>
            <if test="crowdVersion != null">
                crowd_version = #{crowdVersion,jdbcType=BIGINT},
            </if>
            <if test="dispatchTime != null">
                dispatch_time = #{dispatchTime,jdbcType=TIMESTAMP},
            </if>
            <if test="totalUserCnt != null">
                total_user_cnt = #{totalUserCnt,jdbcType=BIGINT},
            </if>
            <if test="execUserCnt != null">
                exec_user_cnt = #{execUserCnt,jdbcType=BIGINT},
            </if>
            <if test="dispatchCnt != null">
                dispatch_cnt = #{dispatchCnt,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=SMALLINT},
            </if>
            <if test="retryTimes != null">
                retry_times = #{retryTimes,jdbcType=SMALLINT},
            </if>
            <if test="errorMsg != null">
                error_msg = #{errorMsg,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedTime != null">
                updated_time = #{updatedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="dFlag != null">
                d_flag = #{dFlag,jdbcType=SMALLINT},
            </if>
            <if test="createdOp != null">
                created_op = #{createdOp,jdbcType=VARCHAR},
            </if>
            <if test="updatedOp != null">
                updated_op = #{updatedOp,jdbcType=VARCHAR},
            </if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 更新语句 -->
    <update id="updateByPrimaryKey" parameterType="com.xftech.cdp.infra.repository.cdp.slice.po.StrategySliceExecLogDo">
        UPDATE strategy_slice_exec_log
        SET
        strategy_id = #{strategyId,jdbcType=BIGINT},
        crowd_id = #{crowdId,jdbcType=BIGINT},
        crowd_slice_id = #{crowdSliceId,jdbcType=BIGINT},
        dispatch_time = #{dispatchTime,jdbcType=TIMESTAMP},
        total_user_cnt = #{totalUserCnt,jdbcType=BIGINT},
        exec_user_cnt = #{execUserCnt,jdbcType=BIGINT},
        dispatch_cnt = #{dispatchCnt,jdbcType=BIGINT},
        status = #{status,jdbcType=SMALLINT},
        retry_times = #{retryTimes,jdbcType=SMALLINT},
        error_msg = #{errorMsg,jdbcType=VARCHAR},
        created_time = #{createdTime,jdbcType=TIMESTAMP},
        updated_time = #{updatedTime,jdbcType=TIMESTAMP},
        d_flag = #{dFlag,jdbcType=SMALLINT},
        created_op = #{createdOp,jdbcType=VARCHAR},
        updated_op = #{updatedOp,jdbcType=VARCHAR}
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateStatus" >
        UPDATE strategy_slice_exec_log
        SET
            status = #{status},
            retry_times = #{retryTimes,jdbcType=SMALLINT}
        WHERE id = #{id,jdbcType=BIGINT} and status= #{fromStatus}
    </update>

    <update id="updateNextDispatchTimeAndStatus" >
        UPDATE strategy_slice_exec_log
        SET
            status = #{status},
            dispatch_time = #{nextDispatchTime,jdbcType=TIMESTAMP},
            error_msg = #{errorMsg,jdbcType=VARCHAR}
        WHERE id = #{id,jdbcType=BIGINT} and status= #{fromStatus}
    </update>

    <update id="updateCntNum" >
        UPDATE strategy_slice_exec_log
        SET
            total_user_cnt = #{totalUserCnt,jdbcType=BIGINT},
            exec_user_cnt = #{execUserCnt,jdbcType=BIGINT},
            dispatch_cnt = #{dispatchCnt,jdbcType=BIGINT}
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 删除语句 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        DELETE FROM strategy_slice_exec_log
        WHERE id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 查询语句 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM strategy_slice_exec_log
        WHERE id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 查询所有记录 -->
    <select id="selectByParam" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM strategy_slice_exec_log
        WHERE strategy_id = #{strategyId,jdbcType=BIGINT}
        and created_time &gt;= #{startTime,jdbcType=TIMESTAMP}
        and created_time &lt; #{endTime,jdbcType=TIMESTAMP}
        and crowd_version = #{crowdVersion,jdbcType=BIGINT}
        and d_flag = 0
    </select>

    <!-- 查询待执行任务  -->
    <select id="selectTodoList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM strategy_slice_exec_log
        WHERE
         created_time &gt;= #{startTime,jdbcType=TIMESTAMP}
        and created_time &lt; #{endTime,jdbcType=TIMESTAMP}
        and dispatch_time &lt;= now()
        and status in (0, 1, 3) -- 待执行，执行中，失败待重试
        and d_flag = 0
        order by dispatch_time desc
        limit #{limit,jdbcType=INTEGER}
    </select>

    <!-- 查询策略下未执行完成的任务 -->
    <select id="selectStrategyTodoList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM strategy_slice_exec_log
        WHERE
         strategy_id = #{strategyId,jdbcType=BIGINT}
        and created_time &gt;= #{startTime,jdbcType=TIMESTAMP}
        and created_time &lt; #{endTime,jdbcType=TIMESTAMP}
        and status in (0, 1, 3)
        and d_flag = 0
    </select>

    <select id="selectStrategyCnt" resultMap="cntMap">
        SELECT
        sum(total_user_cnt) as total_user_cnt,
        sum(exec_user_cnt) as exec_user_cnt,
        sum(dispatch_cnt) as dispatch_cnt
        FROM strategy_slice_exec_log
        WHERE
        strategy_id = #{strategyId,jdbcType=BIGINT}
        and crowd_version = #{crowdVersion,jdbcType=BIGINT}
        and created_time &gt;= #{startTime,jdbcType=TIMESTAMP}
        and created_time &lt; #{endTime,jdbcType=TIMESTAMP}
        and status = 2
        and d_flag = 0
    </select>

</mapper>
