package com.xftech.cdp.domain.auth.service;


import com.xftech.cdp.api.dto.req.auth.GetTokenReq;
import com.xftech.cdp.api.dto.resp.auth.GetTokenResp;

/**
 * <AUTHOR> wanghu
 * @since : 2022/8/2 16:01:44
 */
public interface AuthService {

//    /**
//     * 获取图片验证码
//     * @param req 请求对象
//     * @return 返回对象
//     */
//    GetImageCodeResp getImageCode( GetImageCodeReq req);
//
//    /**
//     * 发送短信验证码
//     * @param req 请求对象
//     * @return 返回对象
//     */
//    Empty sendCaptcha( SendCaptchaReq req);


    /**
     * 获取Token
     *
     * @param req 请求对象
     * @return 返回对象
     */
    GetTokenResp getToken(GetTokenReq req);

}
