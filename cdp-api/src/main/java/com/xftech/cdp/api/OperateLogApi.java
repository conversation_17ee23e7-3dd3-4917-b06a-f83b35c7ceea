package com.xftech.cdp.api;

import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.base.Response;
import com.xftech.cdp.api.dto.req.log.QueryOperateLogReq;
import com.xftech.cdp.api.dto.resp.log.OperateLogResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 *
 * <AUTHOR>
 */
@Api(tags = "操作日志")
@RequestMapping("/log")
public interface OperateLogApi {
    @ApiOperation("创建流控规则")
    @PostMapping("/query")
    Response<PageResultResponse<OperateLogResp>> queryPage(@Validated @RequestBody QueryOperateLogReq queryOperateLogReq);
}
