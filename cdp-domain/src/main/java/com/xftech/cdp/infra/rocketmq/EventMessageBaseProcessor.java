package com.xftech.cdp.infra.rocketmq;

import cn.hutool.crypto.SecureUtil;
import com.xftech.cdp.domain.event.EventMessageProcessor;
import com.xftech.cdp.domain.event.model.constants.EventConfigConstants;
import com.xftech.cdp.domain.mq.MqConsumeService;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventMessageVO;
import com.xftech.cdp.infra.utils.RedisUtils;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class EventMessageBaseProcessor {
    @Autowired
    private MqConsumeService mqConsumeService;

    @Autowired
    private EventMessageProcessor eventMessageProcessor;

    @Autowired
    private RedisUtils redisUtils;

    public void doMessageProcess(RocketMQMessageListener messageListener,MessageExt messageExt,String message){
        log.info("Event message consumer, topic={} messageId={} body={}", messageListener.topic(), messageExt.getMsgId(), message);
        try{
            //消息幂等控制，若已处理过则直接丢弃消息
            if(!isNeedProcess(messageListener,messageExt,message)){
                return;
            }

            BizEventMessageVO bizEventMessage = eventMessageProcessor.doMessageProcessor(messageListener.topic(),
                    messageListener.consumerGroup(),getConsumerTag(messageListener),message);
            mqConsumeService.bizEventProcess(messageExt.getMsgId(),bizEventMessage);
        }catch (Exception e){
            log.error("event msg process error, topic={}, consumer={}, bodyMessage={}", messageListener.topic(), messageListener.consumerGroup(), message, e);
        }
    }

    /**
     * 实时事件消息是否需要处理，true需要处理
     * @param messageListener 消息事件配置信息
     * @param messageExt 消息扩展信息
     * @param message 消息体
     * @return 是否需要处理
     */
    private boolean isNeedProcess(RocketMQMessageListener messageListener,MessageExt messageExt,String message){
        String messageUnionId = getMessageUnionId(messageListener,messageExt);
        if (StringUtils.isNotBlank(messageUnionId) && !redisUtils.lock(messageUnionId, "0", 10, TimeUnit.MINUTES)) {
            log.warn("MessageHandlerSelector 重复消息, topic={}, tag={}, messageId={}, body={}", messageListener.topic(), messageListener.selectorExpression(), messageExt.getMsgId(),message);
            return false;
        }

        return true;
    }

    /**
     * 获取消息的唯一标识信息，用于幂等控制
     * @param messageListener 消息事件配置信息
     * @param messageExt 消息扩展信息
     * @return 唯一标识信息
     */
    private String getMessageUnionId(RocketMQMessageListener messageListener,MessageExt messageExt){
        String topic = messageExt.getTopic(), tag = messageListener.selectorExpression(), messageId = messageExt.getMsgId();
        if(StringUtils.isEmpty(topic) && StringUtils.isEmpty(messageId)){
            return SecureUtil.md5(String.format("cg:%s:", topic + tag) + messageId);
        }

        return null;
    }

    /**
     * 获取消费者tag信息
     * @return 消费者tag信息
     */
    private String getConsumerTag(RocketMQMessageListener messageListener){
        String consumerTag = messageListener.selectorExpression();
        if(StringUtils.isEmpty(consumerTag) || EventConfigConstants.DEFAULT_TAG.equalsIgnoreCase(consumerTag)){
            return null;
        }

        return consumerTag;
    }
}
