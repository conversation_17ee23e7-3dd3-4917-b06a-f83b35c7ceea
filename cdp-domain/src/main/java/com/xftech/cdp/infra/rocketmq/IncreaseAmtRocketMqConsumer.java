/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.rocketmq;

import cn.hutool.crypto.SecureUtil;
import com.xftech.cdp.api.dto.req.external.IncreaseAmtCallbakRequest;
import com.xftech.cdp.domain.strategy.model.dto.NotifyEventDto;
import com.xftech.cdp.domain.strategy.service.dispatch.event.StrategyEventDispatchService;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventMessageVO;
import com.xftech.cdp.infra.utils.DateUtil;
import com.xftech.cdp.infra.utils.JsonUtil;
import com.xftech.cdp.infra.utils.RedisUtils;
import com.xinfei.xfframework.common.starter.mq.MqConsumerListener;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 *
 * <AUTHOR>
 * @version $ IncreaseAmtRocketMqConsumer, v 0.1 2024/3/14 21:02 yye.xu Exp $
 */

@Slf4j
@Component
@AllArgsConstructor
@RocketMQMessageListener(consumerGroup = "cg_xyf_cdp_notify_increaseamt", topic = "tp_xyf_cdp_notify", selectorExpression = "tg_increaseamt", consumeMode = ConsumeMode.CONCURRENTLY)
public class IncreaseAmtRocketMqConsumer extends MqConsumerListener<String> {

    private RedisUtils redisUtils;
    private StrategyEventDispatchService strategyEventDispatchService;

    @Override
    protected void doMessage(String s, String s2, MessageExt messageExt) {
        String lockKey = null;
        if (StringUtils.isNotEmpty(messageExt.getMsgId())) {
            lockKey = SecureUtil.md5("cg:increaseamt:" + messageExt.getMsgId());
        }
        log.info("IncreaseAmtRocketMqConsumer receive message topic={},messageid={},body={}", s, messageExt.getMsgId(), s2);
        try {
            try {
                if (StringUtils.isNotEmpty(lockKey)
                        && !redisUtils.lock(lockKey, "0", 5, TimeUnit.MINUTES)) {
                    log.warn("IncreaseAmtRocketMqConsumer 重复消息, messageid:{}", messageExt.getMsgId());
                    return;
                }
            } catch (Exception ex) {
                log.error("IncreaseAmtRocketMqConsumer, 处理异常", ex);
            }
            NotifyEventDto t = JsonUtil.parse(s2, NotifyEventDto.class);
            BizEventMessageVO bizEventMessageVO = new BizEventMessageVO();
            bizEventMessageVO.setBizEventType("Notify_IncreaseAmt");
            bizEventMessageVO.setAppUserId(t.getAppUserId());
            bizEventMessageVO.setMobile(t.getMobile());
            bizEventMessageVO.setCreditUserId(t.getAppUserId());
            bizEventMessageVO.setApp(t.getApp());
            bizEventMessageVO.setInnerApp(t.getInnerApp());
            bizEventMessageVO.setTriggerDatetime(DateUtil.convertStr(new Date(Optional.ofNullable(t.getEventTime())
                    .orElse(System.currentTimeMillis()))));
            bizEventMessageVO.setEventDatetime(bizEventMessageVO.getTriggerDatetime());

            IncreaseAmtCallbakRequest request = JsonUtil.parse(JsonUtil.toJson(t.getExtraData()), IncreaseAmtCallbakRequest.class);
            boolean succeed = StringUtils.equalsIgnoreCase("S", request.getAdjustment_result());
            // 转换
            BizEventMessageVO.ExtrData extrData = new BizEventMessageVO.ExtrData();
            extrData.setIncreaseAmtStatus(succeed ? 1 : 0);
            extrData.setIncreaseAmtStrategyId(request.getExtend_data().get("xyf_cdp_strategyId").toString());
            // 分转元
            extrData.setAmount(new BigDecimal(request.getExtend_data().get("xyf_cdp_amount").toString())
                    .divide(new BigDecimal("100")));
            if(request.getExtend_data().get("xyf_cdp_increase_code") != null) {
                extrData.setIncreaseCode(request.getExtend_data().get("xyf_cdp_increase_code").toString());
            }
            bizEventMessageVO.setExtrData(extrData);
            strategyEventDispatchService.prescreen(messageExt.getMsgId(), bizEventMessageVO);
        } catch (Exception e) {
            if (StringUtils.isNotEmpty(lockKey)) {
                redisUtils.unlock(lockKey);
            }
            log.error("IncreaseAmtRocketMqConsumer consumer error, messageid={},body={}", messageExt.getMsgId(), s2, e);
        }
    }
}