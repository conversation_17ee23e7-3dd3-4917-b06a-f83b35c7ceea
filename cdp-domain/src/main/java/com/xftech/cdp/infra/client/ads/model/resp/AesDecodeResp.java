package com.xftech.cdp.infra.client.ads.model.resp;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 风险模型分
 * @link <a href="https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001016418">...</a>
 * <AUTHOR>
 * @since 2023/5/23
 */
@Data
public class AesDecodeResp {
        @JSONField(name = "mobile")
        @JsonProperty("mobile")
        private String mobile;

        @JSONField(name = "id_card_number")
        @JsonProperty("id_card_number")
        private String idCardNumber;

        @JSONField(name = "before_mobile")
        @JsonProperty("before_mobile")
        private String beforeMobile;

        @JSONField(name = "before_id_card_number")
        @JsonProperty("before_id_card_number")
        private String beforeIdCardNumber;
}