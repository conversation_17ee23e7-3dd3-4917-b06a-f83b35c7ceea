/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.infra.client.usercenter.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 *
 * <AUTHOR>
 * @version $ BaseCisResp, v 0.1 2023/11/14 16:43 yye.xu Exp $
 */

@Data
public class BaseCisResp<T> {
    private String code;
    private String msg;
    private T data;

    @JsonIgnore
    @JSONField(serialize = false)
    public boolean isCodeSucceed() {
        return StringUtils.equalsIgnoreCase(code, "200");
    }
}