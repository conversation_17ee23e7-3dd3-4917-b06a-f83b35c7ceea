package com.xftech.cdp.domain.event.model.annotation;

import com.xftech.cdp.domain.event.model.enums.DataProcessEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 数据处理器的注解信息
 * <AUTHOR>
 * @version $ ProcessorRule, v 0.1 2024/11/18 11:28 snail Exp $
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface ProcessorRule {
    DataProcessEnum processor();
}
