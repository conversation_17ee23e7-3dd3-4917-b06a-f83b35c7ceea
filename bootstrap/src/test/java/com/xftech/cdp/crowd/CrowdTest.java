package com.xftech.cdp.crowd;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xftech.cdp.BaseTest;
import com.xftech.cdp.api.dto.req.external.ExistDispatchRecordReq;
import com.xftech.cdp.api.dto.resp.CrowdOneResp;
import com.xftech.cdp.api.dto.req.CrowdBatchDeleteReq;
import com.xftech.cdp.api.dto.resp.external.ExistDispatchRecordResp;
import com.xftech.cdp.application.CrowdHandler;
import com.xftech.cdp.domain.cache.CacheCrowdPackService;
import com.xftech.cdp.domain.crowd.model.dispatch.CrowdContext;
import com.xftech.cdp.domain.crowd.model.dispatch.InvokePage;
import com.xftech.cdp.domain.crowd.model.enums.CrowdLabelEnum;
import com.xftech.cdp.domain.crowd.model.enums.CrowdLabelRelationEnum;
import com.xftech.cdp.domain.crowd.model.enums.CrowdPullTypeEnum;
import com.xftech.cdp.domain.crowd.model.enums.LabelEnum;
import com.xftech.cdp.domain.crowd.model.label.LabelConfigurationOptionRadio;
import com.xftech.cdp.domain.crowd.model.label.crowd.CrowdLabelOption;
import com.xftech.cdp.domain.crowd.model.label.crowd.FixedRadioOption;
import com.xftech.cdp.domain.crowd.repository.AdsLabelMonitorDfRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdDetailRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdExecLogRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdPackRepository;
import com.xftech.cdp.domain.crowd.repository.LabelRepository;
import com.xftech.cdp.domain.crowd.service.CrowdPackService;
import com.xftech.cdp.domain.crowd.service.dispatch.CrowdDispatchService;
import com.xftech.cdp.domain.strategy.service.UserDispatchDetailService;
import com.xftech.cdp.infra.client.ads.AdsClient;
import com.xftech.cdp.infra.client.ads.model.req.AdsCrowdReq;
import com.xftech.cdp.infra.client.ads.model.req.AdsCrowdTotalReq;
import com.xftech.cdp.infra.client.ads.model.resp.CrowdBaseResp;
import com.xftech.cdp.infra.client.ads.model.resp.CrowdPushResp;
import com.xftech.cdp.infra.client.ads.model.resp.CrowdTotalResp;
import com.xftech.cdp.infra.config.AppConfigService;
import com.xftech.cdp.infra.rabbitmq.RabbitMqProducer;
import com.xftech.cdp.infra.rabbitmq.factory.impl.BizEventMqService;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventVO;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdLabelDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPackDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.LabelDo;
import com.xftech.cdp.infra.utils.JsonUtil;
import com.xftech.xxljob.config.XxlJobParamConfig;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.lang.reflect.Constructor;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2023/2/14
 */
public class CrowdTest extends BaseTest {
    private static final Logger logger = LoggerFactory.getLogger(CrowdTest.class);

    private static List<InvokePage> invokePages;
    @Resource
    private AdsLabelMonitorDfRepository adsLabelMonitorDfMysqlRepository;
    @Resource
    private CrowdDispatchService crowdDispatchStartRockService;
    @Resource
    private CrowdExecLogRepository crowdExecLogRepository;
    @Resource
    private LabelRepository labelMapper;
    @Resource
    private XxlJobParamConfig xxlJobParamConfig;
    @Resource
    private CrowdDetailRepository crowdDetailRepository;
    @Resource
    private CrowdPackRepository crowdPackRepository;
    @Resource
    private CrowdHandler crowdHandler;
    @Autowired
    private AppConfigService appConfigService;
    @Autowired
    private CrowdPackService crowdPackService;
    @Autowired
    private CacheCrowdPackService cacheCrowdPackService;
    @Autowired
    private AdsClient adsClient;

    @Test
    public void pushCrowd(){
        AdsCrowdReq a = new AdsCrowdReq();
        List<AdsCrowdReq.DataItem> data =Arrays.asList(new AdsCrowdReq.DataItem(12l,"select * from ads_user_label_detail_info_df limit 10"));
        a.setData(data);
        CrowdBaseResp<List<CrowdPushResp>> res = adsClient.pushCrowd(a);
        System.out.println("res====="+ JsonUtil.toJson(res));
    }

    @Test
    public void getCrowdTotal(){
        AdsCrowdTotalReq a = new AdsCrowdTotalReq();
        List<AdsCrowdTotalReq.DataItem> data = Arrays.asList(new AdsCrowdTotalReq.DataItem(12l));
        a.setData(data);
        CrowdBaseResp<List<CrowdTotalResp>> res = adsClient.getCrowdTotal(a);
        System.out.println("res====="+ JsonUtil.toJson(res));
    }

    public static void main(String[] args) {
        Integer a=null;
        System.out.println(a);
        /*subLevel(10001, 4);
        for (InvokePage invokePage : invokePages) {
            System.out.println("***" + invokePage.getAppUserId());
            System.out.println("---" + invokePage.getMaxAppUserId());
        }*/
    }

    public static void subLevel(long maxId, int size) {
        long current = 0;
        long interval = maxId / size;
        invokePages = new ArrayList<>(size);
        int sum = 0;
        while ((current + interval) < maxId && sum < (size - 1)) {
            InvokePage invokePage = new InvokePage();
            invokePage.setAppUserId(current);
            current = current + interval;
            invokePage.setMaxAppUserId(current);
            invokePages.add(invokePage);
            sum++;
        }
        InvokePage invokePage = new InvokePage();
        invokePage.setAppUserId(current);
        invokePage.setMaxAppUserId(maxId);
        invokePages.add(invokePage);
    }

    @Test
    public void t5() {
        crowdDispatchStartRockService.execute(176L);
    }

    @Test
    public void t1() {
        //        Boolean exist = adsLabelMonitorDfMysqlRepository.selectExistByDataDate( LocalDateTime.now() );
        //        System.out.println(exist);
        List<Long> ids = new ArrayList<>();
        ids.add(59L);
        List<Map<String, Long>> longs = crowdExecLogRepository.selectCrowdMaxExecLogIds(ids);
        System.out.println(longs);
    }

    @Test
    public void t2() {
        CrowdContext crowdContext = JSON.parseObject(
                "{\n" +
                        "\t\"delineates\": [{\n" +
                        "\t\t\"crowdLabel\": \"INCLUDE_LABEL\",\n" +
                        "\t\t\"primaryLabels\": [{\n" +
                        "\t\t\t\"execIndex\": 0,\n" +
                        "\t\t\t\"labelRelation\": \"NONE\",\n" +
                        "\t\t\t\"labels\": [{\n" +
                        "\t\t\t\t\"configurationOption\": \"{\\\"no\\\":\\\" is null \\\",\\\"yes\\\":\\\" is not null \\\"}\",\n" +
                        "\t\t\t\t\"configurationOptionType\": \"FIXED_RADIO\",\n" +
                        "\t\t\t\t\"dataWarehouseField\": \"用户属性1\",\n" +
                        "\t\t\t\t\"execIndex\": 0,\n" +
                        "\t\t\t\t\"labelId\": 1,\n" +
                        "\t\t\t\t\"labelName\": \"一个标签\",\n" +
                        "\t\t\t\t\"labelRelation\": \"NONE\",\n" +
                        "\t\t\t\t\"labelValue\": {\n" +
                        "\t\t\t\t\t\"result\": true\n" +
                        "\t\t\t\t}\n" +
                        "\t\t\t}, {\n" +
                        "\t\t\t\t\"configurationOption\": \"{\\\"no\\\":\\\" is null \\\",\\\"yes\\\":\\\" is not null \\\"}\",\n" +
                        "\t\t\t\t\"configurationOptionType\": \"FIXED_RADIO\",\n" +
                        "\t\t\t\t\"dataWarehouseField\": \"用户属性2\",\n" +
                        "\t\t\t\t\"execIndex\": 1,\n" +
                        "\t\t\t\t\"labelId\": 1,\n" +
                        "\t\t\t\t\"labelName\": \"二个标签\",\n" +
                        "\t\t\t\t\"labelRelation\": \"AND\",\n" +
                        "\t\t\t\t\"labelValue\": {\n" +
                        "\t\t\t\t\t\"result\": false\n" +
                        "\t\t\t\t}\n" +
                        "\t\t\t}],\n" +
                        "\t\t\t\"primaryLabel\": \"USER_ATTRIBUTE\"\n" +
                        "\t\t}, {\n" +
                        "\t\t\t\"execIndex\": 1,\n" +
                        "\t\t\t\"labelRelation\": \"OR\",\n" +
                        "\t\t\t\"labels\": [{\n" +
                        "\t\t\t\t\"configurationOption\": \"{\\\"no\\\":\\\" is null \\\",\\\"yes\\\":\\\" is not null \\\"}\",\n" +
                        "\t\t\t\t\"configurationOptionType\": \"FIXED_RADIO\",\n" +
                        "\t\t\t\t\"dataWarehouseField\": \"用户行为1\",\n" +
                        "\t\t\t\t\"execIndex\": 0,\n" +
                        "\t\t\t\t\"labelId\": 1,\n" +
                        "\t\t\t\t\"labelName\": \"三个标签\",\n" +
                        "\t\t\t\t\"labelRelation\": \"NONE\",\n" +
                        "\t\t\t\t\"labelValue\": {\n" +
                        "\t\t\t\t\t\"result\": true\n" +
                        "\t\t\t\t}\n" +
                        "\t\t\t}],\n" +
                        "\t\t\t\"primaryLabel\": \"USER_BEHAVIOR\"\n" +
                        "\t\t}]\n" +
                        "\t}]\n" +
                        "}"
                , CrowdContext.class);


        //        crowdDispatchStartRockService.organizeSqlAndExecute( crowdContext );
        //        System.out.println( crowdContext.getLabelSqlPair() );
    }

    @Test
    public void t3() throws Exception {
        CrowdContext.Delineate delineate = new CrowdContext.Delineate();
        delineate.setCrowdLabel(CrowdLabelEnum.INCLUDE_LABEL);
        List<CrowdContext.PrimaryLabel> primaryLabels = new ArrayList<>();
        delineate.setPrimaryLabels(primaryLabels);
        CrowdContext.PrimaryLabel attribute = new CrowdContext.PrimaryLabel();
        primaryLabels.add(attribute);
        attribute.setLabelRelation(CrowdLabelRelationEnum.NONE);
        attribute.setExecIndex(0);
        attribute.setPrimaryLabel(LabelEnum.PrimaryLabelEnum.USER_ATTRIBUTE);

        List<CrowdContext.Label> labels = new ArrayList<>();
        attribute.setLabels(labels);
        CrowdContext.Label label1 = new CrowdContext.Label();
        LabelConfigurationOptionRadio optionRadio1 = new LabelConfigurationOptionRadio();
        optionRadio1.setYes(" is not null ");
        optionRadio1.setNo(" is null ");
        label1.setConfigurationOption(JSON.toJSONString(optionRadio1));
        label1.setDataWarehouseField("first_activation_success_time");
        label1.setLabelRelation(CrowdLabelRelationEnum.NONE);
        label1.setConfigurationOptionType(LabelEnum.LabelOptionTypeEnum.FIXED_RADIO);
        label1.setLabelId(1L);
        label1.setExecIndex(0);
        FixedRadioOption fixedRadioOption = new FixedRadioOption();
        fixedRadioOption.setResult(true);
        label1.setLabelValue(fixedRadioOption);
        label1.setLabelName("一个标签");


        CrowdContext.Label label2 = new CrowdContext.Label();
        LabelConfigurationOptionRadio optionRadio2 = new LabelConfigurationOptionRadio();
        optionRadio2.setYes(" is not null ");
        optionRadio2.setNo(" is null ");
        label2.setConfigurationOption(JSON.toJSONString(optionRadio2));
        label2.setDataWarehouseField("last_order_time_after_increased");
        label2.setLabelRelation(CrowdLabelRelationEnum.AND);
        label2.setConfigurationOptionType(LabelEnum.LabelOptionTypeEnum.FIXED_RADIO);
        label2.setLabelId(1L);
        label2.setExecIndex(0);
        FixedRadioOption fixedRadioOption2 = new FixedRadioOption();
        fixedRadioOption2.setResult(false);
        label2.setLabelValue(fixedRadioOption2);
        label2.setLabelName("二个标签");

        labels.add(label1);
        labels.add(label2);

        System.out.println(JSON.toJSONString(delineate));


        CrowdContext.PrimaryLabel behavior = new CrowdContext.PrimaryLabel();
        primaryLabels.add(behavior);
        behavior.setLabelRelation(CrowdLabelRelationEnum.OR);
        behavior.setExecIndex(1);
        behavior.setPrimaryLabel(LabelEnum.PrimaryLabelEnum.USER_BEHAVIOR);

        List<CrowdContext.Label> labels1 = new ArrayList<>();
        behavior.setLabels(labels1);
        CrowdContext.Label label11 = new CrowdContext.Label();
        LabelConfigurationOptionRadio behaviorOptionRadio1 = new LabelConfigurationOptionRadio();
        behaviorOptionRadio1.setYes(" is not null ");
        behaviorOptionRadio1.setNo(" is null ");
        label11.setConfigurationOption(JSON.toJSONString(behaviorOptionRadio1));
        label11.setDataWarehouseField("last_payoff_time");
        label11.setLabelRelation(CrowdLabelRelationEnum.NONE);
        label11.setConfigurationOptionType(LabelEnum.LabelOptionTypeEnum.FIXED_RADIO);
        label11.setLabelId(1L);
        label11.setExecIndex(0);
        FixedRadioOption fixedRadioOption11 = new FixedRadioOption();
        fixedRadioOption11.setResult(true);
        label11.setLabelValue(fixedRadioOption11);
        label11.setLabelName("三个标签");
        labels1.add(label11);
        behavior.setLabels(labels1);

        System.out.println(JSON.toJSONString(delineate));


        StringBuilder sql = new StringBuilder("select * from ads_user_label_detail_info_df where");
        for (CrowdContext.PrimaryLabel primaryLabel : delineate.getPrimaryLabels()) {
            StringBuilder labelSql = new StringBuilder();
            labelSql.append(primaryLabel.getLabelRelation().getValue());
            labelSql.append("(");
            for (CrowdContext.Label label : primaryLabel.getLabels()) {
                labelSql.append(label.getLabelRelation().getValue()).append(label.condition());
            }
            labelSql.append(")");
            sql.append(labelSql);
        }

        System.out.println(sql);

        Constructor<CrowdContext> constructor = CrowdContext.class.getDeclaredConstructor(null);
        constructor.setAccessible(true);
        CrowdContext crowdContext = constructor.newInstance(null);
        crowdContext.setDelineates(Lists.list(delineate));

        CrowdPackDo crowdPackBo = new CrowdPackDo();
        crowdPackBo.setId(1L);
        //        crowdContext.setCrowdPackLogId( 1L );
        crowdContext.setCrowdPack(crowdPackBo);
        //        crowdDispatchStartRockService.organizeSqlAndExecute( crowdContext );
        //        crowdDispatchStartRockService.filterAllUserIds( crowdContext );

    }

    @Test
    public void testLabel() {
        // List<LabelDo> labelDos = labelMapper.selectList( IWrapper.of( LabelDo.class ) );
        List<LabelDo> labelDos = labelMapper.getAll(null);
        CrowdLabelDo crowdLabelDo = new CrowdLabelDo();
        for (LabelDo labelBo : labelDos) {
            Integer configurationOptionType = labelBo.getConfigurationOptionType();
            if (configurationOptionType == LabelEnum.LabelOptionTypeEnum.FIXED_RADIO.getCode()) {
                crowdLabelDo.setLabelValue(
                        "{\n" +
                                "  \"result\": false\n" +
                                "}");

            } else if (configurationOptionType == LabelEnum.LabelOptionTypeEnum.FIXED_CHECK.getCode()) {
                crowdLabelDo.setLabelValue("{\n" +
                        "  \"items\": [\n" +
                        "    \"1\"\n" +
                        "  ]\n" +
                        "}");

            } else if (configurationOptionType == LabelEnum.LabelOptionTypeEnum.ENTRY.getCode()) {
                crowdLabelDo.setLabelValue("{\n" +
                        "  \"input\": \"你好\"\n" +
                        "}");
            } else if (configurationOptionType == LabelEnum.LabelOptionTypeEnum.TIME_LIMIT.getCode()) {
                crowdLabelDo.setLabelValue("{\n" +
                        "  \"begin\": \"2023-02-22 11:13:23\",\n" +
                        "  \"end\": \"2023-02-22 11:13:23\"\n" +
                        "}");
            } else if (configurationOptionType == LabelEnum.LabelOptionTypeEnum.VALUE_RANGE.getCode()) {
                crowdLabelDo.setLabelValue("{\n" +
                        "  \"min\": 0,\n" +
                        "  \"max\": 1\n" +
                        "}");
            } else if (configurationOptionType == LabelEnum.LabelOptionTypeEnum.DATA_SEGMENT.getCode()) {
                crowdLabelDo.setLabelValue("{\n" +
                        "  \"segments\": [\n" +
                        "    {\n" +
                        "      \"min\": 0,\n" +
                        "      \"max\": 1\n" +
                        "    }\n" +
                        "  ]\n" +
                        "}");
            } else if (configurationOptionType == LabelEnum.LabelOptionTypeEnum.RANDOM.getCode()) {
                crowdLabelDo.setLabelValue("{\n" +
                        "    \"digits\": 1,\n" +
                        "    \"positionStart\": 0,\n" +
                        "    \"positionEnd\": 1,\n" +
                        "    \"crowdLabelOption\":\n" +
                        "    {\n" +
                        "        \"items\":\n" +
                        "        [\n" +
                        "            \"111\"\n" +
                        "            \"222\"\n" +
                        "        ]\n" +
                        "    }\n" +
                        "}");
                CrowdLabelOption crowdLabelOption = JSON.parseObject(crowdLabelDo.getLabelValue(), LabelEnum.LabelOptionTypeEnum.RANDOM.getClazz());

            }
            System.out.println("label name:" + labelBo.getLabelName() + ":" + crowdLabelDo.getLabelV(LabelEnum.LabelOptionTypeEnum.ofCode(labelBo.getConfigurationOptionType())).condition(labelBo.getDataWarehouseField(), labelBo.getConfigurationOption()));
        }

    }

    @Test
    public void t100() {
        System.out.println(xxlJobParamConfig.getAdminAddresses());
    }

    @Test
    public void txxx() {
        List<CrowdDetailDo> crowdDetailDoList = new ArrayList<>();
        CrowdDetailDo crowdDetailDo = new CrowdDetailDo();
        crowdDetailDo.setCrowdId(0L);
        crowdDetailDo.setApp("app");
        crowdDetailDo.setMobile("123");
        crowdDetailDo.setInnerApp("app");
        crowdDetailDo.setAppUserIdLast2(12);
        crowdDetailDo.setCrowdExecLogId(11L);
        crowdDetailDo.setAbNum("111");
        crowdDetailDo.setUserId(111L);
        crowdDetailDoList.add(crowdDetailDo);
        crowdDetailRepository.saveBatch(crowdDetailDoList);
    }

    @Test
    public void tnn() {
        List<Long> longs = new ArrayList<>();
        longs.add(17L);
        crowdDetailRepository.clearData(LocalDateTimeUtil.beginOfDay(LocalDateTime.now().plus(-1, ChronoUnit.DAYS)), longs);
    }

    @Test
    public void tttt() {
        List<CrowdPackDo> refreshTime = crowdPackRepository.queryLeRefreshTime(LocalDateTime.now(), CrowdPullTypeEnum.USER_LABLE.getCode());
        System.out.println(refreshTime);
    }

    @Test
    public void lc1() {
        // CrowdPackService
        crowdHandler.crowdReset();
    }

    @Test
    public void test11() {
        crowdDispatchStartRockService.execute(204L);
    }

    @Autowired
    private RabbitMqProducer rabbitMqProducer;
    @Test
    public void testMq() {
        for (int i = 0; i < 100; i++) {
            JSONObject data = new JSONObject();
            JSONObject report = new JSONObject();
            data.put("type", "text-sms");
            data.put("report", report);
            report.put("agent_id", "202304101132146406b03ea43ea" + i);
            report.put("mobile", "17090090002");
            report.put("agent", "jiujia");
            report.put("delivery_code", "DB:0108");
            report.put("delivery_msg", "DB:0108");
            report.put("delivery_time", LocalDateTime.now());
            report.put("final_status", "delivered");
            report.put("template_id", "smstpl711002");
            report.put("ua", "postman");
            report.put("batch_num", "2023041011061920");
            rabbitMqProducer.send(data);
        }
    }

    @Autowired
    private BizEventMqService bizEventMqService;
    @Test
    public void test111111() {
        String ss = "{\"biz_event_type\":\"ApplySuccess\",\"mobile\":\"18159310739\",\"app\":\"xyf01\",\"os\":\"ios\",\"inner_app\":\"xyf01\",\"utm_source\":\"cxh_yxqb02\",\"credit_user_id\":\"1054686761\",\"extra_data\":{\"amount\":20500,\"adjust_amount\":3000}}";
        BizEventVO bizEventVO = JSONObject.parseObject(ss, BizEventVO.class);
        try {
            bizEventMqService.sendHighLevelDelayMessage(bizEventVO, 3L);
        } catch (Exception e) {
            logger.warn("发送消息异常", e);
        }
    }


    @Test
    public void a1222() {
        crowdPackService.clearHistoryCrowdDetail();
    }

    @Test
    public void testDeleteCrowd() {
        List<Long> ids = new ArrayList<>();
        ids.add(324L);
        CrowdBatchDeleteReq crowdBatchDeleteReq = new CrowdBatchDeleteReq();
        crowdBatchDeleteReq.setCrowdIds(ids);
        crowdPackService.batchDelete(crowdBatchDeleteReq);
    }

    @Autowired
    private UserDispatchDetailService userDispatchDetailService;
    @Test
    public void a1234() {
        userDispatchDetailService.dispatchFailUserUpdate();
    }


    @Test
    public void dispatch24() {
        ExistDispatchRecordReq existDispatchRecordReq = new ExistDispatchRecordReq();
        existDispatchRecordReq.setAppUserId(1128362134L);
        existDispatchRecordReq.setStrategyIdList(Collections.singletonList(541L));
        ExistDispatchRecordResp existDispatchRecordResp = userDispatchDetailService.existDispatchRecord(existDispatchRecordReq);
        System.out.println(JsonUtil.toJson(existDispatchRecordResp));
    }


    @Test
    public void testcrowdSql(){
        /*CrowdPackDo cd = new CrowdPackDo();
        //StringBuilder right = new StringBuilder("select app_user_id,app,inner_app,ab_num,app_user_id_last2,mobile,register_time,mobile_utm_source,utm_source_list,last_loan_success_utm_source from ads_user_label_detail_info_df where ( ( (attribution_source_big in ('APK','H5全流程','HW','其他','其他付费渠道','自然流量') )) or ( (is_marketing_blacklist = 1 ) or (is_risk_blacklist = 1 ) or (is_no_marketing_user = 1 ) or (is_banned = 1 ))");
        //StringBuilder left = new StringBuilder("select app_user_id,app,inner_app,ab_num,app_user_id_last2,mobile,register_time,mobile_utm_source,utm_source_list,last_loan_success_utm_source from ads_user_label_detail_info_df where ( ( (app in ('xyf01','fxk') )) and ( ((first_activation_success_time between '2023-11-10 00:00:00' and '2023-11-10 23:59:59' ) and (total_remit_times between 0.0 and 0.0 ) and (line_amt between 2000.0 and 99999.0 ) and (fxk_first_loan_time is null ))) and ( (A_score in ('A','B','C','D','E') or (A_score is NULL)))");
        StringBuilder right = new StringBuilder(" select id from user where ( (name=a)");
        StringBuilder left = new StringBuilder("select id from user where ( (age=3)");
        setSql(cd,left,right);
        System.out.println("res1111=="+JSON.toJSONString(cd));
        CrowdPackDo cd2 = new CrowdPackDo();
        setSql(cd2,new StringBuilder("select id from user where ( (age=3)"),null);
        System.out.println("res2222=="+JSON.toJSONString(cd2));
        CrowdPackDo cd3 = new CrowdPackDo();
        setSql(cd3,null,new StringBuilder("select id from user where ( (age=3)"));
        System.out.println("res3333=="+JSON.toJSONString(cd3));*/
        CrowdPackDo cd4 = new CrowdPackDo();
        setSql(cd4,null,null);
        System.out.println("res111=="+JSON.toJSONString(cd4));

    }

    @Test
    public void operateSql(){
        String s = crowdPackService.operateSql();
        System.out.println("res==="+s);
    }

    private void setSql(CrowdPackDo crowdPack,StringBuilder left,StringBuilder right) {
        StringBuilder sql= new StringBuilder();
        if(StringUtils.isNotBlank(left)){
            left.append(" )");
            sql.append(left);
            crowdPack.setIncludeSql(left.toString());
        }
        if(StringUtils.isNotBlank(right)){
            right.append(" )");
            crowdPack.setExcludeSql(right.toString());
        }else {
            crowdPack.setCrowdSql(sql.toString());
            return;
        }
        int whereIndex = right.indexOf("where");
        if(StringUtils.isNotBlank(left) && whereIndex != -1){
            right=new StringBuilder(right.substring(whereIndex + 5));
            sql.append(" and not").append(right);
        }else if(StringUtils.isBlank(left) && whereIndex != -1) {
            sql.append(right.insert(whereIndex + 5, " not"));
        }
        crowdPack.setCrowdSql(sql.toString());
        System.out.println(11);
    }





    @Test
    public void testselectTodayReportCrowd(){
        List<CrowdPackDo> crowdPackDos = crowdPackRepository.selectTodayReportCrowd();
        logger.info(JSONObject.toJSONString(crowdPackDos));
    }

    @Test
    public void testSelectCrowdPack() {
        for (int i = 0; i < 10; i++) {
            System.out.println(i);
            Long id = 1L;
            CrowdPackDo crowdPackDo = cacheCrowdPackService.selectById(id);
            System.out.println(JsonUtil.toJson(crowdPackDo));
            id = 16L;
            crowdPackDo = cacheCrowdPackService.selectById(id);
            System.out.println(JsonUtil.toJson(crowdPackDo));
            System.out.println("---------------------------");
        }
    }
}
