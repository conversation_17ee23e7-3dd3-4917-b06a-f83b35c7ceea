/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.aspect;

import com.alibaba.fastjson.JSON;
import com.xftech.cdp.domain.strategy.service.impl.OperateLogService;
import com.xftech.cdp.infra.annotation.OperateLogAnnotation;
import com.xftech.cdp.infra.repository.cdp.strategy.po.OperateLogDo;
import com.xftech.cdp.infra.utils.OperateLogObjectIdUtils;
import com.xftech.cdp.infra.utils.SsoUtil;
import com.xinfei.xfframework.common.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version $ OperateLogAspect, v 0.1 2024/4/11 14:58 lingang.han Exp $
 */

@Slf4j
@Aspect
@Component
public class OperateLogAspect {

    @Autowired
    private OperateLogService operateLogService;

    @Pointcut("@annotation(com.xftech.cdp.infra.annotation.OperateLogAnnotation)")
    public void logPointCut() {

    }

    @Around("logPointCut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        Object result = new Object();
        try {
            long beginTime = System.currentTimeMillis();
            result = point.proceed();
            List<OperateLogDo> operateLogDoList = buildOperateLog(point, result, beginTime);
            operateLogService.saveBatchOperateLog(operateLogDoList);
        } catch (Exception e) {
            log.error("OperateLogAspect error", e);
            throw e;
        } finally {
            OperateLogObjectIdUtils.clear();
        }
        return result;
    }

    private List<OperateLogDo> buildOperateLog(ProceedingJoinPoint point, Object result, long startTime) {
        List<OperateLogDo> operateLogDoList = new ArrayList<>();
        OperateLogDo op = new OperateLogDo();
        try {
            HttpServletRequest request = getHttpServletRequest();
            MethodSignature signature = (MethodSignature) point.getSignature();
            Method method = signature.getMethod();
            String className = point.getTarget().getClass().getName();
            String methodName = signature.getName();
            String fullMethodName = className + "." + methodName + "()";
            OperateLogAnnotation syslog = method.getAnnotation(OperateLogAnnotation.class);
            op.setUrl(request == null ? fullMethodName : request.getRequestURL().toString().concat("===>").concat(fullMethodName));
            op.setRequestParam(JSON.toJSONString(filterRequestParameter(point)));
            op.setDescription(syslog.description());
            op.setType(syslog.type().getCode());
            op.setModel(syslog.mode().getCode());
            op.setUserIdentify(StringUtils.isBlank(SsoUtil.get().getName()) ? getRequestIp() : SsoUtil.get().getName());
            op.setCreatedTime(LocalDateTime.now());
            op.setUpdatedTime(LocalDateTime.now());
        } catch (Exception e) {
            log.error("OperateLogAspect error", e);
        }

        log.info("请求操作日志：" + JsonUtil.toJson(op));

        long time = System.currentTimeMillis() - startTime;
        op.setResponse(JSON.toJSONString(result));
        op.setDescription(op.getDescription() + "==>请求耗时" + time);
        List<Long> objectIds = OperateLogObjectIdUtils.get();
        if (CollectionUtils.isEmpty(objectIds)) {
            return Collections.singletonList(op);
        }
        objectIds.forEach(objectId -> {
            OperateLogDo operateLog = new OperateLogDo();
            BeanUtils.copyProperties(op, operateLog);
            operateLog.setObjId(objectId);
            operateLogDoList.add(operateLog);
        });
        return operateLogDoList;
    }

    private List<Object> filterRequestParameter(ProceedingJoinPoint point) {
        Object[] args = point.getArgs();
        Stream<?> stream = ArrayUtils.isEmpty(args) ? Stream.empty() : Arrays.stream(args.clone());
        return stream.filter(arg ->
                (!(arg instanceof HttpServletRequest) && !(arg instanceof HttpServletResponse))
        ).map(arg -> {
            if (arg instanceof MultipartFile) {
                MultipartFile file = (MultipartFile) arg;
                Map<String, Object> fileDetails = Collections.emptyMap();
                if (Objects.nonNull(file)) {
                    fileDetails = Collections.singletonMap(
                            "fileName", file.getOriginalFilename()
                    );
                }
                return fileDetails;
            } else {
                return arg;
            }
        }).collect(Collectors.toList());
    }


    public HttpServletRequest getHttpServletRequest() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes == null) {
            return null;
        }
        return ((ServletRequestAttributes) requestAttributes).getRequest();
    }

    private String getRequestIp() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            return request.getRemoteAddr();
        }
        return "";
    }
}