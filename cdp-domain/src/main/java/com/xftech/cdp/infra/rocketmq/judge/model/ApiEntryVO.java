package com.xftech.cdp.infra.rocketmq.judge.model;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version $ apiCredentialStuffingVO, v 0.1 2025/3/14 16:05 tianshuo.qiu Exp $
 */
@Data
public class ApiEntryVO {
    /**
     * userNo
     */
    private Long userNo;

    private String custNo;

    private String app;
    /**
     * 进件数据所属编号inner-app
     */
    private String innerApp;

    private Date creditApplyTime;
    private Date creditResultTime;

    /**
     * 授信状态，success：授信成功，failed：授信失败
     */
    private String activationStatus;
    /**
     * 是否历史授信成功数据标记，1-是，0-否
     */
    private Integer historyCreditSuccessFlag;

    /**
     * 失败类型，risk_failed：风险授信失败，other_failed：非风险被拒
     */
    private String failedType;

    private String traceId;
    //授信可用金额
    private Long avaliableAmount;
    //授信金额
    private Long creditAmount;

}
