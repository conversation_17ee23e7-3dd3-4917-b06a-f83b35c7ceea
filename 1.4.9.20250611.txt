/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_442/Contents/Home/bin/java -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:59096,suspend=y,server=n -javaagent:/Users/<USER>/Library/Caches/JetBrains/IdeaIC2024.3/captureAgent/debugger-agent.jar -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true -Dfile.encoding=UTF-8 -classpath /Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_442/Contents/Home/jre/lib/charsets.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_442/Contents/Home/jre/lib/ext/cldrdata.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_442/Contents/Home/jre/lib/ext/dnsns.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_442/Contents/Home/jre/lib/ext/jaccess.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_442/Contents/Home/jre/lib/ext/localedata.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_442/Contents/Home/jre/lib/ext/nashorn.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_442/Contents/Home/jre/lib/ext/sunec.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_442/Contents/Home/jre/lib/ext/sunjce_provider.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_442/Contents/Home/jre/lib/ext/sunpkcs11.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_442/Contents/Home/jre/lib/ext/zipfs.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_442/Contents/Home/jre/lib/jce.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_442/Contents/Home/jre/lib/jfr.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_442/Contents/Home/jre/lib/jsse.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_442/Contents/Home/jre/lib/management-agent.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_442/Contents/Home/jre/lib/resources.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_442/Contents/Home/jre/lib/rt.jar:/Users/<USER>/IdeaProjects/xyf-cdp/bootstrap/target/classes:/Users/<USER>/IdeaProjects/xyf-cdp/cdp-domain/target/classes:/Users/<USER>/.m2/repository/com/leo/datainsight/client/1.0.7-RELEASE/client-1.0.7-RELEASE.jar:/Users/<USER>/.m2/repository/com/leo/datainsight/common/1.0.7-RELEASE/common-1.0.7-RELEASE.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-text/1.11.0/commons-text-1.11.0.jar:/Users/<USER>/.m2/repository/commons-beanutils/commons-beanutils/1.9.4/commons-beanutils-1.9.4.jar:/Users/<USER>/.m2/repository/commons-collections/commons-collections/3.2.2/commons-collections-3.2.2.jar:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-feign/1.7.0/resilience4j-feign-1.7.0.jar:/Users/<USER>/.m2/repository/io/vavr/vavr/0.10.2/vavr-0.10.2.jar:/Users/<USER>/.m2/repository/io/vavr/vavr-match/0.10.2/vavr-match-0.10.2.jar:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-retry/1.7.0/resilience4j-retry-1.7.0.jar:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-core/1.7.0/resilience4j-core-1.7.0.jar:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-circuitbreaker/1.7.0/resilience4j-circuitbreaker-1.7.0.jar:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-ratelimiter/1.7.0/resilience4j-ratelimiter-1.7.0.jar:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-bulkhead/1.7.0/resilience4j-bulkhead-1.7.0.jar:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-spring-boot2/1.7.0/resilience4j-spring-boot2-1.7.0.jar:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-spring/1.7.0/resilience4j-spring-1.7.0.jar:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-annotations/1.7.0/resilience4j-annotations-1.7.0.jar:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-consumer/1.7.0/resilience4j-consumer-1.7.0.jar:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-circularbuffer/1.7.0/resilience4j-circularbuffer-1.7.0.jar:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-framework-common/1.7.0/resilience4j-framework-common-1.7.0.jar:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-timelimiter/1.7.0/resilience4j-timelimiter-1.7.0.jar:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-micrometer/1.7.0/resilience4j-micrometer-1.7.0.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar:/Users/<USER>/.m2/repository/io/github/openfeign/feign-httpclient/12.4/feign-httpclient-12.4.jar:/Users/<USER>/.m2/repository/com/leo/datainsightcore/client/1.0.2-RELEASE/client-1.0.2-RELEASE.jar:/Users/<USER>/.m2/repository/com/xinfei/xfframework/xfframework-context/1.3.6.20240228/xfframework-context-1.3.6.20240228.jar:/Users/<USER>/.m2/repository/com/squareup/okhttp3/okhttp/3.14.9/okhttp-3.14.9.jar:/Users/<USER>/.m2/repository/com/squareup/okio/okio/1.17.2/okio-1.17.2.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.24/lombok-1.18.24.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.2.15.RELEASE/spring-context-5.2.15.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.2.15.RELEASE/spring-beans-5.2.15.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.2.15.RELEASE/spring-expression-5.2.15.RELEASE.jar:/Users/<USER>/.m2/repository/com/xinfei/xfframework/xfframework-context-base-dto/1.3.6.20240228/xfframework-context-base-dto-1.3.6.20240228.jar:/Users/<USER>/.m2/repository/com/xinfei/chgctrlmng/sdk/1.0.4.20250410/sdk-1.0.4.20250410.jar:/Users/<USER>/.m2/repository/com/xinfei/chgctrlmng/common/1.0.4.20250410/common-1.0.4.20250410.jar:/Users/<USER>/.m2/repository/commons-httpclient/commons-httpclient/3.1/commons-httpclient-3.1.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson2/fastjson2/2.0.53/fastjson2-2.0.53.jar:/Users/<USER>/.m2/repository/org/apache/kafka/kafka-clients/2.5.1/kafka-clients-2.5.1.jar:/Users/<USER>/.m2/repository/com/github/luben/zstd-jni/1.4.4-7/zstd-jni-1.4.4-7.jar:/Users/<USER>/.m2/repository/org/lz4/lz4-java/1.7.1/lz4-java-1.7.1.jar:/Users/<USER>/.m2/repository/org/xerial/snappy/snappy-java/1.1.7.3/snappy-java-1.1.7.3.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar:/Users/<USER>/IdeaProjects/xyf-cdp/cdp-api/target/classes:/Users/<USER>/.m2/repository/com/github/xiaoymin/knife4j-spring-boot-starter/3.0.3/knife4j-spring-boot-starter-3.0.3.jar:/Users/<USER>/.m2/repository/com/github/xiaoymin/knife4j-spring-boot-autoconfigure/3.0.3/knife4j-spring-boot-autoconfigure-3.0.3.jar:/Users/<USER>/.m2/repository/com/github/xiaoymin/knife4j-spring/3.0.3/knife4j-spring-3.0.3.jar:/Users/<USER>/.m2/repository/com/github/xiaoymin/knife4j-annotations/3.0.3/knife4j-annotations-3.0.3.jar:/Users/<USER>/.m2/repository/com/github/xiaoymin/knife4j-core/3.0.3/knife4j-core-3.0.3.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-oas/3.0.0/springfox-oas-3.0.0.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-models/2.1.2/swagger-models-2.1.2.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-bean-validators/3.0.0/springfox-bean-validators-3.0.0.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-core/1.5.22/swagger-core-1.5.22.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.11.4/jackson-dataformat-yaml-2.11.4.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-boot-starter/3.0.0/springfox-boot-starter-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-data-rest/3.0.0/springfox-data-rest-3.0.0.jar:/Users/<USER>/.m2/repository/com/github/xiaoymin/knife4j-spring-ui/3.0.3/knife4j-spring-ui-3.0.3.jar:/Users/<USER>/.m2/repository/com/xinfei/enginepredictcenter/enginepredictcenter-facade/1.0.0.20250228-SNAPSHOT/enginepredictcenter-facade-1.0.0.20250228-20250307.091146-15.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger2/3.0.0/springfox-swagger2-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spi/3.0.0/springfox-spi-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-core/3.0.0/springfox-core-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-schema/3.0.0/springfox-schema-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-common/3.0.0/springfox-swagger-common-3.0.0.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-annotations/2.1.2/swagger-annotations-2.1.2.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/3.0.0/springfox-spring-web-3.0.0.jar:/Users/<USER>/.m2/repository/io/github/classgraph/classgraph/4.8.83/classgraph-4.8.83.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-webmvc/3.0.0/springfox-spring-webmvc-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-webflux/3.0.0/springfox-spring-webflux-3.0.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/2.0.0.RELEASE/spring-plugin-core-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-metadata/2.0.0.RELEASE/spring-plugin-metadata-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-annotations/1.5.20/swagger-annotations-1.5.20.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-models/1.5.20/swagger-models-1.5.20.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-starter-openfeign/3.1.8/spring-cloud-starter-openfeign-3.1.8.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-starter/3.1.7/spring-cloud-starter-3.1.7.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-rsa/1.0.11.RELEASE/spring-security-rsa-1.0.11.RELEASE.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcpkix-jdk15on/1.69/bcpkix-jdk15on-1.69.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.69/bcprov-jdk15on-1.69.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcutil-jdk15on/1.69/bcutil-jdk15on-1.69.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-commons/3.1.7/spring-cloud-commons-3.1.7.jar:/Users/<USER>/.m2/repository/io/github/openfeign/feign-slf4j/11.10/feign-slf4j-11.10.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-openfeign-core/3.1.8/spring-cloud-openfeign-core-3.1.8.jar:/Users/<USER>/.m2/repository/io/github/openfeign/form/feign-form-spring/3.8.0/feign-form-spring-3.8.0.jar:/Users/<USER>/.m2/repository/io/github/openfeign/form/feign-form/3.8.0/feign-form-3.8.0.jar:/Users/<USER>/.m2/repository/commons-fileupload/commons-fileupload/1.5/commons-fileupload-1.5.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.11.0/commons-io-2.11.0.jar:/Users/<USER>/.m2/repository/io/github/openfeign/feign-core/13.0/feign-core-13.0.jar:/Users/<USER>/.m2/repository/javax/validation/validation-api/2.0.1.Final/validation-api-2.0.1.Final.jar:/Users/<USER>/.m2/repository/com/xinfei/appcore/appcore-facade/20250116.20250121.032245-3/appcore-facade-20250116.20250121.032245-3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/2.3.12.RELEASE/spring-boot-starter-data-redis-2.3.12.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/2.3.9.RELEASE/spring-data-redis-2.3.9.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/2.3.9.RELEASE/spring-data-keyvalue-2.3.9.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/2.3.9.RELEASE/spring-data-commons-2.3.9.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.2.15.RELEASE/spring-tx-5.2.15.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/5.2.15.RELEASE/spring-oxm-5.2.15.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/5.2.15.RELEASE/spring-context-support-5.2.15.RELEASE.jar:/Users/<USER>/.m2/repository/io/lettuce/lettuce-core/5.3.7.RELEASE/lettuce-core-5.3.7.RELEASE.jar:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.65.Final/netty-common-4.1.65.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.65.Final/netty-handler-4.1.65.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.65.Final/netty-resolver-4.1.65.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.65.Final/netty-buffer-4.1.65.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.65.Final/netty-codec-4.1.65.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.65.Final/netty-transport-4.1.65.Final.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.3.17.RELEASE/reactor-core-3.3.17.RELEASE.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar:/Users/<USER>/.m2/repository/com/aliyun/oss/aliyun-sdk-oss/3.18.1/aliyun-sdk-oss-3.18.1.jar:/Users/<USER>/.m2/repository/org/jdom/jdom2/2.0.6/jdom2-2.0.6.jar:/Users/<USER>/.m2/repository/org/codehaus/jettison/jettison/1.5.4/jettison-1.5.4.jar:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-core/4.5.10/aliyun-java-sdk-core-4.5.10.jar:/Users/<USER>/.m2/repository/javax/xml/bind/jaxb-api/2.3.1/jaxb-api-2.3.1.jar:/Users/<USER>/.m2/repository/javax/activation/javax.activation-api/1.2.0/javax.activation-api-1.2.0.jar:/Users/<USER>/.m2/repository/org/jacoco/org.jacoco.agent/0.8.5/org.jacoco.agent-0.8.5-runtime.jar:/Users/<USER>/.m2/repository/org/ini4j/ini4j/0.5.4/ini4j-0.5.4.jar:/Users/<USER>/.m2/repository/io/opentracing/opentracing-api/0.33.0/opentracing-api-0.33.0.jar:/Users/<USER>/.m2/repository/io/opentracing/opentracing-util/0.33.0/opentracing-util-0.33.0.jar:/Users/<USER>/.m2/repository/io/opentracing/opentracing-noop/0.33.0/opentracing-noop-0.33.0.jar:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ram/3.1.0/aliyun-java-sdk-ram-3.1.0.jar:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-kms/2.11.0/aliyun-java-sdk-kms-2.11.0.jar:/Users/<USER>/.m2/repository/com/aliyun/java-trace-api/0.2.11-beta/java-trace-api-0.2.11-beta.jar:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-core/0.2.11-beta/aliyun-java-core-0.2.11-beta.jar:/Users/<USER>/.m2/repository/org/dom4j/dom4j/2.1.4/dom4j-2.1.4.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/2.3.12.RELEASE/spring-boot-starter-aop-2.3.12.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.2.15.RELEASE/spring-aop-5.2.15.RELEASE.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.6/aspectjweaver-1.9.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/2.3.12.RELEASE/spring-boot-starter-validation-2.3.12.RELEASE.jar:/Users/<USER>/.m2/repository/org/glassfish/jakarta.el/3.0.3/jakarta.el-3.0.3.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.1.7.Final/hibernate-validator-6.1.7.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.4.2.Final/jboss-logging-3.4.2.Final.jar:/Users/<USER>/.m2/repository/com/xinfei/xfframework/xfframework-starter-apollo/1.3.6.20240228/xfframework-starter-apollo-1.3.6.20240228.jar:/Users/<USER>/.m2/repository/com/xinfei/xfframework/xfframework-starter-apollo-client/1.3.6.20240228/xfframework-starter-apollo-client-1.3.6.20240228.jar:/Users/<USER>/.m2/repository/com/xinfei/xfframework/xfframework-starter-apollo-springboot1/1.3.6.20240228/xfframework-starter-apollo-springboot1-1.3.6.20240228.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel/3.0.5/easyexcel-3.0.5.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi/4.1.2/poi-4.1.2.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-math3/3.6.1/commons-math3-3.6.1.jar:/Users/<USER>/.m2/repository/com/zaxxer/SparseBitSet/1.2/SparseBitSet-1.2.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/4.1.2/poi-ooxml-4.1.2.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.19/commons-compress-1.19.jar:/Users/<USER>/.m2/repository/com/github/virtuald/curvesapi/1.06/curvesapi-1.06.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml-schemas/4.1.2/poi-ooxml-schemas-4.1.2.jar:/Users/<USER>/.m2/repository/org/apache/xmlbeans/xmlbeans/3.1.0/xmlbeans-3.1.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-csv/1.8/commons-csv-1.8.jar:/Users/<USER>/.m2/repository/cglib/cglib/3.3.0/cglib-3.3.0.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm/7.1/asm-7.1.jar:/Users/<USER>/.m2/repository/org/ehcache/ehcache/3.8.1/ehcache-3.8.1.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-runtime/2.3.4/jaxb-runtime-2.3.4.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/txw2/2.3.4/txw2-2.3.4.jar:/Users/<USER>/.m2/repository/com/sun/istack/istack-commons-runtime/3.0.12/istack-commons-runtime-3.0.12.jar:/Users/<USER>/.m2/repository/com/sun/activation/jakarta.activation/1.2.2/jakarta.activation-1.2.2.jar:/Users/<USER>/.m2/repository/com/xuxueli/xxl-job-core/2.2.0/xxl-job-core-2.2.0.jar:/Users/<USER>/.m2/repository/io/netty/netty-all/4.1.65.Final/netty-all-4.1.65.Final.jar:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.8.7/gson-2.8.7.jar:/Users/<USER>/.m2/repository/org/codehaus/groovy/groovy/2.5.14/groovy-2.5.14.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.3.12.RELEASE/spring-boot-starter-web-2.3.12.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.3.12.RELEASE/spring-boot-starter-json-2.3.12.RELEASE.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.11.4/jackson-datatype-jdk8-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.11.4/jackson-datatype-jsr310-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.11.4/jackson-module-parameter-names-2.11.4.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.3.12.RELEASE/spring-boot-starter-tomcat-2.3.12.RELEASE.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.46/tomcat-embed-websocket-9.0.46.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.2.15.RELEASE/spring-web-5.2.15.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.2.15.RELEASE/spring-webmvc-5.2.15.RELEASE.jar:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.5.3.Final/mapstruct-1.5.3.Final.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar:/Users/<USER>/.m2/repository/org/freemarker/freemarker/2.3.30/freemarker-2.3.30.jar:/Users/<USER>/.m2/repository/com/xftech/com.xftech.persistence/1.0.13/com.xftech.persistence-1.0.13.jar:/Users/<USER>/.m2/repository/com/xftech/com.xftech.base/1.0.13/com.xftech.base-1.0.13.jar:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.25/mysql-connector-java-8.0.25.jar:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-starter/2.2.2/mybatis-spring-boot-starter-2.2.2.jar:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/2.2.2/mybatis-spring-boot-autoconfigure-2.2.2.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.9/mybatis-3.5.9.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.0.7/mybatis-spring-2.0.7.jar:/Users/<USER>/.m2/repository/com/alibaba/druid/1.2.16/druid-1.2.16.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/5.7.9/hutool-all-5.7.9.jar:/Users/<USER>/.m2/repository/com/aliyun/alibaba-dingtalk-service-sdk/2.0.0/alibaba-dingtalk-service-sdk-2.0.0.jar:/Users/<USER>/.m2/repository/log4j/log4j/1.2.15/log4j-1.2.15.jar:/Users/<USER>/.m2/repository/javax/mail/mail/1.4/mail-1.4.jar:/Users/<USER>/.m2/repository/javax/activation/activation/1.1/activation-1.1.jar:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.1.1/commons-logging-1.1.1.jar:/Users/<USER>/.m2/repository/org/roaringbitmap/RoaringBitmap/0.9.39/RoaringBitmap-0.9.39.jar:/Users/<USER>/.m2/repository/org/roaringbitmap/shims/0.9.39/shims-0.9.39.jar:/Users/<USER>/.m2/repository/com/cronutils/cron-utils/9.2.0/cron-utils-9.2.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.9/commons-lang3-3.9.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk16/1.46/bcprov-jdk16-1.46.jar:/Users/<USER>/.m2/repository/com/xftech/com.xftech.zipkin.brave/1.0.19/com.xftech.zipkin.brave-1.0.19.jar:/Users/<USER>/.m2/repository/io/zipkin/brave/brave/5.13.11/brave-5.13.11.jar:/Users/<USER>/.m2/repository/io/zipkin/reporter2/zipkin-reporter-brave/2.16.3/zipkin-reporter-brave-2.16.3.jar:/Users/<USER>/.m2/repository/io/zipkin/brave/brave-context-slf4j/5.13.11/brave-context-slf4j-5.13.11.jar:/Users/<USER>/.m2/repository/io/zipkin/brave/brave-instrumentation-http/5.13.11/brave-instrumentation-http-5.13.11.jar:/Users/<USER>/.m2/repository/io/zipkin/brave/brave-instrumentation-httpclient/5.13.11/brave-instrumentation-httpclient-5.13.11.jar:/Users/<USER>/.m2/repository/io/zipkin/brave/brave-instrumentation-spring-webmvc/5.13.11/brave-instrumentation-spring-webmvc-5.13.11.jar:/Users/<USER>/.m2/repository/io/zipkin/brave/brave-instrumentation-servlet/5.13.11/brave-instrumentation-servlet-5.13.11.jar:/Users/<USER>/.m2/repository/io/zipkin/brave/brave-instrumentation-okhttp3/5.13.11/brave-instrumentation-okhttp3-5.13.11.jar:/Users/<USER>/.m2/repository/io/zipkin/brave/brave-instrumentation-mysql8/5.13.11/brave-instrumentation-mysql8-5.13.11.jar:/Users/<USER>/.m2/repository/io/zipkin/reporter2/zipkin-sender-okhttp3/2.16.3/zipkin-sender-okhttp3-2.16.3.jar:/Users/<USER>/.m2/repository/io/zipkin/reporter2/zipkin-reporter/2.16.3/zipkin-reporter-2.16.3.jar:/Users/<USER>/.m2/repository/io/zipkin/zipkin2/zipkin/2.23.2/zipkin-2.23.2.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.15/httpcore-4.4.15.jar:/Users/<USER>/.m2/repository/com/xftech/com.xftech.xxljob/1.0.14/com.xftech.xxljob-1.0.14.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-context/2.2.6.RELEASE/spring-cloud-context-2.2.6.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-crypto/5.3.9.RELEASE/spring-security-crypto-5.3.9.RELEASE.jar:/Users/<USER>/.m2/repository/com/xftech/com.xftech.rabbitmq/1.0.50/com.xftech.rabbitmq-1.0.50.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-amqp/2.3.12.RELEASE/spring-boot-starter-amqp-2.3.12.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/5.2.15.RELEASE/spring-messaging-5.2.15.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/amqp/spring-rabbit/2.4.17/spring-rabbit-2.4.17.jar:/Users/<USER>/.m2/repository/com/rabbitmq/amqp-client/5.9.0/amqp-client-5.9.0.jar:/Users/<USER>/.m2/repository/com/googlecode/aviator/aviator/5.3.3/aviator-5.3.3.jar:/Users/<USER>/.m2/repository/com/xinfei/ssocore/ssocore-client/20240709/ssocore-client-20240709.jar:/Users/<USER>/.m2/repository/cn/dev33/sa-token-spring-boot-starter/1.37.0/sa-token-spring-boot-starter-1.37.0.jar:/Users/<USER>/.m2/repository/cn/dev33/sa-token-servlet/1.37.0/sa-token-servlet-1.37.0.jar:/Users/<USER>/.m2/repository/cn/dev33/sa-token-spring-boot-autoconfig/1.37.0/sa-token-spring-boot-autoconfig-1.37.0.jar:/Users/<USER>/.m2/repository/cn/dev33/sa-token-sso/1.37.0/sa-token-sso-1.37.0.jar:/Users/<USER>/.m2/repository/cn/dev33/sa-token-core/1.37.0/sa-token-core-1.37.0.jar:/Users/<USER>/.m2/repository/cn/dev33/sa-token-redis-jackson/1.37.0/sa-token-redis-jackson-1.37.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-pool2/2.8.1/commons-pool2-2.8.1.jar:/Users/<USER>/.m2/repository/cn/dev33/sa-token-alone-redis/1.37.0/sa-token-alone-redis-1.37.0.jar:/Users/<USER>/.m2/repository/com/xyf/common/xf-random-generator-client/20231226.RELEASE/xf-random-generator-client-20231226.RELEASE.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.15/commons-codec-1.15.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.3.12.RELEASE/spring-boot-autoconfigure-2.3.12.RELEASE.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/30.1-jre/guava-30.1-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.11.4/jackson-databind-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.11.4/jackson-core-2.11.4.jar:/Users/<USER>/.m2/repository/com/xyf/common/xf-random-generator-util/20231226.RELEASE/xf-random-generator-util-20231226.RELEASE.jar:/Users/<USER>/.m2/repository/com/dianping/cat/cat-client-proxy/3.1.7/cat-client-proxy-3.1.7.jar:/Users/<USER>/.m2/repository/com/xinfei/xfframework/xfframework-starter-mq/1.4.9.20250611/xfframework-starter-mq-1.4.9.20250611.jar:/Users/<USER>/.m2/repository/com/xinfei/xfframework/xfframework-starter-mybatispage/1.4.9.20250611/xfframework-starter-mybatispage-1.4.9.20250611.jar:/Users/<USER>/.m2/repository/com/xinfei/xfframework/xfframework-starter-mqbackup/1.4.9.20250611/xfframework-starter-mqbackup-1.4.9.20250611.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.3.12.RELEASE/spring-boot-starter-jdbc-2.3.12.RELEASE.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/3.4.5/HikariCP-3.4.5.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.2.15.RELEASE/spring-jdbc-5.2.15.RELEASE.jar:/Users/<USER>/.m2/repository/com/mysql/mysql-connector-j/8.0.33/mysql-connector-j-8.0.33.jar:/Users/<USER>/.m2/repository/net/javacrumbs/shedlock/shedlock-provider-jdbc-template/4.36.0/shedlock-provider-jdbc-template-4.36.0.jar:/Users/<USER>/.m2/repository/net/javacrumbs/shedlock/shedlock-core/4.36.0/shedlock-core-4.36.0.jar:/Users/<USER>/.m2/repository/org/apache/rocketmq/rocketmq-spring-boot-starter/2.2.3/rocketmq-spring-boot-starter-2.2.3.jar:/Users/<USER>/.m2/repository/org/apache/rocketmq/rocketmq-spring-boot/2.2.3/rocketmq-spring-boot-2.2.3.jar:/Users/<USER>/.m2/repository/org/apache/rocketmq/rocketmq-client/5.0.0/rocketmq-client-5.0.0.jar:/Users/<USER>/.m2/repository/org/apache/rocketmq/rocketmq-common/5.0.0/rocketmq-common-5.0.0.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.0.3/awaitility-4.0.3.jar:/Users/<USER>/.m2/repository/org/apache/rocketmq/rocketmq-acl/5.0.0/rocketmq-acl-5.0.0.jar:/Users/<USER>/.m2/repository/org/apache/rocketmq/rocketmq-proto/2.0.0/rocketmq-proto-2.0.0.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/annotations-api/6.0.53/annotations-api-6.0.53.jar:/Users/<USER>/.m2/repository/org/apache/rocketmq/rocketmq-remoting/5.0.0/rocketmq-remoting-5.0.0.jar:/Users/<USER>/.m2/repository/org/apache/rocketmq/rocketmq-logging/5.0.0/rocketmq-logging-5.0.0.jar:/Users/<USER>/.m2/repository/org/apache/rocketmq/rocketmq-srvutil/5.0.0/rocketmq-srvutil-5.0.0.jar:/Users/<USER>/.m2/repository/commons-cli/commons-cli/1.4/commons-cli-1.4.jar:/Users/<USER>/.m2/repository/com/googlecode/concurrentlinkedhashmap/concurrentlinkedhashmap-lru/1.4.2/concurrentlinkedhashmap-lru-1.4.2.jar:/Users/<USER>/.m2/repository/commons-validator/commons-validator/1.7/commons-validator-1.7.jar:/Users/<USER>/.m2/repository/commons-digester/commons-digester/2.1/commons-digester-2.1.jar:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java-util/3.20.1/protobuf-java-util-3.20.1.jar:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java/3.20.1/protobuf-java-3.20.1.jar:/Users/<USER>/.m2/repository/io/opentelemetry/opentelemetry-api/1.50.0/opentelemetry-api-1.50.0.jar:/Users/<USER>/.m2/repository/io/opentelemetry/opentelemetry-context/1.50.0/opentelemetry-context-1.50.0.jar:/Users/<USER>/.m2/repository/com/xinfei/xfframework/xfframework-starter-feign/1.3.6.20240228/xfframework-starter-feign-1.3.6.20240228.jar:/Users/<USER>/.m2/repository/io/github/openfeign/feign-okhttp/11.4/feign-okhttp-11.4.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-starter-loadbalancer/3.1.7/spring-cloud-starter-loadbalancer-3.1.7.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-loadbalancer/3.1.7/spring-cloud-loadbalancer-3.1.7.jar:/Users/<USER>/.m2/repository/io/projectreactor/addons/reactor-extra/3.3.6.RELEASE/reactor-extra-3.3.6.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-cache/2.3.12.RELEASE/spring-boot-starter-cache-2.3.12.RELEASE.jar:/Users/<USER>/.m2/repository/com/stoyanr/evictor/1.0.0/evictor-1.0.0.jar:/Users/<USER>/.m2/repository/com/github/ben-manes/caffeine/caffeine/2.8.8/caffeine-2.8.8.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.8.0/checker-qual-3.8.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.4.0/error_prone_annotations-2.4.0.jar:/Users/<USER>/.m2/repository/com/xyf/user/cis-query-facade/20241008.RELEASE/cis-query-facade-20241008.RELEASE.jar:/Users/<USER>/.m2/repository/com/xyf/common/cis-common-dto/20231219.1.RELEASE/cis-common-dto-20231219.1.RELEASE.jar:/Users/<USER>/.m2/repository/com/xyf/common/cis-common-constants/1.0.0/cis-common-constants-1.0.0.jar:/Users/<USER>/.m2/repository/com/xyf/user/data-guardian-common/1.0.0/data-guardian-common-1.0.0.jar:/Users/<USER>/.m2/repository/com/xyf/common/xyf-facade-common/1.0.3/xyf-facade-common-1.0.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.11.4/jackson-annotations-2.11.4.jar:/Users/<USER>/.m2/repository/com/xyf/user/user-device-facade/20241118.RELEASE/user-device-facade-20241118.RELEASE.jar:/Users/<USER>/.m2/repository/com/xyf/user/cis-secure-client/20240605/cis-secure-client-20240605.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.46/tomcat-embed-core-9.0.46.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-annotations-api/9.0.46/tomcat-annotations-api-9.0.46.jar:/Users/<USER>/.m2/repository/org/reflections/reflections/0.9.9/reflections-0.9.9.jar:/Users/<USER>/.m2/repository/org/javassist/javassist/3.18.2-GA/javassist-3.18.2-GA.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/annotations/2.0.1/annotations-2.0.1.jar:/Users/<USER>/.m2/repository/com/xinfei/enginebase/util/1.0.4.20250417-3/util-1.0.4.20250417-3.jar:/Users/<USER>/.m2/repository/org/apache/skywalking/apm-toolkit-logback-1.x/8.8.0/apm-toolkit-logback-1.x-8.8.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/2.3.12.RELEASE/spring-boot-starter-test-2.3.12.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.3.12.RELEASE/spring-boot-starter-2.3.12.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.3.12.RELEASE/spring-boot-2.3.12.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.3.12.RELEASE/spring-boot-starter-logging-2.3.12.RELEASE.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.13.3/log4j-to-slf4j-2.13.3.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.30/jul-to-slf4j-1.7.30.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.26/snakeyaml-1.26.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/2.3.12.RELEASE/spring-boot-test-2.3.12.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/2.3.12.RELEASE/spring-boot-test-autoconfigure-2.3.12.RELEASE.jar:/Users/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.4.0/json-path-2.4.0.jar:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.3.1/json-smart-2.3.1.jar:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/2.3.1/accessors-smart-2.3.1.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/2.3.3/jakarta.xml.bind-api-2.3.3.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/1.2.2/jakarta.activation-api-1.2.2.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.16.1/assertj-core-3.16.1.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.6.3/junit-jupiter-5.6.3.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.6.3/junit-jupiter-api-5.6.3.jar:/Users/<USER>/.m2/repository/org/opentest4j/opentest4j/1.2.0/opentest4j-1.2.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.6.3/junit-platform-commons-1.6.3.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.6.3/junit-jupiter-params-5.6.3.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.6.3/junit-jupiter-engine-5.6.3.jar:/Users/<USER>/.m2/repository/org/junit/vintage/junit-vintage-engine/5.6.3/junit-vintage-engine-5.6.3.jar:/Users/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.0/apiguardian-api-1.1.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.6.3/junit-platform-engine-1.6.3.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/3.3.3/mockito-core-3.3.3.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.10.22/byte-buddy-1.10.22.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.10.22/byte-buddy-agent-1.10.22.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/2.6/objenesis-2.6.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/3.3.3/mockito-junit-jupiter-3.3.3.jar:/Users/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.0/jsonassert-1.5.0.jar:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.2.15.RELEASE/spring-core-5.2.15.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.2.15.RELEASE/spring-jcl-5.2.15.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-test/5.2.15.RELEASE/spring-test-5.2.15.RELEASE.jar:/Users/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.7.0/xmlunit-core-2.7.0.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-core/2.17.1/log4j-core-2.17.1.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.13.3/log4j-api-2.13.3.jar:/Users/<USER>/.m2/repository/org/springframework/amqp/spring-amqp/2.4.17/spring-amqp-2.4.17.jar:/Users/<USER>/.m2/repository/org/springframework/retry/spring-retry/1.2.5.RELEASE/spring-retry-1.2.5.RELEASE.jar:/Applications/IntelliJ IDEA CE.app/Contents/lib/idea_rt.jar com.xftech.cdp.CdpApplication
Connected to the target VM, address: '127.0.0.1:59096', transport: 'socket'
当前mybatis 已自动将id主键设置为auto自增类型！
当前使用阿里云mq，地址为rmq-cn-2ml45zb7w0m-vpc.cn-beijing.rmq.aliyuncs.com:8080
2025-06-12 14:05:56.541 [/] [background-preinit] INFO  org.hibernate.validator.internal.util.Version:21 - HV000001: Hibernate Validator 6.1.7.Final
当前使用阿里云mq，地址为rmq-cn-2ml45zb7w0m-vpc.cn-beijing.rmq.aliyuncs.com:8080

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::       (v2.3.12.RELEASE)

2025-06-12 14:05:57.090 [/] [main] INFO  com.xftech.cdp.CdpApplication:652 - The following profiles are active: local
2025-06-12 14:05:59.328 [/] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-12 14:05:59.334 [/] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-12 14:05:59.427 [/] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 84ms. Found 0 Redis repository interfaces.
2025-06-12 14:05:59.693 [/] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner:44 - No MyBatis mapper was found in '[com.xftech.cdp]' package. Please check your configuration.
2025-06-12 14:05:59.696 [/] [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor:413 - Cannot enhance @Configuration bean definition 'multipleRocketMQConfig' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
2025-06-12 14:05:59.696 [/] [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor:413 - Cannot enhance @Configuration bean definition 'rocketMQAutoCreateConfig' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
2025-06-12 14:05:59.696 [/] [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor:413 - Cannot enhance @Configuration bean definition 'rocketMQSubsetBeanConfig' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
2025-06-12 14:05:59.820 [/] [main] INFO  o.springframework.cloud.context.scope.GenericScope:295 - BeanFactory id=466135a6-bb01-3711-b1f6-c8ab0e535ec5
2025-06-12 14:06:00.426 [/] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'mqbackupConfig' of type [com.xinfei.xfframework.common.starter.mqbackup.MqbackupConfig$$EnhancerBySpringCGLIB$$e2072e29] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-12 14:06:00.490 [/] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'rocketMQMessageConverter' of type [org.apache.rocketmq.spring.support.RocketMQMessageConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-12 14:06:00.505 [/] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-12 14:06:00.510 [/] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-12 14:06:00.518 [/] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$416/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-12 14:06:00.535 [/] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-12 14:06:00.554 [/] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'rocketmq-org.apache.rocketmq.spring.autoconfigure.RocketMQProperties' of type [org.apache.rocketmq.spring.autoconfigure.RocketMQProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-12 14:06:00.566 [/] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration$$EnhancerBySpringCGLIB$$44ccd84a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-12 14:06:01.248 [/] [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:108 - Tomcat initialized with port(s): 8080 (http)
2025-06-12 14:06:01.262 [/] [main] INFO  org.apache.coyote.http11.Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-12 14:06:01.263 [/] [main] INFO  org.apache.catalina.core.StandardService:173 - Starting service [Tomcat]
2025-06-12 14:06:01.263 [/] [main] INFO  org.apache.catalina.core.StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.46]
2025-06-12 14:06:01.623 [/] [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/]:173 - Initializing Spring embedded WebApplicationContext
2025-06-12 14:06:01.623 [/] [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext:285 - Root WebApplicationContext: initialization completed in 4513 ms
2025-06-12 14:06:01.631 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: xyf.brave.zipkin.endpoint, beanName: traceConfig, field: com.xftech.zipkin.brave.TraceConfig$$EnhancerBySpringCGLIB$$5258f500.zipkinEndPoint
2025-06-12 14:06:01.632 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: xyf.trace.report.enable, beanName: traceConfig, field: com.xftech.zipkin.brave.TraceConfig$$EnhancerBySpringCGLIB$$5258f500.traceReportEnable
2025-06-12 14:06:01.632 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: xyf.trace.sls.otel.project, beanName: traceConfig, field: com.xftech.zipkin.brave.TraceConfig$$EnhancerBySpringCGLIB$$5258f500.slsOtelProject
2025-06-12 14:06:01.632 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: xyf.trace.sls.otel.instance.id, beanName: traceConfig, field: com.xftech.zipkin.brave.TraceConfig$$EnhancerBySpringCGLIB$$5258f500.slsOtelInstanceId
2025-06-12 14:06:01.632 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: xyf.trace.sls.otel.ak.id, beanName: traceConfig, field: com.xftech.zipkin.brave.TraceConfig$$EnhancerBySpringCGLIB$$5258f500.slsOtelAkId
2025-06-12 14:06:01.632 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: xyf.trace.sls.otel.ak.secret, beanName: traceConfig, field: com.xftech.zipkin.brave.TraceConfig$$EnhancerBySpringCGLIB$$5258f500.slsOtelAkSecret
2025-06-12 14:06:02.161 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: xyf.http.maxTotal, beanName: httpClientConfig, field: com.xftech.zipkin.brave.HttpClientConfig$$EnhancerBySpringCGLIB$$3a30934e.maxTotal
2025-06-12 14:06:02.161 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: xyf.http.maxPreRoute, beanName: httpClientConfig, field: com.xftech.zipkin.brave.HttpClientConfig$$EnhancerBySpringCGLIB$$3a30934e.defaultMaxPerRoute
2025-06-12 14:06:02.161 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: xyf.http.connectTimeout, beanName: httpClientConfig, field: com.xftech.zipkin.brave.HttpClientConfig$$EnhancerBySpringCGLIB$$3a30934e.connectTimeout
2025-06-12 14:06:02.162 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: xyf.http.connectionRequestTimeout, beanName: httpClientConfig, field: com.xftech.zipkin.brave.HttpClientConfig$$EnhancerBySpringCGLIB$$3a30934e.connectionRquestTimeout
2025-06-12 14:06:02.162 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: xyf.http.socketTimeout, beanName: httpClientConfig, field: com.xftech.zipkin.brave.HttpClientConfig$$EnhancerBySpringCGLIB$$3a30934e.socketTimeout
2025-06-12 14:06:02.162 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: xyf.http.connManagerShared, beanName: httpClientConfig, field: com.xftech.zipkin.brave.HttpClientConfig$$EnhancerBySpringCGLIB$$3a30934e.connManagerShared
2025-06-12 14:06:02.162 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: xyf.http.keepAlive, beanName: httpClientConfig, field: com.xftech.zipkin.brave.HttpClientConfig$$EnhancerBySpringCGLIB$$3a30934e.keepAlive
2025-06-12 14:06:02.162 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: xyf.http.maxIdleTime, beanName: httpClientConfig, field: com.xftech.zipkin.brave.HttpClientConfig$$EnhancerBySpringCGLIB$$3a30934e.maxIdleTime
2025-06-12 14:06:02.719 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.http.custom.connectTimeout, beanName: httpCustomConfig, field: com.xftech.cdp.infra.config.HttpCustomConfig.connectTimeout
2025-06-12 14:06:02.719 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.http.custom.connectionRequestTimeout, beanName: httpCustomConfig, field: com.xftech.cdp.infra.config.HttpCustomConfig.connectionRquestTimeout
2025-06-12 14:06:02.719 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.http.custom.socketTimeout, beanName: httpCustomConfig, field: com.xftech.cdp.infra.config.HttpCustomConfig.socketTimeout
2025-06-12 14:06:02.719 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.http.client.maxTotal, beanName: httpCustomConfig, field: com.xftech.cdp.infra.config.HttpCustomConfig.httpMaxTotal
2025-06-12 14:06:02.719 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.http.client.maxPerRoute, beanName: httpCustomConfig, field: com.xftech.cdp.infra.config.HttpCustomConfig.httpMaxPerRoute
2025-06-12 14:06:02.719 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.http.client.validateAfterInactivity, beanName: httpCustomConfig, field: com.xftech.cdp.infra.config.HttpCustomConfig.httpValidateAfterInactivity
2025-06-12 14:06:02.720 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.http.client.socketTimeout, beanName: httpCustomConfig, field: com.xftech.cdp.infra.config.HttpCustomConfig.httpSocketTimeout
2025-06-12 14:06:02.720 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.http.client.connectTimeout, beanName: httpCustomConfig, field: com.xftech.cdp.infra.config.HttpCustomConfig.httpConnectTimeout
2025-06-12 14:06:02.720 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.http.client.connectionRequestTimeout, beanName: httpCustomConfig, field: com.xftech.cdp.infra.config.HttpCustomConfig.httpConnectionRequestTimeout
2025-06-12 14:06:02.720 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.http.client.timeToLive, beanName: httpCustomConfig, field: com.xftech.cdp.infra.config.HttpCustomConfig.timeToLive
2025-06-12 14:06:03.734 [/] [main] INFO  c.xinfei.xfframework.common.starter.mq.MqTemplate:92 - 请注意当前消息发送会限制并发150,超过会限速,详情请访问https://confluence.joyborrow.com/pages/viewpage.action?pageId=30495073
2025-06-12 14:06:03.734 [/] [main] INFO  c.xinfei.xfframework.common.starter.mq.MqTemplate:93 - 单条消息最大值，不能超过102574,超过会发送失败！详情请访问https://confluence.joyborrow.com/pages/viewpage.action?pageId=30495073
2025-06-12 14:06:03.734 [/] [main] INFO  c.xinfei.xfframework.common.starter.mq.MqTemplate:94 - 单条消息消息体大小不能超过102574字节,详情请访问https://confluence.joyborrow.com/pages/viewpage.action?pageId=30495073
2025-06-12 14:06:03.809 [/] [main] INFO  o.a.r.s.autoconfigure.RocketMQAutoConfiguration:121 - a producer (xyf-cdp) init on namesrv rmq-cn-2ml45zb7w0m-vpc.cn-beijing.rmq.aliyuncs.com:8080
2025-06-12 14:06:08.994 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: rocketmq.backup.enable, beanName: mqTemplate, field: com.xinfei.xfframework.common.starter.mq.MqTemplate.backupFlag
2025-06-12 14:06:09.999 [/] [main] INFO  c.xinfei.ssocore.client.config.SsoClientConfigurer:58 - init sso client redis environment...
2025-06-12 14:06:10.114 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: springfox.documentation.swagger-ui.base-url, beanName: springfox.boot.starter.autoconfigure.SwaggerUiWebMvcConfiguration, field: springfox.boot.starter.autoconfigure.SwaggerUiWebMvcConfiguration$$EnhancerBySpringCGLIB$$7aa4a1c1.swaggerBaseUrl
2025-06-12 14:06:10.202 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'push-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:10.467 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'enginepredictcenter' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:10.491 [/] [XF-config-2] INFO  c.xinfei.xfframework.common.starter.mq.MqTemplate:151 - 请注意当前消息发送会限制并发150,超过会限速,详情请访问https://confluence.joyborrow.com/pages/viewpage.action?pageId=30495073
2025-06-12 14:06:10.493 [/] [XF-config-2] INFO  c.xinfei.xfframework.common.starter.mq.MqTemplate:152 - 单条消息最大值，不能超过102574,超过会发送失败！详情请访问https://confluence.joyborrow.com/pages/viewpage.action?pageId=30495073
2025-06-12 14:06:10.526 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'cis-query-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:10.556 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'cis-query-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:10.575 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: xf.user-device-service.url, beanName: userService, field: com.xftech.cdp.infra.client.usercenter.UserService.userServiceUrl
2025-06-12 14:06:10.588 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'datafeaturecore' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:10.691 [/] [main] INFO  org.apache.kafka.clients.producer.ProducerConfig:347 - ProducerConfig values: 
	acks = 1
	batch.size = 16384
	bootstrap.servers = [alikafka-pre-cn-wwo3kbfas003-1-vpc.alikafka.aliyuncs.com:9092, alikafka-pre-cn-wwo3kbfas003-2-vpc.alikafka.aliyuncs.com:9092, alikafka-pre-cn-wwo3kbfas003-3-vpc.alikafka.aliyuncs.com:9092]
	buffer.memory = 33554432
	client.dns.lookup = default
	client.id = producer-1
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 1000
	max.block.ms = 1000
	max.in.flight.requests.per.connection = 5
	max.request.size = 1048576
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.class = class org.apache.kafka.clients.producer.internals.DefaultPartitioner
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retries = 1
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.2
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.StringSerializer

2025-06-12 14:06:10.839 [/] [main] INFO  org.apache.kafka.common.utils.AppInfoParser:117 - Kafka version: 2.5.1
2025-06-12 14:06:10.839 [/] [main] INFO  org.apache.kafka.common.utils.AppInfoParser:118 - Kafka commitId: 0efa8fb0f4c73d92
2025-06-12 14:06:10.839 [/] [main] INFO  org.apache.kafka.common.utils.AppInfoParser:119 - Kafka startTimeMs: 1749708370838
2025-06-12 14:06:10.880 [/] [main] INFO  com.xftech.cdp.infra.utils.Utils:25 - Get ip list:[*************]
2025-06-12 14:06:10.884 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: server.port, beanName: scopedTarget.config, field: com.xftech.cdp.infra.config.Config.port
2025-06-12 14:06:10.884 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: spring.application.name, beanName: scopedTarget.config, field: com.xftech.cdp.infra.config.Config.applicationName
2025-06-12 14:06:10.884 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: aliyun.oss.uploadFilePath, beanName: scopedTarget.config, field: com.xftech.cdp.infra.config.Config.uploadFilePath
2025-06-12 14:06:10.884 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: sms.query.report.day, beanName: scopedTarget.config, field: com.xftech.cdp.infra.config.Config.smsQueryReportDay
2025-06-12 14:06:10.884 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: ads.mobile.aes.key, beanName: scopedTarget.config, field: com.xftech.cdp.infra.config.Config.adsMobileAesKey
2025-06-12 14:06:10.884 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: crowd.pack.sms.flg, beanName: scopedTarget.config, field: com.xftech.cdp.infra.config.Config.crowdPackSmsFlg
2025-06-12 14:06:10.885 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: coupon.query.day, beanName: scopedTarget.config, field: com.xftech.cdp.infra.config.Config.couponQueryDay
2025-06-12 14:06:10.885 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: batch.count.useCache, beanName: scopedTarget.config, field: com.xftech.cdp.infra.config.Config.batchCountUseCache
2025-06-12 14:06:10.885 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: sms.report.over.time, beanName: scopedTarget.config, field: com.xftech.cdp.infra.config.Config.smsReportOverTime
2025-06-12 14:06:10.885 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: refresh.risk.thread.num, beanName: scopedTarget.config, field: com.xftech.cdp.infra.config.Config.refreshRiskThreadNum
2025-06-12 14:06:10.885 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: refresh.risk.page.size, beanName: scopedTarget.config, field: com.xftech.cdp.infra.config.Config.refreshRiskPageSize
2025-06-12 14:06:10.885 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: api.ext.sign.enable, beanName: scopedTarget.config, field: com.xftech.cdp.infra.config.Config.extApiSignEnable
2025-06-12 14:06:10.888 [/] [main] INFO  c.x.c.i.r.s.i.s.SnowflakeSequenceServiceImpl:46 - twepoch:159764278200 ,ip:*************, port:8080
2025-06-12 14:06:11.130 [/] [kafka-producer-network-thread | producer-1] INFO  org.apache.kafka.clients.Metadata:277 - [Producer clientId=producer-1] Cluster ID: 71H1nLm9S5m1vkPkhh2OGg
2025-06-12 14:06:11.221 [/] [main] INFO  c.x.c.i.r.s.i.s.SnowflakeSequenceServiceImpl:52 - start to use redis WorkerId:90 successfully
2025-06-12 14:06:11.444 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'datainsight-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:11.593 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'datainsight' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:11.619 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'datainsightcore' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:11.732 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: CrowdInfoService.queryCrowdInfoRetryCount, beanName: crowdInfoService, field: com.xftech.cdp.distribute.crowd.service.CrowdInfoService.queryCrowdInfoRetryCount
2025-06-12 14:06:11.778 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'appcore' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:11.892 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'goods-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:12.108 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.datacenter.host, beanName: scopedTarget.dataCenterConfig, field: com.xftech.cdp.infra.client.datacenter.config.DataCenterConfig.dataCenterHost
2025-06-12 14:06:12.108 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.datacenter.mobile.decrypt.route, beanName: scopedTarget.dataCenterConfig, field: com.xftech.cdp.infra.client.datacenter.config.DataCenterConfig.mobileDecryptRoute
2025-06-12 14:06:12.108 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.datacenter.mobile.decrypt.key, beanName: scopedTarget.dataCenterConfig, field: com.xftech.cdp.infra.client.datacenter.config.DataCenterConfig.mobileDecryptKey
2025-06-12 14:06:12.108 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.datacenter.id.card.decrypt.route, beanName: scopedTarget.dataCenterConfig, field: com.xftech.cdp.infra.client.datacenter.config.DataCenterConfig.idCardDecryptRoute
2025-06-12 14:06:12.172 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: ReDecisionService.RE_INPUT_COUNT_MAX, beanName: reDecisionService, field: com.xftech.cdp.domain.redecision.service.ReDecisionService.RE_INPUT_COUNT_MAX
2025-06-12 14:06:12.172 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: ReDecisionService.LOCK_SECONDS, beanName: reDecisionService, field: com.xftech.cdp.domain.redecision.service.ReDecisionService.LOCK_SECONDS
2025-06-12 14:06:12.172 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: ReDecisionService.MAX_RETRY_TIMES, beanName: reDecisionService, field: com.xftech.cdp.domain.redecision.service.ReDecisionService.MAX_RETRY_TIMES
2025-06-12 14:06:12.173 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: ReDecisionService.RETRY_INTERVAL_MILLIS, beanName: reDecisionService, field: com.xftech.cdp.domain.redecision.service.ReDecisionService.RETRY_INTERVAL_MILLIS
2025-06-12 14:06:12.183 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: feature_strategy_initiate_cnt, beanName: strategyEngineService, field: com.xftech.cdp.domain.strategy.service.dispatch.event.impl.StrategyEngineService.STRATEGY_INITIATE_CNT
2025-06-12 14:06:12.183 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: feature_last_strategy_initiate_time, beanName: strategyEngineService, field: com.xftech.cdp.domain.strategy.service.dispatch.event.impl.StrategyEngineService.LAST_STRATEGY_INITIATE_TIME
2025-06-12 14:06:12.183 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: feature_last_strategy_initiate_time_default, beanName: strategyEngineService, field: com.xftech.cdp.domain.strategy.service.dispatch.event.impl.StrategyEngineService.LAST_STRATEGY_INITIATE_TIME_DEFAULT
2025-06-12 14:06:12.212 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: http.platform-service.host, beanName: platFormClient, field: com.xftech.cdp.infra.client.platform.PlatFormClient.platformServiceHost
2025-06-12 14:06:12.212 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: http.platform-service.ua, beanName: platFormClient, field: com.xftech.cdp.infra.client.platform.PlatFormClient.platformServiceUa
2025-06-12 14:06:12.212 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: http.platform-service.key, beanName: platFormClient, field: com.xftech.cdp.infra.client.platform.PlatFormClient.platformServiceKey
2025-06-12 14:06:12.350 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'vipcore-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:12.440 [/] [main] WARN  c.x.c.common.httpclient.DefaultHttpInvoker:108 - init default http client invoker
2025-06-12 14:06:12.469 [/] [pool-6-thread-1] INFO  c.x.c.sdk.sdk.change.client.impl.HeartbeatUtil:97 - HeartbeatCheck config: null
2025-06-12 14:06:12.696 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'dps-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:12.737 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.marketing.crowd.user.cache.expire, beanName: crowdCommonService, field: com.xftech.cdp.domain.crowd.service.CrowdCommonService.cacheExpire
2025-06-12 14:06:12.761 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.bi.host, beanName: biConfig, field: com.xftech.cdp.infra.client.bi.config.BiConfig.host
2025-06-12 14:06:12.762 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.bi.route.marketing, beanName: biConfig, field: com.xftech.cdp.infra.client.bi.config.BiConfig.marketingRoute
2025-06-12 14:06:12.873 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: CrowdSliceService.getOssFileSizeRetryCount, beanName: crowdSliceService, field: com.xftech.cdp.distribute.crowd.service.CrowdSliceService.getOssFileSizeRetryCount
2025-06-12 14:06:12.873 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: CrowdSliceService.crowSliceChunkSize, beanName: crowdSliceService, field: com.xftech.cdp.distribute.crowd.service.CrowdSliceService.crowSliceChunkSize
2025-06-12 14:06:13.122 [/] [main] INFO  o.a.r.s.support.DefaultRocketMQListenerContainer:349 - running container: DefaultRocketMQListenerContainer{consumerGroup='cg_xyf_cdp_tp_datainsight_crowdNotice', namespace='', nameServer='rmq-cn-2ml45zb7w0m-vpc.cn-beijing.rmq.aliyuncs.com:8080', topic='tp_datainsight_crowdNotice', consumeMode=ORDERLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-06-12 14:06:13.123 [/] [main] INFO  o.a.r.s.a.ListenerContainerConfiguration:113 - Register the listener to container, listenerBeanName:crowdNoticeMqConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-06-12 14:06:13.125 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: CrowdSliceFallBackTask.getOssInfoRetryCount, beanName: crowdSliceFallBackTask, field: com.xftech.cdp.distribute.crowd.xxljob.CrowdSliceFallBackTask.getOssInfoRetryCount
2025-06-12 14:06:13.128 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: CrowdSyncTask.pageSize, beanName: crowdSyncTask, field: com.xftech.cdp.distribute.crowd.xxljob.CrowdSyncTask.pageSize
2025-06-12 14:06:13.129 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: CrowdSyncTask.crowdInsertBatchSize, beanName: crowdSyncTask, field: com.xftech.cdp.distribute.crowd.xxljob.CrowdSyncTask.crowdInsertBatchSize
2025-06-12 14:06:13.276 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: udp.mq.rabbit.automaticRecovery, beanName: udpMqConfig, field: com.xftech.rabbitmq.config.UdpMqConfig.automaticRecovery
2025-06-12 14:06:13.276 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: upd.mq.rabbit.networkRecoveryInterval, beanName: udpMqConfig, field: com.xftech.rabbitmq.config.UdpMqConfig.networkRecoveryInterval
2025-06-12 14:06:13.276 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: upd.mq.rabbit.topologyRecoveryEnabled, beanName: udpMqConfig, field: com.xftech.rabbitmq.config.UdpMqConfig.topologyRecoveryEnabled
2025-06-12 14:06:13.276 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: upd.mq.rabbit.connectionTimeout, beanName: udpMqConfig, field: com.xftech.rabbitmq.config.UdpMqConfig.connectionTimeout
2025-06-12 14:06:13.276 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: upd.mq.rabbit.handshakeTimeout, beanName: udpMqConfig, field: com.xftech.rabbitmq.config.UdpMqConfig.handshakeTimeout
2025-06-12 14:06:13.276 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: upd.mq.rabbit.shutdownTimeout, beanName: udpMqConfig, field: com.xftech.rabbitmq.config.UdpMqConfig.shutdownTimeout
2025-06-12 14:06:13.276 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: upd.mq.rabbit.durable, beanName: udpMqConfig, field: com.xftech.rabbitmq.config.UdpMqConfig.durable
2025-06-12 14:06:13.276 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: upd.mq.rabbit.autoDelete, beanName: udpMqConfig, field: com.xftech.rabbitmq.config.UdpMqConfig.autoDelete
2025-06-12 14:06:13.276 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: upd.mq.rabbit.exclusive, beanName: udpMqConfig, field: com.xftech.rabbitmq.config.UdpMqConfig.exclusive
2025-06-12 14:06:13.277 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: upd.mq.rabbit.internal, beanName: udpMqConfig, field: com.xftech.rabbitmq.config.UdpMqConfig.internal
2025-06-12 14:06:13.277 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: upd.mq.rabbit.autoAck, beanName: udpMqConfig, field: com.xftech.rabbitmq.config.UdpMqConfig.autoAck
2025-06-12 14:06:13.277 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: upd.mq.rabbit.prefetchCount, beanName: udpMqConfig, field: com.xftech.rabbitmq.config.UdpMqConfig.prefetchCount
2025-06-12 14:06:13.284 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: spring.datasource.type, beanName: udp.datasource-com.xftech.persistence.springboot.config.UdpDataSourceProperties, field: com.xftech.persistence.springboot.config.UdpDataSourceProperties.type
2025-06-12 14:06:13.284 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: spring.datasource.druid.minIdle, beanName: udp.datasource-com.xftech.persistence.springboot.config.UdpDataSourceProperties, field: com.xftech.persistence.springboot.config.UdpDataSourceProperties.defaultMinIdle
2025-06-12 14:06:13.284 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: spring.datasource.druid.maxActive, beanName: udp.datasource-com.xftech.persistence.springboot.config.UdpDataSourceProperties, field: com.xftech.persistence.springboot.config.UdpDataSourceProperties.defaultMaxActive
2025-06-12 14:06:13.284 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: spring.datasource.druid.MaxWait, beanName: udp.datasource-com.xftech.persistence.springboot.config.UdpDataSourceProperties, field: com.xftech.persistence.springboot.config.UdpDataSourceProperties.defaultMaxWait
2025-06-12 14:06:13.284 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: udp.druid.connection.monitor.enable, beanName: udp.datasource-com.xftech.persistence.springboot.config.UdpDataSourceProperties, field: com.xftech.persistence.springboot.config.UdpDataSourceProperties.druidConnectionMonitorEnable
2025-06-12 14:06:13.347 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: udp.datasource.maxResult, beanName: udpLargeSqlConfig, field: com.xftech.persistence.mybatis.interceptor.UdpLargeSqlConfig.maxSize
2025-06-12 14:06:13.347 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: udp.datasource.maxExpend, beanName: udpLargeSqlConfig, field: com.xftech.persistence.mybatis.interceptor.UdpLargeSqlConfig.maxExpend
2025-06-12 14:06:13.361 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: udp.default.datasource.name, beanName: udpDataSourceConfigBuild, field: com.xftech.persistence.springboot.config.UdpDataSourceConfigBuild$$EnhancerBySpringCGLIB$$8ba848f8.defaultDataSourceName
2025-06-12 14:06:13.361 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: spring.datasource.dsname, beanName: udpDataSourceConfigBuild, field: com.xftech.persistence.springboot.config.UdpDataSourceConfigBuild$$EnhancerBySpringCGLIB$$8ba848f8.springDataSourceName
2025-06-12 14:06:13.361 [/] [main] INFO  c.x.p.springboot.config.UdpDataSourceConfigBuild:38 - init UdpDataSourceConfigBuild
2025-06-12 14:06:14.645 [/] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-06-12 14:06:14.647 [/] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'xfRandomGeneratorClientAsyncTaskExecutor'
2025-06-12 14:06:14.658 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: usergroup-metadata.host, beanName: abService, field: cn.xinfei.usergroup.random.client.core.AbService.usergroupMetadataHost
2025-06-12 14:06:14.658 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: message-proxy.host, beanName: abService, field: cn.xinfei.usergroup.random.client.core.AbService.messageProxyHost
2025-06-12 14:06:15.697 [/] [main] INFO  com.xftech.cdp.infra.rabbitmq.config.SmsMqConfig:37 - 短信回执队列MQ-创建连接、消费者......
2025-06-12 14:06:15.697 [/] [main] INFO  com.xftech.cdp.infra.rabbitmq.config.SmsMqConfig:45 - queueConfig=com.xftech.cdp.infra.rabbitmq.config.SmsMqConfig$$EnhancerBySpringCGLIB$$90e2b1f2@74c997b1
2025-06-12 14:06:16.184 [/] [AMQP Connection *************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler:115 - An unexpected connection driver error occured (Exception message: Socket closed)
2025-06-12 14:06:16.187 [/] [main] WARN  com.xftech.cdp.infra.rabbitmq.RabbitMqConsumer:105 - mq consumer create fail.
java.io.IOException: null
	at com.rabbitmq.client.impl.AMQChannel.wrap(AMQChannel.java:129)
	at com.rabbitmq.client.impl.AMQChannel.wrap(AMQChannel.java:125)
	at com.rabbitmq.client.impl.AMQChannel.exnWrappingRpc(AMQChannel.java:147)
	at com.rabbitmq.client.impl.AMQConnection.start(AMQConnection.java:439)
	at com.rabbitmq.client.impl.recovery.RecoveryAwareAMQConnectionFactory.newConnection(RecoveryAwareAMQConnectionFactory.java:64)
	at com.rabbitmq.client.impl.recovery.AutorecoveringConnection.init(AutorecoveringConnection.java:156)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1130)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1087)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1045)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1207)
	at com.xftech.rabbitmq.core.AbsUdpMqService.initConnection(AbsUdpMqService.java:69)
	at com.xftech.rabbitmq.core.AbsUdpMqService.<init>(AbsUdpMqService.java:43)
	at com.xftech.rabbitmq.core.UdpMqConsumer.<init>(UdpMqConsumer.java:33)
	at com.xftech.rabbitmq.build.ConnectionBuilder.build(ConnectionBuilder.java:65)
	at com.xftech.rabbitmq.UdpMqConsumerManager.createUdpMqConsumer(UdpMqConsumerManager.java:39)
	at com.xftech.cdp.infra.rabbitmq.factory.impl.SmsMqService.initConnection(SmsMqService.java:51)
	at com.xftech.cdp.infra.rabbitmq.factory.impl.SmsMqService.init(SmsMqService.java:38)
	at com.xftech.cdp.infra.rabbitmq.RabbitMqConsumer.afterPropertiesSet(RabbitMqConsumer.java:34)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1858)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:897)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:143)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:755)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:312)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1247)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1236)
	at com.xftech.cdp.CdpApplication.main(CdpApplication.java:25)
Caused by: com.rabbitmq.client.ShutdownSignalException: connection error; protocol method: #method<connection.close>(reply-code=530, reply-text=NOT_ALLOWED - vhost dev_xyf_queue_cdp not found, class-id=10, method-id=40)
	at com.rabbitmq.utility.ValueOrException.getValue(ValueOrException.java:66)
	at com.rabbitmq.utility.BlockingValueOrException.uninterruptibleGetValue(BlockingValueOrException.java:36)
	at com.rabbitmq.client.impl.AMQChannel$BlockingRpcContinuation.getReply(AMQChannel.java:502)
	at com.rabbitmq.client.impl.AMQChannel.privateRpc(AMQChannel.java:293)
	at com.rabbitmq.client.impl.AMQChannel.exnWrappingRpc(AMQChannel.java:141)
	... 34 common frames omitted
2025-06-12 14:06:16.580 [/] [main] INFO  o.a.r.s.support.DefaultRocketMQListenerContainer:349 - running container: DefaultRocketMQListenerContainer{consumerGroup='cg_xyf_cdp_notify_increaseamt', namespace='', nameServer='rmq-cn-2ml45zb7w0m-vpc.cn-beijing.rmq.aliyuncs.com:8080', topic='tp_xyf_cdp_notify', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='tg_increaseamt', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-06-12 14:06:16.580 [/] [main] INFO  o.a.r.s.a.ListenerContainerConfiguration:113 - Register the listener to container, listenerBeanName:increaseAmtRocketMqConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-06-12 14:06:16.769 [/] [main] INFO  o.a.r.s.support.DefaultRocketMQListenerContainer:349 - running container: DefaultRocketMQListenerContainer{consumerGroup='cg_xyf_cdp_notify_tg_liferights', namespace='', nameServer='rmq-cn-2ml45zb7w0m-vpc.cn-beijing.rmq.aliyuncs.com:8080', topic='tp_xyf_cdp_notify', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='tg_liferights', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-06-12 14:06:16.769 [/] [main] INFO  o.a.r.s.a.ListenerContainerConfiguration:113 - Register the listener to container, listenerBeanName:lifeRightsRocketMqConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-06-12 14:06:16.811 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: NewRegisterHandler.count, beanName: newRegisterHandler, field: com.xftech.cdp.infra.rocketmq.user.NewRegisterHandler.count
2025-06-12 14:06:16.811 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: NewRegisterHandler.sleepTime, beanName: newRegisterHandler, field: com.xftech.cdp.infra.rocketmq.user.NewRegisterHandler.sleepTime
2025-06-12 14:06:17.008 [/] [main] INFO  o.a.r.s.support.DefaultRocketMQListenerContainer:349 - running container: DefaultRocketMQListenerContainer{consumerGroup='cg_xyf_cdp_tp_app_push_report_callback_tg_xyf_cdp', namespace='', nameServer='rmq-cn-2ml45zb7w0m-vpc.cn-beijing.rmq.aliyuncs.com:8080', topic='tp_app_push_report_callback', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='tg_xyf_cdp', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-06-12 14:06:17.008 [/] [main] INFO  o.a.r.s.a.ListenerContainerConfiguration:113 - Register the listener to container, listenerBeanName:pushCallRocketMqConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-06-12 14:06:17.267 [/] [main] INFO  o.a.r.s.support.DefaultRocketMQListenerContainer:349 - running container: DefaultRocketMQListenerContainer{consumerGroup='cg_xyf_cdp_notify_tg_xDayInterestFree', namespace='', nameServer='rmq-cn-2ml45zb7w0m-vpc.cn-beijing.rmq.aliyuncs.com:8080', topic='tp_xyf_cdp_notify', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='tg_xDayInterestFree', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-06-12 14:06:17.267 [/] [main] INFO  o.a.r.s.a.ListenerContainerConfiguration:113 - Register the listener to container, listenerBeanName:XDayInterestFreeRocketMqConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-06-12 14:06:17.398 [/] [main] INFO  o.a.r.s.support.DefaultRocketMQListenerContainer:349 - running container: DefaultRocketMQListenerContainer{consumerGroup='cg_xyf_cdp_tp_psenginecore_advance_order_create', namespace='', nameServer='rmq-cn-2ml45zb7w0m-vpc.cn-beijing.rmq.aliyuncs.com:8080', topic='tp_psenginecore_advance_order_create', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-06-12 14:06:17.398 [/] [main] INFO  o.a.r.s.a.ListenerContainerConfiguration:113 - Register the listener to container, listenerBeanName:advanceOrderRocketMsgConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_6
2025-06-12 14:06:17.606 [/] [main] INFO  o.a.r.s.support.DefaultRocketMQListenerContainer:349 - running container: DefaultRocketMQListenerContainer{consumerGroup='cg_xyf_cdp_tg_telemkt', namespace='', nameServer='rmq-cn-2ml45zb7w0m-vpc.cn-beijing.rmq.aliyuncs.com:8080', topic='tp_call_center_ai_callback', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='tg_telemkt', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-06-12 14:06:17.608 [/] [main] INFO  o.a.r.s.a.ListenerContainerConfiguration:113 - Register the listener to container, listenerBeanName:aiCallBackStatusRocketMqConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_7
2025-06-12 14:06:17.847 [/] [main] INFO  o.a.r.s.support.DefaultRocketMQListenerContainer:349 - running container: DefaultRocketMQListenerContainer{consumerGroup='cg_xyf_cdp_tp_telemkt_name_ai_call_result', namespace='', nameServer='rmq-cn-2ml45zb7w0m-vpc.cn-beijing.rmq.aliyuncs.com:8080', topic='tp_telemkt_name_ai_call_result', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-06-12 14:06:17.848 [/] [main] INFO  o.a.r.s.a.ListenerContainerConfiguration:113 - Register the listener to container, listenerBeanName:aiProntoCallBackRocketMqConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_8
2025-06-12 14:06:18.053 [/] [main] INFO  o.a.r.s.support.DefaultRocketMQListenerContainer:349 - running container: DefaultRocketMQListenerContainer{consumerGroup='cg_xyf_cdp_tp_userassetcore_cash_coupon_distribute_success', namespace='', nameServer='rmq-cn-2ml45zb7w0m-vpc.cn-beijing.rmq.aliyuncs.com:8080', topic='tp_userassetcore_cash_coupon_distribute_success', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-06-12 14:06:18.053 [/] [main] INFO  o.a.r.s.a.ListenerContainerConfiguration:113 - Register the listener to container, listenerBeanName:couponCallbackRocketMsgConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_9
2025-06-12 14:06:18.249 [/] [main] INFO  o.a.r.s.support.DefaultRocketMQListenerContainer:349 - running container: DefaultRocketMQListenerContainer{consumerGroup='cg_xyf_cdp_tp_rcspt_credit_apply_message', namespace='', nameServer='rmq-cn-2ml45zb7w0m-vpc.cn-beijing.rmq.aliyuncs.com:8080', topic='tp_rcspt_credit_apply_message', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-06-12 14:06:18.250 [/] [main] INFO  o.a.r.s.a.ListenerContainerConfiguration:113 - Register the listener to container, listenerBeanName:creditApplyMsgConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_10
2025-06-12 14:06:18.445 [/] [main] INFO  o.a.r.s.support.DefaultRocketMQListenerContainer:349 - running container: DefaultRocketMQListenerContainer{consumerGroup='cg_xyf_cdp_tp_distributecore_approval_success', namespace='', nameServer='rmq-cn-2ml45zb7w0m-vpc.cn-beijing.rmq.aliyuncs.com:8080', topic='tp_distributecore_approval_success', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-06-12 14:06:18.445 [/] [main] INFO  o.a.r.s.a.ListenerContainerConfiguration:113 - Register the listener to container, listenerBeanName:distributionRocketMsgConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_11
2025-06-12 14:06:18.643 [/] [main] INFO  o.a.r.s.support.DefaultRocketMQListenerContainer:349 - running container: DefaultRocketMQListenerContainer{consumerGroup='cg_xyf_cdp_tp_rcspt_risk_pre_amount_change_message', namespace='', nameServer='rmq-cn-2ml45zb7w0m-vpc.cn-beijing.rmq.aliyuncs.com:8080', topic='tp_rcspt_risk_pre_amount_change_message', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-06-12 14:06:18.643 [/] [main] INFO  o.a.r.s.a.ListenerContainerConfiguration:113 - Register the listener to container, listenerBeanName:extractQuotaCardGuideEventRocketMq, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_12
2025-06-12 14:06:18.918 [/] [main] INFO  o.a.r.s.support.DefaultRocketMQListenerContainer:349 - running container: DefaultRocketMQListenerContainer{consumerGroup='cg_xyf_cdp_tp_batchdeduct_order_pay_result_notify', namespace='', nameServer='rmq-cn-2ml45zb7w0m-vpc.cn-beijing.rmq.aliyuncs.com:8080', topic='tp_batchdeduct_order_pay_result_notify', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-06-12 14:06:18.918 [/] [main] INFO  o.a.r.s.a.ListenerContainerConfiguration:113 - Register the listener to container, listenerBeanName:orderPayMemberEventRocketMq, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_13
2025-06-12 14:06:19.109 [/] [main] INFO  o.a.r.s.support.DefaultRocketMQListenerContainer:349 - running container: DefaultRocketMQListenerContainer{consumerGroup='cg_xyf_cdp_tp_custprod_logoff_apply', namespace='', nameServer='rmq-cn-2ml45zb7w0m-vpc.cn-beijing.rmq.aliyuncs.com:8080', topic='tp_custprod_logoff_apply', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-06-12 14:06:19.109 [/] [main] INFO  o.a.r.s.a.ListenerContainerConfiguration:113 - Register the listener to container, listenerBeanName:userLogOffEventRocketMq, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_14
2025-06-12 14:06:19.310 [/] [main] INFO  o.a.r.s.support.DefaultRocketMQListenerContainer:349 - running container: DefaultRocketMQListenerContainer{consumerGroup='cg_xyf_cdp_notify_tg_vc_notify', namespace='', nameServer='rmq-cn-2ml45zb7w0m-vpc.cn-beijing.rmq.aliyuncs.com:8080', topic='tp_xyf_cdp_notify', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='tg_vc_notify', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-06-12 14:06:19.310 [/] [main] INFO  o.a.r.s.a.ListenerContainerConfiguration:113 - Register the listener to container, listenerBeanName:vipCardSignEventRocketMq, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_15
2025-06-12 14:06:19.499 [/] [main] INFO  o.a.r.s.support.DefaultRocketMQListenerContainer:349 - running container: DefaultRocketMQListenerContainer{consumerGroup='cg_xyf_cdp_tp_ugchanprod_api_judge_engine', namespace='', nameServer='rmq-cn-2ml45zb7w0m-vpc.cn-beijing.rmq.aliyuncs.com:8080', topic='tp_ugchanprod_api_judge_engine', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-06-12 14:06:19.499 [/] [main] INFO  o.a.r.s.a.ListenerContainerConfiguration:113 - Register the listener to container, listenerBeanName:apiCredentialStuffingMsgConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_16
2025-06-12 14:06:19.696 [/] [main] INFO  o.a.r.s.support.DefaultRocketMQListenerContainer:349 - running container: DefaultRocketMQListenerContainer{consumerGroup='cg_xyf_cdp_tp_apiopfcore_credit_result', namespace='', nameServer='rmq-cn-2ml45zb7w0m-vpc.cn-beijing.rmq.aliyuncs.com:8080', topic='tp_apiopfcore_credit_result', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-06-12 14:06:19.696 [/] [main] INFO  o.a.r.s.a.ListenerContainerConfiguration:113 - Register the listener to container, listenerBeanName:apiEntryMsgConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_17
2025-06-12 14:06:19.903 [/] [main] INFO  o.a.r.s.support.DefaultRocketMQListenerContainer:349 - running container: DefaultRocketMQListenerContainer{consumerGroup='cg_xyf_cdp_tp_apiopfcore_api_judge_all', namespace='', nameServer='rmq-cn-2ml45zb7w0m-vpc.cn-beijing.rmq.aliyuncs.com:8080', topic='tp_apiopfcore_api_judge_all', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-06-12 14:06:19.903 [/] [main] INFO  o.a.r.s.a.ListenerContainerConfiguration:113 - Register the listener to container, listenerBeanName:tpApiJudgeRocketMqConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_18
2025-06-12 14:06:20.123 [/] [main] INFO  o.a.r.s.support.DefaultRocketMQListenerContainer:349 - running container: DefaultRocketMQListenerContainer{consumerGroup='cg_xyf_cdp_tp_lendtrade_order_final_status', namespace='', nameServer='rmq-cn-2ml45zb7w0m-vpc.cn-beijing.rmq.aliyuncs.com:8080', topic='tp_lendtrade_order_final_status', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-06-12 14:06:20.124 [/] [main] INFO  o.a.r.s.a.ListenerContainerConfiguration:113 - Register the listener to container, listenerBeanName:lendTradeOrderFinalStatusRocketMqConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_19
2025-06-12 14:06:20.393 [/] [main] INFO  o.a.r.s.support.DefaultRocketMQListenerContainer:349 - running container: DefaultRocketMQListenerContainer{consumerGroup='cg_xyf_cdp_tp_lendtrade_derating', namespace='', nameServer='rmq-cn-2ml45zb7w0m-vpc.cn-beijing.rmq.aliyuncs.com:8080', topic='tp_lendtrade_derating', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-06-12 14:06:20.393 [/] [main] INFO  o.a.r.s.a.ListenerContainerConfiguration:113 - Register the listener to container, listenerBeanName:lendtradeDerateRocketMqConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_20
2025-06-12 14:06:20.505 [/] [refresh-app-banner-market-strategy] INFO  c.x.c.d.strategy.service.impl.AppBannerServiceImpl:131 - *******************************App弹窗资源数据开始加载*********************************************
2025-06-12 14:06:20.606 [/] [main] INFO  o.a.r.s.support.DefaultRocketMQListenerContainer:349 - running container: DefaultRocketMQListenerContainer{consumerGroup='cg_xyf_cdp_tp_biz_report_Login1', namespace='', nameServer='rmq-cn-2ml45zb7w0m-vpc.cn-beijing.rmq.aliyuncs.com:8080', topic='tp_biz_report_Login1', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-06-12 14:06:20.606 [/] [main] INFO  o.a.r.s.a.ListenerContainerConfiguration:113 - Register the listener to container, listenerBeanName:login1RocketMsgConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_21
2025-06-12 14:06:20.610 [/] [refresh-app-banner-market-strategy] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: udp.reject.mybatis, beanName: udpMybatisConfig, field: com.xftech.persistence.mybatis.config.UdpMybatisConfig.injectEnable
2025-06-12 14:06:20.619 [/] [refresh-app-banner-market-strategy] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: spring.datasrouce.dsname, beanName: transactionService, field: com.xftech.persistence.transaction.TransactionService.springDataSourceName
2025-06-12 14:06:20.622 [/] [refresh-app-banner-market-strategy] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: spring.datasource.dsname, beanName: mybatisDataService, field: com.xftech.persistence.mybatis.MybatisDataService.springDataSourceName
2025-06-12 14:06:20.827 [/] [main] INFO  o.a.r.s.support.DefaultRocketMQListenerContainer:349 - running container: DefaultRocketMQListenerContainer{consumerGroup='cg_xyf_cdp_tp_rcspt_risk_access_control_message', namespace='', nameServer='rmq-cn-2ml45zb7w0m-vpc.cn-beijing.rmq.aliyuncs.com:8080', topic='tp_rcspt_risk_access_control_message', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-06-12 14:06:20.827 [/] [main] INFO  o.a.r.s.a.ListenerContainerConfiguration:113 - Register the listener to container, listenerBeanName:accessControlDiversionMsgConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_22
2025-06-12 14:06:21.073 [/] [main] INFO  o.a.r.s.support.DefaultRocketMQListenerContainer:349 - running container: DefaultRocketMQListenerContainer{consumerGroup='cg_xyf_cdp_tg_personal_api_fst_login_temp', namespace='', nameServer='rmq-cn-2ml45zb7w0m-vpc.cn-beijing.rmq.aliyuncs.com:8080', topic='tp_rcspt_risk_amount_change_message', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='tg_personal_api_fst_login_temp', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-06-12 14:06:21.073 [/] [main] INFO  o.a.r.s.a.ListenerContainerConfiguration:113 - Register the listener to container, listenerBeanName:appLoginIncreaseCreditRocketMqConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_23
2025-06-12 14:06:27.481 [/] [refresh-app-banner-market-strategy] INFO  c.x.p.mybatis.interceptor.UdpSqlLogInterceptor:39 - Preparing(strategyMarketChannel.selectStrategyIdsWithAppBannerChannel):
select
         
        id, strategy_id, strategy_group_id, market_channel, template_id,app,dispatch_app, send_time, cron, xxl_job_id, created_time, updated_time, d_flag, created_op, updated_op, ext_info
     
        from strategy_market_channel
        where d_flag = 0
        and market_channel = ?
Parameters:{marketChannel=100}
2025-06-12 14:06:27.556 [/] [refresh-app-banner-market-strategy] INFO  com.alibaba.druid.pool.DruidDataSource:996 - {dataSource-1} inited
2025-06-12 14:06:27.712 [/] [main] INFO  o.a.r.s.support.DefaultRocketMQListenerContainer:349 - running container: DefaultRocketMQListenerContainer{consumerGroup='cg_xyf_cdp_tg_cxh_repay_xyf01_increase_amount', namespace='', nameServer='rmq-cn-2ml45zb7w0m-vpc.cn-beijing.rmq.aliyuncs.com:8080', topic='tp_rcspt_risk_amount_change_message', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='tg_cxh_repay_xyf01_increase_amount', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-06-12 14:06:27.712 [/] [main] INFO  o.a.r.s.a.ListenerContainerConfiguration:113 - Register the listener to container, listenerBeanName:cxhRepayXyf01IncreaseCreditRocketMqConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_24
2025-06-12 14:06:27.918 [/] [main] INFO  o.a.r.s.support.DefaultRocketMQListenerContainer:349 - running container: DefaultRocketMQListenerContainer{consumerGroup='cg_xyf_cdp_tp_rcspt_risk_amount_change_message', namespace='', nameServer='rmq-cn-2ml45zb7w0m-vpc.cn-beijing.rmq.aliyuncs.com:8080', topic='tp_rcspt_risk_amount_change_message', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-06-12 14:06:27.918 [/] [main] INFO  o.a.r.s.a.ListenerContainerConfiguration:113 - Register the listener to container, listenerBeanName:rcsptIncreaseCreditRocketMqConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_25
2025-06-12 14:06:28.117 [/] [main] INFO  o.a.r.s.support.DefaultRocketMQListenerContainer:349 - running container: DefaultRocketMQListenerContainer{consumerGroup='cg_xyf_cdp_tp_repaytrade_repayResult_repaySuccess', namespace='', nameServer='rmq-cn-2ml45zb7w0m-vpc.cn-beijing.rmq.aliyuncs.com:8080', topic='tp_repaytrade_repayResult', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-06-12 14:06:28.117 [/] [main] INFO  o.a.r.s.a.ListenerContainerConfiguration:113 - Register the listener to container, listenerBeanName:repaySuccessMsgConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_26
2025-06-12 14:06:28.329 [/] [main] INFO  o.a.r.s.support.DefaultRocketMQListenerContainer:349 - running container: DefaultRocketMQListenerContainer{consumerGroup='cg_xyf_cdp_tp_repaytrade_repayResult_settleSuccess', namespace='', nameServer='rmq-cn-2ml45zb7w0m-vpc.cn-beijing.rmq.aliyuncs.com:8080', topic='tp_repaytrade_repayResult', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-06-12 14:06:28.329 [/] [main] INFO  o.a.r.s.a.ListenerContainerConfiguration:113 - Register the listener to container, listenerBeanName:settleSuccessMsgConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_27
2025-06-12 14:06:28.362 [/] [refresh-app-banner-market-strategy] INFO  c.x.p.mybatis.interceptor.UdpSqlLogInterceptor:39 - Preparing(strategy.getByIds):
select
         
        id, name,user_convert, detail_description, ab_test, ab_type, biz_key, send_ruler, validity_begin, validity_end, send_frequency, crowd_pack_id,market_channel,status,group_type,market_crowd_type,business_type,flow_ctrl_id, created_time, updated_time, d_flag, created_op, updated_op,updated_op_mobile,type,engine_code,dispatch_config,flow_no,dispatch_type,
        market_type, calling_source
     
        from strategy
        where id in
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         ) 
        and d_flag = 0
Parameters:{ids=[2656, 2658, 2660, 2661, 2537, 2538, 2539, 2540, 2541, 2542, 2607, 2545, 2617, 2620]}
2025-06-12 14:06:28.482 [/] [refresh-app-banner-market-strategy] INFO  c.x.p.mybatis.interceptor.UdpSqlLogInterceptor:39 - Preparing(strategyMarketChannel.selectByStrategyGroupId):
select
         
        id, strategy_id, strategy_group_id, market_channel, template_id,app,dispatch_app, send_time, cron, xxl_job_id, created_time, updated_time, d_flag, created_op, updated_op, ext_info
     
        from strategy_market_channel
        where strategy_group_id = ?
        and d_flag = 0
Parameters:1638465430974519806
2025-06-12 14:06:28.525 [/] [refresh-app-banner-market-strategy] INFO  c.x.c.d.strategy.service.impl.AppBannerServiceImpl:199 - [AppBanner] 成功更新由app资源位id到营销策略的映射:{342=[], 216=[], 338=[StrategyDo(id=2660, name=lgd_test_app_弹窗_2, userConvert=0, detailDescription=, abTest=0, abType=0, bizKey=, sendRuler=1, validityBegin=2024-08-30T00:00, validityEnd=2025-08-30T23:59:59, sendFrequency={"type": 0, "value": []}, crowdPackId=, marketChannel=100, status=0, updatedOp=金能权, updatedOpMobile=***********, updatedTime=2024-08-29T12:59:45, businessType=old-cust, marketCrowdType=null, ids=null, groupType=11, flowCtrlId=323, type=0, marketType=1, callingSource=0, engineCode=null, dispatchConfig=null, flowNo=null, dispatchType=MKT)], 84=[]}
2025-06-12 14:06:28.525 [/] [refresh-app-banner-market-strategy] INFO  c.x.c.d.strategy.service.impl.AppBannerServiceImpl:201 - [AppBanner] 成功更新策略的人群包缓存:{}
2025-06-12 14:06:28.525 [/] [refresh-app-banner-market-strategy] INFO  c.x.c.d.strategy.service.impl.AppBannerServiceImpl:203 - [AppBanner] 成功更新策略的AB分组缓存:{2660=[StrategyGroupDo(id=1638465430974519806, strategyId=2660, name=A组, groupConfig={"digits": 2, "positionEnd": 99, "positionStart": 0, "crowdLabelOption": {"segments": [{"max": "", "min": ""}]}}, isExecutable=1, extInfo=null)]}
2025-06-12 14:06:28.525 [/] [refresh-app-banner-market-strategy] INFO  c.x.c.d.strategy.service.impl.AppBannerServiceImpl:205 - [AppBanner] 成功更新策略AB分组对应的营销渠道缓存:{1638465430974519806=[StrategyMarketChannelDo(id=1638418976184824686, strategyId=2660, strategyGroupId=1638465430974519806, marketChannel=100, templateId=338, app=xyf01, dispatchApp=null, sendTime=null, cron=null, xxlJobId=null, extInfo={"activityId":"502","activityName":"优惠券","couponName":"优惠券","couponId":"352","couponType":"1","discountAmount":"200000","discountRate":"0","expiredDays":"10","couponTypeName":"借款免息券","checked":true})]}
2025-06-12 14:06:28.526 [/] [main] INFO  o.a.r.s.support.DefaultRocketMQListenerContainer:349 - running container: DefaultRocketMQListenerContainer{consumerGroup='cg_xyf_cdp_tg_businesses_identification_increase_amount', namespace='', nameServer='rmq-cn-2ml45zb7w0m-vpc.cn-beijing.rmq.aliyuncs.com:8080', topic='tp_rcspt_risk_amount_change_message', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='tg_businesses_identification_increase_amount', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-06-12 14:06:28.526 [/] [main] INFO  o.a.r.s.a.ListenerContainerConfiguration:113 - Register the listener to container, listenerBeanName:newRiskSingleAdjBusinessMsgConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_28
2025-06-12 14:06:28.714 [/] [main] INFO  o.a.r.s.support.DefaultRocketMQListenerContainer:349 - running container: DefaultRocketMQListenerContainer{consumerGroup='cg_xyf_cdp_tg_vehicle_identification_increase_amount', namespace='', nameServer='rmq-cn-2ml45zb7w0m-vpc.cn-beijing.rmq.aliyuncs.com:8080', topic='tp_rcspt_risk_amount_change_message', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='tg_vehicle_identification_increase_amount', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-06-12 14:06:28.714 [/] [main] INFO  o.a.r.s.a.ListenerContainerConfiguration:113 - Register the listener to container, listenerBeanName:newRiskSingleAdjCarMsgConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_29
2025-06-12 14:06:28.902 [/] [main] INFO  o.a.r.s.support.DefaultRocketMQListenerContainer:349 - running container: DefaultRocketMQListenerContainer{consumerGroup='cg_xyf_cdp_tg_sesame_identification_increase_amount', namespace='', nameServer='rmq-cn-2ml45zb7w0m-vpc.cn-beijing.rmq.aliyuncs.com:8080', topic='tp_rcspt_risk_amount_change_message', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='tg_sesame_identification_increase_amount', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-06-12 14:06:28.903 [/] [main] INFO  o.a.r.s.a.ListenerContainerConfiguration:113 - Register the listener to container, listenerBeanName:newRiskSingleAdjSesameMsgConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_30
2025-06-12 14:06:29.090 [/] [main] INFO  o.a.r.s.support.DefaultRocketMQListenerContainer:349 - running container: DefaultRocketMQListenerContainer{consumerGroup='cg_xyf_cdp_tg_risk_temp_amt', namespace='', nameServer='rmq-cn-2ml45zb7w0m-vpc.cn-beijing.rmq.aliyuncs.com:8080', topic='tp_rcspt_risk_amount_change_message', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='tg_risk_temp_amt', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-06-12 14:06:29.090 [/] [main] INFO  o.a.r.s.a.ListenerContainerConfiguration:113 - Register the listener to container, listenerBeanName:newRiskTempCreditMsgConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_31
2025-06-12 14:06:29.290 [/] [main] INFO  o.a.r.s.support.DefaultRocketMQListenerContainer:349 - running container: DefaultRocketMQListenerContainer{consumerGroup='cg_xyf_cdp_tp_biz_report_Start1', namespace='', nameServer='rmq-cn-2ml45zb7w0m-vpc.cn-beijing.rmq.aliyuncs.com:8080', topic='tp_biz_report_Start1', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-06-12 14:06:29.290 [/] [main] INFO  o.a.r.s.a.ListenerContainerConfiguration:113 - Register the listener to container, listenerBeanName:start1RocketMsgConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_32
2025-06-12 14:06:29.485 [/] [main] INFO  o.a.r.s.support.DefaultRocketMQListenerContainer:349 - running container: DefaultRocketMQListenerContainer{consumerGroup='cg_xyf_cdp_tp_marketing_telname_enter_result', namespace='', nameServer='rmq-cn-2ml45zb7w0m-vpc.cn-beijing.rmq.aliyuncs.com:8080', topic='tp_marketing_telname_enter_result', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-06-12 14:06:29.485 [/] [main] INFO  o.a.r.s.a.ListenerContainerConfiguration:113 - Register the listener to container, listenerBeanName:teleCallRocketMqConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_33
2025-06-12 14:06:29.718 [/] [main] INFO  o.a.r.s.support.DefaultRocketMQListenerContainer:349 - running container: DefaultRocketMQListenerContainer{consumerGroup='cg_xyf_cdp_tp_user_register', namespace='', nameServer='rmq-cn-2ml45zb7w0m-vpc.cn-beijing.rmq.aliyuncs.com:8080', topic='tp_user_register', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-06-12 14:06:29.718 [/] [main] INFO  o.a.r.s.a.ListenerContainerConfiguration:113 - Register the listener to container, listenerBeanName:newRegisterMsgConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_34
2025-06-12 14:06:29.904 [/] [main] INFO  o.a.r.s.support.DefaultRocketMQListenerContainer:349 - running container: DefaultRocketMQListenerContainer{consumerGroup='cg_xyf_cdp_tp_cis_event_report_1004089', namespace='', nameServer='rmq-cn-2ml45zb7w0m-vpc.cn-beijing.rmq.aliyuncs.com:8080', topic='tp_cis_event_report_1004089', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-06-12 14:06:29.905 [/] [main] INFO  o.a.r.s.a.ListenerContainerConfiguration:113 - Register the listener to container, listenerBeanName:newTrackingCardClickMsgConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_35
2025-06-12 14:06:30.099 [/] [main] INFO  o.a.r.s.support.DefaultRocketMQListenerContainer:349 - running container: DefaultRocketMQListenerContainer{consumerGroup='cg_xyf_cdp_tp_cis_event_report_1003806', namespace='', nameServer='rmq-cn-2ml45zb7w0m-vpc.cn-beijing.rmq.aliyuncs.com:8080', topic='tp_cis_event_report_1003806', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-06-12 14:06:30.099 [/] [main] INFO  o.a.r.s.a.ListenerContainerConfiguration:113 - Register the listener to container, listenerBeanName:newTrackingLoanApplyViewMsgConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_36
2025-06-12 14:06:30.118 [/] [main] INFO  com.xftech.cdp.infra.thread.DispatchCrowdExecutor:94 - dispatchCrowdExecutor, 线程池最大值重新设置, maxSize = 9
2025-06-12 14:06:30.121 [/] [main] INFO  com.xftech.cdp.infra.thread.DispatchFlcExecutor:84 - dispatchFlcExecutor, 线程池核心大小重新设置, coreSize = 1
2025-06-12 14:06:30.121 [/] [main] INFO  com.xftech.cdp.infra.thread.DispatchFlcExecutor:94 - dispatchFlcExecutor, 线程池最大值重新设置, maxSize = 1
2025-06-12 14:06:30.123 [/] [main] INFO  com.xftech.cdp.infra.thread.DispatchTaskExecutor:88 - dispatchTaskExecutor, 线程池核心大小重新设置, coreSize = 2
2025-06-12 14:06:30.143 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: springfox.documentation.swagger.v2.use-model-v3, beanName: compatibilityModelMapperImpl, field: springfox.documentation.swagger2.mappers.CompatibilityModelMapperImpl.useModelV3
2025-06-12 14:06:30.157 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: springfox.documentation.swagger.use-model-v3, beanName: serviceModelToSwagger2MapperImpl, field: springfox.documentation.swagger2.mappers.ServiceModelToSwagger2MapperImpl.useModelV3
2025-06-12 14:06:30.546 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: swagger.enabled, beanName: scopedTarget.swaggerConfig, field: com.xftech.cdp.infra.swagger.SwaggerConfig$$EnhancerBySpringCGLIB$$f8c5d397.enabled
2025-06-12 14:06:30.546 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: swagger.groupName, beanName: scopedTarget.swaggerConfig, field: com.xftech.cdp.infra.swagger.SwaggerConfig$$EnhancerBySpringCGLIB$$f8c5d397.groupName
2025-06-12 14:06:30.975 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: xf.client.time.enable, beanName: xfClientAop, field: com.xinfei.xfframework.aop.XfClientAop.timeEnable
2025-06-12 14:06:30.975 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: xf.client.aop.enable, beanName: xfClientAop, field: com.xinfei.xfframework.aop.XfClientAop.enableAop
2025-06-12 14:06:31.046 [/] [main] INFO  com.xftech.base.thread.ExecutorManager:70 - executor threadName=crowd-upload-service, minCoreSize=8, queueSize=2147483647, keepAliveTime=300000, TimeUnit=MILLISECONDS
2025-06-12 14:06:31.052 [/] [main] INFO  com.xftech.base.thread.ExecutorManager:70 - executor threadName=strategy-blank-task-executor, minCoreSize=8, queueSize=2147483647, keepAliveTime=300000, TimeUnit=MILLISECONDS
2025-06-12 14:06:31.053 [/] [main] INFO  com.xftech.base.thread.ExecutorManager:70 - executor threadName=strategy-count-task-executor, minCoreSize=8, queueSize=2147483647, keepAliveTime=300000, TimeUnit=MILLISECONDS
2025-06-12 14:06:31.054 [/] [main] INFO  com.xftech.base.thread.ExecutorManager:70 - executor threadName=refresh-risk-score-task-executor, minCoreSize=8, queueSize=2147483647, keepAliveTime=300000, TimeUnit=MILLISECONDS
2025-06-12 14:06:31.055 [/] [main] INFO  com.xftech.base.thread.ExecutorManager:70 - executor threadName=adminTaskExecutor, minCoreSize=8, queueSize=2147483647, keepAliveTime=300000, TimeUnit=MILLISECONDS
2025-06-12 14:06:31.083 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: xxl.job.admin.addresses, beanName: scopedTarget.xxlJobConfig, field: com.xftech.xxljob.config.XxlJobConfig$$EnhancerBySpringCGLIB$$25e15888.adminAddresses
2025-06-12 14:06:31.083 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: xxl.job.accessToken, beanName: scopedTarget.xxlJobConfig, field: com.xftech.xxljob.config.XxlJobConfig$$EnhancerBySpringCGLIB$$25e15888.accessToken
2025-06-12 14:06:31.083 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: xxl.job.executor.appname, beanName: scopedTarget.xxlJobConfig, field: com.xftech.xxljob.config.XxlJobConfig$$EnhancerBySpringCGLIB$$25e15888.appname
2025-06-12 14:06:31.083 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: xxl.job.executor.address, beanName: scopedTarget.xxlJobConfig, field: com.xftech.xxljob.config.XxlJobConfig$$EnhancerBySpringCGLIB$$25e15888.address
2025-06-12 14:06:31.083 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: xxl.job.executor.ip, beanName: scopedTarget.xxlJobConfig, field: com.xftech.xxljob.config.XxlJobConfig$$EnhancerBySpringCGLIB$$25e15888.ip
2025-06-12 14:06:31.084 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: xxl.job.executor.port, beanName: scopedTarget.xxlJobConfig, field: com.xftech.xxljob.config.XxlJobConfig$$EnhancerBySpringCGLIB$$25e15888.port
2025-06-12 14:06:31.084 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: xxl.job.executor.logpath, beanName: scopedTarget.xxlJobConfig, field: com.xftech.xxljob.config.XxlJobConfig$$EnhancerBySpringCGLIB$$25e15888.logPath
2025-06-12 14:06:31.084 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: xxl.job.executor.logretentiondays, beanName: scopedTarget.xxlJobConfig, field: com.xftech.xxljob.config.XxlJobConfig$$EnhancerBySpringCGLIB$$25e15888.logRetentionDays
2025-06-12 14:06:31.088 [/] [main] INFO  com.xftech.xxljob.config.XxlJobConfig:48 - >>>>>>>>>>> xxl-job config init.
2025-06-12 14:06:31.088 [/] [main] INFO  com.xftech.xxljob.config.XxlJobConfig:49 - adminAddresses=https://dev-xxl-job-admin.testxinfei.cn/xxl-job-admin, appname=xyf-cdp, address=, ip=, port=18080
org.springframework.boot.autoconfigure.data.redis.RedisProperties.getUsername()Ljava/lang/String;
2025-06-12 14:06:32.193 [/] [main] INFO  c.xinfei.ssocore.client.config.SsoClientConfigurer:46 - init sso client config...
____ ____    ___ ____ _  _ ____ _  _ 
[__  |__| __  |  |  | |_/  |___ |\ | 
___] |  |     |  |__| | \_ |___ | \| 
https://sa-token.cc (v1.37.0)
2025-06-12 14:06:32.294 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: server.servlet.context-path, beanName: getApplicationContextPathLoading, field: cn.dev33.satoken.spring.context.path.ApplicationContextPathLoading.contextPath
2025-06-12 14:06:32.294 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: spring.mvc.servlet.path, beanName: getApplicationContextPathLoading, field: cn.dev33.satoken.spring.context.path.ApplicationContextPathLoading.servletPath
2025-06-12 14:06:33.175 [/] [main] WARN  o.s.b.a.freemarker.FreeMarkerAutoConfiguration:65 - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-06-12 14:06:33.228 [/] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler:181 - Initializing ExecutorService 'taskScheduler'
2025-06-12 14:06:33.291 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: SPRING_UTIL_TIMEOUT_SEC, beanName: inetUtilsProperties, field: org.springframework.cloud.commons.util.InetUtilsProperties.timeoutSeconds
2025-06-12 14:06:33.291 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: spring.util.timeout.sec, beanName: inetUtilsProperties, field: org.springframework.cloud.commons.util.InetUtilsProperties.timeoutSeconds
2025-06-12 14:06:33.743 [/] [main] INFO  c.x.x.c.s.m.m.MultipleRocketMQListenerContainer:351 - running container: DefaultRocketMQListenerContainer{consumerGroup='cg_tp_cdp_engine_redecision_delay', namespace='', nameServer='rmq-cn-2ml45zb7w0m-vpc.cn-beijing.rmq.aliyuncs.com:8080', topic='tp_cdp_engine_redecision_delay', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-06-12 14:06:33.748 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:TEST, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5c35267d[class com.xftech.cdp.adapter.crowd.CrowdDispatch$$EnhancerBySpringCGLIB$$910f3055#test]
2025-06-12 14:06:33.748 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:RESET_BIGDATA_ERROR_RESULT_CROWD_PACKS, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3217ac36[class com.xftech.cdp.adapter.crowd.CrowdDispatch$$EnhancerBySpringCGLIB$$910f3055#resetBigDataErrorResultCrowdPacks]
2025-06-12 14:06:33.748 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:CLEAR_HISTORY_CROWD_DETAIL_BY_TRUNCATE, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4f9e42c4[class com.xftech.cdp.adapter.crowd.CrowdDispatch$$EnhancerBySpringCGLIB$$910f3055#clearHistoryCrowdDetailTruncate]
2025-06-12 14:06:33.748 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:CROWD_DISPATCH, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5f4ddd09[class com.xftech.cdp.adapter.crowd.CrowdDispatch$$EnhancerBySpringCGLIB$$910f3055#crowdDispatch]
2025-06-12 14:06:33.748 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:CROWD_RESET_EVERYDAY, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2a3e1e53[class com.xftech.cdp.adapter.crowd.CrowdDispatch$$EnhancerBySpringCGLIB$$910f3055#crowdReset]
2025-06-12 14:06:33.748 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:CROWD_WAREHOUSE_ALARM, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@25c7713e[class com.xftech.cdp.adapter.crowd.CrowdDispatch$$EnhancerBySpringCGLIB$$910f3055#crowdWareHouseAlarm]
2025-06-12 14:06:33.748 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:CROWD_PUSH_RESULT_QUERY, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@18f28f20[class com.xftech.cdp.adapter.crowd.CrowdDispatch$$EnhancerBySpringCGLIB$$910f3055#crowdPushResultQuery]
2025-06-12 14:06:33.748 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:CLEAR_HISTORY_CROWD_DETAIL, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2966bca2[class com.xftech.cdp.adapter.crowd.CrowdDispatch$$EnhancerBySpringCGLIB$$910f3055#clearHistoryCrowdDetail]
2025-06-12 14:06:33.748 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:CROWD_REFRESH_TIMEOUT_ALARM, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7370f35b[class com.xftech.cdp.adapter.crowd.CrowdDispatch$$EnhancerBySpringCGLIB$$910f3055#refreshTimeoutAlarm]
2025-06-12 14:06:33.748 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:PUSH_CROWD_TOTAL, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6da0228a[class com.xftech.cdp.adapter.crowd.CrowdDispatch$$EnhancerBySpringCGLIB$$910f3055#pushCrowdTotal]
2025-06-12 14:06:33.748 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:PUSH_CROWDLIST, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@61a72b91[class com.xftech.cdp.adapter.crowd.CrowdDispatch$$EnhancerBySpringCGLIB$$910f3055#pushCrowdList]
2025-06-12 14:06:33.748 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:PUSH_CROWDLIST_RETRY, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7d3818ab[class com.xftech.cdp.adapter.crowd.CrowdDispatch$$EnhancerBySpringCGLIB$$910f3055#pushCrowdListRetry]
2025-06-12 14:06:33.748 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:PUSH_CROWD_RECORD_LOG, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@9a6c67c[class com.xftech.cdp.adapter.crowd.CrowdDispatch$$EnhancerBySpringCGLIB$$910f3055#pushCrowdListLog]
2025-06-12 14:06:33.748 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:REPORT_DAILY_CROWD, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@657e11b0[class com.xftech.cdp.adapter.crowd.CrowdDispatch$$EnhancerBySpringCGLIB$$910f3055#reportDailyCrowdJob]
2025-06-12 14:06:33.749 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:LABEL_SYNC_TASK, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3d5d552d[class com.xftech.cdp.adapter.label.LabelJob$$EnhancerBySpringCGLIB$$e091af4e#flowStatusChanged]
2025-06-12 14:06:33.750 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:RISK_FORECAST_NEW, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@9def6d[class com.xftech.cdp.adapter.risk.RiskDispatch$$EnhancerBySpringCGLIB$$c4cbeb3b#RiskDispatchNew]
2025-06-12 14:06:33.750 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:RISK_FORECAST, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3fb75c24[class com.xftech.cdp.adapter.risk.RiskDispatch$$EnhancerBySpringCGLIB$$c4cbeb3b#RiskDispatch]
2025-06-12 14:06:33.751 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:FLOW_STATUS_CHANGED, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2a5129f3[class com.xftech.cdp.adapter.strategy.FlowJob$$EnhancerBySpringCGLIB$$dba467e1#flowStatusChanged]
2025-06-12 14:06:33.751 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:FLOW_DISPATCH_TASK_PRODUCER, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7d72f5a9[class com.xftech.cdp.adapter.strategy.FlowJob$$EnhancerBySpringCGLIB$$dba467e1#flowTaskProducer]
2025-06-12 14:06:33.751 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:FLOW_DISPATCH_TASK_CONSUMER, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@127b9bed[class com.xftech.cdp.adapter.strategy.FlowJob$$EnhancerBySpringCGLIB$$dba467e1#flowTaskConsumer]
2025-06-12 14:06:33.752 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:STRATEGY_DISPATCH_TASK_GENERATOR, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1728a3fa[class com.xftech.cdp.adapter.strategy.StrategyDispatch$$EnhancerBySpringCGLIB$$5290bf3b#strategyDispatchTaskProducer]
2025-06-12 14:06:33.752 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:STRATEGY_DISPATCH_TASK_EXECCUTE, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@34151e5d[class com.xftech.cdp.adapter.strategy.StrategyDispatch$$EnhancerBySpringCGLIB$$5290bf3b#strategyDispatchTaskConsumer]
2025-06-12 14:06:33.752 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:STRATEGY_DISPATCH_OFFLINE_ENGINE, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5e206acb[class com.xftech.cdp.adapter.strategy.StrategyDispatch$$EnhancerBySpringCGLIB$$5290bf3b#strategyDispatchOfflineEngine]
2025-06-12 14:06:33.752 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:TEL_STRATEGY_DISPATCH_OFFLINE_ENGINE, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@10ca3611[class com.xftech.cdp.adapter.strategy.StrategyDispatch$$EnhancerBySpringCGLIB$$5290bf3b#telStrategyDispatchOfflineEngine]
2025-06-12 14:06:33.752 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:NOT_TEL_STRATEGY_DISPATCH_OFFLINE_ENGINE, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@422f7081[class com.xftech.cdp.adapter.strategy.StrategyDispatch$$EnhancerBySpringCGLIB$$5290bf3b#notTelStrategyDispatchOfflineEngine]
2025-06-12 14:06:33.752 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:STRATEGY_DISPATCH_TASK_EXECCUTE_TIMEOUT_ALARM, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5ed4796f[class com.xftech.cdp.adapter.strategy.StrategyDispatch$$EnhancerBySpringCGLIB$$5290bf3b#strategyDispatchTaskExectuteTimeOutAlram]
2025-06-12 14:06:33.752 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:STRATEGY_EVENT_REFRESH_STATUS, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3ff264c[class com.xftech.cdp.adapter.strategy.StrategyDispatch$$EnhancerBySpringCGLIB$$5290bf3b#strategyEventRefreshStatus]
2025-06-12 14:06:33.752 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:STRATEGY_EVENT_REFRESH_EXEC_LOG_STATUS, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@42c4f29c[class com.xftech.cdp.adapter.strategy.StrategyDispatch$$EnhancerBySpringCGLIB$$5290bf3b#strategyEventRefreshExecLogStatus]
2025-06-12 14:06:33.752 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:STRATEGY_EVENT_CATCH_METADATA, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7bd6393d[class com.xftech.cdp.adapter.strategy.StrategyDispatch$$EnhancerBySpringCGLIB$$5290bf3b#strategyEventCatchMetadata]
2025-06-12 14:06:33.752 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:STRATEGY_EVENT_EXEC_LOG_REFRESH, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6b42829[class com.xftech.cdp.adapter.strategy.StrategyDispatch$$EnhancerBySpringCGLIB$$5290bf3b#strategyEventExecLogRefresh]
2025-06-12 14:06:33.752 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:T0_STRATEGY_DISPATCH_USER_NUM_ALARM, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7c03a3f5[class com.xftech.cdp.adapter.strategy.StrategyDispatch$$EnhancerBySpringCGLIB$$5290bf3b#t0StrategyDispatchUserNumAlarm]
2025-06-12 14:06:33.752 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:EXIST_STRATEGY_FLOW_CTRL_UPDATE, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@19b6eefb[class com.xftech.cdp.adapter.strategy.StrategyDispatch$$EnhancerBySpringCGLIB$$5290bf3b#existStrategyFlowCtrlUpdate]
2025-06-12 14:06:33.752 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:OFFLINE_STRATEGY_CROWD_STATUS_ALARM, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@53633321[class com.xftech.cdp.adapter.strategy.StrategyDispatch$$EnhancerBySpringCGLIB$$5290bf3b#offlineStrategyCrowdStatusAlarm]
2025-06-12 14:06:33.752 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:STRATEGY_CROWD_PACK_EXPIRE_ALARM, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7431ac04[class com.xftech.cdp.adapter.strategy.StrategyDispatch$$EnhancerBySpringCGLIB$$5290bf3b#strategyCrowdPackExpireAlarm]
2025-06-12 14:06:33.752 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:REPORT_DAILY_STRATEGY_ALARM, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@154ca57c[class com.xftech.cdp.adapter.strategy.StrategyDispatch$$EnhancerBySpringCGLIB$$5290bf3b#reportDailyStrategyAlarmJob]
2025-06-12 14:06:33.752 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:STRATEGY_DISPATCH, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3ba34018[class com.xftech.cdp.adapter.strategy.StrategyDispatch$$EnhancerBySpringCGLIB$$5290bf3b#strategyDispatch]
2025-06-12 14:06:33.752 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:STRATEGY_DISPATCH_RETRY, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@71139676[class com.xftech.cdp.adapter.strategy.StrategyDispatch$$EnhancerBySpringCGLIB$$5290bf3b#strategyDispatchRetry]
2025-06-12 14:06:33.752 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:STRATEGY_REFRESH_STATUS, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@83b55dc[class com.xftech.cdp.adapter.strategy.StrategyDispatch$$EnhancerBySpringCGLIB$$5290bf3b#strategyRefreshStatus]
2025-06-12 14:06:33.752 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:STRATEGY_EVENT_30_MIN_ALARM, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@78ac66e2[class com.xftech.cdp.adapter.strategy.StrategyDispatch$$EnhancerBySpringCGLIB$$5290bf3b#strategyEvent30MinAlarm]
2025-06-12 14:06:33.752 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:STRATEGY_REFRESH_CACHE_DATA, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@59ae4a6f[class com.xftech.cdp.adapter.strategy.StrategyDispatch$$EnhancerBySpringCGLIB$$5290bf3b#strategyCacheDataRefresh]
2025-06-12 14:06:33.752 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:DISPATCH_FAIL_USER_UPDATE, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3463d70a[class com.xftech.cdp.adapter.strategy.StrategyDispatch$$EnhancerBySpringCGLIB$$5290bf3b#dispatchFailUserUpdate]
2025-06-12 14:06:33.752 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:REPORT_DAILY_STRATEGY, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@66a4e1ef[class com.xftech.cdp.adapter.strategy.StrategyDispatch$$EnhancerBySpringCGLIB$$5290bf3b#reportDailyStrategyJob]
2025-06-12 14:06:33.753 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:STAT_OFFLINE_STRATEGY_FLOW_DATA, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7d2410af[class com.xftech.cdp.adapter.strategy.StrategyStatJob$$EnhancerBySpringCGLIB$$4b1ed2b6#offlineStrategyFlowDataJob]
2025-06-12 14:06:33.753 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:STAT_REALTIME_STRATEGY_FLOW_DATA, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5672c056[class com.xftech.cdp.adapter.strategy.StrategyStatJob$$EnhancerBySpringCGLIB$$4b1ed2b6#realtimeStrategyFlowDataJob]
2025-06-12 14:06:33.753 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:STAT_REALTIME_STRATEGY_FLOW_DATA_YESTERDAY, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7c039615[class com.xftech.cdp.adapter.strategy.StrategyStatJob$$EnhancerBySpringCGLIB$$4b1ed2b6#realtimeStrategyYesterdayFlowDataJob]
2025-06-12 14:06:33.753 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:MANUAL_REALTIME_STRATEGY_FLOW_DATA, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3d69bbe3[class com.xftech.cdp.adapter.strategy.StrategyStatJob$$EnhancerBySpringCGLIB$$4b1ed2b6#manualRealtimeStrategyFlowDataJob]
2025-06-12 14:06:33.753 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:STAT_STRATEGY_GROUP_DATA_END_CURRENT_STATUS_JOB, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@60157589[class com.xftech.cdp.adapter.strategy.StrategyStatJob$$EnhancerBySpringCGLIB$$4b1ed2b6#statStrategyGroupDateEndCurrentStatusJob]
2025-06-12 14:06:33.753 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:STAT_STRATEGY_GROUP_DATA_YESTERDAY_JOB, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@20a569ad[class com.xftech.cdp.adapter.strategy.StrategyStatJob$$EnhancerBySpringCGLIB$$4b1ed2b6#statStrategyGroupDateYesterdayJob]
2025-06-12 14:06:33.754 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:STAT_STRATEGY_GROUP_DATA_JOB, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@40f6e092[class com.xftech.cdp.adapter.strategy.StrategyStatJob$$EnhancerBySpringCGLIB$$4b1ed2b6#statStrategyGroupDateJob]
2025-06-12 14:06:33.754 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:STAT_STRATEGY_FLOW_DATA_JOB, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3030edcc[class com.xftech.cdp.adapter.strategy.StrategyStatJob$$EnhancerBySpringCGLIB$$4b1ed2b6#statStrategyFlowDataJob]
2025-06-12 14:06:33.754 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:TABLE_CREATOR, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7bc27f7e[class com.xftech.cdp.adapter.strategy.TableCreatorHandler$$EnhancerBySpringCGLIB$$68d5f9b8#tableCreator]
2025-06-12 14:06:33.755 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:CrowdSliceFallBackTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@702189aa[class com.xftech.cdp.distribute.crowd.xxljob.CrowdSliceFallBackTask$$EnhancerBySpringCGLIB$$28827559#crowdSyncFallBackTask]
2025-06-12 14:06:33.755 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:CrowdSyncTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7a4154c[class com.xftech.cdp.distribute.crowd.xxljob.CrowdSyncTask$$EnhancerBySpringCGLIB$$d3bae7ba#crowdSyncTask]
2025-06-12 14:06:33.756 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:OFFLINE_ENGINE_DISTRIBUTE_TASK_GENERATE, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@30cdd6f9[class com.xftech.cdp.distribute.offline.xxljob.OfflineStrategyJobDispatch$$EnhancerBySpringCGLIB$$a4e8451a#strategySliceTask]
2025-06-12 14:06:33.756 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:OFFLINE_ENGINE_DISTRIBUTE_EXECUTE, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@581c7611[class com.xftech.cdp.distribute.offline.xxljob.OfflineStrategyJobDispatch$$EnhancerBySpringCGLIB$$a4e8451a#strategySliceTaskExecute]
2025-06-12 14:06:33.762 [/] [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:168 - >>>>>>>>>>> xxl-job register jobhandler success, name:RABBIT_MQ_DISPATCH, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@248cc861[class com.xftech.cdp.domain.mq.xxljob.RabbitMqDispatch$$EnhancerBySpringCGLIB$$680d8f9f#rabbitMqDispatch]
2025-06-12 14:06:33.806 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'datainsight' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:33.830 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'datainsightcore' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:33.845 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'appcore' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:33.865 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'appcore' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:33.878 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'appcore' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:33.891 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'appcore' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:33.901 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'appcore' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:33.915 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'appcore' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:33.927 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'appcore' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:33.942 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'appcore' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:33.977 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'appcore' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:33.995 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'appcore' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:34.015 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'appcore' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:34.040 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'appcore' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:34.064 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'appcore' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:34.079 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'appcore' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:34.091 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'appcore' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:34.106 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'appcore' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:34.124 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'appcore' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:34.136 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'appcore' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:34.152 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'appcore' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:34.166 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'appcore' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:34.178 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'enginepredictcenter' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:34.195 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'cis-query-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:34.233 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'cis-query-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:34.257 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'cis-query-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:34.287 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'cis-query-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:34.307 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'cis-query-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:34.324 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'cis-query-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:34.399 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'cis-query-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:34.434 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'cis-query-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:34.481 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'cis-query-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:34.498 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'cis-query-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:34.532 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'cis-query-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:34.546 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'cis-query-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:34.565 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'cis-query-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:34.608 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'cis-query-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:34.643 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'user-device-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:34.694 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'user-device-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:34.741 [/] [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean:418 - For 'user-device-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 14:06:39.994 [/] [Thread-50] INFO  com.xxl.job.core.server.EmbedServer:86 - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 18080
2025-06-12 14:06:40.003 [/] [main] INFO  org.apache.coyote.http11.Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8080"]
2025-06-12 14:06:40.034 [/] [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:220 - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-12 14:06:42.255 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: distribute.task.page.size, beanName: scopedTarget.distributeConfigService, field: com.xftech.cdp.distribute.config.DistributeConfigService.crowdSliceBatchSize
2025-06-12 14:06:42.255 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: oss.special.line.read.length, beanName: scopedTarget.distributeConfigService, field: com.xftech.cdp.distribute.config.DistributeConfigService.ossSpecialLineReadLength
2025-06-12 14:06:42.256 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: oss.endpoint.buket.config, beanName: scopedTarget.distributeConfigService, method: com.xftech.cdp.distribute.config.DistributeConfigService.setOssBucketConfig
2025-06-12 14:06:42.297 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.ads.host, beanName: scopedTarget.adsConfig, field: com.xftech.cdp.infra.client.ads.config.AdsConfig.host
2025-06-12 14:06:42.297 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.ads.crowdHost, beanName: scopedTarget.adsConfig, field: com.xftech.cdp.infra.client.ads.config.AdsConfig.crowdHost
2025-06-12 14:06:42.297 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.ads.route.queryMarketStrategyRoute, beanName: scopedTarget.adsConfig, field: com.xftech.cdp.infra.client.ads.config.AdsConfig.queryMarketStrategyRoute
2025-06-12 14:06:42.297 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.ads.route.queryMarketChannelRoute, beanName: scopedTarget.adsConfig, field: com.xftech.cdp.infra.client.ads.config.AdsConfig.queryMarketChannelRoute
2025-06-12 14:06:42.297 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.ads.route.queryMarketStrategyRoute, beanName: scopedTarget.adsConfig, field: com.xftech.cdp.infra.client.ads.config.AdsConfig.batchOfflineConfig
2025-06-12 14:06:42.297 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.ads.route.queryMarketChannelRoute, beanName: scopedTarget.adsConfig, field: com.xftech.cdp.infra.client.ads.config.AdsConfig.getModelParam
2025-06-12 14:06:42.297 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.ads.route.aesDecode, beanName: scopedTarget.adsConfig, field: com.xftech.cdp.infra.client.ads.config.AdsConfig.aesDecode
2025-06-12 14:06:42.297 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.ads.crowd.runCrowdJob, beanName: scopedTarget.adsConfig, field: com.xftech.cdp.infra.client.ads.config.AdsConfig.runCrowdJob
2025-06-12 14:06:42.297 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.ads.crowd.getCrowdTotal, beanName: scopedTarget.adsConfig, field: com.xftech.cdp.infra.client.ads.config.AdsConfig.getCrowdTotal
2025-06-12 14:06:42.297 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.ads.crowd.tabName, beanName: scopedTarget.adsConfig, field: com.xftech.cdp.infra.client.ads.config.AdsConfig.adsTable
2025-06-12 14:06:42.307 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.coupon.host, beanName: scopedTarget.couponConfig, field: com.xftech.cdp.infra.client.coupon.conig.CouponConfig.host
2025-06-12 14:06:42.308 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.coupon.newHost, beanName: scopedTarget.couponConfig, field: com.xftech.cdp.infra.client.coupon.conig.CouponConfig.newHost
2025-06-12 14:06:42.308 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.coupon.useNewRoutes, beanName: scopedTarget.couponConfig, field: com.xftech.cdp.infra.client.coupon.conig.CouponConfig.useNewRoutes
2025-06-12 14:06:42.308 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.coupon.route.activityList, beanName: scopedTarget.couponConfig, field: com.xftech.cdp.infra.client.coupon.conig.CouponConfig.activityListRoute
2025-06-12 14:06:42.309 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.coupon.route.activityListNew, beanName: scopedTarget.couponConfig, field: com.xftech.cdp.infra.client.coupon.conig.CouponConfig.activityListRouteNew
2025-06-12 14:06:42.309 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.coupon.route.sendBatch, beanName: scopedTarget.couponConfig, field: com.xftech.cdp.infra.client.coupon.conig.CouponConfig.sendBatchRoute
2025-06-12 14:06:42.309 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.coupon.route.sendBatchNew, beanName: scopedTarget.couponConfig, field: com.xftech.cdp.infra.client.coupon.conig.CouponConfig.sendBatchRouteNew
2025-06-12 14:06:42.309 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.coupon.uniqueKey, beanName: scopedTarget.couponConfig, field: com.xftech.cdp.infra.client.coupon.conig.CouponConfig.key
2025-06-12 14:06:42.310 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.coupon.route.getUserCouponList, beanName: scopedTarget.couponConfig, field: com.xftech.cdp.infra.client.coupon.conig.CouponConfig.getUserCouponList
2025-06-12 14:06:42.310 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.coupon.route.getUserCouponList, beanName: scopedTarget.couponConfig, field: com.xftech.cdp.infra.client.coupon.conig.CouponConfig.getUserCouponListNew
2025-06-12 14:06:42.310 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.coupon.route.activity-list, beanName: scopedTarget.couponConfig, field: com.xftech.cdp.infra.client.coupon.conig.CouponConfig.getActivityList
2025-06-12 14:06:42.310 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.coupon.route.activity-list, beanName: scopedTarget.couponConfig, field: com.xftech.cdp.infra.client.coupon.conig.CouponConfig.getActivityListNew
2025-06-12 14:06:42.310 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.coupon.route.activity-detail-list, beanName: scopedTarget.couponConfig, field: com.xftech.cdp.infra.client.coupon.conig.CouponConfig.getActivityCouponList
2025-06-12 14:06:42.310 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.coupon.route.send-user-coupon, beanName: scopedTarget.couponConfig, field: com.xftech.cdp.infra.client.coupon.conig.CouponConfig.sendUserCoupon
2025-06-12 14:06:42.310 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.coupon.route.send-user-coupon, beanName: scopedTarget.couponConfig, field: com.xftech.cdp.infra.client.coupon.conig.CouponConfig.sendUserCouponNew
2025-06-12 14:06:42.340 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.loanMarket.host, beanName: scopedTarget.loanMarketConfig, field: com.xftech.cdp.infra.client.loanmarket.config.LoanMarketConfig.loanMarketHost
2025-06-12 14:06:42.340 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.loanMarket.data.getUtmSourceInfo, beanName: scopedTarget.loanMarketConfig, field: com.xftech.cdp.infra.client.loanmarket.config.LoanMarketConfig.utmSourceInfo
2025-06-12 14:06:42.342 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: aliyun.oss.endpoint, beanName: scopedTarget.ossConfig, field: com.xftech.cdp.infra.client.oss.config.OssConfig.endpoint
2025-06-12 14:06:42.342 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: aliyun.oss.accessKeyId, beanName: scopedTarget.ossConfig, field: com.xftech.cdp.infra.client.oss.config.OssConfig.accessKeyId
2025-06-12 14:06:42.342 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: aliyun.oss.secretAccessKey, beanName: scopedTarget.ossConfig, field: com.xftech.cdp.infra.client.oss.config.OssConfig.accessKeySecret
2025-06-12 14:06:42.342 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: aliyun.oss.bucketName, beanName: scopedTarget.ossConfig, field: com.xftech.cdp.infra.client.oss.config.OssConfig.bucketName
2025-06-12 14:06:42.344 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: usergroup-metadata.host, beanName: scopedTarget.randomConfig, field: com.xftech.cdp.infra.client.randomnum.config.RandomConfig.host
2025-06-12 14:06:42.344 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.random.route.query.list, beanName: scopedTarget.randomConfig, field: com.xftech.cdp.infra.client.randomnum.config.RandomConfig.randomListRoute
2025-06-12 14:06:42.351 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.sms.host, beanName: scopedTarget.smsConfig, field: com.xftech.cdp.infra.client.sms.config.SmsConfig.smsHost
2025-06-12 14:06:42.351 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.sms.personal.host, beanName: scopedTarget.smsConfig, field: com.xftech.cdp.infra.client.sms.config.SmsConfig.smsPersonalHost
2025-06-12 14:06:42.351 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.sms.send.single.route, beanName: scopedTarget.smsConfig, field: com.xftech.cdp.infra.client.sms.config.SmsConfig.singleSendRoute
2025-06-12 14:06:42.351 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.sms.send.batch.route, beanName: scopedTarget.smsConfig, field: com.xftech.cdp.infra.client.sms.config.SmsConfig.batchSendRoute
2025-06-12 14:06:42.351 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.sms.batch.with.param.route, beanName: scopedTarget.smsConfig, field: com.xftech.cdp.infra.client.sms.config.SmsConfig.smsBatchWithParamRoute
2025-06-12 14:06:42.351 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.sms.query.item.batch.route, beanName: scopedTarget.smsConfig, field: com.xftech.cdp.infra.client.sms.config.SmsConfig.batchTemplateQueryRoute
2025-06-12 14:06:42.351 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.sms.query.distinct.batch.route, beanName: scopedTarget.smsConfig, field: com.xftech.cdp.infra.client.sms.config.SmsConfig.batchDistinctTemplateQueryRoute
2025-06-12 14:06:42.351 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.sms.query.group.batch.route, beanName: scopedTarget.smsConfig, field: com.xftech.cdp.infra.client.sms.config.SmsConfig.batchTypeQueryRoute
2025-06-12 14:06:42.351 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.sms.cancel.batch.route, beanName: scopedTarget.smsConfig, field: com.xftech.cdp.infra.client.sms.config.SmsConfig.batchCancelRoute
2025-06-12 14:06:42.351 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.sms.send.batch.route, beanName: scopedTarget.smsConfig, field: com.xftech.cdp.infra.client.sms.config.SmsConfig.batchReport
2025-06-12 14:06:42.351 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.sms.doRealSend, beanName: scopedTarget.smsConfig, field: com.xftech.cdp.infra.client.sms.config.SmsConfig.doRealSend
2025-06-12 14:06:42.351 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.sms.uniqueKey, beanName: scopedTarget.smsConfig, field: com.xftech.cdp.infra.client.sms.config.SmsConfig.key
2025-06-12 14:06:42.355 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.sso.host, beanName: scopedTarget.ssoConfig, field: com.xftech.cdp.infra.client.sso.config.SsoConfig.ssoHost
2025-06-12 14:06:42.355 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.sso.getImageCode.route, beanName: scopedTarget.ssoConfig, field: com.xftech.cdp.infra.client.sso.config.SsoConfig.getImageCodeRoute
2025-06-12 14:06:42.355 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.sso.sendCaptcha.route, beanName: scopedTarget.ssoConfig, field: com.xftech.cdp.infra.client.sso.config.SsoConfig.sendCaptchaRoute
2025-06-12 14:06:42.355 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.sso.getToken.route, beanName: scopedTarget.ssoConfig, field: com.xftech.cdp.infra.client.sso.config.SsoConfig.getTokenRoute
2025-06-12 14:06:42.355 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.sso.getToken.route, beanName: scopedTarget.ssoConfig, field: com.xftech.cdp.infra.client.sso.config.SsoConfig.checkTokenRoute
2025-06-12 14:06:42.357 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.tele.host, beanName: scopedTarget.telemarketingConfig, field: com.xftech.cdp.infra.client.telemarketing.config.TelemarketingConfig.host
2025-06-12 14:06:42.357 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.tele.newHost, beanName: scopedTarget.telemarketingConfig, field: com.xftech.cdp.infra.client.telemarketing.config.TelemarketingConfig.newHost
2025-06-12 14:06:42.357 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.tele.route.nameType, beanName: scopedTarget.telemarketingConfig, field: com.xftech.cdp.infra.client.telemarketing.config.TelemarketingConfig.nameType
2025-06-12 14:06:42.357 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.tele.route.nameDetail, beanName: scopedTarget.telemarketingConfig, field: com.xftech.cdp.infra.client.telemarketing.config.TelemarketingConfig.nameDetail
2025-06-12 14:06:42.357 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.tele.route.nameConfig, beanName: scopedTarget.telemarketingConfig, field: com.xftech.cdp.infra.client.telemarketing.config.TelemarketingConfig.nameConfig
2025-06-12 14:06:42.357 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.tele.route.saveNameConfig, beanName: scopedTarget.telemarketingConfig, field: com.xftech.cdp.infra.client.telemarketing.config.TelemarketingConfig.saveNameConfig
2025-06-12 14:06:42.357 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.tele.route.priorityConfig, beanName: scopedTarget.telemarketingConfig, field: com.xftech.cdp.infra.client.telemarketing.config.TelemarketingConfig.priorityConfig
2025-06-12 14:06:42.357 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.tele.route.pushNameData, beanName: scopedTarget.telemarketingConfig, field: com.xftech.cdp.infra.client.telemarketing.config.TelemarketingConfig.pushNameData
2025-06-12 14:06:42.357 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.tele.route.name.list, beanName: scopedTarget.telemarketingConfig, field: com.xftech.cdp.infra.client.telemarketing.config.TelemarketingConfig.nameListRoute
2025-06-12 14:06:42.357 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.tele.route.save.list, beanName: scopedTarget.telemarketingConfig, field: com.xftech.cdp.infra.client.telemarketing.config.TelemarketingConfig.saveBatchRoute
2025-06-12 14:06:42.357 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.tele.uniqueKey, beanName: scopedTarget.telemarketingConfig, field: com.xftech.cdp.infra.client.telemarketing.config.TelemarketingConfig.key
2025-06-12 14:06:42.357 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.tele.route.policyNameList, beanName: scopedTarget.telemarketingConfig, field: com.xftech.cdp.infra.client.telemarketing.config.TelemarketingConfig.policyNameList
2025-06-12 14:06:42.360 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cis.queryCisUserSwitch, beanName: scopedTarget.cisConfig, field: com.xftech.cdp.infra.client.usercenter.config.CisConfig.queryCisUserSwitch
2025-06-12 14:06:42.360 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cis.queryUserNoByMobileAndAppUrl, beanName: scopedTarget.cisConfig, field: com.xftech.cdp.infra.client.usercenter.config.CisConfig.queryUserNoByMobileAndAppUrl
2025-06-12 14:06:42.360 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cis.queryRegisterInfoByUserNoUrl, beanName: scopedTarget.cisConfig, field: com.xftech.cdp.infra.client.usercenter.config.CisConfig.queryRegisterInfoByUserNo
2025-06-12 14:06:42.360 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cis.queryMobileByUserNoUrl, beanName: scopedTarget.cisConfig, field: com.xftech.cdp.infra.client.usercenter.config.CisConfig.queryMobileByUserNo
2025-06-12 14:06:42.360 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cis.queryBatchUserByUserNo, beanName: scopedTarget.cisConfig, field: com.xftech.cdp.infra.client.usercenter.config.CisConfig.queryBatchUserByUserNo
2025-06-12 14:06:42.367 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: pocketId, beanName: scopedTarget.dataFeatureConfig, field: com.xftech.cdp.infra.client.usercenter.config.DataFeatureConfig.pocketId
2025-06-12 14:06:42.367 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: datafeaturecore.Url, beanName: scopedTarget.dataFeatureConfig, field: com.xftech.cdp.infra.client.usercenter.config.DataFeatureConfig.datafeaturecoreUrl
2025-06-12 14:06:42.370 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: php.user.host, beanName: scopedTarget.userCenterConfig, field: com.xftech.cdp.infra.client.usercenter.config.UserCenterConfig.phpUserHost
2025-06-12 14:06:42.370 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: php.user.getByMobile, beanName: scopedTarget.userCenterConfig, field: com.xftech.cdp.infra.client.usercenter.config.UserCenterConfig.userByMobile
2025-06-12 14:06:42.370 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: php.user.getAbNum, beanName: scopedTarget.userCenterConfig, field: com.xftech.cdp.infra.client.usercenter.config.UserCenterConfig.abNum
2025-06-12 14:06:42.370 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: php.user.batch, beanName: scopedTarget.userCenterConfig, field: com.xftech.cdp.infra.client.usercenter.config.UserCenterConfig.batchUserId
2025-06-12 14:06:42.370 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: php.user.uniqueKey, beanName: scopedTarget.userCenterConfig, field: com.xftech.cdp.infra.client.usercenter.config.UserCenterConfig.key
2025-06-12 14:06:42.384 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.crowd.alarmUrl, beanName: scopedTarget.crowdConfig, field: com.xftech.cdp.infra.config.CrowdConfig.alarmUrl
2025-06-12 14:06:42.384 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.crowd.rockPageSize, beanName: scopedTarget.crowdConfig, field: com.xftech.cdp.infra.config.CrowdConfig.rockPageSize
2025-06-12 14:06:42.384 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.crowd.rockPageSizeNew, beanName: scopedTarget.crowdConfig, field: com.xftech.cdp.infra.config.CrowdConfig.rockPageSizeNew
2025-06-12 14:06:42.384 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.crowd.batchSaveSize, beanName: scopedTarget.crowdConfig, field: com.xftech.cdp.infra.config.CrowdConfig.batchSaveSize
2025-06-12 14:06:42.384 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: cdp.crowd.taskBatchCount, beanName: scopedTarget.crowdConfig, field: com.xftech.cdp.infra.config.CrowdConfig.taskBatchCount
2025-06-12 14:06:42.384 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: crowd.alarm.job, beanName: scopedTarget.crowdConfig, field: com.xftech.cdp.infra.config.CrowdConfig.alarmJobId
2025-06-12 14:06:42.384 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: crowd.Job.stop, beanName: scopedTarget.crowdConfig, field: com.xftech.cdp.infra.config.CrowdConfig.crowdJobStop
2025-06-12 14:06:42.385 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: notQueryUserLabelDetail, beanName: scopedTarget.crowdConfig, field: com.xftech.cdp.infra.config.CrowdConfig.notQueryUserLabelDetail
2025-06-12 14:06:42.385 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: crowd.detail.table.size, beanName: scopedTarget.crowdConfig, field: com.xftech.cdp.infra.config.CrowdConfig.crowdDetailTableSize
2025-06-12 14:06:42.385 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: crowd.refresh.timeout.limit, beanName: scopedTarget.crowdConfig, field: com.xftech.cdp.infra.config.CrowdConfig.crowdRefreshTimeoutLimit
2025-06-12 14:06:42.385 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: crowd.refresh.timeout.atMobile, beanName: scopedTarget.crowdConfig, field: com.xftech.cdp.infra.config.CrowdConfig.crowdRefreshTimeoutAtMobiles
2025-06-12 14:06:42.385 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: crowd.alarm.cp.atMobile, beanName: scopedTarget.crowdConfig, field: com.xftech.cdp.infra.config.CrowdConfig.crowdalarmMobiles
2025-06-12 14:06:42.385 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: crowd.alarm.prod.atMobile, beanName: scopedTarget.crowdConfig, field: com.xftech.cdp.infra.config.CrowdConfig.crowdalarmProdMobiles
2025-06-12 14:06:42.385 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: crowd.execution.time, beanName: scopedTarget.crowdConfig, field: com.xftech.cdp.infra.config.CrowdConfig.executionTime
2025-06-12 14:06:42.385 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: crowd.batcQueryUserMobile.size, beanName: scopedTarget.crowdConfig, field: com.xftech.cdp.infra.config.CrowdConfig.batcQueryUserMobileSize
2025-06-12 14:06:42.388 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: strategy.dispatch.channel.sms.pagesize, beanName: scopedTarget.strategyConfig, field: com.xftech.cdp.infra.config.StrategyConfig.smsBatchSize
2025-06-12 14:06:42.388 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: strategy.dispatch.channel.push.pagesize, beanName: scopedTarget.strategyConfig, field: com.xftech.cdp.infra.config.StrategyConfig.pushBatchSize
2025-06-12 14:06:42.388 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: strategy.dispatch.channel.ai.pronto.pagesize, beanName: scopedTarget.strategyConfig, field: com.xftech.cdp.infra.config.StrategyConfig.aiProntoBatchSize
2025-06-12 14:06:42.388 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: strategy.dispatch.channel.tele.pagesize, beanName: scopedTarget.strategyConfig, field: com.xftech.cdp.infra.config.StrategyConfig.teleBatchSize
2025-06-12 14:06:42.388 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: strategy.dispatch.channel.coupon.pagesize, beanName: scopedTarget.strategyConfig, field: com.xftech.cdp.infra.config.StrategyConfig.couponBatchSize
2025-06-12 14:06:42.388 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: strategy.dispatch.channel.offlineengine.pagesize, beanName: scopedTarget.strategyConfig, field: com.xftech.cdp.infra.config.StrategyConfig.offlieEngineBatchSize
2025-06-12 14:06:42.388 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: strategy.dispatch.channel.increaseAmt.pagesize, beanName: scopedTarget.strategyConfig, field: com.xftech.cdp.infra.config.StrategyConfig.increaseAmtBatchSize
2025-06-12 14:06:42.388 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: strategy.dispatch.detail.query.pagesize, beanName: scopedTarget.strategyConfig, field: com.xftech.cdp.infra.config.StrategyConfig.detailBatchSize
2025-06-12 14:06:42.388 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: strategy.dispatch.blank.query.pagesize, beanName: scopedTarget.strategyConfig, field: com.xftech.cdp.infra.config.StrategyConfig.blankBatchSize
2025-06-12 14:06:42.388 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: strategy.dispatch.index.query.way, beanName: scopedTarget.strategyConfig, field: com.xftech.cdp.infra.config.StrategyConfig.dispatchIndexQueryWay
2025-06-12 14:06:42.388 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: strategy.monitor.today.updateFromCache, beanName: scopedTarget.strategyConfig, field: com.xftech.cdp.infra.config.StrategyConfig.monitorUpdateFromCache
2025-06-12 14:06:42.388 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: strategy.label.query.switch, beanName: scopedTarget.strategyConfig, field: com.xftech.cdp.infra.config.StrategyConfig.labelQuerySwitch
2025-06-12 14:06:42.388 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: strategy.label.query.BatchSize, beanName: scopedTarget.strategyConfig, field: com.xftech.cdp.infra.config.StrategyConfig.labelQueryBatchSize
2025-06-12 14:06:42.388 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: strategy.rule.max.limitDays, beanName: scopedTarget.strategyConfig, field: com.xftech.cdp.infra.config.StrategyConfig.maxLimitDays
2025-06-12 14:06:42.388 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: strategy.rule.max.limitTimes, beanName: scopedTarget.strategyConfig, field: com.xftech.cdp.infra.config.StrategyConfig.maxLimitTimes
2025-06-12 14:06:42.389 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: strategy.realtime.timeout.retryTimes, beanName: scopedTarget.strategyConfig, field: com.xftech.cdp.infra.config.StrategyConfig.retryTimes
2025-06-12 14:06:42.389 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: strategy.realtime.timeout.RetryInterval, beanName: scopedTarget.strategyConfig, field: com.xftech.cdp.infra.config.StrategyConfig.labelTimeOutRetryInterval
2025-06-12 14:06:42.389 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: strategy.offline.crowdStatus.alarmInterval, beanName: scopedTarget.strategyConfig, field: com.xftech.cdp.infra.config.StrategyConfig.offlineStrategyCrowdStatusAlarmInterval
2025-06-12 14:06:42.389 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: strategy.dispatch.record.query.maxDays, beanName: scopedTarget.strategyConfig, field: com.xftech.cdp.infra.config.StrategyConfig.hasDispatchRecordDays
2025-06-12 14:06:42.395 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: spring.rabbitmq.biz.batchSize, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.batchSize
2025-06-12 14:06:42.395 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: spring.rabbitmq.biz.timeout, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.timeout
2025-06-12 14:06:42.395 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: spring.rabbitmq.biz.prefetchSize, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.prefetchSize
2025-06-12 14:06:42.395 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: spring.rabbitmq.host3, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.host3
2025-06-12 14:06:42.395 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: spring.rabbitmq.port3, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.port3
2025-06-12 14:06:42.395 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: spring.rabbitmq.username3, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.username3
2025-06-12 14:06:42.395 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: spring.rabbitmq.password3, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.password3
2025-06-12 14:06:42.395 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: spring.rabbitmq.virtual-host3, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.virtualHost3
2025-06-12 14:06:42.395 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: biz.event.hl.exchange, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.bizEventHighLevelExchange
2025-06-12 14:06:42.395 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: biz.event.hl.exchangeType, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.bizEventHighLevelExchangeType
2025-06-12 14:06:42.395 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: biz.event.hl.routingKey, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.bizEventHighLevelRoutingKey
2025-06-12 14:06:42.395 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: biz.event.hl.queue.name, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.bizEventHighLevelQueueName
2025-06-12 14:06:42.395 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: biz.event.ml.exchange, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.bizEventMiddleLevelExchange
2025-06-12 14:06:42.395 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: biz.event.ml.exchangeType, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.bizEventMiddleLevelExchangeType
2025-06-12 14:06:42.395 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: biz.event.ml.routingKey, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.bizEventMiddleLevelRoutingKey
2025-06-12 14:06:42.395 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: biz.event.ml.queue.name, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.bizEventMiddleLevelQueueName
2025-06-12 14:06:42.396 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: biz.event.atOnce.delay.time, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.bizEventAtOnceDelayTime
2025-06-12 14:06:42.396 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: biz.event.delay.exchange, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.bizEventDelayExchange
2025-06-12 14:06:42.396 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: biz.event.delay.xDelayedType, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.bizEventDelayXDelayedType
2025-06-12 14:06:42.396 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: biz.event.delay.exchangeType, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.bizEventDelayExchangeType
2025-06-12 14:06:42.396 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: biz.event.delay.hl.routingKey, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.bizEventHighLevelDelayRoutingKey
2025-06-12 14:06:42.396 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: biz.event.delay.hl.queue.name, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.bizEventHighLevelDelayQueueName
2025-06-12 14:06:42.396 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: biz.event.delay.ml.routingKey, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.bizEventMiddleLevelDelayRoutingKey
2025-06-12 14:06:42.396 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: biz.event.delay.ml.queue.name, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.bizEventMiddleLevelDelayQueueName
2025-06-12 14:06:42.396 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: biz.event.delay.ll.routingKey, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.bizEventLowLevelDelayRoutingKey
2025-06-12 14:06:42.396 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: biz.event.delay.ll.queue.name, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.bizEventLowLevelDelayQueueName
2025-06-12 14:06:42.396 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: biz.event.dispatch.exchange, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.bizEventDispatchExchange
2025-06-12 14:06:42.396 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: biz.event.dispatch.exchangeType, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.bizEventDispatchExchangeType
2025-06-12 14:06:42.396 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: biz.event.sms.dispatch.routingKey, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.bizEventSmsDispatchRoutingKey
2025-06-12 14:06:42.396 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: biz.event.sms.dispatch.queue.name, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.bizEventSmsDispatchQueueName
2025-06-12 14:06:42.396 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: biz.event.tele.dispatch.routingKey, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.bizEventTeleDispatchRoutingKey
2025-06-12 14:06:42.396 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: biz.event.tele.dispatch.queue.name, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.bizEventTeleDispatchQueueName
2025-06-12 14:06:42.396 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: biz.event.coupon.dispatch.routingKey, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.bizEventCouponDispatchRoutingKey
2025-06-12 14:06:42.396 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: biz.event.coupon.dispatch.queue.name, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.bizEventCouponDispatchQueueName
2025-06-12 14:06:42.396 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: biz.event.noMarket.dispatch.routingKey, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.bizEventNoMarketRoutingKey
2025-06-12 14:06:42.396 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: biz.event.noMarket.dispatch.queue.name, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.bizEventNoMarketQueueName
2025-06-12 14:06:42.396 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: biz.event.push.dispatch.routingKey, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.bizEventPushDispatchRoutingKey
2025-06-12 14:06:42.396 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: biz.event.push.dispatch.queue.name, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.bizEventPushDispatchQueueName
2025-06-12 14:06:42.396 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: biz.event.increaseAmt.dispatch.routingKey, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.bizEventIncreaseAmtDispatchRoutingKey
2025-06-12 14:06:42.396 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: biz.event.increaseAmt.dispatch.queue.name, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.bizEventIncreaseAmtDispatchQueueName
2025-06-12 14:06:42.396 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: biz.event.lifeRights.dispatch.routingKey, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.bizEventLifeRightsDispatchRoutingKey
2025-06-12 14:06:42.396 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: biz.event.lifeRights.dispatch.queue.name, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.bizEventLifeRightsDispatchQueueName
2025-06-12 14:06:42.396 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: biz.event.xDayInterestFree.dispatch.routingKey, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.bizEventXDayInterestFreeDispatchRoutingKey
2025-06-12 14:06:42.397 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: biz.event.xDayInterestFree.dispatch.queue.name, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.bizEventXDayInterestFreeDispatchQueueName
2025-06-12 14:06:42.397 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: biz.event.ai.dispatch.routingKey, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.bizEventAiDispatchRoutingKey
2025-06-12 14:06:42.397 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: biz.event.ai.dispatch.queue.name, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.bizEventAiDispatchQueueName
2025-06-12 14:06:42.397 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: biz.event.decision.exchange, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.bizEventDecisionExchange
2025-06-12 14:06:42.397 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: biz.event.decision.exchangeType, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.bizEventDecisionExchangeType
2025-06-12 14:06:42.397 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: biz.event.decision.routingKey, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.bizEventDecisionRoutingKey
2025-06-12 14:06:42.397 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: biz.event.decision.queue.name, beanName: scopedTarget.bizEventMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.BizEventMqConfig$$EnhancerBySpringCGLIB$$a885970c.bizEventDecisionQueueName
2025-06-12 14:06:42.404 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: spring.rabbitmq.coupon.batchSize, beanName: scopedTarget.couponMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.CouponMqConfig$$EnhancerBySpringCGLIB$$d8941d4b.batchSize
2025-06-12 14:06:42.404 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: spring.rabbitmq.coupon.timeout, beanName: scopedTarget.couponMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.CouponMqConfig$$EnhancerBySpringCGLIB$$d8941d4b.timeout
2025-06-12 14:06:42.404 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: spring.rabbitmq.host2, beanName: scopedTarget.couponMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.CouponMqConfig$$EnhancerBySpringCGLIB$$d8941d4b.host2
2025-06-12 14:06:42.404 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: spring.rabbitmq.port2, beanName: scopedTarget.couponMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.CouponMqConfig$$EnhancerBySpringCGLIB$$d8941d4b.port2
2025-06-12 14:06:42.404 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: spring.rabbitmq.username2, beanName: scopedTarget.couponMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.CouponMqConfig$$EnhancerBySpringCGLIB$$d8941d4b.username2
2025-06-12 14:06:42.404 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: spring.rabbitmq.password2, beanName: scopedTarget.couponMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.CouponMqConfig$$EnhancerBySpringCGLIB$$d8941d4b.password2
2025-06-12 14:06:42.404 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: spring.rabbitmq.virtual-host2, beanName: scopedTarget.couponMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.CouponMqConfig$$EnhancerBySpringCGLIB$$d8941d4b.virtualHost2
2025-06-12 14:06:42.404 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: coupon.callback.exchange, beanName: scopedTarget.couponMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.CouponMqConfig$$EnhancerBySpringCGLIB$$d8941d4b.couponCallbackExchange
2025-06-12 14:06:42.404 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: coupon.callback.exchangeType, beanName: scopedTarget.couponMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.CouponMqConfig$$EnhancerBySpringCGLIB$$d8941d4b.couponExchangeType
2025-06-12 14:06:42.404 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: coupon.callback.routingKey, beanName: scopedTarget.couponMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.CouponMqConfig$$EnhancerBySpringCGLIB$$d8941d4b.couponCallbackRoutingKey
2025-06-12 14:06:42.405 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: coupon.callback.queue.name, beanName: scopedTarget.couponMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.CouponMqConfig$$EnhancerBySpringCGLIB$$d8941d4b.couponCallbackQueueName
2025-06-12 14:06:42.405 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: coupon.callback.deadLetterExchange, beanName: scopedTarget.couponMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.CouponMqConfig$$EnhancerBySpringCGLIB$$d8941d4b.deadLetterExchange
2025-06-12 14:06:42.405 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: coupon.callback.deadLetterExchangeType, beanName: scopedTarget.couponMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.CouponMqConfig$$EnhancerBySpringCGLIB$$d8941d4b.deadLetterExchangeType
2025-06-12 14:06:42.405 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: coupon.callback.deadLetterRoutingKey, beanName: scopedTarget.couponMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.CouponMqConfig$$EnhancerBySpringCGLIB$$d8941d4b.deadLetterRoutingKey
2025-06-12 14:06:42.405 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: coupon.callback.deadLetterQueueName, beanName: scopedTarget.couponMqConfig, field: com.xftech.cdp.infra.rabbitmq.config.CouponMqConfig$$EnhancerBySpringCGLIB$$d8941d4b.deadLetterQueueName
2025-06-12 14:06:42.408 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: sole.sequence.segment.max.value, beanName: scopedTarget.sequenceConfig, field: com.xftech.cdp.infra.redis.sequence.config.SequenceConfig.segmentMaxValue
2025-06-12 14:06:42.412 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: xxl.job.config.adminAddresses, beanName: scopedTarget.xxlJobParamConfig, field: com.xftech.xxljob.config.XxlJobParamConfig.adminAddresses
2025-06-12 14:06:42.412 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: xxl.job.config.jobGroup, beanName: scopedTarget.xxlJobParamConfig, field: com.xftech.xxljob.config.XxlJobParamConfig.jobGroup
2025-06-12 14:06:42.412 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: xxl.job.config.username, beanName: scopedTarget.xxlJobParamConfig, field: com.xftech.xxljob.config.XxlJobParamConfig.username
2025-06-12 14:06:42.412 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: xxl.job.config.password, beanName: scopedTarget.xxlJobParamConfig, field: com.xftech.xxljob.config.XxlJobParamConfig.password
2025-06-12 14:06:42.412 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: xxl.job.config.alarmEmail, beanName: scopedTarget.xxlJobParamConfig, field: com.xftech.xxljob.config.XxlJobParamConfig.alarmEmail
2025-06-12 14:06:42.412 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: xxl.job.config.executorFailRetryCount, beanName: scopedTarget.xxlJobParamConfig, field: com.xftech.xxljob.config.XxlJobParamConfig.executorFailRetryCount
2025-06-12 14:06:42.412 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: xxl.job.config.executorTimeout, beanName: scopedTarget.xxlJobParamConfig, field: com.xftech.xxljob.config.XxlJobParamConfig.executorTimeout
2025-06-12 14:06:42.412 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: xxl.job.config.executorRouteStrategy, beanName: scopedTarget.xxlJobParamConfig, field: com.xftech.xxljob.config.XxlJobParamConfig.executorRouteStrategy
2025-06-12 14:06:42.412 [/] [main] INFO  c.c.f.a.spring.annotation.SpringValueProcessor:141 - Monitoring key: xxl.job.config.executorBlockStrategy, beanName: scopedTarget.xxlJobParamConfig, field: com.xftech.xxljob.config.XxlJobParamConfig.executorBlockStrategy
2025-06-12 14:06:42.431 [/] [main] INFO  com.xftech.cdp.CdpApplication:61 - Started CdpApplication in 51.815 seconds (JVM running for 52.711)
2025-06-12 14:06:42.990 [/] [main] INFO  com.xftech.cdp.runner.InitializeRunner:55 - init aviator function success!
2025-06-12 14:06:42.991 [/] [main] INFO  c.x.p.mybatis.interceptor.UdpSqlLogInterceptor:39 - Preparing(strategy.getExecutingEventStrategy):
select
         
        id, name,user_convert, detail_description, ab_test, ab_type, biz_key, send_ruler, validity_begin, validity_end, send_frequency, crowd_pack_id,market_channel,status,group_type,market_crowd_type,business_type,flow_ctrl_id, created_time, updated_time, d_flag, created_op, updated_op,updated_op_mobile,type,engine_code,dispatch_config,flow_no,dispatch_type,
        market_type, calling_source
     
        from strategy
        where d_flag = 0 and send_ruler = 2 and status = 1
        and flow_no is null
Parameters:null
