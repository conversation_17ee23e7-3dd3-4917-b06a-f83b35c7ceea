package com.xftech.cdp.infra.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.roaringbitmap.longlong.Roaring64Bitmap;

import java.util.List;
import java.util.function.Consumer;

/**
 * @<NAME_EMAIL>
 */

public class UploadCrowdPageReadListen<T> implements ReadListener<T> {
    Roaring64Bitmap bitmap = new Roaring64Bitmap();

    private final int batchCount;
    private List<T> cachedDataList;
    private final Consumer<List<T>> consumer;

    public UploadCrowdPageReadListen(int batchCount,Consumer<List<T>> consumer) {
        this.batchCount = batchCount;
        this.cachedDataList = ListUtils.newArrayListWithExpectedSize(batchCount);
        this.consumer = consumer;
    }

    @Override
    public void invoke(T data, AnalysisContext context) {
        UploadCrowdModel uploadCrowd = (UploadCrowdModel) data;
        if (!StringUtils.isNumeric(uploadCrowd.getUserId())) {
            return;
        }
        long userId = Long.parseLong(uploadCrowd.getUserId());
        if (bitmap.contains(userId)) {
            return;
        }
        this.bitmap.add(userId);
        this.cachedDataList.add(data);
        if (this.cachedDataList.size() >= batchCount) {
            this.consumer.accept(this.cachedDataList);
            this.cachedDataList = ListUtils.newArrayListWithExpectedSize(batchCount);
        }

    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        if (CollectionUtils.isNotEmpty(this.cachedDataList)) {
            this.consumer.accept(this.cachedDataList);
        }

    }
}
