package com.xftech.cdp.strategy;

import com.xftech.cdp.BaseTest;
import com.xftech.cdp.infra.rocketmq.login1.Login1RocketMsgConsumer;

import org.apache.rocketmq.common.message.MessageExt;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @<NAME_EMAIL>
 */
public class StrategyIncreaseTest extends BaseTest {
    @Autowired
    private Login1RocketMsgConsumer login1RocketMsgConsumer;

    @Test
    public void testIncrease() {
        String topic = "tp_biz_report_Login1";
        String message = "{\n" +
                "    \"user_id\": \"111117974000\",\n" +
                "    \"app_user_id\": \"1939303102150119177\",\n" +
                "    \"biz_event_type\": \"Login1\",\n" +
                "    \"type\": \"login\",\n" +
                "    \"login_type\": \"sms_code\",\n" +
                "    \"source_type\": \"wap\",\n" +
                "    \"app\": \"xyf01\",\n" +
                "    \"inner_app\": \"\",\n" +
                "    \"channel\": \"\",\n" +
                "    \"login_utm_source\": \"111\",\n" +
                "    \"mobile\": \"13726289834\",\n" +
                "    \"os\": \"ios\",\n" +
                "    \"ua\": \"xyf-flow-system\",\n" +
                "    \"ip\": \"**************\",\n" +
                "    \"mobile_protyle\": \"m878caf52a122e1e07ff9da6498e6b8ac\",\n" +
                "    \"requested_time\": \"2024-09-17 20:33:17\",\n" +
                "    \"timestamp\": 1726230797,\n" +
                "    \"utm_source\": \"\",\n" +
                "    \"send_datetime\": \"\",\n" +
                "    \"extra_data\": [],\n" +
                "    \"trigger_datetime\": \"2024-09-17 20:33:17\"\n" +
                "}";
        login1RocketMsgConsumer.doMessage(topic, message, new MessageExt());
    }

}