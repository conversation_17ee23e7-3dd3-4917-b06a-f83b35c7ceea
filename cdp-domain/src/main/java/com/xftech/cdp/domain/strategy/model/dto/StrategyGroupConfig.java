package com.xftech.cdp.domain.strategy.model.dto;

import lombok.Data;

import java.util.List;
import java.util.Optional;

/**
 * 策略分组配置
 * <p>
 * 不营销组
 * {"digits": 2, "positionEnd": 99, "positionStart": 0, "crowdLabelOption": {"segments": [{"max": "", "min": ""}]}}
 * <p>
 * UID后两位
 * {"digits": 2, "positionEnd": 99, "positionStart": 0, "crowdLabelOption": {"segments": [{"max": "67", "min": "48"}]}}
 * <p>
 * 一位随机数
 * {"digits": 1, "positionEnd": 5, "positionStart": 5, "crowdLabelOption": {"items": [0, 1, 2, 3, 4]}}
 * <p>
 * 两位随机数
 * {"digits": 2, "positionEnd": 15, "positionStart": 14, "crowdLabelOption": {"segments": [{"max": "45", "min": "00"}]}}
 * <p>
 * 新随机数
 * {"digits": n, "positionEnd": 99, "positionStart": 0, "crowdLabelOption": {"segments": [{"max": "45", "min": "00"}]}}
 *
 * @<NAME_EMAIL>
 * @date 2023/6/29 14:16
 */
@Data
public class StrategyGroupConfig {

    private int digits;

    private int positionStart;

    private int positionEnd;

    private CrowdLabelOption crowdLabelOption;

    @Data
    public static class CrowdLabelOption {

        private List<Long> items;

        private List<Segment> segments;
    }

    @Data
    public static class Segment {
        private Long min;

        private Long max;

        public Long getMin() {
            return Optional.ofNullable(min).orElse(max);
        }

        public Long getMax() {
            return Optional.ofNullable(max).orElse(min);
        }
    }
}
