/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.client.increaseamt.model.resp;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version $ AdsLabelResp, v 0.1 2024/7/4 11:11 lingang.han Exp $
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AccessControlResp {
    private Boolean status;//	客户申请状态	boolean	是	true-正常 false-异常 注：总逻辑：若流程节点为预授信、授信，则判断存在授信禁申返回失败；若流程节点为借款，则判断存在借款禁申或者管制返回失败；若流程节点为none或者其余内容，则判断存在授信禁申或者借款禁申或者管制返回失败（即为最严逻辑）；
    private String flow_no;//	流水号	String	否	status="false"时返回
    private Boolean forbidden_status;//	禁申状态	boolean	是	true-禁申 false-非禁申
    private String forbidden_start_time;//	禁申开始时间	string	否
    private String forbidden_end_time;//	禁申结束时间	string	否
    private String forbidden_reason;//	禁申原因	string	否
    private String control_status;//	管制状态	boolean	是	true-管制 false-非管制
    private String control_reason;//	管制原因	string	否
    private String freeze_status;//	冻结状态	boolean	是	true-冻结 false-非冻结
    private String diversion_label;//	导流标签	String	否	无导流标签返回""
}