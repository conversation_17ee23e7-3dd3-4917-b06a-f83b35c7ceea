# 引擎触达对比分析报告: 离线-引擎触达 vs T0-引擎触达

## 1. 概述

本文档详细对比分析离线-引擎触达和T0-引擎触达两种模式，重点分析它们都使用`StrategyEventDispatchServiceImpl.marketingSend`方法的原因，以及在调用场景、数据处理方式上的差异。

## 2. 共同点分析

### 2.1 使用相同的核心方法

两种触达模式都使用`StrategyEventDispatchServiceImpl.marketingSend`方法作为最终的营销执行入口：

```java
public int marketingSend(DispatchDto dispatchDto, CrowdDetailDo crowdDetailDo,
                        StrategyMarketChannelEnum channelEnum, String groupName,
                        Map detailInfo, @Nullable BizEventVO bizEventVO)
```

### 2.2 相同的渠道分发逻辑

两种模式在`marketingSend`方法内部使用相同的渠道分发逻辑：

```java
switch (channelEnum) {
    case SMS:
        result = eventDispatchService.sendSmsEvent(...);
        break;
    case VOICE:
        result = eventDispatchService.sendTeleEvent(...);
        break;
    case LIFE_RIGHTS:
        result = eventDispatchService.sendLifeRightsEvent(...);
        break;
    // 其他渠道...
}
```

### 2.3 相同的外部服务调用

最终都调用相同的外部服务接口：
- 短信服务接口
- 电销服务接口  
- 生活权益服务接口
- AI语音服务接口等

## 3. 关键差异分析

### 3.1 触发方式差异

| 维度 | 离线-引擎触达 | T0-引擎触达 |
|------|---------------|-------------|
| **触发源** | XxlJob定时任务 | RocketMQ消息事件 |
| **触发频率** | 按预设时间批量触发 | 事件发生时实时触发 |
| **处理模式** | 批量处理 | 单个事件处理 |
| **数据来源** | 人群分片数据 | 实时事件数据 |

### 3.2 调用链路差异

#### 3.2.1 离线-引擎触达调用链路
```mermaid
graph TD
    A[XxlJob定时任务] --> B[OfflineStrategyJobDispatch]
    B --> C[StrategyTaskDistributeHandler]
    C --> D[DistributeOfflineEngineDispatchServiceImpl]
    D --> E[策略引擎决策]
    E --> F[marketingSend方法]
```

#### 3.2.2 T0-引擎触达调用链路
```mermaid
graph TD
    A[RocketMQ消息] --> B[MQ消费者]
    B --> C[StrategyEventDispatchService.prescreen]
    C --> D[StrategyEventDispatchService.rescreen]
    D --> E[策略引擎决策]
    E --> F[marketingSend方法]
```

### 3.3 数据处理方式差异

#### 3.3.1 离线-引擎触达
```java
// 批量处理人群分片数据
List<CrowdDetailDo> crowdDetailList = crowdSliceRepository.selectByCrowdSliceId(crowdSliceId);
for (CrowdDetailDo crowdDetail : crowdDetailList) {
    // 调用引擎决策
    PredictDecisionDto decision = strategyEngineService.predict(strategyDo, crowdDetail, engineCode);
    // 执行营销动作
    for (Action action : decision.getActions()) {
        marketingSend(dispatchDto, crowdDetail, channelEnum, action.getGroup_id(), 
                     action.getDetail_info(), null);
    }
}
```

#### 3.3.2 T0-引擎触达
```java
// 单个事件实时处理
BizEventVO event = parseFromMqMessage(message);
// 预筛阶段
prescreen(messageId, bizEventMessageVO);
// 复筛阶段 - 调用引擎决策
PredictDecisionDto decision = strategyEngineService.predict(strategyDo, crowdDetail, engineCode);
// 执行营销动作
for (Action action : decision.getActions()) {
    marketingSend(dispatchDto, crowdDetail, channelEnum, action.getGroup_id(), 
                 action.getDetail_info(), event);
}
```

### 3.4 引擎决策调用差异

| 维度 | 离线-引擎触达 | T0-引擎触达 |
|------|---------------|-------------|
| **决策时机** | 批量预处理时决策 | 事件发生时实时决策 |
| **用户上下文** | 静态用户属性 | 实时事件上下文 + 用户属性 |
| **决策延迟** | 可接受较高延迟 | 要求低延迟响应 |
| **并发量** | 批量并发，峰值高 | 持续并发，相对平稳 |

### 3.5 业务场景差异

#### 3.5.1 离线-引擎触达适用场景
- **营销活动推广**: 定期的促销活动推送
- **用户关怀**: 定期的用户关怀消息
- **产品推荐**: 基于用户画像的产品推荐
- **风险提醒**: 定期的风险评估和提醒

#### 3.5.2 T0-引擎触达适用场景
- **实时营销**: 用户行为触发的即时营销
- **事件响应**: 特定事件发生后的快速响应
- **个性化推荐**: 基于实时行为的个性化内容
- **风险预警**: 实时风险检测和预警

### 3.6 性能特征差异

| 维度 | 离线-引擎触达 | T0-引擎触达 |
|------|---------------|-------------|
| **吞吐量** | 高吞吐量，批量处理 | 中等吞吐量，单个处理 |
| **延迟** | 高延迟可接受 | 低延迟要求 |
| **资源使用** | 周期性高峰 | 持续稳定 |
| **扩展性** | 水平扩展容易 | 需要考虑实时性 |

## 4. 为什么使用相同的方法？

### 4.1 代码复用原则
- **避免重复代码**: 渠道分发逻辑相同，无需重复实现
- **统一接口标准**: 保证不同触达模式的接口一致性
- **降低维护成本**: 统一的方法便于维护和升级

### 4.2 业务逻辑一致性
- **渠道处理逻辑相同**: 无论离线还是实时，最终的渠道处理逻辑都是一样的
- **外部服务调用相同**: 调用的外部服务接口和参数都是一致的
- **数据记录格式相同**: 执行结果的记录格式和字段都是统一的

### 4.3 架构设计考虑
- **分层架构**: 将触达执行层与业务逻辑层分离
- **策略模式**: 通过参数区分不同的调用场景
- **可扩展性**: 便于后续增加新的触达模式

## 5. 违反单一职责原则的问题

### 5.1 问题分析
`marketingSend`方法确实存在违反单一职责原则的问题：

```java
public int marketingSend(DispatchDto dispatchDto, CrowdDetailDo crowdDetailDo,
                        StrategyMarketChannelEnum channelEnum, String groupName,
                        Map detailInfo, @Nullable BizEventVO bizEventVO) {
    // 既要处理离线引擎触达
    // 又要处理T0引擎触达
    // 还要处理不同的渠道类型
}
```

### 5.2 潜在风险
- **代码耦合度高**: 不同业务场景的代码混合在一起
- **测试复杂度高**: 需要考虑多种场景的组合测试
- **维护难度大**: 修改一种场景可能影响其他场景
- **扩展性受限**: 新增场景需要修改现有方法

### 5.3 重构建议

#### 5.3.1 策略模式重构
```java
public interface MarketingSendStrategy {
    int send(DispatchDto dispatchDto, CrowdDetailDo crowdDetailDo, 
             StrategyMarketChannelEnum channelEnum, String groupName, 
             Map detailInfo);
}

public class OfflineEngineMarketingSendStrategy implements MarketingSendStrategy {
    // 离线引擎触达专用逻辑
}

public class RealtimeEngineMarketingSendStrategy implements MarketingSendStrategy {
    // T0引擎触达专用逻辑
}
```

#### 5.3.2 工厂模式选择策略
```java
public class MarketingSendStrategyFactory {
    public MarketingSendStrategy getStrategy(SendModeEnum sendMode) {
        switch (sendMode) {
            case OFFLINE_ENGINE:
                return new OfflineEngineMarketingSendStrategy();
            case REALTIME_ENGINE:
                return new RealtimeEngineMarketingSendStrategy();
            default:
                throw new IllegalArgumentException("Unsupported send mode: " + sendMode);
        }
    }
}
```

## 6. 监控和运维差异

### 6.1 监控指标差异

| 监控维度 | 离线-引擎触达 | T0-引擎触达 |
|----------|---------------|-------------|
| **执行频率** | 按任务周期监控 | 按事件频率监控 |
| **成功率** | 批次成功率 | 事件处理成功率 |
| **延迟监控** | 任务执行耗时 | 事件处理延迟 |
| **资源监控** | 周期性资源峰值 | 持续资源使用 |

### 6.2 告警策略差异
- **离线模式**: 关注任务执行失败、执行超时
- **实时模式**: 关注消息积压、处理延迟

## 7. 总结

离线-引擎触达和T0-引擎触达虽然使用相同的`marketingSend`方法，但在触发方式、数据处理模式、业务场景等方面存在显著差异。使用相同方法的主要原因是代码复用和架构统一，但这也带来了违反单一职责原则的问题。

建议通过策略模式重构，将不同场景的逻辑分离，提高代码的可维护性和可扩展性，同时保持接口的统一性。这样既能解决单一职责问题，又能保持现有架构的优势。
