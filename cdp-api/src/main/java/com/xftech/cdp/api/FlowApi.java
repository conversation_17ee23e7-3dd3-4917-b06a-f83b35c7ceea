package com.xftech.cdp.api;

import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.base.Response;
import com.xftech.cdp.api.dto.req.flow.strategy.*;
import com.xftech.cdp.api.dto.resp.flow.strategy.Flow;
import com.xftech.cdp.api.dto.resp.flow.strategy.FlowDetailResp;
import com.xftech.cdp.api.dto.resp.flow.strategy.FlowMonitorResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
@Api(tags = "流程画布")
@RequestMapping("/flow/strategy")
public interface FlowApi {
    
    @ApiOperation("画布-创建")
    @PostMapping("/create")
    Response<Boolean> create(@RequestBody FlowCreateReq request);

    @ApiOperation("策略-详情")
    @PostMapping("/detail")
    Response<FlowDetailResp> detail(@RequestBody FlowDetailReq request);

    @ApiOperation("画布-更新")
    @PostMapping("/update")
    Response<Boolean> update(@RequestBody FlowUpdateReq request);

    @ApiOperation("画布-操作")
    @PostMapping("/operate")
    Response<Boolean> operate(@RequestBody FlowOperateReq request);

    @ApiOperation("画布-列表")
    @PostMapping("/list")
    Response<PageResultResponse<Flow>> list(@RequestBody FlowListReq request);

    @ApiOperation("画布-报表")
    @PostMapping("/monitor")
    Response<PageResultResponse<FlowMonitorResp>> monitor(@RequestBody FlowMonitorReq request);
}
