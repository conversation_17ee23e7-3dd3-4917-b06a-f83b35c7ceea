/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.repository.cdp.label.po;

import com.xftech.cdp.feign.model.response.MetaLabelDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $ MetaLabel, v 0.1 2024/6/19 15:00 lingang.han Exp $
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MetaLabelDo {

    private Long id;

    private Long dataId;

    private String labelName;

    private String labelCode;

    private String labelDataValueType;

    private String aggDimension;

    private Integer available;

    private Integer online;

    private String labelEnumValue;

    private String checkResult;

    private Date createdTime;

    private Date updatedTime;

    private Integer dFlag;


    public MetaLabelDo(MetaLabelDto metaLabelDto) {
        this.dataId = metaLabelDto.getId();
        this.labelCode = metaLabelDto.getLabelCode();
        this.labelName = metaLabelDto.getLabelName();
        this.labelDataValueType = metaLabelDto.getDataType();
        this.aggDimension = metaLabelDto.getAggDimension();
        this.available = metaLabelDto.getState();
        this.online = 0;
        List<Map<String, Object>> enumValues = metaLabelDto.getEnumValues();
        if (!CollectionUtils.isEmpty(enumValues)) {
            if (enumValues.size() < 50) {
                this.labelEnumValue = enumValues.stream().filter(item -> StringUtils.isNotBlank(item.get("enumValue").toString())).map(item -> item.get("enumValue").toString()).collect(Collectors.joining(","));
            }
        }
    }

    public void addCheckResult(String result) {
        if (StringUtils.isBlank(this.checkResult)) {
            this.checkResult = result;
        } else if (!this.checkResult.contains(result)) {
            this.checkResult = this.checkResult + result;
        }
    }
}