package com.xftech.cdp.domain.strategy.model.enums;

import com.xftech.cdp.domain.strategy.exception.StrategyException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/2/21
 */
@AllArgsConstructor
@Getter
public enum StrategyAbTestEnum {


    YES(1, "是"),
    NO(0, "否");

    private final int code;

    private final String description;

    public static StrategyAbTestEnum getInstance(Integer abTest) {
        for (StrategyAbTestEnum abTestEnum : StrategyAbTestEnum.values()) {
            if (Objects.equals(abTest, abTestEnum.getCode())) {
                return abTestEnum;
            }
        }
        throw new StrategyException("AbTest 配置异常");
    }
}
