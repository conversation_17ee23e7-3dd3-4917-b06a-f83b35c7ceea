/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.client.increaseamt.model.req;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @version $ Request, v 0.1 2024/3/13 21:13 yye.xu Exp $
 */
//
@Data
public class Request {
    private String id_card_number;
    private String cust_no;
    private String user_no;
    private String mobile;
    private String name;
    private Integer modify_cash_line;
    private String modify_stage_type;
    private String unique_flow_number;
    private boolean is_async;
    private Map extend_data;
    private String call_back_url;

    public Map buildExtData(String tableNo, String batchNum, Long detailId,
                            String strategyType, Integer amount, Long userId, String app, String innerApp, String mobile, Long strategyId, String increaseCode) {
        Map<String, Object> extendData = new HashMap<>(8);
        extendData.put("xyf_cdp_dt", tableNo);
        extendData.put("xyf_cdp_batchNum", batchNum);
        extendData.put("xyf_cdp_pk", detailId);
        extendData.put("xyf_cdp_strategyType", strategyType);
        extendData.put("xyf_cdp_amount", amount);
        extendData.put("xyf_cdp_user_id", userId);
        extendData.put("xyf_cdp_user_app", app);
        extendData.put("xyf_cdp_user_innerApp", innerApp);
        extendData.put("xyf_cdp_user_mobile", mobile);
        extendData.put("xyf_cdp_strategyId", strategyId);
        extendData.put("xyf_cdp_increase_code", increaseCode);
        return extendData;
    }
}