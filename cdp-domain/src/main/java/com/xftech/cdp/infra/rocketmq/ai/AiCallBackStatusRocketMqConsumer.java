package com.xftech.cdp.infra.rocketmq.ai;

import com.xftech.cdp.infra.rocketmq.MessageHandlerSelector;
import com.xftech.cdp.infra.utils.WhitelistSwitchUtil;
import com.xinfei.xfframework.common.starter.mq.MqConsumerListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 放宽结果MQ消息消费
 */
@Component
@Slf4j
@RocketMQMessageListener(consumerGroup = "cg_xyf_cdp_tg_telemkt", topic = "tp_call_center_ai_callback", selectorExpression = "tg_telemkt", consumeMode = ConsumeMode.CONCURRENTLY)
public class AiCallBackStatusRocketMqConsumer extends Mq<PERSON>onsumerListener<String> {

    @Resource
    private MessageHandlerSelector handlerSelector;

    @Override
    protected void doMessage(String topic, String bodyMessage, MessageExt messageExt) {
        if (WhitelistSwitchUtil.boolSwitchByApollo("AiCallBackStatusRocketMqConsumerEnable")) {
            log.info("AiCallBackStatusRocketMqConsumer receive message topic={}, messageId={}, body={}", topic, messageExt.getMsgId(), bodyMessage);
            try {
                handlerSelector.doMessage(topic, "tg_telemkt", messageExt.getMsgId(), bodyMessage);
            } catch (Exception e) {
                log.error("AiCallBackStatusRocketMqConsumer consumer error, topic={} messageId={}, body={}", topic, messageExt.getMsgId(), bodyMessage, e);
            }
        }
    }
}