/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.service;

import com.xftech.cdp.domain.strategy.repository.ReportDailyTaskRepository;
import com.xftech.cdp.infra.repository.cdp.strategy.po.ReportDailyTaskDo;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.extern.log4j.Log4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * @version $ ReportDailyStrategyService, v 0.1 2023/12/1 16:04 lingang.han Exp $
 */

@Service
@Log4j
@NoArgsConstructor
@AllArgsConstructor
public class ReportDailyTaskService {

    @Autowired
    private ReportDailyTaskRepository repository;

    public ReportDailyTaskDo queryById(Long id) {
        return repository.selectById(id);
    }

    public boolean save(ReportDailyTaskDo reportDailyTaskDo) {
        return repository.insert(reportDailyTaskDo);
    }

    public boolean updateById(ReportDailyTaskDo reportDailyTaskDo) {
        return repository.updateById(reportDailyTaskDo);
    }

    public void saveOrUpdate(Integer type) {
        ReportDailyTaskDo reportDailyTaskDo = new ReportDailyTaskDo();
        reportDailyTaskDo.setType(type);
        reportDailyTaskDo.setDate(new Date());
        reportDailyTaskDo.setRefreshTime(new Date());

        if (repository.existReportDailyTaskRecord(reportDailyTaskDo)) {
            repository.updateByDateAndType(reportDailyTaskDo);
        } else {
            save(reportDailyTaskDo);
        }
    }

    public ReportDailyTaskDo selectTodayRecord(Integer type) {
        return repository.selectTodayByType(type);
    }
}