package com.xftech.cdp.infra.rocketmq.judge;

import javax.annotation.Resource;

import com.xftech.cdp.infra.rocketmq.MessageHandlerSelector;

import com.xinfei.xfframework.common.starter.mq.MqConsumerListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version v1.0 2024/9/23
 * @description TpApiJudgeRocketMqConsumer
 */

@Component
@Slf4j
@RocketMQMessageListener(consumerGroup = "cg_xyf_cdp_tp_apiopfcore_api_judge_all", topic = "tp_apiopfcore_api_judge_all", consumeMode = ConsumeMode.CONCURRENTLY)
public class TpApiJudgeRocketMqConsumer extends MqC<PERSON>umerListener<String> {

    @Resource
    private MessageHandlerSelector handlerSelector;

    @Override
    protected void doMessage(String topic, String bodyMessage, MessageExt messageExt) {
        log.info("TpApiJudgeRocketMqConsumer doMessage topic={}, messageId={}, body={}", topic, messageExt.getMsgId(), bodyMessage);
        try {
            handlerSelector.doMessage(topic, null, messageExt.getMsgId(), bodyMessage);
        } catch (Exception e) {
            log.error("TpApiJudgeRocketMqConsumer doMessage error, topic={}, messageId={}, body={}", topic, messageExt.getMsgId(), bodyMessage, e);
        }
    }

}

