package com.xftech.cdp.domain.stat.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.base.database.Page;
import com.xftech.cdp.domain.stat.entity.StatRealtimeStrategyFlowDataEntity;
import com.xftech.cdp.domain.stat.entity.StatRealtimeStrategyGroupDataEntity;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 实时策略分组数据统计
 */
@Component
public class StatRealtimeStrategyGroupDataRepository {

    /**
     * 保存实时策略分组数据
     * @param entity
     */
    public void saveStatRealtimeStrategyGroupData(StatRealtimeStrategyGroupDataEntity entity) {
        DBUtil.insert("statRealtimeStrategyGroupData.saveStatRealtimeStrategyGroupData", entity);
    }

    /**
     * 更新实时策略分组数据
     * @param entity
     */
    public void updateStatRealtimeStrategyGroupData(StatRealtimeStrategyGroupDataEntity entity) {
        DBUtil.update("statRealtimeStrategyGroupData.updateStatRealtimeStrategyGroupData", entity);
    }

    /**
     * 更新实时策略分组数据
     * @param entity
     */
    public Boolean existRealtimeStrategyGroupData(StatRealtimeStrategyGroupDataEntity entity) {
        Integer num = DBUtil.selectOne("statRealtimeStrategyGroupData.existRealtimeStrategyGroupData", entity);
        return num > 0;
    }

    /**
     * 批量保存实时策略分组数据
     * @param list
     */
    public void batchSaveStatRealtimeStrategyGroupData(List<StatRealtimeStrategyGroupDataEntity> list) {
        DBUtil.insertBatch("statRealtimeStrategyGroupData.saveStatRealtimeStrategyGroupData", list);
    }


    /**
     * 根据策略Id和日期查询分组数据
     * @param strategyId 策略ID
     * @param bizDate 日期
     * @return
     */
    public List<StatRealtimeStrategyGroupDataEntity> listByStrategyIdAndBizDate(Long strategyId, String bizDate) {
        Map<String, Object> map = new HashMap<>();
        map.put("strategyId", strategyId);
        map.put("bizDate", bizDate);
        return DBUtil.selectList("statRealtimeStrategyGroupData.listByStrategyIdAndBizDate", map);
    }

}
