/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.label.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * @version $ MetaLabelJoinLabelDto, v 0.1 2024/6/21 14:43 lingang.han Exp $
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MetaLabelJoinLabelDto implements Serializable {
    private static final long serialVersionUID = -7208745714810057581L;

    /* meta_label部分*/
    private Long mlId;

    private Long dataId;

    private String labelName;

    private String labelCode;

    private String labelDataValueType;

    private String aggDimension;

    private Integer available;

    private Integer online;

    private String labelEnumValue;

    private String checkResult;

    private Date createdTime;

    private Date updatedTime;

    private Integer dFlag;

    /* label 部分*/

    private Long lId;

    private Integer primaryLabel;


    private Integer secondaryLabel;


    private String lLabelName;

    private Integer labelType;

    /**
     * label_code
     */
    private String dataWarehouseField;

    private Integer configurationOptionType;

    private Integer exchangeType;

    private String configurationOption;

    private String configurationReflect;

    private String description;

    private String businessType;

    private Integer lDFlag;

    private LocalDateTime lCreatedTime;

    private String lCreatedOp;

    private LocalDateTime lUpdatedTime;

    private String lUpdatedOp;

}