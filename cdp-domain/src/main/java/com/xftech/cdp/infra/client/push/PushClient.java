/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.client.push;

import com.xftech.cdp.feign.PushFeignClient;
import com.xftech.cdp.feign.model.SendPushInfo;
import com.xftech.cdp.feign.model.requset.PushBaseRequest;
import com.xftech.cdp.feign.model.requset.SendPushRequest;
import com.xftech.cdp.feign.model.response.PushResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version $ PushClient, v 0.1 2024/1/19 16:56 lingang.han Exp $
 */

@Slf4j
@Component
public class PushClient {
    @Autowired
    private PushFeignClient pushFeignClient;

    public PushResponse<SendPushInfo> doBatchSendPush(PushBaseRequest<SendPushRequest> request) {
        log.info("send push request:{}", request);
        PushResponse<SendPushInfo> response = pushFeignClient.sendPush(request);
        log.info("send push request:{},response:{}", request, response);
        return response;
    }
}