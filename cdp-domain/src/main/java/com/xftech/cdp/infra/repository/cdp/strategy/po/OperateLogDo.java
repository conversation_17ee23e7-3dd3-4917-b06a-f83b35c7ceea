/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.repository.cdp.strategy.po;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version $ OperateLogDo, v 0.1 2024/4/11 15:51 lingang.han Exp $
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OperateLogDo implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 请求URL
     */
    private String url;

    /**
     * 请求参数
     */
    private String requestParam;

    /**
     * 响应
     */
    private String response;

    /**
     * 操作类型
     */
    private Integer type;

    /**
     * 操作模块
     */
    private Integer model;

    /**
     * 操作对象id
     */
    private Long objId;

    /**
     * 操作描述
     */
    private String description;

    /**
     * 操作人身份标识
     */
    private String userIdentify;

    private Short dFlag;

    private LocalDateTime createdTime;

    private LocalDateTime updatedTime;

}