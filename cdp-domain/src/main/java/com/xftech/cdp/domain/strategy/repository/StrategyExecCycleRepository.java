/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyExecCycleCounterDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyExecCycleDo;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @version $ StrategyExecCycleRepository, v 0.1 2023/11/3 14:37 yye.xu Exp $
 */

@Component
public class StrategyExecCycleRepository {

    public void insert(StrategyExecCycleDo cycleDo){
        DBUtil.insert("strategyExecCycleMapper.insertSelective", cycleDo);
    }

    public StrategyExecCycleDo selectStrategyCycle(Long strategyId, Integer dateValue) {
        Map<String, Object> param = new HashMap<>();
        param.put("strategyId", strategyId);
        param.put("dateValue", dateValue);
        return DBUtil.selectOne("strategyExecCycleMapper.selectStrategyCycle", param);
    }
}