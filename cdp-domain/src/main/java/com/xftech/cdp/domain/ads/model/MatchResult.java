/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.domain.ads.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ MatchResult, v 0.1 2023/10/10 16:32 wancheng.qu Exp $
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class MatchResult<T> {

    /**匹配的标签*/
    private List<T> matchingLabels;
    /**不匹配的标签*/
    private List<T> nonMatchingLabels;

}
